#!/usr/bin/env node

/**
 * Debug script to test the complete advance payment workflow
 * and verify all database relationship fixes are working
 */

const fetch = require('node-fetch');

// Configuration
const BASE_URL = process.env.MEDUSA_URL || 'http://localhost:9000';

// Test data
const TEST_CART_ID = 'cart_01K1AFGGHK08K54G0D1537N3AT'; // Replace with your actual cart ID

async function debugWorkflow() {
  console.log('🔍 DEBUGGING ADVANCE PAYMENT WORKFLOW EXECUTION\n');
  console.log(`📋 Testing with cart ID: ${TEST_CART_ID}\n`);

  try {
    // Step 1: Test advance payment creation
    console.log('='.repeat(80));
    console.log('📋 STEP 1: Testing Advance Payment Creation');
    console.log('='.repeat(80));
    
    const advanceResponse = await fetch(`${BASE_URL}/store/carts/${TEST_CART_ID}/advance-payment`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        payment_mode: "manual",
        advance_amount: 4400,
        metadata: {
          debug_test: true,
          test_timestamp: new Date().toISOString()
        }
      })
    });

    const advanceText = await advanceResponse.text();
    
    if (advanceResponse.ok) {
      console.log('✅ Advance payment creation succeeded');
      const advanceData = JSON.parse(advanceText);
      console.log('📊 Response:', JSON.stringify(advanceData, null, 2));
    } else {
      console.log(`❌ Advance payment creation failed (${advanceResponse.status})`);
      console.log('📄 Error response:', advanceText);
      
      if (advanceText.includes('publishable')) {
        console.log('\n🔑 Retrying with test publishable API key...');
        
        const retryResponse = await fetch(`${BASE_URL}/store/carts/${TEST_CART_ID}/advance-payment`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'x-publishable-api-key': 'pk_test_123'
          },
          body: JSON.stringify({
            payment_mode: "manual",
            advance_amount: 4400,
            metadata: {
              debug_test: true,
              test_timestamp: new Date().toISOString()
            }
          })
        });

        const retryText = await retryResponse.text();
        
        if (retryResponse.ok) {
          console.log('✅ Advance payment creation succeeded with API key');
          const retryData = JSON.parse(retryText);
          console.log('📊 Response:', JSON.stringify(retryData, null, 2));
        } else {
          console.log(`❌ Advance payment creation still failed (${retryResponse.status})`);
          console.log('📄 Error response:', retryText);
          return; // Exit if we can't create advance payment
        }
      } else {
        return; // Exit if it's not an API key issue
      }
    }

    // Step 2: Test payment recording
    console.log('\n' + '='.repeat(80));
    console.log('📋 STEP 2: Testing Payment Recording');
    console.log('='.repeat(80));
    
    // First, get the payment collection ID from the cart
    const cartResponse = await fetch(`${BASE_URL}/store/carts/${TEST_CART_ID}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      }
    });

    if (!cartResponse.ok) {
      console.log('❌ Failed to retrieve cart for payment collection ID');
      return;
    }

    const cartData = await cartResponse.json();
    console.log('📋 Cart data retrieved for payment collection lookup');
    
    // Extract payment collection ID (this might need adjustment based on your cart structure)
    const paymentCollectionId = cartData.cart?.payment_collection?.id || 
                               cartData.cart?.payment_collections?.[0]?.id;
    
    if (!paymentCollectionId) {
      console.log('❌ No payment collection ID found in cart');
      console.log('📄 Cart structure:', JSON.stringify(cartData.cart, null, 2));
      return;
    }

    console.log(`📋 Found payment collection ID: ${paymentCollectionId}`);

    const recordResponse = await fetch(`${BASE_URL}/store/carts/${TEST_CART_ID}/record-payment`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        payment_mode: "manual",
        payment_collection_id: paymentCollectionId,
        manual_payment: {
          payment_method: "bank_transfer",
          reference_number: "DEBUG_TXN_" + Date.now(),
          collected_by: "debug_test",
          amount_received: 4400,
          notes: "Debug test payment recording"
        },
        metadata: {
          debug_test: true,
          test_timestamp: new Date().toISOString()
        }
      })
    });

    const recordText = await recordResponse.text();
    
    if (recordResponse.ok) {
      console.log('✅ Payment recording succeeded');
      const recordData = JSON.parse(recordText);
      console.log('📊 Response:', JSON.stringify(recordData, null, 2));
    } else {
      console.log(`❌ Payment recording failed (${recordResponse.status})`);
      console.log('📄 Error response:', recordText);
    }

    // Step 3: Test cart completion
    console.log('\n' + '='.repeat(80));
    console.log('📋 STEP 3: Testing Cart Completion');
    console.log('='.repeat(80));
    
    const completeResponse = await fetch(`${BASE_URL}/store/carts/${TEST_CART_ID}/complete-with-advance`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        payment_collection_id: paymentCollectionId,
        metadata: {
          debug_test: true,
          test_timestamp: new Date().toISOString()
        }
      })
    });

    const completeText = await completeResponse.text();
    
    if (completeResponse.ok) {
      console.log('✅ Cart completion succeeded');
      const completeData = JSON.parse(completeText);
      console.log('📊 Response:', JSON.stringify(completeData, null, 2));
      
      // Extract order ID for database verification
      const orderId = completeData.order?.id;
      if (orderId) {
        console.log(`\n📋 Order created: ${orderId}`);
        console.log('\n🔍 DATABASE VERIFICATION QUERIES:');
        console.log('Run these SQL queries to verify the fixes:');
        console.log('');
        console.log(`-- 1. Check payment session status (should be 'authorized')`);
        console.log(`SELECT ps.id, ps.status, ps.data FROM payment_session ps`);
        console.log(`JOIN payment_collection pc ON ps.payment_collection_id = pc.id`);
        console.log(`WHERE pc.id = '${paymentCollectionId}';`);
        console.log('');
        console.log(`-- 2. Check payment collection status and amounts`);
        console.log(`SELECT id, status, authorized_amount, captured_amount, completed_at, captured_at`);
        console.log(`FROM payment_collection WHERE id = '${paymentCollectionId}';`);
        console.log('');
        console.log(`-- 3. Check order-cart relationship`);
        console.log(`SELECT id, cart_id FROM "order" WHERE id = '${orderId}';`);
        console.log('');
        console.log(`-- 4. Check order_cart junction table`);
        console.log(`SELECT * FROM order_cart WHERE order_id = '${orderId}' OR cart_id = '${TEST_CART_ID}';`);
      }
      
    } else {
      console.log(`❌ Cart completion failed (${completeResponse.status})`);
      console.log('📄 Error response:', completeText);
    }

  } catch (error) {
    console.error('💥 Debug workflow failed with error:', error.message);
    console.error('Stack trace:', error.stack);
  }
}

// Run the debug workflow
debugWorkflow().then(() => {
  console.log('\n🏁 Debug workflow completed');
  console.log('\n📋 NEXT STEPS:');
  console.log('1. Check the server logs for detailed debugging output');
  console.log('2. Run the SQL verification queries shown above');
  console.log('3. Look for any CRITICAL error messages in the logs');
  console.log('4. Verify that all three database relationship fixes are working');
}).catch(error => {
  console.error('💥 Debug script failed:', error);
  process.exit(1);
});
