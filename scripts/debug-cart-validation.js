#!/usr/bin/env node

/**
 * Debug script to understand the cart validation issue
 */

const fetch = require('node-fetch');

const BASE_URL = process.env.MEDUSA_URL || 'http://localhost:9000';
const PUBLISHABLE_API_KEY = process.env.PUBLISHABLE_API_KEY || 'pk_test_123';

async function debugCartValidation() {
  console.log("🔍 Debugging Cart Validation Issue");
  console.log("=" .repeat(50));

  // Test 1: Check if the endpoint exists
  console.log("\n1️⃣ Testing if /store/carts endpoint exists...");
  try {
    const response = await fetch(`${BASE_URL}/store/carts`, {
      method: 'OPTIONS',
      headers: {
        'x-publishable-api-key': PUBLISHABLE_API_KEY
      }
    });
    console.log(`   Status: ${response.status}`);
    console.log(`   Headers: ${JSON.stringify(Object.fromEntries(response.headers), null, 2)}`);
  } catch (error) {
    console.log(`   Error: ${error.message}`);
  }

  // Test 2: Try with minimal payload
  console.log("\n2️⃣ Testing with minimal payload...");
  const minimalPayload = {
    region_id: "test_region",
    currency_code: "USD"
  };

  try {
    const response = await fetch(`${BASE_URL}/store/carts`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'x-publishable-api-key': PUBLISHABLE_API_KEY
      },
      body: JSON.stringify(minimalPayload)
    });

    console.log(`   Status: ${response.status}`);
    const data = await response.json();
    console.log(`   Response: ${JSON.stringify(data, null, 2)}`);
  } catch (error) {
    console.log(`   Error: ${error.message}`);
  }

  // Test 3: Try with customer_id
  console.log("\n3️⃣ Testing with customer_id...");
  const customerPayload = {
    region_id: "test_region",
    currency_code: "USD",
    customer_id: "test_customer"
  };

  try {
    const response = await fetch(`${BASE_URL}/store/carts`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'x-publishable-api-key': PUBLISHABLE_API_KEY
      },
      body: JSON.stringify(customerPayload)
    });

    console.log(`   Status: ${response.status}`);
    const data = await response.json();
    console.log(`   Response: ${JSON.stringify(data, null, 2)}`);
  } catch (error) {
    console.log(`   Error: ${error.message}`);
  }

  // Test 4: Check what Medusa expects
  console.log("\n4️⃣ Testing different field combinations...");
  
  const testCases = [
    { name: "Only required fields", payload: { region_id: "test", currency_code: "USD" } },
    { name: "With email", payload: { region_id: "test", currency_code: "USD", email: "<EMAIL>" } },
    { name: "With customer_id", payload: { region_id: "test", currency_code: "USD", customer_id: "test" } },
    { name: "With both", payload: { region_id: "test", currency_code: "USD", email: "<EMAIL>", customer_id: "test" } },
  ];

  for (const testCase of testCases) {
    console.log(`\n   Testing: ${testCase.name}`);
    try {
      const response = await fetch(`${BASE_URL}/store/carts`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-publishable-api-key': PUBLISHABLE_API_KEY
        },
        body: JSON.stringify(testCase.payload)
      });

      console.log(`     Status: ${response.status}`);
      if (!response.ok) {
        const error = await response.json();
        console.log(`     Error: ${error.message || JSON.stringify(error)}`);
      } else {
        console.log(`     ✅ Success`);
      }
    } catch (error) {
      console.log(`     Network Error: ${error.message}`);
    }
  }

  // Test 5: Check if there's a different cart endpoint
  console.log("\n5️⃣ Checking alternative endpoints...");
  
  const alternativeEndpoints = [
    '/store/cart',
    '/store/carts/create',
    '/store/cart/create'
  ];

  for (const endpoint of alternativeEndpoints) {
    console.log(`\n   Testing: ${endpoint}`);
    try {
      const response = await fetch(`${BASE_URL}${endpoint}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-publishable-api-key': PUBLISHABLE_API_KEY
        },
        body: JSON.stringify({ region_id: "test", currency_code: "USD" })
      });

      console.log(`     Status: ${response.status}`);
    } catch (error) {
      console.log(`     Error: ${error.message}`);
    }
  }

  console.log("\n📋 Summary");
  console.log("=" .repeat(50));
  console.log("Check the responses above to understand:");
  console.log("1. Which fields are actually required");
  console.log("2. What validation errors you're getting");
  console.log("3. If there are alternative endpoints");
  console.log("4. What the expected payload structure is");
}

if (require.main === module) {
  debugCartValidation().catch(console.error);
}

module.exports = { debugCartValidation };
