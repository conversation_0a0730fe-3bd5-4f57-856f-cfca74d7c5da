#!/usr/bin/env node

/**
 * Setup script for regions and currencies in medusa-store-v2
 * This script creates the necessary regions and currencies for proper cart functionality
 */

const { execSync } = require('child_process');
const path = require('path');

// Configuration for regions and currencies
const REGIONS_CONFIG = [
  {
    name: "Switzerland",
    currency_code: "CHF",
    countries: ["CH"],
    tax_rate: 7.7, // Swiss VAT
    is_default: true
  },
  {
    name: "European Union",
    currency_code: "EUR", 
    countries: ["DE", "FR", "IT", "AT", "NL", "BE", "ES", "PT"],
    tax_rate: 20.0, // Average EU VAT
    is_default: false
  },
  {
    name: "United States",
    currency_code: "USD",
    countries: ["US"],
    tax_rate: 8.5, // Average US sales tax
    is_default: false
  }
];

const CURRENCIES = [
  { code: "CHF", name: "Swiss Franc" },
  { code: "EUR", name: "Euro" },
  { code: "USD", name: "US Dollar" },
  { code: "GBP", name: "British Pound" }
];

async function setupRegionsAndCurrencies() {
  console.log("🏗️  Setting up regions and currencies for medusa-store-v2");
  console.log("=" .repeat(60));

  try {
    // Check if we're in the right directory
    const packageJsonPath = path.join(process.cwd(), 'package.json');
    const packageJson = require(packageJsonPath);
    
    if (!packageJson.name || !packageJson.name.includes('medusa')) {
      console.error("❌ This script must be run from the medusa-store-v2 root directory");
      process.exit(1);
    }

    console.log("📍 Current directory:", process.cwd());
    console.log("📦 Project:", packageJson.name);

    // Step 1: Create currencies
    console.log("\n1️⃣ Creating currencies...");
    for (const currency of CURRENCIES) {
      console.log(`   💰 Creating currency: ${currency.code} (${currency.name})`);
      
      // Note: This would typically be done via Medusa Admin API or database migration
      // For now, we'll provide the SQL commands that need to be run
      console.log(`   SQL: INSERT INTO currency (code, name) VALUES ('${currency.code}', '${currency.name}') ON CONFLICT (code) DO NOTHING;`);
    }

    // Step 2: Create regions
    console.log("\n2️⃣ Creating regions...");
    for (const region of REGIONS_CONFIG) {
      console.log(`   🌍 Creating region: ${region.name} (${region.currency_code})`);
      console.log(`   Countries: ${region.countries.join(', ')}`);
      console.log(`   Tax rate: ${region.tax_rate}%`);
      console.log(`   Default: ${region.is_default ? 'Yes' : 'No'}`);
      
      // Note: This would typically be done via Medusa Admin API
      console.log(`   API Call: POST /admin/regions`);
      console.log(`   Body: {
     "name": "${region.name}",
     "currency_code": "${region.currency_code}",
     "countries": ${JSON.stringify(region.countries)},
     "tax_rate": ${region.tax_rate},
     "metadata": { "is_default": ${region.is_default} }
   }`);
    }

    // Step 3: Create sales channels
    console.log("\n3️⃣ Creating sales channels...");
    const salesChannels = [
      { name: "CRM Sales", description: "Internal CRM sales team" },
      { name: "Website", description: "Public website bookings" },
      { name: "Mobile App", description: "Mobile application bookings" },
      { name: "Partner Portal", description: "Partner and affiliate bookings" }
    ];

    for (const channel of salesChannels) {
      console.log(`   📱 Creating sales channel: ${channel.name}`);
      console.log(`   API Call: POST /admin/sales-channels`);
      console.log(`   Body: {
     "name": "${channel.name}",
     "description": "${channel.description}",
     "is_disabled": false
   }`);
    }

    // Step 4: Create publishable API keys
    console.log("\n4️⃣ Creating publishable API keys...");
    console.log(`   🔑 Create API keys for each sales channel via Medusa Admin`);
    console.log(`   📝 Associate each key with appropriate sales channels`);

    // Step 5: Provide next steps
    console.log("\n✅ Setup complete! Next steps:");
    console.log("=" .repeat(60));
    console.log("1. Run the SQL commands above to create currencies");
    console.log("2. Use Medusa Admin or API to create regions with the provided data");
    console.log("3. Create sales channels using the API calls above");
    console.log("4. Generate publishable API keys for each sales channel");
    console.log("5. Update your cart creation logic to use the default region");
    console.log("6. Test cart creation with the new region/currency requirements");

    // Step 6: Provide example cart creation
    console.log("\n📋 Example cart creation with new requirements:");
    console.log(`
POST /store/carts
{
  "region_id": "reg_switzerland_123",
  "currency_code": "CHF",
  "customer_id": "cus_123",
  "email": "<EMAIL>",
  "sales_channel_id": "sc_crm_sales_123",
  "items": [
    {
      "variant_id": "variant_room_101",
      "quantity": 1,
      "unit_price": 25000,
      "metadata": {
        "check_in": "2024-08-01",
        "check_out": "2024-08-03",
        "guests": 2
      }
    }
  ],
  "metadata": {
    "cart_type": "quote",
    "created_by": "crm_user_123",
    "hotel_id": "hotel_123"
  }
}
`);

    console.log("\n🎉 Region and currency setup guide completed!");
    
  } catch (error) {
    console.error("❌ Error during setup:", error.message);
    process.exit(1);
  }
}

// Helper function to create a region via API (example)
function createRegionExample() {
  return `
// Example function to create region via Medusa Admin API
async function createRegion(regionData) {
  const response = await fetch('/admin/regions', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': 'Bearer YOUR_ADMIN_TOKEN'
    },
    body: JSON.stringify(regionData)
  });
  
  if (!response.ok) {
    throw new Error(\`Failed to create region: \${response.statusText}\`);
  }
  
  return response.json();
}
`;
}

// Run the setup if this script is executed directly
if (require.main === module) {
  setupRegionsAndCurrencies().catch(console.error);
}

module.exports = {
  setupRegionsAndCurrencies,
  REGIONS_CONFIG,
  CURRENCIES
};
