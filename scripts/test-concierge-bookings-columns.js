#!/usr/bin/env node

/**
 * Test script to verify the concierge bookings columns implementation
 * Run with: node scripts/test-concierge-bookings-columns.js
 */

const http = require('http');

function makeRequest(options) {
  return new Promise((resolve, reject) => {
    const req = http.request(options, (res) => {
      let body = '';
      res.on('data', (chunk) => {
        body += chunk;
      });
      res.on('end', () => {
        try {
          const parsedBody = JSON.parse(body);
          resolve({
            statusCode: res.statusCode,
            headers: res.headers,
            body: parsedBody
          });
        } catch (error) {
          resolve({
            statusCode: res.statusCode,
            headers: res.headers,
            body: body
          });
        }
      });
    });

    req.on('error', (error) => {
      reject(error);
    });

    req.end();
  });
}

async function testConciergeBookingsAPI() {
  console.log('🧪 Testing Concierge Bookings API with Enhanced Columns...');
  
  try {
    // Test the API endpoint
    console.log('\n📋 Test: Fetching concierge bookings data');
    const response = await makeRequest({
      hostname: 'localhost',
      port: 9000,
      path: '/admin/concierge-management/orders?limit=5',
      method: 'GET',
      headers: {
        'Content-Type': 'application/json'
      }
    });

    console.log(`Status: ${response.statusCode}`);
    
    if (response.statusCode === 200 && response.body.items) {
      console.log('✅ API endpoint is working');
      console.log(`📊 Found ${response.body.items.length} booking(s)`);
      
      // Test the data structure for our required columns
      if (response.body.items.length > 0) {
        const booking = response.body.items[0];
        console.log('\n📋 Testing data structure for required columns:');
        
        // Test Booking ID (order_id)
        console.log(`✅ Booking ID: ${booking.order_id || 'Missing'}`);
        
        // Test Customer Name (from order.customer or metadata)
        const customerName = booking.order?.customer?.first_name || booking.order?.customer?.last_name 
          ? `${booking.order.customer.first_name || ''} ${booking.order.customer.last_name || ''}`.trim()
          : booking.order?.metadata?.guest_name || booking.order?.email || 'N/A';
        console.log(`✅ Customer Name: ${customerName}`);
        
        // Test Date of Travel (check-in to check-out)
        const checkIn = booking.order?.metadata?.check_in_date;
        const checkOut = booking.order?.metadata?.check_out_date;
        console.log(`✅ Travel Dates: ${checkIn || 'N/A'} to ${checkOut || 'N/A'}`);
        
        // Test Hotel Name
        const hotelName = booking.order?.metadata?.hotel_name;
        console.log(`✅ Hotel Name: ${hotelName || 'N/A'}`);
        
        // Test Payment Info
        const paymentInfo = booking.order?.metadata?.payment_info || {};
        console.log(`✅ Total Price: ${paymentInfo.total_amount || 'N/A'} ${paymentInfo.currency_code || 'USD'}`);
        console.log(`✅ Paid Amount: ${paymentInfo.paid_amount || 'N/A'} ${paymentInfo.currency_code || 'USD'}`);
        console.log(`✅ Remaining: ${paymentInfo.remaining_amount || 'N/A'} ${paymentInfo.currency_code || 'USD'}`);
        
        // Test Status
        const orderStatus = booking.order?.status;
        console.log(`✅ Status: ${orderStatus || 'N/A'}`);
        
        console.log('\n🎉 All required column data is available!');
      } else {
        console.log('ℹ️  No bookings found to test data structure');
      }
    } else if (response.statusCode === 401) {
      console.log('⚠️  Authentication required - this is expected in production');
    } else {
      console.log('❌ API endpoint returned unexpected status');
      console.log('Response body:', response.body);
    }

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    
    if (error.code === 'ECONNREFUSED') {
      console.log('💡 Make sure the Medusa server is running on port 9000');
      console.log('   Run: yarn dev');
    }
  }
}

// Run the test
testConciergeBookingsAPI().catch(console.error);
