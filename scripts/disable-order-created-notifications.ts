import { ExecArgs } from "@camped-ai/framework/types";
import { NOTIFICATION_TEMPLATE_SERVICE } from "../src/modules/notification-template/service";
import NotificationTemplateService from "../src/modules/notification-template/service";

export default async function disableOrderCreatedNotifications({
  container,
}: ExecArgs) {
  console.log("🔧 Disabling order.created notifications...");

  try {
    const notificationTemplateService: NotificationTemplateService = container.resolve(
      NOTIFICATION_TEMPLATE_SERVICE
    );

    // Check for order.created templates
    const orderCreatedTemplates = await notificationTemplateService.listNotificationTemplates({
      event_name: "order.created"
    });
    
    console.log(`Found ${orderCreatedTemplates.length} order.created templates`);
    
    // Disable all order.created templates
    for (const template of orderCreatedTemplates) {
      if (template.is_active) {
        await notificationTemplateService.updateNotificationTemplates(template.id, {
          is_active: false
        });
        console.log(`✅ Disabled template ${template.id} for order.created`);
      } else {
        console.log(`ℹ️ Template ${template.id} for order.created already disabled`);
      }
    }

    // Check for order-created-template templates
    const orderCreatedTemplateTemplates = await notificationTemplateService.listNotificationTemplates({
      event_name: "order-created-template"
    });
    
    console.log(`Found ${orderCreatedTemplateTemplates.length} order-created-template templates`);
    
    // Disable all order-created-template templates
    for (const template of orderCreatedTemplateTemplates) {
      if (template.is_active) {
        await notificationTemplateService.updateNotificationTemplates(template.id, {
          is_active: false
        });
        console.log(`✅ Disabled template ${template.id} for order-created-template`);
      } else {
        console.log(`ℹ️ Template ${template.id} for order-created-template already disabled`);
      }
    }

    console.log("🎉 Successfully disabled order.created notifications");
    
  } catch (error) {
    console.error("❌ Error disabling order.created notifications:", error);
    throw error;
  }
}
