#!/usr/bin/env node

/**
 * Test script to verify concierge service methods are available
 * Run with: node scripts/test-concierge-service-resolution.js
 */

const http = require('http');

function makeRequest(options, data = null) {
  return new Promise((resolve, reject) => {
    const req = http.request(options, (res) => {
      let body = '';
      res.on('data', (chunk) => {
        body += chunk;
      });
      res.on('end', () => {
        resolve({
          statusCode: res.statusCode,
          headers: res.headers,
          body: body
        });
      });
    });

    req.on('error', (err) => {
      reject(err);
    });

    if (data) {
      req.write(data);
    }
    req.end();
  });
}

async function testServiceResolution() {
  console.log('🧪 Testing Concierge Service Resolution...');
  
  const tests = [
    {
      name: 'List Concierge Orders',
      path: '/admin/concierge-management/orders',
      method: 'GET'
    },
    {
      name: 'Get Specific Order (should 404)',
      path: '/admin/concierge-management/orders/test-id',
      method: 'GET'
    },
    {
      name: 'List Order Items (should 404)',
      path: '/admin/concierge-management/orders/test-id/items',
      method: 'GET'
    }
  ];

  for (const test of tests) {
    try {
      console.log(`\n📋 ${test.name}:`);
      
      const response = await makeRequest({
        hostname: 'localhost',
        port: 9000,
        path: test.path,
        method: test.method,
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer test-token'
        }
      });

      console.log(`   Status: ${response.statusCode}`);
      
      if (response.statusCode === 401) {
        console.log('   ✅ Service resolved (auth required as expected)');
      } else if (response.statusCode === 404) {
        console.log('   ✅ Service resolved (resource not found as expected)');
      } else if (response.statusCode === 500) {
        const body = JSON.parse(response.body);
        if (body.error && body.error.includes('Could not resolve')) {
          console.log('   ❌ Service resolution failed');
          console.log(`   Error: ${body.error}`);
        } else {
          console.log('   ⚠️ Other server error (service might be resolved)');
          console.log(`   Error: ${body.error || body.message}`);
        }
      } else {
        console.log(`   ✅ Service resolved (status: ${response.statusCode})`);
      }
      
    } catch (error) {
      console.error(`   ❌ Request failed: ${error.message}`);
      
      if (error.code === 'ECONNREFUSED') {
        console.log('   💡 Make sure the development server is running: npm run dev');
        break;
      }
    }
  }

  console.log('\n🎉 Service resolution test completed!');
  console.log('\n💡 Next steps:');
  console.log('   1. Set up proper authentication to test full functionality');
  console.log('   2. Create database tables if not already done');
  console.log('   3. Test with Postman collection for complete API testing');
}

// Run the test
testServiceResolution().catch(console.error);
