#!/usr/bin/env node

/**
 * Test script to verify meal plan restrictions for Extra Bed and Baby Cot occupancy types
 * This script tests the logic implemented in the pricing table components
 */

console.log('🧪 Testing Meal Plan Restrictions for Extra Bed and Baby Cot');
console.log('=' .repeat(60));

// Mock data similar to what the components receive
const mockOccupancyConfigs = [
  {
    id: 'occ_adult',
    name: 'Adult',
    type: 'EXTRA_ADULT',
  },
  {
    id: 'occ_child',
    name: 'Child',
    type: 'CHILD',
  },
  {
    id: 'occ_extra_bed',
    name: 'Extra Bed',
    type: 'EXTRA_BED',
  },
  {
    id: 'occ_baby_cot',
    name: 'Baby Cot',
    type: 'COT',
  },
  {
    id: 'occ_custom_cot',
    name: 'Custom Baby Cot',
    type: 'CUSTOM', // This should be caught by name matching
  }
];

const mockMealPlans = [
  {
    id: 'mp_no_meals',
    name: 'No Meals',
    type: 'none',
  },
  {
    id: 'mp_bb',
    name: 'Bed & Breakfast',
    type: 'bb',
  },
  {
    id: 'mp_hb',
    name: 'Half Board',
    type: 'hb',
  },
  {
    id: 'mp_fb',
    name: 'Full Board',
    type: 'fb',
  }
];

// Replicate the getAvailableMealPlans logic from the components
function getAvailableMealPlans(occupancyTypeId, occupancyConfigs, mealPlans) {
  const occupancy = occupancyConfigs.find((oc) => oc.id === occupancyTypeId);
  if (!occupancy) return mealPlans;

  // Logic for special accommodations (Extra Bed and Baby Cot)
  const isExtraBed =
    occupancy.type === "EXTRA_BED" ||
    occupancy.name?.toLowerCase().includes("extra bed");
  const isCot =
    occupancy.type === "COT" ||
    occupancy.name?.toLowerCase().includes("cot") ||
    occupancy.name?.toLowerCase().includes("baby cot");

  if (isExtraBed || isCot) {
    // Find the "No Meals" meal plan instead of returning a fake N/A option
    const noMealsPlan = mealPlans.find(
      (mp) => 
        mp.name?.toLowerCase().includes("no meals") ||
        mp.name?.toLowerCase().includes("none") ||
        mp.type === "none"
    );
    
    if (noMealsPlan) {
      return [noMealsPlan];
    }
    
    // Fallback to N/A if no "No Meals" plan exists
    return [{ id: null, name: "No Meals" }];
  }

  // For other occupancy types, return all meal plans
  return mealPlans;
}

// Test each occupancy type
console.log('\n📋 Testing meal plan availability for each occupancy type:\n');

mockOccupancyConfigs.forEach((occupancy, index) => {
  const availableMealPlans = getAvailableMealPlans(occupancy.id, mockOccupancyConfigs, mockMealPlans);
  
  console.log(`${index + 1}. ${occupancy.name} (${occupancy.type}):`);
  console.log(`   Available meal plans: ${availableMealPlans.length}`);
  
  availableMealPlans.forEach((mp, mpIndex) => {
    const status = (occupancy.type === 'EXTRA_BED' || occupancy.type === 'COT' || 
                   occupancy.name?.toLowerCase().includes('cot')) && 
                   mp.name === 'No Meals' ? '✅ RESTRICTED' : '📋 AVAILABLE';
    console.log(`   ${mpIndex + 1}. ${mp.name} ${status}`);
  });
  
  console.log('');
});

// Test specific scenarios
console.log('\n🎯 Testing specific scenarios:\n');

// Test 1: Extra Bed should only have "No Meals"
const extraBedPlans = getAvailableMealPlans('occ_extra_bed', mockOccupancyConfigs, mockMealPlans);
const test1Pass = extraBedPlans.length === 1 && extraBedPlans[0].name === 'No Meals';
console.log(`Test 1 - Extra Bed restriction: ${test1Pass ? '✅ PASS' : '❌ FAIL'}`);
console.log(`   Expected: 1 meal plan ("No Meals"), Got: ${extraBedPlans.length} (${extraBedPlans.map(mp => mp.name).join(', ')})`);

// Test 2: Baby Cot should only have "No Meals"
const babyCotPlans = getAvailableMealPlans('occ_baby_cot', mockOccupancyConfigs, mockMealPlans);
const test2Pass = babyCotPlans.length === 1 && babyCotPlans[0].name === 'No Meals';
console.log(`Test 2 - Baby Cot restriction: ${test2Pass ? '✅ PASS' : '❌ FAIL'}`);
console.log(`   Expected: 1 meal plan ("No Meals"), Got: ${babyCotPlans.length} (${babyCotPlans.map(mp => mp.name).join(', ')})`);

// Test 3: Custom cot (name-based matching) should only have "No Meals"
const customCotPlans = getAvailableMealPlans('occ_custom_cot', mockOccupancyConfigs, mockMealPlans);
const test3Pass = customCotPlans.length === 1 && customCotPlans[0].name === 'No Meals';
console.log(`Test 3 - Custom cot (name matching): ${test3Pass ? '✅ PASS' : '❌ FAIL'}`);
console.log(`   Expected: 1 meal plan ("No Meals"), Got: ${customCotPlans.length} (${customCotPlans.map(mp => mp.name).join(', ')})`);

// Test 4: Adult should have all meal plans
const adultPlans = getAvailableMealPlans('occ_adult', mockOccupancyConfigs, mockMealPlans);
const test4Pass = adultPlans.length === 4;
console.log(`Test 4 - Adult (no restriction): ${test4Pass ? '✅ PASS' : '❌ FAIL'}`);
console.log(`   Expected: 4 meal plans, Got: ${adultPlans.length}`);

// Test 5: Child should have all meal plans
const childPlans = getAvailableMealPlans('occ_child', mockOccupancyConfigs, mockMealPlans);
const test5Pass = childPlans.length === 4;
console.log(`Test 5 - Child (no restriction): ${test5Pass ? '✅ PASS' : '❌ FAIL'}`);
console.log(`   Expected: 4 meal plans, Got: ${childPlans.length}`);

// Summary
const allTestsPass = test1Pass && test2Pass && test3Pass && test4Pass && test5Pass;
console.log('\n' + '='.repeat(60));
console.log(`🏁 Overall Result: ${allTestsPass ? '✅ ALL TESTS PASS' : '❌ SOME TESTS FAILED'}`);
console.log('='.repeat(60));

if (allTestsPass) {
  console.log('\n🎉 The meal plan restriction logic is working correctly!');
  console.log('   • Extra Bed occupancy types are restricted to "No Meals" only');
  console.log('   • Baby Cot occupancy types are restricted to "No Meals" only');
  console.log('   • Other occupancy types have access to all meal plans');
} else {
  console.log('\n⚠️  Some tests failed. Please review the implementation.');
}
