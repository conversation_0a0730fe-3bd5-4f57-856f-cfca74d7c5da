#!/usr/bin/env node

/**
 * Quick test script to verify concierge services are properly registered
 * Run with: node scripts/test-concierge-services.js
 */

const { createMedusaContainer } = require("@camped-ai/framework");

async function testServices() {
  try {
    console.log("🧪 Testing Concierge Services Registration...");
    
    // Create a test container
    const container = createMedusaContainer();
    
    // Load the concierge management module
    const conciergeLoader = require("../src/loaders/concierge-management-module").default;
    await conciergeLoader(container);
    
    // Test service resolution
    console.log("\n📋 Testing Service Resolution:");
    
    try {
      const conciergeOrderService = container.resolve("conciergeOrderService");
      console.log("✅ conciergeOrderService resolved successfully");
      console.log("   Type:", typeof conciergeOrderService);
      console.log("   Methods:", Object.getOwnPropertyNames(Object.getPrototypeOf(conciergeOrderService)).filter(name => name !== 'constructor'));
    } catch (error) {
      console.log("❌ conciergeOrderService failed to resolve:", error.message);
    }
    
    try {
      const conciergeOrderItemService = container.resolve("conciergeOrderItemService");
      console.log("✅ conciergeOrderItemService resolved successfully");
      console.log("   Type:", typeof conciergeOrderItemService);
    } catch (error) {
      console.log("❌ conciergeOrderItemService failed to resolve:", error.message);
    }
    
    try {
      const conciergeManagementService = container.resolve("conciergeManagementService");
      console.log("✅ conciergeManagementService resolved successfully");
      console.log("   Type:", typeof conciergeManagementService);
    } catch (error) {
      console.log("❌ conciergeManagementService failed to resolve:", error.message);
    }
    
    console.log("\n🎉 Service registration test completed!");
    
  } catch (error) {
    console.error("❌ Test failed:", error);
    process.exit(1);
  }
}

// Run the test
testServices().catch(console.error);
