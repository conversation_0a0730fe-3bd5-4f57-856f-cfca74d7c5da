#!/usr/bin/env node

/**
 * Script to clear problematic payment sessions for a cart
 */

const { withClient } = require('../src/utils/db');

const CART_ID = process.env.CART_ID || 'cart_01K17FHQPRKCZJ2BW5M6JF9AET';

async function clearPaymentSessions() {
  console.log(`🧹 Clearing payment sessions for cart: ${CART_ID}`);
  
  try {
    await withClient(async (client) => {
      // Find payment collection for this cart
      const collectionQuery = `
        SELECT pc.id as payment_collection_id
        FROM cart_payment_collection cpc
        JOIN payment_collection pc ON cpc.payment_collection_id = pc.id
        WHERE cpc.cart_id = $1
      `;
      
      const collectionResult = await client.query(collectionQuery, [CART_ID]);
      
      if (collectionResult.rows.length === 0) {
        console.log("No payment collection found for this cart");
        return;
      }
      
      const paymentCollectionId = collectionResult.rows[0].payment_collection_id;
      console.log(`Found payment collection: ${paymentCollectionId}`);
      
      // Find payment sessions
      const sessionsQuery = `
        SELECT id, status, provider_id
        FROM payment_session
        WHERE payment_collection_id = $1
      `;
      
      const sessionsResult = await client.query(sessionsQuery, [paymentCollectionId]);
      
      console.log(`Found ${sessionsResult.rows.length} payment sessions`);
      
      if (sessionsResult.rows.length > 0) {
        console.log("Payment sessions:", sessionsResult.rows);
        
        // Delete payment sessions
        const deleteQuery = `
          DELETE FROM payment_session
          WHERE payment_collection_id = $1
        `;
        
        const deleteResult = await client.query(deleteQuery, [paymentCollectionId]);
        console.log(`Deleted ${deleteResult.rowCount} payment sessions`);
        
        // Optionally delete the payment collection too
        const deleteCollectionQuery = `
          DELETE FROM payment_collection
          WHERE id = $1
        `;
        
        await client.query(deleteCollectionQuery, [paymentCollectionId]);
        console.log("Deleted payment collection");
        
        // Delete cart-payment collection link
        const deleteLinkQuery = `
          DELETE FROM cart_payment_collection
          WHERE cart_id = $1
        `;
        
        await client.query(deleteLinkQuery, [CART_ID]);
        console.log("Deleted cart-payment collection link");
        
        console.log("✅ All payment data cleared for cart");
        console.log("You can now try cart completion again");
      }
    });
    
  } catch (error) {
    console.error("Error clearing payment sessions:", error);
  }
}

if (require.main === module) {
  clearPaymentSessions().catch(console.error);
}

module.exports = { clearPaymentSessions };
