#!/usr/bin/env node

/**
 * Test script to verify the stay details card fixes
 * Tests:
 * 1. Duplicate removal - only sidebar version should exist
 * 2. Variant name display - room name should be prominent, variant ID less prominent
 * 
 * Run with: node scripts/test-stay-details-fixes.js
 */

const fs = require('fs');
const path = require('path');

function readFileContent(filePath) {
  try {
    return fs.readFileSync(path.join(__dirname, '..', filePath), 'utf8');
  } catch (error) {
    console.error(`❌ Could not read file ${filePath}:`, error.message);
    return null;
  }
}

function testDuplicateRemoval() {
  console.log('📋 Testing duplicate stay details removal...');
  
  const pageClientContent = readFileContent('src/admin/routes/concierge-management/bookings/[id]/page-client.tsx');
  if (!pageClientContent) return false;
  
  // Check that StayDetailsSection is not imported in page-client.tsx
  const hasStayDetailsSectionImport = pageClientContent.includes('import StayDetailsSection');
  
  // Check that StayDetailsSection is not used in the main content area
  const hasStayDetailsSectionUsage = pageClientContent.includes('<StayDetailsSection');
  
  // Check that the sidebar still has the stay details (in itinerary section)
  const itinerarySectionContent = readFileContent('src/admin/routes/concierge-management/bookings/[id]/components/concierge-booking-itinerary-section.tsx');
  const sidebarHasStayDetails = itinerarySectionContent && itinerarySectionContent.includes('StayDetailsSection');
  
  if (!hasStayDetailsSectionImport && !hasStayDetailsSectionUsage && sidebarHasStayDetails) {
    console.log('  ✅ Duplicate stay details successfully removed from main content');
    console.log('  ✅ Stay details still present in sidebar');
    return true;
  } else {
    console.log('  ❌ Duplicate removal failed:');
    console.log(`    - Import removed: ${!hasStayDetailsSectionImport}`);
    console.log(`    - Usage removed: ${!hasStayDetailsSectionUsage}`);
    console.log(`    - Sidebar still has stay details: ${sidebarHasStayDetails}`);
    return false;
  }
}

function testVariantNameDisplay() {
  console.log('\n📋 Testing variant name display fix...');
  
  const stayDetailsCardContent = readFileContent('src/admin/routes/concierge-management/bookings/[id]/components/stay-details-card.tsx');
  if (!stayDetailsCardContent) return false;
  
  // Check that the room name is now prominently displayed
  const hasProminentRoomName = stayDetailsCardContent.includes('font-semibold text-lg') && 
                               stayDetailsCardContent.includes('{roomName}');
  
  // Check that variant ID is now less prominent (smaller text, muted)
  const hasLessProminentVariantId = stayDetailsCardContent.includes('text-xs text-muted-foreground font-mono') &&
                                   stayDetailsCardContent.includes('{orderItem.variant_id}');
  
  // Check the structure - room name should come before variant ID in the DOM
  const roomNameIndex = stayDetailsCardContent.indexOf('{roomName}');
  const variantIdIndex = stayDetailsCardContent.indexOf('{orderItem.variant_id}');
  const roomNameComesFirst = roomNameIndex < variantIdIndex && roomNameIndex !== -1 && variantIdIndex !== -1;
  
  // Check that the comment indicates the primary display
  const hasCorrectComment = stayDetailsCardContent.includes('Room Name (Primary Display)');
  
  if (hasProminentRoomName && hasLessProminentVariantId && roomNameComesFirst && hasCorrectComment) {
    console.log('  ✅ Room name is now prominently displayed');
    console.log('  ✅ Variant ID is now less prominent');
    console.log('  ✅ Room name appears before variant ID');
    console.log('  ✅ Correct comment structure');
    return true;
  } else {
    console.log('  ❌ Variant name display fix failed:');
    console.log(`    - Prominent room name: ${hasProminentRoomName}`);
    console.log(`    - Less prominent variant ID: ${hasLessProminentVariantId}`);
    console.log(`    - Room name comes first: ${roomNameComesFirst}`);
    console.log(`    - Correct comment: ${hasCorrectComment}`);
    return false;
  }
}

function testComponentStructure() {
  console.log('\n📋 Testing component structure...');
  
  const stayDetailsCardContent = readFileContent('src/admin/routes/concierge-management/bookings/[id]/components/stay-details-card.tsx');
  if (!stayDetailsCardContent) return false;
  
  // Check that the component still has all required sections
  const hasPriceBreakdown = stayDetailsCardContent.includes('Price Breakdown');
  const hasStayDates = stayDetailsCardContent.includes('Stay Dates');
  const hasHotelInformation = stayDetailsCardContent.includes('Hotel Information');
  
  // Check that utilities are still imported and used
  const hasUtilityImports = stayDetailsCardContent.includes('formatStayDates') &&
                           stayDetailsCardContent.includes('formatCurrency') &&
                           stayDetailsCardContent.includes('getRoomName');
  
  if (hasPriceBreakdown && hasStayDates && hasHotelInformation && hasUtilityImports) {
    console.log('  ✅ All component sections present');
    console.log('  ✅ Utility functions still imported and used');
    return true;
  } else {
    console.log('  ❌ Component structure issues:');
    console.log(`    - Price breakdown: ${hasPriceBreakdown}`);
    console.log(`    - Stay dates: ${hasStayDates}`);
    console.log(`    - Hotel information: ${hasHotelInformation}`);
    console.log(`    - Utility imports: ${hasUtilityImports}`);
    return false;
  }
}

function testExpectedBehavior() {
  console.log('\n📋 Testing expected behavior...');
  
  // Mock order item to test the display logic
  const mockOrderItem = {
    id: 'item_1',
    variant_id: 'variant_01JWR4QCD4518KJ41F7Y1M5EH6',
    title: 'Deluxe Single Room',
    quantity: 1,
    unit_price: 11500,
    metadata: {
      hotel_name: 'Hotel Tschuggen',
      room_config_name: 'Deluxe Configuration',
      check_in_date: '2025-08-01',
      check_out_date: '2025-08-05',
      occupancy_type: 'Adult',
      meal_plan: 'No Meals'
    }
  };

  console.log('  ✅ Expected display hierarchy:');
  console.log('    1. 🏨 Deluxe Single Room (prominent, large text)');
  console.log('    2.    Deluxe Configuration (secondary, muted)');
  console.log('    3.    variant_01JWR4QCD4518KJ41F7Y1M5EH6 (small, muted, for reference)');
  console.log('    4. 📅 Aug 1, 2025 – Aug 5, 2025 (4 nights)');
  console.log('    5. 💰 $115.00/night × 4 = $460.00');
  console.log('    6. 🏨 Hotel Tschuggen');
  console.log('    7. 👤 Adult');
  console.log('    8. 🍽️ No Meals');
  
  return true;
}

// Run all tests
function runTests() {
  console.log('🧪 Testing Stay Details Card Fixes...\n');
  
  const results = [
    testDuplicateRemoval(),
    testVariantNameDisplay(),
    testComponentStructure(),
    testExpectedBehavior()
  ];
  
  const allPassed = results.every(result => result);
  
  console.log('\n🎉 Stay details fixes test completed!');
  console.log('\n📝 Summary:');
  
  if (allPassed) {
    console.log('✅ All tests passed! The stay details card fixes are working correctly.');
    console.log('\n📋 Fixed Issues:');
    console.log('  1. ✅ Removed duplicate stay details from main content area');
    console.log('  2. ✅ Fixed variant name display - room name is now prominent');
    console.log('  3. ✅ Variant ID is now less prominent and for reference only');
    console.log('  4. ✅ Stay details only appear in sidebar (no duplication)');
    console.log('\n🎯 Expected User Experience:');
    console.log('  - Users see stay details only once (in sidebar)');
    console.log('  - Room name is the primary identifier (large, bold)');
    console.log('  - Variant ID is available but not distracting');
    console.log('  - Clean, professional layout matching design requirements');
  } else {
    console.log('❌ Some tests failed. Please review the implementation.');
  }
  
  return allPassed;
}

// Run the tests
runTests();
