import { ExecArgs } from "@camped-ai/framework/types";
import { NOTIFICATION_TEMPLATE_SERVICE } from "../src/modules/notification-template/service";
import NotificationTemplateService from "../src/modules/notification-template/service";

export default async function permanentlyDisableOrderCreatedTemplate({
  container,
}: ExecArgs) {
  console.log("🔧 Permanently disabling order-created-template to prevent errors...");

  try {
    const notificationTemplateService: NotificationTemplateService = container.resolve(
      NOTIFICATION_TEMPLATE_SERVICE
    );

    // Delete all order-created-template templates
    const orderCreatedTemplateTemplates = await notificationTemplateService.listNotificationTemplates({
      event_name: "order-created-template"
    });
    
    console.log(`Found ${orderCreatedTemplateTemplates.length} order-created-template templates`);
    
    for (const template of orderCreatedTemplateTemplates) {
      if (template.id) {
        await notificationTemplateService.deleteNotificationTemplates([template.id]);
        console.log(`✅ Deleted template ${template.id} for order-created-template`);
      }
    }

    // Also delete order.created templates to be safe
    const orderCreatedTemplates = await notificationTemplateService.listNotificationTemplates({
      event_name: "order.created"
    });
    
    console.log(`Found ${orderCreatedTemplates.length} order.created templates`);
    
    for (const template of orderCreatedTemplates) {
      if (template.id) {
        await notificationTemplateService.deleteNotificationTemplates([template.id]);
        console.log(`✅ Deleted template ${template.id} for order.created`);
      }
    }

    console.log("🎉 Successfully deleted all problematic notification templates");
    console.log("ℹ️ The configurable notifications system will no longer try to process order.created events");
    
  } catch (error) {
    console.error("❌ Error deleting notification templates:", error);
    throw error;
  }
}
