import { ExecArgs } from "@camped-ai/framework/types";
import { NOTIFICATION_TEMPLATE_SERVICE } from "../src/modules/notification-template/service";
import NotificationTemplateService from "../src/modules/notification-template/service";

export default async function createMinimalOrderCreatedTemplate({
  container,
}: ExecArgs) {
  console.log("🔧 Creating minimal order-created-template to prevent errors...");

  try {
    const notificationTemplateService: NotificationTemplateService = container.resolve(
      NOTIFICATION_TEMPLATE_SERVICE
    );

    // Create a minimal template that won't cause errors but is disabled
    const minimalTemplate = await notificationTemplateService.createNotificationTemplates({
      event_name: "order-created-template",
      channel: "email",
      subject: "Order Created",
      content: "<p>Order created.</p>", // Minimal content without placeholders
      is_default: true,
      is_active: false, // Keep it disabled
    });

    console.log(`✅ Created minimal order-created-template: ${minimalTemplate.id}`);
    console.log("ℹ️ This template is disabled and won't send notifications");
    console.log("ℹ️ It exists only to prevent the configurable notifications system from failing");

    console.log("🎉 Successfully created minimal template to prevent errors");
    
  } catch (error) {
    console.error("❌ Error creating minimal template:", error);
    throw error;
  }
}
