#!/usr/bin/env node

/**
 * Verification script for Universal Order Synchronization Implementation
 * This script verifies that the implementation meets all specified requirements
 * Run with: node scripts/verify-universal-sync-implementation.js
 */

const fs = require('fs');
const path = require('path');

function verifyImplementation() {
  console.log("🔍 Verifying Universal Order Synchronization Implementation");
  console.log("=" .repeat(60));
  
  const results = {
    passed: 0,
    failed: 0,
    details: []
  };
  
  function checkRequirement(description, condition, details = "") {
    if (condition) {
      console.log(`✅ ${description}`);
      results.passed++;
      results.details.push({ status: "PASS", description, details });
    } else {
      console.log(`❌ ${description}`);
      results.failed++;
      results.details.push({ status: "FAIL", description, details });
    }
  }
  
  // Requirement 1: Event Listener exists
  console.log("\n📋 Requirement 1: Event Listener for Order Creation");
  const conciergeSubscriberPath = path.join(__dirname, '../src/subscribers/concierge-order-creation.ts');
  const conciergeSubscriberExists = fs.existsSync(conciergeSubscriberPath);
  checkRequirement(
    "Concierge order creation subscriber exists",
    conciergeSubscriberExists,
    conciergeSubscriberPath
  );
  
  if (conciergeSubscriberExists) {
    const conciergeSubscriberContent = fs.readFileSync(conciergeSubscriberPath, 'utf8');
    
    checkRequirement(
      "Subscriber listens to 'order.placed' event",
      conciergeSubscriberContent.includes('event: "order.placed"'),
      "Found order.placed event configuration"
    );
    
    checkRequirement(
      "Subscriber has proper export configuration",
      conciergeSubscriberContent.includes('export const config: SubscriberConfig'),
      "Found SubscriberConfig export"
    );
  }
  
  // Requirement 2: Automatic Creation Logic
  console.log("\n📋 Requirement 2: Automatic Creation of Concierge Records");
  if (conciergeSubscriberExists) {
    const conciergeSubscriberContent = fs.readFileSync(conciergeSubscriberPath, 'utf8');
    
    checkRequirement(
      "Creates concierge_order records",
      conciergeSubscriberContent.includes('CreateConciergeOrderWorkflow') || 
      conciergeSubscriberContent.includes('createConciergeOrder'),
      "Found concierge order creation logic"
    );
    
    checkRequirement(
      "Creates concierge_order_item records",
      conciergeSubscriberContent.includes('createConciergeOrderItem'),
      "Found concierge order item creation logic"
    );
    
    checkRequirement(
      "Processes all line items",
      conciergeSubscriberContent.includes('for (const lineItem of order.items)') ||
      conciergeSubscriberContent.includes('order.items.forEach'),
      "Found line item processing logic"
    );
  }
  
  // Requirement 3: No Conditional Logic
  console.log("\n📋 Requirement 3: No Conditional Logic");
  if (conciergeSubscriberExists) {
    const conciergeSubscriberContent = fs.readFileSync(conciergeSubscriberPath, 'utf8');
    
    checkRequirement(
      "No evaluateOrderForConciergeHandling function",
      !conciergeSubscriberContent.includes('evaluateOrderForConciergeHandling'),
      "Conditional evaluation function removed"
    );
    
    checkRequirement(
      "No is_draft_order checks",
      !conciergeSubscriberContent.includes('is_draft_order'),
      "No draft order filtering"
    );
    
    checkRequirement(
      "No order type validations",
      !conciergeSubscriberContent.includes('HIGH_VALUE_THRESHOLD') &&
      !conciergeSubscriberContent.includes('special_requests') &&
      !conciergeSubscriberContent.includes('number_of_rooms') &&
      !conciergeSubscriberContent.includes('vip_customer'),
      "No conditional filtering based on order characteristics"
    );
    
    checkRequirement(
      "Universal sync metadata present",
      conciergeSubscriberContent.includes('universal_sync: true'),
      "Found universal sync flag in metadata"
    );
  }
  
  // Requirement 4: Universal Trigger
  console.log("\n📋 Requirement 4: Universal Trigger for All Orders");
  if (conciergeSubscriberExists) {
    const conciergeSubscriberContent = fs.readFileSync(conciergeSubscriberPath, 'utf8');
    
    checkRequirement(
      "No conditional return statements for order filtering",
      !conciergeSubscriberContent.includes('shouldCreateConciergeOrder.required') &&
      !conciergeSubscriberContent.includes('if (!shouldCreate'),
      "No early returns based on order criteria"
    );
    
    checkRequirement(
      "Comments indicate universal creation",
      conciergeSubscriberContent.includes('universal') || 
      conciergeSubscriberContent.includes('ALL orders'),
      "Documentation indicates universal order processing"
    );
  }
  
  // Requirement 5: Duplicate Logic Removal
  console.log("\n📋 Requirement 5: Consolidated Logic");
  const orderPlacedPath = path.join(__dirname, '../src/subscribers/order-placed.ts');
  const orderPlacedExists = fs.existsSync(orderPlacedPath);
  
  if (orderPlacedExists) {
    const orderPlacedContent = fs.readFileSync(orderPlacedPath, 'utf8');
    
    checkRequirement(
      "Duplicate concierge creation logic removed from order-placed.ts",
      !orderPlacedContent.includes('createConciergeOrderEntry') &&
      !orderPlacedContent.includes('CONCIERGE_MANAGEMENT_MODULE'),
      "Concierge creation logic removed from order-placed subscriber"
    );
    
    checkRequirement(
      "Order-placed subscriber still handles other responsibilities",
      orderPlacedContent.includes('notification') || orderPlacedContent.includes('email'),
      "Order-placed subscriber maintains its primary email notification functionality"
    );
  }
  
  // Summary
  console.log("\n" + "=" .repeat(60));
  console.log("📊 VERIFICATION SUMMARY");
  console.log("=" .repeat(60));
  
  console.log(`✅ Requirements Passed: ${results.passed}`);
  console.log(`❌ Requirements Failed: ${results.failed}`);
  console.log(`📈 Success Rate: ${Math.round((results.passed / (results.passed + results.failed)) * 100)}%`);
  
  if (results.failed === 0) {
    console.log("\n🎉 ALL REQUIREMENTS VERIFIED SUCCESSFULLY!");
    console.log("✅ Universal Order Synchronization is properly implemented");
    console.log("✅ No conditional logic prevents order synchronization");
    console.log("✅ Both concierge_order and concierge_order_item records are created");
    console.log("✅ System will work for ALL order creation methods");
  } else {
    console.log("\n⚠️  SOME REQUIREMENTS NOT MET");
    console.log("Please review the failed requirements above");
  }
  
  console.log("\n📝 Implementation Details:");
  console.log("• Event: order.placed");
  console.log("• Subscriber: concierge-order-creation.ts");
  console.log("• Creates: concierge_order + concierge_order_item records");
  console.log("• Scope: ALL orders (no filtering)");
  console.log("• Duplicate prevention: Checks for existing concierge orders");
  
  return results.failed === 0;
}

// Run verification
const success = verifyImplementation();
process.exit(success ? 0 : 1);
