import { ExecArgs } from "@camped-ai/framework/types";
import { NOTIFICATION_TEMPLATE_SERVICE } from "../src/modules/notification-template/service";
import NotificationTemplateService from "../src/modules/notification-template/service";

export default async function createOrderCreatedTemplate({
  container,
}: ExecArgs) {
  console.log("🔧 Creating order-created-template notification template...");

  try {
    const notificationTemplateService: NotificationTemplateService = container.resolve(
      NOTIFICATION_TEMPLATE_SERVICE
    );

    // Check if order-created-template already exists
    const existingTemplates = await notificationTemplateService.listNotificationTemplates({
      event_name: "order-created-template"
    });

    if (existingTemplates.length > 0) {
      console.log(`Found ${existingTemplates.length} existing order-created-template templates`);
      console.log("Templates already exist, skipping creation");
    } else {
      // Create a new inactive template for order-created-template
      const newTemplate = await notificationTemplateService.createNotificationTemplates({
        event_name: "order-created-template",
        channel: "email",
        subject: "Order Created",
        content: "<p>Order {{order_id}} has been created.</p>",
        is_default: true,
        is_active: false, // Keep it disabled to prevent notifications
      });

      console.log(`✅ Created new order-created-template: ${newTemplate.id}`);
    }

    // Also ensure order.created template exists and is disabled
    const orderCreatedTemplates = await notificationTemplateService.listNotificationTemplates({
      event_name: "order.created"
    });

    if (orderCreatedTemplates.length === 0) {
      const orderCreatedTemplate = await notificationTemplateService.createNotificationTemplates({
        event_name: "order.created",
        channel: "email",
        subject: "Order Created",
        content: "<p>Order {{order_id}} has been created.</p>",
        is_default: true,
        is_active: false, // Keep it disabled
      });
      console.log(`✅ Created new order.created template: ${orderCreatedTemplate.id}`);
    } else {
      console.log(`Found ${orderCreatedTemplates.length} existing order.created templates`);
      console.log("Templates already exist, skipping creation");
    }

    console.log("🎉 Successfully created/updated order-created-template");
    
  } catch (error) {
    console.error("❌ Error creating order-created-template:", error);
    throw error;
  }
}
