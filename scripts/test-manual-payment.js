#!/usr/bin/env node

/**
 * Test script for manual payment completion
 */

const fetch = require('node-fetch');

const BASE_URL = process.env.MEDUSA_URL || 'http://localhost:9000';
const PUBLISHABLE_API_KEY = process.env.PUBLISHABLE_API_KEY || 'pk_test_123';

// Your cart ID
const CART_ID = process.env.CART_ID || 'cart_01K17FHQPRKCZJ2BW5M6JF9AET';

async function testManualPayment() {
  console.log("🧪 Testing Manual Payment Completion");
  console.log("=" .repeat(50));
  console.log(`🛒 Cart ID: ${CART_ID}`);
  console.log("=" .repeat(50));

  try {
    // Test manual payment completion
    console.log("\n1️⃣ Testing manual payment completion...");
    
    const manualPayload = {
      payment_type: "manual",
      partial_amount: 100, // £1 minimal payment
      payment_provider_id: "pp_stripe_stripe",
      metadata: {
        collection_method: "sales_team",
        test_run: true,
        notes: "Quote approval - full payment to be collected manually"
      }
    };

    console.log("Payload:", JSON.stringify(manualPayload, null, 2));

    const response = await fetch(`${BASE_URL}/store/carts/${CART_ID}/complete-with-payment`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'x-publishable-api-key': PUBLISHABLE_API_KEY
      },
      body: JSON.stringify(manualPayload)
    });

    console.log(`📊 Response: ${response.status} ${response.statusText}`);

    const responseData = await response.json();

    if (response.ok) {
      console.log("🎉 SUCCESS! Manual payment completed!");
      
      console.log("\n📋 Order Details:");
      console.log(`   Order ID: ${responseData.order?.id}`);
      console.log(`   Total: ${responseData.order?.total} ${responseData.order?.currency_code}`);
      console.log(`   Status: ${responseData.order?.status}`);

      console.log("\n💰 Payment Information:");
      console.log(`   Payment Type: ${responseData.payment_info?.payment_type}`);
      console.log(`   Paid Amount: ${responseData.payment_info?.paid_amount} ${responseData.payment_info?.currency_code}`);
      console.log(`   Remaining: ${responseData.payment_info?.remaining_amount} ${responseData.payment_info?.currency_code}`);
      console.log(`   Requires Additional Payment: ${responseData.payment_info?.requires_additional_payment ? 'Yes' : 'No'}`);

      console.log("\n📝 Message:");
      console.log(`   ${responseData.message}`);

      if (responseData.next_steps) {
        console.log("\n📋 Next Steps:");
        responseData.next_steps.forEach((step, index) => {
          console.log(`   ${index + 1}. ${step}`);
        });
      }

      console.log("\n✅ Manual Payment Flow Complete:");
      console.log("   - Order created with minimal payment ✓");
      console.log("   - Sales team can now collect full payment ✓");
      console.log("   - Use admin API to mark payment as collected ✓");

      // Show example of marking payment as collected
      console.log("\n💡 Next: Mark payment as collected via admin API:");
      console.log(`
curl -X POST ${BASE_URL}/admin/orders/${responseData.order?.id}/mark-payment-collected \\
  -H "Content-Type: application/json" \\
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN" \\
  -d '{
    "amount": ${responseData.payment_info?.remaining_amount},
    "payment_method": "bank_transfer",
    "collected_by": "sales_team",
    "reference_number": "BANK_TXN_123",
    "notes": "Customer paid full amount via bank transfer"
  }'
      `);

    } else {
      console.log("❌ FAILED! Manual payment completion failed");
      console.log("\n📄 Error Response:");
      console.log(JSON.stringify(responseData, null, 2));

      if (responseData.message) {
        console.log(`\n💬 Error: ${responseData.message}`);
      }
    }

  } catch (error) {
    console.log("❌ NETWORK/PARSING ERROR!");
    console.log(`Error: ${error.message}`);
    
    console.log("\n🔧 Troubleshooting:");
    console.log("1. Check if Medusa server is running");
    console.log("2. Verify the cart ID exists");
    console.log("3. Check server logs for detailed errors");
  }
}

// Test deposit payment too
async function testDepositPayment() {
  console.log("\n\n🧪 Testing Deposit Payment");
  console.log("=" .repeat(40));

  // Note: This would require a fresh cart
  const depositPayload = {
    payment_type: "deposit",
    partial_amount: 20000, // £200 deposit
    payment_provider_id: "pp_stripe_stripe"
  };

  console.log("💡 Deposit payload (use with fresh cart):");
  console.log(JSON.stringify(depositPayload, null, 2));
  console.log("⚠️ Note: This requires a fresh cart ID");
}

// Run tests
async function runTests() {
  await testManualPayment();
  await testDepositPayment();
  
  console.log("\n🎯 Summary");
  console.log("=" .repeat(50));
  console.log("Manual payment completion allows:");
  console.log("1. Quote approval with minimal payment");
  console.log("2. Order creation for sales team workflow");
  console.log("3. Manual payment collection tracking");
  console.log("4. Full payment marking via admin API");
}

if (require.main === module) {
  runTests().catch(console.error);
}

module.exports = { testManualPayment, testDepositPayment };
