#!/usr/bin/env node

/**
 * Test script for cart implementation standards compliance
 * This script tests the cart creation flow with Medusa.js standards
 */

const fetch = require('node-fetch');

// Configuration
const BASE_URL = process.env.MEDUSA_URL || 'http://localhost:9000';
const PUBLISHABLE_API_KEY = process.env.PUBLISHABLE_API_KEY || 'pk_test_123';

// Test data
const TEST_SCENARIOS = [
  {
    name: "Valid Cart Creation",
    data: {
      region_id: "reg_test_123",
      currency_code: "CHF",
      customer_id: "cus_test_123",
      email: "<EMAIL>",
      items: [
        {
          variant_id: "variant_room_101",
          quantity: 1,
          metadata: {
            check_in: "2024-08-01",
            check_out: "2024-08-03"
          }
        }
      ],
      metadata: {
        cart_type: "quote",
        created_by: "crm_test"
      }
    },
    expectedStatus: 200
  },
  {
    name: "Missing Region ID",
    data: {
      currency_code: "CHF",
      email: "<EMAIL>"
    },
    expectedStatus: 400
  },
  {
    name: "Missing Currency Code", 
    data: {
      region_id: "reg_test_123",
      email: "<EMAIL>"
    },
    expectedStatus: 400
  },
  {
    name: "Invalid Currency Code",
    data: {
      region_id: "reg_test_123",
      currency_code: "INVALID",
      email: "<EMAIL>"
    },
    expectedStatus: 400
  },
  {
    name: "Custom Pricing Quote",
    data: {
      region_id: "reg_test_123",
      currency_code: "CHF",
      email: "<EMAIL>",
      items: [
        {
          variant_id: "variant_room_101",
          quantity: 1,
          unit_price: 25000, // Custom price for quote
          title: "Deluxe Room - Special Rate"
        }
      ],
      metadata: {
        cart_type: "quote",
        quote_expires_at: "2024-08-15T00:00:00Z"
      }
    },
    expectedStatus: 200
  }
];

async function testCartStandards() {
  console.log("🧪 Testing Cart Standards Compliance");
  console.log("=" .repeat(50));
  console.log(`🌐 Base URL: ${BASE_URL}`);
  console.log(`🔑 API Key: ${PUBLISHABLE_API_KEY ? 'Configured' : 'Missing'}`);
  console.log("=" .repeat(50));

  let passedTests = 0;
  let totalTests = TEST_SCENARIOS.length;

  // Test 1: Check if regions endpoint works
  console.log("\n1️⃣ Testing Regions Endpoint");
  try {
    const regionsResponse = await fetch(`${BASE_URL}/store/regions`, {
      headers: {
        'x-publishable-api-key': PUBLISHABLE_API_KEY
      }
    });

    if (regionsResponse.ok) {
      const regionsData = await regionsResponse.json();
      console.log(`   ✅ Regions endpoint working (${regionsData.count} regions found)`);
      
      if (regionsData.regions && regionsData.regions.length > 0) {
        const defaultRegion = regionsData.regions.find(r => r.is_default);
        if (defaultRegion) {
          console.log(`   📍 Default region: ${defaultRegion.name} (${defaultRegion.currency_code})`);
          
          // Update test scenarios with real region ID
          TEST_SCENARIOS.forEach(scenario => {
            if (scenario.data.region_id === "reg_test_123") {
              scenario.data.region_id = defaultRegion.id;
            }
          });
        }
      }
    } else {
      console.log(`   ⚠️  Regions endpoint returned ${regionsResponse.status}`);
    }
  } catch (error) {
    console.log(`   ❌ Regions endpoint failed: ${error.message}`);
  }

  // Test 2: Run cart creation scenarios
  console.log("\n2️⃣ Testing Cart Creation Scenarios");
  
  for (let i = 0; i < TEST_SCENARIOS.length; i++) {
    const scenario = TEST_SCENARIOS[i];
    console.log(`\n   Test ${i + 1}: ${scenario.name}`);
    
    try {
      const response = await fetch(`${BASE_URL}/store/carts`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-publishable-api-key': PUBLISHABLE_API_KEY
        },
        body: JSON.stringify(scenario.data)
      });

      const responseData = await response.json();
      
      if (response.status === scenario.expectedStatus) {
        console.log(`   ✅ Expected status ${scenario.expectedStatus}, got ${response.status}`);
        
        if (response.ok && responseData.cart) {
          console.log(`   📋 Cart created: ${responseData.cart.id}`);
          console.log(`   💰 Currency: ${responseData.cart.currency_code}`);
          console.log(`   🌍 Region: ${responseData.cart.region_id}`);
          
          if (responseData.cart.metadata?.custom_pricing) {
            console.log(`   💵 Custom pricing enabled`);
          }
        }
        
        passedTests++;
      } else {
        console.log(`   ❌ Expected status ${scenario.expectedStatus}, got ${response.status}`);
        console.log(`   📄 Response: ${JSON.stringify(responseData, null, 2)}`);
      }
      
    } catch (error) {
      console.log(`   ❌ Test failed: ${error.message}`);
    }
  }

  // Test 3: Test cart completion flow
  console.log("\n3️⃣ Testing Cart Completion Flow");
  try {
    // First create a valid cart
    const cartResponse = await fetch(`${BASE_URL}/store/carts`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'x-publishable-api-key': PUBLISHABLE_API_KEY
      },
      body: JSON.stringify(TEST_SCENARIOS[0].data)
    });

    if (cartResponse.ok) {
      const cartData = await cartResponse.json();
      const cartId = cartData.cart.id;
      
      console.log(`   📋 Created test cart: ${cartId}`);
      
      // Note: We won't actually complete the cart in this test
      // as it requires payment setup, but we can test the endpoint exists
      console.log(`   ℹ️  Cart completion endpoint: POST /store/carts/${cartId}/complete`);
      console.log(`   ℹ️  Payment setup endpoint: POST /store/hotel-management/cart/payment`);
      
    } else {
      console.log(`   ❌ Failed to create test cart for completion test`);
    }
    
  } catch (error) {
    console.log(`   ❌ Cart completion test failed: ${error.message}`);
  }

  // Summary
  console.log("\n📊 Test Results Summary");
  console.log("=" .repeat(50));
  console.log(`✅ Passed: ${passedTests}/${totalTests} tests`);
  console.log(`❌ Failed: ${totalTests - passedTests}/${totalTests} tests`);
  
  if (passedTests === totalTests) {
    console.log("🎉 All tests passed! Cart implementation is Medusa.js standards compliant.");
  } else {
    console.log("⚠️  Some tests failed. Please review the implementation.");
  }

  // Recommendations
  console.log("\n💡 Recommendations");
  console.log("=" .repeat(50));
  console.log("1. Ensure regions and currencies are properly configured");
  console.log("2. Set up publishable API keys for different sales channels");
  console.log("3. Configure default region for fallback scenarios");
  console.log("4. Test cart completion flow with payment integration");
  console.log("5. Monitor cart creation logs for validation warnings");
  console.log("6. Set up cart abandonment tracking for quotes");

  return passedTests === totalTests;
}

// Helper function to create sample regions (for development)
async function createSampleRegions() {
  console.log("\n🏗️  Creating sample regions for testing...");
  
  const regions = [
    {
      name: "Switzerland",
      currency_code: "CHF",
      countries: ["ch"],
      metadata: { is_default: true, tax_rate: 7.7 }
    },
    {
      name: "European Union", 
      currency_code: "EUR",
      countries: ["de", "fr", "it"],
      metadata: { is_default: false, tax_rate: 20.0 }
    }
  ];

  // Note: This would require admin authentication
  console.log("   ℹ️  Use Medusa Admin to create these regions:");
  regions.forEach((region, index) => {
    console.log(`   ${index + 1}. ${region.name} (${region.currency_code})`);
  });
}

// Run tests if script is executed directly
if (require.main === module) {
  testCartStandards()
    .then(success => {
      process.exit(success ? 0 : 1);
    })
    .catch(error => {
      console.error("Test execution failed:", error);
      process.exit(1);
    });
}

module.exports = {
  testCartStandards,
  createSampleRegions,
  TEST_SCENARIOS
};
