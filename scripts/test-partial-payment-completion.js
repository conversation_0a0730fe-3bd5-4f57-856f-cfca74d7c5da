#!/usr/bin/env node

/**
 * Test script for partial payment cart completion
 */

const fetch = require('node-fetch');

const BASE_URL = process.env.MEDUSA_URL || 'http://localhost:9000';
const PUBLISHABLE_API_KEY = process.env.PUBLISHABLE_API_KEY || 'pk_test_123';

// Your cart ID (replace with actual cart ID)
const CART_ID = process.env.CART_ID || 'cart_01JXJ7K6W55Y74WAQ3QQG1WFDA';

async function testPartialPaymentCompletion() {
  console.log("🧪 Testing Partial Payment Cart Completion");
  console.log("=" .repeat(60));
  console.log(`🌐 URL: ${BASE_URL}`);
  console.log(`🛒 Cart ID: ${CART_ID}`);
  console.log("=" .repeat(60));

  try {
    // Test 1: Complete cart with deposit (20% default)
    console.log("\n1️⃣ Testing cart completion with deposit...");
    
    const depositPayload = {
      payment_type: "deposit",
      payment_provider_id: "pp_stripe_stripe", // Use Stripe for all payments
      metadata: {
        collection_method: "sales_team",
        test_run: true
      }
    };

    console.log("Payload:", JSON.stringify(depositPayload, null, 2));

    const response = await fetch(`${BASE_URL}/store/carts/${CART_ID}/complete-with-payment`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'x-publishable-api-key': PUBLISHABLE_API_KEY
      },
      body: JSON.stringify(depositPayload)
    });

    console.log(`📊 Response: ${response.status} ${response.statusText}`);

    const responseData = await response.json();

    if (response.ok) {
      console.log("🎉 SUCCESS! Cart completed with partial payment!");
      
      console.log("\n📋 Order Details:");
      console.log(`   Order ID: ${responseData.order?.id}`);
      console.log(`   Total: ${responseData.order?.total} ${responseData.order?.currency_code}`);
      console.log(`   Status: ${responseData.order?.status}`);
      console.log(`   Customer: ${responseData.order?.email}`);

      console.log("\n💰 Payment Information:");
      console.log(`   Payment Type: ${responseData.payment_info?.payment_type}`);
      console.log(`   Paid Amount: ${responseData.payment_info?.paid_amount} ${responseData.payment_info?.currency_code}`);
      console.log(`   Remaining: ${responseData.payment_info?.remaining_amount} ${responseData.payment_info?.currency_code}`);
      console.log(`   Requires Additional Payment: ${responseData.payment_info?.requires_additional_payment ? 'Yes' : 'No'}`);

      console.log("\n📝 Message:");
      console.log(`   ${responseData.message}`);

      if (responseData.next_steps) {
        console.log("\n📋 Next Steps:");
        responseData.next_steps.forEach((step, index) => {
          console.log(`   ${index + 1}. ${step}`);
        });
      }

      console.log("\n✅ Key Achievements:");
      console.log("   - Payment collection created ✓");
      console.log("   - Payment session authorized ✓");
      console.log("   - Cart completed successfully ✓");
      console.log("   - Order created ✓");
      console.log("   - Payment tracking metadata added ✓");
      console.log("   - Concierge workflow ready ✓");

    } else {
      console.log("❌ FAILED! Cart completion failed");
      console.log("\n📄 Error Response:");
      console.log(JSON.stringify(responseData, null, 2));

      // Check for specific error types
      if (responseData.message) {
        console.log(`\n💬 Error: ${responseData.message}`);
      }

      if (responseData.errors) {
        console.log("\n🔍 Validation Errors:");
        if (Array.isArray(responseData.errors)) {
          responseData.errors.forEach(error => {
            console.log(`   - ${error}`);
          });
        } else {
          console.log(JSON.stringify(responseData.errors, null, 2));
        }
      }
    }

  } catch (error) {
    console.log("❌ NETWORK/PARSING ERROR!");
    console.log(`Error: ${error.message}`);
    
    console.log("\n🔧 Troubleshooting:");
    console.log("1. Check if Medusa server is running (yarn dev)");
    console.log("2. Verify the cart ID exists and is valid");
    console.log("3. Check if the cart has items");
    console.log("4. Verify publishable API key");
    console.log("5. Check server logs for detailed errors");
  }
}

// Test different payment scenarios
async function testDifferentPaymentTypes() {
  console.log("\n\n🧪 Testing Different Payment Types");
  console.log("=" .repeat(50));

  const scenarios = [
    {
      name: "Custom Deposit Amount",
      payload: {
        payment_type: "deposit",
        partial_amount: 25000, // £250 deposit
        payment_provider_id: "pp_manual_manual"
      }
    },
    {
      name: "Manual Payment (Concierge Only)",
      payload: {
        payment_type: "manual",
        partial_amount: 100, // £1 minimal
        payment_provider_id: "pp_manual_manual"
      }
    },
    {
      name: "Full Payment",
      payload: {
        payment_type: "full",
        payment_provider_id: "pp_manual_manual"
      }
    }
  ];

  for (const scenario of scenarios) {
    console.log(`\n📋 Testing: ${scenario.name}`);
    console.log(`   Payload: ${JSON.stringify(scenario.payload, null, 2)}`);
    
    // Note: This would require different cart IDs for each test
    console.log(`   ⚠️ Would need cart ID: cart_${Date.now()}`);
    console.log(`   💡 Use this payload with a fresh cart for testing`);
  }
}

// Test payment collection creation separately
async function testPaymentCollectionOnly() {
  console.log("\n\n🧪 Testing Payment Collection Creation Only");
  console.log("=" .repeat(50));

  const payload = {
    payment_type: "deposit",
    partial_amount: 20000,
    payment_provider_id: "pp_manual_manual",
    metadata: {
      test_collection: true
    }
  };

  try {
    const response = await fetch(`${BASE_URL}/store/carts/${CART_ID}/payment-collection`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'x-publishable-api-key': PUBLISHABLE_API_KEY
      },
      body: JSON.stringify(payload)
    });

    console.log(`📊 Payment Collection Response: ${response.status}`);
    
    const data = await response.json();
    
    if (response.ok) {
      console.log("✅ Payment collection created successfully");
      console.log(`   Collection ID: ${data.payment_collection?.id}`);
      console.log(`   Amount: ${data.payment_amount} ${data.payment_collection?.currency_code}`);
      console.log(`   Remaining: ${data.remaining_amount}`);
    } else {
      console.log("❌ Payment collection failed");
      console.log(`   Error: ${data.message}`);
    }
  } catch (error) {
    console.log(`❌ Payment collection error: ${error.message}`);
  }
}

// Run all tests
async function runAllTests() {
  await testPartialPaymentCompletion();
  await testDifferentPaymentTypes();
  await testPaymentCollectionOnly();
  
  console.log("\n🎯 Summary");
  console.log("=" .repeat(60));
  console.log("The main solution for your 'Payment collection has not been initiated' error:");
  console.log("1. Use the new endpoint: POST /store/carts/{id}/complete-with-payment");
  console.log("2. Specify payment_type: 'deposit', 'full', or 'manual'");
  console.log("3. Cart will complete with partial payment support");
  console.log("4. Order created with payment tracking for concierge team");
  console.log("\nIf the test passed, your cart completion issue is resolved! 🎉");
}

// Usage instructions
function showUsage() {
  console.log("\n📖 Usage Instructions");
  console.log("=" .repeat(40));
  console.log("1. Set environment variables:");
  console.log("   export CART_ID='your_actual_cart_id'");
  console.log("   export MEDUSA_URL='http://localhost:9000'");
  console.log("   export PUBLISHABLE_API_KEY='your_api_key'");
  console.log("");
  console.log("2. Run the test:");
  console.log("   node scripts/test-partial-payment-completion.js");
  console.log("");
  console.log("3. Or test manually:");
  console.log("   curl -X POST http://localhost:9000/store/carts/YOUR_CART_ID/complete-with-payment \\");
  console.log("     -H 'Content-Type: application/json' \\");
  console.log("     -d '{\"payment_type\": \"deposit\", \"partial_amount\": 20000}'");
}

if (require.main === module) {
  if (process.argv.includes('--help') || process.argv.includes('-h')) {
    showUsage();
  } else {
    runAllTests().catch(console.error);
  }
}

module.exports = {
  testPartialPaymentCompletion,
  testDifferentPaymentTypes,
  testPaymentCollectionOnly
};
