#!/usr/bin/env node

/**
 * Quick test script to verify concierge API is working
 * Run with: node scripts/test-concierge-api.js
 */

const http = require('http');

function makeRequest(options, data = null) {
  return new Promise((resolve, reject) => {
    const req = http.request(options, (res) => {
      let body = '';
      res.on('data', (chunk) => {
        body += chunk;
      });
      res.on('end', () => {
        resolve({
          statusCode: res.statusCode,
          headers: res.headers,
          body: body
        });
      });
    });

    req.on('error', (err) => {
      reject(err);
    });

    if (data) {
      req.write(data);
    }
    req.end();
  });
}

async function testConciergeAPI() {
  console.log('🧪 Testing Concierge API...');
  
  try {
    // Test 1: Check if the endpoint exists (should return 401 Unauthorized)
    console.log('\n📋 Test 1: Check endpoint availability');
    const response = await makeRequest({
      hostname: 'localhost',
      port: 9000,
      path: '/admin/concierge-management/orders?limit=5',
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer invalid-token'
      }
    });

    console.log(`Status: ${response.statusCode}`);
    console.log(`Response: ${response.body}`);

    if (response.statusCode === 401) {
      console.log('✅ Endpoint is accessible (returns 401 as expected without valid auth)');
    } else if (response.statusCode === 500) {
      console.log('❌ Service resolution error - check if services are registered');
      console.log('Response body:', response.body);
    } else {
      console.log(`⚠️ Unexpected status code: ${response.statusCode}`);
    }

    // Test 2: Check if we can access the endpoint without auth (should still work for testing)
    console.log('\n📋 Test 2: Check service resolution');
    const response2 = await makeRequest({
      hostname: 'localhost',
      port: 9000,
      path: '/admin/concierge-management/orders',
      method: 'GET',
      headers: {
        'Content-Type': 'application/json'
      }
    });

    console.log(`Status: ${response2.statusCode}`);
    if (response2.statusCode === 401) {
      console.log('✅ Service is properly registered (auth middleware is working)');
    } else if (response2.statusCode === 500 && response2.body.includes('conciergeOrderService')) {
      console.log('❌ Service registration failed');
    } else {
      console.log('✅ Service appears to be working');
    }

    console.log('\n🎉 API test completed!');
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    
    if (error.code === 'ECONNREFUSED') {
      console.log('💡 Make sure the development server is running: npm run dev');
    }
  }
}

// Run the test
testConciergeAPI().catch(console.error);
