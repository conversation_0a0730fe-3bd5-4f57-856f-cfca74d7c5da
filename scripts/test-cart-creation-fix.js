#!/usr/bin/env node

/**
 * Test script to verify cart creation with customer_id works
 */

const fetch = require('node-fetch');

// Configuration
const BASE_URL = process.env.MEDUSA_URL || 'http://localhost:9000';
const PUBLISHABLE_API_KEY = process.env.PUBLISHABLE_API_KEY || 'pk_test_123';

// Your corrected payload (removed customer_id, Medusa uses email to associate with customer)
const TEST_PAYLOAD = {
  "region_id": "reg_01JXJ7K6W55Y74WAQ3QQG1WFDA",
  "currency_code": "GBP",
  "email": "<EMAIL>", // Medusa uses email to find/create customer association
  "sales_channel_id": "sc_01JNR887105TH162F04RB9RKC0",
  "items": [
    {
      "variant_id": "variant_01JWR2V7STJK8SDJK2E84G049N",
      "quantity": 2,
      "unit_price": 98000,
      "title": "Hotel Valsana - Lifestyle Double Room 150",
      "metadata": {
        "product_id": "prod_01JWR26K8BNVDX0A1KKETHSTB6",
        "item_type": "room",
        "hotel_id": "01JWR0DA3F4H9KZ0STCA2VKYXT"
      },
      "requires_shipping": false
    },
    {
      "variant_id": "variant_addon_ps_01K134Q6CGVW54F5K49R58NAVT",
      "quantity": 1,
      "unit_price": 4000,
      "title": "Add Ons - Transportation (Arrival) – Sprinter – 5 – (Per Day)",
      "metadata": {
        "product_id": "product_add_ons_main",
        "item_type": "add-ons",
        "hotel_id": ""
      },
      "requires_shipping": false
    },
    {
      "variant_id": "variant_addon_ps_01K1308S65GSSCBTREQPBXQWEN",
      "quantity": 2,
      "unit_price": 8200,
      "title": "Add Ons - Kids Club – 12-34 – Yeti Primer – 1 – 12 – Arosa, Grindelwald, Andermatt – (Per Day)",
      "metadata": {
        "product_id": "product_add_ons_main",
        "item_type": "add-ons",
        "hotel_id": ""
      },
      "requires_shipping": false
    }
  ]
};

async function testCartCreation() {
  console.log("🧪 Testing Cart Creation Fix");
  console.log("=" .repeat(50));
  console.log(`🌐 Base URL: ${BASE_URL}`);
  console.log(`🔑 API Key: ${PUBLISHABLE_API_KEY ? 'Configured' : 'Missing'}`);
  console.log("=" .repeat(50));

  try {
    console.log("\n📋 Testing cart creation with your exact payload...");
    console.log("Payload:", JSON.stringify(TEST_PAYLOAD, null, 2));

    const response = await fetch(`${BASE_URL}/store/carts`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'x-publishable-api-key': PUBLISHABLE_API_KEY
      },
      body: JSON.stringify(TEST_PAYLOAD)
    });

    console.log(`\n📊 Response Status: ${response.status} ${response.statusText}`);

    const responseData = await response.json();
    
    if (response.ok) {
      console.log("✅ SUCCESS! Cart created successfully");
      console.log(`📋 Cart ID: ${responseData.cart?.id}`);
      console.log(`💰 Currency: ${responseData.cart?.currency_code}`);
      console.log(`🌍 Region: ${responseData.cart?.region_id}`);
      console.log(`👤 Customer: ${responseData.cart?.customer_id || 'Guest'}`);
      console.log(`📧 Email: ${responseData.cart?.email}`);
      
      if (responseData.cart?.items && responseData.cart.items.length > 0) {
        console.log(`🛒 Items: ${responseData.cart.items.length}`);
        responseData.cart.items.forEach((item, index) => {
          console.log(`   ${index + 1}. ${item.title} (Qty: ${item.quantity}, Price: ${item.unit_price})`);
        });
      }
      
      if (responseData.cart?.metadata?.custom_pricing) {
        console.log("💵 Custom pricing enabled (Quote mode)");
      }
      
    } else {
      console.log("❌ FAILED! Cart creation failed");
      console.log("Error details:", JSON.stringify(responseData, null, 2));
      
      // Check for specific error types
      if (responseData.errors) {
        console.log("\n🔍 Validation Errors:");
        responseData.errors.forEach(error => {
          console.log(`   - ${error}`);
        });
      }
      
      if (responseData.message) {
        console.log(`\n💬 Error Message: ${responseData.message}`);
      }
    }

  } catch (error) {
    console.log("❌ NETWORK ERROR!");
    console.log("Error:", error.message);
    
    console.log("\n💡 Troubleshooting:");
    console.log("1. Make sure Medusa server is running (yarn dev)");
    console.log("2. Check if the region_id exists in your database");
    console.log("3. Verify the variant_ids exist");
    console.log("4. Check if the publishable API key is correct");
  }
}

// Test different scenarios
async function testMultipleScenarios() {
  console.log("\n🔄 Testing Multiple Scenarios");
  console.log("=" .repeat(50));

  // Test 1: Minimal payload
  console.log("\n1️⃣ Testing minimal payload...");
  const minimalPayload = {
    region_id: TEST_PAYLOAD.region_id,
    currency_code: TEST_PAYLOAD.currency_code,
    email: TEST_PAYLOAD.email
  };

  try {
    const response = await fetch(`${BASE_URL}/store/carts`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'x-publishable-api-key': PUBLISHABLE_API_KEY
      },
      body: JSON.stringify(minimalPayload)
    });

    if (response.ok) {
      const data = await response.json();
      console.log(`✅ Minimal cart created: ${data.cart?.id}`);
    } else {
      const error = await response.json();
      console.log(`❌ Minimal cart failed: ${error.message}`);
    }
  } catch (error) {
    console.log(`❌ Minimal cart error: ${error.message}`);
  }

  // Test 2: With customer_id only
  console.log("\n2️⃣ Testing with customer_id only...");
  const customerPayload = {
    region_id: TEST_PAYLOAD.region_id,
    currency_code: TEST_PAYLOAD.currency_code,
    customer_id: TEST_PAYLOAD.customer_id
  };

  try {
    const response = await fetch(`${BASE_URL}/store/carts`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'x-publishable-api-key': PUBLISHABLE_API_KEY
      },
      body: JSON.stringify(customerPayload)
    });

    if (response.ok) {
      const data = await response.json();
      console.log(`✅ Customer cart created: ${data.cart?.id}`);
    } else {
      const error = await response.json();
      console.log(`❌ Customer cart failed: ${error.message}`);
    }
  } catch (error) {
    console.log(`❌ Customer cart error: ${error.message}`);
  }
}

// Run tests
async function runAllTests() {
  await testCartCreation();
  await testMultipleScenarios();
  
  console.log("\n🎯 Summary");
  console.log("=" .repeat(50));
  console.log("If the main test passed, your cart creation is working!");
  console.log("If it failed, check the error details above for troubleshooting.");
}

// Execute if run directly
if (require.main === module) {
  runAllTests().catch(console.error);
}

module.exports = {
  testCartCreation,
  testMultipleScenarios,
  TEST_PAYLOAD
};
