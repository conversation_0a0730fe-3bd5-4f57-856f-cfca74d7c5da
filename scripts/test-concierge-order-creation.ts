import { ExecArgs } from "@camped-ai/framework/types";
import { CreateOrderManagementWorkflow } from "../src/workflows/order-management";
import { CONCIERGE_MANAGEMENT_MODULE } from "../src/modules/concierge-management";
import ConciergeManagementService from "../src/modules/concierge-management/service";

export default async function testConciergeOrderCreation({
  container,
}: ExecArgs) {
  console.log("🧪 Testing concierge order creation...");

  try {
    const conciergeOrderService: ConciergeManagementService = container.resolve(
      CONCIERGE_MANAGEMENT_MODULE
    );

    // Create a test order
    const testOrderData = {
      email: "<EMAIL>",
      currency_code: "USD",
      is_draft_order: false,
      billing_address: {
        first_name: "<PERSON>",
        last_name: "Doe",
        phone: "+1234567890",
        address_1: "123 Test Street",
        city: "Test City",
        country_code: "US",
        postal_code: "12345",
      },
      shipping_address: {
        first_name: "<PERSON>",
        last_name: "<PERSON><PERSON>",
        phone: "+1234567890",
        address_1: "123 Test Street",
        city: "Test City",
        country_code: "US",
        postal_code: "12345",
      },
      items: [
        {
          variant_id: "test_variant_123",
          quantity: 2,
          title: "Test Product",
          unit_price: 1000, // $10.00
          metadata: {
            test: true,
          },
        },
        {
          variant_id: "test_variant_456",
          quantity: 1,
          title: "Another Test Product",
          unit_price: 2000, // $20.00
          metadata: {
            test: true,
          },
        },
      ],
      metadata: {
        test_order: true,
        created_by_script: true,
      },
    };

    console.log("📦 Creating test order...");
    
    // Execute the order creation workflow
    const { result: order } = await CreateOrderManagementWorkflow(container).run({
      input: testOrderData,
    });

    console.log(`✅ Order created successfully: ${order.id}`);

    // Wait a moment for the events to be processed
    console.log("⏳ Waiting for events to be processed...");
    await new Promise(resolve => setTimeout(resolve, 2000));

    // Check if concierge order was created
    console.log("🔍 Checking for concierge order...");
    const conciergeOrders = await conciergeOrderService.listConciergeOrders({
      order_id: order.id,
    });

    if (conciergeOrders.length > 0) {
      const conciergeOrder = conciergeOrders[0];
      console.log(`✅ Concierge order created successfully: ${conciergeOrder.id}`);
      console.log(`📋 Status: ${conciergeOrder.status}`);
      console.log(`👤 Customer: ${conciergeOrder.customer_name || 'N/A'}`);
      console.log(`📧 Email: ${conciergeOrder.customer_email || 'N/A'}`);

      // Check concierge order items
      const conciergeOrderItems = await conciergeOrderService.listConciergeOrderItems({
        concierge_order_id: conciergeOrder.id,
      });

      console.log(`📦 Concierge order items created: ${conciergeOrderItems.length}`);
      conciergeOrderItems.forEach((item, index) => {
        console.log(`   ${index + 1}. ${item.item_name} (Qty: ${item.quantity}, Price: $${item.unit_price / 100})`);
      });

      console.log("🎉 SUCCESS: Concierge order creation is working correctly!");
      return true;
    } else {
      console.log("❌ FAILURE: No concierge order was created");
      console.log("🔍 This indicates the order.placed event was not processed correctly");
      return false;
    }

  } catch (error) {
    console.error("❌ Error testing concierge order creation:", error);
    throw error;
  }
}
