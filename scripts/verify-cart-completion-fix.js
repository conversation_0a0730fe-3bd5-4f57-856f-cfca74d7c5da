#!/usr/bin/env node

/**
 * Verification script for the cart completion fix
 * Verifies that cart completion now uses standard workflows and triggers concierge sync
 * Run with: node scripts/verify-cart-completion-fix.js
 */

const fs = require('fs');
const path = require('path');

function verifyCartCompletionFix() {
  console.log("🔍 Verifying Cart Completion Fix");
  console.log("=" .repeat(60));
  
  const results = {
    passed: 0,
    failed: 0,
    details: []
  };
  
  function checkRequirement(description, condition, details = "") {
    if (condition) {
      console.log(`✅ ${description}`);
      results.passed++;
      results.details.push({ status: "PASS", description, details });
    } else {
      console.log(`❌ ${description}`);
      results.failed++;
      results.details.push({ status: "FAIL", description, details });
    }
  }
  
  // Requirement 1: Cart completion uses standard workflow
  console.log("\n📋 Requirement 1: Standard Workflow Usage");
  
  const cartCompletePath = path.join(__dirname, '../src/api/store/carts/[id]/complete/route.ts');
  const cartCompleteExists = fs.existsSync(cartCompletePath);
  checkRequirement(
    "Cart completion endpoint exists",
    cartCompleteExists,
    cartCompletePath
  );
  
  if (cartCompleteExists) {
    const cartCompleteContent = fs.readFileSync(cartCompletePath, 'utf8');
    
    checkRequirement(
      "Uses completeCartWorkflow from Medusa core flows",
      cartCompleteContent.includes('completeCartWorkflow') && 
      cartCompleteContent.includes('@camped-ai/medusa/core-flows'),
      "Standard Medusa workflow imported and used"
    );
    
    checkRequirement(
      "No manual order creation with orderModuleService.createOrders",
      !cartCompleteContent.includes('orderModuleService.createOrders'),
      "Manual order creation removed"
    );
    
    checkRequirement(
      "No manual event emission in cart completion",
      !cartCompleteContent.includes('eventBusService.emit'),
      "Manual event emission removed - handled by workflow"
    );
    
    checkRequirement(
      "Calls completeCartWorkflow.run with cart ID",
      cartCompleteContent.includes('completeCartWorkflow(req.scope).run') &&
      cartCompleteContent.includes('id: cartId'),
      "Proper workflow execution"
    );
  }
  
  // Requirement 2: Event emission through hooks
  console.log("\n📋 Requirement 2: Event Emission via Hooks");
  
  const createOrderHookPath = path.join(__dirname, '../src/workflows/hooks/create-order.ts');
  const createOrderHookExists = fs.existsSync(createOrderHookPath);
  checkRequirement(
    "createOrderWorkflow hook exists",
    createOrderHookExists,
    createOrderHookPath
  );
  
  if (createOrderHookExists) {
    const createOrderHookContent = fs.readFileSync(createOrderHookPath, 'utf8');
    
    checkRequirement(
      "Hook emits order.created event",
      createOrderHookContent.includes('name: "order.created"'),
      "order.created event emission for concierge sync"
    );
    
    checkRequirement(
      "Hook emits order.placed event",
      createOrderHookContent.includes('name: "order.placed"'),
      "order.placed event emission for notifications"
    );
    
    checkRequirement(
      "Hook uses createOrderWorkflow from core flows",
      createOrderHookContent.includes('createOrderWorkflow') &&
      createOrderHookContent.includes('@camped-ai/medusa/core-flows'),
      "Standard Medusa workflow hook"
    );
  }
  
  // Requirement 3: Universal order sync subscriber
  console.log("\n📋 Requirement 3: Universal Order Sync");
  
  const universalSyncPath = path.join(__dirname, '../src/subscribers/universal-order-sync.ts');
  const universalSyncExists = fs.existsSync(universalSyncPath);
  checkRequirement(
    "Universal order sync subscriber exists",
    universalSyncExists,
    universalSyncPath
  );
  
  if (universalSyncExists) {
    const universalSyncContent = fs.readFileSync(universalSyncPath, 'utf8');
    
    checkRequirement(
      "Listens to order.created event",
      universalSyncContent.includes('event: "order.created"'),
      "Correct event subscription"
    );
    
    checkRequirement(
      "Creates concierge orders for ALL orders",
      universalSyncContent.includes('universal sync - ALL orders') ||
      universalSyncContent.includes('Universal Order Synchronization'),
      "No conditional filtering"
    );
    
    checkRequirement(
      "Creates concierge order items",
      universalSyncContent.includes('createConciergeOrderItem'),
      "Complete order synchronization"
    );
  }
  
  // Requirement 4: No duplicate subscribers
  console.log("\n📋 Requirement 4: No Duplicate Logic");
  
  const conciergeOrderCreationPath = path.join(__dirname, '../src/subscribers/concierge-order-creation.ts');
  const conciergeOrderCreationExists = fs.existsSync(conciergeOrderCreationPath);
  checkRequirement(
    "Old concierge-order-creation.ts subscriber removed",
    !conciergeOrderCreationExists,
    "Duplicate subscriber eliminated"
  );
  
  const orderCreatedPath = path.join(__dirname, '../src/subscribers/order-created.ts');
  const orderCreatedExists = fs.existsSync(orderCreatedPath);
  checkRequirement(
    "Redundant order-created.ts subscriber removed",
    !orderCreatedExists,
    "Conflicting subscriber eliminated"
  );
  
  // Requirement 5: Clean architecture
  console.log("\n📋 Requirement 5: Clean Architecture");
  
  checkRequirement(
    "Single point of responsibility for concierge sync",
    universalSyncExists && !conciergeOrderCreationExists,
    "Only universal-order-sync.ts handles concierge creation"
  );
  
  checkRequirement(
    "Standard Medusa patterns followed",
    cartCompleteExists && createOrderHookExists && universalSyncExists,
    "Workflow → Hook → Event → Subscriber pattern"
  );
  
  // Summary
  console.log("\n" + "=" .repeat(60));
  console.log("📊 CART COMPLETION FIX VERIFICATION SUMMARY");
  console.log("=" .repeat(60));
  
  console.log(`✅ Requirements Passed: ${results.passed}`);
  console.log(`❌ Requirements Failed: ${results.failed}`);
  console.log(`📈 Success Rate: ${Math.round((results.passed / (results.passed + results.failed)) * 100)}%`);
  
  if (results.failed === 0) {
    console.log("\n🎉 ALL REQUIREMENTS VERIFIED SUCCESSFULLY!");
    console.log("✅ Cart completion now uses standard Medusa workflows");
    console.log("✅ Events are emitted through proper workflow hooks");
    console.log("✅ Universal concierge synchronization will trigger on cart completion");
    console.log("✅ No manual order creation or event emission");
    console.log("✅ Clean, maintainable architecture following Medusa patterns");
  } else {
    console.log("\n⚠️  SOME REQUIREMENTS NOT MET");
    console.log("Please review the failed requirements above");
  }
  
  console.log("\n📝 Fixed Implementation Flow:");
  console.log("1. POST /store/carts/{id}/complete");
  console.log("2. completeCartWorkflow(req.scope).run({ input: { id: cartId } })");
  console.log("3. createOrderWorkflow creates order from cart");
  console.log("4. createOrderWorkflow.hooks.orderCreated emits order.created event");
  console.log("5. universal-order-sync.ts subscriber creates concierge records");
  console.log("6. Both concierge_order and concierge_order_item records created");
  
  console.log("\n🔧 Key Changes Made:");
  console.log("• Replaced manual order creation with completeCartWorkflow");
  console.log("• Removed manual event emission from cart completion endpoint");
  console.log("• Events now emitted through createOrderWorkflow hook");
  console.log("• Single universal-order-sync.ts subscriber handles all orders");
  console.log("• Standard Medusa workflow patterns followed");
  
  return results.failed === 0;
}

// Run verification
const success = verifyCartCompletionFix();
process.exit(success ? 0 : 1);
