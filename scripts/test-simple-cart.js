#!/usr/bin/env node

/**
 * Simple test for the fixed cart creation
 */

const fetch = require('node-fetch');

const BASE_URL = process.env.MEDUSA_URL || 'http://localhost:9000';
const PUBLISHABLE_API_KEY = process.env.PUBLISHABLE_API_KEY || 'pk_test_123';

// Your corrected payload (no customer_id!)
const CORRECTED_PAYLOAD = {
  "region_id": "reg_01JXJ7K6W55Y74WAQ3QQG1WFDA",
  "currency_code": "GBP",
  "email": "<EMAIL>",
  "sales_channel_id": "sc_01JNR887105TH162F04RB9RKC0",
  "items": [
    {
      "variant_id": "variant_01JWR2V7STJK8SDJK2E84G049N",
      "quantity": 2,
      "unit_price": 98000,
      "title": "Hotel Valsana - Lifestyle Double Room 150",
      "metadata": {
        "product_id": "prod_01JWR26K8BNVDX0A1KKETHSTB6",
        "item_type": "room",
        "hotel_id": "01JWR0DA3F4H9KZ0STCA2VKYXT"
      },
      "requires_shipping": false
    },
    {
      "variant_id": "variant_addon_ps_01K134Q6CGVW54F5K49R58NAVT",
      "quantity": 1,
      "unit_price": 4000,
      "title": "Add Ons - Transportation (Arrival) – Sprinter – 5 – (Per Day)",
      "metadata": {
        "product_id": "product_add_ons_main",
        "item_type": "add-ons",
        "hotel_id": ""
      },
      "requires_shipping": false
    },
    {
      "variant_id": "variant_addon_ps_01K1308S65GSSCBTREQPBXQWEN",
      "quantity": 2,
      "unit_price": 8200,
      "title": "Add Ons - Kids Club – 12-34 – Yeti Primer – 1 – 12 – Arosa, Grindelwald, Andermatt – (Per Day)",
      "metadata": {
        "product_id": "product_add_ons_main",
        "item_type": "add-ons",
        "hotel_id": ""
      },
      "requires_shipping": false
    }
  ]
};

async function testSimpleCart() {
  console.log("🧪 Testing Simple Cart Creation (No customer_id)");
  console.log("=" .repeat(60));
  console.log(`🌐 URL: ${BASE_URL}/store/carts`);
  console.log(`🔑 API Key: ${PUBLISHABLE_API_KEY ? 'Set' : 'Missing'}`);
  console.log("=" .repeat(60));

  try {
    console.log("\n📋 Payload (corrected - no customer_id):");
    console.log(JSON.stringify(CORRECTED_PAYLOAD, null, 2));

    console.log("\n🚀 Making request...");
    const response = await fetch(`${BASE_URL}/store/carts`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'x-publishable-api-key': PUBLISHABLE_API_KEY
      },
      body: JSON.stringify(CORRECTED_PAYLOAD)
    });

    console.log(`\n📊 Response: ${response.status} ${response.statusText}`);

    const responseData = await response.json();

    if (response.ok) {
      console.log("🎉 SUCCESS! Cart created successfully!");
      console.log(`📋 Cart ID: ${responseData.cart?.id}`);
      console.log(`💰 Currency: ${responseData.cart?.currency_code}`);
      console.log(`🌍 Region: ${responseData.cart?.region_id}`);
      console.log(`📧 Email: ${responseData.cart?.email}`);
      
      if (responseData.cart?.customer_id) {
        console.log(`👤 Customer ID: ${responseData.cart.customer_id} (auto-associated by email)`);
      } else {
        console.log(`👤 Customer: Guest cart (no existing customer found for email)`);
      }
      
      if (responseData.cart?.items && responseData.cart.items.length > 0) {
        console.log(`🛒 Items: ${responseData.cart.items.length}`);
        responseData.cart.items.forEach((item, index) => {
          console.log(`   ${index + 1}. ${item.title || item.product_title} (Qty: ${item.quantity})`);
          if (item.unit_price) {
            console.log(`      💵 Custom price: ${item.unit_price} cents`);
          }
        });
      }
      
      if (responseData.cart?.metadata?.custom_pricing) {
        console.log("💵 Custom pricing enabled (Quote mode)");
      }

      console.log("\n✅ Key Takeaways:");
      console.log("   - No customer_id in payload ✓");
      console.log("   - Email used for customer association ✓");
      console.log("   - Medusa standards compliant ✓");
      console.log("   - Custom pricing working ✓");
      
    } else {
      console.log("❌ FAILED! Cart creation failed");
      console.log("\n📄 Error Response:");
      console.log(JSON.stringify(responseData, null, 2));
      
      if (responseData.message) {
        console.log(`\n💬 Error: ${responseData.message}`);
      }
      
      if (responseData.errors) {
        console.log("\n🔍 Validation Errors:");
        if (Array.isArray(responseData.errors)) {
          responseData.errors.forEach(error => {
            console.log(`   - ${error}`);
          });
        } else {
          console.log(JSON.stringify(responseData.errors, null, 2));
        }
      }
    }

  } catch (error) {
    console.log("❌ NETWORK/PARSING ERROR!");
    console.log(`Error: ${error.message}`);
    
    console.log("\n🔧 Troubleshooting:");
    console.log("1. Check if Medusa server is running (yarn dev)");
    console.log("2. Verify the region_id exists");
    console.log("3. Verify the variant_ids exist");
    console.log("4. Check publishable API key");
    console.log("5. Check server logs for detailed errors");
  }
}

// Test minimal payload too
async function testMinimalPayload() {
  console.log("\n\n🧪 Testing Minimal Payload");
  console.log("=" .repeat(40));

  const minimalPayload = {
    email: "<EMAIL>"
  };

  try {
    const response = await fetch(`${BASE_URL}/store/carts`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'x-publishable-api-key': PUBLISHABLE_API_KEY
      },
      body: JSON.stringify(minimalPayload)
    });

    console.log(`📊 Minimal Response: ${response.status}`);
    
    if (response.ok) {
      const data = await response.json();
      console.log(`✅ Minimal cart created: ${data.cart?.id}`);
      console.log(`   Uses default region: ${data.cart?.region_id}`);
      console.log(`   Uses default currency: ${data.cart?.currency_code}`);
    } else {
      const error = await response.json();
      console.log(`❌ Minimal failed: ${error.message || 'Unknown error'}`);
    }
  } catch (error) {
    console.log(`❌ Minimal error: ${error.message}`);
  }
}

// Run tests
async function runTests() {
  await testSimpleCart();
  await testMinimalPayload();
  
  console.log("\n🎯 Summary");
  console.log("=" .repeat(60));
  console.log("The main fix was removing 'customer_id' from the payload.");
  console.log("Medusa uses 'email' to associate carts with customers.");
  console.log("If the test passed, your cart creation is now working!");
}

if (require.main === module) {
  runTests().catch(console.error);
}

module.exports = { testSimpleCart, testMinimalPayload };
