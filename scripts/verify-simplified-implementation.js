#!/usr/bin/env node

/**
 * Verification script for the simplified universal order synchronization implementation
 * Verifies that all requirements are met with the new single-subscriber approach
 * Run with: node scripts/verify-simplified-implementation.js
 */

const fs = require('fs');
const path = require('path');

function verifySimplifiedImplementation() {
  console.log("🔍 Verifying Simplified Universal Order Synchronization Implementation");
  console.log("=" .repeat(70));
  
  const results = {
    passed: 0,
    failed: 0,
    details: []
  };
  
  function checkRequirement(description, condition, details = "") {
    if (condition) {
      console.log(`✅ ${description}`);
      results.passed++;
      results.details.push({ status: "PASS", description, details });
    } else {
      console.log(`❌ ${description}`);
      results.failed++;
      results.details.push({ status: "FAIL", description, details });
    }
  }
  
  // Requirement 1: Single Subscriber for Concierge Order Creation
  console.log("\n📋 Requirement 1: Single Point of Responsibility");
  
  const universalSyncPath = path.join(__dirname, '../src/subscribers/universal-order-sync.ts');
  const universalSyncExists = fs.existsSync(universalSyncPath);
  checkRequirement(
    "Universal order sync subscriber exists",
    universalSyncExists,
    universalSyncPath
  );
  
  const conciergeOrderCreationPath = path.join(__dirname, '../src/subscribers/concierge-order-creation.ts');
  const conciergeOrderCreationExists = fs.existsSync(conciergeOrderCreationPath);
  checkRequirement(
    "Old concierge-order-creation.ts subscriber removed",
    !conciergeOrderCreationExists,
    "Duplicate subscriber eliminated"
  );
  
  const orderCreatedPath = path.join(__dirname, '../src/subscribers/order-created.ts');
  const orderCreatedExists = fs.existsSync(orderCreatedPath);
  checkRequirement(
    "Redundant order-created.ts subscriber removed",
    !orderCreatedExists,
    "Eliminated conflicting subscriber"
  );
  
  // Requirement 2: Correct Event Listening
  console.log("\n📋 Requirement 2: Event Strategy");
  
  if (universalSyncExists) {
    const universalSyncContent = fs.readFileSync(universalSyncPath, 'utf8');
    
    checkRequirement(
      "Subscriber listens to 'order.created' event",
      universalSyncContent.includes('event: "order.created"'),
      "Uses order.created for reliable timing"
    );
    
    checkRequirement(
      "Subscriber has unique ID",
      universalSyncContent.includes('subscriberId: "universal-concierge-sync"'),
      "Proper subscriber identification"
    );
    
    checkRequirement(
      "No longer listens to order.placed",
      !universalSyncContent.includes('event: "order.placed"'),
      "Simplified event strategy"
    );
  }
  
  // Requirement 3: Universal Order Processing
  console.log("\n📋 Requirement 3: Universal Processing");
  
  if (universalSyncExists) {
    const universalSyncContent = fs.readFileSync(universalSyncPath, 'utf8');
    
    checkRequirement(
      "Handles both data.id and data.order_id",
      universalSyncContent.includes('data.id || data.order_id'),
      "Compatible with different event data formats"
    );
    
    checkRequirement(
      "Creates concierge orders for ALL orders",
      universalSyncContent.includes('universal sync - ALL orders') ||
      universalSyncContent.includes('Universal Order Synchronization'),
      "No conditional filtering"
    );
    
    checkRequirement(
      "Creates concierge order items",
      universalSyncContent.includes('createConciergeOrderItem'),
      "Complete order synchronization"
    );
  }
  
  // Requirement 4: Event Emission Consistency
  console.log("\n📋 Requirement 4: Event Emission Consistency");
  
  const orderWorkflowPath = path.join(__dirname, '../src/workflows/order-management/create-order.ts');
  const orderWorkflowExists = fs.existsSync(orderWorkflowPath);
  
  if (orderWorkflowExists) {
    const orderWorkflowContent = fs.readFileSync(orderWorkflowPath, 'utf8');
    
    checkRequirement(
      "API order creation emits order.created",
      orderWorkflowContent.includes('name: "order.created"'),
      "Consistent event emission for API orders"
    );
  }
  
  const cartHookPath = path.join(__dirname, '../src/workflows/hooks/create-order.ts');
  const cartHookExists = fs.existsSync(cartHookPath);
  
  if (cartHookExists) {
    const cartHookContent = fs.readFileSync(cartHookPath, 'utf8');
    
    checkRequirement(
      "Cart conversion emits order.created",
      cartHookContent.includes('name: "order.created"'),
      "Consistent event emission for cart conversions"
    );
    
    checkRequirement(
      "Cart conversion has proper data structure",
      cartHookContent.includes('order_id: order.id') && 
      cartHookContent.includes('customer_id: order.customer_id'),
      "Consistent event data structure"
    );
  }
  
  // Requirement 5: No Duplicate Logic
  console.log("\n📋 Requirement 5: No Duplicate Logic");
  
  const orderPlacedPath = path.join(__dirname, '../src/subscribers/order-placed.ts');
  const orderPlacedExists = fs.existsSync(orderPlacedPath);
  
  if (orderPlacedExists) {
    const orderPlacedContent = fs.readFileSync(orderPlacedPath, 'utf8');
    
    checkRequirement(
      "order-placed.ts no longer creates concierge orders",
      !orderPlacedContent.includes('createConciergeOrderEntry') &&
      !orderPlacedContent.includes('CONCIERGE_MANAGEMENT_MODULE'),
      "Duplicate logic removed"
    );
    
    checkRequirement(
      "order-placed.ts still handles notifications",
      orderPlacedContent.includes('notification') || orderPlacedContent.includes('email'),
      "Maintains primary responsibility"
    );
  }
  
  // Requirement 6: Simplified Architecture
  console.log("\n📋 Requirement 6: Simplified Architecture");
  
  // Count core order event subscribers (not supplier/vendor order management)
  const subscribersDir = path.join(__dirname, '../src/subscribers');
  const coreOrderFiles = fs.readdirSync(subscribersDir).filter(file =>
    (file.includes('order-placed') || file.includes('universal-order')) && file.endsWith('.ts')
  );

  checkRequirement(
    "Core order event subscribers are minimal",
    coreOrderFiles.length <= 3, // universal-order-sync, order-placed, order-placed-feed
    `Found ${coreOrderFiles.length} core order subscribers: ${coreOrderFiles.join(', ')}`
  );

  // Check for concierge synchronization subscribers specifically
  const conciergeFiles = fs.readdirSync(subscribersDir).filter(file =>
    (file.includes('concierge') || file.includes('universal-order-sync')) && file.endsWith('.ts')
  );

  checkRequirement(
    "Single concierge synchronization subscriber",
    conciergeFiles.length === 1 && conciergeFiles[0] === 'universal-order-sync.ts',
    `Found concierge sync subscribers: ${conciergeFiles.join(', ')}`
  );
  
  // Summary
  console.log("\n" + "=" .repeat(70));
  console.log("📊 SIMPLIFIED IMPLEMENTATION VERIFICATION SUMMARY");
  console.log("=" .repeat(70));
  
  console.log(`✅ Requirements Passed: ${results.passed}`);
  console.log(`❌ Requirements Failed: ${results.failed}`);
  console.log(`📈 Success Rate: ${Math.round((results.passed / (results.passed + results.failed)) * 100)}%`);
  
  if (results.failed === 0) {
    console.log("\n🎉 ALL REQUIREMENTS VERIFIED SUCCESSFULLY!");
    console.log("✅ Simplified universal order synchronization is properly implemented");
    console.log("✅ Single point of responsibility for concierge order creation");
    console.log("✅ Consistent event handling across all order creation methods");
    console.log("✅ No duplicate logic or conflicting subscribers");
    console.log("✅ Clean, maintainable architecture");
  } else {
    console.log("\n⚠️  SOME REQUIREMENTS NOT MET");
    console.log("Please review the failed requirements above");
  }
  
  console.log("\n📝 Simplified Implementation Details:");
  console.log("• Single Subscriber: src/subscribers/universal-order-sync.ts");
  console.log("• Event: order.created (reliable timing)");
  console.log("• Subscriber ID: universal-concierge-sync");
  console.log("• Scope: ALL orders (no filtering)");
  console.log("• Creates: concierge_order + concierge_order_item records");
  console.log("• Duplicate Prevention: Checks existing concierge orders");
  console.log("• Event Sources: API orders + Cart conversions");
  
  console.log("\n🔄 Event Flow:");
  console.log("1. Order created (API/Cart) → order.created event");
  console.log("2. universal-order-sync.ts → Creates concierge records");
  console.log("3. order.placed event → Notifications & other processing");
  
  return results.failed === 0;
}

// Run verification
const success = verifySimplifiedImplementation();
process.exit(success ? 0 : 1);
