#!/usr/bin/env node

/**
 * Immediate Region Setup Script
 * 
 * This script creates regions RIGHT NOW via the admin API
 * Run this BEFORE trying to create any carts
 */

const fetch = require('node-fetch');

// Configuration
const BASE_URL = process.env.MEDUSA_URL || 'http://localhost:9000';
const ADMIN_EMAIL = process.env.ADMIN_EMAIL || '<EMAIL>';
const ADMIN_PASSWORD = process.env.ADMIN_PASSWORD || 'supersecret';

// Default regions to create
const REGIONS_TO_CREATE = [
  {
    name: "Switzerland",
    currency_code: "CHF",
    countries: ["ch"],
    tax_rate: 7.7,
    is_default: true
  },
  {
    name: "European Union",
    currency_code: "EUR", 
    countries: ["de", "fr", "it", "at"],
    tax_rate: 20.0,
    is_default: false
  },
  {
    name: "United States",
    currency_code: "USD",
    countries: ["us"],
    tax_rate: 8.5,
    is_default: false
  }
];

async function setupRegionsNow() {
  console.log("🚀 Setting up regions immediately...");
  console.log("=" .repeat(50));
  console.log(`🌐 Medusa URL: ${BASE_URL}`);
  console.log(`👤 Admin Email: ${ADMIN_EMAIL}`);
  console.log("=" .repeat(50));

  try {
    // Step 1: Authenticate as admin
    console.log("\n1️⃣ Authenticating as admin...");
    const authToken = await authenticateAdmin();
    
    if (!authToken) {
      console.error("❌ Failed to authenticate. Cannot create regions.");
      console.log("\n💡 Make sure:");
      console.log("   - Medusa server is running");
      console.log("   - Admin user exists with correct credentials");
      console.log("   - ADMIN_EMAIL and ADMIN_PASSWORD environment variables are set");
      process.exit(1);
    }
    
    console.log("✅ Admin authentication successful");

    // Step 2: Check existing regions
    console.log("\n2️⃣ Checking existing regions...");
    const existingRegions = await getExistingRegions(authToken);
    
    if (existingRegions.length > 0) {
      console.log(`📍 Found ${existingRegions.length} existing regions:`);
      existingRegions.forEach(region => {
        console.log(`   - ${region.name} (${region.currency_code}) ${region.is_default ? '[DEFAULT]' : ''}`);
      });
      
      const hasDefault = existingRegions.some(r => r.is_default);
      if (hasDefault) {
        console.log("✅ Default region already exists. Setup complete!");
        return;
      }
    }

    // Step 3: Create regions
    console.log("\n3️⃣ Creating regions...");
    let createdCount = 0;
    
    for (const regionConfig of REGIONS_TO_CREATE) {
      try {
        const region = await createRegion(authToken, regionConfig);
        console.log(`   ✅ Created: ${region.name} (${region.currency_code}) ${region.is_default ? '[DEFAULT]' : ''}`);
        createdCount++;
      } catch (error) {
        console.log(`   ❌ Failed to create ${regionConfig.name}: ${error.message}`);
      }
    }

    // Step 4: Verify setup
    console.log("\n4️⃣ Verifying setup...");
    const finalRegions = await getExistingRegions(authToken);
    const defaultRegion = finalRegions.find(r => r.is_default);
    
    if (defaultRegion) {
      console.log(`✅ Default region configured: ${defaultRegion.name} (${defaultRegion.currency_code})`);
      console.log(`📋 Region ID for cart creation: ${defaultRegion.id}`);
    } else {
      console.log("⚠️ No default region found. Cart creation may require explicit region_id.");
    }

    console.log("\n🎉 Region setup completed!");
    console.log(`📊 Total regions: ${finalRegions.length}`);
    console.log(`🆕 Created: ${createdCount}`);
    
    // Step 5: Test cart creation readiness
    console.log("\n5️⃣ Testing cart creation readiness...");
    if (defaultRegion) {
      console.log("✅ Cart creation should work with these parameters:");
      console.log(`   region_id: "${defaultRegion.id}"`);
      console.log(`   currency_code: "${defaultRegion.currency_code}"`);
      
      // Show example cart creation
      console.log("\n📋 Example cart creation:");
      console.log(`
POST ${BASE_URL}/store/carts
{
  "region_id": "${defaultRegion.id}",
  "currency_code": "${defaultRegion.currency_code}",
  "email": "<EMAIL>",
  "items": [
    {
      "variant_id": "variant_123",
      "quantity": 1
    }
  ]
}
      `);
    }

  } catch (error) {
    console.error("❌ Setup failed:", error.message);
    process.exit(1);
  }
}

async function authenticateAdmin() {
  try {
    const response = await fetch(`${BASE_URL}/admin/auth/token`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        email: ADMIN_EMAIL,
        password: ADMIN_PASSWORD
      })
    });

    if (!response.ok) {
      throw new Error(`Authentication failed: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    return data.access_token;
    
  } catch (error) {
    console.error("Authentication error:", error.message);
    return null;
  }
}

async function getExistingRegions(authToken) {
  try {
    const response = await fetch(`${BASE_URL}/admin/regions`, {
      headers: {
        'Authorization': `Bearer ${authToken}`
      }
    });

    if (!response.ok) {
      throw new Error(`Failed to fetch regions: ${response.status}`);
    }

    const data = await response.json();
    return data.regions || [];
    
  } catch (error) {
    console.error("Error fetching regions:", error.message);
    return [];
  }
}

async function createRegion(authToken, regionConfig) {
  try {
    const response = await fetch(`${BASE_URL}/admin/regions`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${authToken}`
      },
      body: JSON.stringify(regionConfig)
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || `HTTP ${response.status}`);
    }

    const data = await response.json();
    return data.region;
    
  } catch (error) {
    throw new Error(`Failed to create region: ${error.message}`);
  }
}

// Run if executed directly
if (require.main === module) {
  setupRegionsNow().catch(console.error);
}

module.exports = {
  setupRegionsNow,
  REGIONS_TO_CREATE
};
