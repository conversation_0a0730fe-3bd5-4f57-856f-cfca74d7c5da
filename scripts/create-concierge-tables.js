#!/usr/bin/env node

/**
 * <PERSON><PERSON><PERSON> to manually create concierge order tables
 * Run with: node scripts/create-concierge-tables.js
 */

const { Client } = require('pg');
require('dotenv').config();

const createTablesSQL = `
-- Create concierge_order table
CREATE TABLE IF NOT EXISTS "concierge_order" (
    "id" text NOT NULL,
    "order_id" text NOT NULL,
    "assigned_to" text NULL,
    "notes" text NULL,
    "status" text NOT NULL DEFAULT 'not_started',
    "last_contacted_at" timestamptz NULL,
    "metadata" jsonb NULL,
    "created_at" timestamptz NOT NULL DEFAULT now(),
    "updated_at" timestamptz NOT NULL DEFAULT now(),
    "deleted_at" timestamptz NULL,
    CONSTRAINT "concierge_order_pkey" PRIMARY KEY ("id")
);

-- Create concierge_order_item table
CREATE TABLE IF NOT EXISTS "concierge_order_item" (
    "id" text NOT NULL,
    "concierge_order_id" text NOT NULL,
    "line_item_id" text NULL,
    "variant_id" text NULL,
    "quantity" integer NOT NULL,
    "unit_price" integer NOT NULL,
    "title" text NOT NULL,
    "status" text NOT NULL DEFAULT 'under_review',
    "is_active" boolean NOT NULL DEFAULT true,
    "added_by" text NULL,
    "finalized_by" text NULL,
    "added_at" timestamptz NOT NULL DEFAULT now(),
    "finalized_at" timestamptz NULL,
    "metadata" jsonb NULL,
    "created_at" timestamptz NOT NULL DEFAULT now(),
    "updated_at" timestamptz NOT NULL DEFAULT now(),
    "deleted_at" timestamptz NULL,
    CONSTRAINT "concierge_order_item_pkey" PRIMARY KEY ("id")
);

-- Create indexes for concierge_order
CREATE UNIQUE INDEX IF NOT EXISTS "IDX_concierge_order_order_id" ON "concierge_order" ("order_id") WHERE "deleted_at" IS NULL;
CREATE INDEX IF NOT EXISTS "IDX_concierge_order_assigned_to" ON "concierge_order" ("assigned_to") WHERE "deleted_at" IS NULL;
CREATE INDEX IF NOT EXISTS "IDX_concierge_order_status" ON "concierge_order" ("status") WHERE "deleted_at" IS NULL;
CREATE INDEX IF NOT EXISTS "IDX_concierge_order_created_at" ON "concierge_order" ("created_at");
CREATE INDEX IF NOT EXISTS "IDX_concierge_order_deleted_at" ON "concierge_order" ("deleted_at");

-- Create indexes for concierge_order_item
CREATE INDEX IF NOT EXISTS "IDX_concierge_order_item_concierge_order_id" ON "concierge_order_item" ("concierge_order_id") WHERE "deleted_at" IS NULL;
CREATE INDEX IF NOT EXISTS "IDX_concierge_order_item_line_item_id" ON "concierge_order_item" ("line_item_id") WHERE "deleted_at" IS NULL;
CREATE INDEX IF NOT EXISTS "IDX_concierge_order_item_status" ON "concierge_order_item" ("status") WHERE "deleted_at" IS NULL;
CREATE INDEX IF NOT EXISTS "IDX_concierge_order_item_is_active" ON "concierge_order_item" ("is_active") WHERE "deleted_at" IS NULL;
CREATE INDEX IF NOT EXISTS "IDX_concierge_order_item_created_at" ON "concierge_order_item" ("created_at");
CREATE INDEX IF NOT EXISTS "IDX_concierge_order_item_deleted_at" ON "concierge_order_item" ("deleted_at");

-- Add foreign key constraint
ALTER TABLE "concierge_order_item" 
ADD CONSTRAINT "FK_concierge_order_item_concierge_order_id" 
FOREIGN KEY ("concierge_order_id") REFERENCES "concierge_order" ("id") ON DELETE CASCADE;
`;

async function createTables() {
  console.log('🗃️ Creating concierge order tables...');
  
  const client = new Client({
    connectionString: process.env.DATABASE_URL,
  });

  try {
    await client.connect();
    console.log('✅ Connected to database');

    // Execute the SQL
    await client.query(createTablesSQL);
    console.log('✅ Tables created successfully');

    // Verify tables exist
    const result = await client.query(`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
      AND table_name IN ('concierge_order', 'concierge_order_item')
      ORDER BY table_name;
    `);

    console.log('📋 Verified tables:');
    result.rows.forEach(row => {
      console.log(`   - ${row.table_name}`);
    });

    if (result.rows.length === 2) {
      console.log('🎉 All tables created successfully!');
    } else {
      console.log('⚠️ Some tables may not have been created');
    }

  } catch (error) {
    console.error('❌ Error creating tables:', error.message);
    process.exit(1);
  } finally {
    await client.end();
    console.log('🔌 Database connection closed');
  }
}

// Run the script
createTables().catch(console.error);
