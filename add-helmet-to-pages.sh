#!/bin/bash

# Script to add AdminPageHelmet to all page.tsx files that don't already have it

# Find all page.tsx files
find src/admin/routes -name "page.tsx" -type f | while read -r file; do
    # Check if the file already has AdminPageHelmet import
    if grep -q "AdminPageHelmet" "$file"; then
        echo "✓ $file already has AdminPageHelmet"
        continue
    fi
    
    echo "Adding AdminPageHelmet to $file"
    
    # Calculate the correct relative path to AdminPageHelmet
    # Count the number of directories from the file to src/admin/components
    depth=$(echo "$file" | sed 's|src/admin/routes/||' | tr -cd '/' | wc -c)
    relative_path=""
    for ((i=0; i<depth; i++)); do
        relative_path="../$relative_path"
    done
    relative_path="${relative_path}../components/AdminPageHelmet"
    
    # Create a temporary file for the modified content
    temp_file=$(mktemp)
    
    # Add the import after the last import statement
    awk -v import_line="import AdminPageHelmet from \"$relative_path\";" '
    /^import/ { imports[++import_count] = $0; next }
    !found_first_non_import && !/^import/ && !/^$/ && !/^\/\// {
        for (i = 1; i <= import_count; i++) {
            print imports[i]
        }
        print import_line
        print ""
        found_first_non_import = 1
        print
        next
    }
    { print }
    ' "$file" > "$temp_file"
    
    # Now add <AdminPageHelmet /> after the opening fragment or return statement
    awk '
    /return \(/ {
        print
        getline
        if ($0 ~ /^\s*<>/) {
            print
            print "      <AdminPageHelmet />"
        } else {
            print "    <>"
            print "      <AdminPageHelmet />"
            print $0
        }
        next
    }
    { print }
    ' "$temp_file" > "${temp_file}.2"
    
    # Replace the original file
    mv "${temp_file}.2" "$file"
    rm -f "$temp_file"
    
    echo "✓ Added AdminPageHelmet to $file"
done

echo "Done! AdminPageHelmet has been added to all page.tsx files."
