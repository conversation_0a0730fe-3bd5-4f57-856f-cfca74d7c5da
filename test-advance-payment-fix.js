#!/usr/bin/env node

/**
 * Test script to verify the advance payment API fix
 */

const fetch = require('node-fetch');

// Configuration
const BASE_URL = process.env.MEDUSA_URL || 'http://localhost:9000';

async function testAdvancePayment() {
  console.log('🧪 Testing Advance Payment API Fix...\n');

  try {
    // Step 1: Create a test cart first
    console.log('📋 Step 1: Creating test cart...');
    
    const cartResponse = await fetch(`${BASE_URL}/store/carts`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        region_id: "reg_01JP9R0NP6B5DXGDYHFSSW0FK1",
        currency_code: "GBP",
        email: "<EMAIL>",
        sales_channel_id: "sc_01JNR887105TH162F04RB9RKC0",
        items: [
          {
            variant_id: "variant_01JWR2V7STJK8SDJK2E84G049N",
            quantity: 1,
            unit_price: 10000,
            title: "Test Room",
            metadata: {
              product_id: "prod_01JWR26K8BNVDX0A1KKETHSTB6",
              item_type: "room"
            },
            requires_shipping: false
          }
        ]
      })
    });

    if (!cartResponse.ok) {
      const errorText = await cartResponse.text();
      console.error('❌ Cart creation failed:', errorText);
      return;
    }

    const cartData = await cartResponse.json();
    const cartId = cartData.cart.id;
    console.log(`✅ Cart created: ${cartId}`);

    // Step 2: Test advance payment API
    console.log('\n💰 Step 2: Testing advance payment API...');
    
    const advancePaymentResponse = await fetch(`${BASE_URL}/store/carts/${cartId}/advance-payment`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        // Try without publishable API key first
      },
      body: JSON.stringify({
        payment_mode: "manual",
        advance_amount: 4400,
        metadata: {
          test_run: true,
          test_timestamp: new Date().toISOString()
        }
      })
    });

    const responseText = await advancePaymentResponse.text();
    
    if (advancePaymentResponse.ok) {
      console.log('✅ Advance payment API succeeded!');
      const responseData = JSON.parse(responseText);
      console.log('📊 Response data:', JSON.stringify(responseData, null, 2));
    } else {
      console.log(`❌ Advance payment API failed (${advancePaymentResponse.status})`);
      console.log('📄 Response:', responseText);
      
      // Check if it's a publishable API key issue
      if (responseText.includes('publishable') || responseText.includes('x-publishable-api-key')) {
        console.log('\n🔑 Trying with test publishable API key...');
        
        const retryResponse = await fetch(`${BASE_URL}/store/carts/${cartId}/advance-payment`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'x-publishable-api-key': 'pk_test_123'
          },
          body: JSON.stringify({
            payment_mode: "manual",
            advance_amount: 4400,
            metadata: {
              test_run: true,
              test_timestamp: new Date().toISOString()
            }
          })
        });

        const retryText = await retryResponse.text();
        
        if (retryResponse.ok) {
          console.log('✅ Advance payment API succeeded with test key!');
          const retryData = JSON.parse(retryText);
          console.log('📊 Response data:', JSON.stringify(retryData, null, 2));
        } else {
          console.log(`❌ Advance payment API still failed (${retryResponse.status})`);
          console.log('📄 Response:', retryText);
        }
      }
    }

  } catch (error) {
    console.error('💥 Test failed with error:', error.message);
    console.error('Stack trace:', error.stack);
  }
}

// Run the test
testAdvancePayment().then(() => {
  console.log('\n🏁 Test completed');
}).catch(error => {
  console.error('💥 Test script failed:', error);
  process.exit(1);
});
