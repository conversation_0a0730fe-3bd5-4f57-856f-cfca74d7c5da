-- ============================================================================
-- DATABASE RELATIONSHIP DEBUGGING QUERIES
-- ============================================================================
-- Run these queries to debug the three critical database relationship issues
-- Replace 'YOUR_CART_ID', 'YOUR_ORDER_ID', 'YOUR_PAYMENT_COLLECTION_ID' with actual values

-- ============================================================================
-- ISSUE 1: Payment Session Status Investigation
-- ============================================================================

-- 1.1 Check current payment session status and data
SELECT 
    ps.id as payment_session_id,
    ps.status,
    ps.amount,
    ps.currency_code,
    ps.provider_id,
    ps.data,
    ps.created_at,
    ps.updated_at,
    pc.id as payment_collection_id,
    pc.status as collection_status
FROM payment_session ps
LEFT JOIN payment_collection pc ON ps.payment_collection_id = pc.id
WHERE pc.id = 'YOUR_PAYMENT_COLLECTION_ID'
ORDER BY ps.created_at DESC;

-- 1.2 Check if payment session has authorization data
SELECT 
    id,
    status,
    data::text LIKE '%authorized%' as has_auth_data,
    data::text LIKE '%manual_authorization%' as has_manual_auth,
    data->'authorized_at' as authorized_at,
    data->'status_updated_by' as updated_by
FROM payment_session 
WHERE payment_collection_id = 'YOUR_PAYMENT_COLLECTION_ID';

-- ============================================================================
-- ISSUE 2: Payment Collection Status Investigation  
-- ============================================================================

-- 2.1 Check payment collection status and amounts
SELECT 
    id,
    status,
    amount,
    authorized_amount,
    captured_amount,
    completed_at,
    captured_at,
    created_at,
    updated_at,
    metadata
FROM payment_collection 
WHERE id = 'YOUR_PAYMENT_COLLECTION_ID';

-- 2.2 Check payment collection metadata for our tracking fields
SELECT 
    id,
    status,
    metadata->'advance_payment_status' as advance_status,
    metadata->'payment_recorded_at' as recorded_at,
    metadata->'authorized_amount' as meta_authorized,
    metadata->'captured_amount' as meta_captured,
    metadata->'payment_completion_method' as completion_method
FROM payment_collection 
WHERE id = 'YOUR_PAYMENT_COLLECTION_ID';

-- ============================================================================
-- ISSUE 3: Order-Cart Relationship Investigation
-- ============================================================================

-- 3.1 Check order.cart_id field (primary relationship)
SELECT 
    id as order_id,
    cart_id,
    total,
    status,
    customer_id,
    email,
    created_at,
    updated_at
FROM "order" 
WHERE id = 'YOUR_ORDER_ID';

-- 3.2 Check if order_cart junction table exists and has data
SELECT 
    table_name,
    column_name,
    data_type
FROM information_schema.columns 
WHERE table_name = 'order_cart'
ORDER BY ordinal_position;

-- 3.3 Check order_cart junction table contents (if table exists)
SELECT 
    order_id,
    cart_id,
    created_at,
    updated_at
FROM order_cart 
WHERE order_id = 'YOUR_ORDER_ID' OR cart_id = 'YOUR_CART_ID';

-- ============================================================================
-- COMPREHENSIVE RELATIONSHIP VERIFICATION
-- ============================================================================

-- 4.1 Complete workflow verification query
SELECT 
    c.id as cart_id,
    c.total as cart_total,
    c.currency_code,
    o.id as order_id,
    o.cart_id as order_cart_id,
    o.total as order_total,
    o.status as order_status,
    oc.cart_id as junction_cart_id,
    oc.order_id as junction_order_id,
    pc.id as payment_collection_id,
    pc.status as collection_status,
    pc.amount as collection_amount,
    pc.authorized_amount,
    pc.captured_amount,
    pc.completed_at,
    pc.captured_at,
    ps.id as payment_session_id,
    ps.status as session_status,
    ps.amount as session_amount,
    COUNT(oli.id) as line_items_count
FROM cart c
LEFT JOIN "order" o ON c.id = o.cart_id
LEFT JOIN order_cart oc ON o.id = oc.order_id AND oc.cart_id = c.id
LEFT JOIN cart_payment_collection cpc ON c.id = cpc.cart_id
LEFT JOIN payment_collection pc ON cpc.payment_collection_id = pc.id
LEFT JOIN payment_session ps ON pc.id = ps.payment_collection_id
LEFT JOIN order_line_item oli ON o.id = oli.order_id
WHERE c.id = 'YOUR_CART_ID'
GROUP BY c.id, o.id, oc.cart_id, oc.order_id, pc.id, ps.id;

-- ============================================================================
-- DEBUGGING SPECIFIC ISSUES
-- ============================================================================

-- 5.1 Find all payment sessions for debugging
SELECT 
    ps.id,
    ps.status,
    ps.provider_id,
    ps.amount,
    ps.created_at,
    pc.id as collection_id,
    pc.status as collection_status
FROM payment_session ps
JOIN payment_collection pc ON ps.payment_collection_id = pc.id
JOIN cart_payment_collection cpc ON pc.id = cpc.payment_collection_id
WHERE cpc.cart_id = 'YOUR_CART_ID'
ORDER BY ps.created_at DESC;

-- 5.2 Check for any failed payment session updates
SELECT 
    id,
    status,
    data->'status_updated_by' as updated_by,
    data->'status_updated_at' as updated_at,
    data->'manual_authorization' as manual_auth
FROM payment_session 
WHERE payment_collection_id = 'YOUR_PAYMENT_COLLECTION_ID'
AND (data::text LIKE '%status_updated%' OR data::text LIKE '%manual%');

-- ============================================================================
-- EXPECTED RESULTS AFTER FIXES
-- ============================================================================

/*
EXPECTED RESULTS:

1. Payment Session Status:
   - ps.status should be 'authorized' (not 'pending')
   - ps.data should contain manual_authorization: true
   - ps.data should contain authorized_at timestamp

2. Payment Collection Status:
   - pc.status should be 'authorized' (not 'not_paid')
   - pc.authorized_amount should equal the advance payment amount
   - pc.captured_amount should equal the advance payment amount
   - pc.completed_at should be populated
   - pc.captured_at should be populated

3. Order-Cart Relationship:
   - o.cart_id should equal the cart ID
   - oc.order_id and oc.cart_id should exist in order_cart table (if table exists)
   - line_items_count should be > 0

If any of these are not as expected, the fixes are not working properly.
*/
