# Medusa v2 Development Guidelines

Welcome Augment,

You are tasked with the items in supplier.md

You have to strictly follow medusa's framework, it is wrapped with @camped-ai. Don't create new tables unless its very much needed. Once you are done with the tasks, create a readme with session name and summarise the changes in that file.

There are 3 repos, we always make changes only at medusa-store-v2. Shop is the core and examples are extended use cases or references of some implementations of medusa.
If there are failures or time out or augment didnt respond back, then retry automaticaly or resume automatically.

## Best Practices and Guidelines

### CRITICAL: Avoid Direct SQL Queries

**NEVER use direct SQL queries in the codebase**. This is a strict requirement. Instead:

- Use Medusa's workflow pattern for business logic
- Use module services for data access
- Use the query service for complex queries
- Use the repository pattern when necessary

Direct SQL queries make the code harder to maintain, test, and scale. They also bypass important business logic and validation that should be handled by services and workflows.

### CRITICAL: Avoid Mock Data and Mock Functions

**NEVER use mock data, mock functions, or fallback mechanisms in production code**. This is a strict requirement. These practices create a false sense that functionality is working when it actually isn't in production.

- **No Mock Data**: Don't use hardcoded sample data in production code
- **No Mock Functions**: Don't create fake implementations that return hardcoded results
- **No Silent Fallbacks**: Don't silently fall back to default values when services fail
- **Proper Error Handling**: Always throw appropriate errors when services or data are unavailable

Mock implementations may appear to work in development but will fail in production environments, leading to difficult-to-diagnose issues and data inconsistencies.

When implementing features or making changes to the codebase, follow these best practices to ensure consistency, maintainability, and scalability.

### 1. Module and Service Implementation

- **Use Medusa's Module Pattern**: Always use the `Module` function to define modules:

  ```typescript
  export const exampleModule = Module(EXAMPLE_MODULE, {
    service: ExampleModuleService,
  });
  ```

- **Extend MedusaService**: Services should extend MedusaService with their models:

  ```typescript
  class ExampleModuleService extends MedusaService({
    ExampleModel,
  }) {
    // Service implementation
  }
  ```

- **Avoid Direct SQL Queries**: Never use direct SQL queries. Instead, use:

  - Medusa's query service: `queryService.graph()`
  - Model methods: `this.list()`, `this.retrieve()`, etc.
  - Repository pattern if necessary: `repository.find()`

- **Define Constants for Service Keys**: Always define constants for service keys:
  ```typescript
  export const EXAMPLE_MODULE = "exampleModule";
  ```

### 2. Type Safety and Interfaces

- **Define Clear Interfaces**: Create TypeScript interfaces for all input and output data:

  ```typescript
  export interface CreateExampleInput {
    name: string;
    description?: string;
    // Other properties
  }

  export interface ExampleOutput {
    id: string;
    name: string;
    // Other properties
  }
  ```

- **Avoid `any` Type**: Never use `any` type. Use proper types or generics instead.

- **Use Type Guards**: Implement type guards for runtime type checking:
  ```typescript
  function isExampleInput(obj: unknown): obj is CreateExampleInput {
    return (
      typeof obj === "object" &&
      obj !== null &&
      "name" in obj &&
      typeof obj.name === "string"
    );
  }
  ```

### 3. Data Modeling

- **Use Medusa's Model System**: Define models using Medusa's model system:

  ```typescript
  export const Example = model.define("example", {
    id: model.id().primaryKey(),
    name: model.text(),
    description: model.text().nullable(),
    is_active: model.boolean().default(true),
    // Other fields
  });
  ```

- **Minimize Metadata Usage**: Don't overuse metadata fields. Create proper models instead.

- **Define Relationships**: Use proper relationship definitions:

  ```typescript
  images: model.hasMany(() => ExampleImage, {
    foreignKey: "example_id",
    localKey: "id",
  }),
  ```

- **Create Indexes**: Add indexes for frequently queried fields:
  ```typescript
  .indexes([
    {
      name: "IDX_example_name",
      on: ["name"],
      unique: false,
      where: "deleted_at IS NULL",
    },
  ]);
  ```

### 4. API Route Implementation

- **Use Consistent Pattern**: Follow a consistent pattern for API routes:

  ```typescript
  export const GET = async (req: MedusaRequest, res: MedusaResponse) => {
    try {
      const service = req.scope.resolve(SERVICE_KEY);
      const result = await service.retrieve(req.params.id);
      res.json({ data: result });
    } catch (error) {
      handleError(error, res);
    }
  };
  ```

- **Use Workflows for Complex Operations**: Use workflows for complex operations:

  ```typescript
  export const POST = async (req: MedusaRequest, res: MedusaResponse) => {
    try {
      const { result } = await exampleWorkflow(req.scope).run({
        input: req.body,
      });
      res.status(200).json({ data: result });
    } catch (error) {
      handleError(error, res);
    }
  };
  ```

- **Validate Input**: Always validate input data:

  ```typescript
  const schema = z.object({
    name: z.string().min(1),
    description: z.string().optional(),
    // Other validations
  });

  const validatedData = schema.parse(req.body);
  ```

### 5. Error Handling

- **Use MedusaError**: Use MedusaError for consistent error handling:

  ```typescript
  throw new MedusaError(MedusaError.Types.INVALID_DATA, "Name is required");
  ```

- **Implement Consistent Error Handling**: Use a consistent error handling pattern:

  ```typescript
  function handleError(error, res) {
    const status = error.status || 500;
    const message = error.message || "An unknown error occurred";

    res.status(status).json({ message });
  }
  ```

- **Avoid Try-Catch Nesting**: Avoid deeply nested try-catch blocks.

### 6. Dependency Injection

- **Use Proper Dependency Injection**: Use dependency injection for services:

  ```typescript
  constructor(
    container: MedusaContainer,
    @Inject(Modules.PRODUCT) private readonly productService: IProductModuleService,
    @Inject("otherService") private readonly otherService?: any
  ) {
    super(container);
  }
  ```

- **Resolve Services from Container**: Always resolve services from the container:
  ```typescript
  const service = req.scope.resolve(SERVICE_KEY);
  ```

### 7. Logging

- **Use Proper Logging**: Use a proper logging service instead of console.log:

  ```typescript
  const logger = req.scope.resolve("logger");
  logger.info("Processing request", { requestId });
  ```

- **Log Appropriate Levels**: Use appropriate log levels:
  - `debug`: Detailed debugging information
  - `info`: General information
  - `warn`: Warning conditions
  - `error`: Error conditions

### 8. Testing

- **Write Unit Tests**: Write unit tests for all services and workflows:

  ```typescript
  describe("ExampleModuleService", () => {
    let container;
    let service;

    beforeEach(() => {
      container = createMedusaContainer();
      service = new ExampleModuleService(container);
    });

    it("should create an example", async () => {
      // Test implementation
    });
  });
  ```

- **Mock Dependencies**: Mock dependencies for unit tests:

  ```typescript
  const mockProductService = {
    retrieve: jest.fn().mockResolvedValue({ id: "product_123" }),
  };

  container.register({
    [Modules.PRODUCT]: { resolve: () => mockProductService },
  });
  ```

### 9. Documentation

- **Add JSDoc Comments**: Add JSDoc comments to all public methods:

  ```typescript
  /**
   * Creates a new example.
   *
   * @param {CreateExampleInput} data - The data for the new example
   * @returns {Promise<ExampleOutput>} The created example
   * @throws {MedusaError} If the input data is invalid or if creation fails
   */
  async createExample(data: CreateExampleInput): Promise<ExampleOutput> {
    // Implementation
  }
  ```

- **Document API Routes**: Document all API routes:
  ```typescript
  /**
   * GET /admin/examples/:id
   *
   * Retrieves an example by ID.
   *
   * @param {MedusaRequest} req - The request object
   * @param {MedusaResponse} res - The response object
   * @returns {Promise<void>}
   */
  export const GET = async (req: MedusaRequest, res: MedusaResponse) => {
    // Implementation
  };
  ```

### 10. Performance Considerations

- **Implement Pagination**: Always implement pagination for list endpoints:

  ```typescript
  const limit = parseInt(req.query.limit as string) || 10;
  const offset = parseInt(req.query.offset as string) || 0;

  const [data, count] = await service.list({}, { take: limit, skip: offset });

  res.json({
    data,
    count,
    limit,
    offset,
  });
  ```

- **Use Selective Field Retrieval**: Only retrieve needed fields:

  ```typescript
  const { data } = await queryService.graph({
    entity: "example",
    fields: ["id", "name", "description"],
    // Other options
  });
  ```

- **Implement Caching**: Use caching for frequently accessed data:

  ```typescript
  const cacheKey = `example_${id}`;
  const cached = await cacheService.get(cacheKey);

  if (cached) {
    return JSON.parse(cached);
  }

  const result = await retrieveFromDatabase(id);
  await cacheService.set(cacheKey, JSON.stringify(result), 3600); // Cache for 1 hour

  return result;
  ```

### 11. Avoid Mock Data and Implementations

- **Use Real Data**: Never use mock data or hardcoded values in production code:

  ```typescript
  // AVOID this in production code
  const mockProduct = {
    id: "prod_123",
    title: "Mock Product",
    price: 1000,
  };

  // INSTEAD, fetch real data from services
  const product = await productService.retrieve(productId);
  ```

- **No Mock Functions in Production**: Don't use mock implementations in production code:

  ```typescript
  // AVOID this in production code
  if (!productService) {
    return {
      createProduct: async () => ({ id: "mock-id" }),
      retrieveProduct: async () => ({ id: "mock-id", title: "Mock Product" }),
    };
  }

  // INSTEAD, throw a proper error if a service is unavailable
  if (!productService) {
    throw new MedusaError(
      MedusaError.Types.UNEXPECTED_STATE,
      "Product service is not available"
    );
  }
  ```

- **No Silent Fallbacks**: Don't silently fall back to default values when services fail:

  ```typescript
  // AVOID: Silent fallback to default values
  async function getPrice(productId) {
    try {
      const product = await productService.retrieve(productId);
      return product.price;
    } catch (error) {
      console.log("Error fetching price, using default:", error);
      return 1000; // Default price - DANGEROUS!
    }
  }

  // INSTEAD: Proper error handling with clear error messages
  async function getPrice(productId) {
    try {
      const product = await productService.retrieve(productId);
      return product.price;
    } catch (error) {
      throw new MedusaError(
        MedusaError.Types.INVALID_DATA,
        `Failed to get price for product ${productId}: ${error.message}`
      );
    }
  }
  ```

- **No Hardcoded Prices or Business Logic**: Never hardcode prices or business logic:

  ```typescript
  // AVOID: Hardcoded price calculation
  function calculateTotal(quantity) {
    // Hardcoded base price - DANGEROUS!
    const basePrice = 100;
    // Hardcoded discount logic - DANGEROUS!
    const discount = quantity > 10 ? 0.1 : 0;
    return quantity * basePrice * (1 - discount);
  }

  // INSTEAD: Use services to get prices and apply business logic
  async function calculateTotal(productId, quantity) {
    const pricingService = container.resolve(Modules.PRICING);
    const promotionService = container.resolve(Modules.PROMOTION);

    const price = await pricingService.getProductPrice(productId);
    const discount = await promotionService.getDiscount(productId, quantity);

    return quantity * price * (1 - discount);
  }
  ```

- **No Mock API Responses**: Don't create fake API responses:

  ```typescript
  // AVOID: Mock API responses
  export const GET = async (req: MedusaRequest, res: MedusaResponse) => {
    try {
      // Service not implemented yet, so return mock data
      if (process.env.NODE_ENV === "development") {
        return res.json({
          data: [
            { id: "room_1", name: "Deluxe Room", price: 200 },
            { id: "room_2", name: "Suite", price: 350 },
          ],
        });
      }

      // Real implementation (might never be reached!)
      const roomService = req.scope.resolve("roomService");
      const rooms = await roomService.list();
      return res.json({ data: rooms });
    } catch (error) {
      return res.status(500).json({ message: error.message });
    }
  };

  // INSTEAD: Implement the actual functionality or throw a clear error
  export const GET = async (req: MedusaRequest, res: MedusaResponse) => {
    try {
      const roomService = req.scope.resolve("roomService");
      const rooms = await roomService.list();
      return res.json({ data: rooms });
    } catch (error) {
      return res.status(500).json({
        message: "Room service not fully implemented: " + error.message
      });
    }
  };
  ```

- **Proper Fallbacks**: If fallbacks are necessary, use proper error handling and logging:

  ```typescript
  try {
    return await primaryMethod();
  } catch (error) {
    logger.warn(`Primary method failed: ${error.message}`);
    try {
      return await fallbackMethod();
    } catch (fallbackError) {
      throw new MedusaError(
        MedusaError.Types.UNEXPECTED_STATE,
        `Both primary and fallback methods failed. Primary: ${error.message}, Fallback: ${fallbackError.message}`
      );
    }
  }
  ```

- **Feature Flags**: Use feature flags instead of conditional mock implementations:

  ```typescript
  // AVOID
  const result = isProduction
    ? await realImplementation()
    : mockImplementation();

  // INSTEAD
  const result = await(
    featureFlags.isEnabled("feature_x")
      ? enhancedImplementation()
      : standardImplementation()
  );
  ```

### 12. Workflow Implementation

Workflows are the recommended way to implement business logic in Medusa. They provide a structured approach to handling complex operations and ensure proper separation of concerns.

#### Creating Workflows

1. **Define Workflow Structure**:

   ```typescript
   // src/workflows/example/workflows/example-workflow.ts
   import {
     createWorkflow,
     WorkflowResponse,
   } from "@camped-ai/framework/workflows-sdk";
   import { validateInputStep } from "../steps/validate-input";
   import { processDataStep } from "../steps/process-data";
   import { formatResponseStep } from "../steps/format-response";

   type WorkflowInput = {
     // Input type definition
     name: string;
     description?: string;
   };

   export const exampleWorkflow = createWorkflow(
     "example-workflow",
     (input: WorkflowInput) => {
       // Validate input
       const validatedInput = validateInputStep(input);

       // Process data
       const processedData = processDataStep(validatedInput);

       // Format response
       const response = formatResponseStep(processedData);

       return new WorkflowResponse(response);
     }
   );

   export default exampleWorkflow;
   ```

2. **Create Workflow Steps**:

   ```typescript
   // src/workflows/example/steps/validate-input.ts
   import { createStep, StepResponse } from "@camped-ai/framework/workflows-sdk";
   import { z } from "zod";

   const InputSchema = z.object({
     name: z.string().min(1, "Name is required"),
     description: z.string().optional(),
   });

   export const validateInputStep = createStep(
     "validate-input",
     async (input) => {
       const validatedInput = InputSchema.parse(input);
       return new StepResponse(validatedInput);
     }
   );
   ```

3. **Use Parallel Processing When Possible**:

   ```typescript
   import { parallelize } from "@camped-ai/framework/workflows-sdk";

   // In your workflow
   const [result1, result2] = parallelize(
     step1(input),
     step2(input)
   );
   ```

4. **Add Workflow Hooks**:

   ```typescript
   import { createHook } from "@camped-ai/framework/workflows-sdk";

   // In your workflow
   const dataProcessedHook = createHook(
     "data-processed",
     { id: processedData.id }
   );

   return new WorkflowResponse(response, {
     hooks: [dataProcessedHook],
   });
   ```

5. **Execute Workflows from API Routes**:

   ```typescript
   // src/api/admin/example/route.ts
   import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";
   import exampleWorkflow from "../../../workflows/example/workflows/example-workflow";

   export const POST = async (req: MedusaRequest, res: MedusaResponse) => {
     try {
       const { result } = await exampleWorkflow(req.scope).run({
         input: req.body,
       });

       res.status(200).json({ data: result });
     } catch (error) {
       res.status(500).json({
         message: error.message,
         code: error.code || "unknown_error"
       });
     }
   };
   ```

### 13. Event Subscribers Implementation

Event subscribers allow you to react to events in the system. They are a powerful way to implement cross-cutting concerns and decouple business logic.

1. **Create a Subscriber**:

   ```typescript
   // src/subscribers/example-subscriber.ts
   import { EventBusService } from "@camped-ai/framework/types";
   import { MedusaContainer } from "@camped-ai/framework/types";

   type InjectedDependencies = {
     eventBusService: EventBusService;
   };

   class ExampleSubscriber {
     protected readonly eventBusService_: EventBusService;
     protected readonly container_: MedusaContainer;

     constructor({ eventBusService }: InjectedDependencies, container: MedusaContainer) {
       this.eventBusService_ = eventBusService;
       this.container_ = container;
       this.registerSubscribers();
     }

     registerSubscribers(): void {
       // Subscribe to events
       this.eventBusService_.subscribe("example.created", this.handleExampleCreated);
       this.eventBusService_.subscribe("example.updated", this.handleExampleUpdated);
     }

     handleExampleCreated = async (data: Record<string, any>): Promise<void> => {
       try {
         // Handle the event
         console.log("Example created:", data);

         // Resolve services as needed
         const exampleService = this.container_.resolve("exampleService");
         await exampleService.doSomething(data.id);
       } catch (error) {
         console.error("Error handling example.created event:", error);
       }
     };

     handleExampleUpdated = async (data: Record<string, any>): Promise<void> => {
       // Handle the event
     };
   }

   export default ExampleSubscriber;
   ```

2. **Register the Subscriber**:

   ```typescript
   // src/subscribers/index.ts
   import ExampleSubscriber from "./example-subscriber";

   export default [
     ExampleSubscriber,
     // Other subscribers
   ];
   ```

3. **Emit Events from Services or Workflows**:

   ```typescript
   // In a service
   await this.eventBusService_.emit("example.created", {
     id: example.id,
     // Other data
   });

   // In a workflow step
   const emitEventStep = createStep(
     "emit-event",
     async (input, { container }) => {
       const eventBusService = container.resolve("eventBusService");

       await eventBusService.emit("example.created", {
         id: input.id,
         // Other data
       });

       return new StepResponse(input);
     }
   );
   ```

### 14. Service Resolution Best Practices

Proper service resolution is critical for avoiding common errors in Medusa applications.

#### Using Module Constants

Always use the predefined constants from the `Modules` enum instead of string literals:

```typescript
import { Modules } from "@camped-ai/framework/utils";

// Correct way
const orderService = container.resolve(Modules.ORDER);
const cartService = container.resolve(Modules.CART);
const productService = container.resolve(Modules.PRODUCT);

// Incorrect way - prone to typos and errors
const orderService = container.resolve("order");
const cartService = container.resolve("cart");
```

#### Common Module Constants

Here's a list of commonly used module constants:

```typescript
// Core Commerce Modules
Modules.PRODUCT        // Product module
Modules.ORDER          // Order module
Modules.CART           // Cart module
Modules.CUSTOMER       // Customer module
Modules.USER           // User module
Modules.PAYMENT        // Payment module
Modules.SHIPPING       // Shipping module
Modules.INVENTORY      // Inventory module
Modules.STORE          // Store module
Modules.REGION         // Region module
Modules.CURRENCY       // Currency module
Modules.TAX            // Tax module
Modules.PRICING        // Pricing module
Modules.PROMOTION      // Promotion module
Modules.FULFILLMENT    // Fulfillment module
Modules.SALES_CHANNEL  // Sales channel module
Modules.STOCK_LOCATION // Stock location module

// Architectural Modules
Modules.AUTH           // Authentication module
Modules.FILE           // File storage module
Modules.NOTIFICATION   // Notification module
Modules.EVENT_BUS      // Event bus module
Modules.CACHE          // Cache module
Modules.API_KEY        // API key module
```

#### Custom Module Constants

For your custom modules, create and export constants for service names:

```typescript
// src/modules/hotel-management/room-inventory/index.ts
import { Module } from "@camped-ai/framework/utils";
import RoomInventoryService from "./service";

export const ROOM_INVENTORY_MODULE = "roomInventoryService";

export const roomInventoryModule = Module(ROOM_INVENTORY_MODULE, {
  service: RoomInventoryService,
});

export default roomInventoryModule;
```

Then import and use these constants when resolving services:

```typescript
import { ROOM_INVENTORY_MODULE } from "../modules/hotel-management/room-inventory";

const roomInventoryService = container.resolve(ROOM_INVENTORY_MODULE);
```

#### Dependency Injection in Services

Use dependency injection in your service constructor:

```typescript
import { Modules } from "@camped-ai/framework/utils";
import { MedusaService } from "@camped-ai/framework/utils";
import { Inject } from "awilix";

class ExampleService extends MedusaService({
  ExampleModel,
}) {
  constructor(
    container,
    @Inject(Modules.ORDER) private readonly orderService,
    @Inject(Modules.CART) private readonly cartService,
    @Inject("roomInventoryService") private readonly roomInventoryService
  ) {
    super(container);
  }

  // Now you can use this.orderService, this.cartService, etc.
}
```

#### Debugging Service Resolution

When you encounter service resolution errors, use these debugging techniques:

```typescript
// List all registered services
console.log("Registered services:", Object.keys(container.registrations));

// Check if a specific service is registered
console.log(
  `Is order service registered: ${container.hasRegistration(Modules.ORDER)}`
);

// Try to resolve with error handling
try {
  const orderService = container.resolve(Modules.ORDER);
  console.log("Successfully resolved order service");
} catch (error) {
  console.error("Failed to resolve order service:", error.message);
}
```

### 15. Common Errors and Solutions

#### Service Registration and Resolution Errors

1. **"Service is not registered" or "Could not resolve service" errors**

   **Problem**: These errors occur when trying to resolve a service that hasn't been properly registered in the container.

   **Solutions**:

   - **Use the correct module constant**:

     ```typescript
     // Incorrect
     const orderService = container.resolve("orderService");

     // Correct
     import { Modules } from "@camped-ai/framework/utils";
     const orderService = container.resolve(Modules.ORDER);
     ```

   - **Check service registration in modules**:

     ```typescript
     // Make sure your module is properly defined
     export const exampleModule = Module(EXAMPLE_MODULE, {
       service: ExampleModuleService,
     });

     // And imported in src/modules/index.ts
     import exampleModule from "./example-module";

     export default [
       // other modules
       exampleModule,
     ];
     ```

   - **Register service manually in a loader**:

     ```typescript
     // In src/loaders/example-module.ts
     export default async (container: MedusaContainer): Promise<void> => {
       try {
         if (!container.hasRegistration(EXAMPLE_SERVICE)) {
           container.register({
             [EXAMPLE_SERVICE]: {
               resolve: () => new ExampleService(container),
             },
           });
         }
       } catch (error) {
         console.error("Failed to register example service:", error);
       }
     };

     // Make sure to add this loader to src/loaders/index.ts
     ```

   - **Check for circular dependencies**:

     - Services that depend on each other can cause resolution issues
     - Break circular dependencies by using a third service or restructuring

   - **Use consistent service keys**:

     ```typescript
     // Define constants for service keys
     export const EXAMPLE_SERVICE = "exampleService";

     // Use the same key when registering and resolving
     container.register({
       [EXAMPLE_SERVICE]: { resolve: () => new ExampleService(container) },
     });

     const service = container.resolve(EXAMPLE_SERVICE);
     ```

2. **Service methods not found or undefined**

   **Problem**: Service is resolved but methods are undefined or not found.

   **Solutions**:

   - **Check service implementation**:

     ```typescript
     // Make sure methods are properly defined
     class ExampleService extends MedusaService({
       Example,
     }) {
       async retrieve(id: string): Promise<any> {
         // Implementation
       }
     }
     ```

   - **Check inheritance chain**:

     - Ensure the service properly extends MedusaService
     - Make sure all required models are passed to MedusaService

   - **Use proper TypeScript interfaces**:

     ```typescript
     interface IExampleService {
       retrieve(id: string): Promise<any>;
       // Other methods
     }

     // Then use this interface when resolving
     const exampleService = req.scope.resolve(
       EXAMPLE_SERVICE
     ) as IExampleService;
     ```

3. **Database-related service errors**

   **Problem**: Services fail when trying to access the database.

   **Solutions**:

   - **Check database connection**:

     - Ensure DATABASE_URL is properly set in environment variables
     - Verify database credentials and connection parameters

   - **Check model definitions**:

     ```typescript
     // Make sure models are properly defined
     export const Example = model.define("example", {
       id: model.id().primaryKey(),
       // Other fields
     });
     ```

   - **Run migrations**:

     ```bash
     # Make sure all migrations are applied
     npx medusa migrations run
     ```

   - **Check for transaction issues**:
     - Ensure transactions are properly committed or rolled back
     - Use the transaction manager provided by the service

By following these best practices and solutions, you'll ensure that your code is consistent, maintainable, and scalable.
