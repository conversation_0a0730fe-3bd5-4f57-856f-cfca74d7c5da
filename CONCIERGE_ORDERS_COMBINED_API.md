# Combined Concierge Orders API

## Overview

The `/admin/concierge-management/orders` GET endpoint has been enhanced to return combined data from both the `concierge_orders` and `orders` tables. This eliminates the need for separate API calls to get order details when listing concierge orders.

## Changes Made

### 1. New Service Method
- Added `listConciergeOrdersWithOrderData()` method to `ConciergeManagementService`
- Fetches concierge orders and their related order data in a single operation
- Includes comprehensive error handling and logging

### 2. Enhanced API Response
The endpoint now returns concierge orders with embedded order information:

```json
{
  "concierge_orders": [
    {
      "id": "concierge_order_id",
      "order_id": "order_123",
      "assigned_to": "user_id",
      "notes": "Customer needs special assistance",
      "status": "IN_PROGRESS",
      "created_at": "2024-01-01T00:00:00Z",
      "updated_at": "2024-01-01T00:00:00Z",
      "order": {
        "id": "order_123",
        "display_id": 1001,
        "email": "<EMAIL>",
        "status": "pending",
        "customer": {
          "id": "customer_id",
          "first_name": "<PERSON>",
          "last_name": "Do<PERSON>",
          "email": "<EMAIL>"
        },
        "items": [...],
        "shipping_address": {...},
        "billing_address": {...},
        "payments": [...],
        "fulfillments": [...]
      }
    }
  ],
  "count": 25,
  "limit": 20,
  "offset": 0
}
```

### 3. Enhanced Search Functionality
The `q` parameter now searches across both concierge order and order fields:

**Concierge Order Fields:**
- notes
- assigned_to
- status

**Order Fields:**
- email
- display_id
- customer.first_name
- customer.last_name
- customer.email

### 4. Maintained Compatibility
- All existing query parameters are preserved
- Pagination (`limit`, `offset`) works as before
- Filtering (`status`, `assigned_to`, `order_id`, date ranges) works as before
- Sorting (`order`, `sort_order`) works as before

## API Usage

### Basic Request
```bash
GET /admin/concierge-management/orders?limit=20&offset=0
```

### With Search
```bash
GET /admin/concierge-management/orders?q=john&limit=20
```

### With Filters
```bash
GET /admin/concierge-management/orders?status=IN_PROGRESS&assigned_to=user_123&limit=20
```

### With Date Range
```bash
GET /admin/concierge-management/orders?created_at_gte=2024-01-01T00:00:00Z&created_at_lte=2024-01-31T23:59:59Z
```

## Performance Considerations

1. **Batch Order Fetching**: Orders are fetched in parallel using Promise.all()
2. **Error Resilience**: If individual orders fail to load, the endpoint continues with available data
3. **Graceful Degradation**: If the order service is unavailable, concierge orders are returned with `order: null`
4. **Logging**: Comprehensive logging helps with debugging and monitoring

## Error Handling

- Individual order fetch failures are logged but don't break the entire response
- Order service unavailability is handled gracefully
- Search functionality is applied after data fetching to ensure accuracy
- Proper HTTP status codes and error messages are returned

## Testing

Run the test script to verify functionality:
```bash
node test-concierge-orders-combined.js
```

The test script checks:
- Basic endpoint accessibility
- Response structure validation
- Search functionality
- Filter functionality
- Error handling

## Migration Notes

- **Frontend**: Update frontend code to use the embedded `order` field instead of making separate API calls
- **Backward Compatibility**: The response structure is additive, so existing code should continue to work
- **Performance**: Expect improved performance due to reduced API calls, but individual requests may take slightly longer due to additional data fetching

## Files Modified

1. `src/modules/concierge-management/service.ts` - Added new service method
2. `src/modules/concierge-management/types.ts` - Added new response types and search filter
3. `src/api/admin/concierge-management/orders/route.ts` - Updated to use new service method
4. `test-concierge-orders-combined.js` - Test script for verification
