# Order Management API - Postman Collection Usage Guide

This guide provides detailed instructions for using the Order Management API Postman collection effectively.

## 🎯 Prerequisites

### Software Requirements
- **Postman Desktop App** (recommended) or Postman Web
- **Node.js** (v14+) for automated testing with Newman
- **Medusa Store v2** API server running

### API Server Setup
Ensure your Medusa API server is running and accessible:
```bash
# Start the Medusa server
cd /path/to/medusa-store-v2
yarn dev

# Verify server is running
curl http://localhost:9000/health
```

## 📥 Installation & Setup

### Step 1: Import Collection
1. Open Postman
2. Click **Import** button (top left)
3. Drag and drop or select these files:
   - `Order-Management-API.postman_collection.json`
   - `Order-Management-API.postman_environment.json`

### Step 2: Configure Environment
1. Select **"Order Management API Environment"** from the environment dropdown
2. Click the **eye icon** to view/edit environment variables
3. Update variables as needed:

| Variable | Purpose | Example Value |
|----------|---------|---------------|
| `base_url` | API server URL | `http://localhost:9000` |
| `admin_email` | Admin login email | `<EMAIL>` |
| `admin_password` | Admin password | `admin123` |

### Step 3: Test Connection
Run the **"Admin Login"** request to verify connectivity and authentication.

## 🚀 Running the Collection

### Manual Execution (Recommended for Development)

#### 1. Authentication First
Always start with authentication:
- **🔐 Authentication** → **Admin Login**
- Verify the `auth_token` variable is set automatically

#### 2. Basic Order Workflow
Follow this sequence for a complete order lifecycle:

1. **Create Order**
   ```
   📦 Orders CRUD → Create Order
   ```
   - Creates order with hotel room and breakfast items
   - Sets `order_id` variable automatically

2. **List Orders**
   ```
   📦 Orders CRUD → List Orders
   ```
   - Verify your created order appears in the list

3. **Get Order Details**
   ```
   📦 Orders CRUD → Get Order by ID
   ```
   - Retrieves full order details using `order_id`

4. **Add Items (Draft Mode)**
   ```
   🛒 Order Items Management → Add Items to Order (Draft Mode)
   ```
   - Adds spa treatment and airport transfer

5. **View Order Items**
   ```
   🛒 Order Items Management → Get Order Items
   ```
   - Lists all items in the order

6. **Update Order**
   ```
   📦 Orders CRUD → Update Order
   ```
   - Changes status to "completed"

7. **Add Items (Confirmed Mode)**
   ```
   🛒 Order Items Management → Add Items to Order (Confirmed Mode)
   ```
   - Adds late checkout fee using order edit workflow

#### 3. Error Testing
Test error scenarios:
```
❌ Error Scenarios & Edge Cases → [Any error test]
```

### Automated Execution with Newman

#### Install Newman
```bash
# Global installation
npm install -g newman

# Or local installation in postman directory
cd postman
npm install
```

#### Run Complete Test Suite
```bash
# Basic run
newman run Order-Management-API.postman_collection.json \
  -e Order-Management-API.postman_environment.json

# With HTML report
newman run Order-Management-API.postman_collection.json \
  -e Order-Management-API.postman_environment.json \
  --reporters cli,html \
  --reporter-html-export test-results/report.html
```

#### Using the Test Runner Script
```bash
# Basic test run
node run-tests.js

# Verbose output with reports
node run-tests.js --verbose --html-report

# Test specific folder only
node run-tests.js --folder "📦 Orders CRUD"

# CI/CD integration
node run-tests.js --junit --verbose
```

## 🔧 Customization Guide

### Modifying Test Data

#### Order Creation Data
Edit the **Create Order** request body to match your test scenarios:

```json
{
  "email": "<EMAIL>",
  "currency_code": "EUR",  // Change currency
  "items": [
    {
      "variant_id": "your_variant_id",
      "title": "Your Product Name",
      "quantity": 1,
      "unit_price": 10000  // Price in cents
    }
  ]
}
```

#### Environment Variables
Add custom variables for your test data:

1. Go to **Environment** settings
2. Add new variables:
   ```
   custom_variant_id = "variant_12345"
   test_customer_id = "cust_67890"
   ```
3. Use in requests: `{{custom_variant_id}}`

### Adding New Test Cases

#### 1. Duplicate Existing Request
- Right-click on a similar request
- Select **Duplicate**
- Rename appropriately

#### 2. Modify Request Data
- Update URL, headers, body as needed
- Add/modify test scripts

#### 3. Update Test Scripts
Example test script for validation:
```javascript
pm.test("Custom validation", function () {
    var jsonData = pm.response.json();
    pm.expect(jsonData.custom_field).to.exist;
    pm.expect(jsonData.custom_field).to.eql("expected_value");
});
```

## 🧪 Testing Strategies

### Development Testing
- Run individual requests during development
- Use **Console** tab to debug test scripts
- Verify environment variables are set correctly

### Integration Testing
- Run complete collection to test full workflow
- Test both happy path and error scenarios
- Verify data consistency across requests

### Regression Testing
- Run collection after code changes
- Use automated Newman runs in CI/CD
- Compare results with baseline

### Performance Testing
- Add timing assertions to test scripts
- Monitor response times
- Test with larger datasets

## 🔍 Debugging Tips

### Common Issues

#### 1. Authentication Failures
```
Status: 401 Unauthorized
```
**Solutions:**
- Verify admin credentials in environment
- Check if auth_token is set after login
- Ensure server is running and accessible

#### 2. Order Not Found
```
Status: 404 Not Found
```
**Solutions:**
- Verify order_id variable is set
- Check if order was created successfully
- Ensure you're using the correct order ID

#### 3. Validation Errors
```
Status: 400 Bad Request
```
**Solutions:**
- Check request body format
- Verify required fields are present
- Validate data types and constraints

### Debugging Test Scripts

#### View Console Output
1. Open **Postman Console** (bottom panel)
2. Add debug statements to test scripts:
```javascript
console.log("Response data:", pm.response.json());
console.log("Environment variable:", pm.environment.get("order_id"));
```

#### Test Script Debugging
```javascript
// Debug response structure
pm.test("Debug response", function () {
    console.log("Full response:", pm.response.json());
    console.log("Status code:", pm.response.code);
    console.log("Headers:", pm.response.headers);
});
```

## 📊 Monitoring & Reporting

### Newman HTML Reports
Generate detailed HTML reports:
```bash
newman run collection.json -e environment.json \
  --reporters html \
  --reporter-html-export report.html
```

### CI/CD Integration
Example GitHub Actions workflow:
```yaml
name: API Tests
on: [push, pull_request]
jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - uses: actions/setup-node@v2
        with:
          node-version: '16'
      - run: npm install -g newman
      - run: |
          newman run postman/Order-Management-API.postman_collection.json \
            -e postman/Order-Management-API.postman_environment.json \
            --reporters junit \
            --reporter-junit-export results.xml
```

### Custom Reporting
Create custom reports by parsing Newman JSON output:
```javascript
// In test script
pm.test("Custom metric", function () {
    var responseTime = pm.response.responseTime;
    pm.globals.set("response_time_" + pm.info.requestName, responseTime);
});
```

## 🤝 Best Practices

### Request Organization
- Use descriptive names for requests
- Group related requests in folders
- Add comprehensive descriptions

### Test Script Guidelines
- Write clear, descriptive test names
- Test both positive and negative scenarios
- Include boundary value testing
- Validate response structure and data

### Environment Management
- Use separate environments for dev/staging/prod
- Keep sensitive data in environment variables
- Document required environment variables

### Data Management
- Use realistic test data
- Clean up test data when possible
- Avoid hardcoded values in requests

## 🆘 Troubleshooting

### Collection Import Issues
- Ensure JSON files are valid
- Check Postman version compatibility
- Try importing one file at a time

### Environment Variable Issues
- Verify variable names match exactly
- Check variable scope (environment vs global)
- Ensure variables are enabled

### Network Issues
- Check firewall settings
- Verify SSL certificate handling
- Test with curl to isolate issues

### Test Failures
- Review test script logic
- Check response format changes
- Verify API endpoint availability

## 📞 Support

For additional help:
1. Check the main [README.md](README.md)
2. Review [API Documentation](../docs/order-management-api.md)
3. Check Postman Console for detailed error messages
4. Verify server logs for API-side issues

## 🔗 Additional Resources

- [Postman Learning Center](https://learning.postman.com/)
- [Newman Documentation](https://github.com/postmanlabs/newman)
- [Medusa.js Documentation](https://docs.medusajs.com/)
- [Order Management API Docs](../docs/order-management-api.md)
