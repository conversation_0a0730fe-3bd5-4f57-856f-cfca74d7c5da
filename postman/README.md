# Order Management API - Postman Collection

This directory contains a comprehensive Postman collection for testing the Order Management API with full CRUD operations for orders and order items management.

## 📁 Files Included

- `Order-Management-API.postman_collection.json` - Main Postman collection
- `Order-Management-API.postman_environment.json` - Environment variables
- `README.md` - This documentation file

## 🚀 Quick Start

### 1. Import Collection and Environment

1. Open Postman
2. Click "Import" button
3. Import both files:
   - `Order-Management-API.postman_collection.json`
   - `Order-Management-API.postman_environment.json`
4. Select the "Order Management API Environment" in the environment dropdown

### 2. Configure Environment Variables

Update the following variables in your environment:

| Variable | Description | Default Value |
|----------|-------------|---------------|
| `base_url` | API base URL | `http://localhost:9000` |
| `admin_email` | Admin email for authentication | `<EMAIL>` |
| `admin_password` | Admin password | `admin123` |
| `auth_token` | Bearer token (auto-set) | _(empty)_ |
| `order_id` | Dynamic order ID (auto-set) | _(empty)_ |
| `item_id` | Dynamic item ID (auto-set) | _(empty)_ |

### 3. Run the Collection

**Recommended execution order:**

1. **🔐 Authentication** → Admin Login
2. **📦 Orders CRUD** → Create Order
3. **📦 Orders CRUD** → List Orders
4. **📦 Orders CRUD** → Get Order by ID
5. **🛒 Order Items Management** → Add Items to Order (Draft Mode)
6. **🛒 Order Items Management** → Get Order Items
7. **📦 Orders CRUD** → Update Order
8. **🛒 Order Items Management** → Add Items to Order (Confirmed Mode)
9. **❌ Error Scenarios** → (Run any error test)
10. **📦 Orders CRUD** → Delete Draft Order

## 📋 Collection Structure

### 🔐 Authentication
- **Admin Login**: Authenticates and sets the `auth_token` variable

### 📦 Orders CRUD
- **Create Order**: Creates a new order with items and addresses
- **List Orders**: Retrieves orders with filtering and pagination
- **Get Order by ID**: Fetches a specific order with full details
- **Update Order**: Updates order status, email, and addresses
- **Delete Draft Order**: Deletes a draft order (business logic enforced)

### 🛒 Order Items Management
- **Add Items to Order (Draft Mode)**: Adds items using draft workflow
- **Add Items to Order (Confirmed Mode)**: Adds items using order edit workflow
- **Get Order Items**: Retrieves all items for an order

### ❌ Error Scenarios & Edge Cases
- **Create Order - Invalid Data**: Tests validation errors
- **Get Non-existent Order**: Tests 404 error handling
- **Unauthorized Access**: Tests authentication requirements
- **Delete Non-Draft Order**: Tests business logic validation
- **Add Items - Empty Items Array**: Tests item validation

## 🔧 Features

### Automated Testing
Each request includes comprehensive test scripts that validate:
- HTTP status codes
- Response structure and data types
- Business logic compliance
- Error message formats

### Dynamic Variables
The collection automatically manages variables:
- `auth_token` - Set by login request
- `order_id` - Set by create order request
- `item_id` - Set by add items request

### Realistic Test Data
All requests use realistic hotel booking data:
- Hotel room bookings
- Breakfast packages
- Spa treatments
- Airport transfers
- Customer addresses and preferences

## 📊 Test Data Examples

### Order Creation Data
```json
{
  "email": "<EMAIL>",
  "currency_code": "USD",
  "is_draft_order": true,
  "items": [
    {
      "variant_id": "variant_test_001",
      "title": "Premium Hotel Room",
      "subtitle": "Deluxe King Room with Ocean View",
      "quantity": 1,
      "unit_price": 25000
    }
  ],
  "shipping_address": {
    "first_name": "John",
    "last_name": "Doe",
    "address_1": "123 Ocean Drive",
    "city": "Miami",
    "country_code": "US"
  }
}
```

### Order Items Addition (Draft Mode)
```json
{
  "items": [
    {
      "variant_id": "variant_addon_001",
      "title": "Spa Treatment Package",
      "quantity": 1,
      "unit_price": 15000,
      "metadata": {
        "treatment_type": "couples_massage",
        "duration_minutes": 90
      }
    }
  ],
  "mode": "draft"
}
```

### Order Items Addition (Confirmed Mode)
```json
{
  "items": [
    {
      "variant_id": "variant_addon_003",
      "title": "Late Checkout Fee",
      "quantity": 1,
      "unit_price": 7500,
      "metadata": {
        "checkout_time": "15:00",
        "additional_hours": 4
      }
    }
  ],
  "mode": "confirmed"
}
```

## 🧪 Testing Scenarios

### Happy Path Testing
1. Create a new order with items
2. List orders and verify the created order appears
3. Retrieve the specific order and verify details
4. Add additional items in draft mode
5. Update order status to completed
6. Add items in confirmed mode (uses order edit workflow)
7. Retrieve order items and verify all items are present

### Error Handling Testing
1. **Validation Errors**: Invalid data formats, missing required fields
2. **Authentication Errors**: Missing or invalid tokens
3. **Authorization Errors**: Insufficient permissions
4. **Business Logic Errors**: Attempting invalid operations
5. **Not Found Errors**: Non-existent resources

### Edge Cases
1. Empty items arrays
2. Negative quantities or prices
3. Invalid currency codes
4. Malformed addresses
5. Conflicting order statuses

## 🔍 Response Validation

Each request includes automated tests that verify:

### Success Responses
- Correct HTTP status codes (200, 201)
- Required fields presence
- Data type validation
- Business logic compliance

### Error Responses
- Appropriate error status codes (400, 401, 404, 500)
- Error message format and content
- Error type classification

## 🛠 Customization

### Adding New Test Cases
1. Duplicate an existing request
2. Modify the request data
3. Update the test scripts
4. Add appropriate documentation

### Environment Configuration
Update environment variables for different environments:
- Development: `http://localhost:9000`
- Staging: `https://staging-api.example.com`
- Production: `https://api.example.com`

### Authentication Methods
The collection supports Bearer token authentication. To use different auth methods:
1. Update the authentication request
2. Modify the token extraction logic
3. Update header configurations

## 📈 Monitoring and Reporting

### Running Collection with Newman
```bash
# Install Newman
npm install -g newman

# Run collection
newman run Order-Management-API.postman_collection.json \
  -e Order-Management-API.postman_environment.json \
  --reporters cli,html \
  --reporter-html-export report.html
```

### CI/CD Integration
The collection can be integrated into CI/CD pipelines for automated API testing:
```yaml
# Example GitHub Actions step
- name: Run API Tests
  run: |
    newman run postman/Order-Management-API.postman_collection.json \
      -e postman/Order-Management-API.postman_environment.json \
      --reporters junit \
      --reporter-junit-export results.xml
```

## 🤝 Contributing

When adding new requests to the collection:
1. Follow the existing naming conventions
2. Include comprehensive test scripts
3. Add realistic test data
4. Update this documentation
5. Test thoroughly before committing

## 📞 Support

For issues with the collection:
1. Check environment variable configuration
2. Verify API server is running
3. Ensure authentication is working
4. Review test script console output
5. Check API documentation for changes

## 🔗 Related Documentation

- [Order Management API Documentation](../docs/order-management-api.md)
- [Order Management Implementation Guide](../src/api/admin/orders/README.md)
- [Medusa.js Documentation](https://docs.medusajs.com)
- [Postman Documentation](https://learning.postman.com)
