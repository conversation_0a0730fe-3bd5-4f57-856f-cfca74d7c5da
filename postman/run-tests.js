#!/usr/bin/env node

/**
 * Order Management API Test Runner
 * 
 * This script runs the <PERSON><PERSON> collection using <PERSON> for automated testing.
 * It can be used in CI/CD pipelines or for local testing.
 * 
 * Usage:
 *   node run-tests.js [environment] [options]
 * 
 * Examples:
 *   node run-tests.js                    # Run with default environment
 *   node run-tests.js --verbose          # Run with detailed output
 *   node run-tests.js --folder "Orders CRUD"  # Run specific folder
 *   node run-tests.js --html-report      # Generate HTML report
 */

const newman = require('newman');
const path = require('path');
const fs = require('fs');

// Configuration
const config = {
  collection: path.join(__dirname, 'Order-Management-API.postman_collection.json'),
  environment: path.join(__dirname, 'Order-Management-API.postman_environment.json'),
  outputDir: path.join(__dirname, 'test-results'),
  defaultOptions: {
    reporters: ['cli'],
    delayRequest: 500, // 500ms delay between requests
    timeout: 30000,    // 30 second timeout
    insecure: true,    // Allow self-signed certificates
    color: 'on'
  }
};

// Parse command line arguments
const args = process.argv.slice(2);
const options = { ...config.defaultOptions };

// Parse arguments
args.forEach((arg, index) => {
  switch (arg) {
    case '--verbose':
      options.reporters = ['cli', 'json'];
      options.reporter = {
        json: {
          export: path.join(config.outputDir, 'results.json')
        }
      };
      break;
    
    case '--html-report':
      options.reporters.push('html');
      options.reporter = {
        ...options.reporter,
        html: {
          export: path.join(config.outputDir, 'report.html')
        }
      };
      break;
    
    case '--junit':
      options.reporters.push('junit');
      options.reporter = {
        ...options.reporter,
        junit: {
          export: path.join(config.outputDir, 'junit-report.xml')
        }
      };
      break;
    
    case '--folder':
      const folderName = args[index + 1];
      if (folderName) {
        options.folder = folderName;
      }
      break;
    
    case '--environment':
      const envFile = args[index + 1];
      if (envFile && fs.existsSync(envFile)) {
        config.environment = envFile;
      }
      break;
    
    case '--help':
      showHelp();
      process.exit(0);
      break;
  }
});

// Create output directory if it doesn't exist
if (!fs.existsSync(config.outputDir)) {
  fs.mkdirSync(config.outputDir, { recursive: true });
}

// Show help
function showHelp() {
  console.log(`
Order Management API Test Runner

Usage: node run-tests.js [options]

Options:
  --verbose           Enable verbose output with JSON report
  --html-report       Generate HTML test report
  --junit             Generate JUnit XML report for CI/CD
  --folder <name>     Run tests from specific folder only
  --environment <file> Use custom environment file
  --help              Show this help message

Examples:
  node run-tests.js                           # Basic test run
  node run-tests.js --verbose --html-report   # Full reporting
  node run-tests.js --folder "Orders CRUD"    # Test specific folder
  node run-tests.js --junit                   # CI/CD integration

Folders available:
  - "🔐 Authentication"
  - "📦 Orders CRUD"
  - "🛒 Order Items Management"
  - "❌ Error Scenarios & Edge Cases"
`);
}

// Run the collection
function runTests() {
  console.log('🚀 Starting Order Management API Tests...\n');
  console.log(`Collection: ${config.collection}`);
  console.log(`Environment: ${config.environment}`);
  console.log(`Output Directory: ${config.outputDir}\n`);

  newman.run({
    collection: config.collection,
    environment: config.environment,
    ...options
  }, function (err, summary) {
    if (err) {
      console.error('❌ Test execution failed:', err);
      process.exit(1);
    }

    console.log('\n📊 Test Summary:');
    console.log(`Total Requests: ${summary.run.stats.requests.total}`);
    console.log(`Passed: ${summary.run.stats.requests.total - summary.run.stats.requests.failed}`);
    console.log(`Failed: ${summary.run.stats.requests.failed}`);
    console.log(`Total Assertions: ${summary.run.stats.assertions.total}`);
    console.log(`Assertion Failures: ${summary.run.stats.assertions.failed}`);

    if (summary.run.failures && summary.run.failures.length > 0) {
      console.log('\n❌ Test Failures:');
      summary.run.failures.forEach((failure, index) => {
        console.log(`${index + 1}. ${failure.source.name || 'Unknown'}`);
        console.log(`   Error: ${failure.error.message}`);
        if (failure.error.test) {
          console.log(`   Test: ${failure.error.test}`);
        }
      });
      process.exit(1);
    } else {
      console.log('\n✅ All tests passed successfully!');
      
      if (options.reporter && options.reporter.html) {
        console.log(`📄 HTML Report: ${options.reporter.html.export}`);
      }
      
      if (options.reporter && options.reporter.json) {
        console.log(`📄 JSON Report: ${options.reporter.json.export}`);
      }
      
      if (options.reporter && options.reporter.junit) {
        console.log(`📄 JUnit Report: ${options.reporter.junit.export}`);
      }
      
      process.exit(0);
    }
  });
}

// Validate files exist
if (!fs.existsSync(config.collection)) {
  console.error(`❌ Collection file not found: ${config.collection}`);
  process.exit(1);
}

if (!fs.existsSync(config.environment)) {
  console.error(`❌ Environment file not found: ${config.environment}`);
  process.exit(1);
}

// Check if Newman is installed
try {
  require.resolve('newman');
} catch (e) {
  console.error('❌ Newman is not installed. Please install it with: npm install -g newman');
  process.exit(1);
}

// Run the tests
runTests();
