{"name": "order-management-api-tests", "version": "1.0.0", "description": "Postman collection and automated tests for Order Management API", "main": "run-tests.js", "scripts": {"test": "node run-tests.js", "test:verbose": "node run-tests.js --verbose", "test:html": "node run-tests.js --html-report", "test:ci": "node run-tests.js --junit --verbose", "test:orders": "node run-tests.js --folder \"📦 Orders CRUD\"", "test:items": "node run-tests.js --folder \"🛒 Order Items Management\"", "test:errors": "node run-tests.js --folder \"❌ Error Scenarios & Edge Cases\"", "install-newman": "npm install -g newman", "help": "node run-tests.js --help"}, "keywords": ["postman", "api-testing", "order-management", "medusa", "e-commerce", "automation"], "author": "Order Management API Team", "license": "MIT", "devDependencies": {"newman": "^6.0.0"}, "engines": {"node": ">=14.0.0"}, "repository": {"type": "git", "url": "git+https://github.com/your-org/medusa-store-v2.git"}, "bugs": {"url": "https://github.com/your-org/medusa-store-v2/issues"}, "homepage": "https://github.com/your-org/medusa-store-v2#readme"}