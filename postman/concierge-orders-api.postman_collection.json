{"info": {"name": "Concierge Orders API", "description": "Comprehensive API collection for testing Concierge Order & Item management endpoints", "version": "1.0.0", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{ADMIN_TOKEN}}", "type": "string"}]}, "variable": [{"key": "BASE_URL", "value": "http://localhost:9000", "type": "string"}, {"key": "ADMIN_TOKEN", "value": "your_admin_token_here", "type": "string"}, {"key": "order_id", "value": "order_01234567890", "type": "string"}, {"key": "concierge_order_id", "value": "", "type": "string"}, {"key": "concierge_order_item_id", "value": "", "type": "string"}], "item": [{"name": "Concierge Orders", "item": [{"name": "Create Concierge Order", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 201\", function () {", "    pm.response.to.have.status(201);", "});", "", "pm.test(\"Response has concierge_order\", function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('concierge_order');", "    pm.expect(jsonData.concierge_order).to.have.property('id');", "    ", "    // Store the concierge order ID for subsequent requests", "    pm.collectionVariables.set('concierge_order_id', jsonData.concierge_order.id);", "});", "", "pm.test(\"Concierge order has correct properties\", function () {", "    const jsonData = pm.response.json();", "    const order = jsonData.concierge_order;", "    pm.expect(order).to.have.property('order_id');", "    pm.expect(order).to.have.property('status');", "    pm.expect(order).to.have.property('created_at');", "    pm.expect(order).to.have.property('updated_at');", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"order_id\": \"{{order_id}}\",\n  \"assigned_to\": \"user_123\",\n  \"notes\": \"High-value order requiring concierge attention\",\n  \"status\": \"not_started\",\n  \"metadata\": {\n    \"priority\": \"high\",\n    \"source\": \"api_test\"\n  }\n}"}, "url": {"raw": "{{BASE_URL}}/admin/concierge-management/orders", "host": ["{{BASE_URL}}"], "path": ["admin", "concierge-management", "orders"]}}, "response": []}, {"name": "List Concierge Orders", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response has concierge_orders array\", function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('concierge_orders');", "    pm.expect(jsonData.concierge_orders).to.be.an('array');", "});", "", "pm.test(\"Response has pagination info\", function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('count');", "    pm.expect(jsonData).to.have.property('limit');", "    pm.expect(jsonData).to.have.property('offset');", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{BASE_URL}}/admin/concierge-management/orders?limit=20&offset=0", "host": ["{{BASE_URL}}"], "path": ["admin", "concierge-management", "orders"], "query": [{"key": "limit", "value": "20"}, {"key": "offset", "value": "0"}]}}, "response": []}, {"name": "Get Concierge Order by ID", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response has concierge_order\", function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('concierge_order');", "    pm.expect(jsonData.concierge_order).to.have.property('id');", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{BASE_URL}}/admin/concierge-management/orders/{{concierge_order_id}}", "host": ["{{BASE_URL}}"], "path": ["admin", "concierge-management", "orders", "{{concierge_order_id}}"]}}, "response": []}, {"name": "Update Concierge Order", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Order status updated\", function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData.concierge_order.status).to.eql('in_progress');", "});"], "type": "text/javascript"}}], "request": {"method": "PATCH", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"status\": \"in_progress\",\n  \"notes\": \"Started working on this order\",\n  \"assigned_to\": \"user_456\"\n}"}, "url": {"raw": "{{BASE_URL}}/admin/concierge-management/orders/{{concierge_order_id}}", "host": ["{{BASE_URL}}"], "path": ["admin", "concierge-management", "orders", "{{concierge_order_id}}"]}}, "response": []}, {"name": "Delete Concierge Order", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Order deleted successfully\", function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('message');", "    pm.expect(jsonData.message).to.include('deleted successfully');", "});"], "type": "text/javascript"}}], "request": {"method": "DELETE", "header": [], "url": {"raw": "{{BASE_URL}}/admin/concierge-management/orders/{{concierge_order_id}}", "host": ["{{BASE_URL}}"], "path": ["admin", "concierge-management", "orders", "{{concierge_order_id}}"]}}, "response": []}]}, {"name": "Concierge Order Items", "item": [{"name": "Create Concierge Order Item", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 201\", function () {", "    pm.response.to.have.status(201);", "});", "", "pm.test(\"Response has concierge_order_item\", function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('concierge_order_item');", "    pm.expect(jsonData.concierge_order_item).to.have.property('id');", "    ", "    // Store the item ID for subsequent requests", "    pm.collectionVariables.set('concierge_order_item_id', jsonData.concierge_order_item.id);", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"title\": \"Premium Room Upgrade\",\n  \"quantity\": 1,\n  \"unit_price\": 15000,\n  \"variant_id\": \"variant_123\",\n  \"status\": \"under_review\",\n  \"metadata\": {\n    \"upgrade_type\": \"suite\",\n    \"requested_by\": \"guest\"\n  }\n}"}, "url": {"raw": "{{BASE_URL}}/admin/concierge-management/orders/{{concierge_order_id}}/items", "host": ["{{BASE_URL}}"], "path": ["admin", "concierge-management", "orders", "{{concierge_order_id}}", "items"]}}, "response": []}, {"name": "List Concierge Order Items", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response has concierge_order_items array\", function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('concierge_order_items');", "    pm.expect(jsonData.concierge_order_items).to.be.an('array');", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{BASE_URL}}/admin/concierge-management/orders/{{concierge_order_id}}/items", "host": ["{{BASE_URL}}"], "path": ["admin", "concierge-management", "orders", "{{concierge_order_id}}", "items"]}}, "response": []}, {"name": "Get Concierge Order Item by ID", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response has concierge_order_item\", function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('concierge_order_item');", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{BASE_URL}}/admin/concierge-management/orders/{{concierge_order_id}}/items/{{concierge_order_item_id}}", "host": ["{{BASE_URL}}"], "path": ["admin", "concierge-management", "orders", "{{concierge_order_id}}", "items", "{{concierge_order_item_id}}"]}}, "response": []}, {"name": "Update Concierge Order Item", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Item status updated\", function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData.concierge_order_item.status).to.eql('client_confirmed');", "});"], "type": "text/javascript"}}], "request": {"method": "PATCH", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"status\": \"client_confirmed\",\n  \"quantity\": 2,\n  \"unit_price\": 12000\n}"}, "url": {"raw": "{{BASE_URL}}/admin/concierge-management/orders/{{concierge_order_id}}/items/{{concierge_order_item_id}}", "host": ["{{BASE_URL}}"], "path": ["admin", "concierge-management", "orders", "{{concierge_order_id}}", "items", "{{concierge_order_item_id}}"]}}, "response": []}, {"name": "Deactivate Concierge Order Item", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"<PERSON>em deactivated successfully\", function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('message');", "    pm.expect(jsonData.message).to.include('deactivated successfully');", "});"], "type": "text/javascript"}}], "request": {"method": "DELETE", "header": [], "url": {"raw": "{{BASE_URL}}/admin/concierge-management/orders/{{concierge_order_id}}/items/{{concierge_order_item_id}}", "host": ["{{BASE_URL}}"], "path": ["admin", "concierge-management", "orders", "{{concierge_order_id}}", "items", "{{concierge_order_item_id}}"]}}, "response": []}]}]}