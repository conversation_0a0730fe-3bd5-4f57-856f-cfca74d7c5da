require('dotenv').config();
const { Client } = require('pg');

async function addColumns() {
  const client = new Client({
    connectionString: process.env.DATABASE_URL,
  });

  try {
    await client.connect();
    console.log('Connected to database');

    // Check if columns already exist
    const checkResult = await client.query(`
      SELECT column_name 
      FROM information_schema.columns 
      WHERE table_name = 'destination' 
      AND column_name IN ('internal_web_link', 'external_web_link', 'currency_code', 'margin_percentage')
    `);

    console.log('Existing columns:', checkResult.rows.map(row => row.column_name));

    if (checkResult.rows.length === 0) {
      console.log('Adding new columns to destination table...');
      
      await client.query(`
        ALTER TABLE destination 
        ADD COLUMN internal_web_link TEXT,
        ADD COLUMN external_web_link TEXT,
        ADD COLUMN currency_code TEXT,
        ADD COLUMN margin_percentage DECIMAL(5,2)
      `);

      console.log('✅ Columns added successfully!');
    } else {
      console.log('Some columns already exist. Checking which ones need to be added...');
      
      const existingColumns = checkResult.rows.map(row => row.column_name);
      const columnsToAdd = [];
      
      if (!existingColumns.includes('internal_web_link')) {
        columnsToAdd.push('ADD COLUMN internal_web_link TEXT');
      }
      if (!existingColumns.includes('external_web_link')) {
        columnsToAdd.push('ADD COLUMN external_web_link TEXT');
      }
      if (!existingColumns.includes('currency_code')) {
        columnsToAdd.push('ADD COLUMN currency_code TEXT');
      }
      if (!existingColumns.includes('margin_percentage')) {
        columnsToAdd.push('ADD COLUMN margin_percentage DECIMAL(5,2)');
      }
      
      if (columnsToAdd.length > 0) {
        const alterQuery = `ALTER TABLE destination ${columnsToAdd.join(', ')}`;
        console.log('Executing:', alterQuery);
        await client.query(alterQuery);
        console.log('✅ Missing columns added successfully!');
      } else {
        console.log('All columns already exist!');
      }
    }

    // Verify the columns were added
    const verifyResult = await client.query(`
      SELECT column_name, data_type, is_nullable
      FROM information_schema.columns 
      WHERE table_name = 'destination' 
      AND column_name IN ('internal_web_link', 'external_web_link', 'currency_code', 'margin_percentage')
      ORDER BY column_name
    `);

    console.log('\n=== VERIFICATION ===');
    verifyResult.rows.forEach(row => {
      console.log(`${row.column_name}: ${row.data_type} (nullable: ${row.is_nullable})`);
    });

  } catch (error) {
    console.error('Error:', error);
  } finally {
    await client.end();
  }
}

addColumns();
