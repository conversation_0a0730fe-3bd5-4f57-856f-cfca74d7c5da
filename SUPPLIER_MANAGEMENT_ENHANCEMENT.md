# Supplier Management On-Request Screen Enhancement

## Overview

This document describes the comprehensive enhancement made to the supplier management on-request screen to display detailed data from multiple related tables with proper relationships.

## Enhanced Data Structure

The enhanced API endpoint `/admin/supplier-management/on-request` now returns comprehensive data with the following structure:

### Required Data Table Columns

1. **Request ID**: `concierge_order_item.id` (display name: "Request_id")
2. **Category**: Retrieved from product → product_category_product → category relationship
3. **Title**: `concierge_order_item.title`
4. **Quantity**: `concierge_order_item.quantity`
5. **Unit Price**: `concierge_order_item.unit_price`
6. **Total Price**: Calculated field (quantity × unit_price)
7. **Requested Date**: `concierge_order_item.created_at` (display name: "requested date")
8. **Travel Date**: Check-in and check-out dates from the related `concierge_order` table

### Database Relationships Implemented

```
concierge_order_item → order_line_item (via line_item_id)
order_line_item → order_item (via id to item_id)
order_line_item → product (via product_id)
product → product_category_product → category (many-to-many relationship)
concierge_order_item → concierge_order (via concierge_order_id)
```

## Enhanced Service Method

### `listConciergeOrderItemsWithRelationships`

**Location**: `src/modules/concierge-management/service.ts`

**Purpose**: Fetches concierge order items with comprehensive relationship data for supplier management.

**Key Features**:
- Fetches all related data in a single service call
- Uses Medusa's query system for efficient data retrieval
- Includes product and category information
- Extracts travel dates from concierge order metadata
- Calculates total price (quantity × unit_price)
- Handles missing relationships gracefully

**Enhanced Data Fields**:
```typescript
{
  // Base concierge order item data
  id: string;
  title: string;
  quantity: number;
  unit_price: number;
  status: ConciergeOrderItemStatus;
  
  // Calculated fields
  total_price: number;
  requested_date: Date;
  
  // Travel dates from concierge order metadata
  travel_dates?: {
    check_in_date: string | null;
    check_out_date: string | null;
  };
  
  // Related entity data
  concierge_order?: {
    id: string;
    order_id: string;
    assigned_to?: string;
    notes?: string;
    status: ConciergeOrderStatus;
    metadata?: Record<string, any>;
  };
  
  order?: {
    id: string;
    display_id: number;
    email: string;
    currency_code: string;
    total: number;
  };
  
  order_line_item?: {
    id: string;
    unit_price: number;
    quantity: number;
    title: string;
    variant_id?: string;
    product_id?: string;
  };
  
  product?: {
    id: string;
    title: string;
    description?: string;
  };
  
  category?: {
    id: string;
    name: string;
    handle: string;
  };
}
```

## API Endpoint Enhancement

### GET `/admin/supplier-management/on-request`

**Enhanced Features**:
- Comprehensive relationship data included in response
- Supports all existing query parameters
- Maintains backward compatibility
- Improved error handling and logging

**Query Parameters**:
- `limit`: Number of items per page (default: 20)
- `offset`: Pagination offset (default: 0)
- `status`: Filter by concierge order item status
- `exclude_completed`: Exclude completed items (default: true)
- `order`: Sort field (default: "created_at")
- `sort_order`: Sort direction ("asc" | "desc", default: "desc")
- Additional filters: `is_active`, `line_item_id`, `variant_id`, etc.

**Response Structure**:
```json
{
  "concierge_order_items": [
    {
      "id": "citem_...",
      "title": "Hotel Room Booking",
      "quantity": 2,
      "unit_price": 15000,
      "total_price": 30000,
      "requested_date": "2024-01-15T10:30:00Z",
      "travel_dates": {
        "check_in_date": "2024-02-01",
        "check_out_date": "2024-02-03"
      },
      "category": {
        "id": "pcat_...",
        "name": "Accommodation",
        "handle": "accommodation"
      },
      "product": {
        "id": "prod_...",
        "title": "Deluxe Room",
        "description": "Spacious room with mountain view"
      },
      "concierge_order": {
        "id": "corder_...",
        "order_id": "order_...",
        "status": "in_progress"
      }
    }
  ],
  "count": 25,
  "limit": 20,
  "offset": 0
}
```

## Multi-Select Actions Support

The enhanced data structure supports the following multi-select actions:

1. **Create Order** - Generate new orders from selected items
2. **Append to Existing** - Add selected items to an existing order
3. **Export to Excel** - Export selected data to Excel format

## TypeScript Types

**Location**: `src/modules/concierge-management/types.ts`

New types added:
- `TravelDates`
- `CategoryInfo`
- `ProductInfo`
- `OrderLineItemInfo`
- `OrderInfo`
- `ConciergeOrderInfo`
- `EnhancedConciergeOrderItemResponse`
- `EnhancedConciergeOrderItemListResponse`

## Technical Implementation Details

### Performance Considerations
- Uses Promise.all for parallel data fetching
- Implements graceful error handling for missing relationships
- Maintains pagination for large datasets
- Efficient query patterns using Medusa's query system

### Error Handling
- Graceful degradation when relationships are missing
- Comprehensive logging for debugging
- Fallback to basic data structure if enhancement fails

### Backward Compatibility
- Maintains existing API contract
- Adds new fields without breaking existing consumers
- Preserves all original functionality

## Testing

A test script `test-enhanced-api.js` has been created to validate:
- Basic API functionality
- Data structure completeness
- Filtering and pagination
- Enhanced field presence

## Next Steps

1. **Frontend Integration**: Update the supplier management UI to display the new data columns
2. **Multi-Select Actions**: Implement the bulk action functionality
3. **Export Feature**: Add Excel export capability
4. **Performance Optimization**: Monitor and optimize query performance
5. **Type Safety**: Address TypeScript compatibility issues with the service class

## URL Pattern

The enhanced API supports the following URL pattern:
```
http://localhost:9000/app/supplier-management/requests?status=under_review&page=1
```

This enhancement provides a comprehensive foundation for the supplier management on-request screen with all required data relationships and multi-select functionality support.
