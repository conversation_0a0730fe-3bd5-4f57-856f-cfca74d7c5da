# Booking Add-On Data Linking Fix

## Problem Statement

The refactored booking add-on creation process had a data linking issue where the `concierge_order_item` table was only being linked to the `order_line_item` table but not to the `order_item` table. This created incomplete foreign key relationships and potential data integrity issues.

### Specific Issue:
- The `concierge_order_item` table has an `item_id` field that should reference `order_item.id`
- Previously, only `line_item_id` (referencing `order_line_item.id`) was being populated
- This left the `item_id` field null, breaking the complete data relationship chain

## Solution Implemented

### 1. **Model and Schema Updates**

#### A. Updated Concierge Order Item Model
**File:** `src/modules/concierge-management/models/concierge-order-item.ts`

Added the `item_id` field:
```typescript
// Optional reference to existing order item
item_id: model.text().nullable(),
```

Added corresponding index:
```typescript
{
  name: "IDX_concierge_order_item_item_id",
  on: ["item_id"],
  unique: false,
  where: "deleted_at IS NULL",
},
```

#### B. Database Migration
**File:** `src/migrations/1753140000000-add-item-id-to-concierge-order-item.ts`

```sql
ALTER TABLE "concierge_order_item"
ADD COLUMN IF NOT EXISTS "item_id" text NULL;

CREATE INDEX IF NOT EXISTS "IDX_concierge_order_item_item_id"
ON "concierge_order_item" ("item_id")
WHERE "deleted_at" IS NULL;
```

### 2. **Type System Updates**

#### A. Updated Workflow Input Type
**File:** `src/workflows/concierge-management/create-concierge-order-item.ts`

```typescript
type CreateConciergeOrderItemWorkflowInput = {
  concierge_order_id: string;
  line_item_id?: string;
  item_id?: string;  // Added this field
  variant_id?: string;
  // ... other fields
};
```

#### B. Updated Service Types
**File:** `src/modules/concierge-management/types.ts`

```typescript
export interface ConciergeOrderItemInput {
  concierge_order_id: string;
  line_item_id?: string;
  item_id?: string;  // Added this field
  variant_id?: string;
  // ... other fields
}
```

### 3. **Data Retrieval Logic**

#### A. Order Item ID Retrieval
**File:** `src/api/admin/booking-addons/route.ts`

After the `AddOrderItemsWorkflow` execution, we now query for the corresponding `order_item.id`:

```typescript
// Query the order with its items to find the corresponding order_item
const { data: orders } = await query.graph({
  entity: "order",
  filters: { id: actualOrderId },
  fields: [
    "id",
    "items.id",
    "items.variant_id", 
    "items.quantity",
    "items.unit_price",
    "items.created_at"
  ],
});

// Find the most recently created item with matching variant_id
const matchingItems = orders[0].items
  .filter((item: any) => item.variant_id === data.add_on_variant_id)
  .sort((a: any, b: any) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime());

if (matchingItems.length > 0) {
  orderItemId = matchingItems[0].id;
}
```

#### B. Enhanced Workflow Call
The `CreateConciergeOrderItemWorkflow` now receives both IDs:

```typescript
const { result: conciergeResult } = await CreateConciergeOrderItemWorkflow(req.scope).run({
  input: {
    concierge_order_id: conciergeOrderId,
    line_item_id: createdLineItem.id,     // Links to order_line_item
    item_id: orderItemId,                 // Links to order_item
    // ... other fields
  },
});
```

## Data Relationships After Fix

### Complete Foreign Key Chain:

```
order
  ├── order_line_item (created by AddOrderItemsWorkflow)
  │   └── id → concierge_order_item.line_item_id
  └── order_item (managed by AddOrderItemsWorkflow)
      └── id → concierge_order_item.item_id

concierge_order
  └── concierge_order_item (created by CreateConciergeOrderItemWorkflow)
      ├── line_item_id → order_line_item.id
      ├── item_id → order_item.id
      └── metadata contains cross-references for traceability
```

### Database Schema:

```sql
-- concierge_order_item table now has both foreign keys
CREATE TABLE concierge_order_item (
  id text PRIMARY KEY,
  concierge_order_id text NOT NULL,
  line_item_id text NULL,     -- References order_line_item.id
  item_id text NULL,          -- References order_item.id (NEW)
  variant_id text NULL,
  -- ... other fields
);
```

## Benefits Achieved

### 1. **Complete Data Integrity**
- Both `order_line_item` and `order_item` are now properly linked to `concierge_order_item`
- Full traceability between order management and concierge management systems
- Proper foreign key relationships maintained

### 2. **Enhanced Query Capabilities**
- Can now query concierge items by either `line_item_id` or `item_id`
- Better support for complex reporting and analytics
- Improved data consistency checks

### 3. **Future-Proofing**
- Supports both Medusa's `order_line_item` and `order_item` patterns
- Accommodates different order management workflows
- Provides flexibility for future enhancements

### 4. **Backward Compatibility**
- Existing records with only `line_item_id` continue to work
- New records have both IDs populated
- Graceful handling when `order_item.id` cannot be found

## Error Handling

### Graceful Degradation:
```typescript
try {
  // Attempt to find order_item.id
  orderItemId = matchingItems[0].id;
  console.log("✅ Found corresponding order_item.id:", orderItemId);
} catch (error) {
  console.warn("⚠️ Error querying order items:", error.message, "- will proceed with line_item_id only");
  // Continue with line_item_id only - system remains functional
}
```

### Logging and Monitoring:
```typescript
console.log("🔗 Data linking summary:");
console.log(`   - order_line_item.id: ${createdLineItem.id}`);
console.log(`   - order_item.id: ${orderItemId || 'not found'}`);
console.log(`   - concierge_order_item.line_item_id: ${createdLineItem.id}`);
console.log(`   - concierge_order_item.item_id: ${orderItemId || 'null'}`);
```

## Response Enhancement

### Enhanced API Response:
```typescript
{
  success: true,
  message: "Booking add-on created successfully with proper data linking",
  concierge_order_item: { /* concierge item data */ },
  order_line_item: { /* line item data */ },
  order_item_id: "oi_123",
  data_linking: {
    order_line_item_id: "oli_456",
    order_item_id: "oi_123",
    concierge_order_item_id: "citem_789",
    properly_linked: true
  }
}
```

## Testing Recommendations

### 1. **Integration Tests**
- Verify both `line_item_id` and `item_id` are populated in new records
- Test graceful handling when `order_item.id` cannot be found
- Validate foreign key relationships

### 2. **Data Consistency Tests**
- Ensure existing records continue to work
- Verify query performance with new indexes
- Test complex queries involving both ID types

### 3. **Migration Tests**
- Test migration on existing data
- Verify index creation
- Validate backward compatibility

## Conclusion

This fix ensures complete data integrity between the order management and concierge management systems by properly linking both `order_line_item` and `order_item` records to `concierge_order_item` records. The implementation includes proper error handling, maintains backward compatibility, and provides enhanced traceability for better system reliability.
