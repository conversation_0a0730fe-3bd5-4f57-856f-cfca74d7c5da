/**
 * Example Frontend Component for Enhanced Supplier Management On-Request Screen
 * 
 * This component demonstrates how to use the enhanced API data structure
 * with comprehensive relationship data and multi-select functionality.
 */

import React, { useState, useEffect } from 'react';
import { DataTable } from '@medusajs/ui';

// Types for the enhanced data structure
interface TravelDates {
  check_in_date: string | null;
  check_out_date: string | null;
}

interface CategoryInfo {
  id: string;
  name: string;
  handle: string;
}

interface ProductInfo {
  id: string;
  title: string;
  description?: string;
}

interface ConciergeOrderInfo {
  id: string;
  order_id: string;
  status: string;
}

interface EnhancedConciergeOrderItem {
  id: string;
  title: string;
  quantity: number;
  unit_price: number;
  total_price: number;
  requested_date: string;
  status: string;
  travel_dates?: TravelDates;
  category?: CategoryInfo;
  product?: ProductInfo;
  concierge_order?: ConciergeOrderInfo;
}

interface ApiResponse {
  concierge_order_items: EnhancedConciergeOrderItem[];
  count: number;
  limit: number;
  offset: number;
}

const SupplierManagementOnRequestScreen: React.FC = () => {
  const [data, setData] = useState<ApiResponse | null>(null);
  const [loading, setLoading] = useState(true);
  const [selectedItems, setSelectedItems] = useState<string[]>([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [statusFilter, setStatusFilter] = useState<string>('under_review');

  // Fetch data from enhanced API
  const fetchData = async (page: number = 1, status: string = 'under_review') => {
    setLoading(true);
    try {
      const offset = (page - 1) * 20;
      const response = await fetch(
        `/admin/supplier-management/on-request?status=${status}&limit=20&offset=${offset}`,
        {
          headers: {
            'Authorization': 'Bearer YOUR_TOKEN', // Add proper auth
            'Content-Type': 'application/json',
          },
        }
      );
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
      
      const result: ApiResponse = await response.json();
      setData(result);
    } catch (error) {
      console.error('Failed to fetch data:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchData(currentPage, statusFilter);
  }, [currentPage, statusFilter]);

  // Handle multi-select
  const handleSelectItem = (itemId: string) => {
    setSelectedItems(prev => 
      prev.includes(itemId) 
        ? prev.filter(id => id !== itemId)
        : [...prev, itemId]
    );
  };

  const handleSelectAll = () => {
    if (selectedItems.length === data?.concierge_order_items.length) {
      setSelectedItems([]);
    } else {
      setSelectedItems(data?.concierge_order_items.map(item => item.id) || []);
    }
  };

  // Multi-select actions
  const handleCreateOrder = () => {
    console.log('Creating order for items:', selectedItems);
    // Implement create order logic
  };

  const handleAppendToExisting = () => {
    console.log('Appending items to existing order:', selectedItems);
    // Implement append to existing order logic
  };

  const handleExportToExcel = () => {
    console.log('Exporting items to Excel:', selectedItems);
    // Implement Excel export logic
  };

  // Format currency
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'CHF',
      minimumFractionDigits: 2,
    }).format(amount / 100); // Assuming amount is in cents
  };

  // Format date
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  // Table columns configuration
  const columns = [
    {
      header: (
        <input
          type="checkbox"
          checked={selectedItems.length === data?.concierge_order_items.length && selectedItems.length > 0}
          onChange={handleSelectAll}
        />
      ),
      accessorKey: 'select',
      cell: ({ row }: any) => (
        <input
          type="checkbox"
          checked={selectedItems.includes(row.original.id)}
          onChange={() => handleSelectItem(row.original.id)}
        />
      ),
    },
    {
      header: 'Request ID',
      accessorKey: 'id',
      cell: ({ row }: any) => (
        <span className="font-mono text-sm">{row.original.id}</span>
      ),
    },
    {
      header: 'Category',
      accessorKey: 'category',
      cell: ({ row }: any) => (
        <span>{row.original.category?.name || 'N/A'}</span>
      ),
    },
    {
      header: 'Title',
      accessorKey: 'title',
      cell: ({ row }: any) => (
        <div>
          <div className="font-medium">{row.original.title}</div>
          {row.original.product?.description && (
            <div className="text-sm text-gray-500">{row.original.product.description}</div>
          )}
        </div>
      ),
    },
    {
      header: 'Quantity',
      accessorKey: 'quantity',
      cell: ({ row }: any) => (
        <span>{row.original.quantity}</span>
      ),
    },
    {
      header: 'Unit Price',
      accessorKey: 'unit_price',
      cell: ({ row }: any) => (
        <span>{formatCurrency(row.original.unit_price)}</span>
      ),
    },
    {
      header: 'Total Price',
      accessorKey: 'total_price',
      cell: ({ row }: any) => (
        <span className="font-medium">{formatCurrency(row.original.total_price)}</span>
      ),
    },
    {
      header: 'Requested Date',
      accessorKey: 'requested_date',
      cell: ({ row }: any) => (
        <span>{formatDate(row.original.requested_date)}</span>
      ),
    },
    {
      header: 'Travel Dates',
      accessorKey: 'travel_dates',
      cell: ({ row }: any) => {
        const travelDates = row.original.travel_dates;
        if (!travelDates?.check_in_date) return <span>N/A</span>;
        
        return (
          <div className="text-sm">
            <div>Check-in: {formatDate(travelDates.check_in_date)}</div>
            {travelDates.check_out_date && (
              <div>Check-out: {formatDate(travelDates.check_out_date)}</div>
            )}
          </div>
        );
      },
    },
    {
      header: 'Status',
      accessorKey: 'status',
      cell: ({ row }: any) => (
        <span className={`px-2 py-1 rounded text-xs ${
          row.original.status === 'under_review' ? 'bg-yellow-100 text-yellow-800' :
          row.original.status === 'client_confirmed' ? 'bg-green-100 text-green-800' :
          row.original.status === 'order_placed' ? 'bg-blue-100 text-blue-800' :
          'bg-gray-100 text-gray-800'
        }`}>
          {row.original.status.replace('_', ' ').toUpperCase()}
        </span>
      ),
    },
  ];

  if (loading) {
    return <div className="p-4">Loading...</div>;
  }

  return (
    <div className="p-6">
      <div className="mb-6">
        <h1 className="text-2xl font-bold mb-4">Supplier Management - On Request</h1>
        
        {/* Filters */}
        <div className="flex gap-4 mb-4">
          <select
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value)}
            className="border rounded px-3 py-2"
          >
            <option value="">All Statuses</option>
            <option value="under_review">Under Review</option>
            <option value="client_confirmed">Client Confirmed</option>
            <option value="order_placed">Order Placed</option>
          </select>
        </div>

        {/* Multi-select Actions */}
        {selectedItems.length > 0 && (
          <div className="bg-blue-50 border border-blue-200 rounded p-4 mb-4">
            <div className="flex items-center justify-between">
              <span className="text-blue-800">
                {selectedItems.length} item(s) selected
              </span>
              <div className="flex gap-2">
                <button
                  onClick={handleCreateOrder}
                  className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700"
                >
                  Create Order
                </button>
                <button
                  onClick={handleAppendToExisting}
                  className="bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700"
                >
                  Append to Existing
                </button>
                <button
                  onClick={handleExportToExcel}
                  className="bg-gray-600 text-white px-4 py-2 rounded hover:bg-gray-700"
                >
                  Export to Excel
                </button>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Data Table */}
      <DataTable
        columns={columns}
        data={data?.concierge_order_items || []}
        pageSize={20}
        count={data?.count || 0}
        pageIndex={currentPage - 1}
        pageCount={Math.ceil((data?.count || 0) / 20)}
        canPreviousPage={currentPage > 1}
        canNextPage={currentPage < Math.ceil((data?.count || 0) / 20)}
        previousPage={() => setCurrentPage(prev => Math.max(1, prev - 1))}
        nextPage={() => setCurrentPage(prev => prev + 1)}
      />
    </div>
  );
};

export default SupplierManagementOnRequestScreen;
