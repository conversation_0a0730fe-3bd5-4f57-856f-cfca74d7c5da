import {
  StepResponse,
  WorkflowResponse,
  createStep,
  createWorkflow,
} from "@camped-ai/framework/workflows-sdk";
import {
  emitEventStep,
} from "@camped-ai/medusa/core-flows";
import { Modules } from "@camped-ai/framework/utils";

export type OrderItemInput = {
  variant_id: string;
  quantity: number;
  title: string;
  unit_price: number;
  product_id?: string; // Added to support order_line_item.product_id population
  metadata?: Record<string, any>;
};

export type AddOrderItemsStepInput = {
  order_id: string;
  items: OrderItemInput[];
  mode: "draft" | "confirmed";
};

type AddOrderItemsWorkflowInput = AddOrderItemsStepInput;

const validateOrderAndItemsStep = createStep(
  "validate-order-and-items",
  async (input: AddOrderItemsStepInput, { container }) => {
    // Validate input
    if (!input.order_id) {
      throw new Error("Order ID is required");
    }

    if (!input.items || input.items.length === 0) {
      throw new Error("At least one item is required");
    }

    // Validate each item
    for (const item of input.items) {
      if (!item.variant_id) {
        throw new Error("All items must have a variant_id");
      }
      if (item.quantity <= 0) {
        throw new Error("All items must have a positive quantity");
      }
      if (item.unit_price < 0) {
        throw new Error("All items must have a non-negative unit_price");
      }
      if (!item.title) {
        throw new Error("All items must have a title");
      }
    }

    // Get the order to validate it exists and check its status
    const orderModuleService = container.resolve(Modules.ORDER);
    const order = await orderModuleService.retrieveOrder(input.order_id);

    if (!order) {
      throw new Error(`Order with ID ${input.order_id} not found`);
    }

    // Check if order status is compatible with the mode
    const isDraftOrder = order.status === "pending" || order.is_draft_order;

    if (input.mode === "draft" && !isDraftOrder) {
      throw new Error("Cannot add items in draft mode to a confirmed order");
    }

    return new StepResponse({
      ...input,
      order,
      isDraftOrder,
    });
  }
);

const addItemsToOrderStep = createStep(
  "add-items-to-order",
  async (
    input: AddOrderItemsStepInput & { order: any; isDraftOrder: boolean },
    { container }
  ) => {
    const orderModuleService = container.resolve(Modules.ORDER);
    try {
      const orderLineItem = await orderModuleService.createOrderLineItems(input.order_id, input.items)

      return new StepResponse({
        order: input.order,
        addedItems: orderLineItem,
        mode: input.mode,
      });
    } catch (error) {
      console.error("Error adding items to order:", error);
      throw new Error(`Failed to add items to order: ${error.message}`);
    }
  },
  async (
    data: { order: any; addedItems: any[]; mode: string },
    { container }
  ) => {
    // Compensation: Remove the added items if something goes wrong
    try {
      const orderModuleService = container.resolve(Modules.ORDER);

      if (data.addedItems && data.addedItems.length > 0) {
        const itemIds = data.addedItems.map(item => item.id);
        await orderModuleService.deleteOrderLineItems(itemIds);
      }
    } catch (error) {
      console.error("Failed to compensate order items addition:", error);
    }
  }
);

export const AddOrderItemsWorkflow = createWorkflow(
  "add-order-items",
  (input: AddOrderItemsWorkflowInput) => {
    // Step 1: Validate order and items
    const validatedData = validateOrderAndItemsStep(input);

    // Step 2: Add items to the order
    const result = addItemsToOrderStep(validatedData);

    // Step 3: Emit event for order items addition
    emitEventStep({
      eventName: "order.items_added",
      data: {
        order_id: input.order_id,
        items_count: input.items.length,
        mode: input.mode,
        total_items_added: result.addedItems.length,
      },
    });

    return new WorkflowResponse(result);
  }
);
