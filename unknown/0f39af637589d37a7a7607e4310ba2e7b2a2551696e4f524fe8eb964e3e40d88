import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";
import { SUPPLIER_PRODUCTS_SERVICES_MODULE } from "src/modules/supplier-products-services";
import SupplierProductsServicesModuleService from "src/modules/supplier-products-services/service";
import {
  GetStoreProductServicesQuery,
  GetStoreProductServicesQueryType,
} from "./validators";

/**
 * GET /store/products-services
 * 
 * Store API endpoint for retrieving products and services
 * This endpoint provides customer-facing access to active products/services
 * with appropriate data filtering for store/customer consumption
 */
export const GET = async (
  req: MedusaRequest<{}, GetStoreProductServicesQueryType>,
  res: MedusaResponse
) => {
  try {
    // Validate query parameters
    const validatedQuery = GetStoreProductServicesQuery.parse(req.query);
    
    const supplierProductsServicesService: SupplierProductsServicesModuleService = 
      req.scope.resolve(SUPPLIER_PRODUCTS_SERVICES_MODULE);

    // Parse query parameters
    const limit = parseInt(validatedQuery.limit as string) || 100;
    const offset = parseInt(validatedQuery.offset as string) || 0;

    // Build filters - enforce store-specific constraints
    const filters: any = {
      status: "active", // Force active status for store API
    };

    // Add optional filters
    if (validatedQuery.name) {
      filters.name = validatedQuery.name;
    }

    if (validatedQuery.category_id) {
      filters.category_id = validatedQuery.category_id;
    }

    if (validatedQuery.unit_type_id) {
      filters.unit_type_id = validatedQuery.unit_type_id;
    }

    if (validatedQuery.tag_ids) {
      filters.tag_ids = validatedQuery.tag_ids;
    }

    if (validatedQuery.service_level) {
      filters.service_level = validatedQuery.service_level;
    }

    if (validatedQuery.hotel_id) {
      filters.hotel_id = validatedQuery.hotel_id;
    }

    if (validatedQuery.destination_id) {
      filters.destination_id = validatedQuery.destination_id;
    }

    if (validatedQuery.search) {
      filters.search = validatedQuery.search;
    }

    // Add sorting parameters
    const sort_by = validatedQuery.sort_by || "name";
    const sort_order = validatedQuery.sort_order || "asc";

    console.log("Store Products/Services API - Filters:", filters, "Options:", { limit, offset, sort_by, sort_order });

    // Fetch data using the same service as admin API
    const result = await supplierProductsServicesService.listProductServicesWithFilters(
      filters,
      { limit, offset, sort_by, sort_order }
    );

    console.log("result_LOG",{result})

    // Filter sensitive data for store API
    const filteredData = result.map(item => ({
      id: item.id,
      name: item.name,
      description: item.description,
      productId : item.product_id,
      productVarient:item.product_variant_id
    }));

    res.status(200).json({
      products_services: filteredData,
    });
  } catch (error) {
    console.error("❌ Error in store products-services API:", error);
    
    if (error.name === "ZodError") {
      return res.status(400).json({
        type: "invalid_data",
        message: "Invalid query parameters",
        details: error.errors,
      });
    }

    res.status(500).json({
      type: "server_error",
      message: "Failed to retrieve products/services",
      error: error instanceof Error ? error.message : "Unknown error",
    });
  }
};
