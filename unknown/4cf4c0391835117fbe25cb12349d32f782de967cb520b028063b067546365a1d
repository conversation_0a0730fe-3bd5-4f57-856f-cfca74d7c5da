import {
  <PERSON>,
  Eye,
  More<PERSON><PERSON><PERSON><PERSON>,
  Trash,
  UserX,
  Download,
  Upload,
} from "lucide-react";
import { Buildings } from "@camped-ai/icons";
import {
  Container,
  Heading,
  Text,
  Button,
  Badge,
  Toaster,
  toast,
  DropdownMenu,
  Prompt,
} from "@camped-ai/ui";
import { useTranslation } from "react-i18next";
import { useMemo, useState, useEffect } from "react";
import { Link, useNavigate, useLocation } from "react-router-dom";
import { useReactTable, getCoreRowModel, createColumnHelper, ColumnDef } from "@tanstack/react-table";
import { useRbac } from "../../../hooks/use-rbac";
import { DataTable } from "../../../../components/table/data-table";
import type { Filter } from "../../../../components/table/data-table";
import type { Supplier } from "../../../hooks/supplier-management/use-suppliers-list";
import {
  useSuppliersList,
} from "../../../hooks/supplier-management/use-suppliers-list";
import { useDeleteSupplier, useUpdateSupplier } from "../../../hooks/vendor-management/use-suppliers";
import { getCurrencyDisplayName } from "../../../constants/supplier-form-options";
import { useCategories } from "../../../hooks/supplier-products-services/use-categories";

// Status badge colors
const statusColors = {
  active: "green",
  inactive: "red", 
  pending: "orange",
  suspended: "grey",
  terminated: "grey",
} as const;

interface SuppliersPageClientProps {
  // We'll fetch data inside the component using hooks
}

const SuppliersPageClient: React.FC<SuppliersPageClientProps> = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { t } = useTranslation();
  const { hasPermission } = useRbac();
  const { data: categoriesData } = useCategories({ is_active: true, limit: 100 });
  const deleteSupplier = useDeleteSupplier();
  const updateSupplier = useUpdateSupplier();

  // State for delete confirmation
  const [deleteConfirmOpen, setDeleteConfirmOpen] = useState(false);
  const [supplierToDelete, setSupplierToDelete] = useState<Supplier | null>(null);

  // State for activate/inactivate confirmation
  const [activateConfirmOpen, setActivateConfirmOpen] = useState(false);
  const [supplierToActivate, setSupplierToActivate] = useState<Supplier | null>(null);

  // Get URL search params
  const searchParams = new URLSearchParams(location.search);
  const currentPage = parseInt(searchParams.get("page") || "1");
  const pageSize = parseInt(searchParams.get("limit") || "25");

  // Add after extracting searchParams and currentPage
  const handlePageChange = (pageIndex: number) => {
    const newParams = new URLSearchParams(location.search);
    newParams.set("page", (pageIndex + 1).toString());
    navigate({ search: newParams.toString() });
  };

  // Extract filters from URL
  const search = searchParams.get("q") || searchParams.get("search") || undefined;
  const status = searchParams.get("status") || undefined;
  const preference = searchParams.get("preference") || undefined;
  const sortBy = searchParams.get("order")?.replace("-", "") || "created_at";
  const sortOrder = searchParams.get("order")?.startsWith("-") ? "desc" : "asc";

  // Fetch suppliers data
  const {
    data: suppliersData,
    isLoading,
    error,
  } = useSuppliersList({
    search,
    status,
    preference,
    sort_by: sortBy as "name" | "status" | "created_at" | "updated_at",
    sort_order: sortOrder as "asc" | "desc",
    limit: pageSize,
    offset: (currentPage - 1) * pageSize,
  });

  const suppliers = suppliersData?.suppliers || [];
  const totalCount = suppliersData?.count || 0;

  // Column helper for type safety
  const columnHelper = createColumnHelper<Supplier>();

  // Helper functions
  const getStatusBadgeVariant = (status: string) => {
    return statusColors[status?.toLowerCase() as keyof typeof statusColors] || "grey";
  };

  // Delete handlers
  const handleDeleteClick = (supplier: Supplier) => {
    setSupplierToDelete(supplier);
    setDeleteConfirmOpen(true);
  };

  const handleConfirmDelete = async () => {
    if (!supplierToDelete) return;

    try {
      await deleteSupplier.mutateAsync(supplierToDelete.id);
    } catch (error) {
      console.error("Error deleting supplier:", error);
    } finally {
      setDeleteConfirmOpen(false);
      setSupplierToDelete(null);
    }
  };

  const handleCancelDelete = () => {
    setDeleteConfirmOpen(false);
    setSupplierToDelete(null);
  };

  // Activate/Inactivate handlers
  const handleActivateClick = (supplier: Supplier) => {
    setSupplierToActivate(supplier);
    setActivateConfirmOpen(true);
  };

  const handleConfirmActivate = async () => {
    if (!supplierToActivate) return;

    try {
      const isCurrentlyActive = supplierToActivate.status?.toLowerCase() === 'active';
      const newStatus = isCurrentlyActive ? 'Inactive' : 'Active';

      await updateSupplier.mutateAsync({
        id: supplierToActivate.id,
        status: newStatus,
      });
    } catch (error) {
      console.error("Error updating supplier status:", error);
    } finally {
      setActivateConfirmOpen(false);
      setSupplierToActivate(null);
    }
  };

  const handleCancelActivate = () => {
    setActivateConfirmOpen(false);
    setSupplierToActivate(null);
  };

  // Helper function to get category names from IDs
  const getCategoryNames = (categoryIds: string[]): string[] => {
    if (!categoriesData?.categories || !Array.isArray(categoryIds)) return [];
    return categoryIds
      .map((id) => {
        const category = categoriesData.categories.find((cat) => cat.id === id);
        return category ? category.name : null;
      })
      .filter((name): name is string => name !== null);
  };

  // Helper function to render categories as business type
  const renderBusinessType = (categories: string[]) => {
    if (!categories || categories.length === 0) {
      return <Text className="txt-compact-medium">—</Text>;
    }

    const categoryNames = getCategoryNames(categories);
    if (categoryNames.length === 0) {
      return <Text className="txt-compact-medium">—</Text>;
    }

    // If only one category, show as text
    if (categoryNames.length === 1) {
      return <Text className="txt-compact-medium">{categoryNames[0]}</Text>;
    }

    // If multiple categories, show first one with count
    return (
      <div className="flex items-center gap-1">
        <Text className="txt-compact-medium">{categoryNames[0]}</Text>
        <Badge color="grey" size="xsmall">
          +{categoryNames.length - 1}
        </Badge>
      </div>
    );
  };

  // Helper function to get primary contact
  const getPrimaryContact = (contacts: any[]) => {
    if (!contacts || contacts.length === 0) {
      return { name: "—", email: "—" };
    }

    // Find primary contact or use first contact
    const primaryContact = contacts.find(contact => contact.is_primary) || contacts[0];
    return {
      name: primaryContact.name || "—",
      email: primaryContact.email || "—"
    };
  };

  // Define columns
  const columns = useMemo<ColumnDef<Supplier, any>[]>(() => [
    columnHelper.display({
      id: "supplier_name",
      header: "Supplier Name",
      cell: ({ row }) => {
        const supplier = row.original;
        return (
          <div className="flex items-center gap-x-3 w-[200px] truncate">
            <div className="flex  p-2 items-center justify-center rounded bg-ui-bg-subtle">
              <Buildings className="h-4 w-4 text-ui-fg-subtle" />
            </div>
            <div>
              <Text className="txt-compact-medium-plus" weight="plus">
                {supplier.name}
              </Text>
            </div>
          </div>
        );
      },
    }),
    columnHelper.display({
      id: "status",
      header: "Status",
      cell: ({ row }) => {
        const supplier = row.original;
        return (
          <Badge color={getStatusBadgeVariant(supplier.status)} size="xsmall">
            <span className="inter-small-semibold">
              {supplier.status}
            </span>
          </Badge>
        );
      },
    }),
    columnHelper.display({
      id: "primary_contact",
      header: "Primary Contact",
      cell: ({ row }) => {
        const supplier = row.original;
        const contact = getPrimaryContact(supplier.contacts || []);
        return (
          <div className="w-[200px] truncate">
            <Text className="txt-compact-medium-plus" weight="plus">
              {contact.name}
            </Text>
            {contact.email !== "—" && (
              <Text className="txt-compact-small text-ui-fg-subtle">
                {contact.email}
              </Text>
            )}
          </div>
        );
      },
    }),
    columnHelper.display({
      id: "payment_terms",
      header: "Payment Terms",
      cell: ({ row }) => {
        const supplier = row.original;
        return (
          <Text className="txt-compact-medium">
            {supplier.payout_terms || "—"}
          </Text>
        );
      },
    }),
    columnHelper.display({
      id: "default_currency",
      header: "Default Currency",
      cell: ({ row }) => {
        const supplier = row.original;
        return (
          <Text className="txt-compact-medium">
            {supplier.default_currency ? getCurrencyDisplayName(supplier.default_currency) : "—"}
          </Text>
        );
      },
    }),
    columnHelper.display({
      id: "date_added",
      header: "Date Added",
      cell: ({ row }) => {
        const supplier = row.original;
        return (
          <Text className="txt-compact-medium">
            {supplier.created_at ? new Date(supplier.created_at).toLocaleDateString() : "—"}
          </Text>
        );
      },
    }),
    columnHelper.display({
      id: "actions",
      header: "Actions",
      cell: ({ row }) => {
        const supplier = row.original;
        return (
          <DropdownMenu>
            <DropdownMenu.Trigger asChild>
              <Button variant="transparent" size="small">
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenu.Trigger>
            <DropdownMenu.Content>
              <DropdownMenu.Item asChild>
                <Link to={`/supplier-management/suppliers/${supplier.id}`}>
                  <Eye className="h-4 w-4 mr-2" />
                  View
                </Link>
              </DropdownMenu.Item>
              {hasPermission("supplier_management:edit") && (
                <DropdownMenu.Item asChild>
                  <Link to={`/supplier-management/suppliers/${supplier.id}/edit`}>
                    <Edit className="h-4 w-4 mr-2" />
                    Edit
                  </Link>
                </DropdownMenu.Item>
              )}
              <DropdownMenu.Item
                onClick={(e) => {
                  e.stopPropagation();
                  handleActivateClick(supplier);
                }}
                className={
                  supplier.status?.toLowerCase() === 'active'
                    ? "text-red-600 hover:text-red-700 hover:bg-red-50"
                    : "text-green-600 hover:text-green-700 hover:bg-green-50"
                }
              >
                <UserX className="h-4 w-4 mr-2" />
                {supplier.status?.toLowerCase() === 'active' ? 'Deactivate' : 'Activate'}
              </DropdownMenu.Item>
              {hasPermission("supplier_management:delete") && (
                <DropdownMenu.Item
                  onClick={(e) => {
                    e.stopPropagation();
                    handleDeleteClick(supplier);
                  }}
                  className="text-red-600"
                >
                  <Trash className="h-4 w-4 mr-2" />
                  Delete
                </DropdownMenu.Item>
              )}
            </DropdownMenu.Content>
          </DropdownMenu>
        );
      },
    }),
  ], [hasPermission, navigate, categoriesData]);

  // Define filters
  const filters: Filter[] = useMemo(() => [
    {
      key: "status",
      label: "Status",
      type: "select",
      options: [
        { label: "Active", value: "Active" },
        { label: "Inactive", value: "Inactive" },
        { label: "Pending Approval", value: "Pending Approval" },
        { label: "Suspended", value: "Suspended" },
        { label: "Terminated", value: "Terminated" },
      ],
    }
  ], []);

  // Define sortable columns
  const orderBy = useMemo(() => [
    { key: "name" as keyof Supplier, label: "Supplier Name" },
    { key: "status" as keyof Supplier, label: "Status" },
    { key: "region" as keyof Supplier, label: "Region" },
    { key: "created_at" as keyof Supplier, label: "Date Added" },
  ], []);

  // Create table instance
  const table = useReactTable({
    data: suppliers,
    columns,
    getCoreRowModel: getCoreRowModel(),
    manualPagination: true,
    pageCount: Math.ceil(totalCount / pageSize),
    state: {
      pagination: {
        pageIndex: currentPage - 1,
        pageSize,
      },
    },
  });

  // Override table pagination methods to update URL
  // (pattern from requests/page-client.tsx)
  table.nextPage = () => {
    const newPage = currentPage + 1;
    if (newPage <= Math.ceil(totalCount / pageSize)) {
      const newSearchParams = new URLSearchParams(location.search);
      newSearchParams.set('page', newPage.toString());
      navigate(`${location.pathname}?${newSearchParams.toString()}`, { replace: true });
    }
  };
  table.previousPage = () => {
    const newPage = currentPage - 1;
    if (newPage >= 1) {
      const newSearchParams = new URLSearchParams(location.search);
      newSearchParams.set('page', newPage.toString());
      navigate(`${location.pathname}?${newSearchParams.toString()}`, { replace: true });
    }
  };
  table.setPageIndex = (updater) => {
    const pageIndex = typeof updater === 'function' ? updater(currentPage - 1) : updater;
    const newPage = pageIndex + 1;
    const newSearchParams = new URLSearchParams(location.search);
    newSearchParams.set('page', newPage.toString());
    navigate(`${location.pathname}?${newSearchParams.toString()}`, { replace: true });
  };

  // Sync table pagination with URL
  useEffect(() => {
    const tablePage = table.getState().pagination.pageIndex + 1;
    if (tablePage !== currentPage) {
      const newParams = new URLSearchParams(location.search);
      newParams.set("page", tablePage.toString());
      navigate({ search: newParams.toString() });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [table.getState().pagination.pageIndex]);

  return (
    <Container className="divide-y p-0">
      {/* Header */}
      <div className="flex items-center justify-between px-6 py-4">
        <div>
          <Heading level="h2">Suppliers</Heading>
        </div>
        <div className="flex items-center gap-x-2">
          <Button size="small" variant="secondary" asChild>
            <Link to="export">
              Export
            </Link>
          </Button>
          {hasPermission("supplier_management:bulk_operations") && (
            <Button size="small" variant="secondary" asChild>
              <Link to="import">
                Import
              </Link>
            </Button>
          )}
          {hasPermission("supplier_management:create") && (
            <Button size="small" asChild>
              <Link to="create">{t("actions.create")}</Link>
            </Button>
          )}
        </div>
      </div>

      {/* DataTable */}
      <DataTable
        table={table}
        columns={columns}
        pageSize={pageSize}
        count={totalCount}
        isLoading={isLoading}
        filters={filters}
        orderBy={orderBy}
        search="autofocus"
        pagination
        navigateTo={(row) => `/supplier-management/suppliers/${row.original.id}`}
        queryObject={Object.fromEntries(searchParams)}
        noRecords={{
          title: "No suppliers found",
          message: "Get started by creating your first supplier",
        }}
      />
      <Toaster />

      {/* Delete Confirmation Prompt */}
      <Prompt open={deleteConfirmOpen} onOpenChange={setDeleteConfirmOpen}>
        <Prompt.Content>
          <Prompt.Header>
            <Prompt.Title>Delete Supplier</Prompt.Title>
            <Prompt.Description>
              Are you sure you want to delete "{supplierToDelete?.name}"? This action cannot be undone.
            </Prompt.Description>
          </Prompt.Header>
          <Prompt.Footer>
            <Prompt.Cancel onClick={handleCancelDelete}>
              Cancel
            </Prompt.Cancel>
            <Prompt.Action
              onClick={handleConfirmDelete}
              disabled={deleteSupplier.isPending}
            >
              {deleteSupplier.isPending ? "Deleting..." : "Delete"}
            </Prompt.Action>
          </Prompt.Footer>
        </Prompt.Content>
      </Prompt>

      {/* Activate/Inactivate Confirmation Prompt */}
      <Prompt open={activateConfirmOpen} onOpenChange={setActivateConfirmOpen}>
        <Prompt.Content>
          <Prompt.Header>
            <Prompt.Title>
              {supplierToActivate?.status?.toLowerCase() === 'active' ? 'Inactivate' : 'Activate'} Supplier
            </Prompt.Title>
            <Prompt.Description>
              Are you sure you want to {supplierToActivate?.status?.toLowerCase() === 'active' ? 'inactivate' : 'activate'} "{supplierToActivate?.name}"?
            </Prompt.Description>
          </Prompt.Header>
          <Prompt.Footer>
            <Prompt.Cancel onClick={handleCancelActivate}>
              Cancel
            </Prompt.Cancel>
            <Prompt.Action
              onClick={handleConfirmActivate}
              disabled={updateSupplier.isPending}
            >
              {updateSupplier.isPending
                ? (supplierToActivate?.status?.toLowerCase() === 'active' ? "Inactivating..." : "Activating...")
                : (supplierToActivate?.status?.toLowerCase() === 'active' ? 'Inactivate' : 'Activate')
              }
            </Prompt.Action>
          </Prompt.Footer>
        </Prompt.Content>
      </Prompt>
    </Container>
  );
};

export default SuppliersPageClient;
