import React, { useState } from "react";
import { use<PERSON><PERSON>, Controller } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { Save, X, Plus, Trash2, Upload, CheckCircle, AlertCircle, File } from "lucide-react";
import {
  Container,
  Heading,
  Text,
  Button,
  Input,
  Textarea,
  Select,
  Label,
  toast,
} from "@camped-ai/ui";

import { MultiSelect } from "../../../../components/common/MultiSelect";
import {
  TIMEZONES,
  LANGUAGES,
  PAYMENT_METHODS,
  PAYOUT_TERMS,
  CURRENCIES,
  SUPPLIER_STATUSES,
  SUPPLIER_TYPES,
} from "../../../../constants/supplier-form-options";
import { useCategories } from "../../../../hooks/supplier-products-services/use-categories";
import { useProductsServices } from "../../../../hooks/supplier-products-services/use-products-services";
import { useUploadSupplierDocuments } from "../../../../hooks/vendor-management/use-suppliers";

// Contact interface
interface Contact {
  id: string;
  name: string;
  email: string;
  phone_number: string;
  is_primary: boolean;
}

// Form validation schema
const supplierSchema = z.object({
  // Basic Info
  name: z.string().min(1, "Name is required"),
  supplier_type: z.enum(["Company", "Individual"], {
    required_error: "Supplier type is required",
  }),


  status: z
    .enum(["Active", "Inactive"])
    .default("Active"),

  // Business & Region Information
  timezone: z.string().optional(),
  language_preference: z.array(z.string()).optional(),

  // Financial Information
  payment_method: z.string().optional(),
  payout_terms: z.string().optional(),
  tax_id: z
    .string()
    .optional()
    .refine(
      (val) => !val || /^[A-Z0-9\-]{5,20}$/.test(val.replace(/[\s]/g, "")),
      "Please enter a valid tax ID (5-20 alphanumeric characters)"
    ),
  default_currency: z.string().default("CHF"),
  bank_account_details: z.string().optional(),

  // Additional Information
  categories: z.array(z.string()).min(1, "At least one category is required"),
  products_services: z.array(z.string()).optional(),

  // Contacts
  contacts: z
    .array(
      z.object({
        id: z.string(),
        name: z.string().min(1, "Contact name is required"),
        email: z.string().email("Valid email is required"),
        phone_number: z
          .string()
          .min(1, "Phone number is required")
          .refine(
            (val) =>
              /^[\+]?[1-9][\d]{0,15}$/.test(val.replace(/[\s\-\(\)]/g, "")),
            "Please enter a valid phone number"
          ),

        is_primary: z.boolean(),
      })
    )
    .min(1, "At least one contact is required"),
});

type SupplierFormData = z.infer<typeof supplierSchema>;

interface PreUploadFile {
  file: File;
  id: string;
  progress: number;
  status: "pending" | "uploading" | "success" | "error";
  error?: string;
}

interface SupplierCreateFormProps {
  onSubmit: (data: SupplierFormData) => Promise<{ supplier_id: string } | undefined>;
  onCancel: () => void;
  isSubmitting?: boolean;
}

const SupplierCreateForm: React.FC<SupplierCreateFormProps> = ({
  onSubmit,
  onCancel,
  isSubmitting = false,
}) => {
  // State for cascading field management
  const [selectedCategories, setSelectedCategories] = useState<string[]>([]);

  // Form setup
  const form = useForm<SupplierFormData>({
    resolver: zodResolver(supplierSchema),
    defaultValues: {
      name: "",
      supplier_type: "Company",

      status: "Active",
      timezone: "",
      language_preference: [],
      payment_method: "",
      payout_terms: "",
      tax_id: "",
      default_currency: "CHF",
      bank_account_details: "",
      categories: [],
      products_services: [],
      contacts: [
        {
          id: "1",
          name: "",
          email: "",
          phone_number: "",
          is_primary: true,
        },
      ],
    },
  });

  const {
    handleSubmit,
    control,
    watch,
    setValue,
    formState: { errors },
  } = form;

  const watchedContacts = watch("contacts");
  const watchedSupplierType = watch("supplier_type");

  const [preUploadFiles, setPreUploadFiles] = useState<PreUploadFile[]>([]);
  const [uploading] = useState(false);

  const maxFileSize = 10 * 1024 * 1024; // 10MB
  const allowedTypes = [
    "application/pdf",
    "image/jpeg",
    "image/png",
    "application/msword",
    "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
  ];

  const uploadSupplierDocuments = useUploadSupplierDocuments();

  // Fetch categories data at the top level to avoid hook violations
  const { data: categoriesData } = useCategories({
    is_active: true,
    limit: 100
  });
  const categories = categoriesData?.categories || [];

  // Contact management functions
  const addContact = () => {
    const newContact: Contact = {
      id: Date.now().toString(),
      name: "",
      email: "",
      phone_number: "",
      is_primary: false,
    };
    setValue("contacts", [...watchedContacts, newContact]);
  };

  const removeContact = (contactId: string) => {
    if (watchedContacts.length <= 1) {
      toast.error("At least one contact is required");
      return;
    }
    const updatedContacts = watchedContacts.filter((c) => c.id !== contactId);
    setValue("contacts", updatedContacts);
  };

  const setPrimaryContact = (contactId: string) => {
    const updatedContacts = watchedContacts.map((contact) => ({
      ...contact,
      is_primary: contact.id === contactId,
    }));
    setValue("contacts", updatedContacts);
  };

  // File handling functions

  const handleFiles = (fileList: File[]) => {
    const newFiles: PreUploadFile[] = [];
    fileList.forEach((file) => {
      if (!allowedTypes.includes(file.type)) {
        toast.error(`File type not supported: ${file.name}`);
        return;
      }
      if (file.size > maxFileSize) {
        toast.error(`File too large: ${file.name} (max 10MB)`);
        return;
      }
      newFiles.push({
        file,
        id: Math.random().toString(36).substring(2, 11),
        progress: 0,
        status: "pending",
      });
    });
    setPreUploadFiles((prev) => [...prev, ...newFiles]);
  };

  const removeFile = (id: string) => {
    setPreUploadFiles((prev) => prev.filter((f) => f.id !== id));
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return "0 Bytes";
    const k = 1024;
    const sizes = ["Bytes", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "success":
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case "error":
        return <AlertCircle className="h-4 w-4 text-red-500" />;
      case "uploading":
        return (
          <div className="h-4 w-4 border-2 border-blue-500 border-t-transparent rounded-full animate-spin" />
        );
      default:
        return <File className="h-4 w-4 text-gray-500" />;
    }
  };



  // Form submission
  const handleFormSubmit = async (data: SupplierFormData) => {
    try {
      // Ensure only one primary contact
      const primaryContacts = data.contacts.filter((c) => c.is_primary);
      if (primaryContacts.length !== 1) {
        toast.error("Exactly one contact must be marked as primary");
        return;
      }

      let createdSupplierId: string | undefined = undefined;
      try {
        const result = await onSubmit(data);
        // Try to extract supplier_id from result (if onSubmit returns it)
        if (result && typeof result === "object" && result.supplier_id) {
          createdSupplierId = result.supplier_id;
        }
      } catch (err) {
        // onSubmit already shows error toast
        return;
      }
      // If files are selected and we have a supplier_id, upload them
      if (createdSupplierId && preUploadFiles.length > 0) {
        const files = preUploadFiles.map((f) => f.file);
        try {
          await uploadSupplierDocuments.mutateAsync({ supplierId: createdSupplierId, files });
        } catch (err: any) {
          toast.error(err.message || "Failed to upload documents");
          return;
        }
      }
      // Success and navigation are handled in onSubmit
    } catch (error: any) {
      console.error("Error in form submission:", error);
      toast.error(error.message || "Failed to create supplier");
    }
  };

  return (
    <>
      {/* Basic Information */}
      <Container className="divide-y p-0">
        <div className="flex items-center justify-between px-6 py-4">
          <Heading level="h2">Basic Information</Heading>
        </div>
        <div className="px-6 py-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="space-y-2">
              <Label>Supplier Type *</Label>
              <Controller
                name="supplier_type"
                control={control}
                render={({ field }) => (
                  <Select value={field.value} onValueChange={field.onChange}>
                    <Select.Trigger>
                      <Select.Value placeholder="Select supplier type" />
                    </Select.Trigger>
                    <Select.Content>
                      {SUPPLIER_TYPES.map((type) => (
                        <Select.Item key={type.value} value={type.value}>
                          {type.label}
                        </Select.Item>
                      ))}
                    </Select.Content>
                  </Select>
                )}
              />
              {errors.supplier_type && (
                <Text size="small" className="text-red-600">
                  {errors.supplier_type.message}
                </Text>
              )}
            </div>
            <div className="space-y-2">
              <Label htmlFor="name">
                {watchedSupplierType === "Individual" ? "Full Name" : "Company Name"} *
              </Label>
              <Controller
                name="name"
                control={control}
                render={({ field }) => (
                  <Input
                    {...field}
                    id="name"
                    placeholder={
                      watchedSupplierType === "Individual"
                        ? "Enter full name"
                        : "Enter company name"
                    }
                    className={errors.name ? "border-red-500" : ""}
                  />
                )}
              />
              {errors.name && (
                <Text size="small" className="text-red-600">
                  {errors.name.message}
                </Text>
              )}
            </div>



            <div className="space-y-2">
              <Label>Status</Label>
              <Controller
                name="status"
                control={control}
                render={({ field }) => (
                  <Select value={field.value} onValueChange={field.onChange}>
                    <Select.Trigger>
                      <Select.Value />
                    </Select.Trigger>
                    <Select.Content>
                      {SUPPLIER_STATUSES.map((status) => (
                        <Select.Item key={status.value} value={status.value}>
                          {status.label}
                        </Select.Item>
                      ))}
                    </Select.Content>
                  </Select>
                )}
              />
            </div>



          </div>
        </div>
      </Container>



      {/* Business Information */}
      <Container className="divide-y p-0 mt-1">
        <div className="flex items-center justify-between px-6 py-4">
          <Heading level="h2">Business & Region</Heading>
        </div>
        <div className="px-6 py-4">
          <div className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">

              <div className="space-y-2">
                <Label>Timezone</Label>
                <Controller
                  name="timezone"
                  control={control}
                  render={({ field }) => (
                    <Select
                      value={field.value || ""}
                      onValueChange={field.onChange}
                    >
                      <Select.Trigger>
                        <Select.Value placeholder="Select timezone" />
                      </Select.Trigger>
                      <Select.Content>
                        {TIMEZONES.map((timezone) => (
                          <Select.Item
                            key={timezone.value}
                            value={timezone.value}
                          >
                            {timezone.label}
                          </Select.Item>
                        ))}
                      </Select.Content>
                    </Select>
                  )}
                />
              </div>
              <div className="space-y-2">
                <Label>Language Preference</Label>
                <Controller
                  name="language_preference"
                  control={control}
                  render={({ field }) => (
                    <MultiSelect
                      options={LANGUAGES}
                      selectedValues={field.value || []}
                      onChange={field.onChange}
                      placeholder="Select languages"
                    />
                  )}
                />
              </div>
            </div>


          </div>
        </div>
      </Container>

      {/* Additional Information */}
      <Container className="divide-y p-0 mt-1">
        <div className="flex items-center justify-between px-6 py-4">
          <Heading level="h2">Additional Information</Heading>
        </div>
        <div className="px-6 py-4">
          <div className="space-y-4">
            <div className="grid grid-cols-1 gap-4">
              <div className="space-y-2">
                <Label>Categories *</Label>
                <Controller
                  name="categories"
                  control={control}
                  render={({ field, fieldState }) => {
                    const { data: categoriesData, isLoading } = useCategories({
                      is_active: true,
                      limit: 100
                    });

                    const categories = categoriesData?.categories || [];
                    const categoryOptions = categories.map((category) => ({
                      value: category.id,
                      label: (category as any).icon ? `${(category as any).icon} ${category.name}` : category.name,
                    }));

                    return (
                      <div>
                        <MultiSelect
                          options={categoryOptions}
                          selectedValues={field.value || []}
                          onChange={(values) => {
                            field.onChange(values);
                            setSelectedCategories(values);
                            // Clear products/services when categories change
                            setValue("products_services", []);
                          }}
                          placeholder="Select categories"
                          disabled={isLoading}
                          showSelectAll={true}
                          showSelectedTags={true}
                        />
                        {fieldState.error && (
                          <Text size="small" className="text-red-600 mt-1">
                            {fieldState.error.message}
                          </Text>
                        )}
                      </div>
                    );
                  }}
                />
              </div>

              {/* Products & Services Selection - Conditional */}
              <div className="space-y-2">
                <Label>Products & Services (Optional)</Label>
                <Controller
                  name="products_services"
                  control={control}
                  render={({ field }) => {
                    // Only fetch products/services if categories are selected
                    const shouldFetch = selectedCategories.length > 0;
                    const categoryId = selectedCategories.length === 1 ? selectedCategories[0] : undefined;

                    const { data: productsData, isLoading } = useProductsServices({
                      category_id: categoryId,
                      status: "active",
                      limit: 100,
                    });

                    const products = productsData?.products_services || [];
                    const productOptions = products.map((product) => ({
                      value: product.id,
                      label: product.name,
                    }));

                    const isDisabled = !shouldFetch || isLoading;
                    const placeholderText = selectedCategories.length === 0
                      ? "Please select a category first"
                      : selectedCategories.length > 1
                      ? "Multiple categories selected - please select one category to see products/services"
                      : isLoading
                      ? "Loading products/services..."
                      : "Select products/services";

                    return (
                      <MultiSelect
                        options={productOptions}
                        selectedValues={field.value || []}
                        onChange={field.onChange}
                        placeholder={placeholderText}
                        disabled={isDisabled}
                        showSelectAll={true}
                        showSelectedTags={true}
                      />
                    );
                  }}
                />
                <Text size="small" className="text-ui-fg-subtle">
                  Select the products and services this supplier can provide. This helps determine what offerings they can create.
                </Text>
              </div>

              {/* Supplier Capabilities Preview */}
              {selectedCategories.length > 0 && (
                <div className="space-y-2">
                  <Label>Supplier Capabilities Preview</Label>
                  <div className="border border-gray-200 rounded-lg p-4 bg-gray-50">
                    <Text size="small" className="text-ui-fg-subtle mb-3">
                      Based on your selections, this supplier will be able to create offerings for:
                    </Text>

                    {selectedCategories.map((categoryId) => {
                      const category = categories.find(c => c.id === categoryId);

                      if (!category) return null;

                      const dynamicFields = category.dynamic_field_schema || [];
                      const requiredFields = dynamicFields.filter((field: any) =>
                        field.required && field.used_in_supplier_offering
                      );

                      return (
                        <div key={categoryId} className="mb-4 last:mb-0">
                          <div className="flex items-center gap-2 mb-2">
                            {(category as any).icon && (
                              <span className="text-lg">{(category as any).icon}</span>
                            )}
                            <Text weight="plus" size="small">{category.name}</Text>
                          </div>

                          {requiredFields.length > 0 && (
                            <div className="ml-6">
                              <Text size="xsmall" className="text-ui-fg-muted mb-1">
                                Required fields for supplier offerings:
                              </Text>
                              <div className="flex flex-wrap gap-1">
                                {requiredFields.map((field: any, index: number) => (
                                  <span
                                    key={index}
                                    className="inline-block px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded"
                                  >
                                    {field.label}
                                  </span>
                                ))}
                              </div>
                            </div>
                          )}
                        </div>
                      );
                    })}
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </Container>

      {/* Contacts */}
      <Container className="divide-y p-0 mt-1">
        <div className="flex items-center justify-between px-6 py-4">
          <div>
            <Heading level="h2">Contacts</Heading>
          </div>
          <Button
            type="button"
            variant="secondary"
            size="small"
            onClick={addContact}
            className="flex items-center gap-2"
          >
            <Plus className="w-4 h-4" />
            Add Contact
          </Button>
        </div>
        <div className="px-6 py-4">
          <div className="space-y-4">
            {watchedContacts.map((contact, index) => (
              <div key={contact.id} className="border rounded-lg p-4">
                <div className="flex items-center justify-between mb-3">
                  <Text weight="plus">Contact {index + 1}</Text>
                  <div className="flex items-center gap-2">
                    <Controller
                      name={`contacts.${index}.is_primary`}
                      control={control}
                      render={({ field }) => (
                        <label className="flex items-center gap-2 cursor-pointer">
                          <input
                            type="radio"
                            name="primary_contact"
                            checked={field.value}
                            onChange={() => setPrimaryContact(contact.id)}
                            className="text-blue-600"
                          />
                          <Text size="small">Primary</Text>
                        </label>
                      )}
                    />
                    {watchedContacts.length > 1 && (
                      <Button
                        type="button"
                        variant="danger"
                        size="small"
                        onClick={() => removeContact(contact.id)}
                      >
                        <Trash2 className="w-4 h-4" />
                      </Button>
                    )}
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor={`contact_name_${contact.id}`}>Name *</Label>
                    <Controller
                      name={`contacts.${index}.name`}
                      control={control}
                      render={({ field }) => (
                        <Input
                          {...field}
                          id={`contact_name_${contact.id}`}
                          placeholder="Contact name"
                          className={
                            errors.contacts?.[index]?.name
                              ? "border-red-500"
                              : ""
                          }
                        />
                      )}
                    />
                    {errors.contacts?.[index]?.name && (
                      <Text size="small" className="text-red-600">
                        {errors.contacts[index].name?.message}
                      </Text>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor={`contact_email_${contact.id}`}>Email *</Label>
                    <Controller
                      name={`contacts.${index}.email`}
                      control={control}
                      render={({ field }) => (
                        <Input
                          {...field}
                          id={`contact_email_${contact.id}`}
                          type="email"
                          placeholder="<EMAIL>"
                          className={
                            errors.contacts?.[index]?.email
                              ? "border-red-500"
                              : ""
                          }
                        />
                      )}
                    />
                    {errors.contacts?.[index]?.email && (
                      <Text size="small" className="text-red-600">
                        {errors.contacts[index].email?.message}
                      </Text>
                    )}
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor={`contact_phone_${contact.id}`}>
                      Phone Number *
                    </Label>
                    <Controller
                      name={`contacts.${index}.phone_number`}
                      control={control}
                      render={({ field }) => (
                        <Input
                          {...field}
                          id={`contact_phone_${contact.id}`}
                          type="tel"
                          placeholder="+****************"
                          className={
                            errors.contacts?.[index]?.phone_number
                              ? "border-red-500"
                              : ""
                          }
                        />
                      )}
                    />
                    {errors.contacts?.[index]?.phone_number && (
                      <Text size="small" className="text-red-600">
                        {errors.contacts[index]?.phone_number?.message}
                      </Text>
                    )}
                  </div>
                </div>


              </div>
            ))}
          </div>
        </div>
      </Container>

      {/* Financial Information */}
      <Container className="divide-y p-0 mt-1">
        <div className="flex items-center justify-between px-6 py-4">
          <Heading level="h2">Financial Information</Heading>
        </div>
        <div className="px-6 py-4">
          <div className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="space-y-2">
                <Label>Payment Method</Label>
                <Controller
                  name="payment_method"
                  control={control}
                  render={({ field }) => (
                    <Select
                      value={field.value || ""}
                      onValueChange={field.onChange}
                    >
                      <Select.Trigger>
                        <Select.Value placeholder="Select payment method" />
                      </Select.Trigger>
                      <Select.Content>
                        {PAYMENT_METHODS.map((method) => (
                          <Select.Item key={method.value} value={method.value}>
                            {method.label}
                          </Select.Item>
                        ))}
                      </Select.Content>
                    </Select>
                  )}
                />
              </div>

              <div className="space-y-2">
                <Label>Payment Terms</Label>
                <Controller
                  name="payout_terms"
                  control={control}
                  render={({ field }) => (
                    <Select
                      value={field.value || ""}
                      onValueChange={field.onChange}
                    >
                      <Select.Trigger>
                        <Select.Value placeholder="Select payment terms" />
                      </Select.Trigger>
                      <Select.Content>
                        {PAYOUT_TERMS.map((term) => (
                          <Select.Item key={term.value} value={term.value}>
                            {term.label}
                          </Select.Item>
                        ))}
                      </Select.Content>
                    </Select>
                  )}
                />
              </div>

              <div className="space-y-2">
                <Label>Default Currency *</Label>
                <Controller
                  name="default_currency"
                  control={control}
                  render={({ field }) => (
                    <Select value={field.value} onValueChange={field.onChange}>
                      <Select.Trigger>
                        <Select.Value />
                      </Select.Trigger>
                      <Select.Content>
                        {CURRENCIES.map((currency) => (
                          <Select.Item
                            key={currency.value}
                            value={currency.value}
                          >
                            {currency.label}
                          </Select.Item>
                        ))}
                      </Select.Content>
                    </Select>
                  )}
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="space-y-2">
                <Label htmlFor="tax_id">Tax ID / VAT No.</Label>
                <Controller
                  name="tax_id"
                  control={control}
                  render={({ field }) => (
                    <Input
                      {...field}
                      id="tax_id"
                      placeholder="Enter tax identification number"
                    />
                  )}
                />
                {errors.tax_id && (
                  <Text size="small" className="text-red-600">
                    {errors.tax_id.message}
                  </Text>
                )}
              </div>
            </div>
            <div className="space-y-2">
              <Label htmlFor="bank_account_details">Bank Account Details</Label>
              <Controller
                name="bank_account_details"
                control={control}
                render={({ field }) => (
                  <Textarea
                    {...field}
                    id="bank_account_details"
                    placeholder="Enter bank account information"
                    rows={3}
                  />
                )}
              />
            </div>
          </div>
        </div>
      </Container>

      {/* Documents */}
      <Container className="divide-y p-0 mt-1">
        <div className="flex items-center justify-between px-6 py-4">
          <Heading level="h2">Documents</Heading>
        </div>
        <div className="px-6 py-4">
          <div className="space-y-4">
            <div>
              <Button
                variant="secondary"
                size="small"
                type="button"
                className="mr-2"
                disabled={isSubmitting || uploading}
                onClick={() => {
                  const input = document.createElement('input');
                  input.type = 'file';
                  input.multiple = true;
                  input.accept = '.pdf,.jpg,.jpeg,.png,.doc,.docx';
                  input.onchange = (e) => {
                    const target = e.target as HTMLInputElement;
                    if (target.files) {
                      handleFiles(Array.from(target.files));
                    }
                  };
                  input.click();
                }}
              >
                <Upload className="h-4 w-4 mr-2" />
                Upload Documents
              </Button>
              <Text className="text-ui-fg-subtle text-sm mt-2">
                Supported formats: PDF, JPG, PNG, DOC, DOCX (max 10MB each)
              </Text>
            </div>

            {preUploadFiles.length > 0 && (
              <div className="space-y-2">
                <Text weight="plus">Selected Files</Text>
                {preUploadFiles.map((file) => (
                  <div
                    key={file.id}
                    className="flex items-center justify-between p-3 border rounded-lg"
                  >
                    <div className="flex items-center space-x-3">
                      {getStatusIcon(file.status)}
                      <div>
                        <Text size="small" weight="plus">
                          {file.file.name}
                        </Text>
                        <Text size="small" className="text-gray-500">
                          {formatFileSize(file.file.size)}
                        </Text>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Button
                        variant="transparent"
                        size="small"
                        onClick={() => removeFile(file.id)}
                        disabled={uploading || file.status === "uploading"}
                      >
                        <Trash2 className="h-4 w-4 text-red-500" />
                      </Button>
                      {file.status === "error" && (
                        <Text size="small" className="text-red-600">
                          {file.error}
                        </Text>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            )}

            {preUploadFiles.length === 0 && (
              <Text className="text-gray-500">
                No documents uploaded yet.
              </Text>
            )}
          </div>
        </div>
      </Container>

      {/* Form Actions */}
      <div className="flex items-center justify-end gap-3 mt-3">
        <Button
          type="button"
          variant="secondary"
          onClick={onCancel}
          disabled={isSubmitting}
        >
          <X className="w-4 h-4 mr-2" />
          Cancel
        </Button>
        <Button
          type="button"
          onClick={handleSubmit(handleFormSubmit)}
          disabled={isSubmitting}
        >
          <Save className="w-4 h-4 mr-2" />
          {isSubmitting ? "Creating..." : "Create Supplier"}
        </Button>
      </div>
    </>
  );
};

export default SupplierCreateForm;
