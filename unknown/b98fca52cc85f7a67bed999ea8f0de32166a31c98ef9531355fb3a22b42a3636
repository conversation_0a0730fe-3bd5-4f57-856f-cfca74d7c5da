import { ExecArgs } from "@camped-ai/framework/types";
import { withClient } from "../utils/db";

export default async function migrateConciergeForeignKeys({ container }: ExecArgs) {
  console.log('🔧 Migrating concierge order item foreign keys...');
  
  try {
    await withClient(async (client) => {
      // First, identify all concierge order items with incorrect item_id references
      console.log('\n📊 Identifying records that need migration...');
      
      const incorrectReferencesResult = await client.query(`
        SELECT 
          coi.id as concierge_item_id,
          coi.item_id as current_item_id,
          coi.line_item_id,
          oi.id as correct_order_item_id,
          oli.title as line_item_title
        FROM concierge_order_item coi
        JOIN order_line_item oli ON coi.item_id = oli.id  -- Current incorrect reference
        LEFT JOIN order_item oi ON oi.item_id = oli.id    -- Find the correct order_item
        WHERE coi.deleted_at IS NULL
        ORDER BY coi.created_at DESC
      `);
      
      console.log(`Found ${incorrectReferencesResult.rows.length} records that need migration:`);
      
      let migratedCount = 0;
      let skippedCount = 0;
      let errorCount = 0;
      
      for (const record of incorrectReferencesResult.rows) {
        console.log(`\n🔄 Processing: ${record.concierge_item_id}`);
        console.log(`   Line item: ${record.line_item_title}`);
        console.log(`   Current item_id: ${record.current_item_id} (order_line_item.id)`);
        console.log(`   Correct item_id: ${record.correct_order_item_id || 'NOT FOUND'} (order_item.id)`);
        
        if (!record.correct_order_item_id) {
          console.log(`   ⚠️ Skipping: No corresponding order_item found`);
          skippedCount++;
          continue;
        }
        
        if (record.current_item_id === record.correct_order_item_id) {
          console.log(`   ✅ Already correct: item_id already references order_item`);
          continue;
        }
        
        try {
          // Update the concierge_order_item to use the correct order_item.id
          const updateResult = await client.query(`
            UPDATE concierge_order_item 
            SET item_id = $1, updated_at = NOW()
            WHERE id = $2 AND deleted_at IS NULL
          `, [record.correct_order_item_id, record.concierge_item_id]);
          
          if (updateResult.rowCount > 0) {
            console.log(`   ✅ Migrated: item_id updated to ${record.correct_order_item_id}`);
            migratedCount++;
          } else {
            console.log(`   ❌ Failed: No rows updated`);
            errorCount++;
          }
          
        } catch (error) {
          console.log(`   ❌ Error updating record: ${error.message}`);
          errorCount++;
        }
      }
      
      // Verify the migration results
      console.log('\n📊 Migration Summary:');
      console.log(`   Records processed: ${incorrectReferencesResult.rows.length}`);
      console.log(`   Successfully migrated: ${migratedCount}`);
      console.log(`   Skipped (no order_item found): ${skippedCount}`);
      console.log(`   Errors: ${errorCount}`);
      
      // Verify the final state
      console.log('\n🔍 Verifying final state...');
      
      const finalStateResult = await client.query(`
        SELECT 
          COUNT(*) as total_concierge_items,
          COUNT(CASE WHEN oi.id IS NOT NULL THEN 1 END) as correct_references,
          COUNT(CASE WHEN oli.id IS NOT NULL AND oi.id IS NULL THEN 1 END) as incorrect_references
        FROM concierge_order_item coi
        LEFT JOIN order_item oi ON coi.item_id = oi.id
        LEFT JOIN order_line_item oli ON coi.item_id = oli.id
        WHERE coi.deleted_at IS NULL
      `);
      
      const finalStats = finalStateResult.rows[0];
      console.log(`Final state:`);
      console.log(`   Total concierge items: ${finalStats.total_concierge_items}`);
      console.log(`   Correct references (item_id → order_item.id): ${finalStats.correct_references}`);
      console.log(`   Incorrect references (item_id → order_line_item.id): ${finalStats.incorrect_references}`);
      
      if (finalStats.incorrect_references === '0') {
        console.log('\n🎉 Migration completed successfully! All foreign key references are now correct.');
      } else {
        console.log(`\n⚠️ Migration incomplete: ${finalStats.incorrect_references} records still have incorrect references.`);
      }
      
      // Show a few examples of the corrected records
      console.log('\n📋 Sample of corrected records:');
      
      const sampleResult = await client.query(`
        SELECT 
          coi.id as concierge_item_id,
          coi.item_id,
          coi.line_item_id,
          oi.item_id as order_item_references,
          oli.title as line_item_title
        FROM concierge_order_item coi
        JOIN order_item oi ON coi.item_id = oi.id
        JOIN order_line_item oli ON coi.line_item_id = oli.id
        WHERE coi.deleted_at IS NULL
        ORDER BY coi.updated_at DESC
        LIMIT 3
      `);
      
      sampleResult.rows.forEach((row, index) => {
        console.log(`${index + 1}. ${row.concierge_item_id}`);
        console.log(`   item_id: ${row.item_id} → order_item.id ✅`);
        console.log(`   line_item_id: ${row.line_item_id} → order_line_item.id ✅`);
        console.log(`   order_item.item_id: ${row.order_item_references} (matches line_item_id: ${row.order_item_references === row.line_item_id ? 'YES' : 'NO'})`);
        console.log(`   Line item: ${row.line_item_title}`);
        console.log('');
      });
      
    });
  } catch (error) {
    console.error('❌ Migration failed:', error);
  }
}
