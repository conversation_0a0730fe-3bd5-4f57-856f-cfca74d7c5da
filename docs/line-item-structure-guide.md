# LineItem Structure Guide for Cart Creation

## **Basic LineItem Structure**

```typescript
interface LineItem {
  variant_id: string;           // REQUIRED - Product variant ID
  quantity: number;             // REQUIRED - Item quantity (must be > 0)
  unit_price?: number;          // OPTIONAL - Custom price in cents (for quotes)
  title?: string;              // OPTIONAL - Custom item title
  metadata?: object;           // OPTIONAL - Custom data for the item
  requires_shipping?: boolean; // OPTIONAL - Whether item needs shipping (default: true)
}
```

## **Required Parameters**

### **1. variant_id** (string) - REQUIRED
- **What it is**: The ID of the product variant you want to add to cart
- **Where to get it**: From your product/variant creation or product listing APIs
- **Examples**:
  - Room variants: `"variant_room_101"`, `"variant_deluxe_suite"`
  - Add-on variants: `"variant_ground_transfer_adult"`, `"variant_spa_package"`

### **2. quantity** (number) - REQUIRED
- **What it is**: How many of this item to add
- **Must be**: Greater than 0
- **Examples**:
  - Rooms: `1` (for 1 room), `2` (for 2 rooms)
  - Add-ons: `2` (for 2 adults), `1` (for 1 package)

## **Optional Parameters**

### **3. unit_price** (number) - OPTIONAL
- **What it is**: Custom price per unit in **cents** (not dollars!)
- **When to use**: For quotes with custom pricing
- **Important**: Price is in cents, so $250.00 = `25000`
- **Examples**:
  ```typescript
  unit_price: 25000  // $250.00
  unit_price: 5500   // $55.00
  ```

### **4. title** (string) - OPTIONAL
- **What it is**: Custom display name for the item
- **When to use**: When you want to override the default product title
- **Examples**:
  ```typescript
  title: "Deluxe Room - Special Rate"
  title: "Ground Transfer (2 Adults)"
  title: "Spa Package - Weekend Special"
  ```

### **5. metadata** (object) - OPTIONAL
- **What it is**: Custom data storage for business logic
- **Very useful for**: Hotel bookings, tracking, and custom workflows
- **Examples**: See detailed metadata section below

### **6. requires_shipping** (boolean) - OPTIONAL
- **What it is**: Whether this item needs physical shipping
- **Default**: `true`
- **For hotels**: Usually `false` (digital/service items)
- **Examples**:
  ```typescript
  requires_shipping: false  // Hotel rooms, digital services
  requires_shipping: true   // Physical welcome packages, souvenirs
  ```

## **Real-World Examples**

### **Example 1: Hotel Room Booking**
```typescript
{
  variant_id: "variant_deluxe_room_101",
  quantity: 1,
  title: "Deluxe Room 101",
  unit_price: 25000, // $250.00 per night (custom quote price)
  requires_shipping: false,
  metadata: {
    hotel_id: "hotel_123",
    hotel_name: "Grand Hotel Zurich",
    room_config_id: "room_cfg_deluxe",
    check_in_date: "2024-08-01T12:00:00Z",
    check_out_date: "2024-08-03T12:00:00Z",
    number_of_nights: 2,
    guests: {
      adults: 2,
      children: 1,
      infants: 0
    },
    room_features: ["balcony", "lake_view", "king_bed"],
    special_requests: "High floor, quiet room"
  }
}
```

### **Example 2: Add-on Service (Ground Transfer)**
```typescript
{
  variant_id: "variant_ground_transfer_adult",
  quantity: 2, // 2 adults
  title: "Airport Ground Transfer (Adult)",
  unit_price: 5000, // $50.00 per adult
  requires_shipping: false,
  metadata: {
    add_on_service: true,
    service_id: "service_ground_transfer",
    service_name: "Airport Ground Transfer",
    price_type: "adult",
    pricing_type: "per_person",
    service_level: "hotel",
    pickup_location: "Zurich Airport (ZUR)",
    dropoff_location: "Grand Hotel Zurich",
    pickup_time: "14:30",
    flight_number: "LX1234",
    special_instructions: "Meet at arrivals gate"
  }
}
```

### **Example 3: Package Deal (Spa Package)**
```typescript
{
  variant_id: "variant_spa_package_couples",
  quantity: 1, // 1 package for 2 people
  title: "Couples Spa Package",
  unit_price: 18000, // $180.00 for the package
  requires_shipping: false,
  metadata: {
    add_on_service: true,
    service_id: "service_spa_package",
    service_name: "Couples Spa Package",
    pricing_type: "package",
    service_level: "hotel",
    package_includes: [
      "60-min couples massage",
      "Champagne and strawberries",
      "Access to spa facilities"
    ],
    preferred_date: "2024-08-02",
    preferred_time: "15:00",
    duration_minutes: 120,
    participants: 2
  }
}
```

### **Example 4: Multiple Rooms**
```typescript
[
  {
    variant_id: "variant_deluxe_room_101",
    quantity: 1,
    title: "Deluxe Room 101",
    requires_shipping: false,
    metadata: {
      room_assignment: "Room 101",
      primary_guest: "John Doe",
      guests: { adults: 2, children: 0 }
    }
  },
  {
    variant_id: "variant_standard_room_205", 
    quantity: 1,
    title: "Standard Room 205",
    requires_shipping: false,
    metadata: {
      room_assignment: "Room 205",
      primary_guest: "Jane Smith", 
      guests: { adults: 1, children: 1 }
    }
  }
]
```

## **Common Metadata Patterns**

### **Hotel Room Metadata**
```typescript
metadata: {
  // Hotel Information
  hotel_id: "hotel_123",
  hotel_name: "Grand Hotel Zurich",
  room_config_id: "room_cfg_deluxe",
  room_number: "101",
  
  // Booking Dates
  check_in_date: "2024-08-01T12:00:00Z",
  check_out_date: "2024-08-03T12:00:00Z", 
  number_of_nights: 2,
  
  // Guest Information
  guests: {
    adults: 2,
    children: 1,
    infants: 0
  },
  
  // Room Details
  room_features: ["balcony", "lake_view"],
  bed_type: "king",
  max_occupancy: 4,
  
  // Special Requests
  special_requests: "High floor, quiet room",
  accessibility_needs: false,
  
  // Pricing Context
  rate_plan: "standard",
  promotional_code: "SUMMER2024"
}
```

### **Add-on Service Metadata**
```typescript
metadata: {
  // Service Classification
  add_on_service: true,
  service_id: "service_123",
  service_name: "Ground Transfer",
  service_level: "hotel", // or "destination"
  pricing_type: "per_person", // or "package" or "usage_based"
  
  // Service Details
  duration_minutes: 45,
  capacity: 4,
  location: "Airport to Hotel",
  
  // Scheduling
  service_date: "2024-08-01",
  service_time: "14:30",
  
  // Participants
  participants: [
    { type: "adult", name: "John Doe" },
    { type: "adult", name: "Jane Doe" }
  ],
  
  // Special Requirements
  special_instructions: "Meet at arrivals",
  equipment_needed: ["child_seat"],
  dietary_restrictions: ["vegetarian"]
}
```

## **Important Notes**

### **Pricing**
- ⚠️ **Always use cents**: `25000` = $250.00, not `250`
- 💰 **Custom pricing**: Use `unit_price` for quotes
- 🏷️ **Default pricing**: Omit `unit_price` to use catalog prices

### **Variant IDs**
- 🔍 **Must exist**: Variant must be created before adding to cart
- 🏨 **Room variants**: Individual rooms are variants of room types
- 🎯 **Add-on variants**: Different pricing tiers (adult/child/package)

### **Shipping**
- 🚚 **Physical items**: `requires_shipping: true`
- 💻 **Digital/Services**: `requires_shipping: false`
- 📦 **Hotel bookings**: Usually `false`

### **Metadata Best Practices**
- 📝 **Be descriptive**: Include all relevant business data
- 🔗 **Reference IDs**: Store related entity IDs for tracking
- 📅 **Dates**: Use ISO format for consistency
- 🏷️ **Categorize**: Use flags like `add_on_service: true`

## **Validation Rules**

Your cart validation will check:
- ✅ `variant_id` exists and is active
- ✅ `quantity` is greater than 0
- ✅ `unit_price` is positive number (if provided)
- ✅ Variant has sufficient inventory
- ✅ Pricing is consistent with business rules
