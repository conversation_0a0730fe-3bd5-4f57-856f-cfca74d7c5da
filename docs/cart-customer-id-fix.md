# Cart Customer ID Fix - Medusa Standards Compliance

## Problem
You were getting this error when creating carts:
```
ZodError: [
  {
    "code": "unrecognized_keys",
    "keys": ["customer_id"],
    "path": [],
    "message": "Unrecognized key(s) in object: 'customer_id'"
  }
]
```

## Root Cause
The issue was that we were using `customer_id` in our cart creation payload, but **Medusa's official `StoreCreateCart` interface does NOT include `customer_id`**.

According to the [official Medusa documentation](https://docs.medusajs.com/resources/references/types/HttpTypes/interfaces/types.HttpTypes.StoreCreateCart), the correct fields are:

- `email` - Used to associate the cart with a customer
- `region_id` - Optional (uses default region if not provided)
- `currency_code` - Optional (uses region's currency if not provided)
- `sales_channel_id` - Optional
- `promo_codes` - Optional
- `items` - Optional
- `shipping_address` - Optional
- `billing_address` - Optional
- `metadata` - Optional

## Solution

### ✅ Corrected Payload Structure
```json
{
  "region_id": "reg_01JXJ7K6W55Y74WAQ3QQG1WFDA",
  "currency_code": "GBP",
  "email": "<EMAIL>",
  "sales_channel_id": "sc_01JNR887105TH162F04RB9RKC0",
  "items": [
    {
      "variant_id": "variant_01JWR2V7STJK8SDJK2E84G049N",
      "quantity": 2,
      "unit_price": 98000,
      "title": "Hotel Valsana - Lifestyle Double Room 150",
      "metadata": {
        "product_id": "prod_01JWR26K8BNVDX0A1KKETHSTB6",
        "item_type": "room",
        "hotel_id": "01JWR0DA3F4H9KZ0STCA2VKYXT"
      },
      "requires_shipping": false
    }
  ]
}
```

### ❌ What Was Wrong
```json
{
  "customer_id": "cus_1745223971217", // ❌ NOT SUPPORTED by Medusa
  "email": "<EMAIL>"
}
```

### ✅ What's Correct
```json
{
  "email": "<EMAIL>" // ✅ Medusa uses email to associate with customer
}
```

## How Medusa Handles Customer Association

### For Guest Customers
```json
{
  "email": "<EMAIL>"
}
```
- Medusa creates a guest cart
- If a customer with this email exists, it associates the cart
- If no customer exists, it remains a guest cart

### For Authenticated Customers
```json
{
  "email": "<EMAIL>"
}
```
- If the request is authenticated (has customer session), Medusa automatically associates the cart
- The email is used for order confirmation and customer lookup

## Files Updated

### 1. Cart Schema (`src/api/store/carts/schemas.ts`)
- ❌ Removed `customer_id: z.string().optional()`
- ✅ Added `promo_codes: z.array(z.string()).optional()`
- ✅ Made `region_id` and `currency_code` optional (following Medusa standards)

### 2. Cart Route (`src/api/store/carts/route.ts`)
- ❌ Removed `customer_id` from destructuring
- ✅ Added `promo_codes` to destructuring
- ✅ Updated `cartInput` to exclude `customer_id`

### 3. Validation Utils (`src/utils/cart-validation.ts`)
- ❌ Removed `customer_id` from validation interface
- ✅ Updated validation logic to focus on `email`

### 4. Middleware (`src/api/store/carts/middlewares.ts`)
- ✅ Updated schema to match official Medusa `StoreCreateCart`
- ❌ Removed `customer_id` validation

## Testing Your Fix

### Test Script
```bash
node scripts/test-cart-creation-fix.js
```

### Manual Test
```bash
curl -X POST http://localhost:9000/store/carts \
  -H "Content-Type: application/json" \
  -H "x-publishable-api-key: YOUR_API_KEY" \
  -d '{
    "region_id": "reg_01JXJ7K6W55Y74WAQ3QQG1WFDA",
    "currency_code": "GBP", 
    "email": "<EMAIL>",
    "sales_channel_id": "sc_01JNR887105TH162F04RB9RKC0",
    "items": [
      {
        "variant_id": "variant_01JWR2V7STJK8SDJK2E84G049N",
        "quantity": 2,
        "unit_price": 98000,
        "requires_shipping": false
      }
    ]
  }'
```

## Key Takeaways

1. **Always follow official Medusa interfaces** - Don't assume fields exist
2. **Use `email` for customer association** - Not `customer_id`
3. **Region and currency are optional** - Medusa has sensible defaults
4. **Test with official documentation** - Verify against Medusa's type definitions

## Customer Lookup After Cart Creation

If you need the customer ID after cart creation, you can:

1. **Query the cart** - The response will include customer association
2. **Use Customer API** - Look up customer by email
3. **Check authentication** - If user is logged in, get customer from session

```typescript
// After cart creation, if you need customer info:
const customer = await customerModuleService.listCustomers({
  email: "<EMAIL>"
});
```

## Benefits of This Fix

✅ **Standards Compliant** - Follows official Medusa interfaces
✅ **Future Proof** - Won't break with Medusa updates  
✅ **Cleaner Code** - Removes unnecessary custom logic
✅ **Better Integration** - Works with Medusa's built-in customer handling
✅ **Proper Validation** - Framework validation now works correctly

Your cart creation should now work perfectly with the corrected payload structure!
