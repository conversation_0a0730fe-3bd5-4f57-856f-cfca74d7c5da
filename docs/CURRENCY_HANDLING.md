# Currency Handling Standards

This document outlines the currency handling standards for the medusa-store-v2 application to ensure compliance with Medusa's recommended practices.

## Overview

All currency amounts in the application must follow these principles:

1. **Storage**: All currency amounts are stored as integers in the smallest currency unit (e.g., cents for USD, pence for GBP)
2. **Display**: Currency amounts are converted to decimal format for user display
3. **Input**: User inputs in decimal format are converted to smallest units before storage
4. **API**: All API endpoints expect and return amounts in smallest currency units

## Storage Format

### Database Models

All currency amount fields in database models store values as integers representing the smallest currency unit:

```typescript
// ✅ Correct - stores amount in cents
amount: model.number(), // Amount in smallest currency unit (e.g., cents for USD)

// ❌ Incorrect - stores decimal amounts
amount: model.decimal(10, 2), // Don't use DECIMAL for currency
```

### Examples

- $10.50 USD → stored as `1050` (cents)
- €25.99 EUR → stored as `2599` (cents)
- ¥1000 JPY → stored as `1000` (yen, no decimal places)
- 10.500 KWD → stored as `10500` (fils, 3 decimal places)

## Display Format

### UI Components

Use the standardized currency helpers for consistent display:

```typescript
import { formatCurrencyDisplay } from "../utils/currency-helpers";

// ✅ Correct - converts from smallest unit to display
const displayAmount = formatCurrencyDisplay(1050, "USD"); // "$10.50"

// ❌ Incorrect - assumes amount is already in display format
const displayAmount = new Intl.NumberFormat("en-US", {
  style: "currency",
  currency: "USD",
}).format(amount); // Wrong if amount is in cents
```

### Currency Display Component

Use the `CurrencyDisplay` component for consistent formatting:

```tsx
import { CurrencyDisplay } from "../components/common/currency-display";

<CurrencyDisplay 
  amount={1050} // Amount in smallest unit
  currencyCode="USD"
  showSymbol={true}
/>
```

## Input Handling

### Form Inputs

Use the standardized currency input helpers:

```typescript
import { getCurrencyInputDisplayValue, createCurrencyInputHandler } from "../utils/currency-helpers";

// ✅ Correct - handles conversion automatically
<Input
  type="number"
  value={getCurrencyInputDisplayValue(amount, currencyCode)}
  onChange={createCurrencyInputHandler(currencyCode, setAmount)}
/>

// ❌ Incorrect - manual conversion prone to errors
<Input
  type="number"
  value={amount / 100}
  onChange={(e) => setAmount(parseFloat(e.target.value) * 100)}
/>
```

### Currency Input Component

Use the `CurrencyInput` component for currency-specific inputs:

```tsx
import { CurrencyInput } from "../components/common/currency-display";

<CurrencyInput
  value={amount} // Amount in smallest unit
  onChange={setAmount} // Receives amount in smallest unit
  currencyCode="USD"
/>
```

## API Integration

### Request/Response Format

All API endpoints work with amounts in smallest currency units:

```typescript
// ✅ Correct - API expects amounts in smallest units
const response = await fetch("/api/orders", {
  method: "POST",
  body: JSON.stringify({
    total_amount: 1050, // $10.50 as 1050 cents
    currency_code: "USD"
  })
});

// ✅ Correct - API returns amounts in smallest units
const order = await response.json();
const displayTotal = formatCurrencyDisplay(order.total_amount, order.currency_code);
```

## Utility Functions

### Core Helpers

Located in `src/utils/currency-helpers.ts`:

- `toSmallestCurrencyUnit(amount, currencyCode)` - Convert display amount to smallest unit
- `fromSmallestCurrencyUnit(amount, currencyCode)` - Convert smallest unit to display amount
- `formatCurrencyDisplay(amount, currencyCode, locale)` - Format for display
- `parseCurrencyToSmallestUnit(value, currencyCode)` - Parse string to smallest unit

### Currency-Specific Helpers

- `getCurrencyDecimalDigits(currencyCode)` - Get decimal places for currency
- `isAmountInSmallestUnit(amount, currencyCode)` - Validate format
- `migrateLegacyAmount(amount, currencyCode)` - Convert legacy decimal amounts

## Migration Guide

### Updating Existing Code

1. **Database Migrations**: Convert DECIMAL columns to INTEGER
2. **Model Updates**: Add comments indicating smallest unit storage
3. **UI Components**: Update to use currency helpers
4. **Form Inputs**: Convert to use standardized input handlers
5. **API Endpoints**: Verify smallest unit compliance

### Legacy Data Migration

Use the migration utility for existing decimal data:

```typescript
import { migrateLegacyAmount } from "../utils/currency-helpers";

// Convert existing decimal amounts to smallest units
const newAmount = migrateLegacyAmount(oldDecimalAmount, currencyCode);
```

## Testing

### Unit Tests

Test currency conversion functions:

```typescript
import { toSmallestCurrencyUnit, fromSmallestCurrencyUnit } from "../utils/currency-helpers";

// Test USD (2 decimal places)
expect(toSmallestCurrencyUnit(10.50, "USD")).toBe(1050);
expect(fromSmallestCurrencyUnit(1050, "USD")).toBe(10.50);

// Test JPY (0 decimal places)
expect(toSmallestCurrencyUnit(1000, "JPY")).toBe(1000);
expect(fromSmallestCurrencyUnit(1000, "JPY")).toBe(1000);

// Test KWD (3 decimal places)
expect(toSmallestCurrencyUnit(10.500, "KWD")).toBe(10500);
expect(fromSmallestCurrencyUnit(10500, "KWD")).toBe(10.500);
```

### Integration Tests

Verify end-to-end currency handling:

1. User enters amount in form
2. Amount is converted to smallest unit
3. Amount is stored correctly in database
4. Amount is retrieved and displayed correctly

## Common Pitfalls

### ❌ Avoid These Patterns

1. **Storing decimals in database**:
   ```sql
   -- Don't do this
   ALTER TABLE orders ADD COLUMN amount DECIMAL(10,2);
   ```

2. **Manual conversion without helpers**:
   ```typescript
   // Don't do this - error-prone
   const displayAmount = amount / 100;
   ```

3. **Inconsistent decimal places**:
   ```typescript
   // Don't assume all currencies have 2 decimal places
   const cents = amount * 100; // Wrong for JPY, KWD, etc.
   ```

4. **Direct API amount usage in UI**:
   ```tsx
   // Don't display raw API amounts
   <span>${order.total_amount}</span> {/* Shows 1050 instead of $10.50 */}
   ```

### ✅ Best Practices

1. **Always use currency helpers**
2. **Document currency fields clearly**
3. **Test with multiple currency types**
4. **Validate input ranges appropriately**
5. **Handle edge cases (zero amounts, negative amounts)**

## Support

For questions about currency handling:

1. Check this documentation first
2. Review the utility functions in `src/utils/currency-helpers.ts`
3. Look at existing implementations in the codebase
4. Consult the Medusa documentation on currency handling
