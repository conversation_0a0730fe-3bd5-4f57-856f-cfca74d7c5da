# Stripe + Manual Payment Workflow Guide

## Overview

Your payment system now supports:
1. **Stripe payments** - For online card payments (deposits/full payments)
2. **Manual payment marking** - For bank transfers, cash, checks
3. **Sales team workflow** - Mark payments as collected via admin API

## Payment Flow Options

### **Option 1: Stripe Deposit + Manual Collection**
```mermaid
graph LR
    A[Customer] --> B[Pay Deposit via Stripe]
    B --> C[Order Created]
    C --> D[Sales Team Collects Remaining]
    D --> E[Mark Payment Collected]
    E --> F[Order Completed]
```

### **Option 2: Manual Payment Only**
```mermaid
graph LR
    A[Customer] --> B[Quote Approved]
    B --> C[Order Created with Minimal Payment]
    C --> D[Customer Pays via Bank Transfer]
    D --> E[Sales Team Marks Payment]
    E --> F[Order Completed]
```

### **Option 3: Full Stripe Payment**
```mermaid
graph LR
    A[Customer] --> B[Pay Full Amount via Stripe]
    B --> C[Order Created & Completed]
```

## API Endpoints

### **1. Complete Cart with Payment**
`POST /store/carts/{cart_id}/complete-with-payment`

#### **Stripe Deposit (20% default)**
```json
{
  "payment_type": "deposit",
  "partial_amount": 43280,
  "payment_provider_id": "pp_stripe_stripe"
}
```

#### **Stripe Full Payment**
```json
{
  "payment_type": "full",
  "payment_provider_id": "pp_stripe_stripe"
}
```

#### **Manual Payment (Quote Approval)**
```json
{
  "payment_type": "manual",
  "partial_amount": 100,
  "payment_provider_id": "pp_stripe_stripe"
}
```

### **2. Mark Payment as Collected (Admin)**
`POST /admin/orders/{order_id}/mark-payment-collected`

#### **Bank Transfer Collection**
```json
{
  "amount": 173120,
  "payment_method": "bank_transfer",
  "collected_by": "sales_user_123",
  "reference_number": "TXN123456789",
  "notes": "Customer paid remaining amount via bank transfer",
  "collection_date": "2024-01-15T10:30:00Z"
}
```

#### **Cash Collection**
```json
{
  "amount": 50000,
  "payment_method": "cash",
  "collected_by": "reception_staff",
  "notes": "Cash payment collected at hotel reception"
}
```

### **3. Check Payment Status (Admin)**
`GET /admin/orders/{order_id}/payment-status`

Returns current payment status and collection history.

## Real-World Examples

### **Example 1: £2164 Hotel Booking with £432 Stripe Deposit**

#### Step 1: Customer Pays Deposit
```bash
POST /store/carts/cart_123/complete-with-payment
{
  "payment_type": "deposit",
  "partial_amount": 43200,
  "payment_provider_id": "pp_stripe_stripe"
}
```

**Response:**
```json
{
  "success": true,
  "order": {
    "id": "order_456",
    "total": 216400,
    "currency_code": "GBP"
  },
  "payment_info": {
    "payment_type": "deposit",
    "paid_amount": 43200,
    "remaining_amount": 173200,
    "requires_additional_payment": true
  },
  "message": "Order created with deposit payment. Remaining £1732 to be collected by concierge team."
}
```

#### Step 2: Customer Pays Remaining via Bank Transfer
Sales team receives bank transfer and marks it:

```bash
POST /admin/orders/order_456/mark-payment-collected
{
  "amount": 173200,
  "payment_method": "bank_transfer",
  "collected_by": "sales_manager_john",
  "reference_number": "BANK_TXN_789",
  "notes": "Customer completed payment via bank transfer"
}
```

**Response:**
```json
{
  "success": true,
  "payment_summary": {
    "total_amount": 216400,
    "previously_paid": 43200,
    "amount_collected": 173200,
    "new_paid_amount": 216400,
    "remaining_amount": 0,
    "is_fully_paid": true,
    "payment_status": "fully_paid"
  },
  "message": "Payment fully collected. Order completed."
}
```

### **Example 2: Manual Payment Only (Quote Approval)**

#### Step 1: Approve Quote (No Real Payment)
```bash
POST /store/carts/cart_789/complete-with-payment
{
  "payment_type": "manual",
  "partial_amount": 100,
  "payment_provider_id": "pp_stripe_stripe"
}
```

#### Step 2: Customer Pays Full Amount via Bank Transfer
```bash
POST /admin/orders/order_101/mark-payment-collected
{
  "amount": 216300,
  "payment_method": "bank_transfer",
  "collected_by": "sales_team",
  "reference_number": "WIRE_456789",
  "notes": "Full payment received via wire transfer"
}
```

## Sales Team Workflow

### **Dashboard Integration**
1. **View orders** with payment status
2. **See remaining amounts** to collect
3. **Mark payments** as collected
4. **Track collection history**

### **Payment Methods Supported**
- `bank_transfer` - Wire transfers, ACH, etc.
- `cash` - Cash payments
- `check` - Check payments
- `manual` - Other manual methods
- `stripe_offline` - Stripe payments processed offline

### **Required Information**
- **Amount** - Exact amount collected (in cents)
- **Collected by** - Staff member who processed payment
- **Payment method** - How customer paid
- **Reference number** - Transaction ID, check number, etc.
- **Notes** - Additional context

## Order Metadata Tracking

Each order tracks payment information in metadata:

```json
{
  "metadata": {
    "payment_tracking": {
      "payment_type": "deposit",
      "paid_amount": 216400,
      "remaining_amount": 0,
      "total_amount": 216400,
      "currency_code": "GBP",
      "requires_additional_payment": false,
      "payment_status": "fully_paid",
      "collections": [
        {
          "amount": 43200,
          "payment_method": "stripe",
          "collected_by": "system",
          "collection_date": "2024-01-15T09:00:00Z",
          "status": "collected"
        },
        {
          "amount": 173200,
          "payment_method": "bank_transfer",
          "collected_by": "sales_manager_john",
          "collection_date": "2024-01-15T14:30:00Z",
          "reference_number": "BANK_TXN_789",
          "status": "collected"
        }
      ]
    }
  }
}
```

## Benefits

### **✅ For Customers**
- **Flexible payment options** - Stripe, bank transfer, cash
- **Lower upfront cost** - Pay deposit first
- **Familiar payment methods** - Use preferred payment method

### **✅ For Sales Team**
- **Clear tracking** - See exactly what's owed
- **Easy marking** - Simple API to mark payments
- **Full history** - Complete payment audit trail
- **Workflow integration** - Part of order management

### **✅ For Business**
- **Immediate cash flow** - Stripe deposits collected instantly
- **Reduced risk** - Partial payment secures booking
- **Flexibility** - Handle any payment scenario
- **Compliance** - Full payment tracking and audit trail

## Testing Your Setup

### **Test Stripe Deposit**
```bash
curl -X POST http://localhost:9000/store/carts/YOUR_CART_ID/complete-with-payment \
  -H "Content-Type: application/json" \
  -H "x-publishable-api-key: YOUR_API_KEY" \
  -d '{
    "payment_type": "deposit",
    "partial_amount": 10000,
    "payment_provider_id": "pp_stripe_stripe"
  }'
```

### **Test Manual Payment Marking**
```bash
curl -X POST http://localhost:9000/admin/orders/ORDER_ID/mark-payment-collected \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN" \
  -d '{
    "amount": 50000,
    "payment_method": "bank_transfer",
    "collected_by": "test_user",
    "notes": "Test payment collection"
  }'
```

Your payment workflow is now ready! 🎉
