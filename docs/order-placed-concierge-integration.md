# Order Placed Concierge Integration

## Overview

This document describes the implementation of automatic concierge order creation when an order is placed in the system. The integration ensures that every order placed triggers the creation of corresponding entries in the `concierge_order` and `concierge_order_item` tables.

## Implementation Details

### Modified Files

1. **`src/subscribers/order-placed.ts`**
   - Added imports for concierge management module and types
   - Added call to `createConciergeOrderEntry()` function after order retrieval
   - Added helper function to create concierge order and line items

### How It Works

When an order is placed (`order.placed` event), the subscriber:

1. **Retrieves the order** with all line items
2. **Creates a concierge order entry** with:
   - Reference to the original order ID
   - Status set to `NOT_STARTED`
   - Auto-generated notes
   - Metadata including order details
3. **Creates concierge order items** for each line item with:
   - Reference to the concierge order
   - Reference to the original line item
   - Item details (title, quantity, price)
   - Status set to `UNDER_REVIEW`
   - Metadata including original line item metadata

### Database Schema

#### Concierge Order Table
```sql
CREATE TABLE "concierge_order" (
    "id" text NOT NULL,
    "order_id" text NOT NULL,
    "assigned_to" text NULL,
    "notes" text NULL,
    "status" text NOT NULL DEFAULT 'not_started',
    "last_contacted_at" timestamptz NULL,
    "metadata" jsonb NULL,
    "created_at" timestamptz NOT NULL DEFAULT now(),
    "updated_at" timestamptz NOT NULL DEFAULT now(),
    "deleted_at" timestamptz NULL,
    CONSTRAINT "concierge_order_pkey" PRIMARY KEY ("id")
);
```

#### Concierge Order Item Table
```sql
CREATE TABLE "concierge_order_item" (
    "id" text NOT NULL,
    "concierge_order_id" text NOT NULL,
    "line_item_id" text NULL,
    "variant_id" text NULL,
    "quantity" integer NOT NULL,
    "unit_price" integer NOT NULL,
    "title" text NOT NULL,
    "status" text NOT NULL DEFAULT 'under_review',
    "is_active" boolean NOT NULL DEFAULT true,
    "added_by" text NULL,
    "finalized_by" text NULL,
    "added_at" timestamptz NOT NULL DEFAULT now(),
    "finalized_at" timestamptz NULL,
    "metadata" jsonb NULL,
    "created_at" timestamptz NOT NULL DEFAULT now(),
    "updated_at" timestamptz NOT NULL DEFAULT now(),
    "deleted_at" timestamptz NULL,
    CONSTRAINT "concierge_order_item_pkey" PRIMARY KEY ("id")
);
```

### Status Values

#### Concierge Order Status
- `not_started` - Initial status when order is created
- `in_progress` - When concierge starts working on the order
- `waiting_customer` - Waiting for customer response
- `ready_to_finalize` - Ready for final processing
- `completed` - Order processing completed

#### Concierge Order Item Status
- `under_review` - Concierge team is working with client for confirmation/rejection
- `client_confirmed` - Client confirmed, ready for supplier team action (primary focus)
- `order_placed` - Order has been placed with supplier
- `cancelled` - No longer needed by client or cannot be procured
- `completed` - Item has been delivered/fulfilled

### Error Handling

The implementation includes robust error handling:

1. **Duplicate Prevention**: Checks if a concierge order already exists for the order ID
2. **Non-blocking**: Errors in concierge order creation don't break the main order flow
3. **Logging**: Comprehensive logging for debugging and monitoring

### Testing

A test script is available at `scripts/test-order-placed-concierge.js` to verify:
- Data structure creation
- Import functionality
- Logic flow validation

Run the test with:
```bash
node scripts/test-order-placed-concierge.js
```

### Usage Example

When an order is placed, the system automatically:

```javascript
// Original order
{
  id: "order_123",
  total: 50000,
  currency_code: "usd",
  email: "<EMAIL>",
  items: [
    {
      id: "item_1",
      title: "Hotel Room",
      quantity: 2,
      unit_price: 15000
    }
  ]
}

// Creates concierge order
{
  id: "corder_456",
  order_id: "order_123",
  status: "not_started",
  notes: "Auto-created from order placement",
  metadata: {
    auto_created: true,
    order_total: 50000,
    order_currency: "usd",
    customer_email: "<EMAIL>"
  }
}

// Creates concierge order items
{
  id: "citem_789",
  concierge_order_id: "corder_456",
  line_item_id: "item_1",
  title: "Hotel Room",
  quantity: 2,
  unit_price: 15000,
  status: "under_review",
  metadata: {
    auto_created: true,
    original_line_item_metadata: {...}
  }
}
```

### Benefits

1. **Automatic Tracking**: Every order is automatically tracked in the concierge system
2. **Consistent Data**: Ensures all orders have corresponding concierge entries
3. **Audit Trail**: Maintains complete history of order processing
4. **Workflow Integration**: Enables concierge team to manage all orders systematically

### Future Enhancements

1. **Conditional Creation**: Add logic to only create concierge orders for specific criteria
2. **Assignment Rules**: Automatically assign orders to specific concierges based on rules
3. **Priority Setting**: Set priority levels based on order value or customer type
4. **Notification Integration**: Send notifications to concierge team for new orders
