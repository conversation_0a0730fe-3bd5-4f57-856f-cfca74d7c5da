# Advance Payment Workflow Documentation

## Overview

The Advance Payment Workflow provides a proper Medusa v2-based solution for handling advance payments and cart-to-order conversion in the sales team workflow. This implementation supports both manual payment collection and Stripe integration while maintaining full compatibility with Medusa's payment entities and event system.

## 🔧 **Critical Fixes Implemented**

### **Issue 1: Payment Collection Amount Mismatch - FIXED ✅**
- **Problem**: `createPaymentCollectionForCartWorkflow` always sets `payment_collection.amount = cart.total`
- **Root Cause**: Medusa workflow designed to create payment collections for full cart amount
- **Solution**: Create payment collection with workflow, then update amount to advance amount
- **Result**: `payment_collection.amount` now correctly equals `advance_amount` (not cart total)

### **Issue 2: Missing Payment Collection ID Validation - FIXED ✅**
- **Problem**: Record-payment API assumed one payment collection per cart
- **Root Cause**: No explicit payment collection reference in API
- **Solution**: Added required `payment_collection_id` parameter to record-payment API
- **Result**: Proper Medusa entity linking and validation

### **Issue 3: Cart Payment Collection Relationship - FIXED ✅**
- **Problem**: Manual payment collection creation didn't link to cart properly
- **Root Cause**: Bypassing Medusa workflow broke cart-payment collection relationship
- **Solution**: Use workflow for relationship creation, then update amount
- **Result**: Proper `cart_payment_collection` table relationships maintained

## Architecture

### Three-Step Workflow

1. **Advance Payment Initiation** (`POST /store/carts/{id}/advance-payment`)
2. **Payment Recording** (`POST /store/carts/{id}/record-payment`)
3. **Cart Completion** (`POST /store/carts/{id}/complete-with-advance`)

### Key Features

- ✅ **Medusa v2 Compliance**: Uses only Medusa workflows and core APIs
- ✅ **Dual Payment Modes**: Supports both manual and Stripe payment collection (both use Stripe provider for session management)
- ✅ **Proper Entity Management**: Correctly populates all Medusa payment entities
- ✅ **Event-Driven Integration**: Emits `order.placed` events for concierge synchronization
- ✅ **Partial Payment Tracking**: Maintains remaining balance for future collection
- ✅ **Sales Team Workflow**: Designed for quote-to-order conversion processes

## API Endpoints

### 1. Advance Payment Initiation

**Endpoint**: `POST /store/carts/{id}/advance-payment`

**Purpose**: Initiate advance payment collection without completing the cart.

**Request Body**:
```json
{
  "payment_mode": "manual" | "stripe",
  "advance_amount": 20000,
  "currency_code": "GBP",
  "metadata": {},
  "stripe_options": {
    "return_url": "https://example.com/success",
    "automatic_payment_methods": true
  }
}
```

**Response (Manual Mode)**:
```json
{
  "success": true,
  "payment_collection_id": "paycol_123",
  "cart_id": "cart_123",
  "payment_mode": "manual",
  "advance_amount": 20000,
  "remaining_amount": 80000,
  "currency_code": "GBP",
  "status": "initiated",
  "manual": {
    "collection_method": "manual",
    "status": "awaiting_payment",
    "next_action": "record_manual_payment"
  },
  "next_steps": [
    "Collect payment manually (cash, bank transfer, etc.)",
    "Record payment details using POST /store/carts/{id}/record-payment",
    "Finally call POST /store/carts/{id}/complete-with-advance to create order"
  ]
}
```

**Response (Stripe Mode)**:
```json
{
  "success": true,
  "payment_collection_id": "paycol_123",
  "cart_id": "cart_123",
  "payment_mode": "stripe",
  "advance_amount": 20000,
  "remaining_amount": 80000,
  "currency_code": "GBP",
  "status": "initiated",
  "stripe": {
    "payment_session_id": "ps_123",
    "client_secret": "pi_123_secret_456",
    "status": "requires_payment_method",
    "next_action": "complete_stripe_payment"
  },
  "next_steps": [
    "Use the client_secret to complete Stripe payment on frontend",
    "After successful payment, call POST /store/carts/{id}/record-payment",
    "Finally call POST /store/carts/{id}/complete-with-advance to create order"
  ]
}
```

### 2. Payment Recording

**Endpoint**: `POST /store/carts/{id}/record-payment`

**Purpose**: Record payment completion for both manual and Stripe payments.

**Request Body (Manual)**:
```json
{
  "payment_mode": "manual",
  "payment_collection_id": "paycol_123",
  "manual_payment": {
    "payment_method": "bank_transfer",
    "reference_number": "TXN_123456",
    "collected_by": "sales_team",
    "collection_date": "2024-01-15T10:30:00Z",
    "notes": "Customer payment via bank transfer",
    "amount_received": 20000
  },
  "metadata": {}
}
```

**Request Body (Stripe)**:
```json
{
  "payment_mode": "stripe",
  "payment_collection_id": "paycol_123",
  "stripe_payment": {
    "payment_session_id": "ps_123",
    "payment_intent_id": "pi_123",
    "stripe_payment_method_id": "pm_123"
  },
  "metadata": {}
}
```

**Response**:
```json
{
  "success": true,
  "payment_collection_id": "paycol_123",
  "cart_id": "cart_123",
  "payment_mode": "manual",
  "payment_session_id": "ps_123",
  "transaction_data": {
    "type": "manual",
    "payment_method": "bank_transfer",
    "reference_number": "TXN_123456",
    "collected_by": "sales_team",
    "amount_received": 20000,
    "currency_code": "GBP"
  },
  "status": "recorded",
  "advance_amount": 20000,
  "remaining_amount": 80000,
  "currency_code": "GBP",
  "next_steps": [
    "Payment has been recorded and authorized",
    "Cart is ready for completion",
    "Call POST /store/carts/{id}/complete-with-advance to create order"
  ]
}
```

### 3. Cart Completion with Advance

**Endpoint**: `POST /store/carts/{id}/complete-with-advance`

**Purpose**: Convert cart to order after advance payment confirmation.

**Request Body**:
```json
{
  "payment_collection_id": "paycol_123",
  "metadata": {},
  "order_metadata": {
    "sales_rep": "john_doe",
    "quote_number": "Q-2024-001"
  }
}
```

**Response**:
```json
{
  "success": true,
  "order": {
    "id": "order_123",
    "display_id": 1001,
    "total": 100000,
    "currency_code": "GBP",
    "status": "pending",
    "customer_id": "cus_123",
    "email": "<EMAIL>",
    "created_at": "2024-01-15T10:30:00Z"
  },
  "advance_payment_info": {
    "payment_collection_id": "paycol_123",
    "payment_session_id": "ps_123",
    "payment_mode": "manual",
    "advance_amount": 20000,
    "remaining_amount": 80000,
    "total_amount": 100000,
    "currency_code": "GBP",
    "payment_status": "partial",
    "requires_additional_payment": true,
    "transaction_data": {...}
  },
  "cart_id": "cart_123",
  "workflow_status": "completed",
  "message": "Order created successfully with advance payment of 20000 GBP. Remaining balance: 80000 GBP.",
  "next_steps": [
    "Order is confirmed and created",
    "Advance payment of 20000 GBP has been received",
    "Remaining balance: 80000 GBP",
    "Sales team should collect remaining payment",
    "Use admin API to mark remaining payment as collected when received"
  ],
  "admin_actions": {
    "mark_payment_collected_url": "/admin/orders/order_123/mark-payment-collected",
    "payment_status_url": "/admin/orders/order_123/payment-status",
    "remaining_amount": 80000,
    "currency_code": "GBP"
  }
}
```

## Medusa Entity Population

### Payment Collection
- **Status**: Properly tracked through workflow states
- **Metadata**: Contains advance payment tracking information
- **Amount**: Set to advance amount, not full cart total

### Payment Session
- **Manual Mode**: Created with `pp_manual_manual` provider
- **Stripe Mode**: Created with `pp_stripe_stripe` provider
- **Authorization**: Properly authorized through `authorizePaymentSessionStep`

### Order Metadata
```json
{
  "advance_payment_tracking": {
    "payment_collection_id": "paycol_123",
    "payment_session_id": "ps_123",
    "advance_amount": 20000,
    "remaining_amount": 80000,
    "total_amount": 100000,
    "currency_code": "GBP",
    "payment_mode": "manual",
    "advance_payment_status": "completed",
    "requires_additional_payment": true,
    "transaction_data": {...},
    "completed_at": "2024-01-15T10:30:00Z",
    "created_via": "advance_payment_workflow"
  },
  "payment_summary": {
    "total_order_amount": 100000,
    "advance_paid": 20000,
    "remaining_balance": 80000,
    "currency": "GBP",
    "payment_status": "partial"
  }
}
```

## Event Integration

### Order Placed Event
```json
{
  "name": "order.placed",
  "data": {
    "id": "order_123",
    "order_id": "order_123",
    "customer_id": "cus_123",
    "email": "<EMAIL>",
    "cart_id": "cart_123",
    "total": 100000,
    "currency_code": "GBP",
    "payment_status": "partial",
    "advance_payment": {
      "amount": 20000,
      "remaining": 80000,
      "payment_mode": "manual"
    },
    "conversion_method": "advance_payment_workflow",
    "advance_payment_workflow": true
  }
}
```

## Error Handling

### Common Error Scenarios

1. **Cart Not Found**: Returns 404 with clear message
2. **Payment Collection Already Exists**: Returns 400 with existing collection info
3. **Advance Amount Exceeds Cart Total**: Returns 400 with validation error
4. **Payment Not Recorded**: Returns 400 when trying to complete without recording payment
5. **Stripe Payment Session Errors**: Returns 400 with Stripe-specific error details

### Error Response Format
```json
{
  "message": "Error description",
  "type": "invalid_data",
  "errors": [...] // For validation errors
}
```

## Testing

Use the provided test script to validate the implementation:

```bash
# Set environment variables
export MEDUSA_BACKEND_URL=http://localhost:9000
export TEST_CART_ID=cart_01234567890

# Run tests
node test-advance-payment-workflow.js
```

## Usage Examples

### Manual Payment Workflow Example

```javascript
// Step 1: Initiate advance payment
const initiateResponse = await fetch('/store/carts/cart_123/advance-payment', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    payment_mode: "manual",
    advance_amount: 20000, // £200
    metadata: { sales_rep: "john_doe" }
  })
});

// Step 2: Record manual payment
const recordResponse = await fetch('/store/carts/cart_123/record-payment', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    payment_mode: "manual",
    manual_payment: {
      payment_method: "bank_transfer",
      reference_number: "TXN_123456",
      collected_by: "sales_team",
      amount_received: 20000,
      notes: "Customer advance payment"
    }
  })
});

// Step 3: Complete cart
const completeResponse = await fetch('/store/carts/cart_123/complete-with-advance', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    order_metadata: { quote_number: "Q-2024-001" }
  })
});
```

### Stripe Payment Workflow Example

```javascript
// Step 1: Initiate Stripe payment
const initiateResponse = await fetch('/store/carts/cart_123/advance-payment', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    payment_mode: "stripe",
    advance_amount: 20000,
    stripe_options: {
      return_url: "https://mystore.com/payment-success",
      automatic_payment_methods: true
    }
  })
});

const { client_secret, payment_session_id } = initiateResponse.data.stripe;

// Frontend: Complete Stripe payment using client_secret
// ... Stripe Elements integration ...

// Step 2: Record Stripe payment completion
const recordResponse = await fetch('/store/carts/cart_123/record-payment', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    payment_mode: "stripe",
    stripe_payment: {
      payment_session_id: payment_session_id,
      payment_intent_id: "pi_123", // From Stripe
      stripe_payment_method_id: "pm_123" // From Stripe
    }
  })
});

// Step 3: Complete cart
const completeResponse = await fetch('/store/carts/cart_123/complete-with-advance', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({})
});
```

## Integration with Existing Systems

### Concierge Order Synchronization
- Emits `order.placed` events that trigger concierge order creation
- Maintains compatibility with existing event subscribers
- Preserves all order metadata for concierge workflow

### Sales Team Workflow
- Designed for quote-to-order conversion processes
- Supports manual payment collection workflows
- Provides clear next steps and admin actions

### Admin API Integration
- Orders contain all necessary metadata for admin operations
- Remaining payment collection can be handled through admin APIs
- Full audit trail maintained through transaction data

## Migration from Existing Routes

### Replacing `convert-to-order`
The new workflow replaces the direct cart conversion approach:

**Before**:
```javascript
POST /store/carts/{id}/convert-to-order
{
  "payment_type": "deposit",
  "partial_amount": 20000
}
```

**After**:
```javascript
// Step 1
POST /store/carts/{id}/advance-payment
{ "payment_mode": "manual", "advance_amount": 20000 }

// Step 2
POST /store/carts/{id}/record-payment
{ "payment_mode": "manual", "manual_payment": {...} }

// Step 3
POST /store/carts/{id}/complete-with-advance
{}
```

### Benefits of New Approach
- ✅ Proper Medusa entity management
- ✅ Clear separation of concerns
- ✅ Better error handling and validation
- ✅ Support for both manual and Stripe payments
- ✅ Comprehensive audit trail
- ✅ Event-driven integration
