# Partial Payment Flow Guide - Cart Completion with Deposits

## Problem You Were Facing

```json
{
  "type": "invalid_data",
  "message": "Failed to complete cart: Payment collection has not been initiated for cart"
}
```

**Why this happens**: Medusa requires a payment collection to be initiated before cart completion to ensure payment intent exists.

## Your Use Case: Partial Payment + Deferred Collection

1. **Customer pays deposit** (e.g., 20% of total)
2. **<PERSON>t completes** → Order created
3. **Concierge team** collects remaining payment later

## Solution: Two-Step Payment Flow

### **Step 1: Create Payment Collection (Optional)**
```bash
POST /store/carts/{cart_id}/payment-collection
```

**Request Body:**
```json
{
  "payment_type": "deposit",
  "partial_amount": 20000,
  "payment_provider_id": "pp_manual_manual",
  "metadata": {
    "deposit_percentage": 20,
    "collection_method": "concierge"
  }
}
```

**Payment Types:**
- `"deposit"` - Partial payment (default 20% or custom amount)
- `"full"` - Full payment upfront
- `"manual"` - Minimal payment for manual processing

### **Step 2: Complete Cart with Payment**
```bash
POST /store/carts/{cart_id}/complete-with-payment
```

**Request Body:**
```json
{
  "payment_type": "deposit",
  "partial_amount": 20000,
  "payment_provider_id": "pp_manual_manual"
}
```

## Complete Flow Example

### **Scenario: £980 Hotel Booking with £200 Deposit**

#### 1. Create Cart (Already Done)
```json
{
  "region_id": "reg_01JXJ7K6W55Y74WAQ3QQG1WFDA",
  "currency_code": "GBP",
  "email": "<EMAIL>",
  "items": [
    {
      "variant_id": "variant_room_101",
      "quantity": 2,
      "unit_price": 49000
    }
  ]
}
```
**Cart Total: £980 (98000 pence)**

#### 2. Complete Cart with Deposit
```bash
POST /store/carts/cart_123/complete-with-payment
```

```json
{
  "payment_type": "deposit",
  "partial_amount": 20000,
  "payment_provider_id": "pp_manual_manual",
  "metadata": {
    "deposit_percentage": 20.4,
    "collection_method": "concierge_team"
  }
}
```

#### 3. Response
```json
{
  "success": true,
  "order": {
    "id": "order_456",
    "total": 98000,
    "currency_code": "GBP",
    "status": "pending"
  },
  "payment_info": {
    "payment_type": "deposit",
    "paid_amount": 20000,
    "remaining_amount": 78000,
    "total_amount": 98000,
    "currency_code": "GBP",
    "requires_additional_payment": true
  },
  "message": "Order created with deposit payment. Remaining £780 to be collected by concierge team.",
  "next_steps": [
    "Order is confirmed and created",
    "Customer has paid £200 deposit",
    "Concierge team should collect remaining payment",
    "Remaining amount: £780"
  ]
}
```

## Payment Types Explained

### **1. Deposit Payment (`"deposit"`)**
- **Use case**: Customer pays partial amount upfront
- **Default**: 20% of cart total
- **Custom**: Specify `partial_amount` in cents
- **Example**: £200 deposit on £1000 booking

```json
{
  "payment_type": "deposit",
  "partial_amount": 20000
}
```

### **2. Full Payment (`"full"`)**
- **Use case**: Customer pays entire amount upfront
- **Amount**: Full cart total
- **Example**: £1000 full payment

```json
{
  "payment_type": "full"
}
```

### **3. Manual Payment (`"manual"`)**
- **Use case**: Concierge team handles all payment collection
- **Amount**: Minimal (e.g., £1) to satisfy Medusa requirements
- **Example**: Quote approval without immediate payment

```json
{
  "payment_type": "manual",
  "partial_amount": 100
}
```

## Order Metadata Tracking

After completion, the order includes payment tracking metadata:

```json
{
  "metadata": {
    "payment_tracking": {
      "payment_type": "deposit",
      "paid_amount": 20000,
      "remaining_amount": 78000,
      "total_amount": 98000,
      "currency_code": "GBP",
      "requires_additional_payment": true,
      "payment_collection_id": "paycol_123",
      "payment_session_id": "ps_456",
      "completed_at": "2024-01-15T10:30:00Z"
    },
    "concierge_notes": "Remaining payment of 78000 GBP to be collected by concierge team"
  }
}
```

## Concierge Team Workflow

### **1. Order Created with Deposit**
- Order status: `pending`
- Deposit collected: ✅
- Remaining amount: Tracked in metadata

### **2. Concierge Collection Process**
1. **View order** with payment tracking info
2. **Contact customer** for remaining payment
3. **Process payment** through preferred method
4. **Update order** status to `completed`
5. **Mark payment** as fully collected

### **3. Payment Collection API (Future)**
```bash
POST /admin/orders/{order_id}/collect-payment
```

```json
{
  "amount": 78000,
  "payment_method": "card",
  "collected_by": "concierge_user_123",
  "notes": "Collected remaining payment via phone"
}
```

## Benefits of This Approach

### **✅ For Customers**
- **Lower barrier**: Only pay deposit upfront
- **Flexibility**: Complete payment later
- **Confirmation**: Order created immediately

### **✅ For Business**
- **Cash flow**: Immediate deposit collection
- **Flexibility**: Handle complex payment scenarios
- **Tracking**: Full payment history and status

### **✅ For Concierge Team**
- **Clear tracking**: Know exactly what's owed
- **Workflow integration**: Part of order management
- **Customer service**: Handle payment issues gracefully

## Error Handling

### **Common Issues & Solutions**

#### 1. "Payment collection already exists"
```json
{
  "message": "Payment collection already exists",
  "payment_collection": { "id": "paycol_123" }
}
```
**Solution**: Use existing collection or complete cart directly

#### 2. "Failed to create payment session"
```json
{
  "message": "Failed to create payment session"
}
```
**Solution**: Check payment provider configuration

#### 3. "Cart not found"
```json
{
  "message": "Cart not found"
}
```
**Solution**: Verify cart ID and ensure cart exists

## Testing Your Implementation

### **Test Script**
```bash
# 1. Create cart (already working)
curl -X POST http://localhost:9000/store/carts \
  -H "Content-Type: application/json" \
  -d '{"region_id": "reg_123", "currency_code": "GBP", "email": "<EMAIL>"}'

# 2. Complete with deposit
curl -X POST http://localhost:9000/store/carts/cart_123/complete-with-payment \
  -H "Content-Type: application/json" \
  -d '{"payment_type": "deposit", "partial_amount": 20000}'
```

### **Expected Flow**
1. ✅ Payment collection created
2. ✅ Payment session authorized
3. ✅ Cart completed → Order created
4. ✅ Payment tracking metadata added
5. ✅ Concierge team can see remaining amount

## Next Steps

1. **Test the new endpoint** with your cart
2. **Integrate with frontend** for deposit collection
3. **Build concierge dashboard** to track remaining payments
4. **Add payment collection API** for final payment processing

Your cart completion should now work with partial payments! 🎉
