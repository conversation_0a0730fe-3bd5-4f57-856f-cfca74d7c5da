# Cart Implementation Guide - Medusa.js Standards

## Overview
This guide documents the cart implementation in medusa-store-v2 and provides recommendations for aligning with Medusa.js standards.

## Current Implementation

### Cart API Endpoints

1. **Standard Store Cart**: `POST /store/carts`
   - Used for general e-commerce cart creation
   - Supports custom pricing for quotes
   - Handles line items with product variants

2. **Hotel-Specific Cart**: `POST /store/hotel-management/cart`
   - Specialized for hotel booking workflows
   - Includes room availability checking
   - Handles complex add-on services
   - Manages guest information and travelers

3. **Cart Completion**: `POST /store/carts/{id}/complete`
   - Converts cart to order using `completeCartWorkflow`
   - Supports subscription creation
   - Triggers concierge order synchronization

## Product Structure

### Hotel Products Architecture
- **Room Types** = Products (e.g., "Deluxe Room", "Standard Room")
- **Individual Rooms** = Product Variants (e.g., "Room 101", "Room 102")
- **Add-on Services** = Separate Products with pricing variants
- **Service Categories** = Main products like `product_add_ons_main`

### Add-on Services Structure
- **Per-person pricing**: Adult/child variants
- **Package pricing**: Single package variant
- **Usage-based pricing**: Daily rate variants with guest usage tracking

## Required Parameters (Medusa.js Standards)

### Cart Creation (Official Medusa Standards)
```typescript
{
  region_id?: string,       // Optional - Uses default region if not provided
  currency_code?: string,   // Optional - Uses region's currency if not provided
  email?: string,          // Optional - Used to associate with customer (NO customer_id!)
  sales_channel_id?: string, // Optional - For multi-channel support
  promo_codes?: string[],   // Optional - Promotion codes to apply
  shipping_address?: Address,
  billing_address?: Address,
  items?: LineItem[],
  metadata?: object
}
```

### Line Item Structure
```typescript
{
  variant_id: string,       // REQUIRED - Product variant ID
  quantity: number,         // REQUIRED - Item quantity
  unit_price?: number,      // Optional - Custom pricing for quotes
  title?: string,          // Optional - Custom item title
  metadata?: object,       // Optional - Custom item data
  requires_shipping?: boolean
}
```

## Recommendations

### 1. Region and Currency Management
- **Set up default regions** for your markets (e.g., Switzerland, Europe)
- **Configure currencies** for each region (CHF, EUR, USD)
- **Create region API endpoint** to help CRM select appropriate region
- **Validate region/currency combinations** in cart creation

### 2. Sales Channel Integration
- **Create sales channels** for different booking sources (CRM, Website, Mobile)
- **Scope products** to appropriate sales channels
- **Use publishable API keys** for channel-specific access

### 3. Shipping Method Integration
- **Create virtual shipping methods** for hotel bookings
- **Handle "digital delivery"** for confirmation emails
- **Set up shipping options** for physical items (welcome packages, etc.)

### 4. Tax Integration
- **Configure tax providers** for different regions
- **Apply tax lines** to cart items based on location
- **Handle tax-exempt** scenarios for certain services

### 5. Enhanced Quote Management
- **Use cart metadata** to distinguish quotes from regular orders
- **Implement quote expiration** using cart metadata
- **Add quote approval workflow** before cart completion

## Implementation Steps

### Phase 1: Core Standards Compliance
1. ✅ Fix cart schema to require region_id and currency_code
2. Set up default regions and currencies
3. Create region selection API for CRM
4. Update cart creation to validate region/currency

### Phase 2: Enhanced Integration
1. Implement sales channel scoping
2. Add shipping method integration
3. Configure tax calculation
4. Enhance quote management workflow

### Phase 3: Advanced Features
1. Add cart abandonment tracking
2. Implement cart sharing for quotes
3. Add cart versioning for quote revisions
4. Integrate with external pricing systems

## Testing Recommendations

### Unit Tests
- Cart creation with required parameters
- Line item addition/removal
- Custom pricing validation
- Address validation

### Integration Tests
- End-to-end cart to order flow
- Payment integration
- Concierge order synchronization
- Subscription creation

### Performance Tests
- Cart creation under load
- Complex add-on calculations
- Room availability checking
- Large cart handling

## Best Practices

1. **Always use Medusa workflows** instead of direct service calls
2. **Validate all inputs** using Zod schemas
3. **Handle errors gracefully** with proper error messages
4. **Use metadata extensively** for custom business logic
5. **Maintain backward compatibility** when updating schemas
6. **Log important events** for debugging and monitoring
7. **Test cart completion flow** thoroughly before deployment

## Common Pitfalls to Avoid

1. **Don't bypass workflows** - Always use `createCartWorkflow` and `completeCartWorkflow`
2. **Don't hardcode currencies** - Use region-based currency selection
3. **Don't ignore sales channels** - Properly scope products and carts
4. **Don't skip validation** - Always validate inputs before processing
5. **Don't forget metadata** - Use it for tracking custom business data
