# Concierge Order Module - Setup Guide

## Quick Start

### 1. Database Migration

Run the database migration to create the new tables:

```bash
# Run the migration
npm run build
npx medusa migrations run

# Or if using yarn
yarn build
yarn medusa migrations run
```

### 2. Environment Configuration

Add the following environment variable to your `.env` file:

```bash
# Threshold for automatic concierge order creation (in cents)
# Default: 50000 (500 CHF)
CONCIERGE_HIGH_VALUE_THRESHOLD=50000
```

### 3. Verify Installation

Check that the services are properly registered:

```bash
# Start the development server
npm run dev

# Check the logs for successful registration:
# ✅ Concierge Order Service registered
# ✅ Concierge Order Item Service registered
```

### 4. Test the API

Import the Postman collection from `postman/concierge-orders-api.postman_collection.json` and:

1. Set your `BASE_URL` (default: http://localhost:9000)
2. Set your `ADMIN_TOKEN` (get from admin login)
3. Set a valid `order_id` from your system
4. Run the collection tests

### 5. Verify Automatic Creation

Create a test order that meets the criteria:

```bash
# Example: Create an order with high value
curl -X POST "http://localhost:9000/store/carts/cart_123/complete" \
  -H "Content-Type: application/json"

# Check if concierge order was created automatically
curl -X GET "http://localhost:9000/admin/concierge-management/orders" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

## Troubleshooting

### Common Issues

1. **Migration Fails**
   ```bash
   # Check if tables already exist
   psql -d your_database -c "\dt concierge*"
   
   # If they exist, you may need to drop them first
   # BE CAREFUL - This will delete data!
   psql -d your_database -c "DROP TABLE IF EXISTS concierge_order_item, concierge_order CASCADE;"
   ```

2. **Service Not Registered**
   - Check that the loader is included in `src/loaders/index.ts`
   - Verify no syntax errors in the service files
   - Check server logs for registration messages

3. **API Routes Not Working**
   - Ensure proper authentication token
   - Check that the admin user has proper permissions
   - Verify the API routes are in the correct directory structure

4. **Automatic Creation Not Working**
   - Check the subscriber is loaded properly
   - Verify the order meets the criteria (high value, special requests, etc.)
   - Check server logs for subscriber execution

### Debug Commands

```bash
# Check database tables
psql -d your_database -c "\d concierge_order"
psql -d your_database -c "\d concierge_order_item"

# Check for any concierge orders
psql -d your_database -c "SELECT * FROM concierge_order LIMIT 5;"

# Check server logs
tail -f logs/medusa.log | grep -i concierge
```

## Testing

### Unit Tests

```bash
# Run the concierge order tests
npm test src/modules/concierge-management/__tests__/

# Run all tests
npm test
```

### Integration Tests

Use the Postman collection for comprehensive API testing:

1. **Create Order Flow**:
   - Create concierge order
   - Add items
   - Update status
   - Complete order

2. **Error Handling**:
   - Invalid data
   - Missing authentication
   - Non-existent resources

### Load Testing

For production environments, test with realistic data:

```bash
# Create multiple test orders
for i in {1..10}; do
  curl -X POST "http://localhost:9000/admin/concierge-management/orders" \
    -H "Authorization: Bearer YOUR_TOKEN" \
    -H "Content-Type: application/json" \
    -d "{\"order_id\": \"test_order_$i\"}"
done
```

## Production Deployment

### Pre-deployment Checklist

- [ ] Database migration tested in staging
- [ ] Environment variables configured
- [ ] API authentication working
- [ ] Automatic order creation tested
- [ ] Monitoring and logging configured
- [ ] Backup procedures in place

### Monitoring

Set up monitoring for:

- Concierge order creation rate
- API response times
- Error rates
- Database performance
- Subscriber execution

### Backup

Ensure your backup strategy includes the new tables:

```sql
-- Include in your backup scripts
pg_dump -t concierge_order -t concierge_order_item your_database
```

## Support

For issues or questions:

1. Check the logs for error messages
2. Verify the setup steps were followed correctly
3. Test with the provided Postman collection
4. Review the API documentation in `docs/concierge-order-management.md`
