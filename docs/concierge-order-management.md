# Concierge Order & Item Management Module

## Overview

The Concierge Order & Item Management Module extends Medusa's order lifecycle to enable concierge-driven workflows. This module allows for specialized handling of orders that require manual intervention, approvals, notes, and custom item management.

## Features

- **Concierge Order Management**: Create and manage orders that require concierge attention
- **Item-level Control**: Add, modify, and track individual items within concierge orders
- **Status Tracking**: Comprehensive status management for both orders and items
- **Assignment System**: Assign orders to specific concierge team members
- **Automated Creation**: Automatically create concierge orders based on configurable criteria
- **Workflow Integration**: Built-in workflows for consistent order processing
- **API-First Design**: Complete REST API for integration with external systems

## Architecture

### Models

#### ConciergeOrder
- **Purpose**: Represents orders requiring concierge intervention
- **Key Fields**:
  - `order_id`: Reference to the main Medusa order
  - `assigned_to`: User ID of assigned concierge
  - `status`: Current processing status
  - `notes`: Internal concierge notes
  - `last_contacted_at`: Customer communication tracking

#### ConciergeOrderItem
- **Purpose**: Individual items within concierge orders
- **Key Fields**:
  - `concierge_order_id`: Parent concierge order
  - `line_item_id`: Optional reference to existing line item
  - `title`, `quantity`, `unit_price`: Item details
  - `status`: Item-specific status
  - `is_active`: Soft deletion flag

### Services

#### ConciergeOrderService
- Order CRUD operations
- Status management
- Assignment handling
- Pagination and filtering

#### ConciergeOrderItemService
- Item CRUD operations
- Status tracking
- Bulk operations
- Activity management

### Workflows

#### CreateConciergeOrderWorkflow
- Input validation
- Duplicate checking
- Order creation with rollback

#### UpdateConciergeOrderStatusWorkflow
- Status validation
- Update with compensation
- Audit trail

#### CreateConciergeOrderItemWorkflow
- Parent order verification
- Item creation
- Error handling

## API Endpoints

### Concierge Orders

| Method | Endpoint | Description |
|--------|----------|-------------|
| GET | `/admin/concierge-management/orders` | List concierge orders |
| POST | `/admin/concierge-management/orders` | Create concierge order |
| GET | `/admin/concierge-management/orders/{id}` | Get specific order |
| PATCH | `/admin/concierge-management/orders/{id}` | Update order |
| DELETE | `/admin/concierge-management/orders/{id}` | Delete order |

### Concierge Order Items

| Method | Endpoint | Description |
|--------|----------|-------------|
| GET | `/admin/concierge-management/orders/{id}/items` | List order items |
| POST | `/admin/concierge-management/orders/{id}/items` | Create order item |
| GET | `/admin/concierge-management/orders/{id}/items/{item_id}` | Get specific item |
| PATCH | `/admin/concierge-management/orders/{id}/items/{item_id}` | Update item |
| DELETE | `/admin/concierge-management/orders/{id}/items/{item_id}` | Deactivate item |

## Status Enums

### ConciergeOrderStatus
- `not_started`: Order created but not yet assigned
- `in_progress`: Actively being worked on
- `waiting_customer`: Waiting for customer response
- `ready_to_finalize`: Ready for completion
- `completed`: Order fully processed

### ConciergeOrderItemStatus
- `under_review`: Concierge team is working with client for confirmation/rejection
- `client_confirmed`: Client confirmed, ready for supplier team action (primary focus)
- `order_placed`: Order has been placed with supplier
- `cancelled`: No longer needed by client or cannot be procured
- `completed`: Item has been delivered/fulfilled

## Automatic Order Creation

The system automatically creates concierge orders when orders meet specific criteria:

### Criteria
1. **High-value orders**: Above configurable threshold
2. **Special requests**: Orders with custom requirements
3. **Large bookings**: Multiple rooms or guests
4. **Multiple add-ons**: Complex service requests
5. **Long stays**: Extended duration bookings
6. **VIP customers**: Premium customer tier
7. **Custom pricing**: Modified pricing
8. **Manual verification**: Flagged for review

### Configuration
Set environment variable `CONCIERGE_HIGH_VALUE_THRESHOLD` (default: 50000 cents = 500 CHF)

## Usage Examples

### Creating a Concierge Order

```bash
curl -X POST "http://localhost:9000/admin/concierge-management/orders" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "order_id": "order_123",
    "assigned_to": "user_456",
    "notes": "High-value order requiring special attention",
    "status": "not_started"
  }'
```

### Adding an Item

```bash
curl -X POST "http://localhost:9000/admin/concierge-management/orders/corder_123/items" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "title": "Room Upgrade",
    "quantity": 1,
    "unit_price": 15000,
    "status": "under_review"
  }'
```

### Updating Status

```bash
curl -X PATCH "http://localhost:9000/admin/concierge-management/orders/corder_123" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "status": "in_progress",
    "notes": "Started processing customer requests"
  }'
```

## Testing

### Unit Tests
Run the test suite:
```bash
npm test src/modules/concierge-management/__tests__/
```

### Postman Collection
Import the collection from `postman/concierge-orders-api.postman_collection.json`

### Environment Variables
- `BASE_URL`: API base URL (default: http://localhost:9000)
- `ADMIN_TOKEN`: Admin authentication token
- `order_id`: Test order ID

## Integration

### Event Subscribers
The module includes an event subscriber that automatically creates concierge orders when regular orders are placed and meet the criteria.

### Workflow Integration
All major operations use Medusa v2 workflows for consistency and error handling.

### Service Registration
Services are automatically registered in the dependency injection container via the concierge management module loader.

## Error Handling

All API endpoints include comprehensive error handling:
- Input validation with Zod schemas
- Proper HTTP status codes
- Detailed error messages
- Rollback mechanisms in workflows

## Security

- Bearer token authentication required
- User context tracking for audit trails
- Soft deletion for data integrity
- Input sanitization and validation
