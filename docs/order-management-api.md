# Order Management API Documentation

This document describes the comprehensive Order Management API implementation that provides full CRUD operations for orders using Medusa workflows and following Medusa's standard patterns.

## Overview

The Order Management API provides the following functionality:
- **CREATE**: Order creation API endpoint
- **READ**: Order retrieval API endpoints (single order and list orders)
- **UPDATE**: Order update API endpoint
- **DELETE**: Order deletion API endpoint (for draft orders)
- **Order Items Management**: Add items to existing orders with support for both draft and confirmed orders

## API Endpoints

### 1. Create Order
**POST** `/admin/orders`

Creates a new order using Medusa's standard workflows.

#### Request Body
```json
{
  "region_id": "string (optional)",
  "customer_id": "string (optional)",
  "sales_channel_id": "string (optional)",
  "email": "string (optional)",
  "currency_code": "string (default: USD)",
  "is_draft_order": "boolean (default: true)",
  "status": "pending|completed|archived|canceled|requires_action (optional)",
  "items": [
    {
      "variant_id": "string (required)",
      "product_id": "string (optional)",
      "title": "string (required)",
      "subtitle": "string (optional)",
      "thumbnail": "string (optional)",
      "quantity": "number (required, min: 1)",
      "unit_price": "number (required, min: 0)",
      "is_tax_inclusive": "boolean (default: false)",
      "compare_at_unit_price": "number (optional)",
      "metadata": "object (optional)"
    }
  ],
  "shipping_address": {
    "first_name": "string (optional)",
    "last_name": "string (optional)",
    "address_1": "string (optional)",
    "address_2": "string (optional)",
    "city": "string (optional)",
    "country_code": "string (optional)",
    "province": "string (optional)",
    "postal_code": "string (optional)",
    "phone": "string (optional)",
    "company": "string (optional)"
  },
  "billing_address": {
    "first_name": "string (optional)",
    "last_name": "string (optional)",
    "address_1": "string (optional)",
    "address_2": "string (optional)",
    "city": "string (optional)",
    "country_code": "string (optional)",
    "province": "string (optional)",
    "postal_code": "string (optional)",
    "phone": "string (optional)",
    "company": "string (optional)"
  },
  "metadata": "object (optional)"
}
```

#### Response
```json
{
  "order": {
    "id": "order_123",
    "status": "pending",
    "email": "<EMAIL>",
    "currency_code": "USD",
    "is_draft_order": true,
    "created_at": "2024-01-01T00:00:00Z",
    "updated_at": "2024-01-01T00:00:00Z",
    "metadata": {}
  }
}
```

### 2. List Orders
**GET** `/admin/orders`

Retrieves a list of orders with filtering and pagination.

#### Query Parameters
- `limit`: Number of orders to return (default: 20, max: 100)
- `offset`: Number of orders to skip (default: 0)
- `status`: Filter by order status
- `payment_status`: Filter by payment status
- `fulfillment_status`: Filter by fulfillment status
- `customer_id`: Filter by customer ID
- `email`: Filter by customer email (partial match)
- `region_id`: Filter by region ID
- `currency_code`: Filter by currency code
- `search`: Search in order display ID, email, or customer name
- `fields`: Comma-separated list of fields to include
- `expand`: Comma-separated list of relations to expand

#### Response
```json
{
  "orders": [
    {
      "id": "order_123",
      "status": "pending",
      "email": "<EMAIL>",
      "currency_code": "USD",
      "customer": {},
      "items": [],
      "created_at": "2024-01-01T00:00:00Z"
    }
  ],
  "count": 1,
  "limit": 20,
  "offset": 0
}
```

### 3. Get Order
**GET** `/admin/orders/{id}`

Retrieves a specific order with full details.

#### Response
```json
{
  "order": {
    "id": "order_123",
    "status": "pending",
    "email": "<EMAIL>",
    "currency_code": "USD",
    "customer": {},
    "items": [],
    "shipping_address": {},
    "billing_address": {},
    "payment_collections": [],
    "fulfillments": [],
    "created_at": "2024-01-01T00:00:00Z",
    "updated_at": "2024-01-01T00:00:00Z"
  }
}
```

### 4. Update Order
**PUT** `/admin/orders/{id}`

Updates order details.

#### Request Body
```json
{
  "status": "pending|completed|archived|canceled|requires_action (optional)",
  "email": "string (optional)",
  "shipping_address": {
    "first_name": "string (optional)",
    "last_name": "string (optional)",
    "address_1": "string (optional)",
    "address_2": "string (optional)",
    "city": "string (optional)",
    "country_code": "string (optional)",
    "province": "string (optional)",
    "postal_code": "string (optional)",
    "phone": "string (optional)",
    "company": "string (optional)"
  },
  "billing_address": {
    "first_name": "string (optional)",
    "last_name": "string (optional)",
    "address_1": "string (optional)",
    "address_2": "string (optional)",
    "city": "string (optional)",
    "country_code": "string (optional)",
    "province": "string (optional)",
    "postal_code": "string (optional)",
    "phone": "string (optional)",
    "company": "string (optional)"
  },
  "metadata": "object (optional)"
}
```

### 5. Delete Order
**DELETE** `/admin/orders/{id}`

Deletes an order (only draft orders or pending orders).

#### Response
```json
{
  "id": "order_123",
  "deleted": true
}
```

### 6. Add Order Items
**POST** `/admin/orders/{id}/items`

Adds order items and order line items to existing orders. Supports both draft and confirmed orders using appropriate Medusa workflows.

#### Request Body
```json
{
  "order_id": "string (optional, uses URL param if not provided)",
  "items": [
    {
      "variant_id": "string (required)",
      "quantity": "number (required, min: 1)",
      "title": "string (required)",
      "unit_price": "number (required, min: 0)",
      "metadata": "object (optional)"
    }
  ],
  "mode": "draft|confirmed (default: draft)"
}
```

#### Response
```json
{
  "order_id": "order_123",
  "items_added": [
    {
      "id": "item_123",
      "variant_id": "variant_123",
      "title": "Product Title",
      "quantity": 2,
      "unit_price": 1000
    }
  ],
  "mode": "draft",
  "message": "Successfully added 1 items to order"
}
```

### 7. Get Order Items
**GET** `/admin/orders/{id}/items`

Retrieves all items for a specific order.

#### Response
```json
{
  "order_id": "order_123",
  "items": [
    {
      "id": "item_123",
      "variant_id": "variant_123",
      "title": "Product Title",
      "quantity": 2,
      "unit_price": 1000,
      "variant": {},
      "tax_lines": [],
      "adjustments": []
    }
  ],
  "count": 1
}
```

## Workflows

The implementation uses the following workflows:

### 1. CreateOrderManagementWorkflow
- Wraps Medusa's `createOrderWorkflow`
- Provides validation and error handling
- Emits order creation events

### 2. AddOrderItemsWorkflow
- Supports both draft and confirmed orders
- Uses appropriate Medusa workflows based on order status
- For draft orders: Direct item addition
- For confirmed orders: Order edit workflow pattern

## Authentication & Authorization

All endpoints require:
- Valid authentication token
- Admin or Hotel Manager role permissions

## Error Handling

The API returns standard HTTP status codes:
- `200`: Success
- `201`: Created
- `400`: Bad Request (validation errors)
- `401`: Unauthorized
- `403`: Forbidden
- `404`: Not Found
- `500`: Internal Server Error

Error responses include:
```json
{
  "type": "error_type",
  "message": "Error description"
}
```

## Implementation Details

### File Structure
```
src/
├── api/admin/orders/
│   ├── route.ts                    # Main orders CRUD
│   ├── [id]/route.ts              # Individual order operations
│   ├── [id]/items/route.ts        # Order items management
│   ├── validators.ts              # Validation schemas
│   └── __tests__/                 # Test files
├── workflows/order-management/
│   ├── create-order.ts            # Order creation workflow
│   ├── add-order-items.ts         # Order items workflow
│   └── index.ts                   # Workflow exports
└── docs/
    └── order-management-api.md    # This documentation
```

### Key Features
- Full TypeScript support with comprehensive type definitions
- Zod validation schemas for all endpoints
- Comprehensive error handling and validation
- Event emission for order operations
- Support for both draft and confirmed order workflows
- Scalable architecture using Medusa's dependency injection
- Comprehensive test coverage

## Testing

Run tests with:
```bash
npm test src/api/admin/orders/__tests__/
```

The test suite covers:
- Order creation and validation
- Order listing with filters
- Order retrieval and updates
- Order deletion
- Order items management
- Error handling scenarios
