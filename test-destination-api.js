require('dotenv').config();

async function testDestinationAPI() {
  const baseURL = 'http://localhost:9000';
  
  // First, let's try to authenticate (you might need to adjust this based on your auth setup)
  console.log('Testing destination creation API...');
  
  const destinationData = {
    name: "Test Currency Margin Destination",
    handle: "test-currency-margin-destination-new",
    description: "A test destination with currency and margin fields",
    is_active: true,
    is_featured: false,
    country: "Switzerland",
    location: "Swiss Alps",
    tags: ["mountains", "skiing"],
    // New fields we added
    internal_web_link: "https://internal.example.com/destination",
    external_web_link: "https://external.example.com/destination",
    currency_code: "CHF",
    margin_percentage: 15.5,
    faqs: []
  };

  try {
    const response = await fetch(`${baseURL}/admin/hotel-management/destinations`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        // You might need to add authentication headers here
        // 'Authorization': 'Bearer your-token'
      },
      body: JSON.stringify(destinationData)
    });

    console.log('Response status:', response.status);
    console.log('Response headers:', Object.fromEntries(response.headers.entries()));

    if (response.ok) {
      const result = await response.json();
      console.log('✅ Destination created successfully!');
      console.log('Created destination:', JSON.stringify(result, null, 2));
      
      // Now let's verify the data was saved correctly
      console.log('\n=== VERIFYING DATABASE ===');
      await verifyDestinationInDB(result.destination.handle);
      
    } else {
      const errorText = await response.text();
      console.log('❌ Error creating destination:');
      console.log('Status:', response.status);
      console.log('Error:', errorText);
    }

  } catch (error) {
    console.error('❌ Network error:', error);
  }
}

async function verifyDestinationInDB(handle) {
  const { Client } = require('pg');
  const client = new Client({
    connectionString: process.env.DATABASE_URL,
  });

  try {
    await client.connect();
    
    const result = await client.query(`
      SELECT 
        id, 
        name, 
        handle, 
        internal_web_link, 
        external_web_link, 
        currency_code, 
        margin_percentage,
        created_at
      FROM destination 
      WHERE handle = $1
    `, [handle]);

    if (result.rows.length > 0) {
      const destination = result.rows[0];
      console.log('✅ Destination found in database:');
      console.log('ID:', destination.id);
      console.log('Name:', destination.name);
      console.log('Handle:', destination.handle);
      console.log('Internal Web Link:', destination.internal_web_link);
      console.log('External Web Link:', destination.external_web_link);
      console.log('Currency Code:', destination.currency_code);
      console.log('Margin Percentage:', destination.margin_percentage);
      console.log('Created At:', destination.created_at);
    } else {
      console.log('❌ Destination not found in database');
    }

  } catch (error) {
    console.error('Database verification error:', error);
  } finally {
    await client.end();
  }
}

testDestinationAPI();
