import {
  createStep,
  createWorkflow,
  StepResponse,
  WorkflowResponse,
  transform,
} from "@camped-ai/framework/workflows-sdk";
import { Modules } from "@camped-ai/framework/utils";
import {
  IUserModuleService,
  IAuthModuleService,
} from "@camped-ai/framework/types";
import { emitEventStep } from "@camped-ai/medusa/core-flows";
import crypto from "crypto";

export type CreateUserWorkflowInput = {
  email: string;
  first_name: string;
  last_name: string;
  rbac_metadata: any;
  additional_metadata?: any;
};

type CreateUserStepInput = CreateUserWorkflowInput;

// Step 1: Generate secure dummy password
const generateSecurePasswordStep = createStep(
  "generate-secure-password",
  async () => {
    // Generate a secure random password
    // 16 characters with uppercase, lowercase, numbers, and symbols
    const length = 16;
    const charset =
      "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*";
    let password = "";

    // Ensure at least one character from each category
    const lowercase = "abcdefghijklmnopqrstuvwxyz";
    const uppercase = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
    const numbers = "0123456789";
    const symbols = "!@#$%^&*";

    password += lowercase[crypto.randomInt(lowercase.length)];
    password += uppercase[crypto.randomInt(uppercase.length)];
    password += numbers[crypto.randomInt(numbers.length)];
    password += symbols[crypto.randomInt(symbols.length)];

    // Fill the rest randomly
    for (let i = 4; i < length; i++) {
      password += charset[crypto.randomInt(charset.length)];
    }

    // Shuffle the password to randomize character positions
    const shuffled = password
      .split("")
      .sort(() => Math.random() - 0.5)
      .join("");

    return new StepResponse({ password: shuffled });
  }
);

// Step 2: Create user with dummy password
const createUserStep = createStep(
  "create-user-with-password",
  async (input: CreateUserStepInput & { password: string }, { container }) => {
    const userService: IUserModuleService = container.resolve(Modules.USER);

    // Create the user with dummy password stored in metadata
    const userPayload = {
      email: input.email,
      first_name: input.first_name,
      last_name: input.last_name,
      metadata: {
        rbac: input.rbac_metadata,
        ...input.additional_metadata,
      },
    };

    const users = await userService.createUsers([userPayload]);

    const user = users[0];

    return new StepResponse(user, user.id);
  },
  async (userId: string, { container }) => {
    // Compensation: delete user if something goes wrong
    const userService: IUserModuleService = container.resolve(Modules.USER);

    try {
      // Delete user (no auth identity to clean up since we don't create it)
      await userService.deleteUsers([userId]);
    } catch (error) {
      // Silent compensation failure
    }
  }
);

// Step 3: Create auth identity and link to user (following Medusa CLI pattern)
const createAuthIdentityStep = createStep(
  "create-auth-identity",
  async (
    input: { email: string; password: string; userId: string },
    { container }
  ) => {
    const authService: IAuthModuleService = container.resolve(Modules.AUTH);

    try {
      // Step 1: Register auth identity (following Medusa CLI pattern)
      const registrationResult = await authService.register("emailpass", {
        body: {
          email: input.email,
          password: input.password,
        },
      });

      if (registrationResult.error) {
        throw new Error(
          `Auth registration failed: ${registrationResult.error}`
        );
      }

      const authIdentity = registrationResult.authIdentity;
      if (!authIdentity) {
        throw new Error("Auth identity not returned from registration");
      }

      // Step 2: Link auth identity to user (following Medusa CLI pattern)
      await authService.updateAuthIdentities({
        id: authIdentity.id,
        app_metadata: {
          user_id: input.userId,
        },
      });

      return new StepResponse(authIdentity, authIdentity.id);
    } catch (error) {
      throw error;
    }
  },
  async (authIdentityId: string, { container }) => {
    // Compensation: delete auth identity if something goes wrong
    const authService: IAuthModuleService = container.resolve(Modules.AUTH);
    try {
      await authService.deleteAuthIdentities([authIdentityId]);
    } catch (error) {
      // Silent compensation failure
    }
  }
);

export const CreateUserWorkflow = createWorkflow(
  "create-user-direct",
  (input: CreateUserWorkflowInput) => {
    // Step 1: Generate secure password
    const passwordData = generateSecurePasswordStep();

    // Transform to extract password properly
    const password = transform(passwordData, (data) => data.password);

    // Step 2: Create user FIRST (following Medusa CLI pattern)
    const user = createUserStep({
      email: input.email,
      first_name: input.first_name,
      last_name: input.last_name,
      rbac_metadata: input.rbac_metadata,
      additional_metadata: input.additional_metadata,
      password: password,
    });

    // Step 3: Create auth identity and link to user (following Medusa CLI pattern)
    createAuthIdentityStep({
      email: input.email,
      password: password,
      userId: user.id,
    });

    // Step 4: Emit events for notifications
    emitEventStep({
      eventName: "user.created_direct",
      data: {
        user: user,
        email: input.email,
        first_name: input.first_name,
        last_name: input.last_name,
        temp_password: password, // Pass password in event data, not metadata
      },
    });

    return new WorkflowResponse(user);
  }
);
