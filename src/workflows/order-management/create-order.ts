import {
  StepResponse,
  WorkflowResponse,
  createStep,
  createWorkflow,
} from "@camped-ai/framework/workflows-sdk";
// Removed unused import
import { Modules } from "@camped-ai/framework/utils";

export type CreateOrderStepInput = {
  region_id?: string;
  customer_id?: string;
  sales_channel_id?: string;
  email?: string;
  currency_code?: string;
  is_draft_order?: boolean;
  status?: "pending" | "completed" | "archived" | "canceled" | "requires_action";
  items?: Array<{
    variant_id: string;
    product_id?: string;
    title: string;
    subtitle?: string;
    thumbnail?: string;
    quantity: number;
    unit_price: number;
    is_tax_inclusive?: boolean;
    compare_at_unit_price?: number;
    metadata?: Record<string, any>;
  }>;
  shipping_address?: {
    first_name?: string;
    last_name?: string;
    address_1?: string;
    address_2?: string;
    city?: string;
    country_code?: string;
    province?: string;
    postal_code?: string;
    phone?: string;
    company?: string;
  };
  billing_address?: {
    first_name?: string;
    last_name?: string;
    address_1?: string;
    address_2?: string;
    city?: string;
    country_code?: string;
    province?: string;
    postal_code?: string;
    phone?: string;
    company?: string;
  };
  metadata?: Record<string, any>;
};

type CreateOrderWorkflowInput = CreateOrderStepInput;

const validateOrderInputStep = createStep(
  "validate-order-input",
  async (input: CreateOrderStepInput) => {
    // Validate required fields and business logic
    if (!input.currency_code) {
      input.currency_code = "USD";
    }

    if (input.is_draft_order === undefined) {
      input.is_draft_order = true;
    }

    // Ensure we have either customer_id or email for order creation
    if (!input.customer_id && !input.email) {
      throw new Error("Either customer_id or email must be provided");
    }

    // Validate items if provided
    if (input.items && input.items.length > 0) {
      for (const item of input.items) {
        if (!item.variant_id) {
          throw new Error("All items must have a variant_id");
        }
        if (item.quantity <= 0) {
          throw new Error("All items must have a positive quantity");
        }
        if (item.unit_price < 0) {
          throw new Error("All items must have a non-negative unit_price");
        }
      }
    }

    return new StepResponse(input);
  }
);

const createOrderStep = createStep(
  "create-order",
  async (input: CreateOrderStepInput, { container }) => {
    try {
      // Prepare order data for Medusa's createOrderWorkflow
      const orderData = {
        region_id: input.region_id,
        customer_id: input.customer_id,
        sales_channel_id: input.sales_channel_id,
        email: input.email,
        currency_code: input.currency_code || "USD",
        is_draft_order: input.is_draft_order ?? true,
        status: input.status,
        items: input.items || [],
        shipping_address: input.shipping_address ? {
          ...input.shipping_address,
          id: null, // Ensure new address creation
        } : undefined,
        billing_address: input.billing_address ? {
          ...input.billing_address,
          id: null, // Ensure new address creation
        } : undefined,
        metadata: {
          ...input.metadata,
          created_via: "admin_api",
          order_type: "standard",
        },
      };

      // Use Medusa's order module service directly
      const orderModuleService = container.resolve(Modules.ORDER);

      const orders = await orderModuleService.createOrders([orderData]);
      const order = orders[0];

      if (!order) {
        throw new Error("Failed to create order");
      }

      return new StepResponse(order, order.id);
    } catch (error) {
      console.error("Error creating order:", error);
      throw new Error(`Order creation failed: ${error.message}`);
    }
  },
  async (orderId: string, { container }) => {
    // Compensation: Delete the created order if something goes wrong
    try {
      const orderModuleService = container.resolve(Modules.ORDER);
      await orderModuleService.deleteOrders([orderId]);
    } catch (error) {
      console.error("Failed to compensate order creation:", error);
    }
  }
);

// Step to emit order events with proper data
const emitOrderEventsStep = createStep(
  "emit-order-events",
  async (input: { order: any; originalInput: CreateOrderWorkflowInput }, { container }) => {
    try {
      const eventBusService = container.resolve(Modules.EVENT_BUS);

      // Emit order.created event (triggers universal concierge synchronization)
      await eventBusService.emit({
        name: "order.created",
        data: {
          id: input.order.id,
          order_id: input.order.id,
          customer_id: input.originalInput.customer_id,
          email: input.originalInput.email,
          is_draft_order: input.originalInput.is_draft_order,
          total_items: input.originalInput.items?.length || 0,
        },
      });

      // Emit order.placed event (triggers notifications and other post-processing)
      await eventBusService.emit({
        name: "order.placed",
        data: {
          id: input.order.id,
          order_id: input.order.id,
          customer_id: input.originalInput.customer_id,
          email: input.originalInput.email,
          is_draft_order: input.originalInput.is_draft_order,
          total_items: input.originalInput.items?.length || 0,
        },
      });

      console.log(`✅ [WORKFLOW] Emitted order.created (concierge sync) and order.placed (notifications) events for order: ${input.order.id}`);
      return new StepResponse(true);
    } catch (error) {
      console.error("Error emitting order events:", error);
      // Don't throw error to prevent breaking the workflow
      return new StepResponse(false);
    }
  }
);

export const CreateOrderManagementWorkflow = createWorkflow(
  "create-order-management",
  (input: CreateOrderWorkflowInput) => {
    // Step 1: Validate input
    const validatedInput = validateOrderInputStep(input);

    // Step 2: Create the order
    const order = createOrderStep(validatedInput);

    // Step 3: Emit events for order creation and placement
    emitOrderEventsStep({
      order,
      originalInput: input,
    });

    return new WorkflowResponse(order);
  }
);
