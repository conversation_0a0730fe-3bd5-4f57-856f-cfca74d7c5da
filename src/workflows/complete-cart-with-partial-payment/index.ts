import { createWorkflow, WorkflowResponse } from "@camped-ai/framework/workflows-sdk";
import { 
  completeCartWorkflow,
  useQueryGraphStep,
  createPaymentCollectionForCartWorkflow,
  authorizePaymentSessionStep
} from "@camped-ai/medusa/core-flows";
import { MedusaError } from "@camped-ai/framework/utils";

type WorkflowInput = {
  cart_id: string;
  payment_type?: "deposit" | "full" | "manual";
  partial_amount?: number;
  payment_provider_id?: string;
  metadata?: Record<string, any>;
}

/**
 * Workflow to complete cart with partial payment support
 * 
 * This workflow:
 * 1. Ensures payment collection exists (creates if needed)
 * 2. Handles partial payments for deposits
 * 3. Completes cart to create order
 * 4. Tracks remaining payment amount for concierge team
 */
export const completeCartWithPartialPaymentWorkflow = createWorkflow(
  "complete-cart-with-partial-payment",
  (input: WorkflowInput) => {
    const {
      cart_id,
      payment_type = "deposit",
      partial_amount,
      payment_provider_id = "pp_system_default",
      metadata = {}
    } = input;

    // Get cart details
    const { data: carts } = useQueryGraphStep({
      entity: "cart",
      fields: [
        "id",
        "total",
        "currency_code",
        "customer_id",
        "email",
        "metadata",
        "payment_collection.*"
      ],
      filters: { id: cart_id },
      options: { throwIfKeyNotFound: true }
    });

    const cart = carts[0];

    // Check if payment collection exists
    const hasPaymentCollection = cart.payment_collection && cart.payment_collection.length > 0;

    // If no payment collection exists, create one
    if (!hasPaymentCollection) {
      console.log("Creating payment collection for partial payment...");
      
      // Calculate payment amount
      const paymentAmount = payment_type === "full" 
        ? cart.total 
        : partial_amount || Math.round(cart.total * 0.2); // 20% deposit

      createPaymentCollectionForCartWorkflow.runAsStep({
        input: {
          cart_id: cart_id,
          metadata: {
            cart_id: cart_id,
            cart_total: cart.total,
            payment_amount: paymentAmount,
            payment_type: payment_type,
            is_partial_payment: payment_type !== "full",
            remaining_amount: cart.total - paymentAmount,
            created_via: "partial_payment_workflow",
            ...metadata
          }
        }
      });
    }

    // Complete the cart
    const { id: order_id } = completeCartWorkflow.runAsStep({
      input: { id: cart_id }
    });

    // Get the created order
    const { data: orders } = useQueryGraphStep({
      entity: "order",
      fields: [
        "id",
        "total",
        "currency_code",
        "customer_id",
        "email",
        "metadata",
        "status"
      ],
      filters: { id: order_id },
      options: { throwIfKeyNotFound: true }
    });

    const order = orders[0];

    // Calculate payment tracking information
    const paymentAmount = payment_type === "full" 
      ? cart.total 
      : partial_amount || Math.round(cart.total * 0.2);
    
    const remainingAmount = cart.total - paymentAmount;

    return new WorkflowResponse({
      order: order,
      cart_id: cart_id,
      payment_info: {
        payment_type: payment_type,
        paid_amount: paymentAmount,
        remaining_amount: remainingAmount,
        total_amount: cart.total,
        currency_code: cart.currency_code,
        requires_additional_payment: remainingAmount > 0
      },
      success: true,
      message: payment_type === "manual" 
        ? "Order created successfully. Full payment to be collected by concierge team."
        : `Order created with ${payment_type} payment. Remaining: ${remainingAmount}`
    });
  }
);

export default completeCartWithPartialPaymentWorkflow;
