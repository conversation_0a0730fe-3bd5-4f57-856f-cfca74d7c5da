import {
  StepResponse,
  WorkflowResponse,
  createStep,
  createWorkflow,
} from "@camped-ai/framework/workflows-sdk";
import SupplierModuleService from "src/modules/vendor_management/supplier-service";
import { SUPPLIER_MANAGEMENT_MODULE } from "src/modules/vendor_management";
import { BOOKING_ADD_ONS_MODULE } from "src/modules/booking-add-ons";
import { CONCIERGE_MANAGEMENT_MODULE } from "src/modules/concierge-management";
import BookingAddOnService from "src/modules/booking-add-ons/service";
import { emitEventStep } from "@camped-ai/medusa/core-flows";
import { ConciergeOrderItemStatus } from "src/modules/concierge-management/types";

export type SupplierOrderItem = {
  item_type: "product" | "service";
  item_id: string;
  item_name: string;
  item_description?: string;
  quantity: number;
  unit_price: number;
  service_date?: Date;
  service_duration_minutes?: number;
  product_sku?: string;
  specifications?: Record<string, any>;
  notes?: string;
};

export type CreateSupplierOrderStepInput = {
  supplier_id: string;
  order_type: "product" | "service" | "mixed";
  requested_delivery_date?: Date;
  delivery_address?: string;
  notes?: string;
  internal_notes?: string;
  customer_name?: string;
  customer_email?: string;
  customer_phone?: string;
  hotel_id?: string;
  booking_id?: string;
  items: SupplierOrderItem[];
  metadata?: Record<string, any>;
  booking_addon_ids?: string[]; // IDs of booking add-ons to update
  concierge_order_item_ids?: string[]; // IDs of concierge order items to update
  order_name?: string; // Custom order name
  append_to_existing_order?: string; // ID of existing order to append to
};

type CreateSupplierOrderWorkflowInput = CreateSupplierOrderStepInput;

export const createSupplierOrderStep = createStep(
  "create-supplier-order-step",
  async (input: CreateSupplierOrderStepInput, { container }) => {
    const supplierModuleService: SupplierModuleService = container.resolve(
      SUPPLIER_MANAGEMENT_MODULE
    );

    let order: any;
    let orderItems: any[] = [];

    if (input.append_to_existing_order) {
      // Append to existing order

      // Get existing order
      order = await supplierModuleService.retrieveSupplierOrder(
        input.append_to_existing_order
      );

      if (!order) {
        throw new Error(`Order ${input.append_to_existing_order} not found`);
      }

      // Verify supplier matches
      if (order.supplier_id !== input.supplier_id) {
        throw new Error(`Cannot append to order from different supplier`);
      }

      // Add new items to existing order
      for (const item of input.items) {
        const orderItem = await supplierModuleService.createSupplierOrderItem({
          order_id: order.id,
          item_type: item.item_type,
          item_id: item.item_id,
          item_name: item.item_name,
          item_description: item.item_description,
          quantity: item.quantity,
          unit_price: item.unit_price,
          total_price: item.quantity * item.unit_price,
          service_date: item.service_date,
          service_duration_minutes: item.service_duration_minutes,
          product_sku: item.product_sku,
          specifications: item.specifications,
          notes: item.notes,
        });
        orderItems.push(orderItem);
      }

      // Update order totals
      const allItems = await supplierModuleService.getOrderItems(order.id);
      const subtotal = allItems.reduce(
        (sum, item) => sum + item.total_price,
        0
      );
      const total_amount = subtotal; // Tax calculation can be added later

      // Update order with new totals and merge metadata
      const updatedMetadata = {
        ...order.metadata,
        ...input.metadata,
        booking_addon_ids: [
          ...(order.metadata?.booking_addon_ids || []),
          ...(input.booking_addon_ids || []),
        ],
      };

      order = await supplierModuleService.updateSupplierOrders({
        selector: { id: order.id },
        data: {
          subtotal,
          total_amount,
          metadata: updatedMetadata,
          // Update notes if provided
          ...(input.notes && { notes: input.notes }),
          ...(input.internal_notes && { internal_notes: input.internal_notes }),
        },
      });
    } else {
      // Create new order

      // Calculate totals
      const subtotal = input.items.reduce(
        (sum, item) => sum + item.quantity * item.unit_price,
        0
      );
      const total_amount = subtotal; // Tax calculation can be added later

      // Create the order
      order = await supplierModuleService.createSupplierOrder({
        supplier_id: input.supplier_id,
        order_type: input.order_type,
        subtotal,
        total_amount,
        requested_delivery_date: input.requested_delivery_date,
        delivery_address: input.delivery_address,
        notes: input.notes,
        internal_notes: input.internal_notes,
        customer_name: input.customer_name,
        customer_email: input.customer_email,
        customer_phone: input.customer_phone,
        hotel_id: input.hotel_id,
        booking_id: input.booking_id,
        order_name: input.order_name, // Add custom order name
        metadata: input.metadata,
      });

      // Create order items
      for (const item of input.items) {
        const orderItem = await supplierModuleService.createSupplierOrderItem({
          order_id: order.id,
          item_type: item.item_type,
          item_id: item.item_id,
          item_name: item.item_name,
          item_description: item.item_description,
          quantity: item.quantity,
          unit_price: item.unit_price,
          total_price: item.quantity * item.unit_price,
          service_date: item.service_date,
          service_duration_minutes: item.service_duration_minutes,
          product_sku: item.product_sku,
          specifications: item.specifications,
          notes: item.notes,
        });
        orderItems.push(orderItem);
      }
    }

    return new StepResponse({ order, items: orderItems }, [order.id]);
  },
  async (ids: string[], { container }) => {
    const supplierModuleService: SupplierModuleService = container.resolve(
      SUPPLIER_MANAGEMENT_MODULE
    );
    await supplierModuleService.deleteSupplierOrders(ids);
  }
);

// Step to update booking add-ons with supplier order information
export const updateBookingAddOnsStep = createStep(
  "update-booking-addons-step",
  async (
    input: {
      booking_addon_ids: string[];
      supplier_order_id: string;
      order_status: string;
    },
    { container }
  ) => {
    if (!input.booking_addon_ids || input.booking_addon_ids.length === 0) {
      return new StepResponse({
        updated_count: 0,
        updated_addons: [],
        errors: [],
      });
    }

    const bookingAddOnService: BookingAddOnService = container.resolve(
      BOOKING_ADD_ONS_MODULE
    );


    const updatedAddOns = [];
    const errors = [];

    for (const addonId of input.booking_addon_ids) {
      try {
        const updatedAddOn = await bookingAddOnService.updateBookingAddOn(
          addonId,
          {
            supplier_order_id: input.supplier_order_id,
            order_status: input.order_status as any,
          }
        );
        updatedAddOns.push(updatedAddOn);
      } catch (error) {
        console.error(`❌ Failed to update booking add-on ${addonId}:`, error);
        errors.push(
          `Failed to update booking add-on ${addonId}: ${error.message}`
        );
      }
    }


    return new StepResponse({
      updated_count: updatedAddOns.length,
      updated_addons: updatedAddOns,
      errors,
    });
  },
  async (data: { updated_addons: any[] }, { container }) => {
    // Rollback: Remove supplier order references from booking add-ons
    if (!data.updated_addons || data.updated_addons.length === 0) {
      return;
    }

    const bookingAddOnService: BookingAddOnService = container.resolve(
      BOOKING_ADD_ONS_MODULE
    );

    for (const addon of data.updated_addons) {
      try {
        await bookingAddOnService.updateBookingAddOn(addon.id, {
          supplier_order_id: null,
          order_status: null,
        });
      } catch (error) {
        console.error(
          `❌ Failed to rollback booking add-on ${addon.id}:`,
          error
        );
      }
    }
  }
);

// Step to update concierge order item statuses to "order_placed"
export const updateConciergeOrderItemsStep = createStep(
  "update-concierge-order-items-step",
  async (
    input: {
      concierge_order_item_ids: string[];
      supplier_order_id: string;
      updated_by?: string;
    },
    { container }
  ) => {
    if (!input.concierge_order_item_ids || input.concierge_order_item_ids.length === 0) {
      return new StepResponse({
        updated_count: 0,
        updated_items: [],
        errors: [],
      });
    }

    const conciergeManagementService = container.resolve(CONCIERGE_MANAGEMENT_MODULE);


    const updatedItems = [];
    const errors = [];

    for (const itemId of input.concierge_order_item_ids) {
      try {
        const updatedItem = await conciergeManagementService.updateConciergeOrderItemStatus(
          itemId,
          ConciergeOrderItemStatus.ORDER_PLACED,
          input.updated_by
        );
        updatedItems.push(updatedItem);
      } catch (error) {
        console.error(`❌ Failed to update concierge order item ${itemId}:`, error);
        errors.push(
          `Failed to update concierge order item ${itemId}: ${error.message}`
        );
      }
    }

    return new StepResponse({
      updated_count: updatedItems.length,
      updated_items: updatedItems,
      errors,
    });
  },
  async (data: { updated_items: any[] }, { container }) => {
    // Rollback: Revert concierge order item statuses back to "client_confirmed"
    if (!data.updated_items || data.updated_items.length === 0) {
      return;
    }

    const conciergeManagementService = container.resolve(CONCIERGE_MANAGEMENT_MODULE);

    for (const item of data.updated_items) {
      try {
        await conciergeManagementService.updateConciergeOrderItemStatus(
          item.id,
          ConciergeOrderItemStatus.CLIENT_CONFIRMED
        );
      } catch (error) {
        console.error(
          `❌ Failed to rollback concierge order item ${item.id}:`,
          error
        );
      }
    }
  }
);

export const CreateSupplierOrderWorkflow = createWorkflow(
  "create-supplier-order",
  (input: CreateSupplierOrderWorkflowInput) => {
    // Create the order with items
    const result = createSupplierOrderStep(input);

    // Update booking add-ons if provided (conditionally)
    const updateResult = updateBookingAddOnsStep({
      booking_addon_ids: input.booking_addon_ids || [],
      supplier_order_id: result.order.id,
      order_status: result.order.status,
    });

    // Update concierge order items if provided (conditionally)
    const updateConciergeResult = updateConciergeOrderItemsStep({
      concierge_order_item_ids: input.concierge_order_item_ids || [],
      supplier_order_id: result.order.id,
    });

    // Emit event for order creation
    emitEventStep({
      eventName: "supplier.order.created",
      data: {
        order: result.order,
        items: result.items,
        supplier_id: input.supplier_id,
        order_type: input.order_type,
        total_amount: result.order.total_amount,
        booking_addons_updated: input.booking_addon_ids?.length || 0,
        concierge_items_updated: input.concierge_order_item_ids?.length || 0,
      },
    });

    return new WorkflowResponse(result);
  }
);
