import {
  StepResponse,
  WorkflowResponse,
  createStep,
  createWorkflow,
} from "@camped-ai/framework/workflows-sdk";
import SupplierModuleService from "src/modules/vendor_management/supplier-service";
import { SUPPLIER_MANAGEMENT_MODULE } from "src/modules/vendor_management";
import { emitEventStep } from "@camped-ai/medusa/core-flows";

export type BulkImportSuppliersStepInput = {
  suppliers: Array<{
    name: string;
    handle?: string;
    description?: string;
    supplier_type?: "company" | "individual";

    status?: string;
    verification_status?: string;
    region?: string;
    timezone?: string;
    language_preference?: string[];
    payment_method?: string;
    payout_terms?: string;
    bank_account_details?: Record<string, any>;
    categories?: string[];
    business_registration_number?: string;
    tax_id?: string;
    website?: string;
    address?: string;
    default_currency?: string;
    metadata?: Record<string, any>;
    contacts?: Array<{
      name: string;
      email?: string;
      phone?: string;
      is_whatsapp?: boolean;
      is_primary?: boolean;
    }>;
  }>;
};

type BulkImportSuppliersWorkflowInput = BulkImportSuppliersStepInput;

export const bulkImportSuppliersStep = createStep(
  "bulk-import-suppliers",
  async (input: BulkImportSuppliersStepInput, { container }) => {
    const supplierModuleService: SupplierModuleService = container.resolve(
      SUPPLIER_MANAGEMENT_MODULE
    );

    const results = {
      imported: 0,
      errors: [] as Array<{ index: number; error: string; supplier: any }>,
      suppliers: [] as any[],
    };

    // Process each supplier individually to handle errors gracefully
    for (let i = 0; i < input.suppliers.length; i++) {
      const supplierData = input.suppliers[i];

      try {
        // Generate handle if not provided
        if (!supplierData.handle) {
          supplierData.handle = supplierData.name
            .toLowerCase()
            .replace(/[^a-z0-9]+/g, "-")
            .replace(/^-+|-+$/g, "");
        }

        // Check for duplicate by name or handle
        const existingSuppliersByName =
          await supplierModuleService.listSuppliers({
            name: supplierData.name,
          });

        const existingSuppliersByHandle =
          await supplierModuleService.listSuppliers({
            handle: supplierData.handle,
          });

        const existingSuppliers = [
          ...(existingSuppliersByName.suppliers || []),
          ...(existingSuppliersByHandle.suppliers || []),
        ];

        // If supplier exists, update it instead of creating new one
        if (existingSuppliers.length > 0) {
          try {
            const existingSupplier = existingSuppliers[0]; // Use the first match
            const updatedSupplier = await supplierModuleService.updateSupplier(
              existingSupplier.id,
              supplierData
            );

            results.suppliers.push(updatedSupplier);
            results.imported++;
          } catch (updateError) {
            results.errors.push({
              index: i + 1,
              error: `Failed to update existing supplier: ${updateError.message}`,
              supplier: supplierData,
            });
          }
          continue;
        }

        // Create the supplier using the correct method
        const supplier = await supplierModuleService.createSupplierWithHandle(
          supplierData
        );
        results.suppliers.push(supplier);
        results.imported++;
      } catch (error) {
        results.errors.push({
          index: i + 1,
          error: error.message || "Unknown error occurred",
          supplier: supplierData,
        });
      }
    }

    return new StepResponse(
      results,
      results.suppliers.map((s) => s.id)
    );
  },
  async (supplierIds: string[], { container }) => {
    // Compensation: delete any suppliers that were created
    if (supplierIds && supplierIds.length > 0) {
      const supplierModuleService: SupplierModuleService = container.resolve(
        SUPPLIER_MANAGEMENT_MODULE
      );

      try {
        await supplierModuleService.deleteSuppliers(supplierIds);
      } catch (error) {
        // Silently handle cleanup errors
      }
    }
  }
);

export const BulkImportSuppliersWorkflow = createWorkflow(
  "bulk-import-suppliers",
  (input: BulkImportSuppliersWorkflowInput) => {
    // Import suppliers in bulk
    const result = bulkImportSuppliersStep(input);

    // Emit event for bulk import completion
    emitEventStep({
      eventName: "suppliers.bulk_imported",
      data: {
        total_processed: input.suppliers.length,
        imported: result.imported,
        errors_count: result.errors?.length || 0,
        timestamp: new Date().toISOString(),
      },
    });

    return new WorkflowResponse(result);
  }
);
