import {
  StepResponse,
  WorkflowResponse,
  createStep,
  createWorkflow,
} from "@camped-ai/framework/workflows-sdk";
import SupplierModuleService from "src/modules/vendor_management/supplier-service";
import { SUPPLIER_MANAGEMENT_MODULE } from "src/modules/vendor_management";
import { emitEventStep } from "@camped-ai/medusa/core-flows";

export type DeleteExchangeRateStepInput = {
  id: string;
};

type DeleteExchangeRateWorkflowInput = DeleteExchangeRateStepInput;

const deleteExchangeRateStep = createStep(
  "delete-exchange-rate-step",
  async (input: DeleteExchangeRateStepInput, { container }) => {
    const supplierModuleService: SupplierModuleService = container.resolve(
      SUPPLIER_MANAGEMENT_MODULE
    );

    console.log("Deleting exchange rate with id:", input.id);

    // Validate input
    if (!input.id) {
      throw new Error("Exchange rate ID is required for deletion");
    }

    // Get the exchange rate data before deletion for rollback and event
    const exchangeRate = await supplierModuleService.retrieveExchangeRate(input.id);
    if (!exchangeRate) {
      throw new Error(`Exchange rate with id ${input.id} not found`);
    }

    // Delete the exchange rate
    const result = await supplierModuleService.deleteExchangeRate(input.id);

    console.log("Successfully deleted exchange rate:", result);

    return new StepResponse(result, {
      id: input.id,
      deletedData: {
        date: exchangeRate.date,
        base_currency: exchangeRate.base_currency,
        selling_currency: exchangeRate.selling_currency,
        exchange_rate: exchangeRate.exchange_rate,
        metadata: exchangeRate.metadata,
        created_at: exchangeRate.created_at,
        updated_at: exchangeRate.updated_at,
      },
    });
  },
  async (rollbackData: any, { container }) => {
    const supplierModuleService: SupplierModuleService = container.resolve(
      SUPPLIER_MANAGEMENT_MODULE
    );
    // Rollback: recreate the deleted exchange rate
    await supplierModuleService.createExchangeRate({
      ...rollbackData.deletedData,
      // Note: The ID will be auto-generated, so we can't restore the exact same ID
      // This is a limitation of soft-delete vs hard-delete scenarios
    });
  }
);

export const DeleteExchangeRateWorkflow = createWorkflow(
  "delete-exchange-rate",
  (input: DeleteExchangeRateWorkflowInput) => {
    // Delete the exchange rate
    const result = deleteExchangeRateStep(input);

    // Emit event for exchange rate deletion
    emitEventStep({
      eventName: "exchange_rate.deleted",
      data: {
        id: input.id,
        result: result,
      },
    });

    return new WorkflowResponse(result);
  }
);
