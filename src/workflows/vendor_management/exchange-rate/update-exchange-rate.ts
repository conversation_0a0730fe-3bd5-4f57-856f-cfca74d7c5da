import {
  StepResponse,
  WorkflowResponse,
  createStep,
  createWorkflow,
} from "@camped-ai/framework/workflows-sdk";
import SupplierModuleService from "src/modules/vendor_management/supplier-service";
import { SUPPLIER_MANAGEMENT_MODULE } from "src/modules/vendor_management";
import { emitEventStep } from "@camped-ai/medusa/core-flows";

export type UpdateExchangeRateStepInput = {
  id: string;
  date?: string;
  base_currency?: string;
  selling_currency?: string;
  exchange_rate?: number;
  metadata?: Record<string, any>;
};

type UpdateExchangeRateWorkflowInput = UpdateExchangeRateStepInput;

const updateExchangeRateStep = createStep(
  "update-exchange-rate-step",
  async (input: UpdateExchangeRateStepInput, { container }) => {
    const supplierModuleService: SupplierModuleService = container.resolve(
      SUPPLIER_MANAGEMENT_MODULE
    );

    console.log("Updating exchange rate with input:", input);

    // Validate input
    if (!input.id) {
      throw new Error("Exchange rate ID is required for update");
    }

    // Validate exchange rate is positive if provided
    if (input.exchange_rate !== undefined && input.exchange_rate <= 0) {
      throw new Error("Exchange rate must be a positive number");
    }

    // Get the original exchange rate for rollback
    const originalExchangeRate = await supplierModuleService.retrieveExchangeRate(input.id);
    if (!originalExchangeRate) {
      throw new Error(`Exchange rate with id ${input.id} not found`);
    }

    // Prepare update data
    const updateData: any = {};
    if (input.date !== undefined) updateData.date = input.date;
    if (input.base_currency !== undefined) updateData.base_currency = input.base_currency.toUpperCase();
    if (input.selling_currency !== undefined) updateData.selling_currency = input.selling_currency.toUpperCase();
    if (input.exchange_rate !== undefined) updateData.exchange_rate = input.exchange_rate;
    if (input.metadata !== undefined) updateData.metadata = input.metadata;

    // Update the exchange rate
    const updatedExchangeRate = await supplierModuleService.updateExchangeRate(input.id, updateData);

    console.log("Successfully updated exchange rate:", updatedExchangeRate);

    return new StepResponse(updatedExchangeRate, {
      id: input.id,
      originalData: {
        date: originalExchangeRate.date,
        base_currency: originalExchangeRate.base_currency,
        selling_currency: originalExchangeRate.selling_currency,
        exchange_rate: originalExchangeRate.exchange_rate,
        metadata: originalExchangeRate.metadata,
      },
    });
  },
  async (rollbackData: any, { container }) => {
    const supplierModuleService: SupplierModuleService = container.resolve(
      SUPPLIER_MANAGEMENT_MODULE
    );
    // Rollback: restore the original exchange rate data
    await supplierModuleService.updateExchangeRate(rollbackData.id, rollbackData.originalData);
  }
);

export const UpdateExchangeRateWorkflow = createWorkflow(
  "update-exchange-rate",
  (input: UpdateExchangeRateWorkflowInput) => {
    // Update the exchange rate
    const exchangeRate = updateExchangeRateStep(input);

    // Emit event for exchange rate update
    emitEventStep({
      eventName: "exchange_rate.updated",
      data: {
        exchange_rate: exchangeRate,
        id: input.id,
        updated_fields: Object.keys(input).filter(key => key !== 'id' && input[key] !== undefined),
      },
    });

    return new WorkflowResponse(exchangeRate);
  }
);
