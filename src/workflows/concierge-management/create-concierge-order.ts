import { MedusaContainer } from "@camped-ai/framework/types";
import {
  createStep,
  createWorkflow,
  StepResponse,
  WorkflowResponse,
} from "@camped-ai/framework/workflows-sdk";

import { CONCIERGE_MANAGEMENT_MODULE } from "../../modules/concierge-management";
import { 
  CreateConciergeOrderInput, 
  ConciergeOrderResponse,
  ConciergeOrderStatus 
} from "../../modules/concierge-management/types";

// Input type for the workflow
type CreateConciergeOrderWorkflowInput = {
  order_id: string;
  hotel_id?: string;
  check_in_date?: string | Date;
  check_out_date?: string | Date;
  assigned_to?: string;
  notes?: string;
  status?: ConciergeOrderStatus;
  metadata?: Record<string, any>;
};

// Output type for the workflow
type CreateConciergeOrderWorkflowOutput = {
  success: boolean;
  concierge_order?: ConciergeOrderResponse;
  error?: string;
};

/**
 * Step to validate input data
 */
const validateCreateConciergeOrderInputStep = createStep(
  "validate-create-concierge-order-input",
  async (input: CreateConciergeOrderWorkflowInput) => {
    // Validate required fields
    if (!input.order_id) {
      throw new Error("order_id is required");
    }

    // Validate order_id format (basic validation)
    if (typeof input.order_id !== "string" || input.order_id.trim().length === 0) {
      throw new Error("order_id must be a non-empty string");
    }

    // Validate status if provided
    if (input.status && !Object.values(ConciergeOrderStatus).includes(input.status)) {
      throw new Error(`Invalid status: ${input.status}`);
    }

    return new StepResponse(input);
  }
);

/**
 * Step to check if concierge order already exists for the order
 */
const checkExistingConciergeOrderStep = createStep(
  "check-existing-concierge-order",
  async (input: CreateConciergeOrderWorkflowInput, { container }: { container: MedusaContainer }) => {
    const conciergeManagementService = container.resolve(CONCIERGE_MANAGEMENT_MODULE);

    try {
      const existingOrder = await conciergeManagementService.retrieveConciergeOrderByOrderId(input.order_id);

      if (existingOrder) {
        throw new Error(`Concierge order already exists for order_id: ${input.order_id}`);
      }

      return new StepResponse(input);
    } catch (error) {
      // If error is "not found", that's what we want
      if (error.message.includes("already exists")) {
        throw error;
      }
      // Continue if order doesn't exist
      return new StepResponse(input);
    }
  }
);

/**
 * Step to create the concierge order
 */
const createConciergeOrderStep = createStep(
  "create-concierge-order",
  async (input: CreateConciergeOrderWorkflowInput, { container }: { container: MedusaContainer }) => {
    const conciergeManagementService = container.resolve(CONCIERGE_MANAGEMENT_MODULE);

    try {
      const createData: CreateConciergeOrderInput = {
        order_id: input.order_id,
        hotel_id: input.hotel_id,
        check_in_date: input.check_in_date,
        check_out_date: input.check_out_date,
        assigned_to: input.assigned_to,
        notes: input.notes,
        status: input.status || ConciergeOrderStatus.NOT_STARTED,
        metadata: input.metadata,
      };

      const conciergeOrder = await conciergeManagementService.createConciergeOrder(createData);

      return new StepResponse({
        success: true,
        concierge_order: conciergeOrder,
      });
    } catch (error) {
      return new StepResponse({
        success: false,
        error: error.message,
      });
    }
  },
  // Compensation function to rollback if needed
  async (input: CreateConciergeOrderWorkflowInput, { container }: { container: MedusaContainer }) => {
    const conciergeManagementService = container.resolve(CONCIERGE_MANAGEMENT_MODULE);

    try {
      const existingOrder = await conciergeManagementService.retrieveConciergeOrderByOrderId(input.order_id);
      if (existingOrder) {
        await conciergeManagementService.deleteConciergeOrder(existingOrder.id);
      }
    } catch (error) {
      console.error("Failed to rollback concierge order creation:", error);
    }
  }
);

/**
 * Workflow to create a concierge order
 */
export const createConciergeOrderWorkflowId = "create-concierge-order-workflow";
export const CreateConciergeOrderWorkflow = createWorkflow(
  {
    name: createConciergeOrderWorkflowId,
    retentionTime: 86400, // 24 hours in seconds
    store: true,
  },
  function (input: CreateConciergeOrderWorkflowInput) {
    // Execute steps in sequence
    const validatedInput = validateCreateConciergeOrderInputStep(input);
    const checkedInput = checkExistingConciergeOrderStep(validatedInput);
    const result = createConciergeOrderStep(checkedInput);

    // Return the final result
    return new WorkflowResponse(result);
  }
);

export default CreateConciergeOrderWorkflow;
