import { <PERSON>dusaContainer } from "@camped-ai/framework/types";
import {
  createStep,
  createWorkflow,
  StepResponse,
  WorkflowResponse,
} from "@camped-ai/framework/workflows-sdk";

import { CONCIERGE_MANAGEMENT_MODULE } from "../../modules/concierge-management";
import { 
  CreateConciergeOrderItemInput, 
  ConciergeOrderItemResponse,
  ConciergeOrderItemStatus 
} from "../../modules/concierge-management/types";

// Input type for the workflow
type CreateConciergeOrderItemWorkflowInput = {
  concierge_order_id: string;
  line_item_id?: string;
  item_id?: string;
  variant_id?: string;
  quantity: number;
  unit_price: number;
  title: string;
  status?: ConciergeOrderItemStatus;
  added_by?: string;
  metadata?: Record<string, any>;
  // New fields
  category_id?: string | null;
  start_date?: string | null;
  end_date?: string | null;
};

// Output type for the workflow
type CreateConciergeOrderItemWorkflowOutput = {
  success: boolean;
  concierge_order_item?: ConciergeOrderItemResponse;
  error?: string;
};

/**
 * Step to validate input data
 */
const validateCreateItemInputStep = createStep(
  "validate-create-concierge-order-item-input",
  async (input: CreateConciergeOrderItemWorkflowInput) => {
    // Validate required fields
    if (!input.concierge_order_id) {
      throw new Error("concierge_order_id is required");
    }

    if (!input.title || input.title.trim().length === 0) {
      throw new Error("title is required and cannot be empty");
    }

    if (typeof input.quantity !== "number" || input.quantity <= 0) {
      throw new Error("quantity must be a positive number");
    }

    if (typeof input.unit_price !== "number" || input.unit_price < 0) {
      throw new Error("unit_price must be a non-negative number");
    }

    // Validate status if provided
    if (input.status && !Object.values(ConciergeOrderItemStatus).includes(input.status)) {
      throw new Error(`Invalid status: ${input.status}`);
    }

    return new StepResponse(input);
  }
);

/**
 * Step to verify the concierge order exists
 */
const verifyConciergeOrderExistsStep = createStep(
  "verify-concierge-order-exists",
  async (input: CreateConciergeOrderItemWorkflowInput, { container }: { container: MedusaContainer }) => {
    const conciergeManagementService = container.resolve(CONCIERGE_MANAGEMENT_MODULE);

    try {
      const conciergeOrder = await conciergeManagementService.retrieveConciergeOrder(input.concierge_order_id);

      return new StepResponse({
        ...input,
        concierge_order: conciergeOrder,
      });
    } catch (error) {
      throw new Error(`Concierge order not found: ${error.message}`);
    }
  }
);

/**
 * Step to create the concierge order item
 */
const createConciergeOrderItemStep = createStep(
  "create-concierge-order-item",
  async (
    input: CreateConciergeOrderItemWorkflowInput & { concierge_order: any },
    { container }: { container: MedusaContainer }
  ) => {
    const conciergeManagementService = container.resolve(CONCIERGE_MANAGEMENT_MODULE);

    try {
      const createData: CreateConciergeOrderItemInput = {
        concierge_order_id: input.concierge_order_id,
        line_item_id: input.line_item_id,
        item_id: input.item_id,
        variant_id: input.variant_id,
        quantity: input.quantity,
        unit_price: input.unit_price,
        title: input.title,
        status: input.status || ConciergeOrderItemStatus.UNDER_REVIEW,
        added_by: input.added_by,
        metadata: input.metadata,
        // Include new fields
        category_id: input.category_id,
        start_date: input.start_date,
        end_date: input.end_date,
      };

      const conciergeOrderItem = await conciergeManagementService.createConciergeOrderItem(createData);

      return new StepResponse({
        success: true,
        concierge_order_item: conciergeOrderItem,
      });
    } catch (error) {
      return new StepResponse({
        success: false,
        error: error.message,
      });
    }
  },
  // Compensation function to rollback if needed
  async (
    input: CreateConciergeOrderItemWorkflowInput,
    { container }: { container: MedusaContainer }
  ) => {
    const conciergeManagementService = container.resolve(CONCIERGE_MANAGEMENT_MODULE);

    try {
      // Find and delete the created item
      const items = await conciergeManagementService.listConciergeOrderItems(
        { concierge_order_id: input.concierge_order_id }
      );
      const createdItem = items.find(item =>
        item.title === input.title &&
        item.quantity === input.quantity &&
        item.unit_price === input.unit_price
      );

      if (createdItem) {
        await conciergeManagementService.deleteConciergeOrderItem(createdItem.id);
      }
    } catch (error) {
      console.error("Failed to rollback concierge order item creation:", error);
    }
  }
);

/**
 * Workflow to create a concierge order item
 */
export const createConciergeOrderItemWorkflowId = "create-concierge-order-item-workflow";
export const CreateConciergeOrderItemWorkflow = createWorkflow(
  {
    name: createConciergeOrderItemWorkflowId,
    retentionTime: 86400, // 24 hours in seconds
    store: true,
  },
  function (input: CreateConciergeOrderItemWorkflowInput) {
    // Execute steps in sequence
    const validatedInput = validateCreateItemInputStep(input);
    const verifiedInput = verifyConciergeOrderExistsStep(validatedInput);
    const result = createConciergeOrderItemStep(verifiedInput);

    // Return the final result
    return new WorkflowResponse(result);
  }
);

export default CreateConciergeOrderItemWorkflow;
