import { MedusaContainer } from "@camped-ai/framework/types";
import {
  createStep,
  createWorkflow,
  StepResponse,
  WorkflowResponse,
} from "@camped-ai/framework/workflows-sdk";

import { CONCIERGE_MANAGEMENT_MODULE } from "../../modules/concierge-management";
import { 
  ConciergeOrderResponse,
  ConciergeOrderStatus 
} from "../../modules/concierge-management/types";

// Input type for the workflow
type UpdateConciergeOrderStatusWorkflowInput = {
  concierge_order_id: string;
  status: ConciergeOrderStatus;
  updated_by?: string;
  notes?: string;
};

// Output type for the workflow
type UpdateConciergeOrderStatusWorkflowOutput = {
  success: boolean;
  concierge_order?: ConciergeOrderResponse;
  error?: string;
};

/**
 * Step to validate input data
 */
const validateUpdateStatusInputStep = createStep(
  "validate-update-concierge-order-status-input",
  async (input: UpdateConciergeOrderStatusWorkflowInput) => {
    // Validate required fields
    if (!input.concierge_order_id) {
      throw new Error("concierge_order_id is required");
    }

    if (!input.status) {
      throw new Error("status is required");
    }

    // Validate status
    if (!Object.values(ConciergeOrderStatus).includes(input.status)) {
      throw new Error(`Invalid status: ${input.status}`);
    }

    return new StepResponse(input);
  }
);

/**
 * Step to retrieve and validate the existing concierge order
 */
const retrieveConciergeOrderStep = createStep(
  "retrieve-concierge-order-for-status-update",
  async (input: UpdateConciergeOrderStatusWorkflowInput, { container }: { container: MedusaContainer }) => {
    const conciergeManagementService = container.resolve(CONCIERGE_MANAGEMENT_MODULE);

    try {
      const conciergeOrder = await conciergeManagementService.retrieveConciergeOrder(input.concierge_order_id);

      // Store the original status for potential rollback
      return new StepResponse({
        ...input,
        original_status: conciergeOrder.status,
        concierge_order: conciergeOrder,
      });
    } catch (error) {
      throw new Error(`Failed to retrieve concierge order: ${error.message}`);
    }
  }
);

/**
 * Step to update the concierge order status
 */
const updateConciergeOrderStatusStep = createStep(
  "update-concierge-order-status",
  async (
    input: UpdateConciergeOrderStatusWorkflowInput & { 
      original_status: ConciergeOrderStatus;
      concierge_order: ConciergeOrderResponse;
    }, 
    { container }: { container: MedusaContainer }
  ) => {
    const conciergeManagementService = container.resolve(CONCIERGE_MANAGEMENT_MODULE);

    try {
      // Update the status
      const updatedOrder = await conciergeManagementService.updateConciergeOrderStatus(
        input.concierge_order_id,
        input.status,
        input.updated_by
      );

      // If notes are provided, update them as well
      if (input.notes) {
        const finalOrder = await conciergeManagementService.updateConciergeOrder(
          input.concierge_order_id,
          { notes: input.notes }
        );

        return new StepResponse({
          success: true,
          concierge_order: finalOrder,
        });
      }

      return new StepResponse({
        success: true,
        concierge_order: updatedOrder,
      });
    } catch (error) {
      return new StepResponse({
        success: false,
        error: error.message,
      });
    }
  },
  // Compensation function to rollback status change
  async (
    input: UpdateConciergeOrderStatusWorkflowInput & {
      original_status: ConciergeOrderStatus;
    },
    { container }: { container: MedusaContainer }
  ) => {
    const conciergeManagementService = container.resolve(CONCIERGE_MANAGEMENT_MODULE);

    try {
      await conciergeManagementService.updateConciergeOrderStatus(
        input.concierge_order_id,
        input.original_status,
        input.updated_by
      );
    } catch (error) {
      console.error("Failed to rollback concierge order status update:", error);
    }
  }
);

/**
 * Workflow to update concierge order status
 */
export const updateConciergeOrderStatusWorkflowId = "update-concierge-order-status-workflow";
export const UpdateConciergeOrderStatusWorkflow = createWorkflow(
  {
    name: updateConciergeOrderStatusWorkflowId,
    retentionTime: 86400, // 24 hours in seconds
    store: true,
  },
  function (input: UpdateConciergeOrderStatusWorkflowInput) {
    // Execute steps in sequence
    const validatedInput = validateUpdateStatusInputStep(input);
    const retrievedOrder = retrieveConciergeOrderStep(validatedInput);
    const result = updateConciergeOrderStatusStep(retrievedOrder);

    // Return the final result
    return new WorkflowResponse(result);
  }
);

export default UpdateConciergeOrderStatusWorkflow;
