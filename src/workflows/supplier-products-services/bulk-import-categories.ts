import {
  createStep,
  createWorkflow,
  StepResponse,
  WorkflowResponse,
} from "@camped-ai/framework/workflows-sdk";
import { MedusaError } from "@camped-ai/framework/utils";
import { SUPPLIER_PRODUCTS_SERVICES_MODULE } from "src/modules/supplier-products-services";
import SupplierProductsServicesModuleService from "src/modules/supplier-products-services/service";
import { CreateCategoryInput, CategoryOutput } from "src/modules/supplier-products-services/types";

export type BulkImportCategoriesStepInput = {
  categories: CreateCategoryInput[];
};

type BulkImportCategoriesWorkflowInput = BulkImportCategoriesStepInput;

export interface BulkImportCategoriesResult {
  imported: number;
  errors: Array<{
    index: number;
    name: string;
    error: string;
  }>;
  categories: CategoryOutput[];
}

// Step to validate categories before import
const validateCategoriesStep = createStep(
  "validate-categories-step",
  async (input: BulkImportCategoriesStepInput) => {
    console.log("ValidateCategoriesStep - Input:", JSON.stringify(input, null, 2));

    const { categories } = input;
    const errors: Array<{ index: number; name: string; error: string }> = [];

    // Validate each category
    categories.forEach((category, index) => {
      if (!category.name || category.name.trim() === '') {
        errors.push({
          index,
          name: category.name || 'Unknown',
          error: 'Category name is required'
        });
      }

      // Validate dynamic field schema if present
      if (category.dynamic_field_schema) {
        category.dynamic_field_schema.forEach((field, fieldIndex) => {
          if (!field.label || !field.key || !field.type) {
            errors.push({
              index,
              name: category.name || 'Unknown',
              error: `Dynamic field at index ${fieldIndex} is missing required properties (label, key, type)`
            });
          }

          if ((field.type === 'dropdown' || field.type === 'multi-select') && 
              (!field.options || field.options.length === 0)) {
            errors.push({
              index,
              name: category.name || 'Unknown',
              error: `Dynamic field '${field.label}' requires options for ${field.type} type`
            });
          }
        });
      }
    });

    console.log("ValidateCategoriesStep - Validation errors:", errors);

    return new StepResponse(
      { categories, validationErrors: errors },
      errors.length > 0 ? errors : undefined
    );
  }
);

// Step to bulk import categories
const bulkImportCategoriesStep = createStep(
  "bulk-import-categories-step",
  async (
    input: { categories: CreateCategoryInput[]; validationErrors: Array<{ index: number; name: string; error: string }> },
    { container }
  ) => {
    console.log("BulkImportCategoriesStep - Starting import for categories:", input.categories.length);

    const supplierProductsServicesService: SupplierProductsServicesModuleService =
      container.resolve(SUPPLIER_PRODUCTS_SERVICES_MODULE);

    const result: BulkImportCategoriesResult = {
      imported: 0,
      errors: [...input.validationErrors],
      categories: [],
    };

    // If there are validation errors, don't proceed with import
    if (input.validationErrors.length > 0) {
      console.log("BulkImportCategoriesStep - Skipping import due to validation errors");
      return new StepResponse(result);
    }

    // Import categories one by one
    for (let i = 0; i < input.categories.length; i++) {
      const categoryData = input.categories[i];
      
      try {
        console.log(`BulkImportCategoriesStep - Importing category ${i + 1}/${input.categories.length}:`, categoryData.name);

        // Check if category with same name already exists
        const existingCategories = await supplierProductsServicesService.listCategories(
          { name: categoryData.name },
          { skip: 0, take: 1 }
        );

        if (existingCategories.length > 0) {
          console.log(`BulkImportCategoriesStep - Category '${categoryData.name}' already exists, skipping`);
          result.errors.push({
            index: i,
            name: categoryData.name,
            error: 'Category with this name already exists'
          });
          continue;
        }

        // Create the category
        const createdCategory = await supplierProductsServicesService.createCategory(categoryData);
        
        result.categories.push(createdCategory);
        result.imported++;
        
        console.log(`BulkImportCategoriesStep - Successfully imported category: ${createdCategory.name}`);
        
      } catch (error) {
        console.error(`BulkImportCategoriesStep - Failed to import category '${categoryData.name}':`, error);
        
        result.errors.push({
          index: i,
          name: categoryData.name,
          error: error instanceof Error ? error.message : 'Unknown error occurred'
        });
      }
    }

    console.log("BulkImportCategoriesStep - Import completed:", {
      imported: result.imported,
      errors: result.errors.length,
      total: input.categories.length
    });

    return new StepResponse(result, result.categories.map(c => c.id));
  },
  async (createdCategoryIds: string[], { container }) => {
    console.log("BulkImportCategoriesStep - Compensating for categories:", createdCategoryIds);

    const supplierProductsServicesService: SupplierProductsServicesModuleService =
      container.resolve(SUPPLIER_PRODUCTS_SERVICES_MODULE);

    // Delete any categories that were created during this import
    for (const categoryId of createdCategoryIds) {
      try {
        await supplierProductsServicesService.deleteCategory(categoryId);
        console.log(`BulkImportCategoriesStep - Compensated category: ${categoryId}`);
      } catch (error) {
        console.error(`Failed to compensate category ${categoryId}:`, error);
      }
    }
  }
);

export const BulkImportCategoriesWorkflow = createWorkflow(
  "bulk-import-categories",
  (input: BulkImportCategoriesWorkflowInput) => {
    const validatedInput = validateCategoriesStep(input);
    const result = bulkImportCategoriesStep(validatedInput);
    
    return new WorkflowResponse(result);
  }
);
