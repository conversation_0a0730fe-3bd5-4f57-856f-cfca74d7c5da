import {
  StepResponse,
  WorkflowResponse,
  createStep,
  createWorkflow,
} from "@camped-ai/framework/workflows-sdk";
import DestinationModuleService from "src/modules/hotel-management/destination/service";
import { DESTINATION_MODULE } from "src/modules/hotel-management/destination";
import { emitEventStep } from "@camped-ai/medusa/core-flows";

export type UpdateDestinationStepInput = {
  id: string;
  name?: string;
  handle?: string;
  description?: string;
  is_active?: boolean;
  country?: string;
  location?: string;
  is_featured?: boolean;
  tags?: string[] | string;
  ai_content?: string;
  // New fields for currency/margin enhancement
  internal_web_link?: string;
  external_web_link?: string;
  currency_code?: string;
  margin?: number;
  faqs?: Array<{
    id?: string;
    question: string;
    answer: string;
  }>;
};

type UpdateDestinationWorkflowInput = UpdateDestinationStepInput;

export const updateDestinationStep = createStep(
  "update-destination-step",
  async (input: UpdateDestinationStepInput, { container }) => {
    const destinationModuleService: DestinationModuleService =
      container.resolve(DESTINATION_MODULE);

    const prevUpdatedDestination =
      await destinationModuleService.retrieveDestination(input.id);

    const updateData: any = { ...input };
    if (input.tags) {
      updateData.tags = Array.isArray(input.tags) ? input.tags : [input.tags];
    }

    // Remove FAQs from the main destination update - we'll handle them separately
    delete updateData.faqs;

    const updateDestination = await destinationModuleService.updateDestinations(
      updateData
    );

    // Handle FAQs if provided
    if (input.faqs !== undefined) {
      try {
        // Get existing FAQs from database
        const existingFaqs = await destinationModuleService.getDestinationFaqs(input.id);
        const existingFaqIds = new Set(existingFaqs.map(faq => faq.id));

        console.log('Existing FAQ IDs:', Array.from(existingFaqIds));
        console.log('Input FAQs:', input.faqs.map(f => ({ id: f.id, question: f.question })));

        // Separate FAQs by operation type
        const faqsToCreate = input.faqs.filter(faq => !faq.id); // No ID = new FAQ
        const faqsToUpdate = input.faqs.filter(faq => faq.id && existingFaqIds.has(faq.id)); // Has ID and exists = update
        const faqsToDelete = existingFaqs.filter(existing =>
          !input.faqs.some(inputFaq => inputFaq.id === existing.id)
        ); // Exists in DB but not in input = delete

        console.log(`FAQs to create: ${faqsToCreate.length}`);
        console.log(`FAQs to update: ${faqsToUpdate.length}`);
        console.log(`FAQs to delete: ${faqsToDelete.length}`);

        // 1. Delete FAQs that are no longer needed
        for (const faqToDelete of faqsToDelete) {
          try {
            console.log(`Deleting FAQ ${faqToDelete.id}`);
            await destinationModuleService.deleteDestinationFaq(faqToDelete.id);
          } catch (deleteError) {
            console.error(`Error deleting FAQ ${faqToDelete.id}:`, deleteError);
          }
        }

        // 2. Update existing FAQs
        for (const faqToUpdate of faqsToUpdate) {
          try {
            console.log(`Updating FAQ ${faqToUpdate.id}`);
            await destinationModuleService.updateDestinationFaq(faqToUpdate.id, {
              question: faqToUpdate.question.trim(),
              answer: faqToUpdate.answer.trim(),
            });
          } catch (updateError) {
            console.error(`Error updating FAQ ${faqToUpdate.id}:`, updateError);
          }
        }

        // 3. Create new FAQs
        for (const faqToCreate of faqsToCreate) {
          if (faqToCreate.question && faqToCreate.answer) {
            try {
              console.log(`Creating new FAQ: ${faqToCreate.question}`);
              const cleanFaq = {
                question: faqToCreate.question.trim(),
                answer: faqToCreate.answer.trim(),
                destination_id: input.id,
              };
              const createdFaq = await destinationModuleService.createDestinationFaq(cleanFaq);
              console.log(`Created FAQ ${createdFaq.id}`);
            } catch (createError) {
              console.error(`Error creating FAQ:`, createError);
            }
          }
        }

        console.log('FAQ operations completed successfully');
      } catch (faqError) {
        console.error("Error handling FAQs:", faqError);
        // Don't fail the entire update if FAQ handling fails
      }
    }

    return new StepResponse(updateDestination, prevUpdatedDestination);
  },
  async (prevUpdatedDestination, { container }) => {
    if (!prevUpdatedDestination) {
      return;
    }
    const destinationModuleService: DestinationModuleService =
      container.resolve(DESTINATION_MODULE);

    await destinationModuleService.updateDestinations(prevUpdatedDestination);
  }
);

export const UpdateDestinationWorkflow = createWorkflow(
  "update-destination",
  (input: UpdateDestinationWorkflowInput) => {
    // First, execute the update step
    const destination = updateDestinationStep(input);

    // Collect all changes in a single event
    const changes = [];

    // Check for active status change
    if (input.is_active !== undefined) {
      changes.push({
        type: "active_status",
        new_status: input.is_active
      });
    }

    // Check for featured status change
    if (input.is_featured !== undefined) {
      changes.push({
        type: "featured_status",
        new_status: input.is_featured
      });
    }



    // Only emit event if there are changes
    if (changes.length > 0) {
      emitEventStep({
        eventName: "destination.status_changed",
        data: {
          id: input.id,
          destination_name: destination.name,
          changes: changes
        },
      });
    }

    return new WorkflowResponse(destination);
  }
);
