import { IProductModuleService } from "@camped-ai/framework/types";
import { Modules } from "@camped-ai/framework/utils";
import {
  createStep,
  createWorkflow,
  StepResponse,
  WorkflowResponse,
} from "@camped-ai/framework/workflows-sdk";
import { HOTEL_MODULE } from "src/modules/hotel-management/hotel";
import HotelModuleService from "src/modules/hotel-management/hotel/service";
import { emitEventStep } from "@camped-ai/medusa/core-flows";

export type CreateHotelStepInput = {
  name: string;
  handle: string;
  description?: string;
  is_active?: boolean;
  website?: string;
  email?: string;
  destination_id: string;
  rating?: number;
  total_reviews?: number;
  notes?: string;
  location?: string;
  address?: string;
  phone_number?: string;
  timezone?: string;
  available_languages?: string[];
  tax_type?: string;
  tax_number?: string;
  tags?: string[];
  amenities?: string[];
  rules?: string[];
  safety_measures?: string[];
  currency?: string;
  margin?: number; // New margin field
  check_in_time?: string;
  check_out_time?: string;
  parent_category_id?: string;
  is_featured?: boolean;
  is_pets_allowed?: boolean;
  metadata?: Record<string, any>;
};

type CreateHotelWorkflowInput = CreateHotelStepInput;

export const createHotelStep = createStep(
  "create-hotel-step",
  async (input: CreateHotelStepInput, { container }) => {
    const hotelModuleService: HotelModuleService =
      container.resolve(HOTEL_MODULE);
    const productModuleService: IProductModuleService = container.resolve(
      Modules.PRODUCT
    );
    const category = await productModuleService.createProductCategories({
      name: input.name,
      handle: input.handle,
      description: input?.description,
      is_active: input?.is_active || true,
      parent_category_id: input?.parent_category_id || null,
    });
    const createData = { ...input };
    if (input.tags) {
      createData.tags = input.tags;
    }
    if (input.amenities) {
      createData.amenities = input.amenities;
    }
    if (input.rules) {
      createData.rules = input.rules;
    }
    if (input.safety_measures) {
      createData.safety_measures = input.safety_measures;
    }
    if (input.available_languages) {
      createData.available_languages = input.available_languages;
    }

    const hotel = await hotelModuleService.createHotels({
      ...createData,
      category_id: category.id,
      metadata: input.metadata,
    });
    return new StepResponse(hotel, hotel.id);
  },
  async (ids: string[], { container }) => {
    const hotelModuleService: HotelModuleService =
      container.resolve(HOTEL_MODULE);
    await hotelModuleService.deleteHotels(ids);
  }
);

export const CreateHotelWorkflow = createWorkflow(
  "create-hotel",
  (input: CreateHotelWorkflowInput) => {
    // First, create the hotel
    const hotel = createHotelStep(input);

    // Now emit the event with the hotel data
    emitEventStep({
      eventName: "hotel.created",
      data: {
        id: hotel.id,
        name: input.name,
        destination_id: input.destination_id,
        is_active: input.is_active || true,
        is_featured: input.is_featured || false,
      },
    });

    return new WorkflowResponse(hotel);
  }
);
