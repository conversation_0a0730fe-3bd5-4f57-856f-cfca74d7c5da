import { IProductModuleService } from "@camped-ai/framework/types";
import { Modules } from "@camped-ai/framework/utils";
import {
  createStep,
  createWorkflow,
  StepResponse,
  WorkflowResponse,
} from "@camped-ai/framework/workflows-sdk";
import { HOTEL_MODULE } from "src/modules/hotel-management/hotel";
import HotelModuleService from "src/modules/hotel-management/hotel/service";
import { emitEventStep } from "@camped-ai/medusa/core-flows";

export type UpdateHotelStepInput = {
  id: string;
  name: string;
  handle: string;
  description?: string;
  is_active?: boolean;
  is_internal?: boolean;
  website?: string;
  //   is_suite?: boolean;
  //   is_yeti_hotel?: boolean;
  //   is_creche_hotel?: boolean;
  star?: number;
  note?: string;
  accommodation_note?: string;
  message?: string;
  second_message?: string;
  //   offers_and_supplements?: string;
  parent_category_id?: string;
  is_featured?: boolean;
  destination_id?: string;
  rating?: number;
  total_reviews?: number;
  notes?: string;
  location?: string;
  address?: string;
  phone_number?: string;
  timezone?: string;
  available_languages?: string[];
  tax_type?: string;
  tax_number?: string;
  tags?: string[];
  amenities?: string[];
  rules?: string[];
  safety_measures?: string[];
  currency?: string;
  margin?: number; // New margin field
  check_in_time?: string;
  check_out_time?: string;
  is_pets_allowed?: boolean;
  ai_content?: string;
};

type UpdateHotelWorkflowInput = UpdateHotelStepInput;

export const updateHotelStep = createStep(
  "update-hotel-step",
  async (input: UpdateHotelStepInput, { container }) => {
    const hotelModuleService: HotelModuleService =
      container.resolve(HOTEL_MODULE);
    const productModuleService: IProductModuleService = container.resolve(
      Modules.PRODUCT
    );
    const prevUpdatedHotels = await hotelModuleService.retrieveHotel(input.id);
    const updateHotel = await hotelModuleService.updateHotels(input);
    const filteredCategoryUpdate = Object.fromEntries(
      Object.entries({
        name: input.name,
        handle: input.handle,
        description: input.description,
        is_active: input.is_active,
        is_internal: input.is_internal,
        parent_category_id: input.parent_category_id,
        is_featured: input.is_featured,
      }).filter(([_, value]) => value !== undefined)
    );
    await productModuleService.updateProductCategories(
      prevUpdatedHotels.category_id,
      filteredCategoryUpdate
    );

    return new StepResponse(updateHotel, prevUpdatedHotels);
  },
  async (prevUpdatedHotels, { container }) => {
    if (!prevUpdatedHotels) {
      return;
    }
    const hotelModuleService: HotelModuleService =
      container.resolve(HOTEL_MODULE);

    await hotelModuleService.updateHotels(prevUpdatedHotels);
  }
);

export const UpdateHotelWorkflow = createWorkflow(
  "update-hotel",
  (input: UpdateHotelWorkflowInput) => {
    // First, execute the update step
    const hotel = updateHotelStep(input);

    // Collect all changes in a single event
    const changes = [];

    // Check for active status change
    if (input.is_active !== undefined) {
      changes.push({
        type: "active_status",
        new_status: input.is_active,
      });
    }

    // Check for featured status change
    if (input.is_featured !== undefined) {
      changes.push({
        type: "featured_status",
        new_status: input.is_featured,
      });
    }

    // Only emit event if there are changes
    if (changes.length > 0) {
      emitEventStep({
        eventName: "hotel.status_changed",
        data: {
          id: input.id,
          hotel_name: input.name,
          changes: changes,
        },
      });
    }

    return new WorkflowResponse(hotel);
  }
);
