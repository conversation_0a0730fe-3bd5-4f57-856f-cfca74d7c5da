import {
  createStep,
  createWorkflow,
  StepResponse,
  WorkflowResponse,
} from "@camped-ai/framework/workflows-sdk";
import { MedusaError, Modules } from "@camped-ai/framework/utils";
import { IProductModuleService } from "@camped-ai/framework/types";

export type LinkProductToCategoryStepInput = {
  product_id: string;
  category_id: string;
};

type LinkProductToCategoryWorkflowInput = LinkProductToCategoryStepInput;

/**
 * Step to validate that both product and category exist
 */
export const validateProductAndCategoryStep = createStep(
  "validate-product-and-category",
  async (input: LinkProductToCategoryStepInput, { container }) => {
    const productModuleService: IProductModuleService = container.resolve(
      Modules.PRODUCT
    );

    console.log(`Validating product ${input.product_id} and category ${input.category_id}`);

    // Validate product exists
    try {
      const product = await productModuleService.retrieveProduct(input.product_id);
      if (!product) {
        throw new MedusaError(
          MedusaError.Types.NOT_FOUND,
          `Product with ID ${input.product_id} not found`
        );
      }
      console.log(`Product validated: ${product.title}`);
    } catch (error) {
      throw new MedusaError(
        MedusaError.Types.NOT_FOUND,
        `Product with ID ${input.product_id} not found: ${error.message}`
      );
    }

    // Validate category exists
    try {
      const category = await productModuleService.retrieveProductCategory(input.category_id);
      if (!category) {
        throw new MedusaError(
          MedusaError.Types.NOT_FOUND,
          `Product category with ID ${input.category_id} not found`
        );
      }
      console.log(`Category validated: ${category.name}`);
    } catch (error) {
      throw new MedusaError(
        MedusaError.Types.NOT_FOUND,
        `Product category with ID ${input.category_id} not found: ${error.message}`
      );
    }

    return new StepResponse(input);
  }
);

/**
 * Step to check if product is already linked to the category
 */
export const checkExistingLinkStep = createStep(
  "check-existing-link",
  async (input: LinkProductToCategoryStepInput, { container }) => {
    const productModuleService: IProductModuleService = container.resolve(
      Modules.PRODUCT
    );

    console.log(`Checking if product ${input.product_id} is already linked to category ${input.category_id}`);

    try {
      // Get the product with its categories
      const product = await productModuleService.retrieveProduct(input.product_id, {
        relations: ["categories"],
      });

      // Check if the product is already linked to this category
      const isAlreadyLinked = product.categories?.some(
        (category: any) => category.id === input.category_id
      );

      if (isAlreadyLinked) {
        console.log(`Product ${input.product_id} is already linked to category ${input.category_id}`);
        return new StepResponse({ ...input, alreadyLinked: true });
      }

      console.log(`Product ${input.product_id} is not linked to category ${input.category_id}`);
      return new StepResponse({ ...input, alreadyLinked: false });
    } catch (error) {
      console.error(`Error checking existing link: ${error.message}`);
      // If we can't check, assume it's not linked and proceed
      return new StepResponse({ ...input, alreadyLinked: false });
    }
  }
);

/**
 * Step to link the product to the category
 */
export const linkProductToCategoryStep = createStep(
  "link-product-to-category",
  async (input: LinkProductToCategoryStepInput & { alreadyLinked: boolean }, { container }) => {
    const productModuleService: IProductModuleService = container.resolve(
      Modules.PRODUCT
    );

    // Skip if already linked
    if (input.alreadyLinked) {
      console.log(`Skipping link - product ${input.product_id} already linked to category ${input.category_id}`);
      return new StepResponse({
        product_id: input.product_id,
        category_id: input.category_id,
        linked: false,
        reason: "already_linked",
      });
    }

    console.log(`Linking product ${input.product_id} to category ${input.category_id}`);

    try {
      // Use the updateProducts method to add the product to the category
      await productModuleService.updateProducts(input.product_id, {
        categories: [{ id: input.category_id }],
      });

      console.log(`Successfully linked product ${input.product_id} to category ${input.category_id}`);

      return new StepResponse({
        product_id: input.product_id,
        category_id: input.category_id,
        linked: true,
        reason: "newly_linked",
      });
    } catch (error) {
      console.error(`Error linking product to category: ${error.message}`);
      throw new MedusaError(
        MedusaError.Types.DB_ERROR,
        `Failed to link product ${input.product_id} to category ${input.category_id}: ${error.message}`
      );
    }
  },
  // Compensation function to remove the link if the workflow fails later
  async (result: any, { container }) => {
    if (result?.linked && result?.reason === "newly_linked") {
      console.log(`Compensating: Removing link between product ${result.product_id} and category ${result.category_id}`);
      
      const productModuleService: IProductModuleService = container.resolve(
        Modules.PRODUCT
      );

      try {
        // Remove the category from the product
        await productModuleService.updateProducts(result.product_id, {
          categories: [],
        });
        console.log(`Compensation successful: Removed link between product ${result.product_id} and category ${result.category_id}`);
      } catch (error) {
        console.error(`Compensation failed: ${error.message}`);
      }
    }
  }
);

/**
 * Workflow to link a Room Type (Product) to a Hotel (Product Category)
 */
export const LinkProductToCategoryWorkflow = createWorkflow(
  "link-product-to-category",
  (input: LinkProductToCategoryWorkflowInput) => {
    // Step 1: Validate that both product and category exist
    const validatedInput = validateProductAndCategoryStep(input);

    // Step 2: Check if the product is already linked to the category
    const linkCheckResult = checkExistingLinkStep(validatedInput);

    // Step 3: Link the product to the category if not already linked
    const linkResult = linkProductToCategoryStep(linkCheckResult);

    return new WorkflowResponse(linkResult);
  }
);

export default LinkProductToCategoryWorkflow;
