import { MedusaContainer } from "@camped-ai/framework/types";
import {
  BOOKING_ADD_ONS_MODULE,
  BOOKING_ADD_ONS_SERVICE,
} from "../modules/booking-add-ons";
import BookingAddOnService from "../modules/booking-add-ons/service";

/**
 * Register the Booking Add-ons Module in the dependency injection container
 */
export default async (container: MedusaContainer): Promise<void> => {
  try {
    console.log("🚀 Starting Booking Add-ons Module registration...");

    // Register the module
    if (!container.hasRegistration(BOOKING_ADD_ONS_MODULE)) {
      container.register({
        [BOOKING_ADD_ONS_MODULE]: {
          resolve: () => new BookingAddOnService(container),
        },
      });
      console.log("✅ Booking Add-ons Module registered successfully");
    }

    // Register the service
    if (!container.hasRegistration(BOOKING_ADD_ONS_SERVICE)) {
      container.register({
        [BOOKING_ADD_ONS_SERVICE]: {
          resolve: () => new BookingAddOnService(container),
        },
      });
      console.log("✅ Booking Add-ons Service registered successfully");
    }

    // Also register with legacy name for backward compatibility
    if (!container.hasRegistration("bookingAddOnService")) {
      container.register({
        bookingAddOnService: {
          resolve: () => new BookingAddOnService(container),
        },
      });
      console.log("✅ Booking Add-ons Service registered with legacy name");
    }
  } catch (error) {
    console.error("❌ Failed to register Booking Add-ons Module:", error);
    throw error;
  }
};
