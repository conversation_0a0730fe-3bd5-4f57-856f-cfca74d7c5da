import { MedusaContainer } from "@camped-ai/framework/types";
import { asClass } from "awilix";
import { CONCIERGE_MANAGEMENT_MODULE, ITINERARY_SERVICE } from "../modules/concierge-management";
import ConciergeManagementService from "../modules/concierge-management/service";
import ItineraryService from "../modules/concierge-management/itinerary-service";
import ConciergeOrderService from "../modules/concierge-management/concierge-order-service";
import ConciergeOrderItemService from "../modules/concierge-management/concierge-order-item-service";

/**
 * Register the Concierge Management Module and its services in the dependency injection container
 * This ensures all concierge-related services are available even if the module registration fails
 */
export default async (container: MedusaContainer): Promise<void> => {
  try {
    console.log("🚀 Starting Concierge Management Module registration...");

    // Register the main concierge management module
    if (!container.hasRegistration(CONCIERGE_MANAGEMENT_MODULE)) {
      container.register({
        [CONCIERGE_MANAGEMENT_MODULE]: asClass(ConciergeManagementService).singleton(),
      });
      console.log("✅ Concierge Management Module registered successfully");
    } else {
      console.log("ℹ️ Concierge Management Module already registered");
    }

    // Register the itinerary service
    if (!container.hasRegistration(ITINERARY_SERVICE)) {
      container.register({
        [ITINERARY_SERVICE]: asClass(ItineraryService).singleton(),
      });
      console.log("✅ Itinerary Service registered successfully");
    } else {
      console.log("ℹ️ Itinerary Service already registered");
    }

    // Also register with the exact string for backward compatibility
    if (!container.hasRegistration("itineraryService")) {
      container.register({
        itineraryService: asClass(ItineraryService).singleton(),
      });
      console.log("✅ Itinerary Service registered with string key");
    } else {
      console.log("ℹ️ Itinerary Service with string key already registered");
    }

    // Register the concierge management service with string key for backward compatibility
    if (!container.hasRegistration("conciergeManagementService")) {
      container.register({
        conciergeManagementService: asClass(ConciergeManagementService).singleton(),
      });
      console.log("✅ Concierge Management Service registered with string key");
    } else {
      console.log("ℹ️ Concierge Management Service with string key already registered");
    }

    // Register the concierge order service
    if (!container.hasRegistration("conciergeOrderService")) {
      container.register({
        conciergeOrderService: asClass(ConciergeOrderService).singleton(),
      });
      console.log("✅ Concierge Order Service registered");
    } else {
      console.log("ℹ️ Concierge Order Service already registered");
    }

    // Register the concierge order item service
    if (!container.hasRegistration("conciergeOrderItemService")) {
      container.register({
        conciergeOrderItemService: asClass(ConciergeOrderItemService).singleton(),
      });
      console.log("✅ Concierge Order Item Service registered");
    } else {
      console.log("ℹ️ Concierge Order Item Service already registered");
    }

  } catch (error) {
    console.error("❌ Failed to register Concierge Management Module:", error);
    throw error;
  }
};
