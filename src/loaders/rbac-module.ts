import { MedusaContainer } from "@camped-ai/framework/types";
import { RBAC_MODULE } from "../modules/rbac";
import RbacModuleService from "../modules/rbac/service";

/**
 * Manually register the RBAC module and its services
 * This ensures the services are available even if the module registration fails
 */
export default async (container: MedusaContainer): Promise<void> => {
  try {
    // Register the RBAC service
    if (!container.hasRegistration(RBAC_MODULE)) {
      container.register({
        [RBAC_MODULE]: {
          resolve: (container) => new RbacModuleService(container),
        },
      });
      console.log("✅ RBAC module registered successfully");
    } else {
      console.log("ℹ️ RBAC module already registered");
    }
  } catch (error) {
    console.error("❌ Failed to register RBAC module:", error);
    throw error;
  }
};
