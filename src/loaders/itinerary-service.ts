import { MedusaContainer } from "@camped-ai/framework/types";
import { asClass } from "awilix";
import ItineraryService from "../modules/concierge-management/itinerary-service";
import { ITINERARY_SERVICE } from "../modules/concierge-management";

/**
 * Register the Itinerary Service in the dependency injection container
 * This loader ensures the service is available with both constant and string keys
 */
export default async (container: MedusaContainer): Promise<void> => {
  try {
    console.log("🚀 Starting Itinerary Service registration...");

    // Register the itinerary service using the constant
    if (!container.hasRegistration(ITINERARY_SERVICE)) {
      container.register({
        [ITINERARY_SERVICE]: asClass(ItineraryService).singleton(),
      });
      console.log("✅ Itinerary Service registered with constant key");
    } else {
      console.log("ℹ️ Itinerary Service with constant key already registered");
    }

    // Also register with the exact string for maximum compatibility
    if (!container.hasRegistration("itineraryService")) {
      container.register({
        itineraryService: asClass(ItineraryService).singleton(),
      });
      console.log("✅ Itinerary Service registered with string key");
    } else {
      console.log("ℹ️ Itinerary Service with string key already registered");
    }

    // Verify registration
    const hasConstantKey = container.hasRegistration(ITINERARY_SERVICE);
    const hasStringKey = container.hasRegistration("itineraryService");
    console.log(`🔍 Registration verification - Constant key: ${hasConstantKey}, String key: ${hasStringKey}`);

  } catch (error) {
    console.error("❌ Failed to register Itinerary Service:", error);
    throw error;
  }
};
