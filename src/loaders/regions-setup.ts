import { MedusaContainer } from "@camped-ai/framework/types";
import { Modules } from "@camped-ai/framework/utils";

/**
 * Regions Setup Loader
 * 
 * This loader ensures that essential regions and currencies are created
 * during application startup. This runs BEFORE any cart creation can happen.
 */

interface RegionConfig {
  name: string;
  currency_code: string;
  countries: string[];
  tax_rate?: number;
  is_default?: boolean;
}

const DEFAULT_REGIONS: RegionConfig[] = [
  {
    name: "Switzerland",
    currency_code: "CHF",
    countries: ["ch"],
    tax_rate: 7.7,
    is_default: true
  },
  {
    name: "European Union",
    currency_code: "EUR", 
    countries: ["de", "fr", "it", "at"],
    tax_rate: 20.0,
    is_default: false
  },
  {
    name: "United States",
    currency_code: "USD",
    countries: ["us"],
    tax_rate: 8.5,
    is_default: false
  }
];

const DEFAULT_CURRENCIES = [
  { code: "CHF", name: "Swiss Franc" },
  { code: "EUR", name: "Euro" },
  { code: "USD", name: "US Dollar" },
  { code: "GBP", name: "British Pound" }
];

export default async (container: MedusaContainer): Promise<void> => {
  try {
    console.log("🏗️ Setting up regions and currencies...");

    // Step 1: Ensure currencies exist
    await ensureCurrenciesExist(container);
    
    // Step 2: Ensure regions exist
    await ensureRegionsExist(container);
    
    console.log("✅ Regions and currencies setup completed");
    
  } catch (error) {
    console.error("❌ Failed to setup regions:", error);
    // Don't throw - let the application start, but log the issue
    console.warn("⚠️ Application will start without proper region setup. Cart creation may fail.");
  }
};

async function ensureCurrenciesExist(container: MedusaContainer): Promise<void> {
  try {
    // Note: In Medusa v2, currencies are typically managed at the database level
    // This is a placeholder for currency setup logic
    console.log("💰 Checking currencies...");
    
    for (const currency of DEFAULT_CURRENCIES) {
      console.log(`   - ${currency.code}: ${currency.name}`);
    }
    
    // TODO: Implement actual currency creation if needed
    // Most Medusa installations have currencies pre-configured
    
  } catch (error) {
    console.error("Failed to setup currencies:", error);
  }
}

async function ensureRegionsExist(container: MedusaContainer): Promise<void> {
  try {
    const regionModuleService = container.resolve(Modules.REGION);
    
    // Check if any regions exist
    const existingRegions = await regionModuleService.listRegions({}, { take: 1 });
    
    if (existingRegions.length > 0) {
      console.log(`🌍 Found ${existingRegions.length} existing regions, skipping setup`);
      return;
    }
    
    console.log("🌍 No regions found, creating default regions...");
    
    // Create default regions
    for (const regionConfig of DEFAULT_REGIONS) {
      try {
        const region = await regionModuleService.createRegions({
          name: regionConfig.name,
          currency_code: regionConfig.currency_code,
          countries: regionConfig.countries,
          metadata: {
            tax_rate: regionConfig.tax_rate,
            is_default: regionConfig.is_default,
            created_by: "system_loader"
          }
        });
        
        console.log(`   ✅ Created region: ${region.name} (${region.currency_code})`);
        
      } catch (error) {
        console.error(`   ❌ Failed to create region ${regionConfig.name}:`, error.message);
      }
    }
    
  } catch (error) {
    console.error("Failed to setup regions:", error);
    throw error;
  }
}

/**
 * Helper function to get the default region ID
 * This can be used by other parts of the application
 */
export async function getDefaultRegionId(container: MedusaContainer): Promise<string | null> {
  try {
    const regionModuleService = container.resolve(Modules.REGION);
    
    // Look for default region
    const defaultRegions = await regionModuleService.listRegions({
      metadata: { is_default: true }
    }, { take: 1 });
    
    if (defaultRegions.length > 0) {
      return defaultRegions[0].id;
    }
    
    // Fallback to first available region
    const anyRegions = await regionModuleService.listRegions({}, { take: 1 });
    return anyRegions.length > 0 ? anyRegions[0].id : null;
    
  } catch (error) {
    console.error("Failed to get default region ID:", error);
    return null;
  }
}

/**
 * Helper function to validate if regions are properly set up
 */
export async function validateRegionSetup(container: MedusaContainer): Promise<boolean> {
  try {
    const regionModuleService = container.resolve(Modules.REGION);
    const regions = await regionModuleService.listRegions({}, { take: 1 });
    
    if (regions.length === 0) {
      console.error("❌ No regions found! Cart creation will fail.");
      return false;
    }
    
    console.log("✅ Region setup is valid");
    return true;
    
  } catch (error) {
    console.error("❌ Failed to validate region setup:", error);
    return false;
  }
}
