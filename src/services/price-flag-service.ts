import { MedusaService, MedusaError } from "@camped-ai/framework/utils";
import { SUPPLIER_PRODUCTS_SERVICES_MODULE } from "../modules/supplier-products-services/index";
import { ADD_ON_SERVICE } from "../modules/hotel-management/add-on-service/index";

export interface PriceFlagData {
  productServiceId: string;
  supplierOfferingId: string;
  currentBasePrice: number | null;
  currentBaseCurrency: string | null;
  newHighestPrice: number;
  newHighestCurrency: string;
  priceIncrease: number;
  priceIncreasePercent: number;
  reason: string;
}

export interface PriceFlagResult {
  flagCreated: boolean;
  flagUpdated: boolean;
  productServiceFlag: any;
  addOnFlags: any[];
  message: string;
}

export interface PriceFlagStats {
  totalActiveFlags: number;
  totalFlagsToday: number;
  totalFlagsThisWeek: number;
  averagePriceIncrease: number;
  flagsByCategory: Record<string, number>;
  flagsByCurrency: Record<string, number>;
}

/**
 * Service for managing price flags when supplier offerings exceed base prices
 */
class PriceFlagService extends MedusaService({}) {
  protected supplierProductsServicesModuleService_: any;
  protected addOnService_: any;

  constructor(container: any) {
    super(container);
    this.supplierProductsServicesModuleService_ = container[SUPPLIER_PRODUCTS_SERVICES_MODULE];
    this.addOnService_ = container[ADD_ON_SERVICE];
  }

  /**
   * Create a price flag for a product service
   */
  async createPriceFlag(flagData: PriceFlagData): Promise<PriceFlagResult> {
    try {
      const { productServiceId, supplierOfferingId, newHighestPrice, newHighestCurrency } = flagData;

      // Update the product service with price flag information
      const updatedProductService = await this.supplierProductsServicesModuleService_.updateProductService(
        productServiceId,
        {
          price_flag_active: true,
          price_flag_created_at: new Date(),
          price_flag_supplier_offering_id: supplierOfferingId,
          highest_price: newHighestPrice,
          highest_price_currency: newHighestCurrency,
        }
      );

      // Propagate flag to corresponding add-ons
      const addOnFlags = await this.propagateFlagToAddOns(productServiceId, flagData);

      console.log(`Created price flag for product service ${productServiceId}: ${newHighestPrice} ${newHighestCurrency}`);

      return {
        flagCreated: true,
        flagUpdated: false,
        productServiceFlag: updatedProductService,
        addOnFlags,
        message: `Price flag created: New highest price ${newHighestPrice} ${newHighestCurrency} (${flagData.priceIncreasePercent.toFixed(1)}% increase)`,
      };
    } catch (error) {
      throw new MedusaError(
        MedusaError.Types.UNEXPECTED_STATE,
        `Failed to create price flag: ${error.message}`
      );
    }
  }

  /**
   * Update an existing price flag
   */
  async updatePriceFlag(productServiceId: string, flagData: PriceFlagData): Promise<PriceFlagResult> {
    try {
      const { supplierOfferingId, newHighestPrice, newHighestCurrency } = flagData;

      // Update the product service with new highest price information
      const updatedProductService = await this.supplierProductsServicesModuleService_.updateProductService(
        productServiceId,
        {
          price_flag_supplier_offering_id: supplierOfferingId,
          highest_price: newHighestPrice,
          highest_price_currency: newHighestCurrency,
        }
      );

      // Update corresponding add-ons
      const addOnFlags = await this.propagateFlagToAddOns(productServiceId, flagData);

      console.log(`Updated price flag for product service ${productServiceId}: ${newHighestPrice} ${newHighestCurrency}`);

      return {
        flagCreated: false,
        flagUpdated: true,
        productServiceFlag: updatedProductService,
        addOnFlags,
        message: `Price flag updated: New highest price ${newHighestPrice} ${newHighestCurrency} (${flagData.priceIncreasePercent.toFixed(1)}% increase)`,
      };
    } catch (error) {
      throw new MedusaError(
        MedusaError.Types.UNEXPECTED_STATE,
        `Failed to update price flag: ${error.message}`
      );
    }
  }

  /**
   * Remove/dismiss a price flag
   */
  async dismissPriceFlag(productServiceId: string, reason: string = 'manually_dismissed'): Promise<{
    dismissed: boolean;
    productService: any;
    addOnsUpdated: number;
    message: string;
  }> {
    try {
      // Clear the price flag from the product service
      const updatedProductService = await this.supplierProductsServicesModuleService_.updateProductService(
        productServiceId,
        {
          price_flag_active: false,
          price_flag_created_at: null,
          price_flag_supplier_offering_id: null,
        }
      );

      // Clear flags from corresponding add-ons
      const addOnsUpdated = await this.clearFlagsFromAddOns(productServiceId);

      console.log(`Dismissed price flag for product service ${productServiceId}, reason: ${reason}`);

      return {
        dismissed: true,
        productService: updatedProductService,
        addOnsUpdated,
        message: `Price flag dismissed for product service ${productServiceId}`,
      };
    } catch (error) {
      throw new MedusaError(
        MedusaError.Types.UNEXPECTED_STATE,
        `Failed to dismiss price flag: ${error.message}`
      );
    }
  }

  /**
   * Propagate price flag to corresponding add-ons
   */
  private async propagateFlagToAddOns(productServiceId: string, flagData: PriceFlagData): Promise<any[]> {
    try {
      // Find add-ons that are synced from this product service
      const addOns = await this.addOnService_.listAddOnServices({
        metadata: {
          source_product_service_id: productServiceId
        }
      });

      const updatedAddOns = [];

      for (const addOn of addOns) {
        try {
          const updatedAddOn = await this.addOnService_.updateAddOnService(addOn.id, {
            price_flag_active: true,
            price_flag_created_at: new Date().toISOString(),
            price_flag_supplier_offering_id: flagData.supplierOfferingId,
            highest_price: flagData.newHighestPrice,
            highest_price_currency: flagData.newHighestCurrency,
          });

          updatedAddOns.push(updatedAddOn);
          console.log(`Propagated price flag to add-on ${addOn.id} from product service ${productServiceId}`);
        } catch (error) {
          console.error(`Failed to update add-on ${addOn.id} with price flag:`, error);
        }
      }

      return updatedAddOns;
    } catch (error) {
      console.error(`Failed to propagate price flag to add-ons for product service ${productServiceId}:`, error);
      return [];
    }
  }

  /**
   * Get all add-ons affected by a product service price flag
   */
  async getAffectedAddOns(productServiceId: string): Promise<{
    addOns: any[];
    totalCount: number;
    flaggedCount: number;
    unflaggedCount: number;
  }> {
    try {
      // Find add-ons that are synced from this product service
      const addOns = await this.addOnService_.listAddOnServices({
        metadata: {
          source_product_service_id: productServiceId
        }
      });

      const flaggedAddOns = addOns.filter((addOn: any) =>
        addOn.metadata?.price_flag_active === true
      );

      return {
        addOns,
        totalCount: addOns.length,
        flaggedCount: flaggedAddOns.length,
        unflaggedCount: addOns.length - flaggedAddOns.length,
      };
    } catch (error) {
      console.error(`Failed to get affected add-ons for product service ${productServiceId}:`, error);
      return {
        addOns: [],
        totalCount: 0,
        flaggedCount: 0,
        unflaggedCount: 0,
      };
    }
  }

  /**
   * Sync price flag status between product service and add-ons
   */
  async syncPriceFlagStatus(productServiceId: string): Promise<{
    synced: boolean;
    productServiceFlag: boolean;
    addOnsUpdated: number;
    errors: string[];
  }> {
    try {
      // Get the product service flag status
      const productService = await this.supplierProductsServicesModuleService_.retrieveProductService(
        productServiceId
      );

      const productServiceFlag = productService.price_flag_active || false;
      const errors: string[] = [];
      let addOnsUpdated = 0;

      // Find all related add-ons
      const addOns = await this.addOnService_.listAddOnServices({
        metadata: {
          source_product_service_id: productServiceId
        }
      });

      // Update each add-on to match the product service flag status
      for (const addOn of addOns) {
        try {
          const currentFlag = addOn.metadata?.price_flag_active === true;

          if (currentFlag !== productServiceFlag) {
            await this.addOnService_.updateAddOnService(addOn.id, {
              price_flag_active: productServiceFlag,
              price_flag_created_at: productServiceFlag ? productService.price_flag_created_at?.toISOString() : null,
              price_flag_supplier_offering_id: productServiceFlag ? productService.price_flag_supplier_offering_id : null,
              highest_price: productServiceFlag ? productService.highest_price : null,
              highest_price_currency: productServiceFlag ? productService.highest_price_currency : null,
            });

            addOnsUpdated++;
          }
        } catch (error) {
          errors.push(`Failed to sync add-on ${addOn.id}: ${error.message}`);
        }
      }

      return {
        synced: errors.length === 0,
        productServiceFlag,
        addOnsUpdated,
        errors,
      };
    } catch (error) {
      return {
        synced: false,
        productServiceFlag: false,
        addOnsUpdated: 0,
        errors: [`Failed to sync price flag status: ${error.message}`],
      };
    }
  }

  /**
   * Clear price flags from corresponding add-ons
   */
  private async clearFlagsFromAddOns(productServiceId: string): Promise<number> {
    try {
      // Find add-ons that are synced from this product service
      const addOns = await this.addOnService_.listAddOnServices({
        metadata: {
          source_product_service_id: productServiceId
        }
      });

      let updatedCount = 0;

      for (const addOn of addOns) {
        try {
          await this.addOnService_.updateAddOnService(addOn.id, {
            price_flag_active: false,
            price_flag_created_at: null,
            price_flag_supplier_offering_id: null,
          });

          updatedCount++;
        } catch (error) {
          console.error(`Failed to clear price flag from add-on ${addOn.id}:`, error);
        }
      }

      return updatedCount;
    } catch (error) {
      console.error(`Failed to clear price flags from add-ons for product service ${productServiceId}:`, error);
      return 0;
    }
  }

  /**
   * Get all active price flags
   */
  async getActivePriceFlags(options: {
    limit?: number;
    offset?: number;
    currency?: string;
    category?: string;
    sortBy?: 'created_at' | 'price_increase' | 'name';
    sortOrder?: 'asc' | 'desc';
  } = {}): Promise<{
    flags: any[];
    total: number;
    hasMore: boolean;
  }> {
    try {
      const { limit = 50, offset = 0, sortBy = 'created_at', sortOrder = 'desc' } = options;

      // Get product services with active price flags
      const productServices = await this.supplierProductsServicesModuleService_.listProductServices({
        price_flag_active: true,
        ...(options.currency && { highest_price_currency: options.currency }),
        limit,
        offset,
        order: { [sortBy]: sortOrder },
      });

      const flags = productServices.map((ps: any) => ({
        id: ps.id,
        name: ps.name,
        type: ps.type,
        currentBasePrice: ps.base_cost,
        highestPrice: ps.highest_price,
        highestPriceCurrency: ps.highest_price_currency,
        priceIncrease: ps.highest_price - (ps.base_cost || 0),
        priceIncreasePercent: ps.base_cost ? ((ps.highest_price - ps.base_cost) / ps.base_cost) * 100 : 0,
        flagCreatedAt: ps.price_flag_created_at,
        supplierOfferingId: ps.price_flag_supplier_offering_id,
        category: ps.category?.name,
      }));

      // Get total count for pagination
      const totalCount = await this.supplierProductsServicesModuleService_.countProductServices({
        price_flag_active: true,
        ...(options.currency && { highest_price_currency: options.currency }),
      });

      return {
        flags,
        total: totalCount,
        hasMore: offset + flags.length < totalCount,
      };
    } catch (error) {
      throw new MedusaError(
        MedusaError.Types.UNEXPECTED_STATE,
        `Failed to get active price flags: ${error.message}`
      );
    }
  }

  /**
   * Check if a price flag should be triggered and create/update it if needed
   */
  async checkAndTriggerPriceFlag(
    productServiceId: string,
    supplierOfferingId: string,
    priceComparisonResult: any
  ): Promise<PriceFlagResult | null> {
    try {
      if (!priceComparisonResult.shouldFlag) {
        return null;
      }

      const flagData: PriceFlagData = {
        productServiceId,
        supplierOfferingId,
        currentBasePrice: priceComparisonResult.currentBasePrice,
        currentBaseCurrency: priceComparisonResult.currentBaseCurrency,
        newHighestPrice: priceComparisonResult.supplierPrice,
        newHighestCurrency: priceComparisonResult.supplierCurrency,
        priceIncrease: priceComparisonResult.priceIncrease,
        priceIncreasePercent: priceComparisonResult.priceIncreasePercent,
        reason: `New highest price from supplier offering ${supplierOfferingId}`,
      };

      // Check if a flag already exists
      const productService = await this.supplierProductsServicesModuleService_.retrieveProductService(
        productServiceId
      );

      if (productService.price_flag_active) {
        // Update existing flag if this is a higher price
        if (priceComparisonResult.isNewHighestPrice) {
          return await this.updatePriceFlag(productServiceId, flagData);
        }
        return null; // No update needed
      } else {
        // Create new flag
        return await this.createPriceFlag(flagData);
      }
    } catch (error) {
      throw new MedusaError(
        MedusaError.Types.UNEXPECTED_STATE,
        `Failed to check and trigger price flag: ${error.message}`
      );
    }
  }

  /**
   * Bulk check and trigger price flags for multiple product services
   */
  async bulkCheckAndTriggerPriceFlags(
    checks: Array<{
      productServiceId: string;
      supplierOfferingId: string;
      priceComparisonResult: any;
    }>
  ): Promise<{
    flagsCreated: number;
    flagsUpdated: number;
    errors: Array<{ productServiceId: string; error: string }>;
    results: PriceFlagResult[];
  }> {
    const results: PriceFlagResult[] = [];
    const errors: Array<{ productServiceId: string; error: string }> = [];
    let flagsCreated = 0;
    let flagsUpdated = 0;

    for (const check of checks) {
      try {
        const result = await this.checkAndTriggerPriceFlag(
          check.productServiceId,
          check.supplierOfferingId,
          check.priceComparisonResult
        );

        if (result) {
          results.push(result);
          if (result.flagCreated) flagsCreated++;
          if (result.flagUpdated) flagsUpdated++;
        }
      } catch (error) {
        errors.push({
          productServiceId: check.productServiceId,
          error: error.message,
        });
      }
    }

    return {
      flagsCreated,
      flagsUpdated,
      errors,
      results,
    };
  }

  /**
   * Check if price flag should be triggered based on configurable thresholds
   */
  async shouldTriggerFlag(
    priceIncreasePercent: number,
    priceIncrease: number,
    productServiceId: string
  ): Promise<{
    shouldTrigger: boolean;
    reason: string;
    thresholdUsed: string;
  }> {
    try {
      // Get product service to check category-specific rules
      const productService = await this.supplierProductsServicesModuleService_.retrieveProductService(
        productServiceId,
        { relations: ["category"] }
      );

      // Default thresholds - these could be made configurable
      const defaultThresholds = {
        percentageThreshold: 0, // Any increase triggers flag
        absoluteThreshold: 0, // Any increase triggers flag
        minimumBasePrice: 1, // Only flag if base price is at least 1 unit
      };

      // Category-specific thresholds (could be stored in database)
      const categoryThresholds: Record<string, any> = {
        'premium': { percentageThreshold: 5, absoluteThreshold: 10 },
        'budget': { percentageThreshold: 10, absoluteThreshold: 5 },
        'luxury': { percentageThreshold: 2, absoluteThreshold: 20 },
      };

      const category = productService.category?.name?.toLowerCase();
      const thresholds = categoryThresholds[category] || defaultThresholds;

      // Check if base price meets minimum requirement
      if (productService.base_cost && productService.base_cost < thresholds.minimumBasePrice) {
        return {
          shouldTrigger: false,
          reason: `Base price ${productService.base_cost} below minimum threshold ${thresholds.minimumBasePrice}`,
          thresholdUsed: 'minimum_base_price',
        };
      }

      // Check percentage threshold
      if (priceIncreasePercent < thresholds.percentageThreshold) {
        return {
          shouldTrigger: false,
          reason: `Price increase ${priceIncreasePercent.toFixed(1)}% below threshold ${thresholds.percentageThreshold}%`,
          thresholdUsed: 'percentage_threshold',
        };
      }

      // Check absolute threshold
      if (priceIncrease < thresholds.absoluteThreshold) {
        return {
          shouldTrigger: false,
          reason: `Price increase ${priceIncrease} below absolute threshold ${thresholds.absoluteThreshold}`,
          thresholdUsed: 'absolute_threshold',
        };
      }

      return {
        shouldTrigger: true,
        reason: `Price increase ${priceIncreasePercent.toFixed(1)}% (${priceIncrease}) exceeds thresholds`,
        thresholdUsed: category ? `category_${category}` : 'default',
      };
    } catch (error) {
      // Default to triggering flag if we can't check thresholds
      return {
        shouldTrigger: true,
        reason: `Error checking thresholds: ${error.message}`,
        thresholdUsed: 'error_fallback',
      };
    }
  }

  /**
   * Get price flag statistics
   */
  async getPriceFlagStats(): Promise<PriceFlagStats> {
    try {
      const now = new Date();
      const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
      const weekAgo = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);

      // Get all active flags
      const activeFlags = await this.supplierProductsServicesModuleService_.listProductServices({
        price_flag_active: true,
      });

      // Get flags created today
      const flagsToday = await this.supplierProductsServicesModuleService_.listProductServices({
        price_flag_active: true,
        price_flag_created_at: { $gte: today },
      });

      // Get flags created this week
      const flagsThisWeek = await this.supplierProductsServicesModuleService_.listProductServices({
        price_flag_active: true,
        price_flag_created_at: { $gte: weekAgo },
      });

      // Calculate average price increase
      const priceIncreases = activeFlags
        .filter((ps: any) => ps.base_cost && ps.highest_price)
        .map((ps: any) => ((ps.highest_price - ps.base_cost) / ps.base_cost) * 100);

      const averagePriceIncrease = priceIncreases.length > 0
        ? priceIncreases.reduce((sum: number, increase: number) => sum + increase, 0) / priceIncreases.length
        : 0;

      // Group by category
      const flagsByCategory: Record<string, number> = {};
      activeFlags.forEach((ps: any) => {
        const category = ps.category?.name || 'Uncategorized';
        flagsByCategory[category] = (flagsByCategory[category] || 0) + 1;
      });

      // Group by currency
      const flagsByCurrency: Record<string, number> = {};
      activeFlags.forEach((ps: any) => {
        const currency = ps.highest_price_currency || 'Unknown';
        flagsByCurrency[currency] = (flagsByCurrency[currency] || 0) + 1;
      });

      return {
        totalActiveFlags: activeFlags.length,
        totalFlagsToday: flagsToday.length,
        totalFlagsThisWeek: flagsThisWeek.length,
        averagePriceIncrease,
        flagsByCategory,
        flagsByCurrency,
      };
    } catch (error) {
      throw new MedusaError(
        MedusaError.Types.UNEXPECTED_STATE,
        `Failed to get price flag statistics: ${error.message}`
      );
    }
  }
}

export default PriceFlagService;
