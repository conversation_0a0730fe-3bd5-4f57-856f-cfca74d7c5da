import {
  <PERSON><PERSON>aS<PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
} from "@camped-ai/framework/utils";
import { IProductModuleService } from "@camped-ai/framework/types";

import { withClient } from "../utils/db";
import { ADD_ON_SERVICE } from "../modules/hotel-management/add-on-service/index";
import { SUPPLIER_PRODUCTS_SERVICES_MODULE } from "../modules/supplier-products-services/index";
import { SUPPLIER_MANAGEMENT_MODULE } from "../modules/vendor_management/index";
import {
  AddOnServiceLevel,
  AddOnPricingType,
  AddOnServiceInput,
} from "../modules/hotel-management/add-on-service/types";

export interface SupplierConfig {
  id: string;
  supplier_id: string;
  default_margin_percentage: number;
  default_currency: string;
  auto_sync_enabled: boolean;
  category_mapping: Record<string, string>;
  field_mapping: Record<string, string>;
  pricing_rules: {
    adult_margin?: number;
    child_margin?: number;
    package_margin?: number;
    seasonal_adjustments?: Array<{
      season: string;
      multiplier: number;
    }>;
    volume_discounts?: Array<{
      min_quantity: number;
      discount_percentage: number;
    }>;
  };
  hotel_mapping: {
    default_service_level: string;
    hotel_assignments: Record<string, string>;
    destination_assignments: Record<string, string>;
  };
}

export interface SyncLogEntry {
  id: string;
  supplier_product_service_id: string;
  add_on_product_id?: string;
  sync_action: "create" | "update" | "delete";
  sync_status: "success" | "error" | "pending";
  error_message?: string;
  sync_data?: any;
  created_at: Date;
}

export interface ProductServiceData {
  id: string;
  name: string;
  type: "Product" | "Service";
  description?: string;
  base_cost?: number;
  custom_fields?: Record<string, any>;
  status: "active" | "inactive";
  category_id: string;
  unit_type_id: string;
  service_level?: "hotel" | "destination";
  hotel_id?: string;
  destination_id?: string;
  category?: {
    id: string;
    name: string;
    description?: string;
    is_active: boolean;
  };
  unit_type?: {
    id: string;
    name: string;
    description?: string;
    is_active: boolean;
  };
  suppliers?: Array<{
    id: string;
    supplier_id: string;
    cost: number;
    currency_code: string;
    availability?: string;
    max_capacity?: number;
    season?: string;
    valid_from?: Date;
    valid_until?: Date;
    is_active: boolean;
    is_preferred: boolean;
  }>;
  created_at: Date;
  updated_at: Date;
}

/**
 * Service for synchronizing product services to add-ons
 */
export class ProductServiceSyncService extends MedusaService({}) {
  protected readonly productModuleService: IProductModuleService;
  protected readonly addOnService: any;
  protected readonly supplierProductsServicesService: any;
  protected readonly supplierManagementService: any;
  protected readonly container: any;

  constructor(container: any) {
    super(container);
    this.container = container;
    this.productModuleService = container.resolve(Modules.PRODUCT);
    this.addOnService = container.resolve(ADD_ON_SERVICE);
    this.supplierProductsServicesService = container.resolve(
      SUPPLIER_PRODUCTS_SERVICES_MODULE
    );
    this.supplierManagementService = container.resolve(
      SUPPLIER_MANAGEMENT_MODULE
    );
  }

  /**
   * Get supplier configuration for sync operations
   */
  async getSupplierConfig(supplierId: string): Promise<SupplierConfig> {
    return await withClient(async (client) => {
      const result = await client.query(
        `SELECT * FROM "add_on_supplier_config" WHERE "supplier_id" = $1 AND "deleted_at" IS NULL`,
        [supplierId]
      );

      if (result.rows.length === 0) {
        // Create default config if not exists
        const defaultConfig = {
          id: `asc_${supplierId}`,
          supplier_id: supplierId,
          default_margin_percentage: 20.0,
          default_currency: "CHF",
          auto_sync_enabled: true,
          category_mapping: {},
          field_mapping: {},
          pricing_rules: {
            adult_margin: 20,
            child_margin: 15,
            package_margin: 18,
            seasonal_adjustments: [],
            volume_discounts: [],
          },
          hotel_mapping: {
            default_service_level: "general",
            hotel_assignments: {},
            destination_assignments: {},
          },
        };

        await client.query(
          `INSERT INTO "add_on_supplier_config" (
            "id", "supplier_id", "default_margin_percentage", "default_currency",
            "auto_sync_enabled", "category_mapping", "field_mapping", 
            "pricing_rules", "hotel_mapping"
          ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)`,
          [
            defaultConfig.id,
            defaultConfig.supplier_id,
            defaultConfig.default_margin_percentage,
            defaultConfig.default_currency,
            defaultConfig.auto_sync_enabled,
            JSON.stringify(defaultConfig.category_mapping),
            JSON.stringify(defaultConfig.field_mapping),
            JSON.stringify(defaultConfig.pricing_rules),
            JSON.stringify(defaultConfig.hotel_mapping),
          ]
        );

        return defaultConfig;
      }

      const row = result.rows[0];
      const config = {
        id: row.id,
        supplier_id: row.supplier_id,
        default_margin_percentage: parseFloat(row.default_margin_percentage),
        default_currency: row.default_currency,
        auto_sync_enabled: row.auto_sync_enabled,
        category_mapping: row.category_mapping || {},
        field_mapping: row.field_mapping || {},
        pricing_rules: row.pricing_rules || {
          adult_margin: 20,
          child_margin: 15,
          package_margin: 18,
          seasonal_adjustments: [],
          volume_discounts: [],
        },
        hotel_mapping: row.hotel_mapping || {
          default_service_level: "general",
          hotel_assignments: {},
          destination_assignments: {},
        },
      };

      // Update existing configs that have 'hotel' or 'destination' as default to 'general'
      // This ensures backward compatibility and prevents hotel_id/destination_id requirement errors
      if (
        config.hotel_mapping.default_service_level === "hotel" ||
        config.hotel_mapping.default_service_level === "destination"
      ) {
        const oldLevel = config.hotel_mapping.default_service_level;
        config.hotel_mapping.default_service_level = "general";

        // Optionally update the database record
        try {
          await client.query(
            `UPDATE "add_on_sync_config"
             SET "hotel_mapping" = $1, "updated_at" = NOW()
             WHERE "supplier_id" = $2`,
            [JSON.stringify(config.hotel_mapping), supplierId]
          );
        } catch (updateError) {
          console.warn(
            `⚠️ Failed to update supplier config ${supplierId} in database:`,
            updateError.message
          );
          // Continue with the updated config in memory
        }
      }

      return config;
    });
  }

  /**
   * Log sync operation
   */
  async logSyncOperation(
    entry: Omit<SyncLogEntry, "id" | "created_at">
  ): Promise<void> {
    await withClient(async (client) => {
      await client.query(
        `INSERT INTO "add_on_sync_log" (
          "id", "supplier_product_service_id", "add_on_product_id",
          "sync_action", "sync_status", "error_message", "sync_data"
        ) VALUES ($1, $2, $3, $4, $5, $6, $7)`,
        [
          `sync_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
          entry.supplier_product_service_id,
          entry.add_on_product_id,
          entry.sync_action,
          entry.sync_status,
          entry.error_message,
          JSON.stringify(entry.sync_data),
        ]
      );
    });
  }

  /**
   * Generate a URL-safe handle for add-on from product service ID
   */
  generateAddOnHandle(productServiceId: string): string {
    return `addon-${productServiceId.toLowerCase().replace(/[^a-z0-9]/g, "-")}`;
  }

  /**
   * Calculate pricing with margins
   */
  calculatePricing(
    productService: ProductServiceData,
    config: SupplierConfig
  ): {
    adult_price: number;
    child_price: number;
    package_price: number;
    currency_code: string;
  } {
    const baseCost = productService.base_cost || 0;
    const currency = config.default_currency;

    // Get supplier-specific cost if available
    let supplierCost = baseCost;
    if (productService.suppliers && productService.suppliers.length > 0) {
      const preferredSupplier =
        productService.suppliers.find((s) => s.is_preferred) ||
        productService.suppliers[0];
      supplierCost = preferredSupplier.cost || baseCost;
    }

    // Ensure we have a minimum cost to work with
    if (supplierCost <= 0) {
      console.warn(
        `Product service ${productService.id} has zero or negative cost (${supplierCost}), using minimum cost of 1.00`
      );
      supplierCost = 1.0;
    }

    const adultMargin =
      config.pricing_rules.adult_margin || config.default_margin_percentage;
    const childMargin =
      config.pricing_rules.child_margin ||
      config.default_margin_percentage * 0.75;
    const packageMargin =
      config.pricing_rules.package_margin || config.default_margin_percentage;

    // Ensure margins are not negative (which would result in 0 or negative prices)
    const safeAdultMargin = Math.max(adultMargin, 0);
    const safeChildMargin = Math.max(childMargin, 0);
    const safePackageMargin = Math.max(packageMargin, 0);

    const adultPrice = supplierCost * (1 + safeAdultMargin / 100);
    const childPrice = supplierCost * (1 + safeChildMargin / 100);
    const packagePrice = supplierCost * (1 + safePackageMargin / 100);

    // Ensure all prices are at least 0.01 to pass validation
    return {
      adult_price: Math.max(adultPrice, 0.01),
      child_price: Math.max(childPrice, 0.01),
      package_price: Math.max(packagePrice, 0.01),
      currency_code: currency,
    };
  }

  /**
   * Map product service data to add-on format (Legacy method - kept for compatibility)
   */
  mapProductServiceToAddOn(
    productService: ProductServiceData,
    pricing: {
      adult_price: number;
      child_price: number;
      package_price: number;
      currency_code: string;
    },
    config: SupplierConfig
  ): AddOnServiceInput {
    // Default to GENERAL level for supplier products since they're general inventory items
    // not tied to specific hotels or destinations
    const serviceLevel =
      (config.hotel_mapping.default_service_level as AddOnServiceLevel) ||
      AddOnServiceLevel.GENERAL;

    const addOnData: AddOnServiceInput = {
      name: productService.name,
      description: productService.description || "",
      type: productService.type.toLowerCase(),
      service_level: serviceLevel,
      is_active: productService.status === "active",
      pricing_type: AddOnPricingType.PER_PERSON,
      currency_code: pricing.currency_code,
      adult_price: pricing.adult_price,
      child_price: pricing.child_price,
      package_price: pricing.package_price,
      category_id: productService.category_id,
      max_capacity: productService.suppliers?.[0]?.max_capacity || null,
      // Add metadata for filtering and tracking
      handle: this.generateAddOnHandle(productService.id),
    };

    // Only add hotel_id/destination_id if service level requires it
    // GENERAL level services don't need either
    if (serviceLevel === AddOnServiceLevel.HOTEL) {
      addOnData.hotel_id =
        config.hotel_mapping.hotel_assignments?.[productService.id] ||
        undefined;
    } else if (serviceLevel === AddOnServiceLevel.DESTINATION) {
      addOnData.destination_id =
        config.hotel_mapping.destination_assignments?.[productService.id] ||
        undefined;
    }
    // GENERAL level services don't need hotel_id or destination_id

    return addOnData;
  }

  /**
   * Create product and product_variant records directly from product_service data
   * This method implements the exact data transformation requirements and is idempotent
   */
  async createProductAndVariantFromProductService(
    productService: ProductServiceData
  ): Promise<{ product: any; variant: any }> {
    try {
      // Generate the required IDs with prefixes
      const productId = `prod_addon_${productService.id}`;
      const variantId = `variant_addon_${productService.id}`;

      // Extract customer custom fields from the product service
      const customerFields = await this.extractCustomerFields(
        productService.id
      );

      // Create metadata JSON with all required fields and custom fields
      const metadata = {
        type: productService.type,
        description: productService.description || "",
        category: productService.category_id,
        unit_type_id: productService.unit_type_id,
        add_on_service: true,
        // Store custom fields as JSON object directly
        custom_fields: productService.custom_fields || {},
        // Store customer custom fields separately for booking forms
        customer_fields: customerFields,
        // Add pricing fields
        cost: productService.base_cost || 0,
        cost_currency: "CHF",
        selling_margin: 0,
        selling_price_cost_currency: 0,
        selling_price: 0,
        selling_currency: "CHF",
        // Add service level and location fields
        service_level: productService.service_level || "hotel",
        hotel_id: productService.hotel_id || null,
        destination_id: productService.destination_id || null,
      };

      // Check if product already exists
      let product: any;
      try {
        const existingProducts = await this.productModuleService.listProducts({
          id: [productId],
        });
        if (existingProducts.length > 0) {
          // Product exists, update it
          const productData = {
            title: productService.name,
            subtitle: "",
            description: productService.description || "",
            status: productService.status === "active" ? "published" as const : "draft" as const,
          };
          product = await this.productModuleService.updateProducts(
            productId,
            productData
          );
        } else {
          // Product doesn't exist, create it
          const productData = {
            id: productId,
            title: productService.name,
            subtitle: "",
            description: productService.description || "",
            status: productService.status === "active" ? "published" as const : "draft" as const,
          };
          product = await this.productModuleService.createProducts(productData);
        }
      } catch (error) {
        console.error(`Error handling product ${productId}:`, error);
        throw error;
      }

      // Check if variant already exists
      let variant: any;
      try {
        const existingVariants =
          await this.productModuleService.listProductVariants({
            id: [variantId],
          });
        if (existingVariants.length > 0) {
          // Variant exists, update it
          const variantData = {
            title: productService.name,
            sku: productService.id, // Use raw product_service.id as SKU
            metadata: {
              ...metadata,
              status:
                productService.status === "active" ? "Active" : "Inactive", // Add-ons page status
            },
          };
          variant = await this.productModuleService.updateProductVariants(
            variantId,
            variantData
          );
        } else {
          // Variant doesn't exist, create it
          const variantData = {
            id: variantId,
            title: productService.name,
            sku: productService.id, // Use raw product_service.id as SKU
            product_id: productId,
            metadata: {
              ...metadata,
              status:
                productService.status === "active" ? "Active" : "Inactive", // Add-ons page status
            },
            inventory_quantity: 1,
            manage_inventory: false,
          };
          const createdVariants =
            await this.productModuleService.createProductVariants([
              variantData,
            ]);
          variant = createdVariants[0];
        }
      } catch (error) {
        console.error(`Error handling variant ${variantId}:`, error);
        throw error;
      }

      return {
        product,
        variant,
      };
    } catch (error) {
      console.error(
        `Failed to create/update product and variant from product service ${productService.id}:`,
        error
      );
      throw error;
    }
  }

  /**
   * Find existing add-on by product service ID using the correct ID prefix
   */
  async findAddOnByProductServiceId(
    productServiceId: string
  ): Promise<any | null> {
    try {
      // Search for products with the correct prefixed ID
      const expectedProductId = `prod_addon_${productServiceId}`;
      const products = await this.productModuleService.listProducts({
        id: [expectedProductId],
      });

      if (products.length > 0) {
        return products[0];
      }

      return null;
    } catch (error) {
      console.error(
        `Error finding add-on for product service ${productServiceId}:`,
        error
      );
      return null;
    }
  }

  /**
   * Create/update an add-on from a product service using direct product/variant creation
   * This method is idempotent and handles both create and update scenarios
   */
  async createAddOnFromProductService(
    productService: ProductServiceData,
    _migrationSource:
      | "manual"
      | "supplier_sync"
      | "legacy_migration" = "supplier_sync"
  ): Promise<{ product: any; variant: any }> {
    try {
      // Create/update product and variant directly with correct structure (idempotent)
      const result = await this.createProductAndVariantFromProductService(
        productService
      );

      // Update the product_service record with the synchronized product_id and product_variant_id
      try {
        await this.supplierProductsServicesService.updateProductService(
          productService.id,
          {
            product_id: result.product.id,
            product_variant_id: result.variant.id,
          }
        );
      } catch (updateError) {
        console.warn(
          `⚠️ Failed to update product_service ${productService.id} with sync fields:`,
          updateError.message
        );
        // Don't throw here as the sync itself was successful
      }

      // Automatically map the synced product to the hardcoded category using Medusa's linkable queries
      // This approach uses the product module service to handle the relationship properly
      try {
        // Check if the product is already linked to this category to avoid unnecessary updates
        const productWithCategories = await this.productModuleService.retrieveProduct(
          result.product.id,
          { relations: ["categories"] }
        );

        const isAlreadyLinked = productWithCategories.categories?.some(
          (category: any) => category.id === "product_category_add_ons_main"
        );

        if (!isAlreadyLinked) {
          // Use Medusa's updateProducts method to link the product to the category
          await this.productModuleService.updateProducts(result.product.id, {
            category_ids: ["product_category_add_ons_main"],
          });
        } else {
          //do nothing
        }
      } catch (mappingError) {
        console.warn(
          `⚠️ Failed to link product ${result.product.id} to category product_category_add_ons_main:`,
          mappingError.message
        );
        // Don't throw error to prevent breaking the sync process
        // The sync is still successful even if the mapping fails
      }

      // Log successful sync
      await this.logSyncOperation({
        supplier_product_service_id: productService.id,
        add_on_product_id: result.product.id,
        sync_action: "create",
        sync_status: "success",
        sync_data: { productService, result },
      });
      return result;
    } catch (error) {
      // Log failed sync
      await this.logSyncOperation({
        supplier_product_service_id: productService.id,
        sync_action: "create",
        sync_status: "error",
        error_message: error.message,
        sync_data: { productService },
      });

      console.error(
        `❌ Failed to sync add-on from product service ${productService.id}:`,
        error
      );
      throw error;
    }
  }

  /**
   * Extract customer custom fields for a product service
   */
  private async extractCustomerFields(
    productServiceId: string
  ): Promise<any[]> {
    try {
      // Get the product service with its category
      const productService =
        await this.supplierProductsServicesService.retrieveProductService(
          productServiceId,
          { relations: ["category"] }
        );

      if (!productService?.category?.dynamic_field_schema) {
        return [];
      }

      // Filter for customer fields only
      const customerFields = productService.category.dynamic_field_schema
        .filter((field: any) => field.field_context === "customer")
        .map((field: any, index: number) => ({
          field_id: `${field.key}_${productServiceId}`,
          field_name: field.label,
          field_type: field.type,
          field_key: field.key,
          is_required: field.required || false,
          display_order: index + 1,
          options: field.options || null,
          validation_rules: field.validation_rules || null,
        }));
      return customerFields;
    } catch (error) {
      console.error(
        `❌ Failed to extract customer fields for ${productServiceId}:`,
        error
      );
      return [];
    }
  }

  /**
   * Sync a single product service to add-on (idempotent operation)
   */
  async syncProductServiceToAddOn(
    productServiceId: string
  ): Promise<{ product: any; variant: any }> {
    try {
      // Get product service data (relations are already included in the listProductServicesWithFilters method)
      const productService =
        await this.supplierProductsServicesService.retrieveProductService(
          productServiceId
        );

      if (!productService) {
        throw new MedusaError(
          MedusaError.Types.NOT_FOUND,
          `Product service with ID ${productServiceId} not found`
        );
      }

      // Use the idempotent create method which handles both create and update scenarios
      return await this.createAddOnFromProductService(productService);
    } catch (error) {
      console.error(
        `❌ Failed to sync product service ${productServiceId}:`,
        error
      );
      throw error;
    }
  }

  /**
   * Bulk sync all product services to add-ons
   */
  async bulkSyncProductServicesToAddOns(
    options: {
      limit?: number;
      offset?: number;
      status?: "active" | "inactive";
      type?: "Product" | "Service";
    } = {}
  ): Promise<{
    total: number;
    synced: number;
    errors: Array<{ id: string; error: string }>;
    results: Array<{ product: any; variant: any }>;
  }> {
    try {

      // Separate pagination options from filter criteria
      const { limit = 100, offset = 0, ...filterOptions } = options;

      // Get all product services using the correct method signature
      const productServices =
        await this.supplierProductsServicesService.listProductServicesWithFilters(
          filterOptions, // Filter criteria (status, type)
          { skip: offset, take: limit } // Pagination options
        );

      const results: Array<{ product: any; variant: any }> = [];
      const errors: Array<{ id: string; error: string }> = [];
      let synced = 0;

      // productServices is now an array directly, not an object with data property
      for (const productService of productServices) {
        try {
          const result = await this.syncProductServiceToAddOn(
            productService.id
          );
          results.push(result);
          synced++;
        } catch (error) {
          errors.push({
            id: productService.id,
            error: error.message,
          });
          console.error(
            `❌ Failed to sync product service ${productService.id}:`,
            error
          );
        }
      }


      return {
        total: productServices.length,
        synced,
        errors,
        results,
      };
    } catch (error) {
      console.error("❌ Bulk sync failed:", error);
      throw error;
    }
  }

  /**
   * Get sync status for a product service
   */
  async getSyncStatus(productServiceId: string): Promise<{
    is_synced: boolean;
    last_sync_at?: Date;
    last_sync_status?: "success" | "error";
    add_on_product_id?: string;
    error_message?: string;
  }> {
    return await withClient(async (client) => {
      const result = await client.query(
        `SELECT * FROM "add_on_sync_log"
         WHERE "supplier_product_service_id" = $1
         ORDER BY "created_at" DESC
         LIMIT 1`,
        [productServiceId]
      );

      if (result.rows.length === 0) {
        return { is_synced: false };
      }

      const lastSync = result.rows[0];
      return {
        is_synced: lastSync.sync_status === "success",
        last_sync_at: lastSync.created_at,
        last_sync_status: lastSync.sync_status,
        add_on_product_id: lastSync.add_on_product_id,
        error_message: lastSync.error_message,
      };
    });
  }

  /**
   * Get sync statistics
   */
  async getSyncStatistics(): Promise<{
    total_product_services: number;
    synced_count: number;
    error_count: number;
    pending_count: number;
    last_sync_at?: Date;
  }> {
    return await withClient(async (client) => {
      // Get total product services
      const totalResult = await client.query(
        `SELECT COUNT(*) as count FROM "product_service" WHERE "deleted_at" IS NULL`
      );

      // Get sync statistics
      const syncResult = await client.query(`
        SELECT
          COUNT(CASE WHEN sync_status = 'success' THEN 1 END) as synced_count,
          COUNT(CASE WHEN sync_status = 'error' THEN 1 END) as error_count,
          COUNT(CASE WHEN sync_status = 'pending' THEN 1 END) as pending_count,
          MAX(created_at) as last_sync_at
        FROM "add_on_sync_log"
      `);

      const total = parseInt(totalResult.rows[0].count);
      const stats = syncResult.rows[0];

      return {
        total_product_services: total,
        synced_count: parseInt(stats.synced_count) || 0,
        error_count: parseInt(stats.error_count) || 0,
        pending_count: parseInt(stats.pending_count) || 0,
        last_sync_at: stats.last_sync_at,
      };
    });
  }

  /**
   * Get recent sync logs
   */
  async getRecentSyncLogs(limit: number = 50): Promise<SyncLogEntry[]> {
    return await withClient(async (client) => {
      const result = await client.query(
        `SELECT * FROM "add_on_sync_log"
         ORDER BY "created_at" DESC
         LIMIT $1`,
        [limit]
      );

      return result.rows.map((row: any) => ({
        id: row.id,
        supplier_product_service_id: row.supplier_product_service_id,
        add_on_product_id: row.add_on_product_id,
        sync_action: row.sync_action,
        sync_status: row.sync_status,
        error_message: row.error_message,
        sync_data: row.sync_data,
        created_at: row.created_at,
      }));
    });
  }

  /**
   * Get sync logs for a specific product service
   */
  async getSyncLogsForProductService(
    productServiceId: string
  ): Promise<SyncLogEntry[]> {
    return await withClient(async (client) => {
      const result = await client.query(
        `SELECT * FROM "add_on_sync_log"
         WHERE "supplier_product_service_id" = $1
         ORDER BY "created_at" DESC`,
        [productServiceId]
      );

      return result.rows.map((row: any) => ({
        id: row.id,
        supplier_product_service_id: row.supplier_product_service_id,
        add_on_product_id: row.add_on_product_id,
        sync_action: row.sync_action,
        sync_status: row.sync_status,
        error_message: row.error_message,
        sync_data: row.sync_data,
        created_at: row.created_at,
      }));
    });
  }
}
