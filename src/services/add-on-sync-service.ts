import { MedusaSer<PERSON>, Medus<PERSON><PERSON><PERSON>r, <PERSON><PERSON><PERSON> } from "@camped-ai/framework/utils";
import { IProductModuleService } from "@camped-ai/framework/types";
import { withClient } from "../utils/db.js";
import { ADD_ON_SERVICE } from "../modules/hotel-management/add-on-service/index.js";
import { SUPPLIER_PRODUCTS_SERVICES_MODULE } from "../modules/supplier-products-services/index.js";
import { SUPPLIER_MANAGEMENT_MODULE } from "../modules/vendor_management/index.js";
import {
  EnhancedAddOnServiceMetadata,
  SupplierConfig,
  SyncLogEntry,
  MigrationBackup,
  AddOnServiceLevel,
  AddOnPricingType
} from "../modules/hotel-management/add-on-service/types.js";

/**
 * AddOnSyncService handles the synchronization between supplier products/services
 * and the add-ons system, including migration of existing data and ongoing sync operations.
 */
export class AddOnSyncService extends MedusaService({}) {
  private productService: IProductModuleService;
  private addOnService: any;
  private supplierProductService: any;
  private supplierService: any;

  constructor(container: any) {
    super(container);
    this.productService = container.resolve(Modules.PRODUCT);
    this.addOnService = container.resolve(ADD_ON_SERVICE);
    this.supplierProductService = container.resolve(SUPPLIER_PRODUCTS_SERVICES_MODULE);
    this.supplierService = container.resolve(SUPPLIER_MANAGEMENT_MODULE);
  }

  /**
   * Migrate all existing supplier products/services to add-ons
   */
  async migrateAllSupplierProductsToAddOns(): Promise<{
    success: boolean;
    migrated_count: number;
    errors: string[];
    warnings: string[];
  }> {
    const result = {
      success: true,
      migrated_count: 0,
      errors: [],
      warnings: []
    };

    try {
      // First, backup existing add-ons
      await this.backupExistingAddOns();

      // Get all active supplier products/services
      const supplierProducts = await this.getActiveSupplierProducts();

      console.log(`Found ${supplierProducts.length} supplier products/services to migrate`);

      // Process each supplier product/service
      for (const supplierProduct of supplierProducts) {
        try {
          await this.createAddOnFromSupplierProduct(supplierProduct, 'legacy_migration');
          result.migrated_count++;

          // Log successful migration
          await this.logSyncOperation(
            supplierProduct.id,
            null,
            'migrate',
            'success',
            null,
            { supplier_product: supplierProduct }
          );

        } catch (error) {
          const errorMsg = `Failed to migrate supplier product ${supplierProduct.id}: ${error.message}`;
          result.errors.push(errorMsg);
          console.error(errorMsg);

          // Log failed migration
          await this.logSyncOperation(
            supplierProduct.id,
            null,
            'migrate',
            'error',
            error.message,
            { supplier_product: supplierProduct }
          );
        }
      }

      if (result.errors.length > 0) {
        result.success = false;
      }

      console.log(`Migration completed: ${result.migrated_count} migrated, ${result.errors.length} errors`);
      return result;

    } catch (error) {
      result.success = false;
      result.errors.push(`Migration failed: ${error.message}`);
      console.error('Migration failed:', error);
      return result;
    }
  }

  /**
   * Create an add-on from a supplier product/service
   */
  async createAddOnFromSupplierProduct(
    supplierProduct: any,
    migrationSource: 'manual' | 'supplier_sync' | 'legacy_migration' = 'supplier_sync'
  ): Promise<any> {
    try {
      // Get supplier configuration
      const config = await this.getSupplierConfig(supplierProduct.supplier_id);

      // Calculate pricing with margins
      const pricing = this.calculatePricing(supplierProduct, config);

      // Map supplier data to add-on format
      const addOnData = this.mapSupplierDataToAddOn(supplierProduct, pricing, config, migrationSource);

      // Create add-on
      const addOn = await this.addOnService.createAddOnService(addOnData);

      console.log(`Created add-on ${addOn.id} from supplier product ${supplierProduct.id}`);
      return addOn;

    } catch (error) {
      console.error(`Failed to create add-on from supplier product ${supplierProduct.id}:`, error);
      throw error;
    }
  }

  /**
   * Update an existing add-on from supplier product changes
   */
  async updateAddOnFromSupplierProduct(supplierProduct: any): Promise<any> {
    try {
      // Find existing add-on by supplier_product_service_id
      const existingAddOn = await this.findAddOnBySupplierProductId(supplierProduct.id);

      if (!existingAddOn) {
        console.log(`No existing add-on found for supplier product ${supplierProduct.id}, creating new one`);
        return await this.createAddOnFromSupplierProduct(supplierProduct, 'supplier_sync');
      }

      // Get supplier configuration
      const config = await this.getSupplierConfig(supplierProduct.supplier_id);

      // Calculate updated pricing
      const pricing = this.calculatePricing(supplierProduct, config);

      // Map updated data
      const updateData = this.mapSupplierDataToAddOn(supplierProduct, pricing, config, 'supplier_sync');

      // Update the add-on
      const updatedAddOn = await this.addOnService.updateAddOnService(existingAddOn.id, updateData);

      console.log(`Updated add-on ${existingAddOn.id} from supplier product ${supplierProduct.id}`);
      return updatedAddOn;

    } catch (error) {
      console.error(`Failed to update add-on from supplier product ${supplierProduct.id}:`, error);
      throw error;
    }
  }

  /**
   * Deactivate add-on when supplier product is deleted
   */
  async deactivateAddOnFromSupplierProduct(supplierProductId: string): Promise<void> {
    try {
      const existingAddOn = await this.findAddOnBySupplierProductId(supplierProductId);

      if (existingAddOn) {
        await this.addOnService.updateAddOnService(existingAddOn.id, {
          is_active: false,
          metadata: {
            ...existingAddOn.metadata,
            sync_status: 'synced',
            last_sync_date: new Date().toISOString(),
            deactivation_reason: 'supplier_product_deleted'
          }
        });

        console.log(`Deactivated add-on ${existingAddOn.id} for deleted supplier product ${supplierProductId}`);
      }

    } catch (error) {
      console.error(`Failed to deactivate add-on for supplier product ${supplierProductId}:`, error);
      throw error;
    }
  }

  /**
   * Remove legacy add-ons (manually created ones)
   */
  async removeLegacyAddOns(): Promise<void> {
    try {
      console.log('Starting removal of legacy add-ons...');

      // Get all existing add-ons that don't have supplier integration metadata
      const legacyAddOns = await this.getLegacyAddOns();

      console.log(`Found ${legacyAddOns.length} legacy add-ons to remove`);

      for (const addOn of legacyAddOns) {
        try {
          // Backup before deletion
          await this.backupAddOn(addOn);

          // Delete the add-on
          await this.addOnService.deleteAddOnService(addOn.id);

          console.log(`Removed legacy add-on: ${addOn.id} - ${addOn.name}`);

        } catch (error) {
          console.error(`Failed to remove legacy add-on ${addOn.id}:`, error);
        }
      }

      console.log('Legacy add-ons removal completed');

    } catch (error) {
      console.error('Failed to remove legacy add-ons:', error);
      throw error;
    }
  }

  /**
   * Get all active supplier products/services with their relationships
   */
  private async getActiveSupplierProducts(): Promise<any[]> {
    return withClient(async (client) => {
      const query = `
        SELECT
          ps.*,
          pss.cost,
          pss.currency_code,
          pss.availability,
          pss.max_capacity,
          pss.season,
          pss.valid_from,
          pss.valid_until,
          pss.supplier_id,
          s.name as supplier_name,
          c.name as category_name,
          ut.name as unit_type_name
        FROM product_service ps
        LEFT JOIN product_service_supplier pss ON ps.id = pss.product_service_id
        LEFT JOIN supplier s ON pss.supplier_id = s.id
        LEFT JOIN product_service_category c ON ps.category_id = c.id
        LEFT JOIN product_service_unit_type ut ON ps.unit_type_id = ut.id
        WHERE ps.status = 'active'
          AND ps.deleted_at IS NULL
          AND pss.is_active = true
          AND pss.deleted_at IS NULL
          AND s.deleted_at IS NULL
        ORDER BY ps.created_at ASC
      `;

      const result = await client.raw(query);
      return result.rows || [];
    });
  }

  /**
   * Get legacy add-ons (those without supplier integration metadata)
   */
  private async getLegacyAddOns(): Promise<any[]> {
    try {
      const addOns = await this.addOnService.listAddOnServices({}, { skip: 0, take: 1000 });

      return addOns[0].filter((addOn: any) => {
        const metadata = addOn.metadata || {};
        return !metadata.supplier_product_service_id && !metadata.migration_source;
      });

    } catch (error) {
      console.error('Failed to get legacy add-ons:', error);
      return [];
    }
  }

  /**
   * Backup existing add-ons before migration
   */
  private async backupExistingAddOns(): Promise<void> {
    try {
      console.log('Backing up existing add-ons...');

      const addOns = await this.addOnService.listAddOnServices({}, { skip: 0, take: 1000 });

      for (const addOn of addOns[0]) {
        await this.backupAddOn(addOn);
      }

      console.log(`Backed up ${addOns[0].length} existing add-ons`);

    } catch (error) {
      console.error('Failed to backup existing add-ons:', error);
      throw error;
    }
  }

  /**
   * Backup a single add-on
   */
  private async backupAddOn(addOn: any): Promise<void> {
    return withClient(async (client) => {
      // Get product variants for this add-on
      const variants = await this.productService.listProductVariants({
        product_id: addOn.product_id
      });

      const backupData = {
        id: `backup_${addOn.id}_${Date.now()}`,
        original_product_id: addOn.product_id,
        original_metadata: addOn.metadata || {},
        original_variants: variants || [],
        backup_timestamp: new Date().toISOString()
      };

      await client('add_on_migration_backup').insert(backupData);
    });
  }

  /**
   * Get supplier configuration
   */
  private async getSupplierConfig(supplierId: string): Promise<SupplierConfig> {
    return withClient(async (client) => {
      const result = await client('add_on_supplier_config')
        .where('supplier_id', supplierId)
        .where('deleted_at', null)
        .first();

      if (!result) {
        // Return default configuration
        return {
          id: `default_${supplierId}`,
          supplier_id: supplierId,
          default_margin_percentage: 20,
          default_currency: 'CHF',
          auto_sync_enabled: true,
          category_mapping: {},
          field_mapping: {},
          pricing_rules: {
            adult_margin: 20,
            child_margin: 15,
            package_margin: 18,
            seasonal_adjustments: [],
            volume_discounts: []
          },
          hotel_mapping: {
            default_service_level: 'hotel',
            hotel_assignments: {},
            destination_assignments: {}
          },
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        };
      }

      return result;
    });
  }

  /**
   * Calculate pricing with margins
   */
  private calculatePricing(supplierProduct: any, config: SupplierConfig): any {
    const baseCost = supplierProduct.cost || supplierProduct.base_cost || 0;
    const adultMargin = config.pricing_rules.adult_margin || config.default_margin_percentage || 20;
    const childMargin = config.pricing_rules.child_margin || (adultMargin * 0.75) || 15;

    return {
      adult_price: this.calculateSellingPrice(baseCost, adultMargin, 'percentage_margin'),
      child_price: this.calculateSellingPrice(baseCost, childMargin, 'percentage_margin'),
      package_price: this.calculateSellingPrice(baseCost, config.pricing_rules.package_margin || adultMargin, 'percentage_margin'),
      currency_code: config.default_currency || 'CHF'
    };
  }

  /**
   * Calculate pricing from ProductService base price
   */
  private calculatePricingFromProductService(productService: any, config: SupplierConfig): any {
    const baseCost = productService.base_cost || 0;
    const adultMargin = config.pricing_rules.adult_margin || config.default_margin_percentage || 20;
    const childMargin = config.pricing_rules.child_margin || (adultMargin * 0.75) || 15;

    return {
      adult_price: this.calculateSellingPrice(baseCost, adultMargin, 'percentage_margin'),
      child_price: this.calculateSellingPrice(baseCost, childMargin, 'percentage_margin'),
      package_price: this.calculateSellingPrice(baseCost, config.pricing_rules.package_margin || adultMargin, 'percentage_margin'),
      currency_code: productService.highest_price_currency || config.default_currency || 'CHF'
    };
  }

  /**
   * Calculate selling price based on cost and margin
   * Formula: price = Net cost / (1 - margin/100)
   */
  private calculateSellingPrice(
    baseCost: number,
    margin: number,
    method: 'fixed_margin' | 'percentage_margin' | 'fixed_price' = 'percentage_margin'
  ): number {
    switch (method) {
      case 'percentage_margin':
        // Ensure margin is not 100% or higher to avoid division by zero/negative
        if (margin >= 100) {
          throw new Error(`Invalid margin percentage: ${margin}%. Margin must be less than 100%.`);
        }
        return baseCost / (1 - margin / 100);
      case 'fixed_margin':
        return baseCost + margin;
      case 'fixed_price':
        return margin;
      default:
        return baseCost / (1 - 20 / 100); // Default 20% margin using new formula
    }
  }

  /**
   * Sync add-on pricing when ProductService base price is auto-populated
   */
  async syncAddOnFromProductServiceBasePrice(
    productServiceId: string,
    supplierOfferingId?: string
  ): Promise<any> {
    try {
      // Find existing add-on by product service ID
      const existingAddOn = await this.findAddOnByProductServiceId(productServiceId);

      if (!existingAddOn) {
        console.log(`No existing add-on found for product service ${productServiceId}, skipping sync`);
        return null;
      }

      // Get the product service with updated base price
      const productService = await this.supplierProductService.retrieveProductService(
        productServiceId
      );

      if (!productService.base_cost) {
        console.log(`Product service ${productServiceId} has no base cost, skipping add-on sync`);
        return null;
      }

      // Get supplier configuration (use default if not found)
      const config = await this.getSupplierConfig('default') || this.getDefaultSupplierConfig();

      // Calculate new pricing based on the product service base price
      const pricing = this.calculatePricingFromProductService(productService, config);

      // Update the add-on with new pricing and price tracking info
      const updateData = {
        ...pricing,
        price_flag_active: productService.price_flag_active,
        price_flag_created_at: productService.price_flag_created_at,
        price_flag_supplier_offering_id: productService.price_flag_supplier_offering_id || supplierOfferingId,
        highest_price: productService.highest_price,
        highest_price_currency: productService.highest_price_currency,
        source_product_service_id: productServiceId,
      };

      const updatedAddOn = await this.addOnService.updateAddOnService(existingAddOn.id, updateData);

      console.log(`Synced add-on ${existingAddOn.id} from product service base price update ${productServiceId}`);
      return updatedAddOn;

    } catch (error) {
      console.error(`Failed to sync add-on from product service base price ${productServiceId}:`, error);
      throw error;
    }
  }

  /**
   * Sync price flags from ProductService to corresponding Add-ons
   */
  async syncPriceFlagsToAddOns(
    productServiceId: string,
    flagData: {
      price_flag_active: boolean;
      price_flag_created_at?: string | null;
      price_flag_supplier_offering_id?: string | null;
      highest_price?: number | null;
      highest_price_currency?: string | null;
    }
  ): Promise<any[]> {
    try {
      // Find all add-ons linked to this product service
      const linkedAddOns = await this.findAddOnsByProductServiceId(productServiceId);

      if (linkedAddOns.length === 0) {
        console.log(`No linked add-ons found for product service ${productServiceId}`);
        return [];
      }

      const updatedAddOns = [];

      for (const addOn of linkedAddOns) {
        try {
          const updateData = {
            price_flag_active: flagData.price_flag_active,
            price_flag_created_at: flagData.price_flag_created_at,
            price_flag_supplier_offering_id: flagData.price_flag_supplier_offering_id,
            highest_price: flagData.highest_price,
            highest_price_currency: flagData.highest_price_currency,
          };

          const updatedAddOn = await this.addOnService.updateAddOnService(addOn.id, updateData);
          updatedAddOns.push(updatedAddOn);

          console.log(`Synced price flag to add-on ${addOn.id} from product service ${productServiceId}`);
        } catch (error) {
          console.error(`Failed to sync price flag to add-on ${addOn.id}:`, error);
        }
      }

      return updatedAddOns;
    } catch (error) {
      console.error(`Failed to sync price flags to add-ons for product service ${productServiceId}:`, error);
      throw error;
    }
  }

  /**
   * Find all add-ons linked to a product service
   */
  private async findAddOnsByProductServiceId(productServiceId: string): Promise<any[]> {
    try {
      // Search for add-ons with matching source_product_service_id in metadata
      const addOns = await this.addOnService.listAddOnServices({
        metadata: {
          source_product_service_id: productServiceId
        }
      });

      return addOns || [];
    } catch (error) {
      console.error(`Error finding add-ons by product service ID ${productServiceId}:`, error);
      return [];
    }
  }

  /**
   * Find add-on by product service ID
   */
  private async findAddOnByProductServiceId(productServiceId: string): Promise<any> {
    try {
      // Search for add-ons with matching source_product_service_id in metadata
      const addOns = await this.addOnService.listAddOnServices({
        metadata: {
          source_product_service_id: productServiceId
        }
      });

      return addOns.length > 0 ? addOns[0] : null;
    } catch (error) {
      console.error(`Error finding add-on by product service ID ${productServiceId}:`, error);
      return null;
    }
  }

  /**
   * Get default supplier configuration
   */
  private getDefaultSupplierConfig(): SupplierConfig {
    return {
      id: 'default',
      supplier_id: 'default',
      default_margin_percentage: 20,
      default_currency: 'CHF',
      auto_sync_enabled: true,
      category_mapping: {},
      field_mapping: {},
      hotel_mapping: {
        default_service_level: 'hotel' as const,
      },
      pricing_rules: {
        adult_margin: 20,
        child_margin: 15,
        package_margin: 20,
      },
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    };
  }

  /**
   * Map supplier data to add-on format
   */
  private mapSupplierDataToAddOn(
    supplierProduct: any,
    pricing: any,
    config: SupplierConfig,
    migrationSource: 'manual' | 'supplier_sync' | 'legacy_migration'
  ): any {
    const serviceLevel = this.determineServiceLevel(supplierProduct, config);

    const enhancedMetadata: EnhancedAddOnServiceMetadata = {
      add_on_service: true,
      service_type: supplierProduct.type || 'service',
      service_level: serviceLevel,
      pricing_type: AddOnPricingType.PER_PERSON,
      is_active: supplierProduct.status === 'active',

      // Supplier integration fields
      supplier_product_service_id: supplierProduct.id,
      supplier_id: supplierProduct.supplier_id,
      supplier_name: supplierProduct.supplier_name,
      base_cost: supplierProduct.cost || supplierProduct.base_cost,
      base_currency: config.default_currency,
      margin_percentage: config.default_margin_percentage,
      cost_calculation_method: 'percentage_margin',

      // Enhanced filtering fields
      supplier_category: supplierProduct.category_name,
      supplier_tags: this.extractTags(supplierProduct),
      supplier_custom_fields: supplierProduct.custom_fields || {},
      availability_schedule: supplierProduct.availability,
      seasonal_availability: supplierProduct.season,
      region_availability: this.extractRegions(supplierProduct),

      // Synchronization fields
      last_sync_date: new Date().toISOString(),
      sync_status: 'synced',
      supplier_last_updated: supplierProduct.updated_at,

      // Migration tracking
      migration_source: migrationSource,
      migration_date: new Date().toISOString(),

      // Capacity and availability
      max_capacity: supplierProduct.max_capacity,

      // Multi-currency pricing
      prices: {
        [config.default_currency]: {
          adult_price: Math.round(pricing.adult_price * 100), // Convert to cents
          child_price: Math.round(pricing.child_price * 100),
          package_price: Math.round(pricing.package_price * 100)
        }
      },
      default_currency: config.default_currency,

      // Legacy fields for backward compatibility
      currency_code: config.default_currency,
      adult_price: Math.round(pricing.adult_price * 100),
      child_price: Math.round(pricing.child_price * 100),
      package_price: Math.round(pricing.package_price * 100)
    };

    // Determine hotel/destination assignment
    const assignment = this.determineHotelDestinationAssignment(supplierProduct, config);

    return {
      name: supplierProduct.name,
      description: supplierProduct.description || '',
      service_level: serviceLevel,
      pricing_type: AddOnPricingType.PER_PERSON,
      adult_price: Math.round(pricing.adult_price * 100),
      child_price: Math.round(pricing.child_price * 100),
      currency_code: config.default_currency,
      is_active: supplierProduct.status === 'active',
      hotel_id: assignment.hotel_id,
      destination_id: assignment.destination_id,
      metadata: enhancedMetadata
    };
  }

  /**
   * Determine service level (hotel, destination, or general)
   */
  private determineServiceLevel(supplierProduct: any, config: SupplierConfig): AddOnServiceLevel {
    // Check if there's a specific mapping for this supplier
    const hotelMapping = config.hotel_mapping;

    // Default to general level for supplier products since they're general inventory items
    // not tied to specific hotels or destinations
    if (hotelMapping.default_service_level === 'hotel') {
      return AddOnServiceLevel.HOTEL;
    } else if (hotelMapping.default_service_level === 'destination') {
      return AddOnServiceLevel.DESTINATION;
    } else {
      return AddOnServiceLevel.GENERAL;
    }
  }

  /**
   * Determine hotel/destination assignment
   */
  private determineHotelDestinationAssignment(supplierProduct: any, config: SupplierConfig): {
    hotel_id?: string;
    destination_id?: string;
  } {
    const serviceLevel = this.determineServiceLevel(supplierProduct, config);

    if (serviceLevel === AddOnServiceLevel.HOTEL) {
      // For hotel level, we need to assign to specific hotels
      // This would need to be configured based on supplier regions or other criteria
      return { hotel_id: null }; // Will need manual configuration
    } else if (serviceLevel === AddOnServiceLevel.DESTINATION) {
      // For destination level, assign to destinations
      return { destination_id: null }; // Will need manual configuration
    } else {
      // For general level, no assignment needed
      return {};
    }
  }

  /**
   * Extract tags from supplier product
   */
  private extractTags(supplierProduct: any): string[] {
    const tags: string[] = [];

    // Add category as a tag
    if (supplierProduct.category_name) {
      tags.push(supplierProduct.category_name);
    }

    // Add unit type as a tag
    if (supplierProduct.unit_type_name) {
      tags.push(supplierProduct.unit_type_name);
    }

    // Add seasonal information as tags
    if (supplierProduct.season) {
      tags.push(supplierProduct.season);
    }

    return tags;
  }

  /**
   * Extract regions from supplier product
   */
  private extractRegions(supplierProduct: any): string[] {
    const regions: string[] = [];

    // This would be enhanced based on supplier data structure
    // For now, return empty array
    return regions;
  }

  /**
   * Find existing add-on by supplier product service ID
   */
  private async findAddOnBySupplierProductId(supplierProductId: string): Promise<any> {
    try {
      const addOns = await this.addOnService.listAddOnServices({}, { skip: 0, take: 1000 });

      return addOns[0].find((addOn: any) => {
        const metadata = addOn.metadata || {};
        return metadata.supplier_product_service_id === supplierProductId;
      });

    } catch (error) {
      console.error('Failed to find add-on by supplier product ID:', error);
      return null;
    }
  }

  /**
   * Log sync operation
   */
  private async logSyncOperation(
    supplierProductServiceId: string,
    addOnProductId: string | null,
    syncAction: 'create' | 'update' | 'delete' | 'migrate',
    syncStatus: 'success' | 'error' | 'pending',
    errorMessage?: string,
    syncData?: Record<string, any>
  ): Promise<void> {
    return withClient(async (client) => {
      const logEntry = {
        id: `sync_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
        supplier_product_service_id: supplierProductServiceId,
        add_on_product_id: addOnProductId,
        sync_action: syncAction,
        sync_status: syncStatus,
        error_message: errorMessage,
        sync_data: syncData ? JSON.stringify(syncData) : null,
        created_at: new Date().toISOString()
      };

      await client('add_on_sync_log').insert(logEntry);
    });
  }

  /**
   * Get sync logs for monitoring
   */
  async getSyncLogs(filters?: {
    supplier_product_service_id?: string;
    sync_status?: string;
    sync_action?: string;
    limit?: number;
  }): Promise<SyncLogEntry[]> {
    return withClient(async (client) => {
      let query = client('add_on_sync_log').orderBy('created_at', 'desc');

      if (filters?.supplier_product_service_id) {
        query = query.where('supplier_product_service_id', filters.supplier_product_service_id);
      }

      if (filters?.sync_status) {
        query = query.where('sync_status', filters.sync_status);
      }

      if (filters?.sync_action) {
        query = query.where('sync_action', filters.sync_action);
      }

      if (filters?.limit) {
        query = query.limit(filters.limit);
      }

      const results = await query;
      return results || [];
    });
  }
}

export default AddOnSyncService;