import { MedusaService, MedusaError } from "@camped-ai/framework/utils";
import { SUPPLIER_PRODUCTS_SERVICES_MODULE } from "../modules/supplier-products-services/index";

export interface CleanupResult {
  flagsProcessed: number;
  flagsDismissed: number;
  flagsUpdated: number;
  errors: string[];
  details: Array<{
    productServiceId: string;
    action: 'dismissed' | 'updated' | 'error';
    reason: string;
  }>;
}

/**
 * Service for cleaning up price flags when supplier offerings are removed or deactivated
 */
class PriceFlagCleanupService extends MedusaService({}) {
  protected supplierProductsServicesModuleService_: any;
  protected priceFlagService_: any;
  protected priceComparisonService_: any;

  constructor(container: any) {
    super(container);
    this.supplierProductsServicesModuleService_ = container[SUPPLIER_PRODUCTS_SERVICES_MODULE];
    this.priceFlagService_ = container.priceFlagService;
    this.priceComparisonService_ = container.priceComparisonService;
  }

  /**
   * Clean up price flags for a specific supplier offering that was removed
   */
  async cleanupForRemovedOffering(supplierOfferingId: string): Promise<CleanupResult> {
    const result: CleanupResult = {
      flagsProcessed: 0,
      flagsDismissed: 0,
      flagsUpdated: 0,
      errors: [],
      details: [],
    };

    try {
      // Find all product services that have this supplier offering as their flag source
      const productServicesWithFlag = await this.supplierProductsServicesModuleService_.listProductServices({
        price_flag_active: true,
        price_flag_supplier_offering_id: supplierOfferingId,
      });

      for (const productService of productServicesWithFlag) {
        result.flagsProcessed++;

        try {
          await this.processProductServiceCleanup(productService.id, supplierOfferingId, result);
        } catch (error) {
          result.errors.push(`Error processing product service ${productService.id}: ${error.message}`);
          result.details.push({
            productServiceId: productService.id,
            action: 'error',
            reason: error.message,
          });
        }
      }

      return result;
    } catch (error) {
      throw new MedusaError(
        MedusaError.Types.UNEXPECTED_STATE,
        `Failed to cleanup price flags for removed offering: ${error.message}`
      );
    }
  }

  /**
   * Clean up orphaned price flags (flags pointing to non-existent supplier offerings)
   */
  async cleanupOrphanedFlags(): Promise<CleanupResult> {
    const result: CleanupResult = {
      flagsProcessed: 0,
      flagsDismissed: 0,
      flagsUpdated: 0,
      errors: [],
      details: [],
    };

    try {
      // Get all product services with active price flags
      const productServicesWithFlags = await this.supplierProductsServicesModuleService_.listProductServices({
        price_flag_active: true,
      });

      for (const productService of productServicesWithFlags) {
        result.flagsProcessed++;

        try {
          // Check if the supplier offering still exists and is active
          if (productService.price_flag_supplier_offering_id) {
            const supplierOffering = await this.supplierProductsServicesModuleService_.retrieveSupplierOffering(
              productService.price_flag_supplier_offering_id
            ).catch(() => null);

            if (!supplierOffering || supplierOffering.status !== 'active') {
              // Orphaned flag - dismiss it
              await this.priceFlagService_.dismissPriceFlag(
                productService.id,
                `Supplier offering ${productService.price_flag_supplier_offering_id} no longer exists or is inactive`
              );

              result.flagsDismissed++;
              result.details.push({
                productServiceId: productService.id,
                action: 'dismissed',
                reason: 'Orphaned flag - supplier offering no longer exists or is inactive',
              });
            }
          } else {
            // Flag without supplier offering reference - dismiss it
            await this.priceFlagService_.dismissPriceFlag(
              productService.id,
              'Price flag missing supplier offering reference'
            );

            result.flagsDismissed++;
            result.details.push({
              productServiceId: productService.id,
              action: 'dismissed',
              reason: 'Flag missing supplier offering reference',
            });
          }
        } catch (error) {
          result.errors.push(`Error processing product service ${productService.id}: ${error.message}`);
          result.details.push({
            productServiceId: productService.id,
            action: 'error',
            reason: error.message,
          });
        }
      }

      return result;
    } catch (error) {
      throw new MedusaError(
        MedusaError.Types.UNEXPECTED_STATE,
        `Failed to cleanup orphaned price flags: ${error.message}`
      );
    }
  }

  /**
   * Clean up stale price flags (flags older than a specified age)
   */
  async cleanupStaleFlags(maxAgeHours: number = 168): Promise<CleanupResult> { // Default 7 days
    const result: CleanupResult = {
      flagsProcessed: 0,
      flagsDismissed: 0,
      flagsUpdated: 0,
      errors: [],
      details: [],
    };

    try {
      const cutoffDate = new Date(Date.now() - maxAgeHours * 60 * 60 * 1000);

      // Get all product services with active price flags older than cutoff
      const productServicesWithFlags = await this.supplierProductsServicesModuleService_.listProductServices({
        price_flag_active: true,
        price_flag_created_at: { $lt: cutoffDate },
      });

      for (const productService of productServicesWithFlags) {
        result.flagsProcessed++;

        try {
          // Re-evaluate if the flag is still needed
          if (productService.price_flag_supplier_offering_id) {
            const comparison = await this.priceComparisonService_.comparePrice(
              productService.id,
              productService.price_flag_supplier_offering_id
            );

            if (!comparison.shouldFlag) {
              // Flag no longer needed
              await this.priceFlagService_.dismissPriceFlag(
                productService.id,
                `Stale flag re-evaluation: no longer meets criteria after ${maxAgeHours} hours`
              );

              result.flagsDismissed++;
              result.details.push({
                productServiceId: productService.id,
                action: 'dismissed',
                reason: 'Stale flag no longer meets criteria',
              });
            }
          }
        } catch (error) {
          result.errors.push(`Error processing stale flag for product service ${productService.id}: ${error.message}`);
          result.details.push({
            productServiceId: productService.id,
            action: 'error',
            reason: error.message,
          });
        }
      }

      return result;
    } catch (error) {
      throw new MedusaError(
        MedusaError.Types.UNEXPECTED_STATE,
        `Failed to cleanup stale price flags: ${error.message}`
      );
    }
  }

  /**
   * Process cleanup for a specific product service
   */
  private async processProductServiceCleanup(
    productServiceId: string,
    removedOfferingId: string,
    result: CleanupResult
  ): Promise<void> {
    // Get all remaining active supplier offerings for this product service
    const remainingOfferings = await this.supplierProductsServicesModuleService_.listSupplierOfferings({
      product_service_id: productServiceId,
      status: 'active',
    });

    // Filter out the removed offering
    const activeOfferings = remainingOfferings.filter((offering: any) => 
      offering.id !== removedOfferingId && offering.cost && offering.currency
    );

    if (activeOfferings.length === 0) {
      // No more active offerings, dismiss the flag
      await this.priceFlagService_.dismissPriceFlag(
        productServiceId,
        `All supplier offerings removed for product service`
      );

      result.flagsDismissed++;
      result.details.push({
        productServiceId,
        action: 'dismissed',
        reason: 'No more active supplier offerings',
      });
    } else {
      // Find the new highest price among remaining offerings
      let newHighestOffering = null;
      let newHighestComparison = null;

      for (const offering of activeOfferings) {
        try {
          const comparison = await this.priceComparisonService_.comparePrice(
            productServiceId,
            offering.id
          );

          if (!newHighestOffering || 
              (comparison.supplierPrice > (newHighestComparison?.supplierPrice || 0))) {
            newHighestOffering = offering;
            newHighestComparison = comparison;
          }
        } catch (error) {
          console.error(`Error comparing price for offering ${offering.id}:`, error);
        }
      }

      if (newHighestOffering && newHighestComparison?.shouldFlag) {
        // Update the flag with the new highest offering
        await this.priceFlagService_.updatePriceFlag(productServiceId, {
          productServiceId,
          supplierOfferingId: newHighestOffering.id,
          currentBasePrice: newHighestComparison.currentBasePrice,
          currentBaseCurrency: newHighestComparison.currentBaseCurrency,
          newHighestPrice: newHighestComparison.supplierPrice,
          newHighestCurrency: newHighestComparison.supplierCurrency,
          priceIncrease: newHighestComparison.priceIncrease || 0,
          priceIncreasePercent: newHighestComparison.priceIncreasePercent || 0,
          reason: `Updated after supplier offering ${removedOfferingId} was removed`,
        });

        result.flagsUpdated++;
        result.details.push({
          productServiceId,
          action: 'updated',
          reason: `Updated with new highest offering ${newHighestOffering.id}`,
        });
      } else {
        // New highest price doesn't warrant a flag, dismiss it
        await this.priceFlagService_.dismissPriceFlag(
          productServiceId,
          `New highest price after offering removal doesn't meet flag criteria`
        );

        result.flagsDismissed++;
        result.details.push({
          productServiceId,
          action: 'dismissed',
          reason: 'New highest price does not meet flag criteria',
        });
      }
    }
  }

  /**
   * Run comprehensive cleanup (orphaned, stale, and removed offerings)
   */
  async runComprehensiveCleanup(options: {
    maxAgeHours?: number;
    includeOrphaned?: boolean;
    includeStale?: boolean;
  } = {}): Promise<{
    orphanedResult?: CleanupResult;
    staleResult?: CleanupResult;
    totalFlagsProcessed: number;
    totalFlagsDismissed: number;
    totalFlagsUpdated: number;
    totalErrors: number;
  }> {
    const {
      maxAgeHours = 168,
      includeOrphaned = true,
      includeStale = true,
    } = options;

    const results: any = {};
    let totalFlagsProcessed = 0;
    let totalFlagsDismissed = 0;
    let totalFlagsUpdated = 0;
    let totalErrors = 0;

    if (includeOrphaned) {
      console.log('Running orphaned flags cleanup...');
      results.orphanedResult = await this.cleanupOrphanedFlags();
      totalFlagsProcessed += results.orphanedResult.flagsProcessed;
      totalFlagsDismissed += results.orphanedResult.flagsDismissed;
      totalFlagsUpdated += results.orphanedResult.flagsUpdated;
      totalErrors += results.orphanedResult.errors.length;
    }

    if (includeStale) {
      console.log(`Running stale flags cleanup (max age: ${maxAgeHours} hours)...`);
      results.staleResult = await this.cleanupStaleFlags(maxAgeHours);
      totalFlagsProcessed += results.staleResult.flagsProcessed;
      totalFlagsDismissed += results.staleResult.flagsDismissed;
      totalFlagsUpdated += results.staleResult.flagsUpdated;
      totalErrors += results.staleResult.errors.length;
    }

    return {
      ...results,
      totalFlagsProcessed,
      totalFlagsDismissed,
      totalFlagsUpdated,
      totalErrors,
    };
  }
}

export default PriceFlagCleanupService;
