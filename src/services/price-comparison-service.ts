import { MedusaService, MedusaError } from "@camped-ai/framework/utils";
import { SUPPLIER_PRODUCTS_SERVICES_MODULE } from "../modules/supplier-products-services/index";
import CurrencyConversionService from "./currency-conversion-service";

export interface PriceComparisonResult {
  shouldFlag: boolean;
  shouldAutoPopulate: boolean;
  currentBasePrice: number | null;
  currentBaseCurrency: string | null;
  supplierPrice: number;
  supplierCurrency: string;
  priceIncrease: number | null;
  priceIncreasePercent: number | null;
  isNewHighestPrice: boolean;
  previousHighestPrice: number | null;
  previousHighestCurrency: string | null;
}

export interface CurrencyConversionRate {
  from: string;
  to: string;
  rate: number;
  timestamp: Date;
}

/**
 * Service for comparing prices between Supplier Offerings and Product Services
 * Handles price flagging logic and auto-population decisions
 */
class PriceComparisonService extends MedusaService({}) {
  protected supplierProductsServicesModuleService_: any;
  protected currencyConversionService_: CurrencyConversionService;

  constructor(container: any) {
    super(container);

    // Try to resolve dependencies, with fallbacks
    try {
      this.supplierProductsServicesModuleService_ = container.resolve(SUPPLIER_PRODUCTS_SERVICES_MODULE);
    } catch (error) {
      console.warn("⚠️ [PRICE-COMPARISON] Could not resolve supplier products services module:", error.message);
      this.supplierProductsServicesModuleService_ = null;
    }

    try {
      this.currencyConversionService_ = container.resolve("currencyConversionService");
    } catch (error) {
      console.warn("⚠️ [PRICE-COMPARISON] Could not resolve currency conversion service:", error.message);
      // Create a simple fallback currency service
      this.currencyConversionService_ = {
        convert: async (amount: number, from: string, to: string) => ({ convertedAmount: amount, rate: 1 }),
        getSupportedCurrencies: () => ['CHF', 'EUR', 'USD'],
        getCacheStats: () => ({ hits: 0, misses: 0, size: 0, entries: [] })
      };
    }
  }

  /**
   * Compare a supplier offering price with the current base price
   */
  async comparePrice(
    productServiceId: string,
    supplierOfferingId: string
  ): Promise<PriceComparisonResult> {
    try {
      if (!this.supplierProductsServicesModuleService_) {
        throw new MedusaError(
          MedusaError.Types.UNEXPECTED_STATE,
          "Supplier products services module not available"
        );
      }

      // Get the product service
      const productService = await this.supplierProductsServicesModuleService_.retrieveProductService(
        productServiceId
      );

      // Get the supplier offering
      const supplierOffering = await this.supplierProductsServicesModuleService_.retrieveSupplierOffering(
        supplierOfferingId
      );

      if (!supplierOffering.cost || !supplierOffering.currency) {
        throw new MedusaError(
          MedusaError.Types.INVALID_DATA,
          "Supplier offering must have cost and currency"
        );
      }

      const result: PriceComparisonResult = {
        shouldFlag: false,
        shouldAutoPopulate: false,
        currentBasePrice: productService.base_cost,
        currentBaseCurrency: null, // Will be determined from store settings
        supplierPrice: supplierOffering.cost,
        supplierCurrency: supplierOffering.currency,
        priceIncrease: null,
        priceIncreasePercent: null,
        isNewHighestPrice: false,
        previousHighestPrice: productService.highest_price,
        previousHighestCurrency: productService.highest_price_currency,
      };

      // If no base price exists, should auto-populate
      if (!productService.base_cost) {
        result.shouldAutoPopulate = true;
        result.isNewHighestPrice = true;
        return result;
      }

      // Convert prices to same currency for comparison
      const { normalizedSupplierPrice, normalizedBasePrice } = await this.normalizePrices(
        supplierOffering.cost,
        supplierOffering.currency,
        productService.base_cost,
        productService.highest_price_currency || 'CHF' // Default to CHF
      );

      // Calculate price increase
      result.priceIncrease = normalizedSupplierPrice - normalizedBasePrice;
      result.priceIncreasePercent = (result.priceIncrease / normalizedBasePrice) * 100;

      // Check if this is a new highest price
      if (!productService.highest_price || normalizedSupplierPrice > productService.highest_price) {
        result.isNewHighestPrice = true;
      }

      // Determine if we should flag (price increase > 0 and is new highest)
      result.shouldFlag = result.priceIncrease > 0 && result.isNewHighestPrice;

      return result;
    } catch (error) {
      throw new MedusaError(
        MedusaError.Types.UNEXPECTED_STATE,
        `Failed to compare prices: ${error.message}`
      );
    }
  }

  /**
   * Normalize prices to the same currency for comparison
   */
  private async normalizePrices(
    supplierPrice: number,
    supplierCurrency: string,
    basePrice: number,
    baseCurrency: string
  ): Promise<{ normalizedSupplierPrice: number; normalizedBasePrice: number }> {
    // If currencies are the same, no conversion needed
    if (supplierCurrency === baseCurrency) {
      return {
        normalizedSupplierPrice: supplierPrice,
        normalizedBasePrice: basePrice,
      };
    }

    // Convert supplier price to base currency using the currency conversion service
    const conversionResult = await this.currencyConversionService_.convertAmount(
      supplierPrice,
      supplierCurrency,
      baseCurrency
    );

    return {
      normalizedSupplierPrice: conversionResult.convertedAmount,
      normalizedBasePrice: basePrice,
    };
  }

  /**
   * Check if a price increase should trigger a flag based on thresholds
   */
  async shouldTriggerFlag(
    priceIncreasePercent: number,
    productServiceId: string
  ): Promise<boolean> {
    // Default threshold is any price increase
    // In the future, this could be configurable per product category or supplier
    const defaultThreshold = 0; // 0% increase triggers flag
    
    return priceIncreasePercent > defaultThreshold;
  }

  /**
   * Find the best (lowest) price among all supplier offerings
   */
  async findBestPrice(productServiceId: string): Promise<{
    bestPrice: number;
    bestCurrency: string;
    bestOfferingId: string;
    allPrices: Array<{ price: number; currency: string; offeringId: string; normalized: number }>;
  } | null> {
    const productService = await this.supplierProductsServicesModuleService_.retrieveProductService(
      productServiceId,
      { relations: ["offerings"] }
    );

    const activeOfferings = (productService.offerings || []).filter(
      (offering: any) => offering.status === 'active' && offering.cost
    );

    if (activeOfferings.length === 0) {
      return null;
    }

    const baseCurrency = 'CHF'; // Default comparison currency
    const pricesWithNormalized = [];

    for (const offering of activeOfferings) {
      const { normalizedSupplierPrice } = await this.normalizePrices(
        offering.cost,
        offering.currency,
        0, // Not used in this context
        baseCurrency
      );

      pricesWithNormalized.push({
        price: offering.cost,
        currency: offering.currency,
        offeringId: offering.id,
        normalized: normalizedSupplierPrice,
      });
    }

    // Sort by normalized price to find the best (lowest) price
    pricesWithNormalized.sort((a, b) => a.normalized - b.normalized);
    const best = pricesWithNormalized[0];

    return {
      bestPrice: best.price,
      bestCurrency: best.currency,
      bestOfferingId: best.offeringId,
      allPrices: pricesWithNormalized,
    };
  }

  /**
   * Find the highest price among all supplier offerings
   */
  async findHighestPrice(productServiceId: string): Promise<{
    highestPrice: number;
    highestCurrency: string;
    highestOfferingId: string;
    allPrices: Array<{ price: number; currency: string; offeringId: string; normalized: number }>;
  } | null> {
    const bestPriceResult = await this.findBestPrice(productServiceId);

    if (!bestPriceResult) {
      return null;
    }

    // Sort by normalized price to find the highest price
    const sortedPrices = [...bestPriceResult.allPrices].sort((a, b) => b.normalized - a.normalized);
    const highest = sortedPrices[0];

    return {
      highestPrice: highest.price,
      highestCurrency: highest.currency,
      highestOfferingId: highest.offeringId,
      allPrices: bestPriceResult.allPrices,
    };
  }

  /**
   * Calculate price statistics for a product service
   */
  async calculatePriceStatistics(productServiceId: string): Promise<{
    count: number;
    averagePrice: number;
    medianPrice: number;
    minPrice: number;
    maxPrice: number;
    currency: string;
    priceRange: number;
    standardDeviation: number;
  } | null> {
    const bestPriceResult = await this.findBestPrice(productServiceId);

    if (!bestPriceResult || bestPriceResult.allPrices.length === 0) {
      return null;
    }

    const normalizedPrices = bestPriceResult.allPrices.map(p => p.normalized);
    const count = normalizedPrices.length;

    // Calculate statistics
    const sum = normalizedPrices.reduce((acc, price) => acc + price, 0);
    const averagePrice = sum / count;

    const sortedPrices = [...normalizedPrices].sort((a, b) => a - b);
    const medianPrice = count % 2 === 0
      ? (sortedPrices[count / 2 - 1] + sortedPrices[count / 2]) / 2
      : sortedPrices[Math.floor(count / 2)];

    const minPrice = Math.min(...normalizedPrices);
    const maxPrice = Math.max(...normalizedPrices);
    const priceRange = maxPrice - minPrice;

    // Calculate standard deviation
    const variance = normalizedPrices.reduce((acc, price) => acc + Math.pow(price - averagePrice, 2), 0) / count;
    const standardDeviation = Math.sqrt(variance);

    return {
      count,
      averagePrice,
      medianPrice,
      minPrice,
      maxPrice,
      currency: 'CHF', // Normalized currency
      priceRange,
      standardDeviation,
    };
  }

  /**
   * Get price comparison summary for a product service
   */
  async getPriceComparisonSummary(productServiceId: string): Promise<{
    productService: any;
    activeFlag: boolean;
    flagDetails: any;
    supplierOfferings: any[];
    priceComparisons: PriceComparisonResult[];
    priceStatistics: any;
    bestPrice: any;
    highestPrice: any;
  }> {
    const productService = await this.supplierProductsServicesModuleService_.retrieveProductService(
      productServiceId,
      { relations: ["offerings"] }
    );

    const supplierOfferings = productService.offerings || [];
    const priceComparisons: PriceComparisonResult[] = [];

    // Compare with all supplier offerings
    for (const offering of supplierOfferings) {
      if (offering.status === 'active' && offering.cost) {
        const comparison = await this.comparePrice(productServiceId, offering.id);
        priceComparisons.push(comparison);
      }
    }

    // Get additional price analysis
    const [priceStatistics, bestPrice, highestPrice] = await Promise.all([
      this.calculatePriceStatistics(productServiceId),
      this.findBestPrice(productServiceId),
      this.findHighestPrice(productServiceId),
    ]);

    return {
      productService,
      activeFlag: productService.price_flag_active || false,
      flagDetails: productService.price_flag_active ? {
        createdAt: productService.price_flag_created_at,
        supplierOfferingId: productService.price_flag_supplier_offering_id,
        highestPrice: productService.highest_price,
        highestPriceCurrency: productService.highest_price_currency,
      } : null,
      supplierOfferings,
      priceComparisons,
      priceStatistics,
      bestPrice,
      highestPrice,
    };
  }
}

export default PriceComparisonService;
