import { MedusaService, MedusaError } from "@camped-ai/framework/utils";
import { SUPPLIER_PRODUCTS_SERVICES_MODULE } from "../modules/supplier-products-services/index";
import { ADD_ON_SERVICE } from "../modules/hotel-management/add-on-service/index";

export interface SyncResult {
  success: boolean;
  productServiceId: string;
  addOnsUpdated: number;
  errors: string[];
  details: {
    productService: any;
    addOns: any[];
    priceChanges: Array<{
      addOnId: string;
      oldPrice: number;
      newPrice: number;
      currency: string;
    }>;
  };
}

export interface BulkSyncResult {
  totalProcessed: number;
  successCount: number;
  errorCount: number;
  results: SyncResult[];
  summary: {
    totalAddOnsUpdated: number;
    averageUpdateTime: number;
    errors: string[];
  };
}

/**
 * Service for synchronizing prices between ProductServices and Add-ons
 */
class PriceSynchronizationService extends MedusaService({}) {
  protected supplierProductsServicesModuleService_: any;
  protected addOnService_: any;
  protected addOnSyncService_: any;

  constructor(container: any) {
    super(container);
    this.supplierProductsServicesModuleService_ = container[SUPPLIER_PRODUCTS_SERVICES_MODULE];
    this.addOnService_ = container[ADD_ON_SERVICE];
    this.addOnSyncService_ = container.addOnSyncService;
  }

  /**
   * Synchronize a single ProductService with its corresponding Add-ons
   */
  async syncProductServiceToAddOns(productServiceId: string): Promise<SyncResult> {
    const startTime = Date.now();
    const errors: string[] = [];
    const priceChanges: Array<{
      addOnId: string;
      oldPrice: number;
      newPrice: number;
      currency: string;
    }> = [];

    try {
      // Get the product service
      const productService = await this.supplierProductsServicesModuleService_.retrieveProductService(
        productServiceId
      );

      if (!productService.base_cost) {
        throw new MedusaError(
          MedusaError.Types.INVALID_DATA,
          "Product service has no base cost to sync"
        );
      }

      // Find related add-ons
      const addOns = await this.addOnService_.listAddOnServices({
        metadata: {
          source_product_service_id: productServiceId
        }
      });

      if (addOns.length === 0) {
        return {
          success: true,
          productServiceId,
          addOnsUpdated: 0,
          errors: [],
          details: {
            productService,
            addOns: [],
            priceChanges: [],
          },
        };
      }

      const updatedAddOns: any[] = [];

      // Update each add-on
      for (const addOn of addOns) {
        try {
          const oldPrice = addOn.metadata?.adult_price || 0;
          
          // Use the add-on sync service to update pricing
          const updatedAddOn = await this.addOnSyncService_.syncAddOnFromProductServiceBasePrice(
            productServiceId
          );

          if (updatedAddOn) {
            updatedAddOns.push(updatedAddOn);
            
            const newPrice = updatedAddOn.metadata?.adult_price || 0;
            priceChanges.push({
              addOnId: addOn.id,
              oldPrice,
              newPrice,
              currency: updatedAddOn.metadata?.currency_code || 'CHF',
            });
          }
        } catch (error) {
          errors.push(`Failed to sync add-on ${addOn.id}: ${error.message}`);
        }
      }

      const endTime = Date.now();
      console.log(`Synced ${updatedAddOns.length} add-ons for product service ${productServiceId} in ${endTime - startTime}ms`);

      return {
        success: errors.length === 0,
        productServiceId,
        addOnsUpdated: updatedAddOns.length,
        errors,
        details: {
          productService,
          addOns: updatedAddOns,
          priceChanges,
        },
      };
    } catch (error) {
      return {
        success: false,
        productServiceId,
        addOnsUpdated: 0,
        errors: [error.message],
        details: {
          productService: null,
          addOns: [],
          priceChanges: [],
        },
      };
    }
  }

  /**
   * Bulk synchronize multiple ProductServices with their Add-ons
   */
  async bulkSyncProductServicesToAddOns(
    productServiceIds: string[],
    options: {
      batchSize?: number;
      continueOnError?: boolean;
    } = {}
  ): Promise<BulkSyncResult> {
    const { batchSize = 10, continueOnError = true } = options;
    const startTime = Date.now();
    
    const results: SyncResult[] = [];
    const errors: string[] = [];
    let totalAddOnsUpdated = 0;

    // Process in batches
    for (let i = 0; i < productServiceIds.length; i += batchSize) {
      const batch = productServiceIds.slice(i, i + batchSize);
      
      const batchPromises = batch.map(async (productServiceId) => {
        try {
          return await this.syncProductServiceToAddOns(productServiceId);
        } catch (error) {
          const errorResult: SyncResult = {
            success: false,
            productServiceId,
            addOnsUpdated: 0,
            errors: [error.message],
            details: {
              productService: null,
              addOns: [],
              priceChanges: [],
            },
          };
          
          if (!continueOnError) {
            throw error;
          }
          
          return errorResult;
        }
      });

      try {
        const batchResults = await Promise.all(batchPromises);
        results.push(...batchResults);
        
        // Accumulate statistics
        batchResults.forEach(result => {
          totalAddOnsUpdated += result.addOnsUpdated;
          if (result.errors.length > 0) {
            errors.push(...result.errors);
          }
        });
      } catch (error) {
        if (!continueOnError) {
          throw error;
        }
        errors.push(`Batch processing error: ${error.message}`);
      }
    }

    const endTime = Date.now();
    const totalTime = endTime - startTime;
    const averageUpdateTime = results.length > 0 ? totalTime / results.length : 0;

    const successCount = results.filter(r => r.success).length;
    const errorCount = results.length - successCount;

    return {
      totalProcessed: results.length,
      successCount,
      errorCount,
      results,
      summary: {
        totalAddOnsUpdated,
        averageUpdateTime,
        errors,
      },
    };
  }

  /**
   * Synchronize all ProductServices that have base prices but unsynchronized Add-ons
   */
  async syncAllOutOfSyncProductServices(): Promise<BulkSyncResult> {
    try {
      // Find product services with base prices
      const productServicesWithPrices = await this.supplierProductsServicesModuleService_.listProductServices({
        base_cost: { $ne: null },
        status: 'active',
      });

      const outOfSyncIds: string[] = [];

      // Check which ones have out-of-sync add-ons
      for (const productService of productServicesWithPrices) {
        const addOns = await this.addOnService_.listAddOnServices({
          metadata: {
            source_product_service_id: productService.id
          }
        });

        if (addOns.length > 0) {
          // Check if any add-on needs updating (simplified check)
          const needsUpdate = addOns.some((addOn: any) => {
            const lastUpdated = new Date(addOn.updated_at);
            const productUpdated = new Date(productService.updated_at);
            return productUpdated > lastUpdated;
          });

          if (needsUpdate) {
            outOfSyncIds.push(productService.id);
          }
        }
      }

      console.log(`Found ${outOfSyncIds.length} product services with out-of-sync add-ons`);

      return await this.bulkSyncProductServicesToAddOns(outOfSyncIds);
    } catch (error) {
      throw new MedusaError(
        MedusaError.Types.UNEXPECTED_STATE,
        `Failed to sync out-of-sync product services: ${error.message}`
      );
    }
  }

  /**
   * Get synchronization status for a ProductService
   */
  async getSyncStatus(productServiceId: string): Promise<{
    productService: any;
    addOns: any[];
    inSync: boolean;
    lastSyncTime: Date | null;
    syncNeeded: boolean;
    reasons: string[];
  }> {
    try {
      const productService = await this.supplierProductsServicesModuleService_.retrieveProductService(
        productServiceId
      );

      const addOns = await this.addOnService_.listAddOnServices({
        metadata: {
          source_product_service_id: productServiceId
        }
      });

      const reasons: string[] = [];
      let syncNeeded = false;

      // Check if product service has base price
      if (!productService.base_cost) {
        reasons.push("Product service has no base price");
      }

      // Check if add-ons exist
      if (addOns.length === 0) {
        reasons.push("No add-ons found for this product service");
      } else {
        // Check if add-ons are up to date
        const productUpdated = new Date(productService.updated_at);
        
        addOns.forEach((addOn: any, index: number) => {
          const addOnUpdated = new Date(addOn.updated_at);
          if (productUpdated > addOnUpdated) {
            reasons.push(`Add-on ${index + 1} (${addOn.id}) is outdated`);
            syncNeeded = true;
          }
        });
      }

      // Determine last sync time (approximate)
      const lastSyncTime = addOns.length > 0 
        ? new Date(Math.max(...addOns.map((a: any) => new Date(a.updated_at).getTime())))
        : null;

      const inSync = reasons.length === 0 && !syncNeeded;

      return {
        productService,
        addOns,
        inSync,
        lastSyncTime,
        syncNeeded,
        reasons,
      };
    } catch (error) {
      throw new MedusaError(
        MedusaError.Types.UNEXPECTED_STATE,
        `Failed to get sync status: ${error.message}`
      );
    }
  }

  /**
   * Get synchronization statistics
   */
  async getSyncStatistics(): Promise<{
    totalProductServices: number;
    productServicesWithPrices: number;
    productServicesWithAddOns: number;
    inSyncCount: number;
    outOfSyncCount: number;
    noAddOnsCount: number;
    syncPercentage: number;
  }> {
    try {
      // Get all active product services
      const allProductServices = await this.supplierProductsServicesModuleService_.listProductServices({
        status: 'active',
      });

      // Get product services with base prices
      const withPrices = allProductServices.filter((ps: any) => ps.base_cost);

      let inSyncCount = 0;
      let outOfSyncCount = 0;
      let noAddOnsCount = 0;
      let withAddOnsCount = 0;

      // Check sync status for each
      for (const productService of withPrices) {
        const addOns = await this.addOnService_.listAddOnServices({
          metadata: {
            source_product_service_id: productService.id
          }
        });

        if (addOns.length === 0) {
          noAddOnsCount++;
        } else {
          withAddOnsCount++;
          
          // Simple sync check
          const productUpdated = new Date(productService.updated_at);
          const addOnUpdated = new Date(Math.max(...addOns.map((a: any) => new Date(a.updated_at).getTime())));
          
          if (productUpdated <= addOnUpdated) {
            inSyncCount++;
          } else {
            outOfSyncCount++;
          }
        }
      }

      const syncPercentage = withAddOnsCount > 0 ? (inSyncCount / withAddOnsCount) * 100 : 0;

      return {
        totalProductServices: allProductServices.length,
        productServicesWithPrices: withPrices.length,
        productServicesWithAddOns: withAddOnsCount,
        inSyncCount,
        outOfSyncCount,
        noAddOnsCount,
        syncPercentage,
      };
    } catch (error) {
      throw new MedusaError(
        MedusaError.Types.UNEXPECTED_STATE,
        `Failed to get sync statistics: ${error.message}`
      );
    }
  }
}

export default PriceSynchronizationService;
