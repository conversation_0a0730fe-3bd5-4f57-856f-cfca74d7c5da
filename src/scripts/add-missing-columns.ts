import { Client } from 'pg';

export default async function addMissingColumns() {
  const client = new Client({
    connectionString: process.env.DATABASE_URL,
  });

  try {
    await client.connect();
    console.log("Connected to database successfully");

    // Get the database manager
    const manager = client;

    console.log("Checking if supplier_offering table exists and adding missing columns...");

    // Check if the table exists
    const tableExists = await manager.query(`
      SELECT EXISTS (
        SELECT FROM information_schema.tables
        WHERE table_schema = 'public'
        AND table_name = 'supplier_offering'
      );
    `);

    if (!tableExists.rows[0].exists) {
      console.error("supplier_offering table does not exist!");
      return;
    }

    console.log("supplier_offering table exists, checking for missing columns...");

    // Check if cost column exists
    const costColumnExists = await manager.query(`
      SELECT EXISTS (
        SELECT FROM information_schema.columns
        WHERE table_schema = 'public'
        AND table_name = 'supplier_offering'
        AND column_name = 'cost'
      );
    `);

    // Check if currency column exists
    const currencyColumnExists = await manager.query(`
      SELECT EXISTS (
        SELECT FROM information_schema.columns
        WHERE table_schema = 'public'
        AND table_name = 'supplier_offering'
        AND column_name = 'currency'
      );
    `);

    // Check if currency_override column exists
    const currencyOverrideColumnExists = await manager.query(`
      SELECT EXISTS (
        SELECT FROM information_schema.columns
        WHERE table_schema = 'public'
        AND table_name = 'supplier_offering'
        AND column_name = 'currency_override'
      );
    `);

    console.log(`Column status: cost=${costColumnExists.rows[0].exists}, currency=${currencyColumnExists.rows[0].exists}, currency_override=${currencyOverrideColumnExists.rows[0].exists}`);

    // Add missing columns
    if (!costColumnExists.rows[0].exists) {
      console.log("Adding cost column...");
      await manager.query(`ALTER TABLE supplier_offering ADD COLUMN cost numeric NULL;`);
      console.log("Cost column added successfully");
    }

    if (!currencyColumnExists.rows[0].exists) {
      console.log("Adding currency column...");
      await manager.query(`ALTER TABLE supplier_offering ADD COLUMN currency text NULL;`);
      console.log("Currency column added successfully");
    }

    if (!currencyOverrideColumnExists.rows[0].exists) {
      console.log("Adding currency_override column...");
      await manager.query(`ALTER TABLE supplier_offering ADD COLUMN currency_override boolean NOT NULL DEFAULT false;`);
      console.log("Currency override column added successfully");
    }

    // Add indexes if they don't exist
    console.log("Adding indexes...");

    try {
      await manager.query(`CREATE INDEX IF NOT EXISTS "IDX_supplier_offering_cost" ON "supplier_offering" ("cost") WHERE "deleted_at" IS NULL;`);
      await manager.query(`CREATE INDEX IF NOT EXISTS "IDX_supplier_offering_currency" ON "supplier_offering" ("currency") WHERE "deleted_at" IS NULL;`);
      console.log("Indexes added successfully");
    } catch (error) {
      console.warn("Error adding indexes (they might already exist):", error.message);
    }

    console.log("All missing columns and indexes have been added successfully!");

  } catch (error) {
    console.error("Error adding missing columns:", error);
    throw error;
  } finally {
    await client.end();
  }
}
