import { withClient } from "../utils/db";

async function verifySync() {
  console.log("🔍 Verifying Product Service Sync Fields...");
  
  await withClient(async (client) => {
    // Check if new fields exist
    const schemaResult = await client.query(`
      SELECT column_name, data_type 
      FROM information_schema.columns 
      WHERE table_name = 'product_service' 
      AND column_name IN ('product_id', 'product_variant_id')
    `);
    
    console.log("✅ Schema check:", schemaResult.rows);
    
    // Check sample data
    const dataResult = await client.query(`
      SELECT id, name, product_id, product_variant_id 
      FROM product_service 
      WHERE deleted_at IS NULL 
      LIMIT 3
    `);
    
    console.log("📊 Sample data:", dataResult.rows);
    
    // Check for existing variants
    const variantResult = await client.query(`
      SELECT id, title, sku 
      FROM product_variant 
      WHERE id LIKE 'variant_addon_%' 
      LIMIT 3
    `);
    
    console.log("🎯 Existing variants:", variantResult.rows);
  });
}

verifySync().catch(console.error);
