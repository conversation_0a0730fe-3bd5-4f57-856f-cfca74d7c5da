import {
  linkSalesChannelsToStockLocationWorkflow,
} from "@camped-ai/medusa/core-flows";
import {
  ExecArgs,
} from "@camped-ai/framework/types";
import {
  ContainerRegistrationKeys,
} from "@camped-ai/framework/utils";

export default async function simpleFix({
  container,
}: ExecArgs) {
  const logger = container.resolve("logger");
  const query = container.resolve(ContainerRegistrationKeys.QUERY);

  logger.info("🔧 Simple fix for sales channel to stock location associations...");

  try {
    // 1. Get all sales channels
    const salesChannels = await query.graph({
      entity: "sales_channel",
      fields: ["id", "name", "is_disabled"],
    });

    logger.info(`Found ${salesChannels.data.length} sales channels:`);
    salesChannels.data.forEach((channel: any) => {
      logger.info(`  - ${channel.name} (${channel.id}) - Disabled: ${channel.is_disabled}`);
    });

    // 2. Get all stock locations
    const stockLocations = await query.graph({
      entity: "stock_location",
      fields: ["id", "name"],
    });

    logger.info(`Found ${stockLocations.data.length} stock locations:`);
    stockLocations.data.forEach((location: any) => {
      logger.info(`  - ${location.name} (${location.id})`);
    });

    if (stockLocations.data.length === 0) {
      logger.error("❌ No stock locations found! Please run the seed script first.");
      return;
    }

    const defaultStockLocation = stockLocations.data[0];
    logger.info(`Using default stock location: ${defaultStockLocation.name} (${defaultStockLocation.id})`);

    // 3. Link each active sales channel to the default stock location
    for (const channel of salesChannels.data) {
      if (channel.is_disabled) {
        logger.info(`⏭️ Skipping disabled sales channel: ${channel.name}`);
        continue;
      }

      try {
        logger.info(`🔗 Linking sales channel ${channel.name} to stock location ${defaultStockLocation.name}...`);
        
        await linkSalesChannelsToStockLocationWorkflow(container).run({
          input: {
            id: defaultStockLocation.id,
            add: [channel.id],
          },
        });

        logger.info(`✅ Successfully linked sales channel ${channel.name} to stock location ${defaultStockLocation.name}`);
      } catch (error) {
        if (error.message.includes("already exists") || error.message.includes("duplicate")) {
          logger.info(`✅ Sales channel ${channel.name} already linked to stock location ${defaultStockLocation.name}`);
        } else {
          logger.error(`❌ Failed to link sales channel ${channel.name}: ${error.message}`);
        }
      }
    }

    // 4. Specifically fix the problematic sales channel
    const problematicChannelId = "sc_01JNR887105TH162F04RB9RKC0";
    logger.info(`🎯 Specifically fixing problematic sales channel: ${problematicChannelId}`);
    
    try {
      await linkSalesChannelsToStockLocationWorkflow(container).run({
        input: {
          id: defaultStockLocation.id,
          add: [problematicChannelId],
        },
      });

      logger.info(`✅ Successfully fixed problematic sales channel ${problematicChannelId}`);
    } catch (error) {
      if (error.message.includes("already exists") || error.message.includes("duplicate")) {
        logger.info(`✅ Problematic sales channel ${problematicChannelId} already linked`);
      } else {
        logger.error(`❌ Failed to fix problematic sales channel: ${error.message}`);
      }
    }

    logger.info("✅ Simple fix completed!");

  } catch (error) {
    logger.error("❌ Error during simple fix:", error);
    throw error;
  }
}
