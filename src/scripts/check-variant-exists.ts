#!/usr/bin/env node

/**
 * Check if specific variant exists in the database
 */

import { withClient } from "../utils/db";

async function checkVariantExists() {
  console.log('🔍 Checking if specific variant exists...');
  console.log('=' .repeat(60));

  const targetVariantId = 'variant_addon_01K0RER2Q9S4R1GWPZPXXR2H52';
  
  try {
    return await withClient(async (client) => {
      // Check if the specific variant exists
      console.log(`🔍 Looking for variant: ${targetVariantId}`);
      
      const variantResult = await client.query(`
        SELECT id, title, sku, product_id, metadata
        FROM product_variant 
        WHERE id = $1
      `, [targetVariantId]);

      if (variantResult.rows.length > 0) {
        const variant = variantResult.rows[0];
        console.log(`✅ Variant found:`);
        console.log(`   ID: ${variant.id}`);
        console.log(`   Title: ${variant.title}`);
        console.log(`   SKU: ${variant.sku}`);
        console.log(`   Product ID: ${variant.product_id}`);
        console.log(`   Metadata:`, JSON.stringify(variant.metadata, null, 2));
      } else {
        console.log(`❌ Variant not found: ${targetVariantId}`);
      }

      // Check all variants that start with variant_addon_
      console.log('\n🔍 Checking all add-on variants...');
      const allAddOnVariants = await client.query(`
        SELECT id, title, sku, product_id, metadata
        FROM product_variant 
        WHERE id LIKE 'variant_addon_%'
        ORDER BY id
        LIMIT 10
      `);

      console.log(`Found ${allAddOnVariants.rows.length} add-on variants:`);
      allAddOnVariants.rows.forEach((variant, index) => {
        console.log(`${index + 1}. ${variant.id} - ${variant.title}`);
      });

      // Check if the corresponding product service exists
      console.log('\n🔍 Checking corresponding product service...');
      const productServiceId = 'ps_01K0RER2Q9S4R1GWPZPXXR2H52';
      
      const psResult = await client.query(`
        SELECT id, name, type, status
        FROM product_service 
        WHERE id = $1
      `, [productServiceId]);

      if (psResult.rows.length > 0) {
        const ps = psResult.rows[0];
        console.log(`✅ Product service found:`);
        console.log(`   ID: ${ps.id}`);
        console.log(`   Name: ${ps.name}`);
        console.log(`   Type: ${ps.type}`);
        console.log(`   Status: ${ps.status}`);
      } else {
        console.log(`❌ Product service not found: ${productServiceId}`);
      }

      return {
        variantExists: variantResult.rows.length > 0,
        totalAddOnVariants: allAddOnVariants.rows.length,
        productServiceExists: psResult.rows.length > 0
      };
    });
  } catch (error) {
    console.error('❌ Error checking variant:', error);
    throw error;
  }
}

// Export default function for medusa exec
export default async function() {
  const result = await checkVariantExists();
  console.log('\n📊 Summary:');
  console.log(`   Target variant exists: ${result.variantExists ? '✅' : '❌'}`);
  console.log(`   Total add-on variants: ${result.totalAddOnVariants}`);
  console.log(`   Product service exists: ${result.productServiceExists ? '✅' : '❌'}`);
  return result;
}
