import {
  linkSalesChannelsToStockLocationWorkflow,
  createStockLocationsWorkflow,
} from "@camped-ai/medusa/core-flows";
import {
  ExecArgs,
} from "@camped-ai/framework/types";
import {
  ContainerRegistrationKeys,
  Modules,
} from "@camped-ai/framework/utils";

export default async function fixCartCompletionIssues({
  container,
}: ExecArgs) {
  const logger = container.resolve("logger");
  const query = container.resolve(ContainerRegistrationKeys.QUERY);
  const remoteLink = container.resolve(ContainerRegistrationKeys.REMOTE_LINK);

  logger.info("🔧 Starting cart completion issues diagnosis and fix...");

  try {
    // 1. Check current sales channels
    logger.info("📊 Checking current sales channels...");
    const salesChannels = await query.graph({
      entity: "sales_channel",
      fields: ["id", "name", "is_disabled"],
    });

    logger.info(`Found ${salesChannels.data.length} sales channels:`);
    salesChannels.data.forEach((channel: any) => {
      logger.info(`  - ${channel.name} (${channel.id}) - Disabled: ${channel.is_disabled}`);
    });

    // 2. Check current stock locations
    logger.info("📦 Checking current stock locations...");
    const stockLocations = await query.graph({
      entity: "stock_location",
      fields: ["id", "name"],
    });

    logger.info(`Found ${stockLocations.data.length} stock locations:`);
    stockLocations.data.forEach((location: any) => {
      logger.info(`  - ${location.name} (${location.id})`);
    });

    // 3. Check current sales channel to stock location links
    logger.info("🔗 Checking sales channel to stock location associations...");
    
    for (const channel of salesChannels.data) {
      try {
        const links = await query.graph({
          entity: "sales_channel_stock_location",
          fields: ["sales_channel_id", "stock_location_id"],
          filters: {
            sales_channel_id: channel.id
          }
        });

        if (links.data.length === 0) {
          logger.warn(`❌ Sales channel ${channel.name} (${channel.id}) has NO stock location associations`);
        } else {
          logger.info(`✅ Sales channel ${channel.name} (${channel.id}) is linked to ${links.data.length} stock location(s):`);
          links.data.forEach((link: any) => {
            logger.info(`    - Stock location: ${link.stock_location_id}`);
          });
        }
      } catch (error) {
        logger.warn(`⚠️ Could not check links for sales channel ${channel.id}: ${error.message}`);
      }
    }

    // 4. Create stock location if none exists
    if (stockLocations.data.length === 0) {
      logger.info("🏗️ No stock locations found, creating default stock location...");
      
      const { result: stockLocationResult } = await createStockLocationsWorkflow(
        container
      ).run({
        input: {
          locations: [
            {
              name: "Default Warehouse",
              address: {
                city: "Default City",
                country_code: "US",
                address_1: "Default Address",
              },
            },
          ],
        },
      });

      const newStockLocation = stockLocationResult[0];
      logger.info(`✅ Created stock location: ${newStockLocation.name} (${newStockLocation.id})`);

      // Link fulfillment provider to the new stock location
      await remoteLink.create({
        [Modules.STOCK_LOCATION]: {
          stock_location_id: newStockLocation.id,
        },
        [Modules.FULFILLMENT]: {
          fulfillment_provider_id: "manual_manual",
        },
      });

      stockLocations.data.push(newStockLocation);
    }

    // 5. Fix sales channel to stock location associations
    logger.info("🔧 Fixing sales channel to stock location associations...");
    
    const defaultStockLocation = stockLocations.data[0];
    
    for (const channel of salesChannels.data) {
      if (channel.is_disabled) {
        logger.info(`⏭️ Skipping disabled sales channel: ${channel.name}`);
        continue;
      }

      try {
        // Check if already linked
        const existingLinks = await query.graph({
          entity: "sales_channel_stock_location",
          fields: ["sales_channel_id", "stock_location_id"],
          filters: {
            sales_channel_id: channel.id
          }
        });

        if (existingLinks.data.length === 0) {
          logger.info(`🔗 Linking sales channel ${channel.name} to stock location ${defaultStockLocation.name}...`);
          
          await linkSalesChannelsToStockLocationWorkflow(container).run({
            input: {
              id: defaultStockLocation.id,
              add: [channel.id],
            },
          });

          logger.info(`✅ Successfully linked sales channel ${channel.name} to stock location ${defaultStockLocation.name}`);
        } else {
          logger.info(`✅ Sales channel ${channel.name} already linked to stock location(s)`);
        }
      } catch (error) {
        logger.error(`❌ Failed to link sales channel ${channel.name}: ${error.message}`);
      }
    }

    // 6. Check specific problematic sales channel from error
    const problematicChannelId = "sc_01JNR887105TH162F04RB9RKC0";
    logger.info(`🔍 Checking specific problematic sales channel: ${problematicChannelId}`);
    
    try {
      const specificChannel = await query.graph({
        entity: "sales_channel",
        fields: ["id", "name", "is_disabled"],
        filters: {
          id: problematicChannelId
        }
      });

      if (specificChannel.data.length > 0) {
        const channel = specificChannel.data[0];
        logger.info(`Found problematic channel: ${channel.name} (${channel.id}) - Disabled: ${channel.is_disabled}`);
        
        // Check its links
        const links = await query.graph({
          entity: "sales_channel_stock_location",
          fields: ["sales_channel_id", "stock_location_id"],
          filters: {
            sales_channel_id: channel.id
          }
        });

        if (links.data.length === 0) {
          logger.info(`🔗 Fixing problematic sales channel ${channel.name}...`);
          
          await linkSalesChannelsToStockLocationWorkflow(container).run({
            input: {
              id: defaultStockLocation.id,
              add: [channel.id],
            },
          });

          logger.info(`✅ Fixed problematic sales channel ${channel.name}`);
        } else {
          logger.info(`✅ Problematic sales channel already has ${links.data.length} stock location link(s)`);
        }
      } else {
        logger.warn(`❌ Problematic sales channel ${problematicChannelId} not found`);
      }
    } catch (error) {
      logger.error(`❌ Error checking problematic sales channel: ${error.message}`);
    }

    // 7. Final verification
    logger.info("🔍 Final verification of all sales channel associations...");
    
    for (const channel of salesChannels.data) {
      if (channel.is_disabled) continue;
      
      try {
        const links = await query.graph({
          entity: "sales_channel_stock_location",
          fields: ["sales_channel_id", "stock_location_id"],
          filters: {
            sales_channel_id: channel.id
          }
        });

        if (links.data.length === 0) {
          logger.error(`❌ STILL UNLINKED: Sales channel ${channel.name} (${channel.id})`);
        } else {
          logger.info(`✅ VERIFIED: Sales channel ${channel.name} (${channel.id}) has ${links.data.length} link(s)`);
        }
      } catch (error) {
        logger.error(`❌ Error verifying ${channel.id}: ${error.message}`);
      }
    }

    logger.info("✅ Cart completion issues diagnosis and fix completed!");

  } catch (error) {
    logger.error("❌ Error during cart completion issues fix:", error);
    throw error;
  }
}
