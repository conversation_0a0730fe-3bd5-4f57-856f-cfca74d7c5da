import { withClient } from "../utils/db";

/**
 * <PERSON><PERSON><PERSON> to manually create the product_service_supplier table
 */
async function createProductServiceSupplierTable() {
  try {
    console.log("🚀 Creating product_service_supplier table...");

    await withClient(async (client) => {
      // Create product_service_supplier table
      await client.query(`
        CREATE TABLE IF NOT EXISTS "product_service_supplier" (
          "id" text NOT NULL,
          "product_service_id" text NOT NULL,
          "supplier_id" text NOT NULL,
          "cost" numeric NOT NULL,
          "currency_code" text NOT NULL DEFAULT 'CHF',
          "availability" text NULL,
          "max_capacity" numeric NULL,
          "season" text NULL,
          "valid_from" timestamptz NULL,
          "valid_until" timestamptz NULL,
          "notes" text NULL,
          "lead_time_days" numeric NULL,
          "minimum_order" numeric NULL,
          "is_active" boolean NOT NULL DEFAULT true,
          "is_preferred" boolean NOT NULL DEFAULT false,
          "created_by" text NULL,
          "updated_by" text NULL,
          "created_at" timestamptz NOT NULL DEFAULT now(),
          "updated_at" timestamptz NOT NULL DEFAULT now(),
          "deleted_at" timestamptz NULL,
          CONSTRAINT "product_service_supplier_pkey" PRIMARY KEY ("id")
        );
      `);

      console.log("✅ product_service_supplier table created successfully");

      // Create indexes for better performance
      await client.query(`CREATE INDEX IF NOT EXISTS "IDX_product_service_supplier_ps_id" ON "product_service_supplier" ("product_service_id") WHERE "deleted_at" IS NULL;`);
      await client.query(`CREATE INDEX IF NOT EXISTS "IDX_product_service_supplier_supplier_id" ON "product_service_supplier" ("supplier_id") WHERE "deleted_at" IS NULL;`);
      await client.query(`CREATE UNIQUE INDEX IF NOT EXISTS "IDX_product_service_supplier_unique" ON "product_service_supplier" ("product_service_id", "supplier_id") WHERE "deleted_at" IS NULL;`);
      await client.query(`CREATE INDEX IF NOT EXISTS "IDX_product_service_supplier_is_active" ON "product_service_supplier" ("is_active") WHERE "deleted_at" IS NULL;`);
      await client.query(`CREATE INDEX IF NOT EXISTS "IDX_product_service_supplier_is_preferred" ON "product_service_supplier" ("is_preferred") WHERE "deleted_at" IS NULL;`);

      console.log("✅ Indexes created successfully");

      // Add foreign key constraints
      await client.query(`ALTER TABLE "product_service_supplier" ADD CONSTRAINT "product_service_supplier_product_service_id_foreign" FOREIGN KEY ("product_service_id") REFERENCES "product_service" ("id") ON DELETE CASCADE;`);

      console.log("✅ Foreign key constraints added successfully");

      // Test if table exists by querying it
      const result = await client.query(`SELECT COUNT(*) FROM "product_service_supplier";`);
      console.log(`✅ Table verification successful. Current row count: ${result.rows[0].count}`);
    });

    console.log("🎉 product_service_supplier table setup completed successfully!");
  } catch (error) {
    console.error("❌ Failed to create product_service_supplier table:", error);
    throw error;
  }
}

// Run the script if called directly
if (require.main === module) {
  createProductServiceSupplierTable()
    .then(() => {
      console.log("Script completed successfully");
      process.exit(0);
    })
    .catch((error) => {
      console.error("Script failed:", error);
      process.exit(1);
    });
}

export default createProductServiceSupplierTable;
