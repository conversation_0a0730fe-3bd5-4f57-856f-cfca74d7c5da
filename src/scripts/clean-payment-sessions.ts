import {
  ExecArgs,
} from "@camped-ai/framework/types";
import {
  ContainerRegistrationKeys,
  Modules,
} from "@camped-ai/framework/utils";

export default async function cleanPaymentSessions({
  container,
}: ExecArgs) {
  const logger = container.resolve("logger");
  const query = container.resolve(ContainerRegistrationKeys.QUERY);

  logger.info("🧹 Cleaning up problematic payment sessions...");

  try {
    const cartId = "cart_01K1ARWZK6HCE4XH4MH8VR682V";
    const problematicPaymentSessionId = "payses_01K1ARY46KWKXY2QQDC8AZFP1W";

    // 1. Check the cart and its payment collections
    logger.info(`📋 Checking cart: ${cartId}`);
    
    const cart = await query.graph({
      entity: "cart",
      fields: [
        "id",
        "email",
        "currency_code",
        "total",
        "subtotal",
        "metadata",
        "sales_channel_id",
        "region_id"
      ],
      filters: {
        id: cartId
      }
    });

    if (cart.data.length === 0) {
      logger.warn(`❌ Cart ${cartId} not found`);
      return;
    }

    const cartData = cart.data[0];
    logger.info(`✅ Found cart: ${cartData.id} - Total: ${cartData.total}`);

    // 2. Get payment collections for this cart
    const paymentCollections = await query.graph({
      entity: "cart_payment_collection",
      fields: [
        "cart_id",
        "payment_collection.id",
        "payment_collection.status",
        "payment_collection.amount",
        "payment_collection.currency_code"
      ],
      filters: {
        cart_id: cartId
      }
    });

    if (paymentCollections.data.length === 0) {
      logger.warn(`❌ No payment collections found for cart ${cartId}`);
      return;
    }

    logger.info(`✅ Found ${paymentCollections.data.length} payment collection(s)`);

    // 3. Get all payment sessions for these collections
    const paymentModuleService = container.resolve(Modules.PAYMENT);
    
    for (const pc of paymentCollections.data) {
      const collectionId = pc.payment_collection.id;
      logger.info(`🔍 Checking payment sessions for collection: ${collectionId}`);
      
      try {
        const paymentSessions = await paymentModuleService.listPaymentSessions({
          payment_collection_id: collectionId
        });

        logger.info(`   Found ${paymentSessions.length} payment session(s):`);
        
        for (const session of paymentSessions) {
          logger.info(`     - ${session.id} (Status: ${session.status}, Amount: ${session.amount})`);
          
          // Check if this is the problematic session
          if (session.id === problematicPaymentSessionId) {
            logger.warn(`⚠️ Found problematic payment session: ${session.id}`);
            logger.info(`     Status: ${session.status}`);
            logger.info(`     Amount: ${session.amount}`);
            logger.info(`     Provider: ${session.provider_id}`);
            
            // Try to handle the session based on its status
            if (session.status === "authorized") {
              logger.info(`🔄 Attempting to capture the authorized payment session...`);
              
              try {
                // Try to capture the payment instead of cancelling it
                await paymentModuleService.capturePaymentSession(session.id, {
                  context: {
                    captured_by: "system_cleanup",
                    captured_at: new Date().toISOString(),
                    notes: "Captured during cart completion cleanup"
                  }
                });
                
                logger.info(`✅ Successfully captured payment session: ${session.id}`);
              } catch (captureError) {
                logger.warn(`⚠️ Could not capture payment session: ${captureError.message}`);
                
                // If capture fails, try to void/cancel it
                try {
                  logger.info(`🔄 Attempting to void the payment session...`);
                  await paymentModuleService.deletePaymentSession(session.id);
                  logger.info(`✅ Successfully voided payment session: ${session.id}`);
                } catch (voidError) {
                  logger.error(`❌ Could not void payment session: ${voidError.message}`);
                  
                  // Last resort: try to update its status
                  try {
                    logger.info(`🔄 Attempting to update payment session status...`);
                    await paymentModuleService.updatePaymentSession(session.id, {
                      status: "captured"
                    });
                    logger.info(`✅ Successfully updated payment session status: ${session.id}`);
                  } catch (updateError) {
                    logger.error(`❌ Could not update payment session status: ${updateError.message}`);
                  }
                }
              }
            } else if (session.status === "pending" || session.status === "requires_more") {
              logger.info(`🔄 Attempting to cancel pending payment session...`);
              
              try {
                await paymentModuleService.deletePaymentSession(session.id);
                logger.info(`✅ Successfully cancelled payment session: ${session.id}`);
              } catch (cancelError) {
                logger.error(`❌ Could not cancel payment session: ${cancelError.message}`);
              }
            } else {
              logger.info(`ℹ️ Payment session ${session.id} has status ${session.status} - no action needed`);
            }
          }
        }
      } catch (error) {
        logger.error(`❌ Error checking payment sessions for collection ${collectionId}: ${error.message}`);
      }
    }

    // 4. Check if we can now complete the cart
    logger.info(`🎯 Attempting to complete cart after cleanup...`);
    
    try {
      // Import the workflow here to avoid circular dependencies
      const { completeCartWorkflow } = await import("@camped-ai/medusa/core-flows");
      
      const { result: order } = await completeCartWorkflow(container).run({
        input: { id: cartId }
      });

      logger.info(`🎉 SUCCESS! Cart completed successfully. Order ID: ${order.id}`);
      
    } catch (completionError) {
      logger.error(`❌ Cart completion still failed: ${completionError.message}`);
      logger.info(`   This might require manual intervention or a different approach`);
    }

    logger.info("✅ Payment session cleanup completed!");

  } catch (error) {
    logger.error("❌ Error during payment session cleanup:", error);
    throw error;
  }
}
