#!/usr/bin/env node

/**
 * Sync all missing product services to create variants
 * This will find all product services that don't have corresponding variants and create them
 */

import { withClient } from "../utils/db";

async function syncAllMissingAddOns() {
  console.log('🔄 Syncing all missing add-on variants...');
  console.log('=' .repeat(60));

  try {
    return await withClient(async (client) => {
      // 1. Find all product services that don't have corresponding variants
      console.log('🔍 Finding product services without variants...');
      
      const missingVariantsQuery = `
        SELECT 
          ps.*,
          c.name as category_name,
          ut.name as unit_type_name
        FROM product_service ps
        LEFT JOIN product_service_category c ON ps.category_id = c.id
        LEFT JOIN product_service_unit_type ut ON ps.unit_type_id = ut.id
        WHERE ps.status = 'active' 
          AND ps.deleted_at IS NULL
          AND NOT EXISTS (
            SELECT 1 FROM product_variant pv 
            WHERE pv.id = CONCAT('variant_addon_', REPLACE(ps.id, 'ps_', ''))
          )
        ORDER BY ps.created_at DESC
      `;
      
      const missingResult = await client.query(missingVariantsQuery);
      const missingProductServices = missingResult.rows;
      
      console.log(`📊 Found ${missingProductServices.length} product services without variants`);
      
      if (missingProductServices.length === 0) {
        return {
          success: true,
          message: 'All product services already have variants',
          synced: [],
          total: 0
        };
      }
      
      const syncedVariants = [];
      
      // 2. Create variants for each missing product service
      for (const ps of missingProductServices) {
        try {
          console.log(`\n🔄 Syncing: ${ps.name} (${ps.id})`);
          
          // Generate IDs
          const productId = `prod_addon_${ps.id.replace('ps_', '')}`;
          const variantId = `variant_addon_${ps.id.replace('ps_', '')}`;
          
          // Calculate pricing
          const baseCost = ps.base_cost || 50;
          const margin = 0.20;
          const sellingPrice = Math.round((baseCost / (1 - margin)) * 100) / 100;
          
          // Create metadata
          const productMetadata = {
            add_on_service: true,
            supplier_product_service_id: ps.id,
            service_type: ps.type?.toLowerCase() || 'service',
            category: ps.category_name || 'General',
            unit_type: ps.unit_type_name || 'Per unit',
            base_cost: baseCost,
            selling_price: sellingPrice,
            margin_percentage: margin * 100,
            custom_fields: ps.custom_fields || {},
            supplier_id: ps.supplier_id,
            status: ps.status
          };
          
          const variantMetadata = {
            add_on_service: true,
            supplier_product_service_id: ps.id,
            service_type: ps.type?.toLowerCase() || 'service',
            price_type: 'standard',
            base_cost: baseCost,
            selling_price: sellingPrice,
            margin_percentage: margin * 100,
            custom_fields: ps.custom_fields || {},
            status: 'Active'
          };
          
          // Check if product exists
          const existingProduct = await client.query(
            'SELECT id FROM product WHERE id = $1',
            [productId]
          );
          
          // Create product if it doesn't exist
          if (existingProduct.rows.length === 0) {
            const insertProductQuery = `
              INSERT INTO product (
                id, title, subtitle, description, handle, status,
                metadata, created_at, updated_at
              ) VALUES (
                $1, $2, $3, $4, $5, 'published',
                $6, NOW(), NOW()
              )
            `;
            
            await client.query(insertProductQuery, [
              productId,
              ps.name,
              `${ps.category_name || 'Service'} - ${ps.unit_type_name || 'Per unit'}`,
              ps.description || `${ps.type || 'Service'}: ${ps.name}`,
              `addon-${ps.id.replace('ps_', '')}`,
              JSON.stringify(productMetadata)
            ]);
          }
          
          // Create variant
          const insertVariantQuery = `
            INSERT INTO product_variant (
              id, title, sku, barcode, ean, upc, 
              weight, length, height, width, origin_country,
              hs_code, mid_code, material, metadata,
              product_id, created_at, updated_at
            ) VALUES (
              $1, $2, $3, null, null, null,
              null, null, null, null, null,
              null, null, null, $4,
              $5, NOW(), NOW()
            )
          `;
          
          await client.query(insertVariantQuery, [
            variantId,
            ps.name,
            `ADDON-${ps.id.replace('ps_', '').toUpperCase()}`,
            JSON.stringify(variantMetadata),
            productId
          ]);
          
          syncedVariants.push({
            productServiceId: ps.id,
            productServiceName: ps.name,
            productId,
            variantId,
            baseCost,
            sellingPrice
          });
          
          console.log(`✅ Created variant: ${variantId}`);
          
        } catch (error) {
          console.error(`❌ Failed to sync ${ps.name}:`, error.message);
        }
      }
      
      console.log(`\n📊 Sync Summary:`);
      console.log(`   Total missing: ${missingProductServices.length}`);
      console.log(`   Successfully synced: ${syncedVariants.length}`);
      console.log(`   Failed: ${missingProductServices.length - syncedVariants.length}`);
      
      return {
        success: true,
        message: `Synced ${syncedVariants.length} of ${missingProductServices.length} missing variants`,
        synced: syncedVariants,
        total: missingProductServices.length
      };
    });
  } catch (error) {
    console.error('❌ Error syncing missing add-ons:', error);
    throw error;
  }
}

// Export default function for medusa exec
export default async function() {
  const result = await syncAllMissingAddOns();
  console.log('\n🎯 Final Result:');
  console.log(`   Success: ${result.success ? '✅' : '❌'}`);
  console.log(`   Message: ${result.message}`);
  console.log(`   Synced variants: ${result.synced.length}`);
  
  if (result.synced.length > 0) {
    console.log('\n📋 Synced Variants:');
    result.synced.forEach((item, index) => {
      console.log(`${index + 1}. ${item.variantId} - ${item.productServiceName}`);
    });
  }
  
  return result;
}
