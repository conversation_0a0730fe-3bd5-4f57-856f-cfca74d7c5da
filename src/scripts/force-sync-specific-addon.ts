#!/usr/bin/env node

/**
 * Force sync a specific product service to create the missing variant
 */

import { withClient } from "../utils/db";

async function forceSyncSpecificAddOn() {
  console.log('🔄 Force syncing specific add-on...');
  console.log('=' .repeat(60));

  const targetProductServiceId = 'ps_01K0RER2Q9S4R1GWPZPXXR2H52';
  
  try {
    return await withClient(async (client) => {
      // 1. Get the specific product service
      console.log(`🔍 Looking for product service: ${targetProductServiceId}`);
      
      const productServiceQuery = `
        SELECT 
          ps.*,
          c.name as category_name,
          ut.name as unit_type_name
        FROM product_service ps
        LEFT JOIN product_service_category c ON ps.category_id = c.id
        LEFT JOIN product_service_unit_type ut ON ps.unit_type_id = ut.id
        WHERE ps.id = $1
      `;
      
      const psResult = await client.query(productServiceQuery, [targetProductServiceId]);
      
      if (psResult.rows.length === 0) {
        throw new Error(`Product service ${targetProductServiceId} not found`);
      }
      
      const ps = psResult.rows[0];
      console.log(`✅ Found product service: ${ps.name}`);
      
      // 2. Generate the correct IDs
      const productId = `prod_addon_${ps.id.replace('ps_', '')}`;
      const variantId = `variant_addon_${ps.id.replace('ps_', '')}`;
      
      console.log(`📝 Generated IDs:`);
      console.log(`   Product ID: ${productId}`);
      console.log(`   Variant ID: ${variantId}`);
      
      // 3. Check if they already exist
      const existingProductResult = await client.query(
        'SELECT id FROM product WHERE id = $1',
        [productId]
      );
      
      const existingVariantResult = await client.query(
        'SELECT id FROM product_variant WHERE id = $1',
        [variantId]
      );
      
      console.log(`📊 Existing records:`);
      console.log(`   Product exists: ${existingProductResult.rows.length > 0 ? '✅' : '❌'}`);
      console.log(`   Variant exists: ${existingVariantResult.rows.length > 0 ? '✅' : '❌'}`);
      
      // 4. If variant already exists, we're done
      if (existingVariantResult.rows.length > 0) {
        console.log(`✅ Variant already exists: ${variantId}`);
        return { success: true, message: 'Variant already exists', variantId };
      }
      
      // 5. Calculate pricing
      const baseCost = ps.base_cost || 50;
      const margin = 0.20;
      const sellingPrice = Math.round((baseCost / (1 - margin)) * 100) / 100;
      
      console.log(`💰 Pricing calculation:`);
      console.log(`   Base cost: ${baseCost} CHF`);
      console.log(`   Margin: ${margin * 100}%`);
      console.log(`   Selling price: ${sellingPrice} CHF`);
      
      // 6. Create product metadata
      const productMetadata = {
        add_on_service: true,
        supplier_product_service_id: ps.id,
        service_type: ps.type?.toLowerCase() || 'service',
        category: ps.category_name || 'General',
        unit_type: ps.unit_type_name || 'Per unit',
        base_cost: baseCost,
        selling_price: sellingPrice,
        margin_percentage: margin * 100,
        custom_fields: ps.custom_fields || {},
        supplier_id: ps.supplier_id,
        status: ps.status
      };
      
      // 7. Create variant metadata
      const variantMetadata = {
        add_on_service: true,
        supplier_product_service_id: ps.id,
        service_type: ps.type?.toLowerCase() || 'service',
        price_type: 'standard',
        base_cost: baseCost,
        selling_price: sellingPrice,
        margin_percentage: margin * 100,
        custom_fields: ps.custom_fields || {},
        status: 'Active'
      };
      
      // 8. Create product if it doesn't exist
      if (existingProductResult.rows.length === 0) {
        console.log(`📝 Creating product: ${productId}`);
        
        const insertProductQuery = `
          INSERT INTO product (
            id, title, subtitle, description, handle, status,
            metadata, created_at, updated_at
          ) VALUES (
            $1, $2, $3, $4, $5, 'published',
            $6, NOW(), NOW()
          ) ON CONFLICT (id) DO UPDATE SET
            metadata = EXCLUDED.metadata,
            updated_at = NOW()
        `;
        
        await client.query(insertProductQuery, [
          productId,
          ps.name,
          `${ps.category_name || 'Service'} - ${ps.unit_type_name || 'Per unit'}`,
          ps.description || `${ps.type || 'Service'}: ${ps.name}`,
          `addon-${ps.id.replace('ps_', '')}`,
          JSON.stringify(productMetadata)
        ]);
        
        console.log(`✅ Product created: ${productId}`);
      }
      
      // 9. Create variant
      console.log(`📝 Creating variant: ${variantId}`);
      
      const insertVariantQuery = `
        INSERT INTO product_variant (
          id, title, sku, barcode, ean, upc, 
          weight, length, height, width, origin_country,
          hs_code, mid_code, material, metadata,
          product_id, created_at, updated_at
        ) VALUES (
          $1, $2, $3, null, null, null,
          null, null, null, null, null,
          null, null, null, $4,
          $5, NOW(), NOW()
        ) ON CONFLICT (id) DO UPDATE SET
          metadata = EXCLUDED.metadata,
          updated_at = NOW()
      `;
      
      await client.query(insertVariantQuery, [
        variantId,
        ps.name,
        `ADDON-${ps.id.replace('ps_', '').toUpperCase()}`,
        JSON.stringify(variantMetadata),
        productId
      ]);
      
      console.log(`✅ Variant created: ${variantId}`);
      
      // 10. Verify creation
      const verifyResult = await client.query(
        'SELECT id, title FROM product_variant WHERE id = $1',
        [variantId]
      );
      
      if (verifyResult.rows.length > 0) {
        console.log(`✅ Verification successful: ${verifyResult.rows[0].title}`);
        return {
          success: true,
          message: 'Variant created successfully',
          productId,
          variantId,
          productServiceId: ps.id,
          productServiceName: ps.name
        };
      } else {
        throw new Error('Variant creation verification failed');
      }
    });
  } catch (error) {
    console.error('❌ Error force syncing add-on:', error);
    throw error;
  }
}

// Export default function for medusa exec
export default async function() {
  const result = await forceSyncSpecificAddOn();
  console.log('\n📊 Force Sync Result:');
  console.log(`   Success: ${result.success ? '✅' : '❌'}`);
  console.log(`   Message: ${result.message}`);
  if (result.variantId) {
    console.log(`   Variant ID: ${result.variantId}`);
  }
  return result;
}
