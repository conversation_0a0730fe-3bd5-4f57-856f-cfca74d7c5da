import { withClient } from "../utils/db";

async function checkSupplierOfferings() {
  try {
    await withClient(async (client) => {
      console.log('🔍 Checking unique constraints on supplier_offering table...');
      
      const constraints = await client.query(`
        SELECT 
          indexname, 
          indexdef 
        FROM pg_indexes 
        WHERE tablename = 'supplier_offering' 
        AND indexdef LIKE '%UNIQUE%';
      `);
      
      console.log('Unique constraints found:');
      constraints.rows.forEach(row => {
        console.log(`- ${row.indexname}: ${row.indexdef}`);
      });
      
      console.log('\n🔍 Checking existing supplier offerings...');
      const records = await client.query(`
        SELECT id, product_service_id, supplier_id, active_from, active_to, custom_fields, created_at 
        FROM supplier_offering 
        WHERE supplier_id = 'supplier_01JZ9YK8W6NMXBQ6GARDG9JNXT' 
        AND product_service_id = 'ps_01JZA6YSS59XGDZC7MK7697V4E'
        AND deleted_at IS NULL;
      `);
      
      console.log(`Found ${records.rows.length} existing records:`);
      records.rows.forEach((row, index) => {
        console.log(`${index + 1}. ID: ${row.id}`);
        console.log(`   Created: ${row.created_at}`);
        console.log(`   Active From: ${row.active_from}`);
        console.log(`   Active To: ${row.active_to}`);
        console.log(`   Custom Fields: ${JSON.stringify(row.custom_fields)}`);
        console.log('');
      });
      
      if (records.rows.length > 0) {
        console.log('⚠️  Found existing records that might be causing the conflict.');
        console.log('💡 Deleting existing records to resolve the conflict...');
        
        for (const record of records.rows) {
          await client.query(`
            UPDATE supplier_offering 
            SET deleted_at = NOW() 
            WHERE id = $1;
          `, [record.id]);
          console.log(`✅ Soft deleted record: ${record.id}`);
        }
      } else {
        console.log('✅ No conflicting records found.');
      }
    });
    
    console.log('✅ Check completed successfully.');
  } catch (error) {
    console.error('❌ Error:', error.message);
    process.exit(1);
  }
}

checkSupplierOfferings();
