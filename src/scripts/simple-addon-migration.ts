#!/usr/bin/env node

/**
 * Simple Add-on Migration Script
 *
 * This script performs a simplified migration of supplier products to add-ons
 * demonstrating the core migration functionality.
 */

import { withClient } from "../utils/db";

async function performSimpleMigration() {
  console.log('🚀 Starting Simple Add-on Migration');
  console.log('=' .repeat(50));

  try {
    await withClient(async (client) => {
      // 1. Backup existing add-ons (simulation)
      console.log('\n💾 Step 1: Backing up existing add-ons...');
      const existingAddOns = await client.query(`
        SELECT id, metadata
        FROM product
        WHERE metadata::text LIKE '%add_on_service%'
        AND deleted_at IS NULL
      `);

      console.log(`✅ Found ${existingAddOns.rows.length} existing add-ons to backup`);

      // Simulate backup by logging to sync table
      for (const addOn of existingAddOns.rows) {
        await client.query(`
          INSERT INTO add_on_sync_log (
            id,
            supplier_product_service_id,
            add_on_product_id,
            sync_action,
            sync_status,
            sync_data,
            created_at
          ) VALUES (
            'backup_' || $1 || '_' || extract(epoch from now()),
            'legacy_addon',
            $1,
            'backup',
            'success',
            $2,
            now()
          )
        `, [addOn.id, JSON.stringify({ original_metadata: addOn.metadata })]);
      }
      console.log(`✅ Backed up ${existingAddOns.rows.length} add-ons to sync log`);

      // 2. Get supplier products for migration
      console.log('\n🔄 Step 2: Processing supplier products...');
      const supplierProducts = await client.query(`
        SELECT id, name, type, base_cost, status
        FROM product_service
        WHERE status = 'active'
        AND deleted_at IS NULL
        ORDER BY created_at
      `);

      console.log(`📊 Found ${supplierProducts.rows.length} supplier products to process`);

      // 3. Simulate add-on creation for each supplier product
      let migratedCount = 0;
      for (const product of supplierProducts.rows) {
        try {
          console.log(`\n📦 Processing: ${product.name} (${product.type})`);

          // Calculate pricing with 20% margin using formula: price = Net cost / (1 - margin/100)
          const baseCost = parseFloat(product.base_cost) || 0;
          const margin = 20;
          const adultPrice = Math.round((baseCost / (1 - margin / 100)) * 100); // Convert to cents
          const childPrice = Math.round(adultPrice * 0.75); // 25% discount for children
          const packagePrice = Math.round(adultPrice * 0.9); // 10% discount for packages

          console.log(`  💰 Pricing calculated:`);
          console.log(`     - Base Cost: CHF ${baseCost.toFixed(2)}`);
          console.log(`     - Adult Price: CHF ${(adultPrice / 100).toFixed(2)}`);
          console.log(`     - Child Price: CHF ${(childPrice / 100).toFixed(2)}`);
          console.log(`     - Package Price: CHF ${(packagePrice / 100).toFixed(2)}`);

          // Create enhanced metadata
          const enhancedMetadata = {
            add_on_service: true,
            service_type: product.type,
            service_level: 'hotel', // Default to hotel level
            pricing_type: 'per_person',
            is_active: true,

            // Supplier integration fields
            supplier_product_service_id: product.id,
            supplier_id: 'direct_product',
            supplier_name: 'Direct Product',
            base_cost: baseCost,
            base_currency: 'CHF',
            margin_percentage: margin,
            cost_calculation_method: 'percentage_margin',

            // Enhanced filtering fields
            supplier_category: product.type,
            supplier_tags: [product.type, 'migrated'],
            supplier_custom_fields: {},

            // Synchronization fields
            last_sync_date: new Date().toISOString(),
            sync_status: 'synced',

            // Migration tracking
            migration_source: 'simple_migration',
            migration_date: new Date().toISOString(),

            // Pricing data
            prices: {
              CHF: {
                adult_price: adultPrice,
                child_price: childPrice,
                package_price: packagePrice
              }
            },
            default_currency: 'CHF',
            currency_code: 'CHF',
            adult_price: adultPrice,
            child_price: childPrice,
            package_price: packagePrice
          };

          // Log the migration operation (simulate add-on creation)
          await client.query(`
            INSERT INTO add_on_sync_log (
              id,
              supplier_product_service_id,
              add_on_product_id,
              sync_action,
              sync_status,
              sync_data,
              created_at
            ) VALUES (
              'migrate_' || $1 || '_' || extract(epoch from now()),
              $1,
              'addon_' || $1,
              'migrate',
              'success',
              $2,
              now()
            )
          `, [product.id, JSON.stringify({
            product_name: product.name,
            product_type: product.type,
            metadata: enhancedMetadata,
            pricing: {
              adult_price: adultPrice,
              child_price: childPrice,
              package_price: packagePrice
            }
          })]);

          console.log(`  ✅ Migration logged for: ${product.name}`);
          migratedCount++;

        } catch (error) {
          console.error(`  ❌ Failed to process ${product.name}:`, error.message);

          // Log the error
          await client.query(`
            INSERT INTO add_on_sync_log (
              id,
              supplier_product_service_id,
              sync_action,
              sync_status,
              error_message,
              created_at
            ) VALUES (
              'error_' || $1 || '_' || extract(epoch from now()),
              $1,
              'migrate',
              'error',
              $2,
              now()
            )
          `, [product.id, error.message]);
        }
      }

      // 4. Summary
      console.log('\n📊 Migration Summary:');
      console.log('=' .repeat(50));
      console.log(`✅ Successfully processed: ${migratedCount} products`);
      console.log(`📝 Total operations logged: ${existingAddOns.rows.length + supplierProducts.rows.length}`);

      // Check sync log
      const syncLogResult = await client.query(`
        SELECT sync_action, sync_status, COUNT(*) as count
        FROM add_on_sync_log
        WHERE created_at >= now() - interval '1 hour'
        GROUP BY sync_action, sync_status
        ORDER BY sync_action, sync_status
      `);

      console.log('\n📋 Sync Log Summary:');
      syncLogResult.rows.forEach(row => {
        console.log(`  ${row.sync_action} (${row.sync_status}): ${row.count}`);
      });

      console.log('\n🎉 Simple migration completed successfully!');
      console.log('\n💡 Next Steps:');
      console.log('  1. Review sync logs in add_on_sync_log table');
      console.log('  2. Implement actual product creation in full migration');
      console.log('  3. Set up event subscribers for ongoing sync');
      console.log('  4. Configure supplier-specific settings');
    });
  } catch (error) {
    console.error('❌ Simple migration failed:', error);
    throw error;
  }
}

// Export the function for medusa exec
export default performSimpleMigration;