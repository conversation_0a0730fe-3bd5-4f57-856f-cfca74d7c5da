#!/usr/bin/env node

/**
 * <PERSON><PERSON><PERSON> to check the structure of supplier-related tables
 */

import { withClient } from "../utils/db";

async function checkTableStructure() {
  console.log('🔍 Checking Table Structures');
  console.log('=' .repeat(50));

  try {
    await withClient(async (client) => {
      // Check product_service table structure
      console.log('\n📊 product_service table structure:');
      const psColumns = await client.query(`
        SELECT column_name, data_type, is_nullable, column_default
        FROM information_schema.columns
        WHERE table_name = 'product_service'
        AND table_schema = 'public'
        ORDER BY ordinal_position
      `);

      psColumns.rows.forEach(col => {
        console.log(`  - ${col.column_name}: ${col.data_type} ${col.is_nullable === 'NO' ? '(NOT NULL)' : '(NULL)'}`);
      });

      // Check product_service_supplier table structure
      console.log('\n📊 product_service_supplier table structure:');
      const pssColumns = await client.query(`
        SELECT column_name, data_type, is_nullable, column_default
        FROM information_schema.columns
        WHERE table_name = 'product_service_supplier'
        AND table_schema = 'public'
        ORDER BY ordinal_position
      `);

      pssColumns.rows.forEach(col => {
        console.log(`  - ${col.column_name}: ${col.data_type} ${col.is_nullable === 'NO' ? '(NOT NULL)' : '(NULL)'}`);
      });

      // Check supplier table structure
      console.log('\n📊 supplier table structure:');
      const supplierColumns = await client.query(`
        SELECT column_name, data_type, is_nullable, column_default
        FROM information_schema.columns
        WHERE table_name = 'supplier'
        AND table_schema = 'public'
        ORDER BY ordinal_position
      `);

      supplierColumns.rows.forEach(col => {
        console.log(`  - ${col.column_name}: ${col.data_type} ${col.is_nullable === 'NO' ? '(NOT NULL)' : '(NULL)'}`);
      });

      // Check sample data
      console.log('\n📊 Sample data from product_service:');
      const samplePS = await client.query(`
        SELECT id, name, type, base_cost, status
        FROM product_service
        LIMIT 3
      `);

      samplePS.rows.forEach((row, index) => {
        console.log(`  ${index + 1}. ${row.name} (${row.type}) - Cost: ${row.base_cost || 'N/A'} - Status: ${row.status}`);
      });

      console.log('\n📊 Sample data from product_service_supplier:');
      const samplePSS = await client.query(`
        SELECT product_service_id, supplier_id, cost, currency_code, is_active
        FROM product_service_supplier
        LIMIT 3
      `);

      if (samplePSS.rows.length > 0) {
        samplePSS.rows.forEach((row, index) => {
          console.log(`  ${index + 1}. PS: ${row.product_service_id} - Supplier: ${row.supplier_id} - Cost: ${row.cost} ${row.currency_code} - Active: ${row.is_active}`);
        });
      } else {
        console.log('  No data found in product_service_supplier table');
      }

      // Check relationships
      console.log('\n📊 Checking relationships:');
      const relationshipQuery = await client.query(`
        SELECT
          ps.id as product_service_id,
          ps.name as product_name,
          ps.type,
          ps.base_cost,
          pss.supplier_id,
          pss.cost as supplier_cost,
          pss.currency_code,
          s.name as supplier_name
        FROM product_service ps
        LEFT JOIN product_service_supplier pss ON ps.id = pss.product_service_id
        LEFT JOIN supplier s ON pss.supplier_id = s.id
        WHERE ps.status = 'active' AND ps.deleted_at IS NULL
        LIMIT 5
      `);

      if (relationshipQuery.rows.length > 0) {
        relationshipQuery.rows.forEach((row, index) => {
          console.log(`  ${index + 1}. ${row.product_name}`);
          console.log(`     - Type: ${row.type}`);
          console.log(`     - Base Cost: ${row.base_cost || 'N/A'}`);
          console.log(`     - Supplier: ${row.supplier_name || 'No supplier assigned'}`);
          console.log(`     - Supplier Cost: ${row.supplier_cost || 'N/A'} ${row.currency_code || ''}`);
        });
      } else {
        console.log('  No active product services found');
      }
    });
  } catch (error) {
    console.error('❌ Failed to check table structure:', error);
    throw error;
  }
}

// Export the function for medusa exec
export default checkTableStructure;