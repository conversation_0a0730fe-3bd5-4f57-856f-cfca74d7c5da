#!/usr/bin/env node

import { ExecArgs } from "@camped-ai/framework/types";
import { ContainerRegistrationKeys } from "@camped-ai/framework/utils";

export default async function createTableViaService({
  container,
}: ExecArgs) {
  const logger = container.resolve(ContainerRegistrationKeys.LOGGER);
  
  try {
    logger.info("🚀 Creating supplier_offering table via service...");

    // Get any service that has database access
    const supplierProductsServicesService = container.resolve("supplierProductsServicesModuleService");
    
    // Access the underlying repository or entity manager
    // Let's try to get the entity manager from the service
    const entityManager = (supplierProductsServicesService as any).__container__.resolve("manager");
    
    if (!entityManager) {
      throw new Error("Could not access entity manager");
    }

    logger.info("✅ Entity manager accessed successfully");

    // Create supplier_offering table
    logger.info("Creating supplier_offering table...");
    await entityManager.query(`
      CREATE TABLE IF NOT EXISTS supplier_offering (
        id varchar(255) NOT NULL,
        product_service_id varchar(255) NOT NULL,
        supplier_id varchar(255) NOT NULL,
        active_from timestamptz NULL,
        active_to timestamptz NULL,
        availability_notes text NULL,
        status varchar(255) NOT NULL DEFAULT 'active',
        custom_fields jsonb NULL,
        created_by varchar(255) NULL,
        updated_by varchar(255) NULL,
        created_at timestamptz NOT NULL DEFAULT now(),
        updated_at timestamptz NOT NULL DEFAULT now(),
        deleted_at timestamptz NULL,
        CONSTRAINT supplier_offering_pkey PRIMARY KEY (id),
        CONSTRAINT supplier_offering_status_check CHECK (status IN ('active', 'inactive'))
      );
    `);

    logger.info("✅ supplier_offering table created successfully");

    // Create indexes
    logger.info("Creating indexes...");
    
    await entityManager.query(`
      CREATE INDEX IF NOT EXISTS IDX_supplier_offering_product_service_id 
      ON supplier_offering (product_service_id) 
      WHERE deleted_at IS NULL;
    `);

    await entityManager.query(`
      CREATE INDEX IF NOT EXISTS IDX_supplier_offering_supplier_id 
      ON supplier_offering (supplier_id) 
      WHERE deleted_at IS NULL;
    `);

    await entityManager.query(`
      CREATE UNIQUE INDEX IF NOT EXISTS IDX_supplier_offering_unique 
      ON supplier_offering (product_service_id, supplier_id) 
      WHERE deleted_at IS NULL;
    `);

    await entityManager.query(`
      CREATE INDEX IF NOT EXISTS IDX_supplier_offering_status 
      ON supplier_offering (status) 
      WHERE deleted_at IS NULL;
    `);

    await entityManager.query(`
      CREATE INDEX IF NOT EXISTS IDX_supplier_offering_validity 
      ON supplier_offering (active_from, active_to) 
      WHERE deleted_at IS NULL;
    `);

    await entityManager.query(`
      CREATE INDEX IF NOT EXISTS IDX_supplier_offering_created_by 
      ON supplier_offering (created_by) 
      WHERE deleted_at IS NULL;
    `);

    logger.info("✅ Indexes created successfully");

    // Add foreign key constraint
    logger.info("Adding foreign key constraints...");
    
    await entityManager.query(`
      DO $$
      BEGIN
        IF NOT EXISTS (
          SELECT 1 FROM information_schema.table_constraints 
          WHERE constraint_name = 'supplier_offering_product_service_id_foreign'
        ) THEN
          ALTER TABLE supplier_offering 
          ADD CONSTRAINT supplier_offering_product_service_id_foreign 
          FOREIGN KEY (product_service_id) REFERENCES product_service(id) 
          ON DELETE CASCADE ON UPDATE CASCADE;
        END IF;
      END
      $$;
    `);

    logger.info("✅ Foreign key constraints added successfully");

    // Verify the table was created
    const result = await entityManager.query(`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
      AND table_name = 'supplier_offering';
    `);

    if (result && result.length > 0) {
      logger.info("✅ Table verification successful - supplier_offering table exists");
    } else {
      logger.warn("❌ Table verification failed - supplier_offering table not found");
    }

    logger.info("🎉 supplier_offering table setup completed!");

  } catch (error) {
    logger.error("❌ Error creating supplier_offering table:", error);
    throw error;
  }
}
