#!/usr/bin/env node

/**
 * <PERSON><PERSON><PERSON> to manually create migration tables for add-on supplier integration
 */

import { withClient } from "../utils/db";

async function createMigrationTables() {
  console.log('🚀 Creating Add-on Supplier Integration Tables');
  console.log('=' .repeat(50));

  try {
    await withClient(async (client) => {
      // Create add_on_supplier_config table
      console.log('📊 Creating add_on_supplier_config table...');
      await client.query(`
        CREATE TABLE IF NOT EXISTS "add_on_supplier_config" (
          "id" text NOT NULL,
          "supplier_id" text NOT NULL,
          "default_margin_percentage" decimal(5,2) DEFAULT 20.00,
          "default_currency" text DEFAULT 'CHF',
          "auto_sync_enabled" boolean DEFAULT true,
          "category_mapping" jsonb NULL,
          "field_mapping" jsonb NULL,
          "pricing_rules" jsonb NULL,
          "hotel_mapping" jsonb NULL,
          "created_at" timestamptz DEFAULT now(),
          "updated_at" timestamptz DEFAULT now(),
          "deleted_at" timestamptz NULL,
          PRIMARY KEY ("id"),
          UNIQUE ("supplier_id")
        );
      `);
      console.log('✅ add_on_supplier_config table created');

      // Create indexes for add_on_supplier_config
      console.log('📊 Creating indexes for add_on_supplier_config...');
      await client.query(`
        CREATE INDEX IF NOT EXISTS "IDX_add_on_supplier_config_supplier_id"
        ON "add_on_supplier_config" ("supplier_id")
        WHERE "deleted_at" IS NULL;
      `);

      await client.query(`
        CREATE INDEX IF NOT EXISTS "IDX_add_on_supplier_config_auto_sync"
        ON "add_on_supplier_config" ("auto_sync_enabled")
        WHERE "deleted_at" IS NULL;
      `);
      console.log('✅ Indexes for add_on_supplier_config created');

      // Create add_on_sync_log table
      console.log('📊 Creating add_on_sync_log table...');
      await client.query(`
        CREATE TABLE IF NOT EXISTS "add_on_sync_log" (
          "id" text NOT NULL,
          "supplier_product_service_id" text NOT NULL,
          "add_on_product_id" text NULL,
          "sync_action" text NOT NULL,
          "sync_status" text NOT NULL,
          "error_message" text NULL,
          "sync_data" jsonb NULL,
          "created_at" timestamptz DEFAULT now(),
          PRIMARY KEY ("id")
        );
      `);
      console.log('✅ add_on_sync_log table created');

      // Create indexes for add_on_sync_log
      console.log('📊 Creating indexes for add_on_sync_log...');
      await client.query(`
        CREATE INDEX IF NOT EXISTS "IDX_add_on_sync_log_supplier_ps_id"
        ON "add_on_sync_log" ("supplier_product_service_id");
      `);

      await client.query(`
        CREATE INDEX IF NOT EXISTS "IDX_add_on_sync_log_addon_product_id"
        ON "add_on_sync_log" ("add_on_product_id")
        WHERE "add_on_product_id" IS NOT NULL;
      `);

      await client.query(`
        CREATE INDEX IF NOT EXISTS "IDX_add_on_sync_log_sync_status"
        ON "add_on_sync_log" ("sync_status");
      `);

      await client.query(`
        CREATE INDEX IF NOT EXISTS "IDX_add_on_sync_log_sync_action"
        ON "add_on_sync_log" ("sync_action");
      `);

      await client.query(`
        CREATE INDEX IF NOT EXISTS "IDX_add_on_sync_log_created_at"
        ON "add_on_sync_log" ("created_at");
      `);
      console.log('✅ Indexes for add_on_sync_log created');

      // Create add_on_migration_backup table
      console.log('📊 Creating add_on_migration_backup table...');
      await client.query(`
        CREATE TABLE IF NOT EXISTS "add_on_migration_backup" (
          "id" text NOT NULL,
          "original_product_id" text NOT NULL,
          "original_metadata" jsonb NOT NULL,
          "original_variants" jsonb NOT NULL,
          "backup_timestamp" timestamptz DEFAULT now(),
          PRIMARY KEY ("id")
        );
      `);
      console.log('✅ add_on_migration_backup table created');

      await client.query(`
        CREATE INDEX IF NOT EXISTS "IDX_add_on_migration_backup_product_id"
        ON "add_on_migration_backup" ("original_product_id");
      `);
      console.log('✅ Indexes for add_on_migration_backup created');

      // Insert default supplier configurations for existing suppliers
      console.log('📊 Creating default supplier configurations...');
      await client.query(`
        INSERT INTO "add_on_supplier_config" (
          "id",
          "supplier_id",
          "default_margin_percentage",
          "default_currency",
          "auto_sync_enabled",
          "category_mapping",
          "field_mapping",
          "pricing_rules",
          "hotel_mapping"
        )
        SELECT
          'asc_' || "id",
          "id",
          20.00,
          'CHF',
          true,
          '{}',
          '{}',
          '{
            "adult_margin": 20,
            "child_margin": 15,
            "package_margin": 18,
            "seasonal_adjustments": [],
            "volume_discounts": []
          }',
          '{
            "default_service_level": "hotel",
            "hotel_assignments": {},
            "destination_assignments": {}
          }'
        FROM "supplier"
        WHERE "deleted_at" IS NULL
        ON CONFLICT ("supplier_id") DO NOTHING;
      `);

      // Check how many configurations were created
      const configResult = await client.query(`
        SELECT COUNT(*) as count FROM "add_on_supplier_config"
      `);
      console.log(`✅ Created ${configResult.rows[0].count} supplier configurations`);

      console.log('\n🎉 All migration tables created successfully!');
    });
  } catch (error) {
    console.error('❌ Failed to create migration tables:', error);
    throw error;
  }
}

// Export the function for medusa exec
export default createMigrationTables;