#!/usr/bin/env node

import { withClient } from "../utils/db";

async function checkExistingData() {
  console.log('🔍 Checking existing categories and unit types...');
  
  try {
    await withClient(async (client) => {
      // Check categories
      const cats = await client.query('SELECT id, name FROM product_service_category LIMIT 10');
      console.log('\n📂 Categories:');
      cats.rows.forEach(cat => {
        console.log(`  ${cat.id}: ${cat.name}`);
      });
      
      // Check unit types
      const units = await client.query('SELECT id, name FROM product_service_unit_type LIMIT 10');
      console.log('\n📏 Unit Types:');
      units.rows.forEach(unit => {
        console.log(`  ${unit.id}: ${unit.name}`);
      });
      
      // Check existing product services
      const services = await client.query('SELECT id, name, type, category_id, unit_type_id FROM product_service LIMIT 5');
      console.log('\n🛠️ Existing Product Services:');
      services.rows.forEach(service => {
        console.log(`  ${service.id}: ${service.name} (${service.type}) - Cat: ${service.category_id}, Unit: ${service.unit_type_id}`);
      });
    });
  } catch (error) {
    console.error('❌ Error:', error);
  }
}

export default checkExistingData;
