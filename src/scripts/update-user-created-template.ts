import { ExecArgs } from "@camped-ai/framework/types";
import { NOTIFICATION_TEMPLATE_SERVICE } from "../modules/notification-template/service";
import NotificationTemplateService from "../modules/notification-template/service";
import { userCreatedDirect } from "../modules/my-notification/email/templates/user-created-direct";

/**
 * Update the user.created_direct email template with the new design
 */
export default async function ({ container }: ExecArgs): Promise<void> {
  const notificationTemplateService: NotificationTemplateService =
    container.resolve(NOTIFICATION_TEMPLATE_SERVICE);

  try {
    // Find the existing template
    const existingTemplates =
      await notificationTemplateService.listNotificationTemplates({
        event_name: "user.created_direct",
        channel: "email",
      });

    if (existingTemplates.length === 0) {
      return;
    }

    const existingTemplate = existingTemplates[0];

    // Update the template with new content
    await notificationTemplateService.updateNotificationTemplates([
      {
        id: existingTemplate.id,
        subject: userCreatedDirect.subject,
        content: userCreatedDirect.body,
      },
    ]);
  } catch (error) {
    throw error;
  }
}
