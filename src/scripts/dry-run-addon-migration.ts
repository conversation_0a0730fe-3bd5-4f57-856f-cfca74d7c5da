#!/usr/bin/env node

/**
 * Dry Run Add-on Migration Script
 *
 * This script performs a dry run of the add-on migration to show what would be migrated
 * without making any actual changes to the database.
 */

import { withClient } from "../utils/db";

async function performDryRun() {
  console.log('🔍 Starting Add-on Migration Dry Run');
  console.log('=' .repeat(50));

  try {
    await withClient(async (client) => {
      // 1. Check current add-ons
      console.log('\n📊 Current Add-ons Analysis:');
      const currentAddOnsResult = await client.query(`
        SELECT COUNT(*) as count
        FROM product
        WHERE deleted_at IS NULL
      `);
      const currentAddOns = parseInt(currentAddOnsResult.rows[0]?.count || '0');
      console.log(`  - Total products: ${currentAddOns}`);

      // Check for products with add-on metadata
      const addOnProductsResult = await client.query(`
        SELECT COUNT(*) as count
        FROM product
        WHERE metadata::text LIKE '%add_on_service%'
        AND deleted_at IS NULL
      `);
      const addOnProducts = parseInt(addOnProductsResult.rows[0]?.count || '0');
      console.log(`  - Products with add-on metadata: ${addOnProducts}`);

      // 2. Check supplier products available for migration
      console.log('\n📊 Supplier Products Analysis:');
      const supplierProductsResult = await client.query(`
        SELECT
          ps.id,
          ps.name,
          ps.type,
          ps.base_cost,
          ps.status,
          'Direct Product' as supplier_name
        FROM product_service ps
        WHERE ps.status = 'active'
        AND ps.deleted_at IS NULL
        ORDER BY ps.created_at
      `);

      console.log(`  - Active supplier products/services: ${supplierProductsResult.rows.length}`);

      if (supplierProductsResult.rows.length > 0) {
        console.log('\n📋 Products that would be migrated:');
        supplierProductsResult.rows.forEach((row, index) => {
          const baseCost = parseFloat(row.base_cost) || 0;
          const margin = 20; // Default 20% margin
          const adultPrice = baseCost * (1 + margin / 100);
          const childPrice = adultPrice * 0.75; // 25% discount for children

          console.log(`  ${index + 1}. ${row.name} (${row.type})`);
          console.log(`     - Supplier: ${row.supplier_name || 'Unknown'}`);
          console.log(`     - Base Cost: CHF ${baseCost.toFixed(2)}`);
          console.log(`     - Adult Price (20% margin): CHF ${adultPrice.toFixed(2)}`);
          console.log(`     - Child Price (25% discount): CHF ${childPrice.toFixed(2)}`);
          console.log('');
        });
      }

      // 3. Check supplier configurations
      console.log('\n📊 Supplier Configuration Analysis:');
      const configResult = await client.query(`
        SELECT
          config.supplier_id,
          config.default_margin_percentage,
          config.default_currency,
          config.auto_sync_enabled,
          s.name as supplier_name
        FROM add_on_supplier_config config
        LEFT JOIN supplier s ON config.supplier_id = s.id
        ORDER BY s.name
      `);

      console.log(`  - Configured suppliers: ${configResult.rows.length}`);
      configResult.rows.forEach((config, index) => {
        console.log(`  ${index + 1}. ${config.supplier_name || 'Unknown'}`);
        console.log(`     - Margin: ${config.default_margin_percentage}%`);
        console.log(`     - Currency: ${config.default_currency}`);
        console.log(`     - Auto Sync: ${config.auto_sync_enabled ? 'Enabled' : 'Disabled'}`);
      });

      // 4. Estimate migration impact
      console.log('\n📊 Migration Impact Estimation:');
      const totalProductsToMigrate = supplierProductsResult.rows.length;
      const estimatedVariants = totalProductsToMigrate * 3; // Adult, child, package variants
      const estimatedBackups = addOnProducts;

      console.log(`  - Products to migrate: ${totalProductsToMigrate}`);
      console.log(`  - Estimated variants to create: ${estimatedVariants}`);
      console.log(`  - Existing add-ons to backup: ${estimatedBackups}`);
      console.log(`  - Legacy add-ons to remove: ${addOnProducts}`);

      // 5. Check for potential issues
      console.log('\n⚠️  Potential Issues:');
      const issues = [];

      if (totalProductsToMigrate === 0) {
        issues.push('No supplier products found to migrate');
      }

      // Check for products without base cost
      const noCostResult = await client.query(`
        SELECT COUNT(*) as count
        FROM product_service
        WHERE (base_cost IS NULL OR base_cost = 0)
        AND status = 'active'
        AND deleted_at IS NULL
      `);
      const noCostProducts = parseInt(noCostResult.rows[0]?.count || '0');
      if (noCostProducts > 0) {
        issues.push(`${noCostProducts} products have no base cost defined`);
      }

      // Note: Since we're working with direct products (not supplier relationships),
      // we'll skip the supplier assignment check for now

      if (issues.length === 0) {
        console.log('  ✅ No issues detected');
      } else {
        issues.forEach((issue, index) => {
          console.log(`  ${index + 1}. ${issue}`);
        });
      }

      // 6. Migration recommendations
      console.log('\n💡 Recommendations:');
      if (totalProductsToMigrate === 0) {
        console.log('  1. Add supplier products/services before running migration');
        console.log('  2. Ensure products have base costs defined');
        console.log('  3. Verify supplier assignments are correct');
      } else {
        console.log('  1. Review pricing calculations above');
        console.log('  2. Adjust supplier configurations if needed');
        console.log('  3. Ensure database backup is current');
        console.log('  4. Run migration during low-traffic period');
      }

      console.log('\n🎯 DRY RUN SUMMARY:');
      console.log('=' .repeat(50));
      console.log(`Migration Ready: ${totalProductsToMigrate > 0 ? '✅ YES' : '❌ NO'}`);
      console.log(`Products to Migrate: ${totalProductsToMigrate}`);
      console.log(`Estimated Processing Time: ${Math.ceil(totalProductsToMigrate * 0.5)} seconds`);
      console.log(`Risk Level: ${issues.length === 0 ? '🟢 LOW' : issues.length <= 2 ? '🟡 MEDIUM' : '🔴 HIGH'}`);
      console.log('=' .repeat(50));
    });
  } catch (error) {
    console.error('❌ Dry run failed:', error);
    throw error;
  }
}

// Export the function for medusa exec
export default performDryRun;