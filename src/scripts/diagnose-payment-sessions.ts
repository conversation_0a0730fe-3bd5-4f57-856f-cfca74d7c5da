import {
  ExecArgs,
} from "@camped-ai/framework/types";
import {
  ContainerRegistrationKeys,
  Mo<PERSON>les,
} from "@camped-ai/framework/utils";

export default async function diagnosePaymentSessions({
  container,
}: ExecArgs) {
  const logger = container.resolve("logger");
  const query = container.resolve(ContainerRegistrationKeys.QUERY);

  logger.info("🔍 Starting payment session diagnosis...");

  try {
    const cartId = "cart_01K1ARWZK6HCE4XH4MH8VR682V";
    const problematicPaymentSessionId = "payses_01K1ARY46KWKXY2QQDC8AZFP1W";

    // 1. Check the specific cart
    logger.info(`📋 Checking cart: ${cartId}`);
    
    try {
      const cart = await query.graph({
        entity: "cart",
        fields: [
          "id",
          "email",
          "currency_code",
          "total",
          "subtotal",
          "metadata",
          "sales_channel_id",
          "region_id"
        ],
        filters: {
          id: cartId
        }
      });

      if (cart.data.length > 0) {
        const cartData = cart.data[0];
        logger.info(`✅ Found cart: ${cartData.id}`);
        logger.info(`   Email: ${cartData.email}`);
        logger.info(`   Currency: ${cartData.currency_code}`);
        logger.info(`   Total: ${cartData.total}`);
        logger.info(`   Sales Channel: ${cartData.sales_channel_id}`);
        logger.info(`   Region: ${cartData.region_id}`);
        logger.info(`   Metadata: ${JSON.stringify(cartData.metadata, null, 2)}`);
      } else {
        logger.warn(`❌ Cart ${cartId} not found`);
        return;
      }
    } catch (error) {
      logger.error(`❌ Error checking cart: ${error.message}`);
      return;
    }

    // 2. Check payment collections for this cart
    logger.info(`💳 Checking payment collections for cart: ${cartId}`);
    
    try {
      const paymentCollections = await query.graph({
        entity: "cart_payment_collection",
        fields: [
          "cart_id",
          "payment_collection.id",
          "payment_collection.status",
          "payment_collection.amount",
          "payment_collection.currency_code",
          "payment_collection.metadata"
        ],
        filters: {
          cart_id: cartId
        }
      });

      if (paymentCollections.data.length > 0) {
        logger.info(`✅ Found ${paymentCollections.data.length} payment collection(s):`);
        paymentCollections.data.forEach((pc: any, index: number) => {
          logger.info(`   Collection ${index + 1}:`);
          logger.info(`     ID: ${pc.payment_collection.id}`);
          logger.info(`     Status: ${pc.payment_collection.status}`);
          logger.info(`     Amount: ${pc.payment_collection.amount}`);
          logger.info(`     Currency: ${pc.payment_collection.currency_code}`);
          logger.info(`     Metadata: ${JSON.stringify(pc.payment_collection.metadata, null, 2)}`);
        });

        // Check payment sessions for each collection
        for (const pc of paymentCollections.data) {
          const collectionId = pc.payment_collection.id;
          logger.info(`🔍 Checking payment sessions for collection: ${collectionId}`);
          
          try {
            const paymentSessions = await query.graph({
              entity: "payment_session",
              fields: [
                "id",
                "status",
                "amount",
                "currency_code",
                "provider_id",
                "data",
                "context",
                "payment_collection_id"
              ],
              filters: {
                payment_collection_id: collectionId
              }
            });

            if (paymentSessions.data.length > 0) {
              logger.info(`   ✅ Found ${paymentSessions.data.length} payment session(s):`);
              paymentSessions.data.forEach((ps: any, index: number) => {
                logger.info(`     Session ${index + 1}:`);
                logger.info(`       ID: ${ps.id}`);
                logger.info(`       Status: ${ps.status}`);
                logger.info(`       Amount: ${ps.amount}`);
                logger.info(`       Currency: ${ps.currency_code}`);
                logger.info(`       Provider: ${ps.provider_id}`);
                logger.info(`       Data: ${JSON.stringify(ps.data, null, 2)}`);
                logger.info(`       Context: ${JSON.stringify(ps.context, null, 2)}`);
                
                // Check if this is the problematic session
                if (ps.id === problematicPaymentSessionId) {
                  logger.warn(`⚠️ This is the problematic payment session!`);
                }
              });
            } else {
              logger.warn(`   ❌ No payment sessions found for collection ${collectionId}`);
            }
          } catch (error) {
            logger.error(`   ❌ Error checking payment sessions for collection ${collectionId}: ${error.message}`);
          }
        }
      } else {
        logger.warn(`❌ No payment collections found for cart ${cartId}`);
      }
    } catch (error) {
      logger.error(`❌ Error checking payment collections: ${error.message}`);
    }

    // 3. Check the specific problematic payment session
    logger.info(`🔍 Checking specific problematic payment session: ${problematicPaymentSessionId}`);
    
    try {
      const specificSession = await query.graph({
        entity: "payment_session",
        fields: [
          "id",
          "status",
          "amount",
          "currency_code",
          "provider_id",
          "data",
          "context",
          "payment_collection_id"
        ],
        filters: {
          id: problematicPaymentSessionId
        }
      });

      if (specificSession.data.length > 0) {
        const session = specificSession.data[0];
        logger.info(`✅ Found problematic payment session:`);
        logger.info(`   ID: ${session.id}`);
        logger.info(`   Status: ${session.status}`);
        logger.info(`   Amount: ${session.amount}`);
        logger.info(`   Currency: ${session.currency_code}`);
        logger.info(`   Provider: ${session.provider_id}`);
        logger.info(`   Collection ID: ${session.payment_collection_id}`);
        logger.info(`   Data: ${JSON.stringify(session.data, null, 2)}`);
        logger.info(`   Context: ${JSON.stringify(session.context, null, 2)}`);
      } else {
        logger.warn(`❌ Problematic payment session ${problematicPaymentSessionId} NOT FOUND`);
        logger.info(`   This explains why the cancellation failed!`);
      }
    } catch (error) {
      logger.error(`❌ Error checking specific payment session: ${error.message}`);
    }

    // 4. Check all payment sessions in the system (limited to recent ones)
    logger.info(`📊 Checking recent payment sessions in the system...`);
    
    try {
      const allSessions = await query.graph({
        entity: "payment_session",
        fields: [
          "id",
          "status",
          "amount",
          "currency_code",
          "provider_id",
          "payment_collection_id",
          "created_at"
        ],
        options: {
          limit: 20,
          orderBy: { created_at: "DESC" }
        }
      });

      if (allSessions.data.length > 0) {
        logger.info(`✅ Found ${allSessions.data.length} recent payment sessions:`);
        allSessions.data.forEach((ps: any, index: number) => {
          logger.info(`   ${index + 1}. ${ps.id} - Status: ${ps.status} - Provider: ${ps.provider_id} - Amount: ${ps.amount}`);
        });
      } else {
        logger.warn(`❌ No payment sessions found in the system`);
      }
    } catch (error) {
      logger.error(`❌ Error checking all payment sessions: ${error.message}`);
    }

    // 5. Check payment providers
    logger.info(`🏦 Checking available payment providers...`);
    
    try {
      const paymentModuleService = container.resolve(Modules.PAYMENT);
      const providers = await paymentModuleService.listPaymentProviders();
      
      logger.info(`✅ Found ${providers.length} payment provider(s):`);
      providers.forEach((provider: any, index: number) => {
        logger.info(`   ${index + 1}. ${provider.id} - ${provider.is_enabled ? 'Enabled' : 'Disabled'}`);
      });
    } catch (error) {
      logger.error(`❌ Error checking payment providers: ${error.message}`);
    }

    logger.info("✅ Payment session diagnosis completed!");

  } catch (error) {
    logger.error("❌ Error during payment session diagnosis:", error);
    throw error;
  }
}
