import { Client } from 'pg';

export default async function addConciergeOrderItemFields() {
  console.log("🔧 Adding new fields to concierge_order_item table...");

  const client = new Client({
    connectionString: process.env.DATABASE_URL,
  });

  try {
    await client.connect();
    console.log("Connected to database successfully");

    // Add the new columns
    console.log("📝 Adding category_id, start_date, and end_date columns...");

    // Add columns
    await client.query(`
      ALTER TABLE "concierge_order_item"
      ADD COLUMN IF NOT EXISTS "category_id" text NULL,
      ADD COLUMN IF NOT EXISTS "start_date" timestamptz NULL,
      ADD COLUMN IF NOT EXISTS "end_date" timestamptz NULL;
    `);

    console.log("✅ Columns added successfully");

    // Add foreign key constraint for category_id
    console.log("📝 Adding foreign key constraint for category_id...");
    try {
      await client.query(`
        ALTER TABLE "concierge_order_item"
        ADD CONSTRAINT "FK_concierge_order_item_category_id"
        FOREIGN KEY ("category_id") REFERENCES "product_service_category" ("id")
        ON DELETE SET NULL ON UPDATE CASCADE;
      `);
      console.log("✅ Foreign key constraint added successfully");
    } catch (fkError) {
      console.log("⚠️ Foreign key constraint may already exist or product_service_category table not found:", fkError.message);
    }

    // Add indexes
    console.log("📝 Adding indexes...");

    const indexes = [
      {
        name: "IDX_concierge_order_item_category_id",
        sql: `CREATE INDEX IF NOT EXISTS "IDX_concierge_order_item_category_id"
              ON "concierge_order_item" ("category_id")
              WHERE "deleted_at" IS NULL;`
      },
      {
        name: "IDX_concierge_order_item_start_date",
        sql: `CREATE INDEX IF NOT EXISTS "IDX_concierge_order_item_start_date"
              ON "concierge_order_item" ("start_date")
              WHERE "deleted_at" IS NULL AND "start_date" IS NOT NULL;`
      },
      {
        name: "IDX_concierge_order_item_end_date",
        sql: `CREATE INDEX IF NOT EXISTS "IDX_concierge_order_item_end_date"
              ON "concierge_order_item" ("end_date")
              WHERE "deleted_at" IS NULL AND "end_date" IS NOT NULL;`
      },
      {
        name: "IDX_concierge_order_item_date_range",
        sql: `CREATE INDEX IF NOT EXISTS "IDX_concierge_order_item_date_range"
              ON "concierge_order_item" ("start_date", "end_date")
              WHERE "deleted_at" IS NULL AND "start_date" IS NOT NULL AND "end_date" IS NOT NULL;`
      },
      {
        name: "IDX_concierge_order_item_category_status",
        sql: `CREATE INDEX IF NOT EXISTS "IDX_concierge_order_item_category_status"
              ON "concierge_order_item" ("category_id", "status")
              WHERE "deleted_at" IS NULL;`
      }
    ];

    for (const index of indexes) {
      try {
        await client.query(index.sql);
        console.log(`✅ Index ${index.name} created successfully`);
      } catch (indexError) {
        console.log(`⚠️ Index ${index.name} may already exist:`, indexError.message);
      }
    }

    // Verify the changes
    console.log("🔍 Verifying table structure...");
    const tableInfo = await client.query(`
      SELECT column_name, data_type, is_nullable
      FROM information_schema.columns
      WHERE table_name = 'concierge_order_item'
      AND column_name IN ('category_id', 'start_date', 'end_date')
      ORDER BY column_name;
    `);

    console.log("📋 New columns in concierge_order_item table:");
    tableInfo.rows.forEach((col: any) => {
      console.log(`  - ${col.column_name}: ${col.data_type} (nullable: ${col.is_nullable})`);
    });

    console.log("🎉 Migration completed successfully!");

  } catch (error) {
    console.error("❌ Error during migration:", error);
    throw error;
  } finally {
    await client.end();
  }
}
