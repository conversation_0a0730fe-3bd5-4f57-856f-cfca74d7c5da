export default async function addCostCurrencyMigration(container: any) {
  console.log("Starting migration: Add cost and currency fields to supplier_offering table");

  try {
    // Get the database connection from the container
    const dbConfig = container.cradle?.dbConfig || container.resolve?.("dbConfig");

    if (!dbConfig) {
      console.error("Could not resolve database configuration");
      return;
    }

    // Use a direct database connection approach
    const { Client } = require('pg');
    const client = new Client({
      host: dbConfig.host || 'localhost',
      port: dbConfig.port || 5432,
      database: dbConfig.database || 'medusa-store-v2',
      user: dbConfig.username || 'postgres',
      password: dbConfig.password || '',
    });

    await client.connect();
    console.log("Connected to database");

    // Run the migration queries
    await client.query(`
      -- Add cost column (nullable number for pricing)
      ALTER TABLE supplier_offering
      ADD COLUMN IF NOT EXISTS cost DECIMAL(10,2) NULL;
    `);
    console.log("Added cost column");

    await client.query(`
      -- Add currency column (nullable text for currency code like CHF, EUR, USD)
      ALTER TABLE supplier_offering
      ADD COLUMN IF NOT EXISTS currency VARCHAR(3) NULL;
    `);
    console.log("Added currency column");

    await client.query(`
      -- Add currency_override column (boolean to track if currency differs from supplier default)
      ALTER TABLE supplier_offering
      ADD COLUMN IF NOT EXISTS currency_override BOOLEAN NOT NULL DEFAULT false;
    `);
    console.log("Added currency_override column");

    await client.query(`
      -- Add index for cost-based queries
      CREATE INDEX IF NOT EXISTS IDX_supplier_offering_cost
      ON supplier_offering (cost)
      WHERE deleted_at IS NULL AND cost IS NOT NULL;
    `);
    console.log("Added cost index");

    await client.query(`
      -- Add index for currency-based queries
      CREATE INDEX IF NOT EXISTS IDX_supplier_offering_currency
      ON supplier_offering (currency)
      WHERE deleted_at IS NULL AND currency IS NOT NULL;
    `);
    console.log("Added currency index");

    await client.query(`
      -- Add index for currency override queries
      CREATE INDEX IF NOT EXISTS IDX_supplier_offering_currency_override
      ON supplier_offering (currency_override)
      WHERE deleted_at IS NULL;
    `);
    console.log("Added currency_override index");

    // Add constraints
    try {
      await client.query(`
        -- Add constraint to ensure currency is a valid 3-letter code when provided
        ALTER TABLE supplier_offering
        ADD CONSTRAINT supplier_offering_currency_format_check
        CHECK (currency IS NULL OR (currency ~ '^[A-Z]{3}$'));
      `);
      console.log("Added currency format constraint");
    } catch (error) {
      console.log("Currency format constraint already exists or failed to add");
    }

    try {
      await client.query(`
        -- Add constraint to ensure cost is non-negative when provided
        ALTER TABLE supplier_offering
        ADD CONSTRAINT supplier_offering_cost_positive_check
        CHECK (cost IS NULL OR cost >= 0);
      `);
      console.log("Added cost positive constraint");
    } catch (error) {
      console.log("Cost positive constraint already exists or failed to add");
    }

    // Verify the columns were added
    const result = await client.query(`
      SELECT column_name, data_type, is_nullable, column_default
      FROM information_schema.columns
      WHERE table_name = 'supplier_offering'
      AND column_name IN ('cost', 'currency', 'currency_override')
      ORDER BY column_name;
    `);

    console.log("Migration completed successfully. Added columns:", result.rows);

    await client.end();

  } catch (error) {
    console.error("Migration failed:", error);
    throw error;
  }
}
