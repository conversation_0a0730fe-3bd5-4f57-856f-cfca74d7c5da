import {
  ExecArgs,
} from "@camped-ai/framework/types";
import {
  ContainerRegistrationKeys,
  Modules,
} from "@camped-ai/framework/utils";

export default async function completeCartWithNewSession({
  container,
}: ExecArgs) {
  const logger = container.resolve("logger");
  const query = container.resolve(ContainerRegistrationKeys.QUERY);

  logger.info("🎯 Completing cart with new payment session...");

  try {
    const cartId = "cart_01K1ARWZK6HCE4XH4MH8VR682V";

    // 1. Check the cart
    logger.info(`📋 Checking cart: ${cartId}`);
    
    const cart = await query.graph({
      entity: "cart",
      fields: [
        "id",
        "email",
        "currency_code",
        "total",
        "subtotal",
        "metadata",
        "sales_channel_id",
        "region_id"
      ],
      filters: {
        id: cartId
      }
    });

    if (cart.data.length === 0) {
      logger.warn(`❌ Cart ${cartId} not found`);
      return;
    }

    const cartData = cart.data[0];
    logger.info(`✅ Found cart: ${cartData.id} - Total: ${cartData.total}`);

    // 2. Get payment collections for this cart
    const paymentCollections = await query.graph({
      entity: "cart_payment_collection",
      fields: [
        "cart_id",
        "payment_collection.id",
        "payment_collection.status",
        "payment_collection.amount",
        "payment_collection.currency_code"
      ],
      filters: {
        cart_id: cartId
      }
    });

    let paymentCollectionId;
    
    if (paymentCollections.data.length === 0) {
      logger.info(`📦 No payment collection found, creating one...`);
      
      // Import and create payment collection
      const { createPaymentCollectionForCartWorkflow } = await import("@camped-ai/medusa/core-flows");
      
      const { result: paymentCollection } = await createPaymentCollectionForCartWorkflow(container).run({
        input: {
          cart_id: cartId,
          metadata: {
            cart_id: cartId,
            cart_total: cartData.total,
            created_for_completion: true,
          },
        },
      });

      paymentCollectionId = paymentCollection.id;
      logger.info(`✅ Created payment collection: ${paymentCollectionId}`);
    } else {
      paymentCollectionId = paymentCollections.data[0].payment_collection.id;
      logger.info(`✅ Using existing payment collection: ${paymentCollectionId}`);
    }

    // 3. Create a new payment session
    logger.info(`💳 Creating new payment session...`);
    
    const paymentModuleService = container.resolve(Modules.PAYMENT);
    
    try {
      const paymentSession = await paymentModuleService.createPaymentSession({
        payment_collection_id: paymentCollectionId,
        provider_id: "pp_system_default", // Use system default provider
        currency_code: cartData.currency_code,
        amount: cartData.total,
        context: {
          email: cartData.email,
          cart_id: cartId,
          completion_attempt: true,
          created_at: new Date().toISOString()
        }
      });

      logger.info(`✅ Created payment session: ${paymentSession.id}`);

      // 4. Authorize the payment session
      logger.info(`🔐 Authorizing payment session...`);
      
      await paymentModuleService.authorizePaymentSession(paymentSession.id, {
        context: {
          authorized_by: "system",
          authorized_at: new Date().toISOString(),
          notes: "Authorized for cart completion"
        }
      });

      logger.info(`✅ Payment session authorized: ${paymentSession.id}`);

    } catch (sessionError) {
      logger.error(`❌ Failed to create/authorize payment session: ${sessionError.message}`);
      
      // Try using createPaymentSessionsWorkflow instead
      logger.info(`🔄 Trying with createPaymentSessionsWorkflow...`);
      
      try {
        const { createPaymentSessionsWorkflow } = await import("@camped-ai/medusa/core-flows");
        
        const { result: paymentSessions } = await createPaymentSessionsWorkflow(container).run({
          input: {
            payment_collection_id: paymentCollectionId,
            provider_id: "pp_system_default",
            context: {
              extra: {
                email: cartData.email,
                cart_id: cartId,
                completion_attempt: true,
                created_via: "workflow"
              }
            }
          }
        });

        if (paymentSessions && paymentSessions.length > 0) {
          const session = paymentSessions[0];
          logger.info(`✅ Created payment session via workflow: ${session.id}`);
          
          // Authorize it
          await paymentModuleService.authorizePaymentSession(session.id, {
            context: {
              authorized_by: "system",
              authorized_at: new Date().toISOString(),
              notes: "Authorized for cart completion via workflow"
            }
          });

          logger.info(`✅ Payment session authorized via workflow: ${session.id}`);
        } else {
          throw new Error("No payment sessions returned from workflow");
        }
      } catch (workflowError) {
        logger.error(`❌ Workflow approach also failed: ${workflowError.message}`);
        throw workflowError;
      }
    }

    // 5. Now try to complete the cart
    logger.info(`🎯 Attempting to complete cart...`);
    
    try {
      const { completeCartWorkflow } = await import("@camped-ai/medusa/core-flows");
      
      const { result: order } = await completeCartWorkflow(container).run({
        input: { id: cartId }
      });

      logger.info(`🎉 SUCCESS! Cart completed successfully!`);
      logger.info(`   Order ID: ${order.id}`);
      logger.info(`   Order Total: ${order.total}`);
      logger.info(`   Order Status: ${order.status}`);
      
      return {
        success: true,
        order_id: order.id,
        cart_id: cartId,
        message: "Cart completed successfully"
      };
      
    } catch (completionError) {
      logger.error(`❌ Cart completion failed: ${completionError.message}`);
      logger.error(`   Stack: ${completionError.stack}`);
      
      // Let's check what payment sessions exist now
      logger.info(`🔍 Checking current payment sessions...`);
      
      try {
        const sessions = await paymentModuleService.listPaymentSessions({
          payment_collection_id: paymentCollectionId
        });
        
        logger.info(`   Found ${sessions.length} payment session(s):`);
        sessions.forEach((session: any) => {
          logger.info(`     - ${session.id} (Status: ${session.status}, Amount: ${session.amount})`);
        });
      } catch (listError) {
        logger.error(`   Could not list payment sessions: ${listError.message}`);
      }
      
      throw completionError;
    }

  } catch (error) {
    logger.error("❌ Error during cart completion with new session:", error);
    throw error;
  }
}
