import { Migration } from "@camped-ai/framework/utils";

/**
 * Migration to convert DECIMAL currency columns to INTEGER for Medusa compliance
 * 
 * This migration converts existing DECIMAL(10,2) currency columns to INTEGER
 * to store amounts in smallest currency units (e.g., cents for USD).
 * 
 * Affected tables:
 * - supplier_offering.cost
 * - product_service.base_cost
 * - Any other DECIMAL currency columns
 */
export class Migration20250102000000 extends Migration {
  async up(): Promise<void> {
    // First, convert existing data by multiplying by 100 to convert to cents
    // Handle supplier_offering.cost
    this.addSql(`
      UPDATE "supplier_offering" 
      SET "cost" = ROUND("cost" * 100)
      WHERE "cost" IS NOT NULL;
    `);

    // Handle product_service.base_cost if it exists
    this.addSql(`
      UPDATE "product_service" 
      SET "base_cost" = ROUND("base_cost" * 100)
      WHERE "base_cost" IS NOT NULL;
    `);

    // Now alter the column types to INTEGER
    // supplier_offering.cost
    this.addSql(`
      ALTER TABLE "supplier_offering" 
      ALTER COLUMN "cost" TYPE INTEGER USING ROUND("cost");
    `);

    // product_service.base_cost
    this.addSql(`
      ALTER TABLE "product_service" 
      ALTER COLUMN "base_cost" TYPE INTEGER USING ROUND("base_cost");
    `);

    // Add comments to clarify that these columns store amounts in smallest currency units
    this.addSql(`
      COMMENT ON COLUMN "supplier_offering"."cost" IS 'Cost in smallest currency unit (e.g., cents for USD, pence for GBP)';
    `);

    this.addSql(`
      COMMENT ON COLUMN "product_service"."base_cost" IS 'Base cost in smallest currency unit (e.g., cents for USD, pence for GBP)';
    `);
  }

  async down(): Promise<void> {
    // Revert to DECIMAL and convert back to standard units
    // supplier_offering.cost
    this.addSql(`
      ALTER TABLE "supplier_offering" 
      ALTER COLUMN "cost" TYPE DECIMAL(10,2) USING ("cost"::DECIMAL / 100);
    `);

    // product_service.base_cost
    this.addSql(`
      ALTER TABLE "product_service" 
      ALTER COLUMN "base_cost" TYPE DECIMAL(10,2) USING ("base_cost"::DECIMAL / 100);
    `);

    // Remove comments
    this.addSql(`
      COMMENT ON COLUMN "supplier_offering"."cost" IS NULL;
    `);

    this.addSql(`
      COMMENT ON COLUMN "product_service"."base_cost" IS NULL;
    `);
  }
}
