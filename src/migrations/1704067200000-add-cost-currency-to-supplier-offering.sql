-- Migration: Add cost and currency fields to supplier_offering table
-- Date: 2024-01-01
-- Description: Add cost, currency, and currency_override fields to support pricing in supplier offerings

-- Add cost column (nullable integer for pricing in smallest currency unit - e.g., cents)
ALTER TABLE supplier_offering
ADD COLUMN IF NOT EXISTS cost INTEGER NULL;

-- Add currency column (nullable text for currency code like CHF, EUR, USD)
ALTER TABLE supplier_offering 
ADD COLUMN IF NOT EXISTS currency VARCHAR(3) NULL;

-- Add currency_override column (boolean to track if currency differs from supplier default)
ALTER TABLE supplier_offering 
ADD COLUMN IF NOT EXISTS currency_override BOOLEAN NOT NULL DEFAULT false;

-- Add index for cost-based queries
CREATE INDEX IF NOT EXISTS IDX_supplier_offering_cost 
ON supplier_offering (cost) 
WHERE deleted_at IS NULL AND cost IS NOT NULL;

-- Add index for currency-based queries
CREATE INDEX IF NOT EXISTS IDX_supplier_offering_currency 
ON supplier_offering (currency) 
WHERE deleted_at IS NULL AND currency IS NOT NULL;

-- Add index for currency override queries
CREATE INDEX IF NOT EXISTS IDX_supplier_offering_currency_override 
ON supplier_offering (currency_override) 
WHERE deleted_at IS NULL;

-- Add constraint to ensure currency is a valid 3-letter code when provided
ALTER TABLE supplier_offering 
ADD CONSTRAINT IF NOT EXISTS supplier_offering_currency_format_check 
CHECK (currency IS NULL OR (currency ~ '^[A-Z]{3}$'));

-- Add constraint to ensure cost is non-negative when provided
ALTER TABLE supplier_offering 
ADD CONSTRAINT IF NOT EXISTS supplier_offering_cost_positive_check 
CHECK (cost IS NULL OR cost >= 0);

-- Verify the columns were added
SELECT column_name, data_type, is_nullable, column_default 
FROM information_schema.columns 
WHERE table_name = 'supplier_offering' 
AND column_name IN ('cost', 'currency', 'currency_override')
ORDER BY column_name;
