import { Migration } from "@mikro-orm/migrations";

export class Migration1753133000000UpdateConciergeOrderItemStatusEnum extends Migration {
  async up(): Promise<void> {
    // Update the status enum values for concierge_order_item table
    // First, update any existing 'rejected' and 'finalized' statuses to the new values
    this.addSql(`
      UPDATE "concierge_order_item" 
      SET "status" = 'cancelled' 
      WHERE "status" = 'rejected';
    `);

    this.addSql(`
      UPDATE "concierge_order_item" 
      SET "status" = 'completed' 
      WHERE "status" = 'finalized';
    `);

    // Note: PostgreSQL doesn't support direct enum modification with existing data
    // We need to create a new enum type and update the column
    
    // Create new enum type with updated values
    this.addSql(`
      CREATE TYPE concierge_order_item_status_new AS ENUM (
        'under_review',
        'client_confirmed',
        'order_placed',
        'cancelled',
        'completed'
      );
    `);

    // Update the column to use the new enum type
    this.addSql(`
      ALTER TABLE "concierge_order_item" 
      ALTER COLUMN "status" TYPE concierge_order_item_status_new 
      USING "status"::text::concierge_order_item_status_new;
    `);

    // Drop the old enum type if it exists
    this.addSql(`
      DROP TYPE IF EXISTS concierge_order_item_status;
    `);

    // Rename the new enum type to the original name
    this.addSql(`
      ALTER TYPE concierge_order_item_status_new RENAME TO concierge_order_item_status;
    `);

    // Update the default value to ensure it's still 'under_review'
    this.addSql(`
      ALTER TABLE "concierge_order_item" 
      ALTER COLUMN "status" SET DEFAULT 'under_review';
    `);
  }

  async down(): Promise<void> {
    // Revert back to the old enum values
    // First, update the new status values back to old ones
    this.addSql(`
      UPDATE "concierge_order_item" 
      SET "status" = 'rejected' 
      WHERE "status" = 'cancelled';
    `);

    this.addSql(`
      UPDATE "concierge_order_item" 
      SET "status" = 'finalized' 
      WHERE "status" = 'completed';
    `);

    // Update any 'order_placed' status to 'client_confirmed' as fallback
    this.addSql(`
      UPDATE "concierge_order_item" 
      SET "status" = 'client_confirmed' 
      WHERE "status" = 'order_placed';
    `);

    // Create the old enum type
    this.addSql(`
      CREATE TYPE concierge_order_item_status_old AS ENUM (
        'under_review',
        'client_confirmed',
        'rejected',
        'finalized'
      );
    `);

    // Update the column to use the old enum type
    this.addSql(`
      ALTER TABLE "concierge_order_item" 
      ALTER COLUMN "status" TYPE concierge_order_item_status_old 
      USING "status"::text::concierge_order_item_status_old;
    `);

    // Drop the new enum type
    this.addSql(`
      DROP TYPE IF EXISTS concierge_order_item_status;
    `);

    // Rename the old enum type back to the original name
    this.addSql(`
      ALTER TYPE concierge_order_item_status_old RENAME TO concierge_order_item_status;
    `);

    // Restore the default value
    this.addSql(`
      ALTER TABLE "concierge_order_item" 
      ALTER COLUMN "status" SET DEFAULT 'under_review';
    `);
  }
}
