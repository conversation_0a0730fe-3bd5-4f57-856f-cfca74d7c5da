import { Migration } from "@mikro-orm/migrations";

export class Migration1753133000000AddBookingFieldsToConciergeOrder extends Migration {
  async up(): Promise<void> {
    // Add hotel_id, check_in_date, and check_out_date fields to concierge_order table
    this.addSql(`
      ALTER TABLE "concierge_order" 
      ADD COLUMN IF NOT EXISTS "hotel_id" text NULL,
      ADD COLUMN IF NOT EXISTS "check_in_date" timestamptz NULL,
      ADD COLUMN IF NOT EXISTS "check_out_date" timestamptz NULL;
    `);

    // Create indexes for the new fields
    this.addSql(`
      CREATE INDEX IF NOT EXISTS "IDX_concierge_order_hotel_id" 
      ON "concierge_order" ("hotel_id") 
      WHERE "deleted_at" IS NULL;
    `);

    this.addSql(`
      CREATE INDEX IF NOT EXISTS "IDX_concierge_order_check_in_date" 
      ON "concierge_order" ("check_in_date") 
      WHERE "deleted_at" IS NULL;
    `);

    this.addSql(`
      CREATE INDEX IF NOT EXISTS "IDX_concierge_order_check_out_date" 
      ON "concierge_order" ("check_out_date") 
      WHERE "deleted_at" IS NULL;
    `);
  }

  async down(): Promise<void> {
    // Drop indexes first
    this.addSql(`
      DROP INDEX IF EXISTS "IDX_concierge_order_hotel_id";
    `);

    this.addSql(`
      DROP INDEX IF EXISTS "IDX_concierge_order_check_in_date";
    `);

    this.addSql(`
      DROP INDEX IF EXISTS "IDX_concierge_order_check_out_date";
    `);

    // Drop the columns
    this.addSql(`
      ALTER TABLE "concierge_order" 
      DROP COLUMN IF EXISTS "hotel_id",
      DROP COLUMN IF EXISTS "check_in_date",
      DROP COLUMN IF EXISTS "check_out_date";
    `);
  }
}
