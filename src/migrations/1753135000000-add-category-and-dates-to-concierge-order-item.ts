import { Migration } from "@mikro-orm/migrations";

export class Migration1753135000000AddCategoryAndDatesToConciergeOrderItem extends Migration {
  async up(): Promise<void> {
    // Add category_id, start_date, and end_date fields to concierge_order_item table
    this.addSql(`
      ALTER TABLE "concierge_order_item" 
      ADD COLUMN IF NOT EXISTS "category_id" text NULL,
      ADD COLUMN IF NOT EXISTS "start_date" timestamptz NULL,
      ADD COLUMN IF NOT EXISTS "end_date" timestamptz NULL;
    `);

    // Add foreign key constraint for category_id referencing product_service_category table
    this.addSql(`
      ALTER TABLE "concierge_order_item" 
      ADD CONSTRAINT "FK_concierge_order_item_category_id" 
      FOREIGN KEY ("category_id") REFERENCES "product_service_category" ("id") 
      ON DELETE SET NULL ON UPDATE CASCADE;
    `);

    // Create indexes for the new fields for better query performance
    this.addSql(`
      CREATE INDEX IF NOT EXISTS "IDX_concierge_order_item_category_id" 
      ON "concierge_order_item" ("category_id") 
      WHERE "deleted_at" IS NULL;
    `);

    this.addSql(`
      CREATE INDEX IF NOT EXISTS "IDX_concierge_order_item_start_date" 
      ON "concierge_order_item" ("start_date") 
      WHERE "deleted_at" IS NULL AND "start_date" IS NOT NULL;
    `);

    this.addSql(`
      CREATE INDEX IF NOT EXISTS "IDX_concierge_order_item_end_date" 
      ON "concierge_order_item" ("end_date") 
      WHERE "deleted_at" IS NULL AND "end_date" IS NOT NULL;
    `);

    // Create composite index for date range queries
    this.addSql(`
      CREATE INDEX IF NOT EXISTS "IDX_concierge_order_item_date_range" 
      ON "concierge_order_item" ("start_date", "end_date") 
      WHERE "deleted_at" IS NULL AND "start_date" IS NOT NULL AND "end_date" IS NOT NULL;
    `);

    // Create composite index for category and status filtering
    this.addSql(`
      CREATE INDEX IF NOT EXISTS "IDX_concierge_order_item_category_status" 
      ON "concierge_order_item" ("category_id", "status") 
      WHERE "deleted_at" IS NULL;
    `);
  }

  async down(): Promise<void> {
    // Drop indexes first
    this.addSql(`
      DROP INDEX IF EXISTS "IDX_concierge_order_item_category_status";
    `);

    this.addSql(`
      DROP INDEX IF EXISTS "IDX_concierge_order_item_date_range";
    `);

    this.addSql(`
      DROP INDEX IF EXISTS "IDX_concierge_order_item_end_date";
    `);

    this.addSql(`
      DROP INDEX IF EXISTS "IDX_concierge_order_item_start_date";
    `);

    this.addSql(`
      DROP INDEX IF EXISTS "IDX_concierge_order_item_category_id";
    `);

    // Drop foreign key constraint
    this.addSql(`
      ALTER TABLE "concierge_order_item" 
      DROP CONSTRAINT IF EXISTS "FK_concierge_order_item_category_id";
    `);

    // Drop the columns
    this.addSql(`
      ALTER TABLE "concierge_order_item" 
      DROP COLUMN IF EXISTS "category_id",
      DROP COLUMN IF EXISTS "start_date",
      DROP COLUMN IF EXISTS "end_date";
    `);
  }
}
