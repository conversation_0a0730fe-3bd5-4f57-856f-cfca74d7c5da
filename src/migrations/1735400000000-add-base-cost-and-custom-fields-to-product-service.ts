import { Migration } from "@camped-ai/framework/utils";

/**
 * Migration to add base_cost and custom_fields columns to product_service table
 * 
 * This migration adds:
 * - base_cost: DECIMAL field for storing base cost in CHF
 * - custom_fields: JSON field for storing dynamic field values
 * - Index on base_cost for performance
 */
export class Migration20250703400000 extends Migration {
  async up(): Promise<void> {
    // Add base_cost column (integer for smallest currency unit - e.g., cents)
    this.addSql(`
      ALTER TABLE "product_service"
      ADD COLUMN "base_cost" INTEGER NULL;
    `);

    // Add custom_fields column (JSON storage for dynamic field values)
    this.addSql(`
      ALTER TABLE "product_service" 
      ADD COLUMN "custom_fields" JSONB NULL;
    `);

    // Add index on base_cost for performance
    this.addSql(`
      CREATE INDEX IF NOT EXISTS "IDX_product_service_base_cost" 
      ON "product_service" ("base_cost") 
      WHERE "deleted_at" IS NULL AND "base_cost" IS NOT NULL;
    `);

    // Add index on custom_fields for JSON queries
    this.addSql(`
      CREATE INDEX IF NOT EXISTS "IDX_product_service_custom_fields" 
      ON "product_service" USING GIN ("custom_fields") 
      WHERE "deleted_at" IS NULL AND "custom_fields" IS NOT NULL;
    `);
  }

  async down(): Promise<void> {
    // Drop indexes first
    this.addSql(`DROP INDEX IF EXISTS "IDX_product_service_custom_fields";`);
    this.addSql(`DROP INDEX IF EXISTS "IDX_product_service_base_cost";`);
    
    // Drop columns
    this.addSql(`ALTER TABLE "product_service" DROP COLUMN IF EXISTS "custom_fields";`);
    this.addSql(`ALTER TABLE "product_service" DROP COLUMN IF EXISTS "base_cost";`);
  }
}
