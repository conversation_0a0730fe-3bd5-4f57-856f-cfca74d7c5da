import { Migration } from "@mikro-orm/migrations";

export class Migration1753132000000CreateConciergeOrderTables extends Migration {
  async up(): Promise<void> {
    // Create concierge_order table
    this.addSql(`
      create table if not exists "concierge_order" (
        "id" text not null,
        "order_id" text not null,
        "assigned_to" text null,
        "notes" text null,
        "status" text not null default 'not_started',
        "last_contacted_at" timestamptz null,
        "metadata" jsonb null,
        "created_at" timestamptz not null default now(),
        "updated_at" timestamptz not null default now(),
        "deleted_at" timestamptz null,
        constraint "concierge_order_pkey" primary key ("id")
      );
    `);

    // Create concierge_order_item table
    this.addSql(`
      create table if not exists "concierge_order_item" (
        "id" text not null,
        "concierge_order_id" text not null,
        "line_item_id" text null,
        "variant_id" text null,
        "quantity" integer not null,
        "unit_price" integer not null,
        "title" text not null,
        "status" text not null default 'under_review',
        "is_active" boolean not null default true,
        "added_by" text null,
        "finalized_by" text null,
        "added_at" timestamptz not null default now(),
        "finalized_at" timestamptz null,
        "metadata" jsonb null,
        "created_at" timestamptz not null default now(),
        "updated_at" timestamptz not null default now(),
        "deleted_at" timestamptz null,
        constraint "concierge_order_item_pkey" primary key ("id")
      );
    `);

    // Create indexes for concierge_order
    this.addSql(`
      CREATE UNIQUE INDEX IF NOT EXISTS "IDX_concierge_order_order_id" ON "concierge_order" ("order_id") WHERE "deleted_at" IS NULL;
      CREATE INDEX IF NOT EXISTS "IDX_concierge_order_assigned_to" ON "concierge_order" ("assigned_to") WHERE "deleted_at" IS NULL;
      CREATE INDEX IF NOT EXISTS "IDX_concierge_order_status" ON "concierge_order" ("status") WHERE "deleted_at" IS NULL;
      CREATE INDEX IF NOT EXISTS "IDX_concierge_order_created_at" ON "concierge_order" ("created_at");
      CREATE INDEX IF NOT EXISTS "IDX_concierge_order_deleted_at" ON "concierge_order" ("deleted_at");
    `);

    // Create indexes for concierge_order_item
    this.addSql(`
      CREATE INDEX IF NOT EXISTS "IDX_concierge_order_item_concierge_order_id" ON "concierge_order_item" ("concierge_order_id") WHERE "deleted_at" IS NULL;
      CREATE INDEX IF NOT EXISTS "IDX_concierge_order_item_line_item_id" ON "concierge_order_item" ("line_item_id") WHERE "deleted_at" IS NULL;
      CREATE INDEX IF NOT EXISTS "IDX_concierge_order_item_status" ON "concierge_order_item" ("status") WHERE "deleted_at" IS NULL;
      CREATE INDEX IF NOT EXISTS "IDX_concierge_order_item_is_active" ON "concierge_order_item" ("is_active") WHERE "deleted_at" IS NULL;
      CREATE INDEX IF NOT EXISTS "IDX_concierge_order_item_created_at" ON "concierge_order_item" ("created_at");
      CREATE INDEX IF NOT EXISTS "IDX_concierge_order_item_deleted_at" ON "concierge_order_item" ("deleted_at");
    `);

    // Add foreign key constraint
    this.addSql(`
      ALTER TABLE "concierge_order_item" 
      ADD CONSTRAINT "FK_concierge_order_item_concierge_order_id" 
      FOREIGN KEY ("concierge_order_id") REFERENCES "concierge_order" ("id") ON DELETE CASCADE;
    `);
  }

  async down(): Promise<void> {
    // Drop foreign key constraint first
    this.addSql(`
      ALTER TABLE "concierge_order_item" 
      DROP CONSTRAINT IF EXISTS "FK_concierge_order_item_concierge_order_id";
    `);

    // Drop tables
    this.addSql('drop table if exists "concierge_order_item" cascade;');
    this.addSql('drop table if exists "concierge_order" cascade;');
  }
}
