import { Migration } from "@mikro-orm/migrations";

export class Migration20250131140000 extends Migration {
  async up(): Promise<void> {
    // Add item_id column to concierge_order_item table
    this.addSql(`
      ALTER TABLE "concierge_order_item"
      ADD COLUMN IF NOT EXISTS "item_id" text NULL;
    `);

    // Add index for item_id
    this.addSql(`
      CREATE INDEX IF NOT EXISTS "IDX_concierge_order_item_item_id"
      ON "concierge_order_item" ("item_id")
      WHERE "deleted_at" IS NULL;
    `);

    console.log("✅ Added item_id column and index to concierge_order_item table");
  }

  async down(): Promise<void> {
    // Remove index
    this.addSql(`
      DROP INDEX IF EXISTS "IDX_concierge_order_item_item_id";
    `);

    // Remove column
    this.addSql(`
      ALTER TABLE "concierge_order_item"
      DROP COLUMN IF EXISTS "item_id";
    `);

    console.log("✅ Removed item_id column and index from concierge_order_item table");
  }
}
