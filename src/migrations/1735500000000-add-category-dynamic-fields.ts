import { Migration } from "@camped-ai/framework/utils";

export const migration: Migration = {
  name: "1735500000000-add-category-dynamic-fields",
  async up({ sql }) {
    // Add new columns to product_service_category table
    await sql`
      ALTER TABLE "product_service_category" 
      ADD COLUMN IF NOT EXISTS "category_type" text DEFAULT 'Both' CHECK (category_type IN ('Product', 'Service', 'Both'))
    `;

    await sql`
      ALTER TABLE "product_service_category" 
      ADD COLUMN IF NOT EXISTS "icon" text NULL
    `;

    await sql`
      ALTER TABLE "product_service_category" 
      ADD COLUMN IF NOT EXISTS "dynamic_field_schema" jsonb NULL
    `;

    // Create index for category_type for better query performance
    await sql`
      CREATE INDEX IF NOT EXISTS "IDX_product_service_category_type" 
      ON "product_service_category" ("category_type") 
      WHERE "deleted_at" IS NULL
    `;

    // Create GIN index for dynamic_field_schema JSON queries
    await sql`
      CREATE INDEX IF NOT EXISTS "IDX_product_service_category_dynamic_schema" 
      ON "product_service_category" USING GIN ("dynamic_field_schema") 
      WHERE "deleted_at" IS NULL AND "dynamic_field_schema" IS NOT NULL
    `;

    // Update existing records to have default category_type if NULL
    await sql`
      UPDATE "product_service_category" 
      SET "category_type" = 'Both' 
      WHERE "category_type" IS NULL
    `;

    console.log("✅ Successfully added category dynamic fields columns and indexes");
  },

  async down({ sql }) {
    // Drop indexes first
    await sql`DROP INDEX IF EXISTS "IDX_product_service_category_dynamic_schema"`;
    await sql`DROP INDEX IF EXISTS "IDX_product_service_category_type"`;

    // Drop columns
    await sql`ALTER TABLE "product_service_category" DROP COLUMN IF EXISTS "dynamic_field_schema"`;
    await sql`ALTER TABLE "product_service_category" DROP COLUMN IF EXISTS "icon"`;
    await sql`ALTER TABLE "product_service_category" DROP COLUMN IF EXISTS "category_type"`;

    console.log("✅ Successfully removed category dynamic fields columns and indexes");
  },
};

export default migration;
