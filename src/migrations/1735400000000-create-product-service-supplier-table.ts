import { Migration } from "@camped-ai/framework/utils";

/**
 * Migration to create product_service_supplier table
 * 
 * This table creates the bridge between products/services and suppliers,
 * allowing multiple suppliers to be linked to each product/service with
 * pricing, availability, and other relationship-specific data.
 */
export class Migration20250702400000 extends Migration {
  async up(): Promise<void> {
    // Create product_service_supplier table
    this.addSql(`
      CREATE TABLE IF NOT EXISTS "product_service_supplier" (
        "id" text NOT NULL,
        "product_service_id" text NOT NULL,
        "supplier_id" text NOT NULL,
        "cost" numeric NOT NULL,
        "currency_code" text NOT NULL DEFAULT 'CHF',
        "availability" text NULL,
        "max_capacity" numeric NULL,
        "season" text NULL,
        "valid_from" timestamptz NULL,
        "valid_until" timestamptz NULL,
        "notes" text NULL,
        "lead_time_days" numeric NULL,
        "minimum_order" numeric NULL,
        "is_active" boolean NOT NULL DEFAULT true,
        "is_preferred" boolean NOT NULL DEFAULT false,
        "created_by" text NULL,
        "updated_by" text NULL,
        "created_at" timestamptz NOT NULL DEFAULT now(),
        "updated_at" timestamptz NOT NULL DEFAULT now(),
        "deleted_at" timestamptz NULL,
        CONSTRAINT "product_service_supplier_pkey" PRIMARY KEY ("id")
      );
    `);

    // Create indexes for better performance
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_product_service_supplier_ps_id" ON "product_service_supplier" ("product_service_id") WHERE "deleted_at" IS NULL;`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_product_service_supplier_supplier_id" ON "product_service_supplier" ("supplier_id") WHERE "deleted_at" IS NULL;`);
    this.addSql(`CREATE UNIQUE INDEX IF NOT EXISTS "IDX_product_service_supplier_unique" ON "product_service_supplier" ("product_service_id", "supplier_id") WHERE "deleted_at" IS NULL;`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_product_service_supplier_is_active" ON "product_service_supplier" ("is_active") WHERE "deleted_at" IS NULL;`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_product_service_supplier_is_preferred" ON "product_service_supplier" ("is_preferred") WHERE "deleted_at" IS NULL;`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_product_service_supplier_season" ON "product_service_supplier" ("valid_from", "valid_until") WHERE "deleted_at" IS NULL;`);

    // Add foreign key constraints
    this.addSql(`ALTER TABLE "product_service_supplier" ADD CONSTRAINT "product_service_supplier_product_service_id_foreign" FOREIGN KEY ("product_service_id") REFERENCES "product_service" ("id") ON DELETE CASCADE;`);
    
    // Note: We don't add a foreign key constraint to supplier table since it's in a different module
    // The supplier_id will be validated at the application level
  }

  async down(): Promise<void> {
    // Drop the table
    this.addSql(`DROP TABLE IF EXISTS "product_service_supplier" CASCADE;`);
  }
}
