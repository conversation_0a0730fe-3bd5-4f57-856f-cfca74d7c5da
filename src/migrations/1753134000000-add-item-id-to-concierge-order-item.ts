import { Migration } from "@mikro-orm/migrations";

export class Migration1753134000000AddItemIdToConciergeOrderItem extends Migration {
  async up(): Promise<void> {
    // Add item_id field to concierge_order_item table
    this.addSql(`
      ALTER TABLE "concierge_order_item" 
      ADD COLUMN IF NOT EXISTS "item_id" text NULL;
    `);

    // Create index for the new item_id field
    this.addSql(`
      CREATE INDEX IF NOT EXISTS "IDX_concierge_order_item_item_id" 
      ON "concierge_order_item" ("item_id") 
      WHERE "deleted_at" IS NULL;
    `);
  }

  async down(): Promise<void> {
    // Drop the index first
    this.addSql(`
      DROP INDEX IF EXISTS "IDX_concierge_order_item_item_id";
    `);

    // Drop the item_id column
    this.addSql(`
      ALTER TABLE "concierge_order_item" 
      DROP COLUMN IF EXISTS "item_id";
    `);
  }
}
