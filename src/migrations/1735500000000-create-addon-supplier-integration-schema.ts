import { Migration } from "@camped-ai/framework/utils";

/**
 * Migration to create add-on supplier integration schema
 *
 * This migration creates the necessary tables and schema changes for
 * integrating the supplier management system with the add-ons system:
 *
 * 1. add_on_supplier_config - Configuration for supplier-to-addon mapping
 * 2. add_on_sync_log - Tracking synchronization operations
 * 3. Enhanced metadata structure for existing add-ons
 */
export class Migration20250706500000 extends Migration {
  async up(): Promise<void> {
    // Create add_on_supplier_config table
    this.addSql(`
      CREATE TABLE IF NOT EXISTS "add_on_supplier_config" (
        "id" text NOT NULL,
        "supplier_id" text NOT NULL,
        "default_margin_percentage" decimal(5,2) DEFAULT 20.00,
        "default_currency" text DEFAULT 'CHF',
        "auto_sync_enabled" boolean DEFAULT true,
        "category_mapping" jsonb NULL,
        "field_mapping" jsonb NULL,
        "pricing_rules" jsonb NULL,
        "hotel_mapping" jsonb NULL,
        "created_at" timestamptz DEFAULT now(),
        "updated_at" timestamptz DEFAULT now(),
        "deleted_at" timestamptz NULL,
        PRIMARY KEY ("id"),
        UNIQUE ("supplier_id")
      );
    `);

    // Create indexes for add_on_supplier_config
    this.addSql(`
      CREATE INDEX "IDX_add_on_supplier_config_supplier_id"
      ON "add_on_supplier_config" ("supplier_id")
      WHERE "deleted_at" IS NULL;
    `);

    this.addSql(`
      CREATE INDEX "IDX_add_on_supplier_config_auto_sync"
      ON "add_on_supplier_config" ("auto_sync_enabled")
      WHERE "deleted_at" IS NULL;
    `);

    // Create add_on_sync_log table
    this.addSql(`
      CREATE TABLE IF NOT EXISTS "add_on_sync_log" (
        "id" text NOT NULL,
        "supplier_product_service_id" text NOT NULL,
        "add_on_product_id" text NULL,
        "sync_action" text NOT NULL,
        "sync_status" text NOT NULL,
        "error_message" text NULL,
        "sync_data" jsonb NULL,
        "created_at" timestamptz DEFAULT now(),
        PRIMARY KEY ("id")
      );
    `);

    // Create indexes for add_on_sync_log
    this.addSql(`
      CREATE INDEX "IDX_add_on_sync_log_supplier_ps_id"
      ON "add_on_sync_log" ("supplier_product_service_id");
    `);

    this.addSql(`
      CREATE INDEX "IDX_add_on_sync_log_addon_product_id"
      ON "add_on_sync_log" ("add_on_product_id")
      WHERE "add_on_product_id" IS NOT NULL;
    `);

    this.addSql(`
      CREATE INDEX "IDX_add_on_sync_log_sync_status"
      ON "add_on_sync_log" ("sync_status");
    `);

    this.addSql(`
      CREATE INDEX "IDX_add_on_sync_log_sync_action"
      ON "add_on_sync_log" ("sync_action");
    `);

    this.addSql(`
      CREATE INDEX "IDX_add_on_sync_log_created_at"
      ON "add_on_sync_log" ("created_at");
    `);

    // Create add_on_migration_backup table to store original add-on data
    this.addSql(`
      CREATE TABLE IF NOT EXISTS "add_on_migration_backup" (
        "id" text NOT NULL,
        "original_product_id" text NOT NULL,
        "original_metadata" jsonb NOT NULL,
        "original_variants" jsonb NOT NULL,
        "backup_timestamp" timestamptz DEFAULT now(),
        PRIMARY KEY ("id")
      );
    `);

    this.addSql(`
      CREATE INDEX "IDX_add_on_migration_backup_product_id"
      ON "add_on_migration_backup" ("original_product_id");
    `);

    // Insert default supplier configurations for existing suppliers
    this.addSql(`
      INSERT INTO "add_on_supplier_config" (
        "id",
        "supplier_id",
        "default_margin_percentage",
        "default_currency",
        "auto_sync_enabled",
        "category_mapping",
        "field_mapping",
        "pricing_rules",
        "hotel_mapping"
      )
      SELECT
        'asc_' || "id",
        "id",
        20.00,
        'CHF',
        true,
        '{}',
        '{}',
        '{
          "adult_margin": 20,
          "child_margin": 15,
          "package_margin": 18,
          "seasonal_adjustments": [],
          "volume_discounts": []
        }',
        '{
          "default_service_level": "hotel",
          "hotel_assignments": {},
          "destination_assignments": {}
        }'
      FROM "supplier"
      WHERE "deleted_at" IS NULL
      ON CONFLICT ("supplier_id") DO NOTHING;
    `);
  }

  async down(): Promise<void> {
    // Drop tables in reverse order
    this.addSql(`DROP TABLE IF EXISTS "add_on_migration_backup";`);
    this.addSql(`DROP TABLE IF EXISTS "add_on_sync_log";`);
    this.addSql(`DROP TABLE IF EXISTS "add_on_supplier_config";`);
  }
}