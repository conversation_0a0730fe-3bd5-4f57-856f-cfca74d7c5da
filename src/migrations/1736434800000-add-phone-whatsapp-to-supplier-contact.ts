import { Migration } from "@mikro-orm/migrations";

/**
 * Migration to add phone_number and is_whatsapp fields to supplier_contact table
 *
 * This migration adds:
 * - phone_number: nullable text field for contact phone numbers
 * - is_whatsapp: boolean field to indicate if phone supports WhatsApp (default: false)
 * - Indexes for efficient phone number searching and filtering
 */
export class Migration1736434800000 extends Migration {
  async up(): Promise<void> {
    // Add phone_number column to supplier_contact table
    this.addSql(`
      ALTER TABLE "supplier_contact"
      ADD COLUMN "phone_number" text
    `);

    // Add is_whatsapp column to supplier_contact table
    this.addSql(`
      ALTER TABLE "supplier_contact"
      ADD COLUMN "is_whatsapp" boolean NOT NULL DEFAULT false
    `);

    // Create index for phone_number
    this.addSql(`
      CREATE INDEX "IDX_supplier_contact_phone"
      ON "supplier_contact" ("phone_number")
      WHERE "deleted_at" IS NULL AND "phone_number" IS NOT NULL
    `);

    // Create composite index for phone and WhatsApp filtering
    this.addSql(`
      CREATE INDEX "IDX_supplier_contact_phone_whatsapp"
      ON "supplier_contact" ("phone_number", "is_whatsapp")
      WHERE "deleted_at" IS NULL AND "phone_number" IS NOT NULL
    `);
  }

  async down(): Promise<void> {
    // Drop indexes first
    this.addSql(`
      DROP INDEX IF EXISTS "IDX_supplier_contact_phone_whatsapp"
    `);

    this.addSql(`
      DROP INDEX IF EXISTS "IDX_supplier_contact_phone"
    `);

    // Drop columns
    this.addSql(`
      ALTER TABLE "supplier_contact"
      DROP COLUMN IF EXISTS "is_whatsapp"
    `);

    this.addSql(`
      ALTER TABLE "supplier_contact"
      DROP COLUMN IF EXISTS "phone_number"
    `);
  }
}
