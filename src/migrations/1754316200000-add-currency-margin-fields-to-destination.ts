import { Migration } from "@mikro-orm/migrations";

/**
 * Migration to add currency and margin fields to destination table
 *
 * This migration adds:
 * - internal_web_link column (TEXT, nullable)
 * - external_web_link column (TEXT, nullable)
 * - currency_code column (TEXT, nullable)
 * - margin_percentage column (DECIMAL(5,2), nullable)
 */
export class Migration1754316200000 extends Migration {
  async up(): Promise<void> {
    this.addSql(`
      ALTER TABLE "destination" 
      ADD COLUMN "internal_web_link" TEXT,
      ADD COLUMN "external_web_link" TEXT,
      ADD COLUMN "currency_code" TEXT,
      ADD COLUMN "margin_percentage" DECIMAL(5,2)
    `);
  }

  async down(): Promise<void> {
    this.addSql(`
      ALTER TABLE "destination" 
      DROP COLUMN IF EXISTS "internal_web_link",
      DROP COLUMN IF EXISTS "external_web_link",
      DROP COLUMN IF EXISTS "currency_code",
      DROP COLUMN IF EXISTS "margin_percentage"
    `);
  }
}
