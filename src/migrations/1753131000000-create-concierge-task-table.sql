-- Create the concierge_task table
CREATE TABLE IF NOT EXISTS "concierge_task" (
    "id" varchar PRIMARY KEY DEFAULT gen_random_uuid(),
    "title" varchar(255) NOT NULL,
    "description" text,
    "status" varchar(50) NOT NULL DEFAULT 'pending',
    "priority" varchar(50) NOT NULL DEFAULT 'medium',
    "entity_type" varchar(100),
    "entity_id" varchar(255),
    "assigned_to" varchar(255),
    "created_by" varchar(255),
    "updated_by" varchar(255),
    "due_date" timestamptz,
    "is_deleted" boolean NOT NULL DEFAULT false,
    "deleted_at" timestamptz,
    "created_at" timestamptz NOT NULL DEFAULT now(),
    "updated_at" timestamptz NOT NULL DEFAULT now(),
    "metadata" jsonb
);

-- Create indexes for better query performance
CREATE INDEX IF NOT EXISTS "IDX_concierge_task_entity" ON "concierge_task" ("entity_type", "entity_id");
CREATE INDEX IF NOT EXISTS "IDX_concierge_task_status" ON "concierge_task" ("status");
CREATE INDEX IF NOT EXISTS "IDX_concierge_task_priority" ON "concierge_task" ("priority");
CREATE INDEX IF NOT EXISTS "IDX_concierge_task_assigned_to" ON "concierge_task" ("assigned_to");
CREATE INDEX IF NOT EXISTS "IDX_concierge_task_created_at" ON "concierge_task" ("created_at");
CREATE INDEX IF NOT EXISTS "IDX_concierge_task_due_date" ON "concierge_task" ("due_date");
CREATE INDEX IF NOT EXISTS "IDX_concierge_task_is_deleted" ON "concierge_task" ("is_deleted");
