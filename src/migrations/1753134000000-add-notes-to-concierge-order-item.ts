import { Migration } from "@mikro-orm/migrations";

export class Migration1753134000000AddNotesToConciergeOrderItem extends Migration {
  async up(): Promise<void> {
    // Add notes column to concierge_order_item table
    this.addSql(`
      ALTER TABLE "concierge_order_item" 
      ADD COLUMN IF NOT EXISTS "notes" text NULL;
    `);

    // Add index for notes column for performance when searching/filtering by notes
    this.addSql(`
      CREATE INDEX IF NOT EXISTS "IDX_concierge_order_item_notes" 
      ON "concierge_order_item" ("notes") 
      WHERE "deleted_at" IS NULL AND "notes" IS NOT NULL;
    `);
  }

  async down(): Promise<void> {
    // Drop the index first
    this.addSql(`
      DROP INDEX IF EXISTS "IDX_concierge_order_item_notes";
    `);

    // Drop the notes column
    this.addSql(`
      ALTER TABLE "concierge_order_item" 
      DROP COLUMN IF EXISTS "notes";
    `);
  }
}
