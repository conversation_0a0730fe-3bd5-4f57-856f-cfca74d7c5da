import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { sdk } from "../../admin/lib/sdk";

export interface BookingAddOn {
  id: string;
  order_id: string;
  add_on_variant_id: string;
  add_on_name: string;
  quantity: number;
  unit_price: number;
  total_price: number;
  currency_code: string;
  customer_field_responses: Record<string, any>;
  add_on_metadata: Record<string, any>;
  supplier_order_id?: string;
  order_status?: "pending" | "confirmed" | "in_progress" | "completed" | "cancelled";
  created_at: string;
  updated_at: string;
  order?: {
    id: string;
    display_id: string;
    email: string;
    metadata?: {
      customer_name?: string;
      check_in_date?: string;
      check_out_date?: string;
      hotel_name?: string;
    };
  };
  add_on?: {
    id: string;
    title: string;
    metadata?: {
      supplier_id?: string;
      supplier_name?: string;
      category?: string;
      type?: string;
    };
  };
}

export interface BookingAddOnsResponse {
  booking_addons: BookingAddOn[];
  count: number;
  offset: number;
  limit: number;
}

export interface BookingAddOnsFilters {
  order_id?: string;
  order_status?: string;
  limit?: number;
  offset?: number;
  q?: string;
}

export const useBookingAddOns = (filters: BookingAddOnsFilters = {}) => {
  const query = useQuery({
    queryKey: ["booking-addons", filters],
    queryFn: async (): Promise<BookingAddOnsResponse> => {
      const searchParams = new URLSearchParams();

      if (filters.order_id) searchParams.set("order_id", filters.order_id);
      if (filters.order_status) searchParams.set("order_status", filters.order_status);
      if (filters.limit) searchParams.set("limit", filters.limit.toString());
      if (filters.offset) searchParams.set("offset", filters.offset.toString());
      if (filters.q) searchParams.set("q", filters.q);

      const url = `/admin/booking-addons${searchParams.toString() ? `?${searchParams.toString()}` : ""}`;
      const result = await sdk.client.fetch(url);
      return result as BookingAddOnsResponse;
    },
  });

  return query;
};

export const useBookingAddOn = (id: string) => {
  const query = useQuery({
    queryKey: ["booking-addon", id],
    queryFn: async (): Promise<BookingAddOn> => {
      const result = await sdk.client.fetch(`/admin/booking-addons/${id}`);
      return (result as any).booking_add_on;
    },
    enabled: !!id,
  });

  return query;
};

export const useUpdateBookingAddOn = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ id, data }: { id: string; data: Partial<BookingAddOn> }) => {
      const result = await sdk.client.fetch(`/admin/booking-addons/${id}`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: data,
      });

      return result;
    },
    onSuccess: (_, { id }) => {
      queryClient.invalidateQueries({ queryKey: ["booking-addon", id] });
      queryClient.invalidateQueries({ queryKey: ["booking-addons"] });
    },
  });
};

export const useDeleteBookingAddOn = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (id: string) => {
      const result = await sdk.client.fetch(`/admin/booking-addons/${id}`, {
        method: "DELETE",
      });

      return result;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["booking-addons"] });
    },
  });
};
