import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query"
import { sdk } from "../../lib/sdk"

export interface Artist {
  id: string
  name: string
  slug: string
  profile_image: string
  short_bio: string
  long_bio?: string
  status: "active" | "inactive"
  region?: string
  mediums: string[]
  themes: string[]
  style?: string
  art_category?: string
  catalog_link?: string
  socials?: {
    instagram?: string
    website?: string
    email?: string
  }
  created_at: string
  updated_at: string
}

export interface ArtistListParams {
  limit?: number
  offset?: number
  name?: string
  status?: "active" | "inactive"
  region?: string
  mediums?: string | string[]
  themes?: string | string[]
  style?: string
  art_category?: string
  sort_by?: "name" | "created_at" | "updated_at" | "status" | "region"
  sort_order?: "asc" | "desc"
  created_at?: any
  updated_at?: any
  fields?: string
}

export interface ArtistListResponse {
  artists: Artist[]
  count: number
  offset: number
  limit: number
}

export const useArtists = (
  params: ArtistListParams = {},
  options: any = {}
) => {
  return useQuery<ArtistListResponse>({
    queryKey: ["artists", params],
    queryFn: async (): Promise<ArtistListResponse> => {
      const searchParams = new URLSearchParams()

      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          if (Array.isArray(value)) {
            searchParams.append(key, value.join(","))
          } else if (typeof value === "object") {
            searchParams.append(key, JSON.stringify(value))
          } else {
            searchParams.append(key, String(value))
          }
        }
      })

      const response = await sdk.client.fetch(
        `/admin/artists?${searchParams.toString()}`,
        {}
      )

      console.log("Artists API Response:", response)

      return response as ArtistListResponse
    },
    ...options,
  })
}

export const useArtist = (id: string, options: any = {}) => {
  return useQuery({
    queryKey: ["artist", id],
    queryFn: async (): Promise<Artist> => {
      const response = await sdk.client.fetch(
        `/admin/artists/${id}`,
        {}
      )
      return response.artist
    },
    enabled: !!id,
    ...options,
  })
}

export const useCreateArtist = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async (data: Partial<Artist>) => {
      const response = await sdk.client.fetch(
        `/admin/artists`,
        {
          method: "POST",
          body: data,
          headers: {
            "Content-Type": "application/json",
          },
        }
      )
      return response
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["artists"] })
    },
  })
}

export const useUpdateArtist = (id: string) => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async (data: Partial<Artist>) => {
      const response = await sdk.client.fetch(
        `/admin/artists/${id}`,
        {
          method: "PUT",
          body: data,
          headers: {
            "Content-Type": "application/json",
          },
        }
      )
      return response
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["artists"] })
      queryClient.invalidateQueries({ queryKey: ["artist", id] })
    },
  })
}

export const useDeleteArtist = (id: string) => {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: async () => {
      const response = await sdk.client.fetch(
        `/admin/artists/${id}`,
        {
          method: "DELETE",
        }
      )
      return response
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["artists"] })
      queryClient.invalidateQueries({ queryKey: ["artist", id] })
    },
  })
}

export const useImportArtists = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async ({ file }: { file: File }) => {
      const formData = new FormData()
      formData.append('file', file)

      const response = await sdk.client.fetch("/admin/artists/import", {
        method: "POST",
        body: formData,
      })
      return response
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["artists"] })
    },
  })
}

export const useConfirmImportArtists = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async (transactionId: string) => {
      const response = await sdk.client.fetch(`/admin/artists/import/${transactionId}/confirm`, {
        method: "POST",
      })
      return response
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["artists"] })
    },
  })
}

export const useUploadArtistImage = () => {
  return useMutation({
    mutationFn: async ({ file }: { file: File }) => {
      const formData = new FormData()
      formData.append('profile_image', file)

      const response = await sdk.client.fetch("/admin/artists/upload", {
        method: "POST",
        body: formData,
      })
      return response
    },
  })
}

export interface ArtistArtwork {
  id: string
  title: string
  thumbnail?: string
  handle: string
  status: string
}

export interface ArtistArtworksResponse {
  artist_id: string
  artworks: ArtistArtwork[]
  total: number
}

export const useArtistArtworks = (artistId: string, options: any = {}) => {
  return useQuery<ArtistArtworksResponse>({
    queryKey: ["artist-artworks", artistId],
    queryFn: async (): Promise<ArtistArtworksResponse> => {
      const response = await sdk.client.fetch(
        `/admin/artists/${artistId}/artworks`,
        {}
      )
      return response as ArtistArtworksResponse
    },
    enabled: !!artistId,
    ...options,
  })
}

// Product-related interfaces
export interface Product {
  id: string
  title: string
  handle: string
  description?: string
  thumbnail?: string
  status: string
  created_at: string
  updated_at: string
}

export interface ProductListResponse {
  products: Product[]
  count: number
  offset: number
  limit: number
}

export interface CreateProductData {
  title: string
  handle?: string
  description?: string
  status?: "draft" | "published"
  images?: { url: string }[]
  weight?: number
  category_ids?: string[]
}

// Product hooks
export const useProducts = (params: any = {}, options: any = {}) => {
  return useQuery<ProductListResponse>({
    queryKey: ["products", params],
    queryFn: async (): Promise<ProductListResponse> => {
      const searchParams = new URLSearchParams()

      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          if (Array.isArray(value)) {
            searchParams.append(key, value.join(","))
          } else {
            searchParams.append(key, String(value))
          }
        }
      })

      const response = await sdk.client.fetch(
        `/admin/products?${searchParams.toString()}`,
        {}
      )

      return response as ProductListResponse
    },
    ...options,
  })
}

export const useCreateProduct = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async (data: CreateProductData) => {
      const response = await sdk.client.fetch(
        `/admin/products`,
        {
          method: "POST",
          body: data,
          headers: {
            "Content-Type": "application/json",
          },
        }
      )
      return response
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["products"] })
    },
  })
}

export const useLinkArtworks = (artistId: string) => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async (productIds: string[]) => {
      const response = await sdk.client.fetch(
        `/admin/artists/${artistId}/artworks`,
        {
          method: "POST",
          body: { product_ids: productIds },
          headers: {
            "Content-Type": "application/json",
          },
        }
      )
      return response
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["artist-artworks", artistId] })
      queryClient.invalidateQueries({ queryKey: ["products"] })
    },
  })
}

export const useUnlinkArtworks = (artistId: string) => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async (productIds: string[]) => {
      const response = await sdk.client.fetch(
        `/admin/artists/${artistId}/artworks`,
        {
          method: "DELETE",
          body: { product_ids: productIds },
          headers: {
            "Content-Type": "application/json",
          },
        }
      )
      return response
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["artist-artworks", artistId] })
      queryClient.invalidateQueries({ queryKey: ["products"] })
    },
  })
}
