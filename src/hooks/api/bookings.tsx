import { QueryClient } from "@tanstack/react-query"
import { useQuery, useMutation, useQueryClient, UseQueryOptions, UseMutationOptions } from "@tanstack/react-query"
import { HttpTypes } from "@camped-ai/types"
import { sdk } from "../../admin/lib/sdk"
import { queryClient } from "../../admin/lib/query-client"
import { queryKeysFactory } from "../../admin/lib/query-key-factory"

// Query key factory for bookings
const BOOKINGS_QUERY_KEY = "bookings" as const
export const bookingsQueryKeys = queryKeysFactory(BOOKINGS_QUERY_KEY)

// Types for booking data
export interface BookingFilters {
  limit?: number
  offset?: number
  hotel_id?: string
  status?: string
  payment_status?: string
  booking_status?: string
  sort_by?: string
  sort_order?: 'asc' | 'desc'
  customer_id?: string
  page?: number
}

export interface Booking {
  id: string
  display_id: string
  customer_id: string
  email: string
  status: string
  payment_status: string
  total: number
  currency_code: string
  created_at: string
  updated_at: string
  metadata?: {
    hotel_id?: string
    hotel_name?: string
    room_config_id?: string
    room_config_name?: string
    room_number?: string
    check_in_date?: string
    check_out_date?: string
    guest_name?: string
    guest_email?: string
    guest_phone?: string
    number_of_guests?: number
    special_requests?: string
    booking_status?: string
    [key: string]: any
  }
  items?: any[]
  payments?: any[]
  shipping_address?: any
  billing_address?: any
}

export interface BookingsResponse {
  bookings: Booking[]
  count: number
  limit: number
  offset: number
}

export interface BookingResponse {
  booking: Booking
}

// Booking list query function
const bookingsListQuery = (filters: BookingFilters = {}) => ({
  queryKey: bookingsQueryKeys.list(filters),
  queryFn: async (): Promise<BookingsResponse> => {
    const params = new URLSearchParams()
    
    // Add filters to query params
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        params.append(key, value.toString())
      }
    })

    const url = `/admin/hotel-management/bookings${params.toString() ? `?${params.toString()}` : ''}`
    
    try {
      const response = await sdk.client.fetch(url)
      return response as BookingsResponse
    } catch (error) {
      console.error('Failed to fetch bookings:', error)
      throw error
    }
  }
})

// Booking detail query function
const bookingDetailQuery = (id: string) => ({
  queryKey: bookingsQueryKeys.detail(id),
  queryFn: async (): Promise<BookingResponse> => {
    try {
      const response = await sdk.client.fetch(`/admin/hotel-management/bookings/${id}`)
      return response as BookingResponse
    } catch (error) {
      console.error(`Failed to fetch booking ${id}:`, error)
      throw error
    }
  }
})

// Loader function for bookings list
export const bookingsLoader = (client: QueryClient) => {
  return async (filters: BookingFilters = {}) => {
    const query = bookingsListQuery(filters)

    return (
      queryClient.getQueryData<BookingsResponse>(query.queryKey) ?? 
      (await client.fetchQuery(query))
    )
  }
}

// Loader function for booking detail
export const bookingLoader = (client: QueryClient) => {
  return async (id: string) => {
    const query = bookingDetailQuery(id)

    return (
      queryClient.getQueryData<BookingResponse>(query.queryKey) ?? 
      (await client.fetchQuery(query))
    )
  }
}

// Hook to fetch bookings list
export const useBookings = (
  filters: BookingFilters = {},
  options?: Omit<UseQueryOptions<BookingsResponse, Error, BookingsResponse>, "queryKey" | "queryFn">
) => {
  return useQuery({
    ...bookingsListQuery(filters),
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
    refetchOnWindowFocus: false,
    ...options,
  })
}

// Hook to fetch a single booking
export const useBooking = (
  id: string,
  options?: Omit<UseQueryOptions<BookingResponse, Error, BookingResponse>, "queryKey" | "queryFn">
) => {
  return useQuery({
    ...bookingDetailQuery(id),
    enabled: !!id,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
    refetchOnWindowFocus: false,
    ...options,
  })
}

// Hook to create a booking reservation
export const useCreateBookingReservation = (
  options?: UseMutationOptions<any, Error, any>
) => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async (payload: any) => {
      try {
        const response = await sdk.client.fetch('/store/hotel-management/bookings/reserve', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(payload),
        })
        return response
      } catch (error) {
        console.error('Failed to create booking reservation:', error)
        throw error
      }
    },
    onSuccess: (data, variables, context) => {
      // Invalidate bookings queries to refresh the data
      queryClient.invalidateQueries({ queryKey: bookingsQueryKeys.lists() })
      options?.onSuccess?.(data, variables, context)
    },
    ...options,
  })
}

// Hook to confirm a booking
export const useConfirmBooking = (
  options?: UseMutationOptions<any, Error, any>
) => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async (payload: any) => {
      try {
        const response = await sdk.client.fetch('/store/hotel-management/bookings/confirm', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(payload),
        })
        return response
      } catch (error) {
        console.error('Failed to confirm booking:', error)
        throw error
      }
    },
    onSuccess: (data, variables, context) => {
      // Invalidate bookings queries to refresh the data
      queryClient.invalidateQueries({ queryKey: bookingsQueryKeys.lists() })
      if (variables.reservation_id) {
        queryClient.invalidateQueries({ 
          queryKey: bookingsQueryKeys.detail(variables.reservation_id) 
        })
      }
      options?.onSuccess?.(data, variables, context)
    },
    ...options,
  })
}

// Hook to update booking status
export const useUpdateBookingStatus = (
  bookingId: string,
  options?: UseMutationOptions<any, Error, any>
) => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async (payload: { status: string; metadata?: any }) => {
      try {
        const response = await sdk.client.fetch(`/admin/hotel-management/bookings/${bookingId}/status`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(payload),
        })
        return response
      } catch (error) {
        console.error(`Failed to update booking ${bookingId} status:`, error)
        throw error
      }
    },
    onSuccess: (data, variables, context) => {
      // Invalidate bookings queries to refresh the data
      queryClient.invalidateQueries({ queryKey: bookingsQueryKeys.lists() })
      queryClient.invalidateQueries({ queryKey: bookingsQueryKeys.detail(bookingId) })
      options?.onSuccess?.(data, variables, context)
    },
    ...options,
  })
}

// Hook to process booking payment
export const useProcessBookingPayment = (
  options?: UseMutationOptions<any, Error, any>
) => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async (payload: any) => {
      try {
        const response = await sdk.client.fetch('/store/hotel-management/bookings/payment', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(payload),
        })
        return response
      } catch (error) {
        console.error('Failed to process booking payment:', error)
        throw error
      }
    },
    onSuccess: (data, variables, context) => {
      // Invalidate bookings queries to refresh the data
      queryClient.invalidateQueries({ queryKey: bookingsQueryKeys.lists() })
      if (variables.booking_id) {
        queryClient.invalidateQueries({ 
          queryKey: bookingsQueryKeys.detail(variables.booking_id) 
        })
      }
      options?.onSuccess?.(data, variables, context)
    },
    ...options,
  })
}
