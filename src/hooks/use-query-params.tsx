import { useSearchParams } from "react-router-dom"

export const useQueryParams = (keys: string[], prefix?: string) => {
  const [searchParams] = useSearchParams()
  
  const queryObject: Record<string, string | undefined> = {}
  
  keys.forEach((key) => {
    const paramKey = prefix ? `${prefix}_${key}` : key
    queryObject[key] = searchParams.get(paramKey) || undefined
  })
  
  return queryObject
}
