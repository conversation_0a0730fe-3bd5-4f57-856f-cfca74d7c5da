import { useQueryParams } from "../../use-query-params"
import { ArtistListParams } from "../../api/artists"

type UseArtistTableQueryProps = {
  prefix?: string
  pageSize?: number
}

const DEFAULT_FIELDS = "id,name,slug,profile_image,short_bio,status,region,art_category,created_at,updated_at"

export const useArtistTableQuery = ({
  prefix,
  pageSize = 20,
}: UseArtistTableQueryProps) => {
  const queryObject = useQueryParams(
    [
      "offset",
      "order",
      "q",
      "created_at",
      "updated_at",
      "status",
      "region",
      "art_category",
      "id",
    ],
    prefix
  )

  const {
    offset,
    created_at,
    updated_at,
    status,
    region,
    art_category,
    order,
    q,
  } = queryObject

  const searchParams: ArtistListParams = {
    limit: pageSize,
    offset: offset ? Number(offset) : 0,
    created_at: created_at ? JSON.parse(created_at) : undefined,
    updated_at: updated_at ? JSON.parse(updated_at) : undefined,
    status: status?.split(",")[0] as "active" | "inactive" | undefined,
    region: region?.split(",")[0],
    art_category: art_category?.split(",")[0],
    sort_by: order?.split(".")[0] as "name" | "created_at" | "updated_at" | "status" | "region" | undefined,
    sort_order: order?.split(".")[1] as "asc" | "desc" | undefined,
    name: q,
    fields: DEFAULT_FIELDS,
  }

  return {
    searchParams,
    raw: queryObject,
  }
}
