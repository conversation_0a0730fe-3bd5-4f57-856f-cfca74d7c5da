import { useCallback } from "react"
import { useSearchParams } from "react-router-dom"

interface UseSelectedParamsProps {
  param: string
  prefix?: string
  multiple?: boolean
}

export const useSelectedParams = ({
  param,
  prefix,
  multiple = false,
}: UseSelectedParamsProps) => {
  const [searchParams, setSearchParams] = useSearchParams()
  
  const paramKey = prefix ? `${prefix}_${param}` : param

  const get = useCallback(() => {
    const value = searchParams.get(paramKey)
    if (!value) return multiple ? [] : undefined
    
    if (multiple) {
      return value.split(",").filter(Boolean)
    }
    
    return [value]
  }, [searchParams, paramKey, multiple])

  const add = useCallback((value: string | string[]) => {
    setSearchParams((prev) => {
      const newParams = new URLSearchParams(prev)
      
      if (Array.isArray(value)) {
        if (value.length === 0) {
          newParams.delete(paramKey)
        } else {
          newParams.set(paramKey, value.join(","))
        }
      } else {
        newParams.set(paramKey, value)
      }
      
      return newParams
    })
  }, [setSearchParams, paramKey])

  const remove = useCallback((value: string) => {
    if (!multiple) {
      setSearchParams((prev) => {
        const newParams = new URLSearchParams(prev)
        newParams.delete(paramKey)
        return newParams
      })
      return
    }

    const current = get() as string[]
    const updated = current.filter(v => v !== value)
    add(updated)
  }, [get, add, multiple, setSearchParams, paramKey])

  const deleteAll = useCallback(() => {
    setSearchParams((prev) => {
      const newParams = new URLSearchParams(prev)
      newParams.delete(paramKey)
      return newParams
    })
  }, [setSearchParams, paramKey])

  return {
    get,
    add,
    remove,
    delete: deleteAll,
  }
}
