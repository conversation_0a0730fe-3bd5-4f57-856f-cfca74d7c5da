import { createColumnHelper } from "@tanstack/react-table"
import { useMemo } from "react"
import { useSearchParams } from "react-router-dom"
import { Artist } from "../../api/artists"
import {
  <PERSON><PERSON><PERSON>,
  ArtistHeader,
} from "../../../components/table/table-cells/artist/artist-cell"
import {
  ArtistStatusCell,
  ArtistStatusHeader,
} from "../../../components/table/table-cells/artist/artist-status-cell"
import {
  RegionCell,
  RegionHeader,
} from "../../../components/table/table-cells/artist/region-cell"
import {
  CategoryCell,
  CategoryHeader,
} from "../../../components/table/table-cells/artist/category-cell"
import {
  <PERSON><PERSON>ell,
  BiographyHeader,
} from "../../../components/table/table-cells/artist/biography-cell"

// Sortable header component
const SortableHeader = ({ children, sortKey }: { children: React.ReactNode, sortKey: string }) => {
  const [searchParams, setSearchParams] = useSearchParams()
  const currentOrder = searchParams.get('order')

  const isActive = currentOrder?.replace('-', '') === sortKey
  const isDesc = currentOrder?.startsWith('-')

  const handleSort = () => {
    setSearchParams((prev) => {
      const newParams = new URLSearchParams(prev)

      if (isActive) {
        if (isDesc) {
          // Remove sorting
          newParams.delete('order')
        } else {
          // Switch to descending
          newParams.set('order', `-${sortKey}`)
        }
      } else {
        // Set ascending
        newParams.set('order', sortKey)
      }

      return newParams
    })
  }

  return (
    <button
      onClick={handleSort}
      className="flex items-center gap-1 hover:text-gray-700 transition-colors"
    >
      <span>{children}</span>
      <div className="flex flex-col">
        <svg
          className={`w-3 h-3 ${isActive && !isDesc ? 'text-blue-600' : 'text-gray-300'}`}
          fill="currentColor"
          viewBox="0 0 20 20"
        >
          <path fillRule="evenodd" d="M14.707 12.707a1 1 0 01-1.414 0L10 9.414l-3.293 3.293a1 1 0 01-1.414-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 010 1.414z" clipRule="evenodd" />
        </svg>
        <svg
          className={`w-3 h-3 -mt-1 ${isActive && isDesc ? 'text-blue-600' : 'text-gray-300'}`}
          fill="currentColor"
          viewBox="0 0 20 20"
        >
          <path fillRule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clipRule="evenodd" />
        </svg>
      </div>
    </button>
  )
}

const columnHelper = createColumnHelper<Artist>()

export const useArtistTableColumns = () => {
  return useMemo(
    () => [
      columnHelper.display({
        id: "artist",
        header: () => <SortableHeader sortKey="name">Artist</SortableHeader>,
        cell: ({ row }) => <ArtistCell artist={row.original} />,
      }),
      columnHelper.accessor("short_bio", {
        header: () => <BiographyHeader />,
        cell: ({ row }) => (
          <BiographyCell biography={row.original.short_bio} />
        ),
      }),
      columnHelper.accessor("region", {
        header: () => <SortableHeader sortKey="region">Region</SortableHeader>,
        cell: ({ row }) => (
          <RegionCell region={row.original.region} />
        ),
      }),
      columnHelper.accessor("art_category", {
        header: () => <SortableHeader sortKey="art_category">Category</SortableHeader>,
        cell: ({ row }) => (
          <CategoryCell category={row.original.art_category} />
        ),
      }),
      columnHelper.accessor("status", {
        header: () => <SortableHeader sortKey="status">Status</SortableHeader>,
        cell: ({ row }) => <ArtistStatusCell status={row.original.status} />,
      }),
    ],
    []
  )
}
