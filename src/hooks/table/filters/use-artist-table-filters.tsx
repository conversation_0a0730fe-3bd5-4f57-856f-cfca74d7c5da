import { useMemo } from "react"
import { Filter } from "../../components/table/data-table-filter"

const excludeableFields = [
  "status",
  "region", 
  "art_category",
] as const

export const useArtistTableFilters = (
  exclude?: (typeof excludeableFields)[number][]
) => {
  const filters = useMemo(() => {
    let filterList: Filter[] = []

    // Status filter
    if (!exclude?.includes("status")) {
      const statusFilter: Filter = {
        key: "status",
        label: "Status",
        type: "select",
        multiple: true,
        options: [
          {
            label: "Active",
            value: "active",
          },
          {
            label: "Inactive", 
            value: "inactive",
          },
        ],
      }
      filterList.push(statusFilter)
    }

    // Region filter
    if (!exclude?.includes("region")) {
      const regionFilter: Filter = {
        key: "region",
        label: "Region",
        type: "select",
        multiple: true,
        options: [
          { label: "North America", value: "north_america" },
          { label: "Europe", value: "europe" },
          { label: "Asia", value: "asia" },
          { label: "South America", value: "south_america" },
          { label: "Africa", value: "africa" },
          { label: "Oceania", value: "oceania" },
        ],
      }
      filterList.push(regionFilter)
    }

    // Art Category filter
    if (!exclude?.includes("art_category")) {
      const categoryFilter: Filter = {
        key: "art_category",
        label: "Art Category",
        type: "select",
        multiple: true,
        options: [
          { label: "Painting", value: "painting" },
          { label: "Sculpture", value: "sculpture" },
          { label: "Photography", value: "photography" },
          { label: "Digital Art", value: "digital_art" },
          { label: "Mixed Media", value: "mixed_media" },
          { label: "Drawing", value: "drawing" },
          { label: "Printmaking", value: "printmaking" },
          { label: "Textile Art", value: "textile_art" },
        ],
      }
      filterList.push(categoryFilter)
    }

    // Date filters
    const dateFilters: Filter[] = [
      { label: "Created At", key: "created_at" },
      { label: "Updated At", key: "updated_at" },
    ].map((f) => ({
      key: f.key,
      label: f.label,
      type: "date" as const,
    }))

    filterList = [...filterList, ...dateFilters]

    return filterList
  }, [exclude])

  return filters
}
