import {
  useReactTable,
  getCoreRowModel,
  getPaginationRowModel,
  ColumnDef,
} from "@tanstack/react-table"

interface UseDataTableProps<TData> {
  data: TData[]
  columns: ColumnDef<TData, any>[]
  count: number
  enablePagination?: boolean
  pageSize: number
  getRowId?: (row: TData) => string
}

export const useDataTable = <TData,>({
  data,
  columns,
  count,
  enablePagination = true,
  pageSize,
  getRowId,
}: UseDataTableProps<TData>) => {
  const table = useReactTable({
    data,
    columns,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: enablePagination ? getPaginationRowModel() : undefined,
    getRowId,
    initialState: {
      pagination: {
        pageSize,
        pageIndex: 0,
      },
    },
    pageCount: enablePagination ? Math.ceil(count / pageSize) : undefined,
    manualPagination: true,
  })

  return { table }
}
