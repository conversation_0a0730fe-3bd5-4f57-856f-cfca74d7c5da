import { Heading, But<PERSON> } from "@camped-ai/ui"
import { useTranslation } from "react-i18next"

import { Link, useNavigate } from "react-router-dom";

interface PageHeaderProps {
    hasCreate?: boolean;
    hasImport?: boolean;
    hasExport?: boolean;
    title: string;
    path: string;
}



export const PageHeader = ({ hasCreate, hasImport, hasExport, title, path }: PageHeaderProps) => {

    const navigate = useNavigate()
    const { t } = useTranslation()

    return (
        <div className="flex items-center justify-between px-6 py-4">
            <div>
                <Heading level="h2">{title}</Heading>
            </div>
            <div className="flex items-center gap-x-2">
                {hasExport && (
                    <Button
                        variant="secondary"
                        size="small"
                        onClick={() => navigate(`${path}/export`)}
                        className="flex items-center gap-2"
                    >
                        Export
                    </Button>
                )}
                {hasImport && (
                    <Button
                        variant="secondary"
                        size="small"
                        onClick={() => navigate(`${path}/import`)}
                        className="flex items-center gap-2"
                    >
                        Import
                    </Button>
                )}
                {hasCreate && (
                    <Button size="small" asChild>
                        <Link to={`${path}/create`}>
                            {t("actions.create")}
                        </Link>
                    </Button>
                )}
            </div>
        </div>
    )
}