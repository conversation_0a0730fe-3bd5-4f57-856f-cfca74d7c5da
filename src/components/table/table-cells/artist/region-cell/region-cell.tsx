import { PlaceholderCell } from "../../common/placeholder-cell"

type RegionCellProps = {
  region?: string | null
}

export const RegionCell = ({ region }: RegionCellProps) => {
  if (!region) {
    return <PlaceholderCell />
  }

  const getRegionLabel = (region: string) => {
    switch (region.toLowerCase()) {
      case "north_america":
        return "North America"
      case "south_america":
        return "South America"
      case "europe":
        return "Europe"
      case "asia":
        return "Asia"
      case "africa":
        return "Africa"
      case "oceania":
        return "Oceania"
      default:
        return region
    }
  }

  return (
    <div className="flex h-full w-full items-center overflow-hidden">
      <span className="truncate text-ui-fg-base">
        {getRegionLabel(region)}
      </span>
    </div>
  )
}

export const RegionHeader = () => {
  return (
    <div className="flex h-full w-full items-center">
      <span className="truncate">Region</span>
    </div>
  )
}
