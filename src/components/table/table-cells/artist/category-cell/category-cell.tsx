import { PlaceholderCell } from "../../common/placeholder-cell"

type CategoryCellProps = {
  category?: string | null
}

export const CategoryCell = ({ category }: CategoryCellProps) => {
  if (!category) {
    return <PlaceholderCell />
  }

  const getCategoryLabel = (category: string) => {
    switch (category.toLowerCase()) {
      case "painting":
        return "Painting"
      case "sculpture":
        return "Sculpture"
      case "photography":
        return "Photography"
      case "digital_art":
        return "Digital Art"
      case "mixed_media":
        return "Mixed Media"
      case "drawing":
        return "Drawing"
      case "printmaking":
        return "Printmaking"
      case "textile_art":
        return "Textile Art"
      default:
        return category
    }
  }

  return (
    <div className="flex h-full w-full items-center overflow-hidden">
      <span className="truncate text-ui-fg-base">
        {getCategoryLabel(category)}
      </span>
    </div>
  )
}

export const CategoryHeader = () => {
  return (
    <div className="flex h-full w-full items-center">
      <span className="truncate">Category</span>
    </div>
  )
}
