import { PlaceholderCell } from "../../common/placeholder-cell"

type BiographyCellProps = {
  biography?: string | null
}

export const BiographyCell = ({ biography }: BiographyCellProps) => {
  if (!biography) {
    return <PlaceholderCell />
  }

  // Truncate biography to a reasonable length for table display
  const truncatedBio = biography.length > 100 
    ? `${biography.substring(0, 100)}...` 
    : biography

  return (
    <div className="flex h-full w-full items-center overflow-hidden">
      <span className="truncate text-ui-fg-base" title={biography}>
        {truncatedBio}
      </span>
    </div>
  )
}

export const BiographyHeader = () => {
  return (
    <div className="flex h-full w-full items-center">
      <span className="truncate">Biography</span>
    </div>
  )
}
