import { Artist } from "../../../../api/artists"
import { PlaceholderCell } from "../../common/placeholder-cell"

type ArtistCellProps = {
  artist: Artist
}

export const ArtistCell = ({ artist }: ArtistCellProps) => {
  if (!artist.name) {
    return <PlaceholderCell />
  }

  return (
    <div className="flex h-full w-full items-center gap-x-3 overflow-hidden">
      {artist.profile_image && (
        <div className="flex h-8 w-8 items-center justify-center overflow-hidden rounded-md">
          <img
            src={artist.profile_image}
            alt={artist.name}
            className="h-full w-full object-cover"
            onError={(e) => {
              // Hide image if it fails to load
              e.currentTarget.style.display = 'none'
            }}
          />
        </div>
      )}
      <div className="flex flex-col overflow-hidden">
        <span className="truncate font-medium text-ui-fg-base">
          {artist.name}
        </span>
        {artist.slug && (
          <span className="truncate text-ui-fg-subtle text-xs">
            /{artist.slug}
          </span>
        )}
      </div>
    </div>
  )
}

export const ArtistHeader = () => {
  return (
    <div className="flex h-full w-full items-center">
      <span className="truncate">Artist</span>
    </div>
  )
}
