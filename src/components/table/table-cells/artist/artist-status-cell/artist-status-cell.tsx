import { clx } from "@camped-ai/ui"
import { PlaceholderCell } from "../../common/placeholder-cell"

type ArtistStatusCellProps = {
  status?: string | null
}

export const ArtistStatusCell = ({ status }: ArtistStatusCellProps) => {
  if (!status) {
    return <PlaceholderCell />
  }

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case "active":
        return "green"
      case "inactive":
        return "red"
      default:
        return "grey"
    }
  }

  const getStatusLabel = (status: string) => {
    switch (status.toLowerCase()) {
      case "active":
        return "Active"
      case "inactive":
        return "Inactive"
      default:
        return status
    }
  }

  const color = getStatusColor(status)
  const label = getStatusLabel(status)

  return (
    <div className="txt-compact-small text-ui-fg-subtle flex h-full w-full items-center gap-x-2 overflow-hidden">
      <div
        role="presentation"
        className="flex h-5 w-2 items-center justify-center"
      >
        <div
          className={clx(
            "h-2 w-2 rounded-sm shadow-[0px_0px_0px_1px_rgba(0,0,0,0.12)_inset]",
            {
              "bg-ui-tag-neutral-icon": color === "grey",
              "bg-ui-tag-green-icon": color === "green",
              "bg-ui-tag-red-icon": color === "red",
              "bg-ui-tag-blue-icon": color === "blue",
              "bg-ui-tag-orange-icon": color === "orange",
              "bg-ui-tag-purple-icon": color === "purple",
            }
          )}
        />
      </div>
      <span className="truncate">{label}</span>
    </div>
  )
}

export const ArtistStatusHeader = () => {
  return (
    <div className="flex h-full w-full items-center">
      <span className="truncate">Status</span>
    </div>
  )
}
