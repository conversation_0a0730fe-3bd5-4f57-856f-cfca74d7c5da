import { Container, Heading, Text } from "@camped-ai/ui";

interface JsonViewSectionProps {
  data: any;
}

export const JsonViewSection = ({ data }: JsonViewSectionProps) => {
  return (
    <Container className="divide-y p-0">
      <div className="flex items-center justify-between px-6 py-4">
        <Heading level="h2">JSON View</Heading>
      </div>
      <div className="px-6 py-4">
        <pre className="bg-muted p-4 rounded-md overflow-auto text-sm">
          {JSON.stringify(data, null, 2)}
        </pre>
      </div>
    </Container>
  );
};
