import { Container, Heading } from "@camped-ai/ui";
import { SectionRow } from "./section/section-row";

interface MetadataSectionProps {
  data: any;
}

export const MetadataSection = ({ data }: MetadataSectionProps) => {
  if (!data || !data.metadata) {
    return null;
  }

  return (
    <Container className="divide-y p-0">
      <div className="flex items-center justify-between px-6 py-4">
        <Heading level="h2">Metadata</Heading>
      </div>
      {Object.entries(data.metadata).map(([key, value]) => (
        <SectionRow 
          key={key}
          title={key} 
          value={typeof value === 'object' ? JSON.stringify(value) : String(value)} 
        />
      ))}
    </Container>
  );
};
