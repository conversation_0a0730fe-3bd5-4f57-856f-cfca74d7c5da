import { Module } from "@camped-ai/framework/utils";
import BookingAddOnService from "./service";

export const BOOKING_ADD_ONS_MODULE = "booking_add_ons";
export const BOOKING_ADD_ONS_SERVICE = "bookingAddOnsService";

// Export types
export {
  CreateBookingAddOnInput,
  UpdateBookingAddOnInput,
  BookingAddOnResponse,
  AvailableAddOnResponse,
  CustomerFieldValidationResult,
} from "./types";

// Create the module
const bookingAddOnsModule = Module(BOOKING_ADD_ONS_MODULE, {
  service: BookingAddOnService,
});

// Export the module
export default bookingAddOnsModule;
