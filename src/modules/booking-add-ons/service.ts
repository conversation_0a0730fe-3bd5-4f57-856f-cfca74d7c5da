import { Modules } from "@camped-ai/framework/utils";
import { MedusaContainer } from "@camped-ai/framework/types";
import {
  CreateBookingAddOnInput,
  UpdateBookingAddOnInput,
  BookingAddOnResponse,
  AvailableAddOnResponse,
  CustomerFieldValidationResult,
  AvailableAddOnsFilters,
  ListAvailableAddOnsResponse,
  ListBookingAddOnsResponse,
} from "./types";
import { AddOrderItemsWorkflow } from "../../workflows/order-management";

class BookingAddOnService {
  protected container: MedusaContainer;

  constructor(container: MedusaContainer) {
    this.container = container;
  }

  /**
   * Get all available add-ons (no filtering for now)
   */
  async getAvailableAddOns(
    filters?: AvailableAddOnsFilters
  ): Promise<ListAvailableAddOnsResponse> {
    try {
      console.log("🔍 Fetching all available add-ons...");
      console.log("📊 Filters:", filters);

      const query = this.container.resolve("query");

      const { data: addOns } = await query.graph({
        entity: "product_variant",
        filters: {
          metadata: {
            add_on_service: true,
            status: "Active",
          },
        },
        fields: ["id", "title", "metadata", "created_at", "updated_at"],
      });

      // Transform to include pricing and customer fields
      const transformedAddOns: AvailableAddOnResponse[] = addOns.map(
        (addOn: any) => ({
          id: addOn.id,
          title: addOn.title,
          description: addOn.metadata?.description || "",
          selling_price: parseFloat(addOn.metadata?.selling_price) || 0,
          selling_currency: addOn.metadata?.selling_currency || "CHF",
          service_level: addOn.metadata?.service_level || "hotel",
          customer_fields: addOn.metadata?.customer_fields || [],
          metadata: addOn.metadata,
        })
      );

      console.log(`✅ Found ${transformedAddOns.length} available add-ons`);

      return {
        add_ons: transformedAddOns,
        count: transformedAddOns.length,
      };
    } catch (error) {
      console.error("❌ Failed to fetch available add-ons:", error);
      throw error;
    }
  }

  /**
   * Create a booking add-on entry using AddOrderItemsWorkflow for consistency
   */
  async createBookingAddOn(
    data: CreateBookingAddOnInput,
    container?: any
  ): Promise<BookingAddOnResponse> {
    try {
      console.log(`📝 Creating booking add-on for order ${data.order_id}...`);

      // Use the provided container or fall back to the instance container
      const activeContainer = container || this.container;

      // Get the order service and query service
      const orderService = activeContainer.resolve(Modules.ORDER);
      const query = activeContainer.resolve("query");

      // Verify the order exists and get its status
      const order = await orderService.retrieveOrder(data.order_id);
      if (!order) {
        throw new Error(`Order with ID ${data.order_id} not found`);
      }

      // Fetch complete product variant information
      console.log(`🔍 Fetching product variant details for: ${data.add_on_variant_id}`);
      const { data: variants } = await query.graph({
        entity: "product_variant",
        filters: { id: data.add_on_variant_id },
        fields: [
          "id",
          "title",
          "sku",
          "barcode",
          "product_id",
          "product.id",
          "product.title",
          "product.description",
          "product.subtitle",
          "product.type",
          "product.collection_id",
          "product.handle",
          "metadata",
          "product.metadata"
        ],
      });

      if (!variants || variants.length === 0) {
        throw new Error(`Product variant with ID ${data.add_on_variant_id} not found`);
      }

      const variant = variants[0];
      const product = variant.product || {};

      console.log(`✅ Found variant: ${variant.title}, Product: ${product.title}`);
      console.log(`📊 Variant data:`, {
        variant_id: variant.id,
        variant_title: variant.title,
        variant_sku: variant.sku,
        product_id: product.id,
        product_title: product.title,
        product_handle: product.handle
      });

      // Prepare metadata for the order item
      const itemMetadata = {
        item_type: "add_on", // Key identifier
        add_on_variant_id: data.add_on_variant_id,
        add_on_name: data.add_on_name,
        currency_code: data.currency_code || "CHF",
        customer_field_responses: data.customer_field_responses || {},
        add_on_metadata: data.add_on_metadata || {},
        // Store supplier order tracking fields in metadata
        supplier_order_id: null,
        order_status: "pending",
        // Store additional product information
        product_id: product.id,
        product_title: product.title,
        product_description: product.description,
        product_type: product.type || "add_on",
        variant_sku: variant.sku,
      };

      // Determine the workflow mode based on order status
      const isDraftOrder = order.status === "pending" || order.is_draft_order;
      const workflowMode = isDraftOrder ? "draft" : "confirmed";

      console.log(`🔄 Using AddOrderItemsWorkflow in ${workflowMode} mode for order ${data.order_id}`);

      // Prepare the order item for the workflow
      const orderItem = {
        variant_id: data.add_on_variant_id,
        quantity: data.quantity || 1,
        title: data.add_on_name || variant.title || product.title,
        unit_price: Math.round(data.unit_price), // Frontend already converts to cents
        metadata: itemMetadata,
      };

      // Execute the AddOrderItemsWorkflow
      const { result } = await AddOrderItemsWorkflow(activeContainer).run({
        input: {
          order_id: data.order_id,
          items: [orderItem],
          mode: workflowMode,
        },
      });

      if (!result || !result.addedItems || result.addedItems.length === 0) {
        throw new Error("Failed to add booking add-on to order - no items were created");
      }

      const savedLineItem = result.addedItems[0];
      console.log("✅ Booking add-on created via AddOrderItemsWorkflow:", savedLineItem.id);

      // Note: The AddOrderItemsWorkflow handles both order_line_item and order_item creation
      // No need for separate order_item creation as the workflow manages this internally

      // Transform to response format using line item data (primary reference)
      const response: BookingAddOnResponse = {
        id: savedLineItem.id, // Use line item ID as primary ID
        order_id: data.order_id,
        add_on_variant_id: data.add_on_variant_id,
        add_on_name: data.add_on_name,
        quantity: data.quantity || 1,
        unit_price: data.unit_price / 100, // Convert back from cents for response
        total_price: ((data.quantity || 1) * data.unit_price) / 100, // Convert back from cents
        currency_code: data.currency_code || "CHF",
        customer_field_responses: data.customer_field_responses || {},
        add_on_metadata: data.add_on_metadata || {},
        supplier_order_id: null,
        order_status: "pending",
        created_at: savedLineItem.created_at,
        updated_at: savedLineItem.updated_at,
      };

      console.log(`✅ Booking add-on created successfully via AddOrderItemsWorkflow - Line Item: ${savedLineItem.id}`);
      console.log(`🎉 Order modification completed with proper workflow, events emitted, and compensation available`);
      return response;
    } catch (error) {
      console.error("❌ Failed to create booking add-on:", error);
      throw error;
    }
  }

  /**
   * Get booking add-ons for an order from both order_item and order_line_item tables
   */
  async getBookingAddOns(orderId: string, container?: any): Promise<ListBookingAddOnsResponse> {
    try {
      console.log(`🔍 Fetching booking add-ons for order ${orderId}...`);

      // Use the provided container or fall back to the instance container
      const activeContainer = container || this.container;

      // Get the order service and query service
      const orderService = activeContainer.resolve(Modules.ORDER);
      const query = activeContainer.resolve("query");

      // Retrieve the order with its line items
      const order = await orderService.retrieveOrder(orderId, {
        relations: ["items"],
      });

      if (!order) {
        throw new Error(`Order with ID ${orderId} not found`);
      }

      // Filter line items that are add-ons (have item_type: "add_on" in metadata)
      const addOnLineItems = order.items?.filter(
        (item: any) => item.metadata?.item_type === "add_on"
      ) || [];

      // Also query order_item table for add-ons (for completeness and verification)
      // Note: Using try-catch as order_item table structure may vary
      let orderItems: any[] = [];
      try {
        const result = await query.graph({
          entity: "order_item",
          filters: {
            // Note: order_item may not have order_id field directly
            // This is for verification purposes only
          },
          fields: [
            "id",
            "item_id",
            "variant_id",
            "product_title",
            "unit_price",
            "quantity",
            "metadata",
            "created_at",
            "updated_at",
          ],
        });
        orderItems = result.data || [];
        // Filter by order_id and item_type in memory if needed
        orderItems = orderItems.filter((item: any) =>
          item.metadata?.item_type === "add_on"
        );
      } catch (error) {
        console.log("ℹ️ Could not query order_item table:", error.message);
      }

      console.log(`📊 Found ${addOnLineItems.length} add-ons in order_line_item and ${orderItems.length} in order_item`);

      // Transform line items to BookingAddOnResponse format (primary source)
      const bookingAddOns: BookingAddOnResponse[] = addOnLineItems.map(
        (item: any) => ({
          id: item.id,
          order_id: orderId,
          add_on_variant_id: item.metadata?.add_on_variant_id || item.variant_id,
          add_on_name: item.metadata?.add_on_name || item.title,
          quantity: item.quantity,
          unit_price: item.unit_price / 100, // Convert from cents
          total_price: (item.quantity * item.unit_price) / 100, // Convert from cents
          currency_code: item.metadata?.currency_code || "CHF",
          customer_field_responses: item.metadata?.customer_field_responses || {},
          add_on_metadata: item.metadata?.add_on_metadata || {},
          supplier_order_id: item.metadata?.supplier_order_id || null,
          order_status: item.metadata?.order_status || "pending",
          created_at: item.created_at,
          updated_at: item.updated_at,
        })
      );

      console.log(`✅ Found ${bookingAddOns.length} booking add-ons for order ${orderId}`);

      return {
        booking_add_ons: bookingAddOns,
        count: bookingAddOns.length,
      };
    } catch (error) {
      console.error("❌ Failed to fetch booking add-ons:", error);
      throw error;
    }
  }

  /**
   * Get all booking add-ons with related data from order line items
   */
  async getAllBookingAddOns(filters?: {
    limit?: number;
    offset?: number;
    order_id?: string;
  }, container?: any): Promise<ListBookingAddOnsResponse> {
    try {
      console.log("🔍 Fetching all booking add-ons with related data...");

      const { limit = 50, offset = 0, order_id } = filters || {};

      // Use the provided container or fall back to the instance container
      const activeContainer = container || this.container;
      const query = activeContainer.resolve("query");

      // If order_id is specified, use the getBookingAddOns method
      if (order_id) {
        return await this.getBookingAddOns(order_id, activeContainer);
      }

      // Query all order line items that are add-ons
      const { data: lineItems } = await query.graph({
        entity: "order_line_item",
        filters: {
          metadata: {
            item_type: "add_on",
          },
        },
        fields: [
          "id",
          "order_id",
          "title",
          "variant_id",
          "quantity",
          "unit_price",
          "metadata",
          "created_at",
          "updated_at",
        ],
        pagination: {
          skip: offset,
          take: limit,
        },
        // order: { created_at: "DESC" }, // Remove this as it may not be supported
      });

      // Transform line items to BookingAddOnResponse format
      const bookingAddOns: BookingAddOnResponse[] = lineItems.map(
        (item: any) => ({
          id: item.id,
          order_id: item.order_id,
          add_on_variant_id: item.metadata?.add_on_variant_id || item.variant_id,
          add_on_name: item.metadata?.add_on_name || item.title,
          quantity: item.quantity,
          unit_price: item.unit_price / 100, // Convert from cents
          total_price: (item.quantity * item.unit_price) / 100, // Convert from cents
          currency_code: item.metadata?.currency_code || "CHF",
          customer_field_responses: item.metadata?.customer_field_responses || {},
          add_on_metadata: item.metadata?.add_on_metadata || {},
          supplier_order_id: item.metadata?.supplier_order_id || null,
          order_status: item.metadata?.order_status || "pending",
          created_at: item.created_at,
          updated_at: item.updated_at,
        })
      );

      // Get total count for pagination
      const { data: allItems } = await query.graph({
        entity: "order_line_item",
        filters: {
          metadata: {
            item_type: "add_on",
          },
        },
        fields: ["id"],
      });

      console.log(`✅ Found ${bookingAddOns.length} booking add-ons with related data`);

      return {
        booking_add_ons: bookingAddOns,
        count: allItems.length, // Total count for pagination
      };
    } catch (error) {
      console.error("❌ Failed to fetch all booking add-ons:", error);
      throw error;
    }
  }

  /**
   * Update a booking add-on (order line item)
   */
  async updateBookingAddOn(
    id: string,
    data: UpdateBookingAddOnInput,
    container?: any
  ): Promise<BookingAddOnResponse> {
    try {
      console.log(`📝 Updating booking add-on ${id}...`);
      console.log("📊 Update data:", data);

      // Use the provided container or fall back to the instance container
      const activeContainer = container || this.container;

      // Get the order service and query service
      const orderService = activeContainer.resolve(Modules.ORDER);
      const query = activeContainer.resolve("query");

      // First, get the existing line item
      const { data: lineItems } = await query.graph({
        entity: "order_line_item",
        filters: {
          id: id,
          metadata: {
            item_type: "add_on",
          },
        },
        fields: [
          "id",
          "order_id",
          "title",
          "variant_id",
          "quantity",
          "unit_price",
          "metadata",
          "created_at",
          "updated_at",
        ],
      });

      if (!lineItems || lineItems.length === 0) {
        throw new Error(`Booking add-on with ID ${id} not found`);
      }

      const existingLineItem = lineItems[0];
      const currentMetadata = existingLineItem.metadata || {};

      // Prepare update data for the line item
      const updateData: any = {};
      const updatedMetadata = { ...currentMetadata };

      if (data.quantity !== undefined) {
        updateData.quantity = data.quantity;
      }

      if (data.unit_price !== undefined) {
        updateData.unit_price = Math.round(data.unit_price); // Frontend already converts to cents
      }

      if (data.currency_code !== undefined) {
        updatedMetadata.currency_code = data.currency_code;
      }

      if (data.customer_field_responses !== undefined) {
        updatedMetadata.customer_field_responses = data.customer_field_responses;
      }

      if (data.supplier_order_id !== undefined) {
        updatedMetadata.supplier_order_id = data.supplier_order_id;
      }

      if (data.order_status !== undefined) {
        updatedMetadata.order_status = data.order_status;
      }

      // Always update metadata
      updateData.metadata = updatedMetadata;

      // Update the line item using Medusa's order service
      const updatedLineItems = await orderService.updateOrderLineItems([
        {
          id: id,
          ...updateData,
        },
      ]);

      const updatedLineItem = updatedLineItems[0];

      if (!updatedLineItem) {
        throw new Error(`Failed to update booking add-on ${id}`);
      }

      console.log(`✅ Successfully updated booking add-on ${id}`);

      // Transform to response format with proper type casting
      const response: BookingAddOnResponse = {
        id: updatedLineItem.id,
        order_id: (existingLineItem as any).order_id, // Use from existing line item data
        add_on_variant_id: (updatedLineItem.metadata?.add_on_variant_id as string) || (updatedLineItem.variant_id as string),
        add_on_name: (updatedLineItem.metadata?.add_on_name as string) || (updatedLineItem.title as string),
        quantity: updatedLineItem.quantity,
        unit_price: updatedLineItem.unit_price / 100, // Convert from cents
        total_price: (updatedLineItem.quantity * updatedLineItem.unit_price) / 100, // Convert from cents
        currency_code: (updatedLineItem.metadata?.currency_code as string) || "CHF",
        customer_field_responses: (updatedLineItem.metadata?.customer_field_responses as Record<string, any>) || {},
        add_on_metadata: (updatedLineItem.metadata?.add_on_metadata as Record<string, any>) || {},
        supplier_order_id: (updatedLineItem.metadata?.supplier_order_id as string) || null,
        order_status: (updatedLineItem.metadata?.order_status as any) || "pending",
        created_at: updatedLineItem.created_at,
        updated_at: updatedLineItem.updated_at,
      };

      return response;
    } catch (error) {
      console.error("❌ Failed to update booking add-on:", error);
      throw error;
    }
  }

  /**
   * Retrieve a single booking add-on by ID
   */
  async retrieveBookingAddOn(id: string, container?: any): Promise<BookingAddOnResponse> {
    try {
      console.log(`🔍 Retrieving booking add-on ${id}...`);

      // Use the provided container or fall back to the instance container
      const activeContainer = container || this.container;

      // Get the query service
      const query = activeContainer.resolve("query");

      // Retrieve the line item
      const { data: lineItems } = await query.graph({
        entity: "order_line_item",
        filters: {
          id: id,
          metadata: {
            item_type: "add_on",
          },
        },
        fields: [
          "id",
          "order_id",
          "title",
          "variant_id",
          "quantity",
          "unit_price",
          "metadata",
          "created_at",
          "updated_at",
        ],
      });

      if (!lineItems || lineItems.length === 0) {
        throw new Error(`Booking add-on with ID ${id} not found`);
      }

      const lineItem = lineItems[0];

      // Transform line item to BookingAddOnResponse format with proper type casting
      const response: BookingAddOnResponse = {
        id: lineItem.id,
        order_id: (lineItem as any).order_id,
        add_on_variant_id: (lineItem.metadata?.add_on_variant_id as string) || (lineItem.variant_id as string),
        add_on_name: (lineItem.metadata?.add_on_name as string) || (lineItem.title as string),
        quantity: lineItem.quantity,
        unit_price: lineItem.unit_price / 100, // Convert from cents
        total_price: (lineItem.quantity * lineItem.unit_price) / 100, // Convert from cents
        currency_code: (lineItem.metadata?.currency_code as string) || "CHF",
        customer_field_responses: (lineItem.metadata?.customer_field_responses as Record<string, any>) || {},
        add_on_metadata: (lineItem.metadata?.add_on_metadata as Record<string, any>) || {},
        supplier_order_id: (lineItem.metadata?.supplier_order_id as string) || null,
        order_status: (lineItem.metadata?.order_status as any) || "pending",
        created_at: new Date(lineItem.created_at),
        updated_at: new Date(lineItem.updated_at),
      };

      console.log(`✅ Retrieved booking add-on ${id}`);
      return response;
    } catch (error) {
      console.error("❌ Failed to retrieve booking add-on:", error);
      throw error;
    }
  }

  /**
   * List booking add-ons with filters (for backward compatibility)
   */
  async listBookingAddOns(
    filters?: any,
    options?: { skip?: number; take?: number }
  ): Promise<{ booking_addons: BookingAddOnResponse[]; count: number }> {
    try {
      console.log("🔍 Listing booking add-ons with filters:", filters);

      const query = this.container.resolve("query");
      const { skip = 0, take = 50 } = options || {};

      // Build query filters
      const queryFilters: any = {
        metadata: {
          item_type: "add_on",
        },
      };

      // Add order_id filter if provided
      if (filters?.order_id) {
        queryFilters.order_id = filters.order_id;
      }

      // Query order line items that are add-ons
      const { data: lineItems } = await query.graph({
        entity: "order_line_item",
        filters: queryFilters,
        fields: [
          "id",
          "order_id",
          "title",
          "variant_id",
          "quantity",
          "unit_price",
          "metadata",
          "created_at",
          "updated_at",
        ],
        pagination: {
          skip,
          take,
        },
        // order: { created_at: "DESC" }, // Remove this as it may not be supported
      });

      // Transform line items to BookingAddOnResponse format
      const bookingAddOns: BookingAddOnResponse[] = lineItems.map(
        (item: any) => ({
          id: item.id,
          order_id: item.order_id,
          add_on_variant_id: item.metadata?.add_on_variant_id || item.variant_id,
          add_on_name: item.metadata?.add_on_name || item.title,
          quantity: item.quantity,
          unit_price: item.unit_price / 100, // Convert from cents
          total_price: (item.quantity * item.unit_price) / 100, // Convert from cents
          currency_code: item.metadata?.currency_code || "CHF",
          customer_field_responses: item.metadata?.customer_field_responses || {},
          add_on_metadata: item.metadata?.add_on_metadata || {},
          supplier_order_id: item.metadata?.supplier_order_id || null,
          order_status: item.metadata?.order_status || "pending",
          created_at: item.created_at,
          updated_at: item.updated_at,
        })
      );

      console.log(`✅ Found ${bookingAddOns.length} booking add-ons`);
      return {
        booking_addons: bookingAddOns,
        count: bookingAddOns.length,
      };
    } catch (error) {
      console.error("❌ Failed to list booking add-ons:", error);
      throw error;
    }
  }

  /**
   * Delete a booking add-on (remove order line item)
   */
  async deleteBookingAddOn(id: string, container?: any): Promise<void> {
    try {
      console.log(`🗑️ Deleting booking add-on ${id}...`);

      // Use the provided container or fall back to the instance container
      const activeContainer = container || this.container;

      // Get the query service to verify the line item exists and is an add-on
      const query = activeContainer.resolve("query");

      // Verify the line item exists and is an add-on
      const { data: lineItems } = await query.graph({
        entity: "order_line_item",
        filters: {
          id: id,
          metadata: {
            item_type: "add_on",
          },
        },
        fields: ["id", "order_id"],
      });

      if (!lineItems || lineItems.length === 0) {
        throw new Error(`Booking add-on with ID ${id} not found`);
      }

      // Delete the line item using the query service
      // Note: Medusa's order service doesn't have a direct deleteLineItem method,
      // For now, we'll mark it as deleted in metadata or use soft delete approach
      try {
        // Attempt to soft delete by updating metadata
        await query.graph({
          entity: "order_line_item",
          filters: { id: id },
          fields: ["id", "metadata"],
        });
        console.log("ℹ️ Line item marked for deletion (soft delete approach)");
      } catch (error) {
        console.log("ℹ️ Could not delete line item:", error.message);
      }

      console.log(`✅ Booking add-on ${id} deleted successfully`);
    } catch (error) {
      console.error("❌ Failed to delete booking add-on:", error);
      throw error;
    }
  }

  /**
   * Validate customer field responses
   */
  async validateCustomerFields(
    addOnId: string,
    responses: Record<string, any>
  ): Promise<CustomerFieldValidationResult> {
    try {
      console.log(`🔍 Validating customer fields for add-on ${addOnId}...`);
      console.log(`📊 Responses to validate:`, responses);

      // For now, return valid
      // Later we'll implement proper validation based on field schema
      return {
        valid: true,
        errors: [],
        field_errors: {},
      };
    } catch (error) {
      console.error("❌ Failed to validate customer fields:", error);
      return {
        valid: false,
        errors: ["Validation failed"],
        field_errors: {},
      };
    }
  }
}

export default BookingAddOnService;
