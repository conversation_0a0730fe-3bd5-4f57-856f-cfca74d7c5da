import { Migration } from "@mikro-orm/migrations";

export class Migration20250715165200 extends Migration {
  async up(): Promise<void> {
    // Add deleted_at column if it doesn't exist
    this.addSql(
      'alter table "booking_add_ons" add column if not exists "deleted_at" timestamptz null;'
    );

    // Add created_at and updated_at columns if they don't exist (Medusa standard)
    this.addSql(
      'alter table "booking_add_ons" add column if not exists "created_at" timestamptz not null default now();'
    );
    this.addSql(
      'alter table "booking_add_ons" add column if not exists "updated_at" timestamptz not null default now();'
    );
  }

  async down(): Promise<void> {
    this.addSql(
      'alter table "booking_add_ons" drop column if exists "deleted_at";'
    );
    this.addSql(
      'alter table "booking_add_ons" drop column if exists "created_at";'
    );
    this.addSql(
      'alter table "booking_add_ons" drop column if exists "updated_at";'
    );
  }
}
