import { Migration } from "@mikro-orm/migrations";

/**
 * Migration to add supplier order tracking fields to booking_add_ons table
 *
 * This migration adds the necessary fields to track the relationship between
 * booking add-ons and supplier orders, including status synchronization.
 */
export class Migration20250716000000 extends Migration {
  async up(): Promise<void> {
    // Add supplier_order_id field with foreign key reference
    this.addSql(`
      ALTER TABLE "booking_add_ons" 
      ADD COLUMN IF NOT EXISTS "supplier_order_id" VARCHAR NULL;
    `);

    // Add order_status field to track supplier order status
    this.addSql(`
      ALTER TABLE "booking_add_ons" 
      ADD COLUMN IF NOT EXISTS "order_status" VARCHAR NULL;
    `);

    // Add constraint to ensure valid order status values
    this.addSql(`
      DO $$
      BEGIN
        IF NOT EXISTS (
          SELECT 1 FROM information_schema.table_constraints
          WHERE constraint_name = 'booking_add_ons_order_status_check'
          AND table_name = 'booking_add_ons'
        ) THEN
          ALTER TABLE "booking_add_ons"
          ADD CONSTRAINT "booking_add_ons_order_status_check"
          CHECK ("order_status" IN ('pending', 'confirmed', 'in_progress', 'completed', 'cancelled') OR "order_status" IS NULL);
        END IF;
      END $$;
    `);

    // Add foreign key constraint to supplier_order table
    this.addSql(`
      DO $$
      BEGIN
        IF NOT EXISTS (
          SELECT 1 FROM information_schema.table_constraints
          WHERE constraint_name = 'fk_booking_add_ons_supplier_order'
          AND table_name = 'booking_add_ons'
        ) THEN
          ALTER TABLE "booking_add_ons"
          ADD CONSTRAINT "fk_booking_add_ons_supplier_order"
          FOREIGN KEY ("supplier_order_id") REFERENCES "supplier_order" ("id")
          ON DELETE SET NULL ON UPDATE CASCADE;
        END IF;
      END $$;
    `);

    // Add indexes for performance
    this.addSql(`
      CREATE INDEX IF NOT EXISTS "idx_booking_add_ons_supplier_order_id" 
      ON "booking_add_ons" ("supplier_order_id");
    `);

    this.addSql(`
      CREATE INDEX IF NOT EXISTS "idx_booking_add_ons_order_status" 
      ON "booking_add_ons" ("order_status");
    `);

    // Composite index for efficient queries
    this.addSql(`
      CREATE INDEX IF NOT EXISTS "idx_booking_add_ons_supplier_order_status" 
      ON "booking_add_ons" ("supplier_order_id", "order_status");
    `);

    console.log(
      "✅ Added supplier order tracking fields to booking_add_ons table"
    );
  }

  async down(): Promise<void> {
    // Drop indexes first
    this.addSql(
      `DROP INDEX IF EXISTS "idx_booking_add_ons_supplier_order_status";`
    );
    this.addSql(`DROP INDEX IF EXISTS "idx_booking_add_ons_order_status";`);
    this.addSql(
      `DROP INDEX IF EXISTS "idx_booking_add_ons_supplier_order_id";`
    );

    // Drop foreign key constraint
    this.addSql(`
      DO $$
      BEGIN
        IF EXISTS (
          SELECT 1 FROM information_schema.table_constraints
          WHERE constraint_name = 'fk_booking_add_ons_supplier_order'
          AND table_name = 'booking_add_ons'
        ) THEN
          ALTER TABLE "booking_add_ons" DROP CONSTRAINT "fk_booking_add_ons_supplier_order";
        END IF;
      END $$;
    `);

    // Drop check constraint
    this.addSql(`
      DO $$
      BEGIN
        IF EXISTS (
          SELECT 1 FROM information_schema.table_constraints
          WHERE constraint_name = 'booking_add_ons_order_status_check'
          AND table_name = 'booking_add_ons'
        ) THEN
          ALTER TABLE "booking_add_ons" DROP CONSTRAINT "booking_add_ons_order_status_check";
        END IF;
      END $$;
    `);

    // Drop columns
    this.addSql(
      `ALTER TABLE "booking_add_ons" DROP COLUMN IF EXISTS "order_status";`
    );
    this.addSql(
      `ALTER TABLE "booking_add_ons" DROP COLUMN IF EXISTS "supplier_order_id";`
    );

    console.log(
      "✅ Removed supplier order tracking fields from booking_add_ons table"
    );
  }
}
