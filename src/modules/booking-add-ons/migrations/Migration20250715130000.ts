import { Migration } from '@mikro-orm/migrations';

export class Migration20250715130000 extends Migration {

  async up(): Promise<void> {
    // Create booking_add_ons table
    this.addSql(`
      CREATE TABLE IF NOT EXISTS "booking_add_ons" (
        "id" VARCHAR PRIMARY KEY,
        "order_id" VARCHAR NOT NULL,
        "add_on_variant_id" VARCHAR NOT NULL,
        "add_on_name" VARCHAR NOT NULL,
        "quantity" INTEGER DEFAULT 1,
        "unit_price" DECIMAL(10,2) NOT NULL,
        "total_price" DECIMAL(10,2) NOT NULL,
        "currency_code" VARCHAR(3) NOT NULL DEFAULT 'CHF',
        "customer_field_responses" JSONB,
        "add_on_metadata" JSONB,
        "created_at" TIMESTAMP DEFAULT NOW(),
        "updated_at" TIMESTAMP DEFAULT NOW()
      );
    `);

    // Add indexes for performance
    this.addSql(`
      CREATE INDEX IF NOT EXISTS "idx_booking_add_ons_order_id" 
      ON "booking_add_ons" ("order_id");
    `);

    this.addSql(`
      CREATE INDEX IF NOT EXISTS "idx_booking_add_ons_variant_id" 
      ON "booking_add_ons" ("add_on_variant_id");
    `);

    this.addSql(`
      CREATE INDEX IF NOT EXISTS "idx_booking_add_ons_created_at" 
      ON "booking_add_ons" ("created_at");
    `);

    console.log('✅ Created booking_add_ons table with indexes');
  }

  async down(): Promise<void> {
    // Drop indexes first
    this.addSql(`DROP INDEX IF EXISTS "idx_booking_add_ons_created_at";`);
    this.addSql(`DROP INDEX IF EXISTS "idx_booking_add_ons_variant_id";`);
    this.addSql(`DROP INDEX IF EXISTS "idx_booking_add_ons_order_id";`);
    
    // Drop table
    this.addSql(`DROP TABLE IF EXISTS "booking_add_ons";`);
    
    console.log('✅ Dropped booking_add_ons table and indexes');
  }
}
