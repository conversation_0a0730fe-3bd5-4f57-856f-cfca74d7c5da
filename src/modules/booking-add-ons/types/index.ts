// Input types for creating booking add-ons
export interface CreateBookingAddOnInput {
  order_id: string;
  add_on_variant_id: string;
  add_on_name: string;
  quantity?: number;
  unit_price: number;
  currency_code?: string;
  customer_field_responses?: Record<string, any>;
  add_on_metadata?: Record<string, any>;
}

// Input types for updating booking add-ons
export interface UpdateBookingAddOnInput {
  quantity?: number;
  customer_field_responses?: Record<string, any>;
  unit_price?: number;
  currency_code?: string;
  supplier_order_id?: string | null;
  order_status?:
    | "pending"
    | "confirmed"
    | "in_progress"
    | "completed"
    | "cancelled"
    | null;
}

// Response type for booking add-ons
export interface BookingAddOnResponse {
  id: string;
  order_id: string;
  add_on_variant_id: string;
  add_on_name: string;
  quantity: number;
  unit_price: number;
  total_price: number;
  currency_code: string;
  customer_field_responses?: Record<string, any>;
  add_on_metadata?: Record<string, any>;
  supplier_order_id?: string;
  order_status?:
    | "pending"
    | "confirmed"
    | "in_progress"
    | "completed"
    | "cancelled";
  created_at: Date;
  updated_at: Date;
}

// Response type for available add-ons
export interface AvailableAddOnResponse {
  id: string;
  title: string;
  description: string;
  selling_price: number;
  selling_currency: string;
  service_level: string;
  customer_fields: CustomerField[];
  metadata: Record<string, any>;
}

// Customer field structure
export interface CustomerField {
  field_id: string;
  field_name: string;
  field_type: string;
  field_key: string;
  is_required: boolean;
  display_order: number;
  options?: string[] | null;
  validation_rules?: Record<string, any> | null;
}

// Validation result for customer fields
export interface CustomerFieldValidationResult {
  valid: boolean;
  errors: string[];
  field_errors?: Record<string, string>;
}

// Filter options for available add-ons
export interface AvailableAddOnsFilters {
  hotel_id?: string;
  destination_id?: string;
  service_level?: string;
  is_active?: boolean;
}

// List response for available add-ons
export interface ListAvailableAddOnsResponse {
  add_ons: AvailableAddOnResponse[];
  count: number;
}

// List response for booking add-ons
export interface ListBookingAddOnsResponse {
  booking_add_ons: BookingAddOnResponse[];
  count: number;
}
