export const userCreatedDirect = {
  subject: "Welcome! Your account is ready",
  body: `
       <!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>Welcome - Your Account is Ready</title>
  <style>
    :root {
      --bg-color: #f9fafb;
      --container-bg: #ffffff;
      --text-primary: #1f2937;
      --text-secondary: #4b5563;
      --text-muted: #6b7280;
      --border-color: #e5e7eb;
      --border-light: #f3f4f6;
      --shadow-color: rgba(0, 0, 0, 0.05);
      --credentials-bg: #f1f5f9;
      --warning-bg: #fff7e6;
      --warning-border: #ffedc2;
      --warning-text: #8a6d3b;
      --primary-color: #2563eb;
      --primary-hover: #1e40af;
      --password-bg: #1e293b;
      --password-text: #ffffff;
    }

    @media (prefers-color-scheme: dark) {
      :root {
        --bg-color: #111827;
        --container-bg: #1f2937;
        --text-primary: #f9fafb;
        --text-secondary: #d1d5db;
        --text-muted: #9ca3af;
        --border-color: #374151;
        --border-light: #4b5563;
        --shadow-color: rgba(0, 0, 0, 0.3);
        --credentials-bg: #2d3748;
        --warning-bg: #4b3f28;
        --warning-border: #6b5b2a;
        --warning-text: #fcd34d;
        --primary-color: #3b82f6;
        --primary-hover: #2563eb;
        --password-bg: #f9fafb;
        --password-text: #1e293b;
      }
    }

    body {
      font-family: Arial, sans-serif;
      background-color: var(--bg-color);
      color: var(--text-primary);
      margin: 0;
      padding: 0;
    }

    .container {
      max-width: 600px;
      margin: 40px auto;
      padding: 24px;
      background-color: var(--container-bg);
      border: 1px solid var(--border-color);
      border-radius: 10px;
      box-shadow: 0px 6px 12px var(--shadow-color);
    }

    .logo {
      text-align: center;
      margin-bottom: 20px;
    }

    .logo img {
      max-width: 100px;
    }

    h1 {
      text-align: center;
      color: var(--text-primary);
      margin-bottom: 10px;
    }

    p {
      text-align: center;
      color: var(--text-secondary);
    }

    .btn {
      display: block;
      width: max-content;
      margin: 20px auto;
      padding: 12px 28px;
      background-color: var(--primary-color);
      color: white;
      text-decoration: none;
      font-weight: bold;
      border-radius: 6px;
      transition: background-color 0.3s;
    }

    .btn:hover {
      background-color: var(--primary-hover);
    }

    .credentials-box {
      background-color: var(--credentials-bg);
      border: 1px solid var(--border-color);
      padding: 16px;
      border-radius: 6px;
      margin-top: 20px;
      text-align: left;
    }

    .credentials-box p {
      margin: 8px 0;
      color: var(--text-primary);
    }

    .password-code {
      background-color: var(--password-bg);
      color: var(--password-text);
      padding: 6px 12px;
      border-radius: 6px;
      font-family: monospace;
      font-weight: bold;
      font-size: 14px;
      display: inline-block;
      margin-left: 10px;
    }

    .security-notice {
      background-color: var(--warning-bg);
      border: 1px solid var(--warning-border);
      color: var(--warning-text);
      padding: 15px;
      margin: 20px 0;
      border-radius: 6px;
      text-align: left;
    }

    .feature-list {
      text-align: left;
      max-width: 400px;
      margin: 0 auto;
      color: var(--text-secondary);
    }

    .feature-list p {
      margin: 6px 0;
    }

    .footer {
      font-size: 12px;
      color: var(--text-muted);
      text-align: center;
      margin-top: 30px;
    }

    .footer a {
      color: var(--primary-color);
      text-decoration: none;
    }

    .footer a:hover {
      color: var(--primary-hover);
      text-decoration: underline;
    }

    hr {
      border: none;
      border-top: 1px solid var(--border-light);
      margin: 30px 0;
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="logo">
      <img src="https://dev-assets.store.flinkk.io/powderbyrne/logo.png" alt="Brand Logo" />
    </div>

    <h1>Welcome to Our Platform!</h1>
    <p>Hello <strong>{{ user.first_name }} {{ user.last_name }}</strong>,</p>
    <p>Your account has been created successfully. Here are your login credentials:</p>

    <div class="credentials-box">
      <p><strong>📧 Login Email:</strong> {{ user.email }}</p>
      <p><strong>🔑 Temporary Password:</strong><span class="password-code">{{ tempPassword }}</span></p>
    </div>

    <p>Click the button below to log in to your account:</p>
    <a href="{{ resetLink }}" class="btn">Login to Your Account</a>

    <div class="security-notice">
      <strong>⚠️ Security Notice:</strong> This is a temporary password. You'll be prompted to change it on your first login for security reasons.
    </div>

    <p>Once you're in, you can:</p>
    <div class="feature-list">
      <p>• Access your dashboard and assigned features</p>
      <p>• Manage your account settings</p>
      <p>• Collaborate with your team</p>
    </div>

    <hr />

    <p class="footer">
      Need help? Contact our <a href="{{ frontendURL }}/contact-us" target="_blank">support team</a>.
    </p>
  </div>
</body>
</html>

    `,
};
