import { MedusaContainer } from "@camped-ai/framework/types";
import ConciergeOrderService from "../concierge-order-service";
import { ConciergeOrderStatus } from "../types";

// Mock the MedusaService
jest.mock("@camped-ai/framework/utils", () => ({
  MedusaService: jest.fn().mockImplementation((models) => {
    return class MockMedusaService {
      constructor(container: any) {
        this.container_ = container;
      }
      
      async createConciergeOrders(data: any[]) {
        return data.map((item, index) => ({
          id: `corder_${index + 1}`,
          ...item,
          created_at: new Date(),
          updated_at: new Date(),
        }));
      }
      
      async retrieveConciergeOrders(ids: string[], config?: any) {
        return ids.map(id => ({
          id,
          order_id: "order_123",
          status: ConciergeOrderStatus.NOT_STARTED,
          created_at: new Date(),
          updated_at: new Date(),
        }));
      }
      
      async listConciergeOrders(filters: any, options?: any) {
        return [
          {
            id: "corder_1",
            order_id: "order_123",
            status: ConciergeOrderStatus.NOT_STARTED,
            created_at: new Date(),
            updated_at: new Date(),
          }
        ];
      }
      
      async updateConciergeOrders(data: any[]) {
        return data;
      }
      
      async softDeleteConciergeOrders(ids: string[]) {
        return ids;
      }
    };
  }),
  MedusaError: class MedusaError extends Error {
    static Types = {
      DB_ERROR: "db_error",
      NOT_FOUND: "not_found",
    };
    
    type: string;
    
    constructor(type: string, message: string) {
      super(message);
      this.type = type;
    }
  },
}));

describe("ConciergeOrderService", () => {
  let service: ConciergeOrderService;
  let container: MedusaContainer;

  beforeEach(() => {
    container = {} as MedusaContainer;
    service = new ConciergeOrderService(container);
  });

  describe("createConciergeOrder", () => {
    it("should create a concierge order successfully", async () => {
      const input = {
        order_id: "order_123",
        assigned_to: "user_456",
        notes: "Test notes",
      };

      const result = await service.createConciergeOrder(input);

      expect(result).toBeDefined();
      expect(result.id).toBe("corder_1");
      expect(result.order_id).toBe("order_123");
      expect(result.assigned_to).toBe("user_456");
      expect(result.notes).toBe("Test notes");
      expect(result.status).toBe(ConciergeOrderStatus.NOT_STARTED);
    });

    it("should set default status when not provided", async () => {
      const input = {
        order_id: "order_123",
      };

      const result = await service.createConciergeOrder(input);

      expect(result.status).toBe(ConciergeOrderStatus.NOT_STARTED);
    });
  });

  describe("retrieveConciergeOrder", () => {
    it("should retrieve a concierge order by ID", async () => {
      const result = await service.retrieveConciergeOrder("corder_1");

      expect(result).toBeDefined();
      expect(result.id).toBe("corder_1");
      expect(result.order_id).toBe("order_123");
    });
  });

  describe("retrieveConciergeOrderByOrderId", () => {
    it("should retrieve a concierge order by order ID", async () => {
      const result = await service.retrieveConciergeOrderByOrderId("order_123");

      expect(result).toBeDefined();
      expect(result?.order_id).toBe("order_123");
    });

    it("should return null when no order found", async () => {
      // Mock empty result
      service.listConciergeOrders = jest.fn().mockResolvedValue([]);
      
      const result = await service.retrieveConciergeOrderByOrderId("nonexistent");

      expect(result).toBeNull();
    });
  });

  describe("updateConciergeOrder", () => {
    it("should update a concierge order", async () => {
      const updateData = {
        notes: "Updated notes",
        status: ConciergeOrderStatus.IN_PROGRESS,
      };

      const result = await service.updateConciergeOrder("corder_1", updateData);

      expect(result).toBeDefined();
      expect(result.id).toBe("corder_1");
    });
  });

  describe("assignConciergeOrder", () => {
    it("should assign a concierge order to a user", async () => {
      const result = await service.assignConciergeOrder("corder_1", "user_456", "admin_123");

      expect(result).toBeDefined();
      expect(result.id).toBe("corder_1");
    });
  });

  describe("updateConciergeOrderStatus", () => {
    it("should update concierge order status", async () => {
      const result = await service.updateConciergeOrderStatus(
        "corder_1", 
        ConciergeOrderStatus.COMPLETED, 
        "user_456"
      );

      expect(result).toBeDefined();
      expect(result.id).toBe("corder_1");
    });
  });

  describe("getConciergeOrdersForUser", () => {
    it("should get concierge orders for a specific user", async () => {
      const result = await service.getConciergeOrdersForUser("user_456");

      expect(result).toBeDefined();
      expect(Array.isArray(result)).toBe(true);
    });
  });

  describe("getConciergeOrdersByStatus", () => {
    it("should get concierge orders by status", async () => {
      const result = await service.getConciergeOrdersByStatus(ConciergeOrderStatus.NOT_STARTED);

      expect(result).toBeDefined();
      expect(Array.isArray(result)).toBe(true);
    });
  });

  describe("listConciergeOrdersWithPagination", () => {
    it("should list concierge orders with pagination", async () => {
      const result = await service.listConciergeOrdersWithPagination(
        { status: ConciergeOrderStatus.NOT_STARTED },
        { limit: 10, offset: 0 }
      );

      expect(result).toBeDefined();
      expect(result.concierge_orders).toBeDefined();
      expect(result.count).toBeDefined();
      expect(result.limit).toBe(10);
      expect(result.offset).toBe(0);
    });
  });

  describe("deleteConciergeOrder", () => {
    it("should soft delete a concierge order", async () => {
      await expect(service.deleteConciergeOrder("corder_1")).resolves.not.toThrow();
    });
  });
});
