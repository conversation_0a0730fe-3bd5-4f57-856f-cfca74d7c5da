import { Module } from "@camped-ai/framework/utils";
import ConciergeManagementService from "./service";
import ItineraryService from "./itinerary-service";
import repositoryLoader from "./loaders";

export const CONCIERGE_MANAGEMENT_MODULE = "conciergeManagementModuleService";
export const ITINERARY_SERVICE = "itineraryService";

export const conciergeManagementModule = Module(CONCIERGE_MANAGEMENT_MODULE, {
  service: ConciergeManagementService,
  loaders: [repositoryLoader],
});

export default conciergeManagementModule;

// Export models
export { ConciergeTask } from "./models/concierge-task";
export { Note } from "./models/note";
export { Itinerary, ItineraryDay, ItineraryEvent } from "./models/itinerary";
export { ConciergeOrder } from "./models/concierge-order";
export { ConciergeOrderItem } from "./models/concierge-order-item";

// Export services
export { default as ItineraryService } from "./itinerary-service";
export { default as ConciergeOrderService } from "./concierge-order-service";
export { default as ConciergeOrderItemService } from "./concierge-order-item-service";

// Export types
export * from "./types";
