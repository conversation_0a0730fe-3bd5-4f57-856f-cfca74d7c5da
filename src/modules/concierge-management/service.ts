import {
  MedusaService,
  MedusaError,
  Modules,
  ContainerRegistrationKeys,
} from "@camped-ai/framework/utils";
import { getPool } from "../../utils/db";
import { ConciergeTask } from "./models/concierge-task";
import { Note } from "./models/note";
import { ConciergeOrder } from "./models/concierge-order";
import { ConciergeOrderItem } from "./models/concierge-order-item";
import { SUPPLIER_PRODUCTS_SERVICES_MODULE } from "../supplier-products-services";
import {
  ConciergeManagementServiceMethods,
  ConciergeTaskResponse,
  CreateConciergeTaskRequest,
  UpdateConciergeTaskRequest,
  ListConciergeTasksRequest,
  ConciergeTaskListResponse,
  TaskStatus,
  NoteResponse,
  CreateNoteRequest,
  UpdateNoteRequest,
  ListNotesRequest,
  NoteListResponse,
  CreateConciergeOrderInput,
  UpdateConciergeOrderInput,
  ConciergeOrderResponse,
  ConciergeOrderFilters,
  ConciergeOrderListOptions,
  ConciergeOrderListResponse,
  ConciergeOrderStatus,
  CreateConciergeOrderItemInput,
  UpdateConciergeOrderItemInput,
  ConciergeOrderItemResponse,
  ConciergeOrderItemFilters,
  ConciergeOrderItemListOptions,
  ConciergeOrderItemListResponse,
  ConciergeOrderItemStatus,
} from "./types";

/**
 * Concierge Management Service
 *
 * Provides comprehensive concierge management functionality including
 * task management, assignments, and entity linking.
 */
class ConciergeManagementService
  extends MedusaService({
    ConciergeTask,
    Note,
    ConciergeOrder,
    ConciergeOrderItem,
  })
  implements ConciergeManagementServiceMethods
{
  protected container_: any;

  constructor(container: any) {
    super(container);
    this.container_ = container;
  }


  /**
   * Create category lookup map from passed category data
   */
  private createCategoryLookupMapFromData(categories: any[]): Record<string, any> {
    try {

      if (!categories || categories.length === 0) {
        return {};
      }

      // Build lookup map from passed category data
      const categoryMap: Record<string, any> = {};
      categories.forEach((category: any) => {
        categoryMap[category.id] = {
          id: category.id,
          name: category.name,
          description: category.description || null,
          icon: category.icon || null
        };
      });

      return categoryMap;
    } catch (error) {
      return {};
    }
  }


  /**
   * Pre-fetch categories and create lookup map - using supplier products services module (DEPRECATED)
   */
  private async createCategoryLookupMap(): Promise<Record<string, any>> {
    try {

      // Resolve the supplier products services module - same approach as working APIs
      const supplierProductsServicesService = this.container_.resolve(
        SUPPLIER_PRODUCTS_SERVICES_MODULE
      );

      if (!supplierProductsServicesService) {
        return {};
      }

      // Use listCategories method - same approach as working APIs
      const categories = await supplierProductsServicesService.listCategories(
        { is_active: true },
        { skip: 0, take: 100 }
      );

      if (!categories || categories.length === 0) {
        return {};
      }


      // Build lookup map from service results
      const categoryMap: Record<string, any> = {};
      categories.forEach((category: any) => {
        categoryMap[category.id] = {
          id: category.id,
          name: category.name,
          description: category.description || null,
          icon: category.icon || null,
        };
      });



      return categoryMap;
    } catch (error) {
      console.warn(
        `❌ [DEBUG] Failed to pre-fetch categories via service:`,
        error.message
      );
      return {};
    }
  }

  /**
   * Create a new concierge task
   */
  async createTask(
    data: CreateConciergeTaskRequest
  ): Promise<ConciergeTaskResponse> {
    try {
      const taskData = {
        ...data.task,
        is_deleted: false,
      };

      const tasks = await this.createConciergeTasks([taskData]);
      return tasks[0] as ConciergeTaskResponse;
    } catch (error) {
      throw new MedusaError(
        MedusaError.Types.DB_ERROR,
        `Failed to create concierge task: ${error.message}`
      );
    }
  }

  /**
   * Update an existing concierge task
   */
  async updateTask(
    id: string,
    data: UpdateConciergeTaskRequest
  ): Promise<ConciergeTaskResponse> {
    try {
      const updateData = {
        ...data.task,
      };

      const tasks = await this.updateConciergeTasks([
        {
          id,
          ...updateData,
        },
      ]);

      return tasks[0] as ConciergeTaskResponse;
    } catch (error) {
      throw new MedusaError(
        MedusaError.Types.DB_ERROR,
        `Failed to update concierge task: ${error.message}`
      );
    }
  }

  /**
   * Retrieve a single concierge task
   */
  async retrieveTask(id: string): Promise<ConciergeTaskResponse> {
    try {
      const task = await this.retrieveConciergeTask(id);

      if (!task) {
        throw new MedusaError(
          MedusaError.Types.NOT_FOUND,
          `Task with id ${id} not found`
        );
      }

      return task as ConciergeTaskResponse;
    } catch (error) {
      if (error instanceof MedusaError) {
        throw error;
      }
      throw new MedusaError(
        MedusaError.Types.DB_ERROR,
        `Failed to retrieve concierge task: ${error.message}`
      );
    }
  }

  /**
   * List concierge tasks with filtering and pagination
   */
  async listTasks(
    request: ListConciergeTasksRequest
  ): Promise<ConciergeTaskListResponse> {
    try {
      const { filters = {}, options = {} } = request;
      const {
        limit = 20,
        offset = 0,
        order = { created_at: "DESC" },
      } = options;

      // Build filters - exclude soft deleted by default
      const queryFilters = {
        ...filters,
        is_deleted:
          filters.is_deleted !== undefined ? filters.is_deleted : false,
      };

      const tasks = await this.listConciergeTasks(queryFilters, {
        skip: offset,
        take: limit,
        order,
      });

      // Get count separately
      const allTasks = await this.listConciergeTasks(queryFilters);
      const count = Array.isArray(allTasks) ? allTasks.length : 0;

      return {
        tasks: tasks as ConciergeTaskResponse[],
        count,
        limit,
        offset,
      };
    } catch (error) {
      throw new MedusaError(
        MedusaError.Types.DB_ERROR,
        `Failed to list concierge tasks: ${error.message}`
      );
    }
  }

  /**
   * Hard delete a concierge task
   */
  async deleteTask(id: string): Promise<void> {
    try {
      await this.deleteConciergeTasks([id]);
    } catch (error) {
      throw new MedusaError(
        MedusaError.Types.DB_ERROR,
        `Failed to delete concierge task: ${error.message}`
      );
    }
  }

  /**
   * Soft delete a concierge task
   */
  async softDeleteTask(
    id: string,
    deletedBy?: string
  ): Promise<ConciergeTaskResponse> {
    try {
      const updateData = {
        is_deleted: true,
        deleted_at: new Date(),
        ...(deletedBy && { updated_by: deletedBy }),
      };

      const tasks = await this.updateConciergeTasks([
        {
          id,
          ...updateData,
        },
      ]);

      return tasks[0] as ConciergeTaskResponse;
    } catch (error) {
      throw new MedusaError(
        MedusaError.Types.DB_ERROR,
        `Failed to soft delete concierge task: ${error.message}`
      );
    }
  }

  /**
   * Assign a task to a user
   */
  async assignTask(
    taskId: string,
    userId: string,
    assignedBy?: string
  ): Promise<ConciergeTaskResponse> {
    return await this.updateTask(taskId, {
      task: {
        assigned_to: userId,
        status: TaskStatus.IN_PROGRESS,
        ...(assignedBy && { updated_by: assignedBy }),
      },
    });
  }

  /**
   * Unassign a task from a user
   */
  async unassignTask(
    taskId: string,
    unassignedBy?: string
  ): Promise<ConciergeTaskResponse> {
    return await this.updateTask(taskId, {
      task: {
        assigned_to: undefined,
        status: TaskStatus.PENDING,
        ...(unassignedBy && { updated_by: unassignedBy }),
      },
    });
  }

  /**
   * Mark a task as completed
   */
  async completeTask(
    taskId: string,
    completedBy?: string
  ): Promise<ConciergeTaskResponse> {
    return await this.updateTask(taskId, {
      task: {
        status: TaskStatus.COMPLETED,
        ...(completedBy && { updated_by: completedBy }),
      },
    });
  }

  /**
   * Cancel a task
   */
  async cancelTask(
    taskId: string,
    cancelledBy?: string
  ): Promise<ConciergeTaskResponse> {
    return await this.updateTask(taskId, {
      task: {
        status: TaskStatus.CANCELLED,
        ...(cancelledBy && { updated_by: cancelledBy }),
      },
    });
  }

  /**
   * Get all tasks for a specific entity
   */
  async getTasksForEntity(
    entityType: string,
    entityId: string
  ): Promise<ConciergeTaskResponse[]> {
    try {
      const result = await this.listTasks({
        filters: {
          entity_type: entityType,
          entity_id: entityId,
          is_deleted: false,
        },
        options: {
          order: { created_at: "DESC" },
          limit: 100,
        },
      });

      return result.tasks;
    } catch (error) {
      throw new MedusaError(
        MedusaError.Types.DB_ERROR,
        `Failed to get tasks for entity: ${error.message}`
      );
    }
  }

  /**
   * Get all tasks assigned to a specific user
   */
  async getTasksForUser(userId: string): Promise<ConciergeTaskResponse[]> {
    try {
      const result = await this.listTasks({
        filters: {
          assigned_to: userId,
          is_deleted: false,
        },
        options: {
          order: { due_date: "ASC", priority: "DESC" },
          limit: 100,
        },
      });

      return result.tasks;
    } catch (error) {
      throw new MedusaError(
        MedusaError.Types.DB_ERROR,
        `Failed to get tasks for user: ${error.message}`
      );
    }
  }

  // ===== NOTE MANAGEMENT METHODS =====

  /**
   * Create a new note
   */
  async createNote(data: CreateNoteRequest): Promise<NoteResponse> {
    try {
      const noteData = {
        ...data.note,
        deleted: false,
      };

      const notes = await this.createNotes([noteData]);
      return notes[0] as NoteResponse;
    } catch (error) {
      throw new MedusaError(
        MedusaError.Types.DB_ERROR,
        `Failed to create note: ${error.message}`
      );
    }
  }

  /**
   * Update an existing note
   */
  async updateNote(id: string, data: UpdateNoteRequest): Promise<NoteResponse> {
    try {
      const updateData = {
        ...data.note,
      };

      const notes = await this.updateNotes([
        {
          id,
          ...updateData,
        },
      ]);

      return notes[0] as NoteResponse;
    } catch (error) {
      throw new MedusaError(
        MedusaError.Types.DB_ERROR,
        `Failed to update note: ${error.message}`
      );
    }
  }

  /**
   * Retrieve a single note by ID
   */
  async getNote(id: string): Promise<NoteResponse> {
    try {
      const [notes] = await this.listAndCountNotes({ id });
      const note = notes[0];

      if (!note || note.deleted) {
        throw new MedusaError(
          MedusaError.Types.NOT_FOUND,
          `Note with id ${id} not found`
        );
      }

      return note as NoteResponse;
    } catch (error) {
      if (error instanceof MedusaError) {
        throw error;
      }

      throw new MedusaError(
        MedusaError.Types.DB_ERROR,
        `Failed to retrieve note: ${error.message}`
      );
    }
  }

  /**
   * List notes with filtering and pagination
   */
  async getNotes(request: ListNotesRequest): Promise<NoteListResponse> {
    try {
      const { filters = {}, options = {} } = request;
      const {
        limit = 20,
        offset = 0,
        order = { created_at: "DESC" },
      } = options;

      // Build filters - exclude soft deleted by default
      const queryFilters = {
        ...filters,
        deleted: filters.deleted !== undefined ? filters.deleted : false,
      };

      const [notes, count] = await this.listAndCountNotes(queryFilters, {
        skip: offset,
        take: limit,
        order,
      });

      return {
        notes: notes as NoteResponse[],
        count,
        limit,
        offset,
      };
    } catch (error) {
      throw new MedusaError(
        MedusaError.Types.DB_ERROR,
        `Failed to list notes: ${error.message}`
      );
    }
  }

  /**
   * Hard delete a note
   */
  async deleteNote(id: string): Promise<void> {
    try {
      await this.deleteNotes([id]);
    } catch (error) {
      throw new MedusaError(
        MedusaError.Types.DB_ERROR,
        `Failed to delete note: ${error.message}`
      );
    }
  }

  /**
   * Soft delete a note
   */
  async softDeleteNote(id: string, deletedBy?: string): Promise<NoteResponse> {
    return await this.updateNote(id, {
      note: {
        deleted: true,
        ...(deletedBy && { updated_by_id: deletedBy }),
      },
    });
  }

  /**
   * Get all notes for a specific entity
   */
  async getNotesForEntity(
    entity: string,
    entityId: string
  ): Promise<NoteResponse[]> {
    try {
      const result = await this.getNotes({
        filters: {
          entity,
          entity_id: entityId,
          deleted: false,
        },
        options: {
          order: { created_at: "DESC" },
          limit: 100,
        },
      });

      return result.notes;
    } catch (error) {
      throw new MedusaError(
        MedusaError.Types.DB_ERROR,
        `Failed to get notes for entity: ${error.message}`
      );
    }
  }

  // ===== CONCIERGE ORDER METHODS =====

  /**
   * Create a new concierge order
   */
  async createConciergeOrder(
    data: CreateConciergeOrderInput
  ): Promise<ConciergeOrderResponse> {
    try {
      const orderData = {
        ...data,
        status: data.status || ConciergeOrderStatus.NOT_STARTED,
      };

      const orders = await this.createConciergeOrders([orderData]);
      return orders[0] as ConciergeOrderResponse;
    } catch (error) {
      throw new MedusaError(
        MedusaError.Types.DB_ERROR,
        `Failed to create concierge order: ${error.message}`
      );
    }
  }

  /**
   * Retrieve a concierge order by ID
   */
  async retrieveConciergeOrder(
    id: string,
    config?: any
  ): Promise<ConciergeOrderResponse> {
    try {
      const orders = await this.listConciergeOrders(
        { id },
        { take: 1, ...config }
      );
      if (!orders || orders.length === 0) {
        throw new MedusaError(
          MedusaError.Types.NOT_FOUND,
          `Concierge order with id: ${id} was not found`
        );
      }
      return orders[0] as ConciergeOrderResponse;
    } catch (error) {
      if (error instanceof MedusaError) {
        throw error;
      }
      throw new MedusaError(
        MedusaError.Types.DB_ERROR,
        `Failed to retrieve concierge order: ${error.message}`
      );
    }
  }

  /**
   * Retrieve a concierge order by order ID
   */
  async retrieveConciergeOrderByOrderId(
    orderId: string,
    config?: any
  ): Promise<ConciergeOrderResponse | null> {
    try {
      const orders = await this.listConciergeOrders(
        { order_id: orderId },
        { take: 1, ...config }
      );
      return orders.length > 0 ? orders[0] : null;
    } catch (error) {
      throw new MedusaError(
        MedusaError.Types.DB_ERROR,
        `Failed to retrieve concierge order by order ID: ${error.message}`
      );
    }
  }

  /**
   * Update a concierge order
   */
  async updateConciergeOrder(
    id: string,
    data: UpdateConciergeOrderInput
  ): Promise<ConciergeOrderResponse> {
    try {
      const updateData = {
        ...data,
        ...(data.last_contacted_at && {
          last_contacted_at: new Date(data.last_contacted_at),
        }),
      };

      await this.updateConciergeOrders([{ id, ...updateData }]);
      return await this.retrieveConciergeOrder(id);
    } catch (error) {
      throw new MedusaError(
        MedusaError.Types.DB_ERROR,
        `Failed to update concierge order: ${error.message}`
      );
    }
  }

  /**
   * List concierge orders with filters and pagination
   */
  async listConciergeOrdersWithPagination(
    filters: ConciergeOrderFilters = {},
    options: ConciergeOrderListOptions = {}
  ): Promise<ConciergeOrderListResponse> {
    try {
      const { limit = 20, offset = 0, ...listOptions } = options;

      const orders = await this.listConciergeOrders(filters, {
        take: limit,
        skip: offset,
        ...listOptions,
      });

      // Get total count for pagination
      const totalCount = await this.listConciergeOrders(filters, {
        take: undefined,
        skip: undefined,
      });

      return {
        concierge_orders: orders,
        count: totalCount.length,
        limit,
        offset,
      };
    } catch (error) {
      throw new MedusaError(
        MedusaError.Types.DB_ERROR,
        `Failed to list concierge orders: ${error.message}`
      );
    }
  }

  /**
   * List concierge orders with combined order data (from both concierge_orders and orders tables)
   */
  async listConciergeOrdersWithOrderData(
    filters: ConciergeOrderFilters = {},
    options: ConciergeOrderListOptions = {}
  ): Promise<any> {
    try {
      const { limit = 20, offset = 0, ...listOptions } = options;

      // Extract search parameter if present
      const searchQuery = (filters as any).search;
      const cleanFilters = { ...filters };
      delete (cleanFilters as any).search;

      // Get concierge orders first
      const conciergeOrders = await this.listConciergeOrders(cleanFilters, {
        take: limit,
        skip: offset,
        ...listOptions,
      });

      // Get total count for pagination
      const totalCount = await this.listConciergeOrders(cleanFilters, {
        take: undefined,
        skip: undefined,
      });

      // If no concierge orders found, return empty result
      if (!conciergeOrders || conciergeOrders.length === 0) {
        return {
          concierge_orders: [],
          count: 0,
          limit,
          offset,
        };
      }

      // Extract order IDs from concierge orders
      const orderIds = conciergeOrders
        .map((co: any) => co.order_id)
        .filter((id: string) => id);

      // Get order data using the order module
      let ordersData: any[] = [];
      if (orderIds.length > 0) {
        try {

          // Resolve the order service using the correct container and module reference
          const orderService = this.container_.resolve(Modules.ORDER);

          // Fetch orders with related data
          const orderPromises = orderIds.map(async (orderId: string) => {
            try {
              const order = await orderService.retrieveOrder(orderId, {
                relations: [
                  "customer",
                  "items",
                  "shipping_address",
                  "billing_address",
                  "payments",
                  "fulfillments",
                ],
              });

              return order;
            } catch (error) {
              console.warn(
                `[ConciergeService] Failed to fetch order ${orderId}:`,
                error.message
              );
              return null;
            }
          });

          const orderResults = await Promise.all(orderPromises);
          ordersData = orderResults.filter((order) => order !== null);
        } catch (error) {
          console.warn(
            "[ConciergeService] Failed to fetch order data:",
            error.message
          );
          console.warn("[ConciergeService] Error details:", error);
          // Continue without order data if there's an error
        }
      }

      // Combine concierge orders with order data
      let combinedOrders = conciergeOrders.map((conciergeOrder: any) => {
        const orderData = ordersData.find(
          (order) => order.id === conciergeOrder.order_id
        );

        return {
          ...conciergeOrder,
          order: orderData || null,
        };
      });

      // Apply search filter if provided
      if (searchQuery && searchQuery.trim()) {
        const searchTerm = searchQuery.toLowerCase().trim();
        combinedOrders = combinedOrders.filter((item: any) => {
          // Search in concierge order fields
          const conciergeMatch =
            (item.notes && item.notes.toLowerCase().includes(searchTerm)) ||
            (item.assigned_to &&
              item.assigned_to.toLowerCase().includes(searchTerm)) ||
            (item.status && item.status.toLowerCase().includes(searchTerm));

          // Search in order fields if order data is available
          const orderMatch =
            item.order &&
            ((item.order.email &&
              item.order.email.toLowerCase().includes(searchTerm)) ||
              (item.order.display_id &&
                item.order.display_id.toString().includes(searchTerm)) ||
              (item.order.customer &&
                item.order.customer.first_name &&
                item.order.customer.first_name
                  .toLowerCase()
                  .includes(searchTerm)) ||
              (item.order.customer &&
                item.order.customer.last_name &&
                item.order.customer.last_name
                  .toLowerCase()
                  .includes(searchTerm)) ||
              (item.order.customer &&
                item.order.customer.email &&
                item.order.customer.email.toLowerCase().includes(searchTerm)));

          return conciergeMatch || orderMatch;
        });
      }

      return {
        concierge_orders: combinedOrders,
        count: searchQuery ? combinedOrders.length : totalCount.length,
        limit,
        offset,
      };
    } catch (error) {
      throw new MedusaError(
        MedusaError.Types.DB_ERROR,
        `Failed to list concierge orders with order data: ${error.message}`
      );
    }
  }

  /**
   * Assign a concierge order to a user
   */
  async assignConciergeOrder(
    id: string,
    assignedTo: string,
    assignedBy?: string
  ): Promise<ConciergeOrderResponse> {
    try {
      return await this.updateConciergeOrder(id, {
        assigned_to: assignedTo,
        status: ConciergeOrderStatus.IN_PROGRESS,
        metadata: {
          assigned_by: assignedBy,
          assigned_at: new Date().toISOString(),
        },
      });
    } catch (error) {
      throw new MedusaError(
        MedusaError.Types.DB_ERROR,
        `Failed to assign concierge order: ${error.message}`
      );
    }
  }

  /**
   * Update concierge order status
   */
  async updateConciergeOrderStatus(
    id: string,
    status: ConciergeOrderStatus,
    updatedBy?: string
  ): Promise<ConciergeOrderResponse> {
    try {
      return await this.updateConciergeOrder(id, {
        status,
        metadata: {
          status_updated_by: updatedBy,
          status_updated_at: new Date().toISOString(),
        },
      });
    } catch (error) {
      throw new MedusaError(
        MedusaError.Types.DB_ERROR,
        `Failed to update concierge order status: ${error.message}`
      );
    }
  }

  /**
   * Delete a concierge order (soft delete)
   */
  async deleteConciergeOrder(id: string): Promise<void> {
    try {
      await this.softDeleteConciergeOrders([id]);
    } catch (error) {
      throw new MedusaError(
        MedusaError.Types.DB_ERROR,
        `Failed to delete concierge order: ${error.message}`
      );
    }
  }

  // ===== CONCIERGE ORDER ITEM METHODS =====

  /**
   * Create a new concierge order item
   */
  async createConciergeOrderItem(
    data: CreateConciergeOrderItemInput
  ): Promise<ConciergeOrderItemResponse> {
    try {
      const itemData = {
        ...data,
        status: data.status || ConciergeOrderItemStatus.UNDER_REVIEW,
        added_at: new Date(),
        is_active: data.is_active !== undefined ? data.is_active : true,
      };

      const items = await this.createConciergeOrderItems([itemData]);
      return items[0] as ConciergeOrderItemResponse;
    } catch (error) {
      throw new MedusaError(
        MedusaError.Types.DB_ERROR,
        `Failed to create concierge order item: ${error.message}`
      );
    }
  }

  /**
   * Retrieve a concierge order item by ID
   */
  async retrieveConciergeOrderItem(
    id: string,
    config?: any
  ): Promise<ConciergeOrderItemResponse> {
    try {
      const items = await this.listConciergeOrderItems(
        { id },
        { take: 1, ...config }
      );
      if (!items || items.length === 0) {
        throw new MedusaError(
          MedusaError.Types.NOT_FOUND,
          `Concierge order item with id: ${id} was not found`
        );
      }
      return items[0] as ConciergeOrderItemResponse;
    } catch (error) {
      if (error instanceof MedusaError) {
        throw error;
      }
      throw new MedusaError(
        MedusaError.Types.DB_ERROR,
        `Failed to retrieve concierge order item: ${error.message}`
      );
    }
  }

  /**
   * Update a concierge order item
   */
  async updateConciergeOrderItem(
    id: string,
    data: UpdateConciergeOrderItemInput
  ): Promise<ConciergeOrderItemResponse> {
    try {
      const updateData = {
        ...data,
        ...(data.finalized_at && { finalized_at: new Date(data.finalized_at) }),
      };

      await this.updateConciergeOrderItems([{ id, ...updateData }]);
      return await this.retrieveConciergeOrderItem(id);
    } catch (error) {
      throw new MedusaError(
        MedusaError.Types.DB_ERROR,
        `Failed to update concierge order item: ${error.message}`
      );
    }
  }

  /**
   * List concierge order items with filters and pagination
   */
  async listConciergeOrderItemsWithPagination(
    filters: ConciergeOrderItemFilters = {},
    options: ConciergeOrderItemListOptions = {}
  ): Promise<ConciergeOrderItemListResponse> {
    try {
      const { limit = 20, offset = 0, ...listOptions } = options;

      const items = await this.listConciergeOrderItems(filters, {
        take: limit,
        skip: offset,
        ...listOptions,
      });

      // Get total count for pagination
      const totalCount = await this.listConciergeOrderItems(filters, {
        take: undefined,
        skip: undefined,
      });

      return {
        concierge_order_items: items,
        count: totalCount.length,
        limit,
        offset,
      };
    } catch (error) {
      throw new MedusaError(
        MedusaError.Types.DB_ERROR,
        `Failed to list concierge order items: ${error.message}`
      );
    }
  }

  /**
   * List concierge order items with enhanced relationship data for supplier management
   *
   * Enhanced Database Schema and Column Mapping:
   * 1. request_id: concierge_order_item.id
   * 2. hotel: concierge_order.hotel_id → hotel.name
   * 3. category: concierge_order_item.category_id → product_service_category.name
   * 4. title: concierge_order_item.title
   * 5. quantity: concierge_order_item.quantity
   * 6. unit_price: concierge_order_item.unit_price
   * 7. total_price: quantity * unit_price
   * 8. booking: concierge_order_item.concierge_order_id
   * 9. customer: concierge_order.order_id → order.customer_id → customer.first_name + ' ' + customer.last_name
   * 10. check_in_date: concierge_order.check_in_date
   * 11. check_out_date: concierge_order.check_out_date
   */
  async listConciergeOrderItemsWithRelationships(
    filters: ConciergeOrderItemFilters = {},
    options: ConciergeOrderItemListOptions = {},
    categories: any[] = []
  ): Promise<any> {
    try {
      const { limit = 20, offset = 0, ...listOptions } = options;

      // Extract search parameter and handle it separately
      const searchQuery = (filters as any).search;
      const cleanFilters = { ...filters };
      delete (cleanFilters as any).search;

      // If search is provided and looks like an item ID, filter by ID directly
      if (searchQuery && searchQuery.startsWith('citem_')) {
        cleanFilters.id = searchQuery;
      }

      // Ensure search parameter is completely removed from filters
      delete (cleanFilters as any).search;

      // Get concierge order items first
      const finalFilters = { ...cleanFilters };
      delete (finalFilters as any).search;

      const items = await this.listConciergeOrderItems(finalFilters, {
        take: limit,
        skip: offset,
        ...listOptions,
      });

      // Get total count for pagination using cleaned filters
      const totalCount = await this.listConciergeOrderItems(finalFilters, {
        take: undefined,
        skip: undefined,
      });

      if (!items || items.length === 0) {
        return {
          concierge_order_items: [],
          count: 0,
          limit,
          offset,
        };
      }

      // Create category lookup map from passed categories
      const categoryLookupMap =
        this.createCategoryLookupMapFromData(categories);

      // Get services for fetching enhanced relationship data using proper Medusa patterns
      let orderService: any;
      let productCategoryService: any;
      let supplierProductsServicesService: any;

      try {
        orderService = this.container_["orderService"];
      } catch (error) {
        console.warn("Could not resolve order service:", error.message);
      }

      try {
        productCategoryService = this.container_["productCategoryService"];
      } catch (error) {
        console.warn(
          "Could not resolve product category service:",
          error.message
        );
      }

      try {
        supplierProductsServicesService =
          this.container_["supplierProductsServicesService"];
      } catch (error) {
        console.warn(
          "Could not resolve supplier products services service:",
          error.message
        );
      }

      // Get enhanced relationship data for each item
      const enhancedItems = await Promise.all(
        items.map(async (item: any) => {
          try {

            // Get concierge order data with enhanced relationships
            const conciergeOrder = await this.retrieveConciergeOrder(
              item.concierge_order_id
            );


            // Initialize enhanced result object with required schema mapping
            const result: any = {
              // 1. request_id: concierge_order_item.id
              request_id: item.id,

              // Core item data
              ...item,

              // 4. title: concierge_order_item.title
              title: item.title,

              // 5. quantity: concierge_order_item.quantity
              quantity: item.quantity,

              // 6. unit_price: concierge_order_item.unit_price
              unit_price: item.unit_price,

              // 7. total_price: Calculate as quantity * unit_price
              total_price: item.quantity * item.unit_price,

              // 8. booking: concierge_order_item.concierge_order_id
              booking: item.concierge_order_id,

              // Format dates for display
              requested_date: item.created_at,
            };

            // Get category data if category_id exists - using pre-fetched lookup map
            if (item.category_id) {
              try {


                if (categoryLookupMap && categoryLookupMap[item.category_id]) {
                  const categoryData = categoryLookupMap[item.category_id];
                  result.category = {
                    id: categoryData.id,
                    name: categoryData.name,
                    description: categoryData.description || null,
                    icon: categoryData.icon || null,
                  };

                } else {

                }
              } catch (categoryError) {
                console.warn(
                  `❌ [DEBUG] Category lookup failed for item ${item.id}:`,
                  categoryError.message
                );
              }
            }



            // Add enhanced concierge order data with hotel and date information
            if (conciergeOrder) {
              result.concierge_order = {
                id: conciergeOrder.id,
                order_id: conciergeOrder.order_id,
                assigned_to: conciergeOrder.assigned_to,
                notes: conciergeOrder.notes,
                status: conciergeOrder.status,
                last_contacted_at: conciergeOrder.last_contacted_at,
                created_at: conciergeOrder.created_at,
                updated_at: conciergeOrder.updated_at,
                metadata: conciergeOrder.metadata,
              };

              // 2. hotel: concierge_order.hotel_id → hotel.name
              if (conciergeOrder.hotel_id) {
                try {
                  // First, try to get hotel name from item metadata (most reliable)
                  let hotelNameFromMetadata = null;
                  if (item.metadata?.original_line_item_metadata?.hotel_name) {
                    hotelNameFromMetadata = item.metadata.original_line_item_metadata.hotel_name;
                  }

                  if (hotelNameFromMetadata) {
                    result.hotel = hotelNameFromMetadata;
                  } else {
                    // Try to resolve hotel name using direct database query
                    try {
                      // Use direct database query as a reliable fallback
                      const pool = getPool();
                      const queryResult = await pool.query(
                        'SELECT id, name FROM hotel WHERE id = $1 AND deleted_at IS NULL',
                        [conciergeOrder.hotel_id]
                      );

                      if (queryResult.rows && queryResult.rows.length > 0) {
                        const hotel = queryResult.rows[0];
                        result.hotel = hotel.name;
                      } else {
                        result.hotel = conciergeOrder.hotel_id;
                      }
                    } catch (error) {
                      result.hotel = conciergeOrder.hotel_id;
                    }
                  }
                } catch (hotelError) {
                  result.hotel = conciergeOrder.hotel_id;
                }
              }

              // 10. check_in_date: concierge_order.check_in_date
              result.check_in_date = conciergeOrder.check_in_date;

              // 11. check_out_date: concierge_order.check_out_date
              result.check_out_date = conciergeOrder.check_out_date;

              // Legacy travel dates support
              if (conciergeOrder.metadata) {
                const metadata = conciergeOrder.metadata;
                result.travel_dates = {
                  check_in_date:
                    conciergeOrder.check_in_date ||
                    metadata.check_in_date ||
                    null,
                  check_out_date:
                    conciergeOrder.check_out_date ||
                    metadata.check_out_date ||
                    null,
                };
              }
            }

            // Get enhanced order and customer data if available
            if (conciergeOrder?.order_id && orderService) {
              try {


                // Fetch order data with customer relationships using order service
                const orderStartTime = Date.now();


                const order = await orderService.retrieve(
                  conciergeOrder.order_id,
                  {
                    relations: [
                      "customer",
                      "items",
                      "items.variant",
                      "items.variant.product",
                      "items.variant.product.categories",
                    ],
                  }
                );

                const orderQueryTime = Date.now() - orderStartTime;

                if (order) {
                  result.order = {
                    id: order.id,
                    display_id: order.display_id,
                    email: order.email,
                    currency_code: order.currency_code,
                    total: order.total,
                    created_at: order.created_at,
                    updated_at: order.updated_at,
                  };

                  // 9. customer: concierge_order.order_id → order.customer_id → customer.first_name + ' ' + customer.last_name
                  if (order.customer) {
                    const firstName = order.customer.first_name || "";
                    const lastName = order.customer.last_name || "";
                    const fullName = `${firstName} ${lastName}`.trim();

                    // Set customer name for backward compatibility
                    result.customer =
                      fullName ||
                      order.customer.email ||
                      `Customer ${order.customer.id}`;

                    // Add full customer object for detailed access
                    result.customer_details = {
                      id: order.customer.id,
                      first_name: order.customer.first_name,
                      last_name: order.customer.last_name,
                      email: order.customer.email,
                      phone: order.customer.phone,
                      full_name: fullName || order.customer.email || `Customer ${order.customer.id}`,
                    };

                  } else if (order.customer_id) {
                    // If customer object is not loaded but customer_id exists, try to fetch customer directly
                    try {
                      const customerService =
                        this.container_.resolve("customerService");
                      const customer = await customerService.retrieve(
                        order.customer_id
                      );
                      if (customer) {
                        const firstName = customer.first_name || "";
                        const lastName = customer.last_name || "";
                        const fullName = `${firstName} ${lastName}`.trim();
                        result.customer =
                          fullName ||
                          customer.email ||
                          `Customer ${customer.id}`;

                        // Add full customer object for detailed access
                        result.customer_details = {
                          id: customer.id,
                          first_name: customer.first_name,
                          last_name: customer.last_name,
                          email: customer.email,
                          phone: customer.phone,
                          full_name: fullName || customer.email || `Customer ${customer.id}`,
                        };


                      } else {
                        result.customer =
                          order.email || `Customer ${order.customer_id}`;

                      }
                    } catch (customerError) {
                      console.warn(
                        `Failed to fetch customer ${order.customer_id}:`,
                        customerError.message
                      );
                      result.customer =
                        order.email || `Customer ${order.customer_id}`;

                    }
                  } else {
                    result.customer = order.email || "Unknown Customer";

                  }

                  // Find matching line item if line_item_id exists
                  if (item.line_item_id && order.items) {


                    const lineItem = order.items.find(
                      (li: any) => li.id === item.line_item_id
                    );


                    if (lineItem) {
                      result.order_line_item = {
                        id: lineItem.id,
                        unit_price: lineItem.unit_price,
                        quantity: lineItem.quantity,
                        title: lineItem.title,
                        variant_id: lineItem.variant_id,
                        product_id: lineItem.variant?.product_id,
                        metadata: lineItem.metadata,
                      };

                      // Order item is the same as line item in this context
                      result.order_item = {
                        id: lineItem.id,
                        quantity: lineItem.quantity,
                        unit_price: lineItem.unit_price,
                        title: lineItem.title,
                        variant_id: lineItem.variant_id,
                        product_id: lineItem.variant?.product_id,
                        metadata: lineItem.metadata,
                      };

                      // Extract hotel name from line item metadata if available
                      if (lineItem.metadata?.original_line_item_metadata?.hotel_name) {
                        result.hotel = lineItem.metadata.original_line_item_metadata.hotel_name;
                      }

                      // Extract product and category data from the line item variant
                      if (lineItem.variant?.product) {
                        const product = lineItem.variant.product;


                        result.product = {
                          id: product.id,
                          title: product.title,
                          description: product.description,
                          metadata: product.metadata,
                        };

                        // Extract category information - store as string for consistency
                        if (
                          product.categories &&
                          product.categories.length > 0
                        ) {
                          // Use the first category as primary category
                          const primaryCategory = product.categories[0];


                          // Only set category if not already set by direct lookup
                          if (!result.category) {
                            result.category = primaryCategory.name;

                          }
                        } else {

                        }
                      } else {

                      }
                    }
                  }
                }
              } catch (orderError) {
                console.warn(
                  `Failed to fetch order data for ${conciergeOrder.order_id}:`,
                  orderError.message
                );
              }
            }

            // Extract customer information from concierge order metadata if customer name not found
            if (!result.customer && conciergeOrder?.metadata?.customer_email) {
              const customerEmail = conciergeOrder.metadata.customer_email;

              try {
                // Try to fetch customer details by email to get first_name and last_name
                const queryService = this.container_.resolve(ContainerRegistrationKeys.QUERY);
                const customerResult = await queryService.graph({
                  entity: "customer",
                  filters: { email: customerEmail },
                  fields: ["id", "first_name", "last_name", "email"],
                });

                if (customerResult.data && customerResult.data.length > 0) {
                  const customer = customerResult.data[0];
                  if (customer.first_name || customer.last_name) {
                    result.customer = `${customer.first_name || ""} ${customer.last_name || ""}`.trim();
                  } else {
                    // Fallback to email if no name available
                    result.customer = customerEmail;
                  }
                } else {
                  // Fallback to email if customer not found
                  result.customer = customerEmail;
                }
              } catch (customerError) {
                console.warn(`Failed to fetch customer by email ${customerEmail}:`, customerError.message);
                // Fallback to email if there's an error
                result.customer = customerEmail;
              }
            }

            // 3. category: Enhanced category lookup using concierge_order_item.category_id → product_service_category.name
            if (item.category_id) {
              try {


                // Try multiple approaches for category lookup
                let categoryFound = false;

                // Approach 1: Direct category lookup using productCategoryService
                if (productCategoryService && !categoryFound) {
                  try {
                    const category =
                      await productCategoryService.retrieveProductCategory(
                        item.category_id
                      );
                    if (category) {
                      result.category = category.name;
                      categoryFound = true;

                    }
                  } catch (err) {

                  }
                }

                // Approach 2: Try using supplierProductsServicesService to get category
                if (supplierProductsServicesService && !categoryFound) {
                  try {
                    const categoryData =
                      await supplierProductsServicesService.retrieveProductCategory(
                        item.category_id
                      );
                    if (categoryData) {
                      result.category = categoryData.name;
                      categoryFound = true;

                    }
                  } catch (err) {

                  }
                }

                if (!categoryFound) {

                }
              } catch (directCategoryError) {
                console.warn(
                  `Failed to fetch direct category data for category_id ${item.category_id}:`,
                  directCategoryError.message
                );
              }
            }

            // Enhanced category lookup using product_variant_id from product_service table (fallback)
            if (
              !result.category &&
              item.variant_id &&
              supplierProductsServicesService
            ) {
              try {


                // Find product_service record that has this variant_id as product_variant_id
                const productServices =
                  await supplierProductsServicesService.listProductServices({
                    product_variant_id: item.variant_id,
                  });

                if (productServices && productServices.length > 0) {
                  const productService = productServices[0];


                  // Get the product service with category relationship
                  const productServiceWithCategory =
                    await supplierProductsServicesService.retrieveProductService(
                      productService.id,
                      { relations: ["category"] }
                    );

                  if (productServiceWithCategory?.category) {
                    result.category = productServiceWithCategory.category.name;

                  }
                }
              } catch (enhancedCategoryError) {
                console.warn(
                  `Failed to fetch enhanced category data for variant_id ${item.variant_id}:`,
                  enhancedCategoryError.message
                );
              }
            }

            // Extract hotel name from concierge order item metadata if available
            if (item.metadata?.original_line_item_metadata?.hotel_name) {
              result.hotel = item.metadata.original_line_item_metadata.hotel_name;
            }

            return result;
          } catch (error) {
            console.warn(
              `❌ [DEBUG] Failed to enhance item ${item.id}:`,
              error.message
            );
            const fallbackResult = {
              ...item,
              total_price: item.quantity * item.unit_price,
              requested_date: item.created_at,
            };
            return fallbackResult;
          }
        })
      );


      const finalResult = {
        concierge_order_items: enhancedItems,
        count: totalCount.length,
        limit,
        offset,
      };

      return finalResult;
    } catch (error) {
      throw new MedusaError(
        MedusaError.Types.DB_ERROR,
        `Failed to list concierge order items with relationships: ${error.message}`
      );
    }
  }

  /**
   * Update concierge order item status
   */
  async updateConciergeOrderItemStatus(
    id: string,
    status: ConciergeOrderItemStatus,
    updatedBy?: string
  ): Promise<ConciergeOrderItemResponse> {
    try {
      const updateData: UpdateConciergeOrderItemInput = {
        status,
        metadata: {
          status_updated_by: updatedBy,
          status_updated_at: new Date().toISOString(),
        },
      };

      // If completing, set finalized_at and finalized_by
      if (status === ConciergeOrderItemStatus.COMPLETED) {
        updateData.finalized_at = new Date().toISOString();
        updateData.finalized_by = updatedBy;
      }

      return await this.updateConciergeOrderItem(id, updateData);
    } catch (error) {
      throw new MedusaError(
        MedusaError.Types.DB_ERROR,
        `Failed to update concierge order item status: ${error.message}`
      );
    }
  }

  /**
   * Deactivate a concierge order item (soft removal)
   */
  async deactivateConciergeOrderItem(
    id: string,
    deactivatedBy?: string
  ): Promise<ConciergeOrderItemResponse> {
    try {
      return await this.updateConciergeOrderItem(id, {
        is_active: false,
        metadata: {
          deactivated_by: deactivatedBy,
          deactivated_at: new Date().toISOString(),
        },
      });
    } catch (error) {
      throw new MedusaError(
        MedusaError.Types.DB_ERROR,
        `Failed to deactivate concierge order item: ${error.message}`
      );
    }
  }

  /**
   * Delete a concierge order item (soft delete)
   */
  async deleteConciergeOrderItem(id: string): Promise<void> {
    try {
      await this.softDeleteConciergeOrderItems([id]);
    } catch (error) {
      throw new MedusaError(
        MedusaError.Types.DB_ERROR,
        `Failed to delete concierge order item: ${error.message}`
      );
    }
  }
}

export default ConciergeManagementService;
