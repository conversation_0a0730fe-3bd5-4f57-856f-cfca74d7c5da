import { MedusaService } from "@camped-ai/framework/utils";
import { MedusaContainer } from "@camped-ai/framework/types";
import { EntityManager } from "typeorm";
import {
  Itinerary,
  ItineraryDay,
  ItineraryEvent,
  ItineraryEventMedia,
} from "./models/itinerary";



/**
 * Service for managing itineraries, days, and events
 */
class ItineraryService extends MedusaService({
  Itinerary,
  ItineraryDay,
  ItineraryEvent,
  ItineraryEventMedia,
}) {
  protected container_: MedusaContainer;
  protected activeManager_: EntityManager;

  constructor(container: MedusaContainer) {
    super(container);
    this.container_ = container;
    this.activeManager_ = this.container_["manager" as any];
  }

  /**
   * Create an itinerary from a booking
   */
  async createItineraryFromBooking(
    bookingId: string,
    bookingStartDate: Date,
    createdBy?: string
  ) {
    // Check if itinerary already exists for this booking
    const existingItinerary = await this.listAllItineraries({
      booking_id: bookingId,
    });

    if (existingItinerary.length > 0) {
      throw new Error("Itinerary already exists for this booking");
    }

    // Create the itinerary
    const itinerary = await this.createItineraries({
      booking_id: bookingId,
      title: null,
      status: "DRAFT",
      created_by: createdBy,
    });

    // Create the first day using booking start date
    const firstDay = await this.createItineraryDays({
      itinerary_id: itinerary.id,
      date: bookingStartDate,
      title: null,
      sort_order: 1,
    });

    return {
      itinerary,
      firstDay,
    };
  }

  /**
   * Get full itinerary with days and events
   */
  async getItineraryWithDaysAndEvents(itineraryId: string) {
    try {
      const itinerary = await this.retrieveItinerary(itineraryId);

      if (!itinerary) {
        throw new Error("Itinerary not found");
      }

      const days = await this.listItineraryDays(
        { itinerary_id: itineraryId },
        { order: { sort_order: "ASC" } }
      );

      // Ensure days is always an array
      const daysList = Array.isArray(days) ? days : [];

      console.log({daysList})

      const daysWithEvents = await Promise.all(
        daysList.map(async (day) => {
          try {
            const events = await this.listItineraryEvents(
              { day_id: day.id },
            );

            console.log({events})

            return {
              ...day,
              events: events,
            };
          } catch (error) {
            console.error(`Error fetching events for day ${day.id}:`, error);
            return {
              ...day,
              events: [],
            };
          }
        })
      );

      return {
        ...itinerary,
        days: daysWithEvents,
      };
    } catch (error) {
      console.error("Error in getItineraryWithDaysAndEvents:", error);
      throw error;
    }
  }

  /**
   * Add a new day to an itinerary
   */
  async addDayToItinerary(
    itineraryId: string,
    date: Date,
    title?: string
  ) {
    // Check if date already exists
    const existingDay = await this.listItineraryDays({
      itinerary_id: itineraryId,
      date: date,
    });

    if (existingDay.length > 0) {
      throw new Error("A day with this date already exists");
    }

    // Get the next sort order
    const existingDays = await this.listItineraryDays(
      { itinerary_id: itineraryId },
      { order: { sort_order: "DESC" }, take: 1 }
    );

    const nextSortOrder = existingDays.length > 0 ? existingDays[0].sort_order + 1 : 1;

    return await this.createItineraryDays({
      itinerary_id: itineraryId,
      date: date,
      title: title,
      sort_order: nextSortOrder,
    });
  }

  /**
   * Reorder days in an itinerary
   */
  async reorderDays(dayUpdates: Array<{ id: string; sort_order: number }>) {
    const updatePromises = dayUpdates.map(({ id, sort_order }) =>
      this.updateItineraryDays({ id, sort_order })
    );

    return await Promise.all(updatePromises);
  }

  /**
   * Add an event to a day
   */
  async addEventToDay(dayId: string, eventData: {
    category: string;
    type?: string;
    title: string;
    notes?: string;
    start_time?: string;
    end_time?: string;
    duration?: string;
    timezone?: string;
    details?: Record<string, any>;
    price?: number;
    currency?: string;
    media?: string[]; // Array of media URLs
    // New fields for supplier offering integration
    supplier_offering_id?: string;
    category_id?: string;
    supplier_id?: string;
    from_date?: string;
    to_date?: string;
  }) {
    console.log('Creating event with data:', eventData);

    // Extract media URLs from eventData
    const mediaUrls = eventData.media || [];

    // Extract supplier offering fields
    const {
      media,
      supplier_offering_id,
      category_id,
      supplier_id,
      from_date,
      to_date,
      ...eventDataWithoutSpecialFields
    } = eventData;

    // Determine event source
    const event_source = supplier_offering_id ? "supplier_offering" : "manual";

    // Parse dates if provided
    const fromDateParsed = from_date ? new Date(from_date) : undefined;
    const toDateParsed = to_date ? new Date(to_date) : undefined;

    // Prepare details object with supplier offering information (for backward compatibility)
    const details = {
      ...eventData.details,
      ...(supplier_offering_id && { supplier_offering_id }),
      ...(category_id && { category_id }),
      ...(supplier_id && { supplier_id }),
      ...(from_date && { from_date }),
      ...(to_date && { to_date }),
    };

    const createData = {
      day_id: dayId,
      ...eventDataWithoutSpecialFields,
      details: Object.keys(details).length > 0 ? details : undefined,
      category: eventData.category as any,
      // New dedicated fields
      supplier_offering_id,
      supplier_id,
      category_id,
      from_date: fromDateParsed,
      to_date: toDateParsed,
      event_source: event_source as any,
    };

    console.log('🔧 [SERVICE] Creating event with data:', JSON.stringify(createData, null, 2));

    try {
      const event = await this.createItineraryEvents(createData);
      console.log('✅ [SERVICE] Event created successfully:', JSON.stringify(event, null, 2));

      // Create media records if any
      if (mediaUrls.length > 0) {
        console.log('📸 [SERVICE] Creating media records for event:', event.id);
        await this.addMediaToEvent(event.id, mediaUrls);
      }

      console.log('🎉 [SERVICE] Event creation completed, returning event');
      return event;
    } catch (error) {
      console.error('❌ [SERVICE] Error in createItineraryEvents:', error);
      console.error('❌ [SERVICE] Error stack:', error instanceof Error ? error.stack : 'No stack trace');
      throw error;
    }
  }

  /**
   * Add media to an event
   */
  async addMediaToEvent(eventId: string, mediaUrls: string[]) {
    const mediaPromises = mediaUrls.map((url, index) =>
      this.createItineraryEventMedias({
        event_id: eventId,
        url: url,
        type: this.getMediaTypeFromUrl(url),
        sort_order: index,
      })
    );

    return await Promise.all(mediaPromises);
  }

  /**
   * Get media type from URL
   */
  private getMediaTypeFromUrl(url: string): "image" | "video" | "document" {
    const extension = url.split('.').pop()?.toLowerCase();

    if (['jpg', 'jpeg', 'png', 'gif', 'webp', 'svg'].includes(extension || '')) {
      return 'image';
    } else if (['mp4', 'avi', 'mov', 'wmv', 'flv', 'webm'].includes(extension || '')) {
      return 'video';
    } else {
      return 'document';
    }
  }

  /**
   * Update an event
   */
  async updateEvent(eventId: string, eventData: Partial<{
    category: string;
    type: string;
    title: string;
    notes: string;
    start_time: string;
    end_time: string;
    duration: string;
    timezone: string;
    details: Record<string, any>;
    price: number;
    currency: string;
    media: string[];
  }>) {
    // Extract media from eventData
    const { media, ...eventDataWithoutMedia } = eventData;

    // Update the event
    const updateData = {
      id: eventId,
      ...eventDataWithoutMedia,
      category: eventData.category as any,
    };

    const updatedEvent = await this.updateItineraryEvents(updateData);

    // Update media if provided
    if (media !== undefined) {
      // Delete existing media
      const existingMedia = await this.listItineraryEventMedias({ event_id: eventId });
      if (existingMedia.length > 0) {
        const mediaIds = existingMedia.map(m => m.id);
        await this.deleteItineraryEventMedias(mediaIds);
      }

      // Add new media
      if (media.length > 0) {
        await this.addMediaToEvent(eventId, media);
      }
    }

    return updatedEvent;
  }

  /**
   * Delete an event and its media
   */
  async deleteEvent(eventId: string) {
    // Delete associated media first
    const existingMedia = await this.listItineraryEventMedias({ event_id: eventId });
    if (existingMedia.length > 0) {
      const mediaIds = existingMedia.map(m => m.id);
      await this.deleteItineraryEventMedias(mediaIds);
    }

    // Delete the event
    return await this.deleteItineraryEvents(eventId);
  }

  /**
   * Delete a day and all its events
   */
  async deleteDay(dayId: string) {
    // First delete all events for this day
    const events = await this.listItineraryEvents({ day_id: dayId });
    
    if (events.length > 0) {
      const eventIds = events.map(event => event.id);
      await this.deleteItineraryEvents(eventIds);
    }

    // Then delete the day
    return await this.deleteItineraryDays(dayId);
  }

  /**
   * Delete an entire itinerary and all its associated data
   */
  async deleteItinerary(itineraryId: string) {
    // First get all days for this itinerary
    const days = await this.listItineraryDays({ itinerary_id: itineraryId });

    // Delete all days (which will cascade delete events and media)
    if (days.length > 0) {
      for (const day of days) {
        await this.deleteDay(day.id);
      }
    }

    // Finally delete the itinerary itself
    return await this.deleteItineraries(itineraryId);
  }





  /**
   * Get events for a specific day with media
   */
  async getEventsForDay(dayId: string) {
    const events = await this.listItineraryEvents(
      { day_id: dayId },
      { order: { start_time: "ASC" } }
    );

    // Add media to each event
    const eventsWithMedia = await Promise.all(
      events.map(async (event) => {
        const media = await this.listItineraryEventMedias(
          { event_id: event.id },
          { order: { sort_order: "ASC" } }
        );
        return {
          ...event,
          media: media || [],
        };
      })
    );

    return eventsWithMedia;
  }

  /**
   * Update itinerary status
   */
  async updateItineraryStatus(itineraryId: string, status: "DRAFT" | "FINALIZED") {
    return await this.updateItineraries({ id: itineraryId, status });
  }

  /**
   * List itineraries with filtering
   */
  async listAllItineraries(filters: any = {}) {
    try {
      console.log("listAllItineraries called with filters:", filters);

      // Use the repository directly to get itineraries
      // Use the list method to get itineraries
      const itineraries = await this.listItineraries(filters, {
        order: { created_at: "DESC" },
      });

      // For each itinerary, fetch its days and events
      const itinerariesWithDetails = await Promise.all(
        itineraries.map(async (itinerary) => {
          try {
            const days = await this.listItineraryDays(
              { itinerary_id: itinerary.id },
              { order: { sort_order: "ASC" } }
            );

            const daysList = Array.isArray(days) ? days : [];

            const daysWithEvents = await Promise.all(
              daysList.map(async (day) => {
                try {
                  const events = await this.listItineraryEvents(
                    { day_id: day.id },
                    { order: { start_time: "ASC" } }
                  );
                  return {
                    ...day,
                    events: Array.isArray(events) ? events : [],
                  };
                } catch (error) {
                  console.error(`Error fetching events for day ${day.id}:`, error);
                  return {
                    ...day,
                    events: [],
                  };
                }
              })
            );

            return {
              ...itinerary,
              days: daysWithEvents,
            };
          } catch (error) {
            console.error(`Error fetching days for itinerary ${itinerary.id}:`, error);
            return {
              ...itinerary,
              days: [],
            };
          }
        })
      );

      console.log("Found itineraries:", itinerariesWithDetails?.length || 0);
      return itinerariesWithDetails;
    } catch (error) {
      console.error("Error in listAllItineraries:", error);
      throw error;
    }
  }

  /**
   * Get itinerary by booking ID
   */
  async getItineraryByBookingId(bookingId: string) {
    const itineraries = await this.listAllItineraries({ booking_id: bookingId });
    return itineraries.length > 0 ? itineraries[0] : null;
  }
}

export default ItineraryService;
