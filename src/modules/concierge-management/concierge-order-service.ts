import { MedusaService, MedusaError } from "@camped-ai/framework/utils";
import { MedusaContainer } from "@camped-ai/framework/types";
import { EntityManager } from "typeorm";
import { ConciergeOrder } from "./models/concierge-order";
import { ConciergeOrderItem } from "./models/concierge-order-item";
import {
  CreateConciergeOrderInput,
  UpdateConciergeOrderInput,
  ConciergeOrderResponse,
  ConciergeOrderFilters,
  ConciergeOrderListOptions,
  ConciergeOrderListResponse,
  ConciergeOrderStatus,
} from "./types";

/**
 * Concierge Order Service
 *
 * Provides comprehensive concierge order management functionality including
 * order creation, status management, and assignment handling.
 */
class ConciergeOrderService extends MedusaService({
  ConciergeOrder,
  ConciergeOrderItem,
}) {
  protected container_: MedusaContainer;
  protected activeManager_: EntityManager;

  constructor(container: MedusaContainer) {
    super(container);
    this.container_ = container;
    this.activeManager_ = this.container_["manager" as any];
  }

  /**
   * Create a new concierge order
   */
  async createConciergeOrder(data: CreateConciergeOrderInput): Promise<ConciergeOrderResponse> {
    try {
      const orderData = {
        ...data,
        status: data.status || ConciergeOrderStatus.NOT_STARTED,
      };

      const orders = await this.createConciergeOrders([orderData]);
      return orders[0] as ConciergeOrderResponse;
    } catch (error) {
      throw new MedusaError(
        MedusaError.Types.DB_ERROR,
        `Failed to create concierge order: ${error.message}`
      );
    }
  }

  /**
   * Retrieve a concierge order by ID
   */
  async retrieveConciergeOrder(id: string, config?: any): Promise<ConciergeOrderResponse> {
    try {
      const order = await this.retrieveConciergeOrders([id], config);
      if (!order || order.length === 0) {
        throw new MedusaError(
          MedusaError.Types.NOT_FOUND,
          `Concierge order with id: ${id} was not found`
        );
      }
      return order[0] as ConciergeOrderResponse;
    } catch (error) {
      if (error instanceof MedusaError) {
        throw error;
      }
      throw new MedusaError(
        MedusaError.Types.DB_ERROR,
        `Failed to retrieve concierge order: ${error.message}`
      );
    }
  }

  /**
   * Retrieve a concierge order by order ID
   */
  async retrieveConciergeOrderByOrderId(orderId: string, config?: any): Promise<ConciergeOrderResponse | null> {
    try {
      const orders = await this.listConciergeOrders(
        { order_id: orderId },
        { take: 1, ...config }
      );
      return orders.length > 0 ? orders[0] : null;
    } catch (error) {
      throw new MedusaError(
        MedusaError.Types.DB_ERROR,
        `Failed to retrieve concierge order by order ID: ${error.message}`
      );
    }
  }

  /**
   * Update a concierge order
   */
  async updateConciergeOrder(id: string, data: UpdateConciergeOrderInput): Promise<ConciergeOrderResponse> {
    try {
      const updateData = {
        ...data,
        ...(data.last_contacted_at && { last_contacted_at: new Date(data.last_contacted_at) }),
      };

      await this.updateConciergeOrders([{ id, ...updateData }]);
      return await this.retrieveConciergeOrder(id);
    } catch (error) {
      throw new MedusaError(
        MedusaError.Types.DB_ERROR,
        `Failed to update concierge order: ${error.message}`
      );
    }
  }

  /**
   * List concierge orders with filters and pagination
   */
  async listConciergeOrdersWithPagination(
    filters: ConciergeOrderFilters = {},
    options: ConciergeOrderListOptions = {}
  ): Promise<ConciergeOrderListResponse> {
    try {
      const { limit = 20, offset = 0, ...listOptions } = options;
      
      const orders = await this.listConciergeOrders(filters, {
        take: limit,
        skip: offset,
        ...listOptions,
      });

      // Get total count for pagination
      const totalCount = await this.listConciergeOrders(filters, { take: undefined, skip: undefined });

      return {
        concierge_orders: orders,
        count: totalCount.length,
        limit,
        offset,
      };
    } catch (error) {
      throw new MedusaError(
        MedusaError.Types.DB_ERROR,
        `Failed to list concierge orders: ${error.message}`
      );
    }
  }

  /**
   * Assign a concierge order to a user
   */
  async assignConciergeOrder(id: string, assignedTo: string, assignedBy?: string): Promise<ConciergeOrderResponse> {
    try {
      return await this.updateConciergeOrder(id, {
        assigned_to: assignedTo,
        status: ConciergeOrderStatus.IN_PROGRESS,
        metadata: {
          assigned_by: assignedBy,
          assigned_at: new Date().toISOString(),
        },
      });
    } catch (error) {
      throw new MedusaError(
        MedusaError.Types.DB_ERROR,
        `Failed to assign concierge order: ${error.message}`
      );
    }
  }

  /**
   * Update concierge order status
   */
  async updateConciergeOrderStatus(id: string, status: ConciergeOrderStatus, updatedBy?: string): Promise<ConciergeOrderResponse> {
    try {
      return await this.updateConciergeOrder(id, {
        status,
        metadata: {
          status_updated_by: updatedBy,
          status_updated_at: new Date().toISOString(),
        },
      });
    } catch (error) {
      throw new MedusaError(
        MedusaError.Types.DB_ERROR,
        `Failed to update concierge order status: ${error.message}`
      );
    }
  }

  /**
   * Get concierge orders for a specific user
   */
  async getConciergeOrdersForUser(userId: string): Promise<ConciergeOrderResponse[]> {
    try {
      return await this.listConciergeOrders({ assigned_to: userId });
    } catch (error) {
      throw new MedusaError(
        MedusaError.Types.DB_ERROR,
        `Failed to get concierge orders for user: ${error.message}`
      );
    }
  }

  /**
   * Get concierge orders by status
   */
  async getConciergeOrdersByStatus(status: ConciergeOrderStatus): Promise<ConciergeOrderResponse[]> {
    try {
      return await this.listConciergeOrders({ status });
    } catch (error) {
      throw new MedusaError(
        MedusaError.Types.DB_ERROR,
        `Failed to get concierge orders by status: ${error.message}`
      );
    }
  }

  /**
   * Delete a concierge order (soft delete)
   */
  async deleteConciergeOrder(id: string): Promise<void> {
    try {
      await this.softDeleteConciergeOrders([id]);
    } catch (error) {
      throw new MedusaError(
        MedusaError.Types.DB_ERROR,
        `Failed to delete concierge order: ${error.message}`
      );
    }
  }
}

export default ConciergeOrderService;
