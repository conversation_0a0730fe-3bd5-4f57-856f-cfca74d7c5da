import { model } from "@camped-ai/framework/utils";

/**
 * Note Model
 *
 * Represents notes in the concierge management system.
 * Notes can be linked to various entities like bookings or deals.
 */
export const Note = model.define("note", {
  id: model.id({ prefix: "note" }).primaryKey(),
  title: model.text(),
  content: model.text().nullable(),

  // Entity linking
  entity: model.text(), // e.g., "booking", "deal", "guest"
  entity_id: model.text(), // ID of the linked entity

  // Soft delete support
  deleted: model.boolean().default(false),

  // User tracking
  created_by_id: model.text().nullable(), // User ID who created the note
  updated_by_id: model.text().nullable(), // User ID who last updated the note
})
.indexes([
  {
    name: "IDX_note_entity",
    on: ["entity", "entity_id"],
    where: "deleted = false",
  },
  {
    name: "IDX_note_created_at",
    on: ["created_at"],
  },
  {
    name: "IDX_note_deleted",
    on: ["deleted"],
  },
]);

export default Note;
