import { model } from "@camped-ai/framework/utils";

/**
 * Concierge Task Model
 *
 * Represents tasks in the concierge management system.
 * Tasks can be assigned to users and linked to various entities like bookings.
 */
export const ConciergeTask = model.define("concierge_task", {
  id: model.id({ prefix: "ctask" }).primaryKey(),
  title: model.text(),
  description: model.text().nullable(),

  // Status and Priority
  status: model.enum([
    "pending",
    "in_progress",
    "review",
    "completed",
    "cancelled"
  ]).default("pending"),

  priority: model.enum([
    "low",
    "medium",
    "high",
    "urgent"
  ]).default("medium"),

  // Entity linking
  entity_type: model.text().nullable(), // e.g., "booking", "deal", "guest"
  entity_id: model.text().nullable(), // ID of the linked entity

  // Assignment and timing
  assigned_to: model.text().nullable(), // User ID
  created_by: model.text().nullable(), // User ID who created the task
  updated_by: model.text().nullable(), // User ID who last updated the task
  due_date: model.dateTime().nullable(),

  // Soft delete support
  is_deleted: model.boolean().default(false),

  // Additional metadata
  metadata: model.json().nullable(),
});

export default ConciergeTask;
