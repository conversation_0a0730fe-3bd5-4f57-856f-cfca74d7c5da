import { model } from "@camped-ai/framework/utils";

/**
 * Itinerary Model
 * 
 * Represents the main itinerary associated with a booking.
 * One booking can have one itinerary.
 */
export const Itinerary = model.define("itinerary", {
  id: model.id({ prefix: "itin" }).primaryKey(),
  booking_id: model.text().index(), // Reference to booking
  title: model.text().nullable(),
  status: model.enum([
    "DRAFT",
    "FINALIZED"
  ]).default("DRAFT"),
  created_by: model.text().nullable(), // User ID
})
.indexes([
  {
    name: "IDX_itinerary_booking_id",
    on: ["booking_id"],
    unique: true, // One itinerary per booking
    where: "deleted_at IS NULL",
  },
]);

/**
 * Itinerary Day Model
 * 
 * Represents individual days within an itinerary.
 * Each day can contain multiple events.
 */
export const ItineraryDay = model.define("itinerary_day", {
  id: model.id({ prefix: "itin_day" }).primaryKey(),
  itinerary_id: model.text().index(), // Reference to itinerary
  date: model.dateTime(),
  title: model.text().nullable(), // e.g., "Welcome to Los Angeles"
  sort_order: model.number().default(0),
})
.indexes([
  {
    name: "IDX_itinerary_day_itinerary_id",
    on: ["itinerary_id"],
    where: "deleted_at IS NULL",
  },
  {
    name: "IDX_itinerary_day_date_unique",
    on: ["itinerary_id", "date"],
    unique: true, // Unique date per itinerary
    where: "deleted_at IS NULL",
  },
  {
    name: "IDX_itinerary_day_sort_order",
    on: ["itinerary_id", "sort_order"],
    where: "deleted_at IS NULL",
  },
]);

/**
 * Itinerary Event Media Model
 *
 * Represents media files associated with an itinerary event.
 */
export const ItineraryEventMedia = model.define("itinerary_event_media", {
  id: model.id({ prefix: "itin_media" }).primaryKey(),
  event_id: model.text().index(), // Reference to itinerary event
  url: model.text(), // Media URL
  type: model.enum(["image", "video", "document"]).default("image"),
  title: model.text().nullable(), // Optional title/caption
  description: model.text().nullable(), // Optional description
  metadata: model.json().nullable(), // Additional metadata (size, dimensions, etc.)
  sort_order: model.number().default(0), // For ordering media items
})
.indexes([
  {
    name: "IDX_itinerary_event_media_event_id",
    on: ["event_id"],
    where: "deleted_at IS NULL",
  },
  {
    name: "IDX_itinerary_event_media_sort_order",
    on: ["event_id", "sort_order"],
    where: "deleted_at IS NULL",
  },
]);

/**
 * Itinerary Event Model
 *
 * Represents individual events within a day.
 * Supports both manual events and API-based events from supplier offerings.
 */
export const ItineraryEvent = model.define("itinerary_event", {
  id: model.id({ prefix: "itin_event" }).primaryKey(),
  day_id: model.text().index(), // Reference to itinerary day
  category: model.text(), // Dynamic category name (no longer restricted to enum)
  type: model.text().nullable(), // e.g., "Departure", "Check-in", "Food/Drink"
  title: model.text(),
  notes: model.text().nullable(),
  start_time: model.text().nullable(), // Time as string (HH:MM)
  end_time: model.text().nullable(),
  duration: model.text().nullable(), // Duration as string
  timezone: model.text().nullable(),
  details: model.json().nullable(), // Flexible schema for category-specific data
  price: model.number().nullable(), // Price in smallest currency unit
  currency: model.text().nullable(),

  // New fields for supplier offering integration
  supplier_offering_id: model.text().nullable(), // Reference to supplier offering
  supplier_id: model.text().nullable(), // Reference to supplier
  category_id: model.text().nullable(), // Reference to product service category
  from_date: model.dateTime().nullable(), // Start date for the event
  to_date: model.dateTime().nullable(), // End date for the event
  event_source: model.enum(["manual", "supplier_offering"]).default("manual"), // Source of the event

  // Removed: media, attachments, people arrays - using separate models instead
})
.indexes([
  {
    name: "IDX_itinerary_event_day_id",
    on: ["day_id"],
    where: "deleted_at IS NULL",
  },
  {
    name: "IDX_itinerary_event_category",
    on: ["category"],
    where: "deleted_at IS NULL",
  },
  {
    name: "IDX_itinerary_event_start_time",
    on: ["day_id", "start_time"],
    where: "deleted_at IS NULL",
  },
  {
    name: "IDX_itinerary_event_supplier_offering_id",
    on: ["supplier_offering_id"],
    where: "deleted_at IS NULL AND supplier_offering_id IS NOT NULL",
  },
  {
    name: "IDX_itinerary_event_supplier_id",
    on: ["supplier_id"],
    where: "deleted_at IS NULL AND supplier_id IS NOT NULL",
  },
  {
    name: "IDX_itinerary_event_category_id",
    on: ["category_id"],
    where: "deleted_at IS NULL AND category_id IS NOT NULL",
  },
  {
    name: "IDX_itinerary_event_event_source",
    on: ["event_source"],
    where: "deleted_at IS NULL",
  },
  {
    name: "IDX_itinerary_event_date_range",
    on: ["from_date", "to_date"],
    where: "deleted_at IS NULL AND from_date IS NOT NULL AND to_date IS NOT NULL",
  },
]);

export default Itinerary;
