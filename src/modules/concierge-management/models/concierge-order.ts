import { model } from "@camped-ai/framework/utils";
import { ConciergeOrderItem } from "./concierge-order-item";

/**
 * Concierge Order Model
 *
 * Represents orders in the concierge management system that extend regular Medusa orders.
 * These orders require concierge intervention for approvals, notes, and specialized handling.
 */
export const ConciergeOrder = model.define("concierge_order", {
  id: model.id({ prefix: "corder" }).primaryKey(),

  // Reference to the main Medusa order
  order_id: model.text().index(),

  // Hotel and booking information
  hotel_id: model.text().nullable(), // Reference to the hotel
  check_in_date: model.dateTime().nullable(), // Check-in date for the booking
  check_out_date: model.dateTime().nullable(), // Check-out date for the booking

  // Assignment and management
  assigned_to: model.text().nullable(), // User ID of assigned concierge
  notes: model.text().nullable(), // Internal concierge notes
  
  // Status tracking
  status: model.enum([
    "not_started",
    "in_progress", 
    "waiting_customer",
    "ready_to_finalize",
    "completed"
  ]).default("not_started"),
  
  // Communication tracking
  last_contacted_at: model.dateTime().nullable(),

  // Metadata for additional information
  metadata: model.json().nullable(),
  
  // Relationships
  concierge_order_items: model.hasMany(() => ConciergeOrderItem, {
    mappedBy: "concierge_order",
  }),
})
.indexes([
  {
    name: "IDX_concierge_order_order_id",
    on: ["order_id"],
    unique: true, // One concierge order per regular order
    where: "deleted_at IS NULL",
  },
  {
    name: "IDX_concierge_order_assigned_to",
    on: ["assigned_to"],
    unique: false,
    where: "deleted_at IS NULL",
  },
  {
    name: "IDX_concierge_order_status",
    on: ["status"],
    unique: false,
    where: "deleted_at IS NULL",
  },
  {
    name: "IDX_concierge_order_hotel_id",
    on: ["hotel_id"],
    unique: false,
    where: "deleted_at IS NULL",
  },
  {
    name: "IDX_concierge_order_check_in_date",
    on: ["check_in_date"],
    unique: false,
    where: "deleted_at IS NULL",
  },
]);

export default ConciergeOrder;
