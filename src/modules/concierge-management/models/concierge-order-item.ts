import { model } from "@camped-ai/framework/utils";
import { ConciergeOrder } from "./concierge-order";

/**
 * Concierge Order Item Model
 *
 * Represents individual items within a concierge order that require special handling.
 * These can be linked to existing line items or represent new items added by the concierge.
 */
export const ConciergeOrderItem = model.define("concierge_order_item", {
  id: model.id({ prefix: "citem" }).primaryKey(),
  
  // Relationship to concierge order
  concierge_order: model.belongsTo(() => ConciergeOrder, {
    foreignKey: "concierge_order_id",
  }),
  
  // Optional reference to existing line item
  line_item_id: model.text().nullable(),

  // Optional reference to existing order item
  item_id: model.text().nullable(),

  // Product/variant information
  variant_id: model.text().nullable(),
  
  // Item details
  quantity: model.number(),
  unit_price: model.number(), // Price in smallest currency unit
  title: model.text(),

  // Category relationship
  category_id: model.text().nullable(),

  // Date fields for service period
  start_date: model.dateTime().nullable(),
  end_date: model.dateTime().nullable(),
  
  // Status tracking
  status: model.enum([
    "under_review",
    "client_confirmed",
    "order_placed",
    "cancelled",
    "completed"
  ]).default("under_review"),

  // Notes for this specific item
  notes: model.text().nullable(),

  // Activity tracking
  is_active: model.boolean().default(true),
  added_by: model.text().nullable(), // User ID who added this item
  finalized_by: model.text().nullable(), // User ID who finalized this item
  added_at: model.dateTime().default(new Date()),
  finalized_at: model.dateTime().nullable(),

  // Metadata for additional information
  metadata: model.json().nullable(),
})
.indexes([
  {
    name: "IDX_concierge_order_item_concierge_order_id",
    on: ["concierge_order_id"],
    unique: false,
    where: "deleted_at IS NULL",
  },
  {
    name: "IDX_concierge_order_item_line_item_id",
    on: ["line_item_id"],
    unique: false,
    where: "deleted_at IS NULL",
  },
  {
    name: "IDX_concierge_order_item_item_id",
    on: ["item_id"],
    unique: false,
    where: "deleted_at IS NULL",
  },
  {
    name: "IDX_concierge_order_item_status",
    on: ["status"],
    unique: false,
    where: "deleted_at IS NULL",
  },
  {
    name: "IDX_concierge_order_item_is_active",
    on: ["is_active"],
    unique: false,
    where: "deleted_at IS NULL",
  },
  {
    name: "IDX_concierge_order_item_category_id",
    on: ["category_id"],
    unique: false,
    where: "deleted_at IS NULL",
  },
  {
    name: "IDX_concierge_order_item_start_date",
    on: ["start_date"],
    unique: false,
    where: "deleted_at IS NULL AND start_date IS NOT NULL",
  },
  {
    name: "IDX_concierge_order_item_end_date",
    on: ["end_date"],
    unique: false,
    where: "deleted_at IS NULL AND end_date IS NOT NULL",
  },
  {
    name: "IDX_concierge_order_item_date_range",
    on: ["start_date", "end_date"],
    unique: false,
    where: "deleted_at IS NULL AND start_date IS NOT NULL AND end_date IS NOT NULL",
  },
]);

export default ConciergeOrderItem;
