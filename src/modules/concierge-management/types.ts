/**
 * Concierge Management Module Types
 * 
 * This file contains all type definitions for the concierge management system
 * including task management, assignments, and related functionality.
 */

// Enums
export enum TaskStatus {
  PENDING = "pending",
  IN_PROGRESS = "in_progress", 
  REVIEW = "review",
  COMPLETED = "completed",
  CANCELLED = "cancelled",
}

export enum TaskPriority {
  LOW = "low",
  MEDIUM = "medium",
  HIGH = "high",
  URGENT = "urgent",
}

export enum EntityType {
  BOOKING = "booking",
  DEAL = "deal",
  GUEST = "guest",
  ITINERARY = "itinerary",
}

// Note interfaces
export interface NoteInput {
  title: string;
  content?: string;
  entity: string;
  entity_id: string;
  created_by_id?: string;
}

export interface NoteResponse extends NoteInput {
  id: string;
  created_at: Date;
  updated_at: Date;
  updated_by_id?: string;
  deleted: boolean;
  deleted_at?: Date;
}

export interface NoteUpdateInput {
  title?: string;
  content?: string;
  updated_by_id?: string;
  deleted?: boolean;
}

// Note request/response types
export interface CreateNoteRequest {
  note: NoteInput;
}

export interface UpdateNoteRequest {
  note: NoteUpdateInput;
}

export interface ListNotesRequest {
  filters?: {
    entity?: string;
    entity_id?: string;
    deleted?: boolean;
    created_by_id?: string;
    q?: string; // Search query
    created_at_gte?: string;
    created_at_lte?: string;
  };
  options?: {
    limit?: number;
    offset?: number;
    order?: Record<string, "ASC" | "DESC">;
  };
}

export interface NoteListResponse {
  notes: NoteResponse[];
  count: number;
  limit: number;
  offset: number;
}

// Base interfaces
export interface ConciergeTaskInput {
  title: string;
  description?: string;
  status?: TaskStatus;
  priority?: TaskPriority;
  entity_type?: string;
  entity_id?: string;
  assigned_to?: string; // User ID
  created_by?: string; // User ID
  due_date?: Date;
  metadata?: Record<string, any>;
}

export interface ConciergeTaskResponse extends ConciergeTaskInput {
  id: string;
  created_at: Date;
  updated_at: Date;
  updated_by?: string;
  is_deleted: boolean;
  deleted_at?: Date;
}

export interface ConciergeTaskUpdateInput {
  title?: string;
  description?: string;
  status?: TaskStatus;
  priority?: TaskPriority;
  entity_type?: string;
  entity_id?: string;
  assigned_to?: string;
  due_date?: Date;
  metadata?: Record<string, any>;
  updated_by?: string;
}

// Query interfaces
export interface ConciergeTaskFilters {
  id?: string | string[];
  title?: string;
  status?: TaskStatus | TaskStatus[];
  priority?: TaskPriority | TaskPriority[];
  entity_type?: string;
  entity_id?: string;
  assigned_to?: string;
  created_by?: string;
  due_date?: {
    gte?: Date;
    lte?: Date;
  };
  created_at?: {
    gte?: Date;
    lte?: Date;
  };
  is_deleted?: boolean;
}

export interface ConciergeTaskListOptions {
  limit?: number;
  offset?: number;
  order?: Record<string, "ASC" | "DESC">;
}

export interface ConciergeTaskListResponse {
  tasks: ConciergeTaskResponse[];
  count: number;
  limit: number;
  offset: number;
}

// Service method interfaces
export interface CreateConciergeTaskRequest {
  task: ConciergeTaskInput;
}

export interface UpdateConciergeTaskRequest {
  task: ConciergeTaskUpdateInput;
}

export interface ListConciergeTasksRequest {
  filters?: ConciergeTaskFilters;
  options?: ConciergeTaskListOptions;
}

// Service interface
export interface ConciergeManagementServiceMethods {
  // Task management
  createTask(data: CreateConciergeTaskRequest): Promise<ConciergeTaskResponse>;
  updateTask(id: string, data: UpdateConciergeTaskRequest): Promise<ConciergeTaskResponse>;
  retrieveTask(id: string): Promise<ConciergeTaskResponse>;
  listTasks(request: ListConciergeTasksRequest): Promise<ConciergeTaskListResponse>;
  deleteTask(id: string): Promise<void>;
  softDeleteTask(id: string, deletedBy?: string): Promise<ConciergeTaskResponse>;

  // Task assignment
  assignTask(taskId: string, userId: string, assignedBy?: string): Promise<ConciergeTaskResponse>;
  unassignTask(taskId: string, unassignedBy?: string): Promise<ConciergeTaskResponse>;

  // Task status management
  completeTask(taskId: string, completedBy?: string): Promise<ConciergeTaskResponse>;
  cancelTask(taskId: string, cancelledBy?: string): Promise<ConciergeTaskResponse>;

  // Entity-specific methods
  getTasksForEntity(entityType: string, entityId: string): Promise<ConciergeTaskResponse[]>;
  getTasksForUser(userId: string): Promise<ConciergeTaskResponse[]>;

  // Note management
  createNote(data: CreateNoteRequest): Promise<NoteResponse>;
  updateNote(id: string, data: UpdateNoteRequest): Promise<NoteResponse>;
  getNote(id: string): Promise<NoteResponse>;
  getNotes(request: ListNotesRequest): Promise<NoteListResponse>;
  deleteNote(id: string): Promise<void>;
  softDeleteNote(id: string, deletedBy?: string): Promise<NoteResponse>;

  // Note entity-specific methods
  getNotesForEntity(entity: string, entityId: string): Promise<NoteResponse[]>;
}

// Concierge Order Enums
export enum ConciergeOrderStatus {
  NOT_STARTED = "not_started",
  IN_PROGRESS = "in_progress",
  WAITING_CUSTOMER = "waiting_customer",
  READY_TO_FINALIZE = "ready_to_finalize",
  COMPLETED = "completed",
}

export enum ConciergeOrderItemStatus {
  UNDER_REVIEW = "under_review",
  CLIENT_CONFIRMED = "client_confirmed",
  ORDER_PLACED = "order_placed",
  CANCELLED = "cancelled",
  COMPLETED = "completed",
}

// Concierge Order interfaces
export interface ConciergeOrderInput {
  order_id: string;
  hotel_id?: string;
  check_in_date?: string | Date;
  check_out_date?: string | Date;
  assigned_to?: string;
  notes?: string;
  status?: ConciergeOrderStatus;
  last_contacted_at?: string | Date;
  metadata?: Record<string, any>;
}

export interface CreateConciergeOrderInput extends ConciergeOrderInput {}

export interface UpdateConciergeOrderInput extends Partial<Omit<ConciergeOrderInput, 'order_id'>> {}

export interface ConciergeOrderResponse extends ConciergeOrderInput {
  id: string;
  created_at: Date;
  updated_at: Date;
  deleted_at?: Date;
}

export interface ConciergeOrderFilters {
  id?: string | string[];
  order_id?: string | string[];
  hotel_id?: string | string[];
  assigned_to?: string | string[];
  status?: ConciergeOrderStatus | ConciergeOrderStatus[];
  check_in_date?: {
    gte?: Date;
    lte?: Date;
  };
  check_out_date?: {
    gte?: Date;
    lte?: Date;
  };
  created_at?: {
    gte?: Date;
    lte?: Date;
  };
  updated_at?: {
    gte?: Date;
    lte?: Date;
  };
  search?: string; // For searching across concierge order and order fields
}

export interface ConciergeOrderListOptions {
  limit?: number;
  offset?: number;
  order?: Record<string, "ASC" | "DESC">;
  relations?: string[];
}

export interface ConciergeOrderListResponse {
  concierge_orders: ConciergeOrderResponse[];
  count: number;
  limit: number;
  offset: number;
}

export interface ConciergeOrderWithOrderData extends ConciergeOrderResponse {
  order: any | null; // Order data from the orders table
}

export interface ConciergeOrderWithOrderDataListResponse {
  concierge_orders: ConciergeOrderWithOrderData[];
  count: number;
  limit: number;
  offset: number;
}

// Concierge Order Item interfaces
export interface ConciergeOrderItemInput {
  concierge_order_id: string;
  line_item_id?: string;
  item_id?: string;
  variant_id?: string;
  quantity: number;
  unit_price: number;
  title: string;
  status?: ConciergeOrderItemStatus;
  notes?: string;
  is_active?: boolean;
  added_by?: string;
  finalized_by?: string;
  added_at?: string | Date;
  finalized_at?: string | Date;
  metadata?: Record<string, any>;
  // New fields for category and date range
  category_id?: string | null;
  start_date?: string | null;
  end_date?: string | null;
}

export interface CreateConciergeOrderItemInput extends ConciergeOrderItemInput {}

export interface UpdateConciergeOrderItemInput extends Partial<Omit<ConciergeOrderItemInput, 'concierge_order_id'>> {}

export interface ConciergeOrderItemResponse extends ConciergeOrderItemInput {
  id: string;
  created_at: Date;
  updated_at: Date;
  deleted_at?: Date;
}

export interface ConciergeOrderItemFilters {
  id?: string | string[];
  concierge_order_id?: string | string[];
  line_item_id?: string | string[];
  item_id?: string | string[];
  variant_id?: string | string[];
  status?: ConciergeOrderItemStatus | ConciergeOrderItemStatus[];
  is_active?: boolean;
  added_by?: string | string[];
  finalized_by?: string | string[];
  created_at?: {
    gte?: Date;
    lte?: Date;
  };
  updated_at?: {
    gte?: Date;
    lte?: Date;
  };
}

export interface ConciergeOrderItemListOptions {
  limit?: number;
  offset?: number;
  order?: Record<string, "ASC" | "DESC">;
  relations?: string[];
}

export interface ConciergeOrderItemListResponse {
  concierge_order_items: ConciergeOrderItemResponse[];
  count: number;
  limit: number;
  offset: number;
}

// Enhanced types for supplier management on-request screen
export interface TravelDates {
  check_in_date: string | null;
  check_out_date: string | null;
}

export interface CategoryInfo {
  id: string;
  name: string;
  handle: string;
  metadata?: Record<string, any>;
}

export interface ProductInfo {
  id: string;
  title: string;
  description?: string;
  metadata?: Record<string, any>;
}

export interface OrderLineItemInfo {
  id: string;
  unit_price: number;
  quantity: number;
  title: string;
  variant_id?: string;
  product_id?: string;
  metadata?: Record<string, any>;
}

export interface OrderInfo {
  id: string;
  display_id: number;
  email: string;
  currency_code: string;
  total: number;
  created_at: Date;
  updated_at: Date;
}

export interface ConciergeOrderInfo {
  id: string;
  order_id: string;
  assigned_to?: string;
  notes?: string;
  status: ConciergeOrderStatus;
  last_contacted_at?: Date;
  created_at: Date;
  updated_at: Date;
  metadata?: Record<string, any>;
}

export interface EnhancedConciergeOrderItemResponse extends ConciergeOrderItemResponse {
  // Calculated fields
  total_price: number;
  requested_date: Date;

  // Travel dates from concierge order metadata
  travel_dates?: TravelDates;

  // Related entity data
  concierge_order?: ConciergeOrderInfo;
  order?: OrderInfo;
  order_line_item?: OrderLineItemInfo;
  order_item?: OrderLineItemInfo; // Same as order_line_item in this context
  product?: ProductInfo;
  category?: CategoryInfo;
}

export interface EnhancedConciergeOrderItemListResponse {
  concierge_order_items: EnhancedConciergeOrderItemResponse[];
  count: number;
  limit: number;
  offset: number;
}
