import { LoaderOptions } from "@camped-ai/framework/types";
import { asClass } from "awilix";
import { BaseRepository } from "../repositories";
import ItineraryService from "../itinerary-service";
import ConciergeOrderService from "../concierge-order-service";
import ConciergeOrderItemService from "../concierge-order-item-service";
import { ITINERARY_SERVICE } from "../index";

export default async ({ container }: LoaderOptions): Promise<void> => {
  console.log("🚀 [CONCIERGE-MODULE] Starting concierge management module loader...");

  try {
    // Register base repository
    container.register({
      baseRepository: asClass(BaseRepository).singleton(),
    });
    console.log("✅ [CONCIERGE-MODULE] BaseRepository registered");

    // Register itinerary service with constant key
    if (!container.hasRegistration(ITINERARY_SERVICE)) {
      container.register({
        [ITINERARY_SERVICE]: asClass(ItineraryService).singleton(),
      });
      console.log("✅ [CONCIERGE-MODULE] ItineraryService registered with constant key");
    }

    // Register itinerary service with string key for backward compatibility
    if (!container.hasRegistration("itineraryService")) {
      container.register({
        itineraryService: asClass(ItineraryService).singleton(),
      });
      console.log("✅ [CONCIERGE-MODULE] ItineraryService registered with string key");
    }

    // Register concierge order service
    if (!container.hasRegistration("conciergeOrderService")) {
      container.register({
        conciergeOrderService: asClass(ConciergeOrderService).singleton(),
      });
      console.log("✅ [CONCIERGE-MODULE] ConciergeOrderService registered");
    }

    // Register concierge order item service
    if (!container.hasRegistration("conciergeOrderItemService")) {
      container.register({
        conciergeOrderItemService: asClass(ConciergeOrderItemService).singleton(),
      });
      console.log("✅ [CONCIERGE-MODULE] ConciergeOrderItemService registered");
    }

    // Verify registration
    const hasConstantKey = container.hasRegistration(ITINERARY_SERVICE);
    const hasStringKey = container.hasRegistration("itineraryService");
    const hasConciergeOrderService = container.hasRegistration("conciergeOrderService");
    const hasConciergeOrderItemService = container.hasRegistration("conciergeOrderItemService");
    console.log(`🔍 [CONCIERGE-MODULE] Registration verification - Constant: ${hasConstantKey}, String: ${hasStringKey}, ConciergeOrder: ${hasConciergeOrderService}, ConciergeOrderItem: ${hasConciergeOrderItemService}`);

  } catch (error) {
    console.error("❌ [CONCIERGE-MODULE] Failed to register services:", error);
    throw error;
  }
}
