import { MedusaService, MedusaError } from "@camped-ai/framework/utils";
import { MedusaContainer } from "@camped-ai/framework/types";
import { EntityManager } from "typeorm";
import { ConciergeOrderItem } from "./models/concierge-order-item";
import {
  CreateConciergeOrderItemInput,
  UpdateConciergeOrderItemInput,
  ConciergeOrderItemResponse,
  ConciergeOrderItemFilters,
  ConciergeOrderItemListOptions,
  ConciergeOrderItemListResponse,
  ConciergeOrderItemStatus,
} from "./types";

/**
 * Concierge Order Item Service
 *
 * Provides comprehensive concierge order item management functionality including
 * item creation, status management, and lifecycle tracking.
 */
class ConciergeOrderItemService extends MedusaService({
  ConciergeOrderItem,
}) {
  protected container_: MedusaContainer;
  protected activeManager_: EntityManager;

  constructor(container: MedusaContainer) {
    super(container);
    this.container_ = container;
    this.activeManager_ = this.container_["manager" as any];
  }

  /**
   * Create a new concierge order item
   */
  async createConciergeOrderItem(data: CreateConciergeOrderItemInput): Promise<ConciergeOrderItemResponse> {
    try {
      const itemData = {
        ...data,
        status: data.status || ConciergeOrderItemStatus.UNDER_REVIEW,
        added_at: new Date(),
        is_active: data.is_active !== undefined ? data.is_active : true,
      };

      const items = await this.createConciergeOrderItems([itemData]);
      return items[0] as ConciergeOrderItemResponse;
    } catch (error) {
      throw new MedusaError(
        MedusaError.Types.DB_ERROR,
        `Failed to create concierge order item: ${error.message}`
      );
    }
  }

  /**
   * Retrieve a concierge order item by ID
   */
  async retrieveConciergeOrderItem(id: string, config?: any): Promise<ConciergeOrderItemResponse> {
    try {
      const item = await this.retrieveConciergeOrderItems([id], config);
      if (!item || item.length === 0) {
        throw new MedusaError(
          MedusaError.Types.NOT_FOUND,
          `Concierge order item with id: ${id} was not found`
        );
      }
      return item[0] as ConciergeOrderItemResponse;
    } catch (error) {
      if (error instanceof MedusaError) {
        throw error;
      }
      throw new MedusaError(
        MedusaError.Types.DB_ERROR,
        `Failed to retrieve concierge order item: ${error.message}`
      );
    }
  }

  /**
   * Update a concierge order item
   */
  async updateConciergeOrderItem(id: string, data: UpdateConciergeOrderItemInput): Promise<ConciergeOrderItemResponse> {
    try {
      const updateData = {
        ...data,
        ...(data.finalized_at && { finalized_at: new Date(data.finalized_at) }),
      };

      await this.updateConciergeOrderItems([{ id, ...updateData }]);
      return await this.retrieveConciergeOrderItem(id);
    } catch (error) {
      throw new MedusaError(
        MedusaError.Types.DB_ERROR,
        `Failed to update concierge order item: ${error.message}`
      );
    }
  }

  /**
   * List concierge order items with filters and pagination
   */
  async listConciergeOrderItemsWithPagination(
    filters: ConciergeOrderItemFilters = {},
    options: ConciergeOrderItemListOptions = {}
  ): Promise<ConciergeOrderItemListResponse> {
    try {
      const { limit = 20, offset = 0, ...listOptions } = options;
      
      const items = await this.listConciergeOrderItems(filters, {
        take: limit,
        skip: offset,
        ...listOptions,
      });

      // Get total count for pagination
      const totalCount = await this.listConciergeOrderItems(filters, { take: undefined, skip: undefined });

      return {
        concierge_order_items: items,
        count: totalCount.length,
        limit,
        offset,
      };
    } catch (error) {
      throw new MedusaError(
        MedusaError.Types.DB_ERROR,
        `Failed to list concierge order items: ${error.message}`
      );
    }
  }

  /**
   * Update concierge order item status
   */
  async updateConciergeOrderItemStatus(
    id: string, 
    status: ConciergeOrderItemStatus, 
    updatedBy?: string
  ): Promise<ConciergeOrderItemResponse> {
    try {
      const updateData: UpdateConciergeOrderItemInput = {
        status,
        metadata: {
          status_updated_by: updatedBy,
          status_updated_at: new Date().toISOString(),
        },
      };

      // If completing, set finalized_at and finalized_by
      if (status === ConciergeOrderItemStatus.COMPLETED) {
        updateData.finalized_at = new Date().toISOString();
        updateData.finalized_by = updatedBy;
      }

      return await this.updateConciergeOrderItem(id, updateData);
    } catch (error) {
      throw new MedusaError(
        MedusaError.Types.DB_ERROR,
        `Failed to update concierge order item status: ${error.message}`
      );
    }
  }

  /**
   * Get concierge order items for a specific concierge order
   */
  async getConciergeOrderItemsForOrder(conciergeOrderId: string): Promise<ConciergeOrderItemResponse[]> {
    try {
      return await this.listConciergeOrderItems({ concierge_order_id: conciergeOrderId });
    } catch (error) {
      throw new MedusaError(
        MedusaError.Types.DB_ERROR,
        `Failed to get concierge order items for order: ${error.message}`
      );
    }
  }

  /**
   * Get active concierge order items for a specific concierge order
   */
  async getActiveConciergeOrderItemsForOrder(conciergeOrderId: string): Promise<ConciergeOrderItemResponse[]> {
    try {
      return await this.listConciergeOrderItems({ 
        concierge_order_id: conciergeOrderId,
        is_active: true 
      });
    } catch (error) {
      throw new MedusaError(
        MedusaError.Types.DB_ERROR,
        `Failed to get active concierge order items for order: ${error.message}`
      );
    }
  }

  /**
   * Get concierge order items by status
   */
  async getConciergeOrderItemsByStatus(status: ConciergeOrderItemStatus): Promise<ConciergeOrderItemResponse[]> {
    try {
      return await this.listConciergeOrderItems({ status });
    } catch (error) {
      throw new MedusaError(
        MedusaError.Types.DB_ERROR,
        `Failed to get concierge order items by status: ${error.message}`
      );
    }
  }

  /**
   * Deactivate a concierge order item (soft removal)
   */
  async deactivateConciergeOrderItem(id: string, deactivatedBy?: string): Promise<ConciergeOrderItemResponse> {
    try {
      return await this.updateConciergeOrderItem(id, {
        is_active: false,
        metadata: {
          deactivated_by: deactivatedBy,
          deactivated_at: new Date().toISOString(),
        },
      });
    } catch (error) {
      throw new MedusaError(
        MedusaError.Types.DB_ERROR,
        `Failed to deactivate concierge order item: ${error.message}`
      );
    }
  }

  /**
   * Reactivate a concierge order item
   */
  async reactivateConciergeOrderItem(id: string, reactivatedBy?: string): Promise<ConciergeOrderItemResponse> {
    try {
      return await this.updateConciergeOrderItem(id, {
        is_active: true,
        metadata: {
          reactivated_by: reactivatedBy,
          reactivated_at: new Date().toISOString(),
        },
      });
    } catch (error) {
      throw new MedusaError(
        MedusaError.Types.DB_ERROR,
        `Failed to reactivate concierge order item: ${error.message}`
      );
    }
  }

  /**
   * Delete a concierge order item (soft delete)
   */
  async deleteConciergeOrderItem(id: string): Promise<void> {
    try {
      await this.softDeleteConciergeOrderItems([id]);
    } catch (error) {
      throw new MedusaError(
        MedusaError.Types.DB_ERROR,
        `Failed to delete concierge order item: ${error.message}`
      );
    }
  }

  /**
   * Bulk update concierge order items status
   */
  async bulkUpdateConciergeOrderItemsStatus(
    ids: string[], 
    status: ConciergeOrderItemStatus, 
    updatedBy?: string
  ): Promise<ConciergeOrderItemResponse[]> {
    try {
      const updatePromises = ids.map(id => 
        this.updateConciergeOrderItemStatus(id, status, updatedBy)
      );
      return await Promise.all(updatePromises);
    } catch (error) {
      throw new MedusaError(
        MedusaError.Types.DB_ERROR,
        `Failed to bulk update concierge order items status: ${error.message}`
      );
    }
  }
}

export default ConciergeOrderItemService;
