import { Migration } from "@mikro-orm/migrations";

export class Migration20250723000000 extends Migration {
  override async up(): Promise<void> {
    // Create itinerary_event_media table
    this.addSql(`
      CREATE TABLE IF NOT EXISTS "itinerary_event_media" (
        "id" text NOT NULL,
        "event_id" text NOT NULL,
        "url" text NOT NULL,
        "type" text CHECK ("type" IN ('image', 'video', 'document')) NOT NULL DEFAULT 'image',
        "title" text NULL,
        "description" text NULL,
        "metadata" jsonb NULL,
        "sort_order" integer NOT NULL DEFAULT 0,
        "created_at" timestamptz NOT NULL DEFAULT now(),
        "updated_at" timestamptz NOT NULL DEFAULT now(),
        "deleted_at" timestamptz NULL,
        CONSTRAINT "itinerary_event_media_pkey" PRIMARY KEY ("id")
      );
    `);

    // Create indexes for itinerary_event_media
    this.addSql(`
      CREATE INDEX "IDX_itinerary_event_media_event_id" ON "itinerary_event_media" ("event_id") WHERE "deleted_at" IS NULL;
    `);

    this.addSql(`
      CREATE INDEX "IDX_itinerary_event_media_sort_order" ON "itinerary_event_media" ("event_id", "sort_order") WHERE "deleted_at" IS NULL;
    `);

    // Remove the problematic array columns from itinerary_event
    this.addSql(`
      ALTER TABLE "itinerary_event"
      DROP COLUMN IF EXISTS "media",
      DROP COLUMN IF EXISTS "attachments",
      DROP COLUMN IF EXISTS "people";
    `);

    // Remove any existing constraints related to array fields
    this.addSql(`
      ALTER TABLE "itinerary_event"
      DROP CONSTRAINT IF EXISTS "CHK_itinerary_event_media_is_array";
    `);

    this.addSql(`
      ALTER TABLE "itinerary_event"
      DROP CONSTRAINT IF EXISTS "CHK_itinerary_event_attachments_is_array";
    `);

    this.addSql(`
      ALTER TABLE "itinerary_event"
      DROP CONSTRAINT IF EXISTS "CHK_itinerary_event_people_is_array";
    `);

    console.log('✅ ItineraryEventMedia table created and array columns removed');
  }

  override async down(): Promise<void> {
    // Drop the itinerary_event_media table
    this.addSql(`
      DROP TABLE IF EXISTS "itinerary_event_media";
    `);

    // Re-add the array columns to itinerary_event
    this.addSql(`
      ALTER TABLE "itinerary_event"
      ADD COLUMN "media" jsonb DEFAULT '[]'::jsonb,
      ADD COLUMN "attachments" jsonb DEFAULT '[]'::jsonb,
      ADD COLUMN "people" jsonb DEFAULT '[]'::jsonb;
    `);
  }
}
