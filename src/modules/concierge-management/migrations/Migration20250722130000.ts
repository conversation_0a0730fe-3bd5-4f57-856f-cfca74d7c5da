import { Migration } from '@mikro-orm/migrations';

export class Migration20250722130000 extends Migration {

  override async up(): Promise<void> {
    // Create note table
    this.addSql(`create table if not exists "note" ("id" text not null, "title" text not null, "content" text null, "entity" text not null, "entity_id" text not null, "deleted" boolean not null default false, "created_by_id" text null, "updated_by_id" text null, "created_at" timestamptz not null default now(), "updated_at" timestamptz not null default now(), "deleted_at" timestamptz null, constraint "note_pkey" primary key ("id"));`);

    // Create indexes for better query performance
    this.addSql(`create index if not exists "IDX_note_entity" on "note" ("entity", "entity_id") where "deleted" = false;`);
    this.addSql(`create index if not exists "IDX_note_created_at" on "note" ("created_at");`);
    this.addSql(`create index if not exists "IDX_note_deleted" on "note" ("deleted");`);
  }

  override async down(): Promise<void> {
    this.addSql(`drop table if exists "note" cascade;`);
  }

}
