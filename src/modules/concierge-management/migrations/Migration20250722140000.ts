import { Migration } from "@mikro-orm/migrations";

export class Migration20250722140000 extends Migration {
  override async up(): Promise<void> {
    // Fix existing itinerary_event records where array fields are stored as empty objects instead of empty arrays
    
    // Update media field: convert {} to []
    this.addSql(`
      UPDATE "itinerary_event" 
      SET "media" = '[]'::jsonb 
      WHERE "media" = '{}'::jsonb;
    `);
    
    // Update attachments field: convert {} to []
    this.addSql(`
      UPDATE "itinerary_event" 
      SET "attachments" = '[]'::jsonb 
      WHERE "attachments" = '{}'::jsonb;
    `);
    
    // Update people field: convert {} to []
    this.addSql(`
      UPDATE "itinerary_event" 
      SET "people" = '[]'::jsonb 
      WHERE "people" = '{}'::jsonb;
    `);
    
    // Set default values for NULL fields
    this.addSql(`
      UPDATE "itinerary_event" 
      SET "media" = '[]'::jsonb 
      WHERE "media" IS NULL;
    `);
    
    this.addSql(`
      UPDATE "itinerary_event" 
      SET "attachments" = '[]'::jsonb 
      WHERE "attachments" IS NULL;
    `);
    
    this.addSql(`
      UPDATE "itinerary_event" 
      SET "people" = '[]'::jsonb 
      WHERE "people" IS NULL;
    `);
    
    // Alter table to set default values and make fields NOT NULL
    this.addSql(`
      ALTER TABLE "itinerary_event"
      ALTER COLUMN "media" SET DEFAULT '[]'::jsonb,
      ALTER COLUMN "media" SET NOT NULL;
    `);

    this.addSql(`
      ALTER TABLE "itinerary_event"
      ALTER COLUMN "attachments" SET DEFAULT '[]'::jsonb,
      ALTER COLUMN "attachments" SET NOT NULL;
    `);

    this.addSql(`
      ALTER TABLE "itinerary_event"
      ALTER COLUMN "people" SET DEFAULT '[]'::jsonb,
      ALTER COLUMN "people" SET NOT NULL;
    `);

    // Add check constraints to ensure array fields are always arrays, not objects
    this.addSql(`
      ALTER TABLE "itinerary_event"
      ADD CONSTRAINT "CHK_itinerary_event_media_is_array"
      CHECK (jsonb_typeof("media") = 'array');
    `);

    this.addSql(`
      ALTER TABLE "itinerary_event"
      ADD CONSTRAINT "CHK_itinerary_event_attachments_is_array"
      CHECK (jsonb_typeof("attachments") = 'array');
    `);

    this.addSql(`
      ALTER TABLE "itinerary_event"
      ADD CONSTRAINT "CHK_itinerary_event_people_is_array"
      CHECK (jsonb_typeof("people") = 'array');
    `);
  }

  override async down(): Promise<void> {
    // Remove check constraints first
    this.addSql(`
      ALTER TABLE "itinerary_event"
      DROP CONSTRAINT IF EXISTS "CHK_itinerary_event_media_is_array";
    `);

    this.addSql(`
      ALTER TABLE "itinerary_event"
      DROP CONSTRAINT IF EXISTS "CHK_itinerary_event_attachments_is_array";
    `);

    this.addSql(`
      ALTER TABLE "itinerary_event"
      DROP CONSTRAINT IF EXISTS "CHK_itinerary_event_people_is_array";
    `);

    // Revert the changes - make fields nullable again and remove defaults
    this.addSql(`
      ALTER TABLE "itinerary_event"
      ALTER COLUMN "media" DROP DEFAULT,
      ALTER COLUMN "media" DROP NOT NULL;
    `);

    this.addSql(`
      ALTER TABLE "itinerary_event"
      ALTER COLUMN "attachments" DROP DEFAULT,
      ALTER COLUMN "attachments" DROP NOT NULL;
    `);

    this.addSql(`
      ALTER TABLE "itinerary_event"
      ALTER COLUMN "people" DROP DEFAULT,
      ALTER COLUMN "people" DROP NOT NULL;
    `);
  }
}
