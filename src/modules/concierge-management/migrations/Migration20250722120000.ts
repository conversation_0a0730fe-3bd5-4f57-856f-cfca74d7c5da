import { Migration } from '@mikro-orm/migrations';

export class Migration20250722120000 extends Migration {

  override async up(): Promise<void> {
    // Create itinerary table
    this.addSql(`create table if not exists "itinerary" ("id" text not null, "booking_id" text not null, "title" text null, "status" text check ("status" in ('DRAFT', 'FINALIZED')) not null default 'DRAFT', "created_by" text null, "created_at" timestamptz not null default now(), "updated_at" timestamptz not null default now(), "deleted_at" timestamptz null, constraint "itinerary_pkey" primary key ("id"));`);
    
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_itinerary_booking_id" ON "itinerary" ("booking_id") WHERE deleted_at IS NULL;`);
    this.addSql(`CREATE UNIQUE INDEX IF NOT EXISTS "IDX_itinerary_booking_id_unique" ON "itinerary" ("booking_id") WHER<PERSON> deleted_at IS NULL;`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_itinerary_deleted_at" ON "itinerary" (deleted_at) WHERE deleted_at IS NULL;`);

    // Create itinerary_day table
    this.addSql(`create table if not exists "itinerary_day" ("id" text not null, "itinerary_id" text not null, "date" timestamptz not null, "title" text null, "sort_order" integer not null default 0, "created_at" timestamptz not null default now(), "updated_at" timestamptz not null default now(), "deleted_at" timestamptz null, constraint "itinerary_day_pkey" primary key ("id"));`);
    
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_itinerary_day_itinerary_id" ON "itinerary_day" ("itinerary_id") WHERE deleted_at IS NULL;`);
    this.addSql(`CREATE UNIQUE INDEX IF NOT EXISTS "IDX_itinerary_day_date_unique" ON "itinerary_day" ("itinerary_id", "date") WHERE deleted_at IS NULL;`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_itinerary_day_sort_order" ON "itinerary_day" ("itinerary_id", "sort_order") WHERE deleted_at IS NULL;`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_itinerary_day_deleted_at" ON "itinerary_day" (deleted_at) WHERE deleted_at IS NULL;`);

    // Create itinerary_event table
    this.addSql(`create table if not exists "itinerary_event" ("id" text not null, "day_id" text not null, "category" text check ("category" in ('Flight', 'Lodging', 'Activity', 'Cruise', 'Transport', 'Info')) not null, "type" text null, "title" text not null, "notes" text null, "start_time" text null, "end_time" text null, "duration" text null, "timezone" text null, "details" jsonb null, "price" integer null, "currency" text null, "media" jsonb null, "attachments" jsonb null, "people" jsonb null, "created_at" timestamptz not null default now(), "updated_at" timestamptz not null default now(), "deleted_at" timestamptz null, constraint "itinerary_event_pkey" primary key ("id"));`);
    
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_itinerary_event_day_id" ON "itinerary_event" ("day_id") WHERE deleted_at IS NULL;`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_itinerary_event_category" ON "itinerary_event" ("category") WHERE deleted_at IS NULL;`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_itinerary_event_start_time" ON "itinerary_event" ("day_id", "start_time") WHERE deleted_at IS NULL;`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_itinerary_event_deleted_at" ON "itinerary_event" (deleted_at) WHERE deleted_at IS NULL;`);
  }

  override async down(): Promise<void> {
    this.addSql(`drop table if exists "itinerary_event" cascade;`);
    this.addSql(`drop table if exists "itinerary_day" cascade;`);
    this.addSql(`drop table if exists "itinerary" cascade;`);
  }

}
