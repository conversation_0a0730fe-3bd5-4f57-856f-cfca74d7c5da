import { Migration } from "@mikro-orm/migrations";

export class Migration20250723120000 extends Migration {
  override async up(): Promise<void> {
    console.log('🚀 [Migration] Starting API-based events database updates...');

    // 1. Remove the CHECK constraint on category field to support dynamic categories
    this.addSql(`
      ALTER TABLE "itinerary_event"
      DROP CONSTRAINT IF EXISTS "itinerary_event_category_check";
    `);

    // 2. Add new fields to support supplier offering integration
    this.addSql(`
      ALTER TABLE "itinerary_event"
      ADD COLUMN IF NOT EXISTS "supplier_offering_id" text NULL,
      ADD COLUMN IF NOT EXISTS "supplier_id" text NULL,
      ADD COLUMN IF NOT EXISTS "category_id" text NULL,
      ADD COLUMN IF NOT EXISTS "from_date" date NULL,
      ADD COLUMN IF NOT EXISTS "to_date" date NULL,
      ADD COLUMN IF NOT EXISTS "event_source" text DEFAULT 'manual' CHECK ("event_source" IN ('manual', 'supplier_offering'));
    `);

    // 3. Create indexes for the new fields
    this.addSql(`
      CREATE INDEX IF NOT EXISTS "IDX_itinerary_event_supplier_offering_id" 
      ON "itinerary_event" ("supplier_offering_id") 
      WHERE "deleted_at" IS NULL AND "supplier_offering_id" IS NOT NULL;
    `);

    this.addSql(`
      CREATE INDEX IF NOT EXISTS "IDX_itinerary_event_supplier_id" 
      ON "itinerary_event" ("supplier_id") 
      WHERE "deleted_at" IS NULL AND "supplier_id" IS NOT NULL;
    `);

    this.addSql(`
      CREATE INDEX IF NOT EXISTS "IDX_itinerary_event_category_id" 
      ON "itinerary_event" ("category_id") 
      WHERE "deleted_at" IS NULL AND "category_id" IS NOT NULL;
    `);

    this.addSql(`
      CREATE INDEX IF NOT EXISTS "IDX_itinerary_event_event_source" 
      ON "itinerary_event" ("event_source") 
      WHERE "deleted_at" IS NULL;
    `);

    this.addSql(`
      CREATE INDEX IF NOT EXISTS "IDX_itinerary_event_date_range" 
      ON "itinerary_event" ("from_date", "to_date") 
      WHERE "deleted_at" IS NULL AND "from_date" IS NOT NULL AND "to_date" IS NOT NULL;
    `);

    // 4. Update existing events to have event_source = 'manual'
    this.addSql(`
      UPDATE "itinerary_event" 
      SET "event_source" = 'manual' 
      WHERE "event_source" IS NULL;
    `);

    // 5. Add comments to document the new fields
    this.addSql(`
      COMMENT ON COLUMN "itinerary_event"."supplier_offering_id" IS 'Reference to supplier offering that created this event';
    `);

    this.addSql(`
      COMMENT ON COLUMN "itinerary_event"."supplier_id" IS 'Reference to supplier that provided the offering';
    `);

    this.addSql(`
      COMMENT ON COLUMN "itinerary_event"."category_id" IS 'Reference to product service category';
    `);

    this.addSql(`
      COMMENT ON COLUMN "itinerary_event"."from_date" IS 'Start date for the event (from supplier offering)';
    `);

    this.addSql(`
      COMMENT ON COLUMN "itinerary_event"."to_date" IS 'End date for the event (from supplier offering)';
    `);

    this.addSql(`
      COMMENT ON COLUMN "itinerary_event"."event_source" IS 'Source of the event: manual or supplier_offering';
    `);

    this.addSql(`
      COMMENT ON COLUMN "itinerary_event"."category" IS 'Dynamic category name - no longer restricted to enum values';
    `);

    console.log('✅ [Migration] API-based events database updates completed successfully');
  }

  override async down(): Promise<void> {
    console.log('🔄 [Migration] Rolling back API-based events database updates...');

    // Remove comments
    this.addSql(`
      COMMENT ON COLUMN "itinerary_event"."supplier_offering_id" IS NULL;
    `);

    this.addSql(`
      COMMENT ON COLUMN "itinerary_event"."supplier_id" IS NULL;
    `);

    this.addSql(`
      COMMENT ON COLUMN "itinerary_event"."category_id" IS NULL;
    `);

    this.addSql(`
      COMMENT ON COLUMN "itinerary_event"."from_date" IS NULL;
    `);

    this.addSql(`
      COMMENT ON COLUMN "itinerary_event"."to_date" IS NULL;
    `);

    this.addSql(`
      COMMENT ON COLUMN "itinerary_event"."event_source" IS NULL;
    `);

    this.addSql(`
      COMMENT ON COLUMN "itinerary_event"."category" IS NULL;
    `);

    // Drop indexes
    this.addSql(`DROP INDEX IF EXISTS "IDX_itinerary_event_supplier_offering_id";`);
    this.addSql(`DROP INDEX IF EXISTS "IDX_itinerary_event_supplier_id";`);
    this.addSql(`DROP INDEX IF EXISTS "IDX_itinerary_event_category_id";`);
    this.addSql(`DROP INDEX IF EXISTS "IDX_itinerary_event_event_source";`);
    this.addSql(`DROP INDEX IF EXISTS "IDX_itinerary_event_date_range";`);

    // Remove new columns
    this.addSql(`
      ALTER TABLE "itinerary_event"
      DROP COLUMN IF EXISTS "supplier_offering_id",
      DROP COLUMN IF EXISTS "supplier_id",
      DROP COLUMN IF EXISTS "category_id",
      DROP COLUMN IF EXISTS "from_date",
      DROP COLUMN IF EXISTS "to_date",
      DROP COLUMN IF EXISTS "event_source";
    `);

    // Re-add the CHECK constraint on category field
    this.addSql(`
      ALTER TABLE "itinerary_event"
      ADD CONSTRAINT "itinerary_event_category_check"
      CHECK ("category" IN ('Flight', 'Lodging', 'Activity', 'Cruise', 'Transport', 'Info'));
    `);

    console.log('✅ [Migration] API-based events database rollback completed');
  }
}
