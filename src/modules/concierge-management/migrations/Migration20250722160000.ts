import { Migration } from "@mikro-orm/migrations";

export class Migration20250722160000 extends Migration {
  override async up(): Promise<void> {
    // This migration ensures all array fields are properly formatted
    // and applies the new RobustArrayType schema changes
    
    // First, ensure all existing data is in proper array format
    this.addSql(`
      UPDATE "itinerary_event" 
      SET 
        media = CASE 
          WHEN media IS NULL THEN '[]'::jsonb
          WHEN jsonb_typeof(media) != 'array' THEN '[]'::jsonb
          ELSE media 
        END,
        attachments = CASE 
          WHEN attachments IS NULL THEN '[]'::jsonb
          WHEN jsonb_typeof(attachments) != 'array' THEN '[]'::jsonb
          ELSE attachments 
        END,
        people = CASE 
          WHEN people IS NULL THEN '[]'::jsonb
          WHEN jsonb_typeof(people) != 'array' THEN '[]'::jsonb
          ELSE people 
        END
      WHERE 
        media IS NULL OR jsonb_typeof(media) != 'array' OR
        attachments IS NULL OR jsonb_typeof(attachments) != 'array' OR
        people IS NULL OR jsonb_typeof(people) != 'array';
    `);
    
    // Add a comment to document the custom type usage
    this.addSql(`
      COMMENT ON COLUMN "itinerary_event"."media" IS 'Array of image/video URLs - uses RobustArrayType';
    `);
    
    this.addSql(`
      COMMENT ON COLUMN "itinerary_event"."attachments" IS 'Array of file URLs - uses RobustArrayType';
    `);
    
    this.addSql(`
      COMMENT ON COLUMN "itinerary_event"."people" IS 'Array of user IDs - uses RobustArrayType';
    `);
  }

  override async down(): Promise<void> {
    // Remove comments
    this.addSql(`
      COMMENT ON COLUMN "itinerary_event"."media" IS NULL;
    `);
    
    this.addSql(`
      COMMENT ON COLUMN "itinerary_event"."attachments" IS NULL;
    `);
    
    this.addSql(`
      COMMENT ON COLUMN "itinerary_event"."people" IS NULL;
    `);
  }
}
