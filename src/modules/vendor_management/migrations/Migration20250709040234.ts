import { Migration } from '@mikro-orm/migrations';

export class Migration20250709040234 extends Migration {

  override async up(): Promise<void> {
    this.addSql(`alter table if exists "supplier_contact" add column if not exists "phone_number" text null, add column if not exists "is_whatsapp" boolean not null default false;`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_supplier_contact_phone" ON "supplier_contact" (phone_number) WHERE deleted_at IS NULL AND phone_number IS NOT NULL;`);
  }

  override async down(): Promise<void> {
    this.addSql(`drop index if exists "IDX_supplier_contact_phone";`);
    this.addSql(`alter table if exists "supplier_contact" drop column if exists "phone_number", drop column if exists "is_whatsapp";`);
  }

}
