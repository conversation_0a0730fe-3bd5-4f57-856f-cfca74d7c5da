{"namespaces": ["public"], "name": "public", "tables": [{"columns": {"id": {"name": "id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "supplier_type": {"name": "supplier_type", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "default": "'Company'", "enumItems": ["Company", "Individual"], "mappedType": "enum"}, "name": {"name": "name", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "phone": {"name": "phone", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "text"}, "email": {"name": "email", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "website": {"name": "website", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "text"}, "handle": {"name": "handle", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "text"}, "status": {"name": "status", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "default": "'Pending Approval'", "enumItems": ["Active", "Inactive", "Pending Approval", "Suspended", "Terminated"], "mappedType": "enum"}, "business_type": {"name": "business_type", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "preference": {"name": "preference", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "enumItems": ["Preferred", "Backup"], "mappedType": "enum"}, "region": {"name": "region", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "text"}, "timezone": {"name": "timezone", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "text"}, "language_preference": {"name": "language_preference", "type": "jsonb", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "json"}, "payment_method": {"name": "payment_method", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "text"}, "payout_terms": {"name": "payout_terms", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "text"}, "tax_id": {"name": "tax_id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "text"}, "default_currency": {"name": "default_currency", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "text"}, "bank_account_details": {"name": "bank_account_details", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "text"}, "address_line_1": {"name": "address_line_1", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "text"}, "address_line_2": {"name": "address_line_2", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "text"}, "city": {"name": "city", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "text"}, "state": {"name": "state", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "text"}, "postal_code": {"name": "postal_code", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "text"}, "country": {"name": "country", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "text"}, "metadata": {"name": "metadata", "type": "jsonb", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "json"}, "created_at": {"name": "created_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "default": "now()", "mappedType": "datetime"}, "updated_at": {"name": "updated_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "default": "now()", "mappedType": "datetime"}, "deleted_at": {"name": "deleted_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 6, "mappedType": "datetime"}}, "name": "supplier", "schema": "public", "indexes": [{"keyName": "IDX_supplier_deleted_at", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_supplier_deleted_at\" ON \"supplier\" (deleted_at) WHERE deleted_at IS NULL"}, {"keyName": "IDX_supplier_handle", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_supplier_handle\" ON \"supplier\" (handle) WHERE deleted_at IS NULL"}, {"keyName": "IDX_supplier_status_business_type", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_supplier_status_business_type\" ON \"supplier\" (status, business_type) WHERE deleted_at IS NULL"}, {"keyName": "IDX_supplier_business_type", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_supplier_business_type\" ON \"supplier\" (business_type) WHERE deleted_at IS NULL"}, {"keyName": "IDX_supplier_name", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_supplier_name\" ON \"supplier\" (name) WHERE deleted_at IS NULL"}, {"keyName": "IDX_supplier_status_business_type_name", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_supplier_status_business_type_name\" ON \"supplier\" (status, business_type, name) WHERE deleted_at IS NULL"}, {"keyName": "supplier_pkey", "columnNames": ["id"], "composite": false, "constraint": true, "primary": true, "unique": true}], "checks": [], "foreignKeys": {}, "nativeEnums": {}}, {"columns": {"id": {"name": "id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "name": {"name": "name", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "email": {"name": "email", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "phone_number": {"name": "phone_number", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "text"}, "is_whatsapp": {"name": "is_whatsapp", "type": "boolean", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "default": "false", "mappedType": "boolean"}, "is_primary": {"name": "is_primary", "type": "boolean", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "default": "false", "mappedType": "boolean"}, "supplier_id": {"name": "supplier_id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "created_at": {"name": "created_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "default": "now()", "mappedType": "datetime"}, "updated_at": {"name": "updated_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "default": "now()", "mappedType": "datetime"}, "deleted_at": {"name": "deleted_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 6, "mappedType": "datetime"}}, "name": "supplier_contact", "schema": "public", "indexes": [{"keyName": "IDX_supplier_contact_supplier_id", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_supplier_contact_supplier_id\" ON \"supplier_contact\" (supplier_id) WHERE deleted_at IS NULL"}, {"keyName": "IDX_supplier_contact_deleted_at", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_supplier_contact_deleted_at\" ON \"supplier_contact\" (deleted_at) WHERE deleted_at IS NULL"}, {"keyName": "IDX_supplier_contact_email", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_supplier_contact_email\" ON \"supplier_contact\" (email) WHERE deleted_at IS NULL"}, {"keyName": "IDX_supplier_contact_phone", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_supplier_contact_phone\" ON \"supplier_contact\" (phone_number) WHERE deleted_at IS NULL AND phone_number IS NOT NULL"}, {"keyName": "IDX_supplier_contact_is_primary", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_supplier_contact_is_primary\" ON \"supplier_contact\" (supplier_id, is_primary) WHERE deleted_at IS NULL"}, {"keyName": "IDX_supplier_contact_email_primary", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_supplier_contact_email_primary\" ON \"supplier_contact\" (email, is_primary) WHERE deleted_at IS NULL"}, {"keyName": "IDX_supplier_contact_name_primary", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_supplier_contact_name_primary\" ON \"supplier_contact\" (name, is_primary) WHERE deleted_at IS NULL"}, {"keyName": "supplier_contact_pkey", "columnNames": ["id"], "composite": false, "constraint": true, "primary": true, "unique": true}], "checks": [], "foreignKeys": {"supplier_contact_supplier_id_foreign": {"constraintName": "supplier_contact_supplier_id_foreign", "columnNames": ["supplier_id"], "localTableName": "public.supplier_contact", "referencedColumnNames": ["id"], "referencedTableName": "public.supplier", "updateRule": "cascade"}}, "nativeEnums": {}}, {"columns": {"id": {"name": "id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "contact_type": {"name": "contact_type", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "enumItems": ["email", "phone", "meeting", "site_visit", "system_notification"], "mappedType": "enum"}, "direction": {"name": "direction", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "enumItems": ["inbound", "outbound"], "mappedType": "enum"}, "subject": {"name": "subject", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "content": {"name": "content", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "text"}, "contact_person": {"name": "contact_person", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "text"}, "contact_method": {"name": "contact_method", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "text"}, "status": {"name": "status", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "default": "'completed'", "enumItems": ["completed", "pending_response", "follow_up_required", "cancelled"], "mappedType": "enum"}, "follow_up_date": {"name": "follow_up_date", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 6, "mappedType": "datetime"}, "related_order_id": {"name": "related_order_id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "text"}, "related_contract_id": {"name": "related_contract_id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "text"}, "created_by": {"name": "created_by", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "metadata": {"name": "metadata", "type": "jsonb", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "json"}, "supplier_id": {"name": "supplier_id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "created_at": {"name": "created_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "default": "now()", "mappedType": "datetime"}, "updated_at": {"name": "updated_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "default": "now()", "mappedType": "datetime"}, "deleted_at": {"name": "deleted_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 6, "mappedType": "datetime"}}, "name": "supplier_contact_history", "schema": "public", "indexes": [{"keyName": "IDX_supplier_contact_history_supplier_id", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_supplier_contact_history_supplier_id\" ON \"supplier_contact_history\" (supplier_id) WHERE deleted_at IS NULL"}, {"keyName": "IDX_supplier_contact_history_deleted_at", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_supplier_contact_history_deleted_at\" ON \"supplier_contact_history\" (deleted_at) WHERE deleted_at IS NULL"}, {"keyName": "IDX_supplier_contact_history_type", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_supplier_contact_history_type\" ON \"supplier_contact_history\" (contact_type) WHERE deleted_at IS NULL"}, {"keyName": "IDX_supplier_contact_history_status", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_supplier_contact_history_status\" ON \"supplier_contact_history\" (status) WHERE deleted_at IS NULL"}, {"keyName": "IDX_supplier_contact_history_follow_up", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_supplier_contact_history_follow_up\" ON \"supplier_contact_history\" (follow_up_date) WHERE deleted_at IS NULL AND follow_up_date IS NOT NULL"}, {"keyName": "IDX_supplier_contact_history_created_by", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_supplier_contact_history_created_by\" ON \"supplier_contact_history\" (created_by) WHERE deleted_at IS NULL"}, {"keyName": "supplier_contact_history_pkey", "columnNames": ["id"], "composite": false, "constraint": true, "primary": true, "unique": true}], "checks": [], "foreignKeys": {"supplier_contact_history_supplier_id_foreign": {"constraintName": "supplier_contact_history_supplier_id_foreign", "columnNames": ["supplier_id"], "localTableName": "public.supplier_contact_history", "referencedColumnNames": ["id"], "referencedTableName": "public.supplier", "updateRule": "cascade"}}, "nativeEnums": {}}, {"columns": {"id": {"name": "id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "contract_number": {"name": "contract_number", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "contract_type": {"name": "contract_type", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "enumItems": ["service_agreement", "supply_agreement", "master_agreement", "nda", "maintenance_contract", "other"], "mappedType": "enum"}, "status": {"name": "status", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "default": "'draft'", "enumItems": ["draft", "pending_review", "active", "expired", "terminated", "renewed"], "mappedType": "enum"}, "title": {"name": "title", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "description": {"name": "description", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "text"}, "start_date": {"name": "start_date", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "mappedType": "datetime"}, "end_date": {"name": "end_date", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "mappedType": "datetime"}, "renewal_date": {"name": "renewal_date", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 6, "mappedType": "datetime"}, "notice_period_days": {"name": "notice_period_days", "type": "integer", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "default": "30", "mappedType": "integer"}, "contract_value": {"name": "contract_value", "type": "integer", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "integer"}, "currency_code": {"name": "currency_code", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "default": "'USD'", "mappedType": "text"}, "payment_terms": {"name": "payment_terms", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "text"}, "payment_schedule": {"name": "payment_schedule", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "enumItems": ["one_time", "monthly", "quarterly", "annually", "milestone_based"], "mappedType": "enum"}, "terms_and_conditions": {"name": "terms_and_conditions", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "text"}, "sla_requirements": {"name": "sla_requirements", "type": "jsonb", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "json"}, "penalties": {"name": "penalties", "type": "jsonb", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "json"}, "document_url": {"name": "document_url", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "text"}, "signed_document_url": {"name": "signed_document_url", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "text"}, "auto_renewal": {"name": "auto_renewal", "type": "boolean", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "default": "false", "mappedType": "boolean"}, "auto_renewal_period_months": {"name": "auto_renewal_period_months", "type": "integer", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "integer"}, "metadata": {"name": "metadata", "type": "jsonb", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "json"}, "supplier_id": {"name": "supplier_id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "created_at": {"name": "created_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "default": "now()", "mappedType": "datetime"}, "updated_at": {"name": "updated_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "default": "now()", "mappedType": "datetime"}, "deleted_at": {"name": "deleted_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 6, "mappedType": "datetime"}}, "name": "supplier_contract", "schema": "public", "indexes": [{"keyName": "IDX_supplier_contract_supplier_id", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_supplier_contract_supplier_id\" ON \"supplier_contract\" (supplier_id) WHERE deleted_at IS NULL"}, {"keyName": "IDX_supplier_contract_deleted_at", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_supplier_contract_deleted_at\" ON \"supplier_contract\" (deleted_at) WHERE deleted_at IS NULL"}, {"keyName": "IDX_supplier_contract_number", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE UNIQUE INDEX IF NOT EXISTS \"IDX_supplier_contract_number\" ON \"supplier_contract\" (contract_number) WHERE deleted_at IS NULL"}, {"keyName": "IDX_supplier_contract_status", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_supplier_contract_status\" ON \"supplier_contract\" (status) WHERE deleted_at IS NULL"}, {"keyName": "IDX_supplier_contract_dates", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_supplier_contract_dates\" ON \"supplier_contract\" (start_date, end_date) WHERE deleted_at IS NULL"}, {"keyName": "IDX_supplier_contract_renewal", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_supplier_contract_renewal\" ON \"supplier_contract\" (renewal_date) WHERE deleted_at IS NULL AND renewal_date IS NOT NULL"}, {"keyName": "supplier_contract_pkey", "columnNames": ["id"], "composite": false, "constraint": true, "primary": true, "unique": true}], "checks": [], "foreignKeys": {"supplier_contract_supplier_id_foreign": {"constraintName": "supplier_contract_supplier_id_foreign", "columnNames": ["supplier_id"], "localTableName": "public.supplier_contract", "referencedColumnNames": ["id"], "referencedTableName": "public.supplier", "updateRule": "cascade"}}, "nativeEnums": {}}, {"columns": {"id": {"name": "id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "payment_number": {"name": "payment_number", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "amount": {"name": "amount", "type": "integer", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "integer"}, "currency_code": {"name": "currency_code", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "default": "'USD'", "mappedType": "text"}, "due_date": {"name": "due_date", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "mappedType": "datetime"}, "paid_date": {"name": "paid_date", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 6, "mappedType": "datetime"}, "status": {"name": "status", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "default": "'pending'", "enumItems": ["pending", "paid", "overdue", "cancelled"], "mappedType": "enum"}, "payment_method": {"name": "payment_method", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "text"}, "reference_number": {"name": "reference_number", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "text"}, "notes": {"name": "notes", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "text"}, "contract_id": {"name": "contract_id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "created_at": {"name": "created_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "default": "now()", "mappedType": "datetime"}, "updated_at": {"name": "updated_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "default": "now()", "mappedType": "datetime"}, "deleted_at": {"name": "deleted_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 6, "mappedType": "datetime"}}, "name": "supplier_contract_payment", "schema": "public", "indexes": [{"keyName": "IDX_supplier_contract_payment_contract_id", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_supplier_contract_payment_contract_id\" ON \"supplier_contract_payment\" (contract_id) WHERE deleted_at IS NULL"}, {"keyName": "IDX_supplier_contract_payment_deleted_at", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_supplier_contract_payment_deleted_at\" ON \"supplier_contract_payment\" (deleted_at) WHERE deleted_at IS NULL"}, {"keyName": "IDX_supplier_contract_payment_status", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_supplier_contract_payment_status\" ON \"supplier_contract_payment\" (status) WHERE deleted_at IS NULL"}, {"keyName": "IDX_supplier_contract_payment_due_date", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_supplier_contract_payment_due_date\" ON \"supplier_contract_payment\" (due_date) WHERE deleted_at IS NULL"}, {"keyName": "IDX_supplier_contract_payment_paid_date", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_supplier_contract_payment_paid_date\" ON \"supplier_contract_payment\" (paid_date) WHERE deleted_at IS NULL AND paid_date IS NOT NULL"}, {"keyName": "supplier_contract_payment_pkey", "columnNames": ["id"], "composite": false, "constraint": true, "primary": true, "unique": true}], "checks": [], "foreignKeys": {"supplier_contract_payment_contract_id_foreign": {"constraintName": "supplier_contract_payment_contract_id_foreign", "columnNames": ["contract_id"], "localTableName": "public.supplier_contract_payment", "referencedColumnNames": ["id"], "referencedTableName": "public.supplier_contract", "updateRule": "cascade"}}, "nativeEnums": {}}, {"columns": {"id": {"name": "id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "name": {"name": "name", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "url": {"name": "url", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "metadata": {"name": "metadata", "type": "jsonb", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "json"}, "rank": {"name": "rank", "type": "integer", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "default": "0", "mappedType": "integer"}, "supplier_id": {"name": "supplier_id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "created_at": {"name": "created_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "default": "now()", "mappedType": "datetime"}, "updated_at": {"name": "updated_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "default": "now()", "mappedType": "datetime"}, "deleted_at": {"name": "deleted_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 6, "mappedType": "datetime"}}, "name": "supplier_document", "schema": "public", "indexes": [{"keyName": "IDX_supplier_document_supplier_id", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_supplier_document_supplier_id\" ON \"supplier_document\" (supplier_id) WHERE deleted_at IS NULL"}, {"keyName": "IDX_supplier_document_deleted_at", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_supplier_document_deleted_at\" ON \"supplier_document\" (deleted_at) WHERE deleted_at IS NULL"}, {"keyName": "supplier_document_pkey", "columnNames": ["id"], "composite": false, "constraint": true, "primary": true, "unique": true}], "checks": [], "foreignKeys": {"supplier_document_supplier_id_foreign": {"constraintName": "supplier_document_supplier_id_foreign", "columnNames": ["supplier_id"], "localTableName": "public.supplier_document", "referencedColumnNames": ["id"], "referencedTableName": "public.supplier", "updateRule": "cascade"}}, "nativeEnums": {}}, {"columns": {"id": {"name": "id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "order_number": {"name": "order_number", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "status": {"name": "status", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "default": "'pending'", "enumItems": ["pending", "confirmed", "in_progress", "completed", "cancelled"], "mappedType": "enum"}, "order_type": {"name": "order_type", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "enumItems": ["product", "service", "mixed"], "mappedType": "enum"}, "subtotal": {"name": "subtotal", "type": "integer", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "integer"}, "tax_amount": {"name": "tax_amount", "type": "integer", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "default": "0", "mappedType": "integer"}, "total_amount": {"name": "total_amount", "type": "integer", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "integer"}, "currency_code": {"name": "currency_code", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "default": "'USD'", "mappedType": "text"}, "requested_delivery_date": {"name": "requested_delivery_date", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 6, "mappedType": "datetime"}, "actual_delivery_date": {"name": "actual_delivery_date", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 6, "mappedType": "datetime"}, "delivery_address": {"name": "delivery_address", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "text"}, "notes": {"name": "notes", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "text"}, "internal_notes": {"name": "internal_notes", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "text"}, "customer_name": {"name": "customer_name", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "text"}, "customer_email": {"name": "customer_email", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "text"}, "customer_phone": {"name": "customer_phone", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "text"}, "hotel_id": {"name": "hotel_id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "text"}, "booking_id": {"name": "booking_id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "text"}, "metadata": {"name": "metadata", "type": "jsonb", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "json"}, "supplier_id": {"name": "supplier_id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "created_at": {"name": "created_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "default": "now()", "mappedType": "datetime"}, "updated_at": {"name": "updated_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "default": "now()", "mappedType": "datetime"}, "deleted_at": {"name": "deleted_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 6, "mappedType": "datetime"}}, "name": "supplier_order", "schema": "public", "indexes": [{"keyName": "IDX_supplier_order_supplier_id", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_supplier_order_supplier_id\" ON \"supplier_order\" (supplier_id) WHERE deleted_at IS NULL"}, {"keyName": "IDX_supplier_order_deleted_at", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_supplier_order_deleted_at\" ON \"supplier_order\" (deleted_at) WHERE deleted_at IS NULL"}, {"keyName": "IDX_supplier_order_number", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE UNIQUE INDEX IF NOT EXISTS \"IDX_supplier_order_number\" ON \"supplier_order\" (order_number) WHERE deleted_at IS NULL"}, {"keyName": "IDX_supplier_order_status", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_supplier_order_status\" ON \"supplier_order\" (status) WHERE deleted_at IS NULL"}, {"keyName": "IDX_supplier_order_status_date", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_supplier_order_status_date\" ON \"supplier_order\" (status, created_at) WHERE deleted_at IS NULL"}, {"keyName": "IDX_supplier_order_hotel_booking", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_supplier_order_hotel_booking\" ON \"supplier_order\" (hotel_id, booking_id) WHERE deleted_at IS NULL"}, {"keyName": "IDX_supplier_order_delivery_date", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_supplier_order_delivery_date\" ON \"supplier_order\" (requested_delivery_date) WHERE deleted_at IS NULL"}, {"keyName": "supplier_order_pkey", "columnNames": ["id"], "composite": false, "constraint": true, "primary": true, "unique": true}], "checks": [], "foreignKeys": {"supplier_order_supplier_id_foreign": {"constraintName": "supplier_order_supplier_id_foreign", "columnNames": ["supplier_id"], "localTableName": "public.supplier_order", "referencedColumnNames": ["id"], "referencedTableName": "public.supplier", "updateRule": "cascade"}}, "nativeEnums": {}}, {"columns": {"id": {"name": "id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "item_type": {"name": "item_type", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "enumItems": ["product", "service"], "mappedType": "enum"}, "item_id": {"name": "item_id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "item_name": {"name": "item_name", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "item_description": {"name": "item_description", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "text"}, "quantity": {"name": "quantity", "type": "integer", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "integer"}, "unit_price": {"name": "unit_price", "type": "integer", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "integer"}, "total_price": {"name": "total_price", "type": "integer", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "integer"}, "service_date": {"name": "service_date", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 6, "mappedType": "datetime"}, "service_duration_minutes": {"name": "service_duration_minutes", "type": "integer", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "integer"}, "product_sku": {"name": "product_sku", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "text"}, "specifications": {"name": "specifications", "type": "jsonb", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "json"}, "status": {"name": "status", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "default": "'pending'", "enumItems": ["pending", "confirmed", "in_progress", "completed", "cancelled"], "mappedType": "enum"}, "notes": {"name": "notes", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "text"}, "order_id": {"name": "order_id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "created_at": {"name": "created_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "default": "now()", "mappedType": "datetime"}, "updated_at": {"name": "updated_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "default": "now()", "mappedType": "datetime"}, "deleted_at": {"name": "deleted_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 6, "mappedType": "datetime"}}, "name": "supplier_order_item", "schema": "public", "indexes": [{"keyName": "IDX_supplier_order_item_order_id", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_supplier_order_item_order_id\" ON \"supplier_order_item\" (order_id) WHERE deleted_at IS NULL"}, {"keyName": "IDX_supplier_order_item_deleted_at", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_supplier_order_item_deleted_at\" ON \"supplier_order_item\" (deleted_at) WHERE deleted_at IS NULL"}, {"keyName": "IDX_supplier_order_item_type_id", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_supplier_order_item_type_id\" ON \"supplier_order_item\" (item_type, item_id) WHERE deleted_at IS NULL"}, {"keyName": "IDX_supplier_order_item_status", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_supplier_order_item_status\" ON \"supplier_order_item\" (status) WHERE deleted_at IS NULL"}, {"keyName": "IDX_supplier_order_item_service_date", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_supplier_order_item_service_date\" ON \"supplier_order_item\" (service_date) WHERE deleted_at IS NULL AND service_date IS NOT NULL"}, {"keyName": "supplier_order_item_pkey", "columnNames": ["id"], "composite": false, "constraint": true, "primary": true, "unique": true}], "checks": [], "foreignKeys": {"supplier_order_item_order_id_foreign": {"constraintName": "supplier_order_item_order_id_foreign", "columnNames": ["order_id"], "localTableName": "public.supplier_order_item", "referencedColumnNames": ["id"], "referencedTableName": "public.supplier_order", "updateRule": "cascade"}}, "nativeEnums": {}}, {"columns": {"id": {"name": "id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "name": {"name": "name", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "description": {"name": "description", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "text"}, "sku": {"name": "sku", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "text"}, "category": {"name": "category", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "unit_of_measure": {"name": "unit_of_measure", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "current_stock": {"name": "current_stock", "type": "integer", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "default": "0", "mappedType": "integer"}, "minimum_stock": {"name": "minimum_stock", "type": "integer", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "default": "0", "mappedType": "integer"}, "maximum_stock": {"name": "maximum_stock", "type": "integer", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "integer"}, "specifications": {"name": "specifications", "type": "jsonb", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "json"}, "images": {"name": "images", "type": "jsonb", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "json"}, "is_active": {"name": "is_active", "type": "boolean", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "default": "true", "mappedType": "boolean"}, "supplier_id": {"name": "supplier_id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "created_at": {"name": "created_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "default": "now()", "mappedType": "datetime"}, "updated_at": {"name": "updated_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "default": "now()", "mappedType": "datetime"}, "deleted_at": {"name": "deleted_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 6, "mappedType": "datetime"}}, "name": "supplier_product", "schema": "public", "indexes": [{"keyName": "IDX_supplier_product_supplier_id", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_supplier_product_supplier_id\" ON \"supplier_product\" (supplier_id) WHERE deleted_at IS NULL"}, {"keyName": "IDX_supplier_product_deleted_at", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_supplier_product_deleted_at\" ON \"supplier_product\" (deleted_at) WHERE deleted_at IS NULL"}, {"keyName": "IDX_supplier_product_sku", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_supplier_product_sku\" ON \"supplier_product\" (sku) WHERE deleted_at IS NULL AND sku IS NOT NULL"}, {"keyName": "IDX_supplier_product_category", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_supplier_product_category\" ON \"supplier_product\" (category) WHERE deleted_at IS NULL"}, {"keyName": "IDX_supplier_product_supplier_category", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_supplier_product_supplier_category\" ON \"supplier_product\" (supplier_id, category) WHERE deleted_at IS NULL"}, {"keyName": "IDX_supplier_product_is_active", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_supplier_product_is_active\" ON \"supplier_product\" (is_active) WHERE deleted_at IS NULL"}, {"keyName": "supplier_product_pkey", "columnNames": ["id"], "composite": false, "constraint": true, "primary": true, "unique": true}], "checks": [], "foreignKeys": {"supplier_product_supplier_id_foreign": {"constraintName": "supplier_product_supplier_id_foreign", "columnNames": ["supplier_id"], "localTableName": "public.supplier_product", "referencedColumnNames": ["id"], "referencedTableName": "public.supplier", "updateRule": "cascade"}}, "nativeEnums": {}}, {"columns": {"id": {"name": "id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "pricing_type": {"name": "pricing_type", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "enumItems": ["fixed", "tiered", "seasonal", "bulk_discount", "time_based"], "mappedType": "enum"}, "base_price": {"name": "base_price", "type": "integer", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "integer"}, "currency_code": {"name": "currency_code", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "default": "'USD'", "mappedType": "text"}, "tier_rules": {"name": "tier_rules", "type": "jsonb", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "json"}, "seasonal_rules": {"name": "seasonal_rules", "type": "jsonb", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "json"}, "time_rules": {"name": "time_rules", "type": "jsonb", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "json"}, "bulk_discount_rules": {"name": "bulk_discount_rules", "type": "jsonb", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "json"}, "valid_from": {"name": "valid_from", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 6, "mappedType": "datetime"}, "valid_until": {"name": "valid_until", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 6, "mappedType": "datetime"}, "is_active": {"name": "is_active", "type": "boolean", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "default": "true", "mappedType": "boolean"}, "product_id": {"name": "product_id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "created_at": {"name": "created_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "default": "now()", "mappedType": "datetime"}, "updated_at": {"name": "updated_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "default": "now()", "mappedType": "datetime"}, "deleted_at": {"name": "deleted_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 6, "mappedType": "datetime"}}, "name": "supplier_product_pricing", "schema": "public", "indexes": [{"keyName": "IDX_supplier_product_pricing_product_id", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_supplier_product_pricing_product_id\" ON \"supplier_product_pricing\" (product_id) WHERE deleted_at IS NULL"}, {"keyName": "IDX_supplier_product_pricing_deleted_at", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_supplier_product_pricing_deleted_at\" ON \"supplier_product_pricing\" (deleted_at) WHERE deleted_at IS NULL"}, {"keyName": "IDX_supplier_product_pricing_type", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_supplier_product_pricing_type\" ON \"supplier_product_pricing\" (pricing_type) WHERE deleted_at IS NULL"}, {"keyName": "IDX_supplier_product_pricing_active", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_supplier_product_pricing_active\" ON \"supplier_product_pricing\" (is_active) WHERE deleted_at IS NULL"}, {"keyName": "IDX_supplier_product_pricing_validity", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_supplier_product_pricing_validity\" ON \"supplier_product_pricing\" (valid_from, valid_until) WHERE deleted_at IS NULL"}, {"keyName": "supplier_product_pricing_pkey", "columnNames": ["id"], "composite": false, "constraint": true, "primary": true, "unique": true}], "checks": [], "foreignKeys": {"supplier_product_pricing_product_id_foreign": {"constraintName": "supplier_product_pricing_product_id_foreign", "columnNames": ["product_id"], "localTableName": "public.supplier_product_pricing", "referencedColumnNames": ["id"], "referencedTableName": "public.supplier_product", "updateRule": "cascade"}}, "nativeEnums": {}}, {"columns": {"id": {"name": "id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "name": {"name": "name", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "description": {"name": "description", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "text"}, "category": {"name": "category", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "duration_minutes": {"name": "duration_minutes", "type": "integer", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "integer"}, "capacity": {"name": "capacity", "type": "integer", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "integer"}, "is_available": {"name": "is_available", "type": "boolean", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "default": "true", "mappedType": "boolean"}, "availability_schedule": {"name": "availability_schedule", "type": "jsonb", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "json"}, "requirements": {"name": "requirements", "type": "jsonb", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "json"}, "images": {"name": "images", "type": "jsonb", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "json"}, "supplier_id": {"name": "supplier_id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "created_at": {"name": "created_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "default": "now()", "mappedType": "datetime"}, "updated_at": {"name": "updated_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "default": "now()", "mappedType": "datetime"}, "deleted_at": {"name": "deleted_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 6, "mappedType": "datetime"}}, "name": "supplier_service", "schema": "public", "indexes": [{"keyName": "IDX_supplier_service_supplier_id", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_supplier_service_supplier_id\" ON \"supplier_service\" (supplier_id) WHERE deleted_at IS NULL"}, {"keyName": "IDX_supplier_service_deleted_at", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_supplier_service_deleted_at\" ON \"supplier_service\" (deleted_at) WHERE deleted_at IS NULL"}, {"keyName": "IDX_supplier_service_category", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_supplier_service_category\" ON \"supplier_service\" (category) WHERE deleted_at IS NULL"}, {"keyName": "IDX_supplier_service_supplier_category", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_supplier_service_supplier_category\" ON \"supplier_service\" (supplier_id, category) WHERE deleted_at IS NULL"}, {"keyName": "IDX_supplier_service_is_available", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_supplier_service_is_available\" ON \"supplier_service\" (is_available) WHERE deleted_at IS NULL"}, {"keyName": "supplier_service_pkey", "columnNames": ["id"], "composite": false, "constraint": true, "primary": true, "unique": true}], "checks": [], "foreignKeys": {"supplier_service_supplier_id_foreign": {"constraintName": "supplier_service_supplier_id_foreign", "columnNames": ["supplier_id"], "localTableName": "public.supplier_service", "referencedColumnNames": ["id"], "referencedTableName": "public.supplier", "updateRule": "cascade"}}, "nativeEnums": {}}, {"columns": {"id": {"name": "id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "pricing_type": {"name": "pricing_type", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "enumItems": ["fixed", "hourly", "daily", "weekly", "seasonal", "age_based"], "mappedType": "enum"}, "base_price": {"name": "base_price", "type": "integer", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "integer"}, "currency_code": {"name": "currency_code", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "default": "'USD'", "mappedType": "text"}, "age_rules": {"name": "age_rules", "type": "jsonb", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "json"}, "hourly_rate": {"name": "hourly_rate", "type": "integer", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "integer"}, "daily_rate": {"name": "daily_rate", "type": "integer", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "integer"}, "weekly_rate": {"name": "weekly_rate", "type": "integer", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "integer"}, "seasonal_multipliers": {"name": "seasonal_multipliers", "type": "jsonb", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "json"}, "peak_multiplier": {"name": "peak_multiplier", "type": "integer", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "default": "1", "mappedType": "integer"}, "off_peak_multiplier": {"name": "off_peak_multiplier", "type": "integer", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "default": "1", "mappedType": "integer"}, "valid_from": {"name": "valid_from", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 6, "mappedType": "datetime"}, "valid_until": {"name": "valid_until", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 6, "mappedType": "datetime"}, "is_active": {"name": "is_active", "type": "boolean", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "default": "true", "mappedType": "boolean"}, "service_id": {"name": "service_id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "created_at": {"name": "created_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "default": "now()", "mappedType": "datetime"}, "updated_at": {"name": "updated_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "default": "now()", "mappedType": "datetime"}, "deleted_at": {"name": "deleted_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 6, "mappedType": "datetime"}}, "name": "supplier_service_pricing", "schema": "public", "indexes": [{"keyName": "IDX_supplier_service_pricing_service_id", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_supplier_service_pricing_service_id\" ON \"supplier_service_pricing\" (service_id) WHERE deleted_at IS NULL"}, {"keyName": "IDX_supplier_service_pricing_deleted_at", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_supplier_service_pricing_deleted_at\" ON \"supplier_service_pricing\" (deleted_at) WHERE deleted_at IS NULL"}, {"keyName": "IDX_supplier_service_pricing_type", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_supplier_service_pricing_type\" ON \"supplier_service_pricing\" (pricing_type) WHERE deleted_at IS NULL"}, {"keyName": "IDX_supplier_service_pricing_active", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_supplier_service_pricing_active\" ON \"supplier_service_pricing\" (is_active) WHERE deleted_at IS NULL"}, {"keyName": "IDX_supplier_service_pricing_validity", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_supplier_service_pricing_validity\" ON \"supplier_service_pricing\" (valid_from, valid_until) WHERE deleted_at IS NULL"}, {"keyName": "supplier_service_pricing_pkey", "columnNames": ["id"], "composite": false, "constraint": true, "primary": true, "unique": true}], "checks": [], "foreignKeys": {"supplier_service_pricing_service_id_foreign": {"constraintName": "supplier_service_pricing_service_id_foreign", "columnNames": ["service_id"], "localTableName": "public.supplier_service_pricing", "referencedColumnNames": ["id"], "referencedTableName": "public.supplier_service", "updateRule": "cascade"}}, "nativeEnums": {}}, {"columns": {"id": {"name": "id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "name": {"name": "name", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "handle": {"name": "handle", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "text"}, "description": {"name": "description", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "text"}, "type": {"name": "type", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "enumItems": ["hotel_partner", "service_provider", "transport_provider", "activity_provider", "tour_operator"], "mappedType": "enum"}, "status": {"name": "status", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "default": "'pending_approval'", "enumItems": ["active", "inactive", "pending_approval", "suspended", "terminated"], "mappedType": "enum"}, "website": {"name": "website", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "text"}, "tax_id": {"name": "tax_id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "text"}, "registration_number": {"name": "registration_number", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "text"}, "address": {"name": "address", "type": "jsonb", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "json"}, "metadata": {"name": "metadata", "type": "jsonb", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "json"}, "created_at": {"name": "created_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "default": "now()", "mappedType": "datetime"}, "updated_at": {"name": "updated_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "default": "now()", "mappedType": "datetime"}, "deleted_at": {"name": "deleted_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 6, "mappedType": "datetime"}}, "name": "vendor", "schema": "public", "indexes": [{"keyName": "IDX_vendor_deleted_at", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_vendor_deleted_at\" ON \"vendor\" (deleted_at) WHERE deleted_at IS NULL"}, {"keyName": "IDX_vendor_handle", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_vendor_handle\" ON \"vendor\" (handle) WHERE deleted_at IS NULL"}, {"keyName": "IDX_vendor_status_type", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_vendor_status_type\" ON \"vendor\" (status, type) WHERE deleted_at IS NULL"}, {"keyName": "IDX_vendor_type", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_vendor_type\" ON \"vendor\" (type) WHERE deleted_at IS NULL"}, {"keyName": "vendor_pkey", "columnNames": ["id"], "composite": false, "constraint": true, "primary": true, "unique": true}], "checks": [], "foreignKeys": {}, "nativeEnums": {}}, {"columns": {"id": {"name": "id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "contact_type": {"name": "contact_type", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "enumItems": ["email", "phone", "meeting", "site_visit", "system_notification"], "mappedType": "enum"}, "direction": {"name": "direction", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "enumItems": ["inbound", "outbound"], "mappedType": "enum"}, "subject": {"name": "subject", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "content": {"name": "content", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "text"}, "contact_person": {"name": "contact_person", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "text"}, "contact_method": {"name": "contact_method", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "text"}, "status": {"name": "status", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "default": "'completed'", "enumItems": ["completed", "pending_response", "follow_up_required", "cancelled"], "mappedType": "enum"}, "follow_up_date": {"name": "follow_up_date", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 6, "mappedType": "datetime"}, "related_order_id": {"name": "related_order_id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "text"}, "related_contract_id": {"name": "related_contract_id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "text"}, "created_by": {"name": "created_by", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "metadata": {"name": "metadata", "type": "jsonb", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "json"}, "vendor_id": {"name": "vendor_id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "created_at": {"name": "created_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "default": "now()", "mappedType": "datetime"}, "updated_at": {"name": "updated_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "default": "now()", "mappedType": "datetime"}, "deleted_at": {"name": "deleted_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 6, "mappedType": "datetime"}}, "name": "vendor_contact_history", "schema": "public", "indexes": [{"keyName": "IDX_vendor_contact_history_vendor_id", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_vendor_contact_history_vendor_id\" ON \"vendor_contact_history\" (vendor_id) WHERE deleted_at IS NULL"}, {"keyName": "IDX_vendor_contact_history_deleted_at", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_vendor_contact_history_deleted_at\" ON \"vendor_contact_history\" (deleted_at) WHERE deleted_at IS NULL"}, {"keyName": "IDX_vendor_contact_history_type", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_vendor_contact_history_type\" ON \"vendor_contact_history\" (contact_type) WHERE deleted_at IS NULL"}, {"keyName": "IDX_vendor_contact_history_status", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_vendor_contact_history_status\" ON \"vendor_contact_history\" (status) WHERE deleted_at IS NULL"}, {"keyName": "IDX_vendor_contact_history_follow_up", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_vendor_contact_history_follow_up\" ON \"vendor_contact_history\" (follow_up_date) WHERE deleted_at IS NULL AND follow_up_date IS NOT NULL"}, {"keyName": "IDX_vendor_contact_history_created_by", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_vendor_contact_history_created_by\" ON \"vendor_contact_history\" (created_by) WHERE deleted_at IS NULL"}, {"keyName": "vendor_contact_history_pkey", "columnNames": ["id"], "composite": false, "constraint": true, "primary": true, "unique": true}], "checks": [], "foreignKeys": {"vendor_contact_history_vendor_id_foreign": {"constraintName": "vendor_contact_history_vendor_id_foreign", "columnNames": ["vendor_id"], "localTableName": "public.vendor_contact_history", "referencedColumnNames": ["id"], "referencedTableName": "public.vendor", "updateRule": "cascade"}}, "nativeEnums": {}}, {"columns": {"id": {"name": "id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "contract_number": {"name": "contract_number", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "contract_type": {"name": "contract_type", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "enumItems": ["service_agreement", "supply_agreement", "master_agreement", "nda", "maintenance_contract", "other"], "mappedType": "enum"}, "status": {"name": "status", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "default": "'draft'", "enumItems": ["draft", "pending_review", "active", "expired", "terminated", "renewed"], "mappedType": "enum"}, "title": {"name": "title", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "description": {"name": "description", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "text"}, "start_date": {"name": "start_date", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "mappedType": "datetime"}, "end_date": {"name": "end_date", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "mappedType": "datetime"}, "renewal_date": {"name": "renewal_date", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 6, "mappedType": "datetime"}, "notice_period_days": {"name": "notice_period_days", "type": "integer", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "default": "30", "mappedType": "integer"}, "contract_value": {"name": "contract_value", "type": "integer", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "integer"}, "currency_code": {"name": "currency_code", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "default": "'USD'", "mappedType": "text"}, "payment_terms": {"name": "payment_terms", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "text"}, "payment_schedule": {"name": "payment_schedule", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "enumItems": ["one_time", "monthly", "quarterly", "annually", "milestone_based"], "mappedType": "enum"}, "terms_and_conditions": {"name": "terms_and_conditions", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "text"}, "sla_requirements": {"name": "sla_requirements", "type": "jsonb", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "json"}, "penalties": {"name": "penalties", "type": "jsonb", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "json"}, "document_url": {"name": "document_url", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "text"}, "signed_document_url": {"name": "signed_document_url", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "text"}, "auto_renewal": {"name": "auto_renewal", "type": "boolean", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "default": "false", "mappedType": "boolean"}, "auto_renewal_period_months": {"name": "auto_renewal_period_months", "type": "integer", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "integer"}, "metadata": {"name": "metadata", "type": "jsonb", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "json"}, "vendor_id": {"name": "vendor_id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "created_at": {"name": "created_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "default": "now()", "mappedType": "datetime"}, "updated_at": {"name": "updated_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "default": "now()", "mappedType": "datetime"}, "deleted_at": {"name": "deleted_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 6, "mappedType": "datetime"}}, "name": "vendor_contract", "schema": "public", "indexes": [{"keyName": "IDX_vendor_contract_vendor_id", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_vendor_contract_vendor_id\" ON \"vendor_contract\" (vendor_id) WHERE deleted_at IS NULL"}, {"keyName": "IDX_vendor_contract_deleted_at", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_vendor_contract_deleted_at\" ON \"vendor_contract\" (deleted_at) WHERE deleted_at IS NULL"}, {"keyName": "IDX_vendor_contract_number", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE UNIQUE INDEX IF NOT EXISTS \"IDX_vendor_contract_number\" ON \"vendor_contract\" (contract_number) WHERE deleted_at IS NULL"}, {"keyName": "IDX_vendor_contract_status", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_vendor_contract_status\" ON \"vendor_contract\" (status) WHERE deleted_at IS NULL"}, {"keyName": "IDX_vendor_contract_dates", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_vendor_contract_dates\" ON \"vendor_contract\" (start_date, end_date) WHERE deleted_at IS NULL"}, {"keyName": "IDX_vendor_contract_renewal", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_vendor_contract_renewal\" ON \"vendor_contract\" (renewal_date) WHERE deleted_at IS NULL AND renewal_date IS NOT NULL"}, {"keyName": "vendor_contract_pkey", "columnNames": ["id"], "composite": false, "constraint": true, "primary": true, "unique": true}], "checks": [], "foreignKeys": {"vendor_contract_vendor_id_foreign": {"constraintName": "vendor_contract_vendor_id_foreign", "columnNames": ["vendor_id"], "localTableName": "public.vendor_contract", "referencedColumnNames": ["id"], "referencedTableName": "public.vendor", "updateRule": "cascade"}}, "nativeEnums": {}}, {"columns": {"id": {"name": "id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "payment_number": {"name": "payment_number", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "amount": {"name": "amount", "type": "integer", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "integer"}, "currency_code": {"name": "currency_code", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "default": "'USD'", "mappedType": "text"}, "due_date": {"name": "due_date", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "mappedType": "datetime"}, "paid_date": {"name": "paid_date", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 6, "mappedType": "datetime"}, "status": {"name": "status", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "default": "'pending'", "enumItems": ["pending", "paid", "overdue", "cancelled"], "mappedType": "enum"}, "payment_method": {"name": "payment_method", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "text"}, "reference_number": {"name": "reference_number", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "text"}, "notes": {"name": "notes", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "text"}, "contract_id": {"name": "contract_id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "created_at": {"name": "created_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "default": "now()", "mappedType": "datetime"}, "updated_at": {"name": "updated_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "default": "now()", "mappedType": "datetime"}, "deleted_at": {"name": "deleted_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 6, "mappedType": "datetime"}}, "name": "vendor_contract_payment", "schema": "public", "indexes": [{"keyName": "IDX_vendor_contract_payment_contract_id", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_vendor_contract_payment_contract_id\" ON \"vendor_contract_payment\" (contract_id) WHERE deleted_at IS NULL"}, {"keyName": "IDX_vendor_contract_payment_deleted_at", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_vendor_contract_payment_deleted_at\" ON \"vendor_contract_payment\" (deleted_at) WHERE deleted_at IS NULL"}, {"keyName": "IDX_vendor_contract_payment_status", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_vendor_contract_payment_status\" ON \"vendor_contract_payment\" (status) WHERE deleted_at IS NULL"}, {"keyName": "IDX_vendor_contract_payment_due_date", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_vendor_contract_payment_due_date\" ON \"vendor_contract_payment\" (due_date) WHERE deleted_at IS NULL"}, {"keyName": "IDX_vendor_contract_payment_reference", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_vendor_contract_payment_reference\" ON \"vendor_contract_payment\" (reference_number) WHERE deleted_at IS NULL AND reference_number IS NOT NULL"}, {"keyName": "vendor_contract_payment_pkey", "columnNames": ["id"], "composite": false, "constraint": true, "primary": true, "unique": true}], "checks": [], "foreignKeys": {"vendor_contract_payment_contract_id_foreign": {"constraintName": "vendor_contract_payment_contract_id_foreign", "columnNames": ["contract_id"], "localTableName": "public.vendor_contract_payment", "referencedColumnNames": ["id"], "referencedTableName": "public.vendor_contract", "updateRule": "cascade"}}, "nativeEnums": {}}, {"columns": {"id": {"name": "id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "document_type": {"name": "document_type", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "enumItems": ["contract", "certificate", "insurance", "license", "compliance", "tax_document", "bank_details", "other"], "mappedType": "enum"}, "name": {"name": "name", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "description": {"name": "description", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "text"}, "file_url": {"name": "file_url", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "file_size": {"name": "file_size", "type": "integer", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "integer"}, "file_type": {"name": "file_type", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "text"}, "status": {"name": "status", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "default": "'pending_review'", "enumItems": ["pending_review", "approved", "rejected", "expired"], "mappedType": "enum"}, "expiry_date": {"name": "expiry_date", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 6, "mappedType": "datetime"}, "reminder_days_before": {"name": "reminder_days_before", "type": "integer", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "default": "30", "mappedType": "integer"}, "reviewed_by": {"name": "reviewed_by", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "text"}, "reviewed_at": {"name": "reviewed_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 6, "mappedType": "datetime"}, "review_notes": {"name": "review_notes", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "text"}, "version": {"name": "version", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "default": "'1.0'", "mappedType": "text"}, "is_current_version": {"name": "is_current_version", "type": "boolean", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "default": "true", "mappedType": "boolean"}, "metadata": {"name": "metadata", "type": "jsonb", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "json"}, "vendor_id": {"name": "vendor_id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "parent_document_id": {"name": "parent_document_id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "created_at": {"name": "created_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "default": "now()", "mappedType": "datetime"}, "updated_at": {"name": "updated_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "default": "now()", "mappedType": "datetime"}, "deleted_at": {"name": "deleted_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 6, "mappedType": "datetime"}}, "name": "vendor_document", "schema": "public", "indexes": [{"keyName": "IDX_vendor_document_vendor_id", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_vendor_document_vendor_id\" ON \"vendor_document\" (vendor_id) WHERE deleted_at IS NULL"}, {"keyName": "IDX_vendor_document_parent_document_id", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_vendor_document_parent_document_id\" ON \"vendor_document\" (parent_document_id) WHERE deleted_at IS NULL"}, {"keyName": "IDX_vendor_document_deleted_at", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_vendor_document_deleted_at\" ON \"vendor_document\" (deleted_at) WHERE deleted_at IS NULL"}, {"keyName": "IDX_vendor_document_type", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_vendor_document_type\" ON \"vendor_document\" (document_type) WHERE deleted_at IS NULL"}, {"keyName": "IDX_vendor_document_status", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_vendor_document_status\" ON \"vendor_document\" (status) WHERE deleted_at IS NULL"}, {"keyName": "IDX_vendor_document_expiry", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_vendor_document_expiry\" ON \"vendor_document\" (expiry_date) WHERE deleted_at IS NULL AND expiry_date IS NOT NULL"}, {"keyName": "IDX_vendor_document_current_version", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_vendor_document_current_version\" ON \"vendor_document\" (is_current_version) WHERE deleted_at IS NULL"}, {"keyName": "vendor_document_pkey", "columnNames": ["id"], "composite": false, "constraint": true, "primary": true, "unique": true}], "checks": [], "foreignKeys": {"vendor_document_vendor_id_foreign": {"constraintName": "vendor_document_vendor_id_foreign", "columnNames": ["vendor_id"], "localTableName": "public.vendor_document", "referencedColumnNames": ["id"], "referencedTableName": "public.vendor", "updateRule": "cascade"}, "vendor_document_parent_document_id_foreign": {"constraintName": "vendor_document_parent_document_id_foreign", "columnNames": ["parent_document_id"], "localTableName": "public.vendor_document", "referencedColumnNames": ["id"], "referencedTableName": "public.vendor_document", "updateRule": "cascade"}}, "nativeEnums": {}}, {"columns": {"id": {"name": "id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "order_number": {"name": "order_number", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "status": {"name": "status", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "default": "'pending'", "enumItems": ["pending", "confirmed", "in_progress", "completed", "cancelled"], "mappedType": "enum"}, "order_type": {"name": "order_type", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "enumItems": ["product", "service", "mixed"], "mappedType": "enum"}, "subtotal": {"name": "subtotal", "type": "integer", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "integer"}, "tax_amount": {"name": "tax_amount", "type": "integer", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "default": "0", "mappedType": "integer"}, "total_amount": {"name": "total_amount", "type": "integer", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "integer"}, "currency_code": {"name": "currency_code", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "default": "'USD'", "mappedType": "text"}, "requested_delivery_date": {"name": "requested_delivery_date", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 6, "mappedType": "datetime"}, "actual_delivery_date": {"name": "actual_delivery_date", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 6, "mappedType": "datetime"}, "delivery_address": {"name": "delivery_address", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "text"}, "notes": {"name": "notes", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "text"}, "internal_notes": {"name": "internal_notes", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "text"}, "customer_name": {"name": "customer_name", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "text"}, "customer_email": {"name": "customer_email", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "text"}, "customer_phone": {"name": "customer_phone", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "text"}, "hotel_id": {"name": "hotel_id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "text"}, "booking_id": {"name": "booking_id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "text"}, "metadata": {"name": "metadata", "type": "jsonb", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "json"}, "vendor_id": {"name": "vendor_id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "created_at": {"name": "created_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "default": "now()", "mappedType": "datetime"}, "updated_at": {"name": "updated_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "default": "now()", "mappedType": "datetime"}, "deleted_at": {"name": "deleted_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 6, "mappedType": "datetime"}}, "name": "vendor_order", "schema": "public", "indexes": [{"keyName": "IDX_vendor_order_vendor_id", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_vendor_order_vendor_id\" ON \"vendor_order\" (vendor_id) WHERE deleted_at IS NULL"}, {"keyName": "IDX_vendor_order_deleted_at", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_vendor_order_deleted_at\" ON \"vendor_order\" (deleted_at) WHERE deleted_at IS NULL"}, {"keyName": "IDX_vendor_order_number", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE UNIQUE INDEX IF NOT EXISTS \"IDX_vendor_order_number\" ON \"vendor_order\" (order_number) WHERE deleted_at IS NULL"}, {"keyName": "IDX_vendor_order_status", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_vendor_order_status\" ON \"vendor_order\" (status) WHERE deleted_at IS NULL"}, {"keyName": "IDX_vendor_order_status_date", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_vendor_order_status_date\" ON \"vendor_order\" (status, created_at) WHERE deleted_at IS NULL"}, {"keyName": "IDX_vendor_order_hotel_booking", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_vendor_order_hotel_booking\" ON \"vendor_order\" (hotel_id, booking_id) WHERE deleted_at IS NULL"}, {"keyName": "IDX_vendor_order_delivery_date", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_vendor_order_delivery_date\" ON \"vendor_order\" (requested_delivery_date) WHERE deleted_at IS NULL"}, {"keyName": "vendor_order_pkey", "columnNames": ["id"], "composite": false, "constraint": true, "primary": true, "unique": true}], "checks": [], "foreignKeys": {"vendor_order_vendor_id_foreign": {"constraintName": "vendor_order_vendor_id_foreign", "columnNames": ["vendor_id"], "localTableName": "public.vendor_order", "referencedColumnNames": ["id"], "referencedTableName": "public.vendor", "updateRule": "cascade"}}, "nativeEnums": {}}, {"columns": {"id": {"name": "id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "item_type": {"name": "item_type", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "enumItems": ["product", "service"], "mappedType": "enum"}, "item_id": {"name": "item_id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "item_name": {"name": "item_name", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "item_description": {"name": "item_description", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "text"}, "quantity": {"name": "quantity", "type": "integer", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "integer"}, "unit_price": {"name": "unit_price", "type": "integer", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "integer"}, "total_price": {"name": "total_price", "type": "integer", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "integer"}, "service_date": {"name": "service_date", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 6, "mappedType": "datetime"}, "service_duration_minutes": {"name": "service_duration_minutes", "type": "integer", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "integer"}, "product_sku": {"name": "product_sku", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "text"}, "specifications": {"name": "specifications", "type": "jsonb", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "json"}, "status": {"name": "status", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "default": "'pending'", "enumItems": ["pending", "confirmed", "in_progress", "completed", "cancelled"], "mappedType": "enum"}, "notes": {"name": "notes", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "text"}, "order_id": {"name": "order_id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "created_at": {"name": "created_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "default": "now()", "mappedType": "datetime"}, "updated_at": {"name": "updated_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "default": "now()", "mappedType": "datetime"}, "deleted_at": {"name": "deleted_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 6, "mappedType": "datetime"}}, "name": "vendor_order_item", "schema": "public", "indexes": [{"keyName": "IDX_vendor_order_item_order_id", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_vendor_order_item_order_id\" ON \"vendor_order_item\" (order_id) WHERE deleted_at IS NULL"}, {"keyName": "IDX_vendor_order_item_deleted_at", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_vendor_order_item_deleted_at\" ON \"vendor_order_item\" (deleted_at) WHERE deleted_at IS NULL"}, {"keyName": "IDX_vendor_order_item_type_id", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_vendor_order_item_type_id\" ON \"vendor_order_item\" (item_type, item_id) WHERE deleted_at IS NULL"}, {"keyName": "IDX_vendor_order_item_status", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_vendor_order_item_status\" ON \"vendor_order_item\" (status) WHERE deleted_at IS NULL"}, {"keyName": "IDX_vendor_order_item_service_date", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_vendor_order_item_service_date\" ON \"vendor_order_item\" (service_date) WHERE deleted_at IS NULL AND service_date IS NOT NULL"}, {"keyName": "vendor_order_item_pkey", "columnNames": ["id"], "composite": false, "constraint": true, "primary": true, "unique": true}], "checks": [], "foreignKeys": {"vendor_order_item_order_id_foreign": {"constraintName": "vendor_order_item_order_id_foreign", "columnNames": ["order_id"], "localTableName": "public.vendor_order_item", "referencedColumnNames": ["id"], "referencedTableName": "public.vendor_order", "updateRule": "cascade"}}, "nativeEnums": {}}, {"columns": {"id": {"name": "id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "name": {"name": "name", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "description": {"name": "description", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "text"}, "sku": {"name": "sku", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "text"}, "category": {"name": "category", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "unit_of_measure": {"name": "unit_of_measure", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "current_stock": {"name": "current_stock", "type": "integer", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "default": "0", "mappedType": "integer"}, "minimum_stock": {"name": "minimum_stock", "type": "integer", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "default": "0", "mappedType": "integer"}, "maximum_stock": {"name": "maximum_stock", "type": "integer", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "integer"}, "specifications": {"name": "specifications", "type": "jsonb", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "json"}, "images": {"name": "images", "type": "jsonb", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "json"}, "is_active": {"name": "is_active", "type": "boolean", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "default": "true", "mappedType": "boolean"}, "vendor_id": {"name": "vendor_id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "created_at": {"name": "created_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "default": "now()", "mappedType": "datetime"}, "updated_at": {"name": "updated_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "default": "now()", "mappedType": "datetime"}, "deleted_at": {"name": "deleted_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 6, "mappedType": "datetime"}}, "name": "vendor_product", "schema": "public", "indexes": [{"keyName": "IDX_vendor_product_vendor_id", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_vendor_product_vendor_id\" ON \"vendor_product\" (vendor_id) WHERE deleted_at IS NULL"}, {"keyName": "IDX_vendor_product_deleted_at", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_vendor_product_deleted_at\" ON \"vendor_product\" (deleted_at) WHERE deleted_at IS NULL"}, {"keyName": "IDX_vendor_product_sku", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_vendor_product_sku\" ON \"vendor_product\" (sku) WHERE deleted_at IS NULL AND sku IS NOT NULL"}, {"keyName": "IDX_vendor_product_category", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_vendor_product_category\" ON \"vendor_product\" (category) WHERE deleted_at IS NULL"}, {"keyName": "IDX_vendor_product_vendor_category", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_vendor_product_vendor_category\" ON \"vendor_product\" (vendor_id, category) WHERE deleted_at IS NULL"}, {"keyName": "IDX_vendor_product_is_active", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_vendor_product_is_active\" ON \"vendor_product\" (is_active) WHERE deleted_at IS NULL"}, {"keyName": "vendor_product_pkey", "columnNames": ["id"], "composite": false, "constraint": true, "primary": true, "unique": true}], "checks": [], "foreignKeys": {"vendor_product_vendor_id_foreign": {"constraintName": "vendor_product_vendor_id_foreign", "columnNames": ["vendor_id"], "localTableName": "public.vendor_product", "referencedColumnNames": ["id"], "referencedTableName": "public.vendor", "updateRule": "cascade"}}, "nativeEnums": {}}, {"columns": {"id": {"name": "id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "pricing_type": {"name": "pricing_type", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "enumItems": ["fixed", "tiered", "seasonal", "bulk_discount", "time_based"], "mappedType": "enum"}, "base_price": {"name": "base_price", "type": "integer", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "integer"}, "currency_code": {"name": "currency_code", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "default": "'USD'", "mappedType": "text"}, "tier_rules": {"name": "tier_rules", "type": "jsonb", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "json"}, "seasonal_rules": {"name": "seasonal_rules", "type": "jsonb", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "json"}, "time_rules": {"name": "time_rules", "type": "jsonb", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "json"}, "bulk_discount_rules": {"name": "bulk_discount_rules", "type": "jsonb", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "json"}, "valid_from": {"name": "valid_from", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 6, "mappedType": "datetime"}, "valid_until": {"name": "valid_until", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 6, "mappedType": "datetime"}, "is_active": {"name": "is_active", "type": "boolean", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "default": "true", "mappedType": "boolean"}, "product_id": {"name": "product_id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "created_at": {"name": "created_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "default": "now()", "mappedType": "datetime"}, "updated_at": {"name": "updated_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "default": "now()", "mappedType": "datetime"}, "deleted_at": {"name": "deleted_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 6, "mappedType": "datetime"}}, "name": "vendor_product_pricing", "schema": "public", "indexes": [{"keyName": "IDX_vendor_product_pricing_product_id", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_vendor_product_pricing_product_id\" ON \"vendor_product_pricing\" (product_id) WHERE deleted_at IS NULL"}, {"keyName": "IDX_vendor_product_pricing_deleted_at", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_vendor_product_pricing_deleted_at\" ON \"vendor_product_pricing\" (deleted_at) WHERE deleted_at IS NULL"}, {"keyName": "IDX_vendor_product_pricing_type", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_vendor_product_pricing_type\" ON \"vendor_product_pricing\" (pricing_type) WHERE deleted_at IS NULL"}, {"keyName": "IDX_vendor_product_pricing_active", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_vendor_product_pricing_active\" ON \"vendor_product_pricing\" (is_active) WHERE deleted_at IS NULL"}, {"keyName": "IDX_vendor_product_pricing_validity", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_vendor_product_pricing_validity\" ON \"vendor_product_pricing\" (valid_from, valid_until) WHERE deleted_at IS NULL"}, {"keyName": "vendor_product_pricing_pkey", "columnNames": ["id"], "composite": false, "constraint": true, "primary": true, "unique": true}], "checks": [], "foreignKeys": {"vendor_product_pricing_product_id_foreign": {"constraintName": "vendor_product_pricing_product_id_foreign", "columnNames": ["product_id"], "localTableName": "public.vendor_product_pricing", "referencedColumnNames": ["id"], "referencedTableName": "public.vendor_product", "updateRule": "cascade"}}, "nativeEnums": {}}, {"columns": {"id": {"name": "id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "name": {"name": "name", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "description": {"name": "description", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "text"}, "category": {"name": "category", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "duration_minutes": {"name": "duration_minutes", "type": "integer", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "integer"}, "capacity": {"name": "capacity", "type": "integer", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "integer"}, "is_available": {"name": "is_available", "type": "boolean", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "default": "true", "mappedType": "boolean"}, "availability_schedule": {"name": "availability_schedule", "type": "jsonb", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "json"}, "requirements": {"name": "requirements", "type": "jsonb", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "json"}, "images": {"name": "images", "type": "jsonb", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "json"}, "vendor_id": {"name": "vendor_id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "created_at": {"name": "created_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "default": "now()", "mappedType": "datetime"}, "updated_at": {"name": "updated_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "default": "now()", "mappedType": "datetime"}, "deleted_at": {"name": "deleted_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 6, "mappedType": "datetime"}}, "name": "vendor_service", "schema": "public", "indexes": [{"keyName": "IDX_vendor_service_vendor_id", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_vendor_service_vendor_id\" ON \"vendor_service\" (vendor_id) WHERE deleted_at IS NULL"}, {"keyName": "IDX_vendor_service_deleted_at", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_vendor_service_deleted_at\" ON \"vendor_service\" (deleted_at) WHERE deleted_at IS NULL"}, {"keyName": "IDX_vendor_service_category", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_vendor_service_category\" ON \"vendor_service\" (category) WHERE deleted_at IS NULL"}, {"keyName": "IDX_vendor_service_vendor_category", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_vendor_service_vendor_category\" ON \"vendor_service\" (vendor_id, category) WHERE deleted_at IS NULL"}, {"keyName": "IDX_vendor_service_is_available", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_vendor_service_is_available\" ON \"vendor_service\" (is_available) WHERE deleted_at IS NULL"}, {"keyName": "vendor_service_pkey", "columnNames": ["id"], "composite": false, "constraint": true, "primary": true, "unique": true}], "checks": [], "foreignKeys": {"vendor_service_vendor_id_foreign": {"constraintName": "vendor_service_vendor_id_foreign", "columnNames": ["vendor_id"], "localTableName": "public.vendor_service", "referencedColumnNames": ["id"], "referencedTableName": "public.vendor", "updateRule": "cascade"}}, "nativeEnums": {}}, {"columns": {"id": {"name": "id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "pricing_type": {"name": "pricing_type", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "enumItems": ["fixed", "hourly", "daily", "weekly", "seasonal", "age_based"], "mappedType": "enum"}, "base_price": {"name": "base_price", "type": "integer", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "integer"}, "currency_code": {"name": "currency_code", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "default": "'USD'", "mappedType": "text"}, "age_rules": {"name": "age_rules", "type": "jsonb", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "json"}, "hourly_rate": {"name": "hourly_rate", "type": "integer", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "integer"}, "daily_rate": {"name": "daily_rate", "type": "integer", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "integer"}, "weekly_rate": {"name": "weekly_rate", "type": "integer", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "integer"}, "seasonal_multipliers": {"name": "seasonal_multipliers", "type": "jsonb", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "json"}, "peak_multiplier": {"name": "peak_multiplier", "type": "integer", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "default": "1", "mappedType": "integer"}, "off_peak_multiplier": {"name": "off_peak_multiplier", "type": "integer", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "default": "1", "mappedType": "integer"}, "valid_from": {"name": "valid_from", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 6, "mappedType": "datetime"}, "valid_until": {"name": "valid_until", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 6, "mappedType": "datetime"}, "is_active": {"name": "is_active", "type": "boolean", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "default": "true", "mappedType": "boolean"}, "service_id": {"name": "service_id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "created_at": {"name": "created_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "default": "now()", "mappedType": "datetime"}, "updated_at": {"name": "updated_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "default": "now()", "mappedType": "datetime"}, "deleted_at": {"name": "deleted_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 6, "mappedType": "datetime"}}, "name": "vendor_service_pricing", "schema": "public", "indexes": [{"keyName": "IDX_vendor_service_pricing_service_id", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_vendor_service_pricing_service_id\" ON \"vendor_service_pricing\" (service_id) WHERE deleted_at IS NULL"}, {"keyName": "IDX_vendor_service_pricing_deleted_at", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_vendor_service_pricing_deleted_at\" ON \"vendor_service_pricing\" (deleted_at) WHERE deleted_at IS NULL"}, {"keyName": "IDX_vendor_service_pricing_type", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_vendor_service_pricing_type\" ON \"vendor_service_pricing\" (pricing_type) WHERE deleted_at IS NULL"}, {"keyName": "IDX_vendor_service_pricing_active", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_vendor_service_pricing_active\" ON \"vendor_service_pricing\" (is_active) WHERE deleted_at IS NULL"}, {"keyName": "IDX_vendor_service_pricing_validity", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_vendor_service_pricing_validity\" ON \"vendor_service_pricing\" (valid_from, valid_until) WHERE deleted_at IS NULL"}, {"keyName": "vendor_service_pricing_pkey", "columnNames": ["id"], "composite": false, "constraint": true, "primary": true, "unique": true}], "checks": [], "foreignKeys": {"vendor_service_pricing_service_id_foreign": {"constraintName": "vendor_service_pricing_service_id_foreign", "columnNames": ["service_id"], "localTableName": "public.vendor_service_pricing", "referencedColumnNames": ["id"], "referencedTableName": "public.vendor_service", "updateRule": "cascade"}}, "nativeEnums": {}}], "nativeEnums": {}}