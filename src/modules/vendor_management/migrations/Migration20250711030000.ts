import { Migration } from "@mikro-orm/migrations";

/**
 * Migration to update supplier_type check constraint
 * 
 * Updates the existing supplier_type_check constraint to accept the new capitalized enum values.
 * This fixes the issue where the database constraint still expects old values like 'company', 'individual'
 * but the application is sending new values like 'Company', 'Individual'.
 */
export class Migration20250711030000 extends Migration {
  async up(): Promise<void> {
    // Drop the existing constraint if it exists
    this.addSql(
      `ALTER TABLE "supplier" DROP CONSTRAINT IF EXISTS "supplier_type_check";`
    );

    // Update any existing data to use the new capitalized values BEFORE adding constraint
    this.addSql(
      `UPDATE "supplier" SET "supplier_type" = 'Company' WHERE "supplier_type" = 'company';`
    );
    this.addSql(
      `UPDATE "supplier" SET "supplier_type" = 'Individual' WHERE "supplier_type" = 'individual';`
    );

    // Also update status values if they exist with old format
    this.addSql(
      `UPDATE "supplier" SET "status" = 'Active' WHERE "status" = 'active';`
    );
    this.addSql(
      `UPDATE "supplier" SET "status" = 'Inactive' WHERE "status" = 'inactive';`
    );
    this.addSql(
      `UPDATE "supplier" SET "status" = 'Pending Approval' WHERE "status" = 'pending_approval';`
    );
    this.addSql(
      `UPDATE "supplier" SET "status" = 'Suspended' WHERE "status" = 'suspended';`
    );
    this.addSql(
      `UPDATE "supplier" SET "status" = 'Terminated' WHERE "status" = 'terminated';`
    );

    // Now add the updated constraint with new enum values after data is updated
    this.addSql(
      `ALTER TABLE "supplier" ADD CONSTRAINT "supplier_type_check" CHECK ("supplier_type" IN ('Company', 'Individual'));`
    );
  }

  async down(): Promise<void> {
    // Revert status values back to old format
    this.addSql(
      `UPDATE "supplier" SET "status" = 'active' WHERE "status" = 'Active';`
    );
    this.addSql(
      `UPDATE "supplier" SET "status" = 'inactive' WHERE "status" = 'Inactive';`
    );
    this.addSql(
      `UPDATE "supplier" SET "status" = 'pending_approval' WHERE "status" = 'Pending Approval';`
    );
    this.addSql(
      `UPDATE "supplier" SET "status" = 'suspended' WHERE "status" = 'Suspended';`
    );
    this.addSql(
      `UPDATE "supplier" SET "status" = 'terminated' WHERE "status" = 'Terminated';`
    );

    // Revert supplier_type values back to old format
    this.addSql(
      `UPDATE "supplier" SET "supplier_type" = 'company' WHERE "supplier_type" = 'Company';`
    );
    this.addSql(
      `UPDATE "supplier" SET "supplier_type" = 'individual' WHERE "supplier_type" = 'Individual';`
    );

    // Drop the new constraint
    this.addSql(
      `ALTER TABLE "supplier" DROP CONSTRAINT IF EXISTS "supplier_type_check";`
    );

    // Add back the old constraint
    this.addSql(
      `ALTER TABLE "supplier" ADD CONSTRAINT "supplier_type_check" CHECK ("supplier_type" IN ('company', 'individual'));`
    );
  }
}
