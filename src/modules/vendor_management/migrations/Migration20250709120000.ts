import { Migration } from "@mikro-orm/migrations";

/**
 * Migration to simplify supplier_document table structure
 *
 * This migration simplifies the supplier_document table to follow the hotel-image pattern:
 * - Removes complex document management fields (document_type, status, expiry tracking, etc.)
 * - Removes approval workflow fields (reviewed_by, reviewed_at, review_notes)
 * - Removes version control fields (version, is_current_version, parent_document_id)
 * - Keeps only essential fields: id, url, metadata, rank, supplier_id
 * - Simplifies indexes to only the URL index
 * - Renames file_url to url for consistency
 */
export class Migration20250709120000 extends Migration {
  override async up(): Promise<void> {
    // Step 1: Drop existing indexes that will be removed
    this.addSql(`DROP INDEX IF EXISTS "IDX_supplier_document_type";`);
    this.addSql(`DROP INDEX IF EXISTS "IDX_supplier_document_status";`);
    this.addSql(`DROP INDEX IF EXISTS "IDX_supplier_document_expiry";`);
    this.addSql(
      `DROP INDEX IF EXISTS "IDX_supplier_document_current_version";`
    );
    this.addSql(`DROP INDEX IF EXISTS "IDX_supplier_document_deleted_at";`);

    // Step 2: Create a backup of existing data (optional - for safety)
    this.addSql(
      `CREATE TABLE IF NOT EXISTS "supplier_document_backup" AS SELECT * FROM "supplier_document";`
    );

    // Step 3: Add new columns that we'll keep
    this.addSql(
      `ALTER TABLE "supplier_document" ADD COLUMN IF NOT EXISTS "name" text;`
    );
    this.addSql(
      `ALTER TABLE "supplier_document" ADD COLUMN IF NOT EXISTS "url" text;`
    );
    this.addSql(
      `ALTER TABLE "supplier_document" ADD COLUMN IF NOT EXISTS "rank" integer DEFAULT 0;`
    );

    // Step 4: Migrate existing data - copy file_url to url and set default name
    this.addSql(
      `UPDATE "supplier_document" SET "url" = "file_url" WHERE "url" IS NULL;`
    );
    this.addSql(
      `UPDATE "supplier_document" SET "name" = COALESCE("name", 'Document') WHERE "name" IS NULL;`
    );

    // Step 5: Make required columns NOT NULL after data migration
    this.addSql(
      `ALTER TABLE "supplier_document" ALTER COLUMN "url" SET NOT NULL;`
    );
    this.addSql(
      `ALTER TABLE "supplier_document" ALTER COLUMN "name" SET NOT NULL;`
    );

    // Step 6: Drop all the complex fields we no longer need
    this.addSql(
      `ALTER TABLE "supplier_document" DROP COLUMN IF EXISTS "document_type";`
    );
    this.addSql(
      `ALTER TABLE "supplier_document" DROP COLUMN IF EXISTS "name";`
    );
    this.addSql(
      `ALTER TABLE "supplier_document" DROP COLUMN IF EXISTS "description";`
    );
    this.addSql(
      `ALTER TABLE "supplier_document" DROP COLUMN IF EXISTS "file_url";`
    );
    this.addSql(
      `ALTER TABLE "supplier_document" DROP COLUMN IF EXISTS "file_size";`
    );
    this.addSql(
      `ALTER TABLE "supplier_document" DROP COLUMN IF EXISTS "file_type";`
    );
    this.addSql(
      `ALTER TABLE "supplier_document" DROP COLUMN IF EXISTS "status";`
    );
    this.addSql(
      `ALTER TABLE "supplier_document" DROP COLUMN IF EXISTS "expiry_date";`
    );
    this.addSql(
      `ALTER TABLE "supplier_document" DROP COLUMN IF EXISTS "reminder_days_before";`
    );
    this.addSql(
      `ALTER TABLE "supplier_document" DROP COLUMN IF EXISTS "reviewed_by";`
    );
    this.addSql(
      `ALTER TABLE "supplier_document" DROP COLUMN IF EXISTS "reviewed_at";`
    );
    this.addSql(
      `ALTER TABLE "supplier_document" DROP COLUMN IF EXISTS "review_notes";`
    );
    this.addSql(
      `ALTER TABLE "supplier_document" DROP COLUMN IF EXISTS "version";`
    );
    this.addSql(
      `ALTER TABLE "supplier_document" DROP COLUMN IF EXISTS "is_current_version";`
    );
    this.addSql(
      `ALTER TABLE "supplier_document" DROP COLUMN IF EXISTS "parent_document_id";`
    );

    // Step 7: No indexes needed - keeping it simple like hotel-image model
  }

  override async down(): Promise<void> {
    // Step 1: No indexes to drop since we're keeping it simple

    // Step 2: Add back all the complex fields
    this.addSql(
      `ALTER TABLE "supplier_document" ADD COLUMN IF NOT EXISTS "document_type" text;`
    );
    this.addSql(
      `ALTER TABLE "supplier_document" ADD COLUMN IF NOT EXISTS "name" text;`
    );
    this.addSql(
      `ALTER TABLE "supplier_document" ADD COLUMN IF NOT EXISTS "description" text;`
    );
    this.addSql(
      `ALTER TABLE "supplier_document" ADD COLUMN IF NOT EXISTS "file_url" text;`
    );
    this.addSql(
      `ALTER TABLE "supplier_document" ADD COLUMN IF NOT EXISTS "file_size" integer;`
    );
    this.addSql(
      `ALTER TABLE "supplier_document" ADD COLUMN IF NOT EXISTS "file_type" text;`
    );
    this.addSql(
      `ALTER TABLE "supplier_document" ADD COLUMN IF NOT EXISTS "status" text DEFAULT 'pending_review';`
    );
    this.addSql(
      `ALTER TABLE "supplier_document" ADD COLUMN IF NOT EXISTS "expiry_date" timestamptz;`
    );
    this.addSql(
      `ALTER TABLE "supplier_document" ADD COLUMN IF NOT EXISTS "reminder_days_before" integer DEFAULT 30;`
    );
    this.addSql(
      `ALTER TABLE "supplier_document" ADD COLUMN IF NOT EXISTS "reviewed_by" text;`
    );
    this.addSql(
      `ALTER TABLE "supplier_document" ADD COLUMN IF NOT EXISTS "reviewed_at" timestamptz;`
    );
    this.addSql(
      `ALTER TABLE "supplier_document" ADD COLUMN IF NOT EXISTS "review_notes" text;`
    );
    this.addSql(
      `ALTER TABLE "supplier_document" ADD COLUMN IF NOT EXISTS "version" text DEFAULT '1.0';`
    );
    this.addSql(
      `ALTER TABLE "supplier_document" ADD COLUMN IF NOT EXISTS "is_current_version" boolean DEFAULT true;`
    );
    this.addSql(
      `ALTER TABLE "supplier_document" ADD COLUMN IF NOT EXISTS "parent_document_id" text;`
    );

    // Step 3: Restore data from backup if it exists
    this.addSql(`
      UPDATE "supplier_document" SET 
        "document_type" = backup."document_type",
        "name" = backup."name",
        "description" = backup."description",
        "file_url" = backup."file_url",
        "file_size" = backup."file_size",
        "file_type" = backup."file_type",
        "status" = backup."status",
        "expiry_date" = backup."expiry_date",
        "reminder_days_before" = backup."reminder_days_before",
        "reviewed_by" = backup."reviewed_by",
        "reviewed_at" = backup."reviewed_at",
        "review_notes" = backup."review_notes",
        "version" = backup."version",
        "is_current_version" = backup."is_current_version",
        "parent_document_id" = backup."parent_document_id"
      FROM "supplier_document_backup" backup 
      WHERE "supplier_document"."id" = backup."id";
    `);

    // Step 4: Recreate the original indexes
    this.addSql(
      `CREATE INDEX IF NOT EXISTS "IDX_supplier_document_type" ON "supplier_document" ("document_type") WHERE "deleted_at" IS NULL;`
    );
    this.addSql(
      `CREATE INDEX IF NOT EXISTS "IDX_supplier_document_status" ON "supplier_document" ("status") WHERE "deleted_at" IS NULL;`
    );
    this.addSql(
      `CREATE INDEX IF NOT EXISTS "IDX_supplier_document_expiry" ON "supplier_document" ("expiry_date") WHERE "deleted_at" IS NULL AND "expiry_date" IS NOT NULL;`
    );
    this.addSql(
      `CREATE INDEX IF NOT EXISTS "IDX_supplier_document_current_version" ON "supplier_document" ("is_current_version") WHERE "deleted_at" IS NULL;`
    );
    this.addSql(
      `CREATE INDEX IF NOT EXISTS "IDX_supplier_document_deleted_at" ON "supplier_document" ("deleted_at") WHERE "deleted_at" IS NULL;`
    );

    // Step 5: Drop the new columns
    this.addSql(
      `ALTER TABLE "supplier_document" DROP COLUMN IF EXISTS "name";`
    );
    this.addSql(`ALTER TABLE "supplier_document" DROP COLUMN IF EXISTS "url";`);
    this.addSql(
      `ALTER TABLE "supplier_document" DROP COLUMN IF EXISTS "rank";`
    );

    // Step 6: Drop the backup table
    this.addSql(`DROP TABLE IF EXISTS "supplier_document_backup";`);
  }
}
