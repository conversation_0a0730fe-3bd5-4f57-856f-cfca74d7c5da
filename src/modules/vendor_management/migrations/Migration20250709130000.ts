import { Migration } from '@mikro-orm/migrations';

/**
 * Migration to add name field to supplier_document table
 * 
 * This migration adds the name field to the existing supplier_document table
 * to store document names separately from metadata.
 */
export class Migration20250709130000 extends Migration {

  override async up(): Promise<void> {
    // Add the name column to the supplier_document table
    this.addSql(`ALTER TABLE "supplier_document" ADD COLUMN IF NOT EXISTS "name" text;`);

    // Set default names for existing documents based on metadata or generic name
    this.addSql(`
      UPDATE "supplier_document" 
      SET "name" = COALESCE(
        metadata->>'originalName',
        'Document'
      ) 
      WHERE "name" IS NULL;
    `);

    // Make the name column NOT NULL after setting default values
    this.addSql(`ALTER TABLE "supplier_document" ALTER COLUMN "name" SET NOT NULL;`);
  }

  override async down(): Promise<void> {
    // Drop the name column
    this.addSql(`ALTER TABLE "supplier_document" DROP COLUMN IF EXISTS "name";`);
  }
}
