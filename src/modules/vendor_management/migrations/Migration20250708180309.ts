import { Migration } from '@mikro-orm/migrations';

export class Migration20250708180309 extends Migration {

  override async up(): Promise<void> {
    this.addSql(`alter table if exists "supplier" alter column "bank_account_details" type text using ("bank_account_details"::text);`);
  }

  override async down(): Promise<void> {
    this.addSql(`alter table if exists "supplier" alter column "bank_account_details" type jsonb using ("bank_account_details"::jsonb);`);
  }

}
