import { Migration } from "@mikro-orm/migrations";

export class Migration20250708175725 extends Migration {
  override async up(): Promise<void> {
    this.addSql(
      `create table if not exists "supplier" ("id" text not null, "supplier_type" text check ("supplier_type" in ('company', 'individual')) not null default 'company', "name" text not null, "phone" text null, "email" text not null, "website" text null, "handle" text null, "status" text check ("status" in ('active', 'inactive', 'pending_approval', 'suspended', 'terminated')) not null default 'pending_approval', "business_type" text not null, "region" text null, "timezone" text null, "language_preference" jsonb null, "payment_method" text null, "payout_terms" text null, "tax_id" text null, "default_currency" text null, "bank_account_details" jsonb null, "categories" jsonb null, "address_line_1" text null, "address_line_2" text null, "city" text null, "state" text null, "postal_code" text null, "country" text null, "metadata" jsonb null, "created_at" timestamptz not null default now(), "updated_at" timestamptz not null default now(), "deleted_at" timestamptz null, constraint "supplier_pkey" primary key ("id"));`
    );
    this.addSql(
      `CREATE INDEX IF NOT EXISTS "IDX_supplier_deleted_at" ON "supplier" (deleted_at) WHERE deleted_at IS NULL;`
    );
    this.addSql(
      `CREATE INDEX IF NOT EXISTS "IDX_supplier_handle" ON "supplier" (handle) WHERE deleted_at IS NULL;`
    );
    this.addSql(
      `CREATE INDEX IF NOT EXISTS "IDX_supplier_status_business_type" ON "supplier" (status, business_type) WHERE deleted_at IS NULL;`
    );
    this.addSql(
      `CREATE INDEX IF NOT EXISTS "IDX_supplier_business_type" ON "supplier" (business_type) WHERE deleted_at IS NULL;`
    );

    this.addSql(
      `create table if not exists "supplier_contact" ("id" text not null, "name" text not null, "email" text not null, "is_primary" boolean not null default false, "supplier_id" text not null, "created_at" timestamptz not null default now(), "updated_at" timestamptz not null default now(), "deleted_at" timestamptz null, constraint "supplier_contact_pkey" primary key ("id"));`
    );
    this.addSql(
      `CREATE INDEX IF NOT EXISTS "IDX_supplier_contact_supplier_id" ON "supplier_contact" (supplier_id) WHERE deleted_at IS NULL;`
    );
    this.addSql(
      `CREATE INDEX IF NOT EXISTS "IDX_supplier_contact_deleted_at" ON "supplier_contact" (deleted_at) WHERE deleted_at IS NULL;`
    );
    this.addSql(
      `CREATE INDEX IF NOT EXISTS "IDX_supplier_contact_email" ON "supplier_contact" (email) WHERE deleted_at IS NULL;`
    );
    this.addSql(
      `CREATE INDEX IF NOT EXISTS "IDX_supplier_contact_is_primary" ON "supplier_contact" (supplier_id, is_primary) WHERE deleted_at IS NULL;`
    );

    this.addSql(
      `create table if not exists "supplier_contact_history" ("id" text not null, "contact_type" text check ("contact_type" in ('email', 'phone', 'meeting', 'site_visit', 'system_notification')) not null, "direction" text check ("direction" in ('inbound', 'outbound')) not null, "subject" text not null, "content" text null, "contact_person" text null, "contact_method" text null, "status" text check ("status" in ('completed', 'pending_response', 'follow_up_required', 'cancelled')) not null default 'completed', "follow_up_date" timestamptz null, "related_order_id" text null, "related_contract_id" text null, "created_by" text not null, "metadata" jsonb null, "supplier_id" text not null, "created_at" timestamptz not null default now(), "updated_at" timestamptz not null default now(), "deleted_at" timestamptz null, constraint "supplier_contact_history_pkey" primary key ("id"));`
    );
    this.addSql(
      `CREATE INDEX IF NOT EXISTS "IDX_supplier_contact_history_supplier_id" ON "supplier_contact_history" (supplier_id) WHERE deleted_at IS NULL;`
    );
    this.addSql(
      `CREATE INDEX IF NOT EXISTS "IDX_supplier_contact_history_deleted_at" ON "supplier_contact_history" (deleted_at) WHERE deleted_at IS NULL;`
    );
    this.addSql(
      `CREATE INDEX IF NOT EXISTS "IDX_supplier_contact_history_type" ON "supplier_contact_history" (contact_type) WHERE deleted_at IS NULL;`
    );
    this.addSql(
      `CREATE INDEX IF NOT EXISTS "IDX_supplier_contact_history_status" ON "supplier_contact_history" (status) WHERE deleted_at IS NULL;`
    );
    this.addSql(
      `CREATE INDEX IF NOT EXISTS "IDX_supplier_contact_history_follow_up" ON "supplier_contact_history" (follow_up_date) WHERE deleted_at IS NULL AND follow_up_date IS NOT NULL;`
    );
    this.addSql(
      `CREATE INDEX IF NOT EXISTS "IDX_supplier_contact_history_created_by" ON "supplier_contact_history" (created_by) WHERE deleted_at IS NULL;`
    );

    this.addSql(
      `create table if not exists "supplier_contract" ("id" text not null, "contract_number" text not null, "contract_type" text check ("contract_type" in ('service_agreement', 'supply_agreement', 'master_agreement', 'nda', 'maintenance_contract', 'other')) not null, "status" text check ("status" in ('draft', 'pending_review', 'active', 'expired', 'terminated', 'renewed')) not null default 'draft', "title" text not null, "description" text null, "start_date" timestamptz not null, "end_date" timestamptz not null, "renewal_date" timestamptz null, "notice_period_days" integer not null default 30, "contract_value" integer null, "currency_code" text not null default 'USD', "payment_terms" text null, "payment_schedule" text check ("payment_schedule" in ('one_time', 'monthly', 'quarterly', 'annually', 'milestone_based')) null, "terms_and_conditions" text null, "sla_requirements" jsonb null, "penalties" jsonb null, "document_url" text null, "signed_document_url" text null, "auto_renewal" boolean not null default false, "auto_renewal_period_months" integer null, "metadata" jsonb null, "supplier_id" text not null, "created_at" timestamptz not null default now(), "updated_at" timestamptz not null default now(), "deleted_at" timestamptz null, constraint "supplier_contract_pkey" primary key ("id"));`
    );
    this.addSql(
      `CREATE INDEX IF NOT EXISTS "IDX_supplier_contract_supplier_id" ON "supplier_contract" (supplier_id) WHERE deleted_at IS NULL;`
    );
    this.addSql(
      `CREATE INDEX IF NOT EXISTS "IDX_supplier_contract_deleted_at" ON "supplier_contract" (deleted_at) WHERE deleted_at IS NULL;`
    );
    this.addSql(
      `CREATE UNIQUE INDEX IF NOT EXISTS "IDX_supplier_contract_number" ON "supplier_contract" (contract_number) WHERE deleted_at IS NULL;`
    );
    this.addSql(
      `CREATE INDEX IF NOT EXISTS "IDX_supplier_contract_status" ON "supplier_contract" (status) WHERE deleted_at IS NULL;`
    );
    this.addSql(
      `CREATE INDEX IF NOT EXISTS "IDX_supplier_contract_dates" ON "supplier_contract" (start_date, end_date) WHERE deleted_at IS NULL;`
    );
    this.addSql(
      `CREATE INDEX IF NOT EXISTS "IDX_supplier_contract_renewal" ON "supplier_contract" (renewal_date) WHERE deleted_at IS NULL AND renewal_date IS NOT NULL;`
    );

    this.addSql(
      `create table if not exists "supplier_contract_payment" ("id" text not null, "payment_number" text not null, "amount" integer not null, "currency_code" text not null default 'USD', "due_date" timestamptz not null, "paid_date" timestamptz null, "status" text check ("status" in ('pending', 'paid', 'overdue', 'cancelled')) not null default 'pending', "payment_method" text null, "reference_number" text null, "notes" text null, "contract_id" text not null, "created_at" timestamptz not null default now(), "updated_at" timestamptz not null default now(), "deleted_at" timestamptz null, constraint "supplier_contract_payment_pkey" primary key ("id"));`
    );
    this.addSql(
      `CREATE INDEX IF NOT EXISTS "IDX_supplier_contract_payment_contract_id" ON "supplier_contract_payment" (contract_id) WHERE deleted_at IS NULL;`
    );
    this.addSql(
      `CREATE INDEX IF NOT EXISTS "IDX_supplier_contract_payment_deleted_at" ON "supplier_contract_payment" (deleted_at) WHERE deleted_at IS NULL;`
    );
    this.addSql(
      `CREATE INDEX IF NOT EXISTS "IDX_supplier_contract_payment_status" ON "supplier_contract_payment" (status) WHERE deleted_at IS NULL;`
    );
    this.addSql(
      `CREATE INDEX IF NOT EXISTS "IDX_supplier_contract_payment_due_date" ON "supplier_contract_payment" (due_date) WHERE deleted_at IS NULL;`
    );
    this.addSql(
      `CREATE INDEX IF NOT EXISTS "IDX_supplier_contract_payment_paid_date" ON "supplier_contract_payment" (paid_date) WHERE deleted_at IS NULL AND paid_date IS NOT NULL;`
    );

    this.addSql(
      `create table if not exists "supplier_document" ("id" text not null, "document_type" text check ("document_type" in ('contract', 'certificate', 'insurance', 'license', 'compliance', 'tax_document', 'bank_details', 'other')) not null, "name" text not null, "description" text null, "file_url" text not null, "file_size" integer null, "file_type" text null, "status" text check ("status" in ('pending_review', 'approved', 'rejected', 'expired')) not null default 'pending_review', "expiry_date" timestamptz null, "reminder_days_before" integer not null default 30, "reviewed_by" text null, "reviewed_at" timestamptz null, "review_notes" text null, "version" text not null default '1.0', "is_current_version" boolean not null default true, "parent_document_id" text null, "metadata" jsonb null, "supplier_id" text not null, "created_at" timestamptz not null default now(), "updated_at" timestamptz not null default now(), "deleted_at" timestamptz null, constraint "supplier_document_pkey" primary key ("id"));`
    );
    this.addSql(
      `CREATE INDEX IF NOT EXISTS "IDX_supplier_document_supplier_id" ON "supplier_document" (supplier_id) WHERE deleted_at IS NULL;`
    );
    this.addSql(
      `CREATE INDEX IF NOT EXISTS "IDX_supplier_document_deleted_at" ON "supplier_document" (deleted_at) WHERE deleted_at IS NULL;`
    );
    this.addSql(
      `CREATE INDEX IF NOT EXISTS "IDX_supplier_document_type" ON "supplier_document" (document_type) WHERE deleted_at IS NULL;`
    );
    this.addSql(
      `CREATE INDEX IF NOT EXISTS "IDX_supplier_document_status" ON "supplier_document" (status) WHERE deleted_at IS NULL;`
    );
    this.addSql(
      `CREATE INDEX IF NOT EXISTS "IDX_supplier_document_expiry" ON "supplier_document" (expiry_date) WHERE deleted_at IS NULL AND expiry_date IS NOT NULL;`
    );
    this.addSql(
      `CREATE INDEX IF NOT EXISTS "IDX_supplier_document_current_version" ON "supplier_document" (is_current_version) WHERE deleted_at IS NULL;`
    );

    this.addSql(
      `create table if not exists "supplier_order" ("id" text not null, "order_number" text not null, "status" text check ("status" in ('pending', 'confirmed', 'in_progress', 'completed', 'cancelled')) not null default 'pending', "order_type" text check ("order_type" in ('product', 'service', 'mixed')) not null, "subtotal" integer not null, "tax_amount" integer not null default 0, "total_amount" integer not null, "currency_code" text not null default 'USD', "requested_delivery_date" timestamptz null, "actual_delivery_date" timestamptz null, "delivery_address" text null, "notes" text null, "internal_notes" text null, "customer_name" text null, "customer_email" text null, "customer_phone" text null, "hotel_id" text null, "booking_id" text null, "metadata" jsonb null, "supplier_id" text not null, "created_at" timestamptz not null default now(), "updated_at" timestamptz not null default now(), "deleted_at" timestamptz null, constraint "supplier_order_pkey" primary key ("id"));`
    );
    this.addSql(
      `CREATE INDEX IF NOT EXISTS "IDX_supplier_order_supplier_id" ON "supplier_order" (supplier_id) WHERE deleted_at IS NULL;`
    );
    this.addSql(
      `CREATE INDEX IF NOT EXISTS "IDX_supplier_order_deleted_at" ON "supplier_order" (deleted_at) WHERE deleted_at IS NULL;`
    );
    this.addSql(
      `CREATE UNIQUE INDEX IF NOT EXISTS "IDX_supplier_order_number" ON "supplier_order" (order_number) WHERE deleted_at IS NULL;`
    );
    this.addSql(
      `CREATE INDEX IF NOT EXISTS "IDX_supplier_order_status" ON "supplier_order" (status) WHERE deleted_at IS NULL;`
    );
    this.addSql(
      `CREATE INDEX IF NOT EXISTS "IDX_supplier_order_status_date" ON "supplier_order" (status, created_at) WHERE deleted_at IS NULL;`
    );
    this.addSql(
      `CREATE INDEX IF NOT EXISTS "IDX_supplier_order_hotel_booking" ON "supplier_order" (hotel_id, booking_id) WHERE deleted_at IS NULL;`
    );
    this.addSql(
      `CREATE INDEX IF NOT EXISTS "IDX_supplier_order_delivery_date" ON "supplier_order" (requested_delivery_date) WHERE deleted_at IS NULL;`
    );

    this.addSql(
      `create table if not exists "supplier_order_item" ("id" text not null, "item_type" text check ("item_type" in ('product', 'service')) not null, "item_id" text not null, "item_name" text not null, "item_description" text null, "quantity" integer not null, "unit_price" integer not null, "total_price" integer not null, "service_date" timestamptz null, "service_duration_minutes" integer null, "product_sku" text null, "specifications" jsonb null, "status" text check ("status" in ('pending', 'confirmed', 'in_progress', 'completed', 'cancelled')) not null default 'pending', "notes" text null, "order_id" text not null, "created_at" timestamptz not null default now(), "updated_at" timestamptz not null default now(), "deleted_at" timestamptz null, constraint "supplier_order_item_pkey" primary key ("id"));`
    );
    this.addSql(
      `CREATE INDEX IF NOT EXISTS "IDX_supplier_order_item_order_id" ON "supplier_order_item" (order_id) WHERE deleted_at IS NULL;`
    );
    this.addSql(
      `CREATE INDEX IF NOT EXISTS "IDX_supplier_order_item_deleted_at" ON "supplier_order_item" (deleted_at) WHERE deleted_at IS NULL;`
    );
    this.addSql(
      `CREATE INDEX IF NOT EXISTS "IDX_supplier_order_item_type_id" ON "supplier_order_item" (item_type, item_id) WHERE deleted_at IS NULL;`
    );
    this.addSql(
      `CREATE INDEX IF NOT EXISTS "IDX_supplier_order_item_status" ON "supplier_order_item" (status) WHERE deleted_at IS NULL;`
    );
    this.addSql(
      `CREATE INDEX IF NOT EXISTS "IDX_supplier_order_item_service_date" ON "supplier_order_item" (service_date) WHERE deleted_at IS NULL AND service_date IS NOT NULL;`
    );

    this.addSql(
      `create table if not exists "supplier_product" ("id" text not null, "name" text not null, "description" text null, "sku" text null, "category" text not null, "unit_of_measure" text not null, "current_stock" integer not null default 0, "minimum_stock" integer not null default 0, "maximum_stock" integer null, "specifications" jsonb null, "images" jsonb null, "is_active" boolean not null default true, "supplier_id" text not null, "created_at" timestamptz not null default now(), "updated_at" timestamptz not null default now(), "deleted_at" timestamptz null, constraint "supplier_product_pkey" primary key ("id"));`
    );
    this.addSql(
      `CREATE INDEX IF NOT EXISTS "IDX_supplier_product_supplier_id" ON "supplier_product" (supplier_id) WHERE deleted_at IS NULL;`
    );
    this.addSql(
      `CREATE INDEX IF NOT EXISTS "IDX_supplier_product_deleted_at" ON "supplier_product" (deleted_at) WHERE deleted_at IS NULL;`
    );
    this.addSql(
      `CREATE INDEX IF NOT EXISTS "IDX_supplier_product_sku" ON "supplier_product" (sku) WHERE deleted_at IS NULL AND sku IS NOT NULL;`
    );
    this.addSql(
      `CREATE INDEX IF NOT EXISTS "IDX_supplier_product_category" ON "supplier_product" (category) WHERE deleted_at IS NULL;`
    );
    this.addSql(
      `CREATE INDEX IF NOT EXISTS "IDX_supplier_product_supplier_category" ON "supplier_product" (supplier_id, category) WHERE deleted_at IS NULL;`
    );
    this.addSql(
      `CREATE INDEX IF NOT EXISTS "IDX_supplier_product_is_active" ON "supplier_product" (is_active) WHERE deleted_at IS NULL;`
    );

    this.addSql(
      `create table if not exists "supplier_product_pricing" ("id" text not null, "pricing_type" text check ("pricing_type" in ('fixed', 'tiered', 'seasonal', 'bulk_discount', 'time_based')) not null, "base_price" integer not null, "currency_code" text not null default 'USD', "tier_rules" jsonb null, "seasonal_rules" jsonb null, "time_rules" jsonb null, "bulk_discount_rules" jsonb null, "valid_from" timestamptz null, "valid_until" timestamptz null, "is_active" boolean not null default true, "product_id" text not null, "created_at" timestamptz not null default now(), "updated_at" timestamptz not null default now(), "deleted_at" timestamptz null, constraint "supplier_product_pricing_pkey" primary key ("id"));`
    );
    this.addSql(
      `CREATE INDEX IF NOT EXISTS "IDX_supplier_product_pricing_product_id" ON "supplier_product_pricing" (product_id) WHERE deleted_at IS NULL;`
    );
    this.addSql(
      `CREATE INDEX IF NOT EXISTS "IDX_supplier_product_pricing_deleted_at" ON "supplier_product_pricing" (deleted_at) WHERE deleted_at IS NULL;`
    );
    this.addSql(
      `CREATE INDEX IF NOT EXISTS "IDX_supplier_product_pricing_type" ON "supplier_product_pricing" (pricing_type) WHERE deleted_at IS NULL;`
    );
    this.addSql(
      `CREATE INDEX IF NOT EXISTS "IDX_supplier_product_pricing_active" ON "supplier_product_pricing" (is_active) WHERE deleted_at IS NULL;`
    );
    this.addSql(
      `CREATE INDEX IF NOT EXISTS "IDX_supplier_product_pricing_validity" ON "supplier_product_pricing" (valid_from, valid_until) WHERE deleted_at IS NULL;`
    );

    this.addSql(
      `create table if not exists "supplier_service" ("id" text not null, "name" text not null, "description" text null, "category" text not null, "duration_minutes" integer null, "capacity" integer null, "is_available" boolean not null default true, "availability_schedule" jsonb null, "requirements" jsonb null, "images" jsonb null, "supplier_id" text not null, "created_at" timestamptz not null default now(), "updated_at" timestamptz not null default now(), "deleted_at" timestamptz null, constraint "supplier_service_pkey" primary key ("id"));`
    );
    this.addSql(
      `CREATE INDEX IF NOT EXISTS "IDX_supplier_service_supplier_id" ON "supplier_service" (supplier_id) WHERE deleted_at IS NULL;`
    );
    this.addSql(
      `CREATE INDEX IF NOT EXISTS "IDX_supplier_service_deleted_at" ON "supplier_service" (deleted_at) WHERE deleted_at IS NULL;`
    );
    this.addSql(
      `CREATE INDEX IF NOT EXISTS "IDX_supplier_service_category" ON "supplier_service" (category) WHERE deleted_at IS NULL;`
    );
    this.addSql(
      `CREATE INDEX IF NOT EXISTS "IDX_supplier_service_supplier_category" ON "supplier_service" (supplier_id, category) WHERE deleted_at IS NULL;`
    );
    this.addSql(
      `CREATE INDEX IF NOT EXISTS "IDX_supplier_service_is_available" ON "supplier_service" (is_available) WHERE deleted_at IS NULL;`
    );

    this.addSql(
      `create table if not exists "supplier_service_pricing" ("id" text not null, "pricing_type" text check ("pricing_type" in ('fixed', 'hourly', 'daily', 'weekly', 'seasonal', 'age_based')) not null, "base_price" integer not null, "currency_code" text not null default 'USD', "age_rules" jsonb null, "hourly_rate" integer null, "daily_rate" integer null, "weekly_rate" integer null, "seasonal_multipliers" jsonb null, "peak_multiplier" integer not null default 1, "off_peak_multiplier" integer not null default 1, "valid_from" timestamptz null, "valid_until" timestamptz null, "is_active" boolean not null default true, "service_id" text not null, "created_at" timestamptz not null default now(), "updated_at" timestamptz not null default now(), "deleted_at" timestamptz null, constraint "supplier_service_pricing_pkey" primary key ("id"));`
    );
    this.addSql(
      `CREATE INDEX IF NOT EXISTS "IDX_supplier_service_pricing_service_id" ON "supplier_service_pricing" (service_id) WHERE deleted_at IS NULL;`
    );
    this.addSql(
      `CREATE INDEX IF NOT EXISTS "IDX_supplier_service_pricing_deleted_at" ON "supplier_service_pricing" (deleted_at) WHERE deleted_at IS NULL;`
    );
    this.addSql(
      `CREATE INDEX IF NOT EXISTS "IDX_supplier_service_pricing_type" ON "supplier_service_pricing" (pricing_type) WHERE deleted_at IS NULL;`
    );
    this.addSql(
      `CREATE INDEX IF NOT EXISTS "IDX_supplier_service_pricing_active" ON "supplier_service_pricing" (is_active) WHERE deleted_at IS NULL;`
    );
    this.addSql(
      `CREATE INDEX IF NOT EXISTS "IDX_supplier_service_pricing_validity" ON "supplier_service_pricing" (valid_from, valid_until) WHERE deleted_at IS NULL;`
    );

    this.addSql(
      `create table if not exists "vendor" ("id" text not null, "name" text not null, "handle" text null, "description" text null, "type" text check ("type" in ('hotel_partner', 'service_provider', 'transport_provider', 'activity_provider', 'tour_operator')) not null, "status" text check ("status" in ('active', 'inactive', 'pending_approval', 'suspended', 'terminated')) not null default 'pending_approval', "website" text null, "tax_id" text null, "registration_number" text null, "address" jsonb null, "metadata" jsonb null, "created_at" timestamptz not null default now(), "updated_at" timestamptz not null default now(), "deleted_at" timestamptz null, constraint "vendor_pkey" primary key ("id"));`
    );
    this.addSql(
      `CREATE INDEX IF NOT EXISTS "IDX_vendor_deleted_at" ON "vendor" (deleted_at) WHERE deleted_at IS NULL;`
    );
    this.addSql(
      `CREATE INDEX IF NOT EXISTS "IDX_vendor_handle" ON "vendor" (handle) WHERE deleted_at IS NULL;`
    );
    // Only create index if type column exists
    this.addSql(`
      DO $$
      BEGIN
        IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'vendor' AND column_name = 'type') THEN
          CREATE INDEX IF NOT EXISTS "IDX_vendor_status_type" ON "vendor" (status, type) WHERE deleted_at IS NULL;
        END IF;
      END $$;
    `);
    // Only create index if type column exists
    this.addSql(`
      DO $$
      BEGIN
        IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'vendor' AND column_name = 'type') THEN
          CREATE INDEX IF NOT EXISTS "IDX_vendor_type" ON "vendor" (type) WHERE deleted_at IS NULL;
        END IF;
      END $$;
    `);

    this.addSql(
      `create table if not exists "vendor_contact_history" ("id" text not null, "contact_type" text check ("contact_type" in ('email', 'phone', 'meeting', 'site_visit', 'system_notification')) not null, "direction" text check ("direction" in ('inbound', 'outbound')) not null, "subject" text not null, "content" text null, "contact_person" text null, "contact_method" text null, "status" text check ("status" in ('completed', 'pending_response', 'follow_up_required', 'cancelled')) not null default 'completed', "follow_up_date" timestamptz null, "related_order_id" text null, "related_contract_id" text null, "created_by" text not null, "metadata" jsonb null, "vendor_id" text not null, "created_at" timestamptz not null default now(), "updated_at" timestamptz not null default now(), "deleted_at" timestamptz null, constraint "vendor_contact_history_pkey" primary key ("id"));`
    );
    this.addSql(
      `CREATE INDEX IF NOT EXISTS "IDX_vendor_contact_history_vendor_id" ON "vendor_contact_history" (vendor_id) WHERE deleted_at IS NULL;`
    );
    this.addSql(
      `CREATE INDEX IF NOT EXISTS "IDX_vendor_contact_history_deleted_at" ON "vendor_contact_history" (deleted_at) WHERE deleted_at IS NULL;`
    );
    this.addSql(
      `CREATE INDEX IF NOT EXISTS "IDX_vendor_contact_history_type" ON "vendor_contact_history" (contact_type) WHERE deleted_at IS NULL;`
    );
    this.addSql(
      `CREATE INDEX IF NOT EXISTS "IDX_vendor_contact_history_status" ON "vendor_contact_history" (status) WHERE deleted_at IS NULL;`
    );
    this.addSql(
      `CREATE INDEX IF NOT EXISTS "IDX_vendor_contact_history_follow_up" ON "vendor_contact_history" (follow_up_date) WHERE deleted_at IS NULL AND follow_up_date IS NOT NULL;`
    );
    this.addSql(
      `CREATE INDEX IF NOT EXISTS "IDX_vendor_contact_history_created_by" ON "vendor_contact_history" (created_by) WHERE deleted_at IS NULL;`
    );

    this.addSql(
      `create table if not exists "vendor_contract" ("id" text not null, "contract_number" text not null, "contract_type" text check ("contract_type" in ('service_agreement', 'supply_agreement', 'master_agreement', 'nda', 'maintenance_contract', 'other')) not null, "status" text check ("status" in ('draft', 'pending_review', 'active', 'expired', 'terminated', 'renewed')) not null default 'draft', "title" text not null, "description" text null, "start_date" timestamptz not null, "end_date" timestamptz not null, "renewal_date" timestamptz null, "notice_period_days" integer not null default 30, "contract_value" integer null, "currency_code" text not null default 'USD', "payment_terms" text null, "payment_schedule" text check ("payment_schedule" in ('one_time', 'monthly', 'quarterly', 'annually', 'milestone_based')) null, "terms_and_conditions" text null, "sla_requirements" jsonb null, "penalties" jsonb null, "document_url" text null, "signed_document_url" text null, "auto_renewal" boolean not null default false, "auto_renewal_period_months" integer null, "metadata" jsonb null, "vendor_id" text not null, "created_at" timestamptz not null default now(), "updated_at" timestamptz not null default now(), "deleted_at" timestamptz null, constraint "vendor_contract_pkey" primary key ("id"));`
    );
    this.addSql(
      `CREATE INDEX IF NOT EXISTS "IDX_vendor_contract_vendor_id" ON "vendor_contract" (vendor_id) WHERE deleted_at IS NULL;`
    );
    this.addSql(
      `CREATE INDEX IF NOT EXISTS "IDX_vendor_contract_deleted_at" ON "vendor_contract" (deleted_at) WHERE deleted_at IS NULL;`
    );
    this.addSql(
      `CREATE UNIQUE INDEX IF NOT EXISTS "IDX_vendor_contract_number" ON "vendor_contract" (contract_number) WHERE deleted_at IS NULL;`
    );
    this.addSql(
      `CREATE INDEX IF NOT EXISTS "IDX_vendor_contract_status" ON "vendor_contract" (status) WHERE deleted_at IS NULL;`
    );
    this.addSql(
      `CREATE INDEX IF NOT EXISTS "IDX_vendor_contract_dates" ON "vendor_contract" (start_date, end_date) WHERE deleted_at IS NULL;`
    );
    this.addSql(
      `CREATE INDEX IF NOT EXISTS "IDX_vendor_contract_renewal" ON "vendor_contract" (renewal_date) WHERE deleted_at IS NULL AND renewal_date IS NOT NULL;`
    );

    this.addSql(
      `create table if not exists "vendor_contract_payment" ("id" text not null, "payment_number" text not null, "amount" integer not null, "currency_code" text not null default 'USD', "due_date" timestamptz not null, "paid_date" timestamptz null, "status" text check ("status" in ('pending', 'paid', 'overdue', 'cancelled')) not null default 'pending', "payment_method" text null, "reference_number" text null, "notes" text null, "contract_id" text not null, "created_at" timestamptz not null default now(), "updated_at" timestamptz not null default now(), "deleted_at" timestamptz null, constraint "vendor_contract_payment_pkey" primary key ("id"));`
    );
    this.addSql(
      `CREATE INDEX IF NOT EXISTS "IDX_vendor_contract_payment_contract_id" ON "vendor_contract_payment" (contract_id) WHERE deleted_at IS NULL;`
    );
    this.addSql(
      `CREATE INDEX IF NOT EXISTS "IDX_vendor_contract_payment_deleted_at" ON "vendor_contract_payment" (deleted_at) WHERE deleted_at IS NULL;`
    );
    this.addSql(
      `CREATE INDEX IF NOT EXISTS "IDX_vendor_contract_payment_status" ON "vendor_contract_payment" (status) WHERE deleted_at IS NULL;`
    );
    this.addSql(
      `CREATE INDEX IF NOT EXISTS "IDX_vendor_contract_payment_due_date" ON "vendor_contract_payment" (due_date) WHERE deleted_at IS NULL;`
    );
    this.addSql(
      `CREATE INDEX IF NOT EXISTS "IDX_vendor_contract_payment_reference" ON "vendor_contract_payment" (reference_number) WHERE deleted_at IS NULL AND reference_number IS NOT NULL;`
    );

    this.addSql(
      `create table if not exists "vendor_document" ("id" text not null, "document_type" text check ("document_type" in ('contract', 'certificate', 'insurance', 'license', 'compliance', 'tax_document', 'bank_details', 'other')) not null, "name" text not null, "description" text null, "file_url" text not null, "file_size" integer null, "file_type" text null, "status" text check ("status" in ('pending_review', 'approved', 'rejected', 'expired')) not null default 'pending_review', "expiry_date" timestamptz null, "reminder_days_before" integer not null default 30, "reviewed_by" text null, "reviewed_at" timestamptz null, "review_notes" text null, "version" text not null default '1.0', "is_current_version" boolean not null default true, "metadata" jsonb null, "vendor_id" text not null, "parent_document_id" text not null, "created_at" timestamptz not null default now(), "updated_at" timestamptz not null default now(), "deleted_at" timestamptz null, constraint "vendor_document_pkey" primary key ("id"));`
    );
    this.addSql(
      `CREATE INDEX IF NOT EXISTS "IDX_vendor_document_vendor_id" ON "vendor_document" (vendor_id) WHERE deleted_at IS NULL;`
    );
    this.addSql(
      `CREATE INDEX IF NOT EXISTS "IDX_vendor_document_parent_document_id" ON "vendor_document" (parent_document_id) WHERE deleted_at IS NULL;`
    );
    this.addSql(
      `CREATE INDEX IF NOT EXISTS "IDX_vendor_document_deleted_at" ON "vendor_document" (deleted_at) WHERE deleted_at IS NULL;`
    );
    this.addSql(
      `CREATE INDEX IF NOT EXISTS "IDX_vendor_document_type" ON "vendor_document" (document_type) WHERE deleted_at IS NULL;`
    );
    this.addSql(
      `CREATE INDEX IF NOT EXISTS "IDX_vendor_document_status" ON "vendor_document" (status) WHERE deleted_at IS NULL;`
    );
    this.addSql(
      `CREATE INDEX IF NOT EXISTS "IDX_vendor_document_expiry" ON "vendor_document" (expiry_date) WHERE deleted_at IS NULL AND expiry_date IS NOT NULL;`
    );
    this.addSql(
      `CREATE INDEX IF NOT EXISTS "IDX_vendor_document_current_version" ON "vendor_document" (is_current_version) WHERE deleted_at IS NULL;`
    );

    this.addSql(
      `create table if not exists "vendor_order" ("id" text not null, "order_number" text not null, "status" text check ("status" in ('pending', 'confirmed', 'in_progress', 'completed', 'cancelled')) not null default 'pending', "order_type" text check ("order_type" in ('product', 'service', 'mixed')) not null, "subtotal" integer not null, "tax_amount" integer not null default 0, "total_amount" integer not null, "currency_code" text not null default 'USD', "requested_delivery_date" timestamptz null, "actual_delivery_date" timestamptz null, "delivery_address" text null, "notes" text null, "internal_notes" text null, "customer_name" text null, "customer_email" text null, "customer_phone" text null, "hotel_id" text null, "booking_id" text null, "metadata" jsonb null, "vendor_id" text not null, "created_at" timestamptz not null default now(), "updated_at" timestamptz not null default now(), "deleted_at" timestamptz null, constraint "vendor_order_pkey" primary key ("id"));`
    );
    this.addSql(
      `CREATE INDEX IF NOT EXISTS "IDX_vendor_order_vendor_id" ON "vendor_order" (vendor_id) WHERE deleted_at IS NULL;`
    );
    this.addSql(
      `CREATE INDEX IF NOT EXISTS "IDX_vendor_order_deleted_at" ON "vendor_order" (deleted_at) WHERE deleted_at IS NULL;`
    );
    this.addSql(
      `CREATE UNIQUE INDEX IF NOT EXISTS "IDX_vendor_order_number" ON "vendor_order" (order_number) WHERE deleted_at IS NULL;`
    );
    this.addSql(
      `CREATE INDEX IF NOT EXISTS "IDX_vendor_order_status" ON "vendor_order" (status) WHERE deleted_at IS NULL;`
    );
    this.addSql(
      `CREATE INDEX IF NOT EXISTS "IDX_vendor_order_status_date" ON "vendor_order" (status, created_at) WHERE deleted_at IS NULL;`
    );
    this.addSql(
      `CREATE INDEX IF NOT EXISTS "IDX_vendor_order_hotel_booking" ON "vendor_order" (hotel_id, booking_id) WHERE deleted_at IS NULL;`
    );
    this.addSql(
      `CREATE INDEX IF NOT EXISTS "IDX_vendor_order_delivery_date" ON "vendor_order" (requested_delivery_date) WHERE deleted_at IS NULL;`
    );

    this.addSql(
      `create table if not exists "vendor_order_item" ("id" text not null, "item_type" text check ("item_type" in ('product', 'service')) not null, "item_id" text not null, "item_name" text not null, "item_description" text null, "quantity" integer not null, "unit_price" integer not null, "total_price" integer not null, "service_date" timestamptz null, "service_duration_minutes" integer null, "product_sku" text null, "specifications" jsonb null, "status" text check ("status" in ('pending', 'confirmed', 'in_progress', 'completed', 'cancelled')) not null default 'pending', "notes" text null, "order_id" text not null, "created_at" timestamptz not null default now(), "updated_at" timestamptz not null default now(), "deleted_at" timestamptz null, constraint "vendor_order_item_pkey" primary key ("id"));`
    );
    this.addSql(
      `CREATE INDEX IF NOT EXISTS "IDX_vendor_order_item_order_id" ON "vendor_order_item" (order_id) WHERE deleted_at IS NULL;`
    );
    this.addSql(
      `CREATE INDEX IF NOT EXISTS "IDX_vendor_order_item_deleted_at" ON "vendor_order_item" (deleted_at) WHERE deleted_at IS NULL;`
    );
    this.addSql(
      `CREATE INDEX IF NOT EXISTS "IDX_vendor_order_item_type_id" ON "vendor_order_item" (item_type, item_id) WHERE deleted_at IS NULL;`
    );
    this.addSql(
      `CREATE INDEX IF NOT EXISTS "IDX_vendor_order_item_status" ON "vendor_order_item" (status) WHERE deleted_at IS NULL;`
    );
    this.addSql(
      `CREATE INDEX IF NOT EXISTS "IDX_vendor_order_item_service_date" ON "vendor_order_item" (service_date) WHERE deleted_at IS NULL AND service_date IS NOT NULL;`
    );

    this.addSql(
      `create table if not exists "vendor_product" ("id" text not null, "name" text not null, "description" text null, "sku" text null, "category" text not null, "unit_of_measure" text not null, "current_stock" integer not null default 0, "minimum_stock" integer not null default 0, "maximum_stock" integer null, "specifications" jsonb null, "images" jsonb null, "is_active" boolean not null default true, "vendor_id" text not null, "created_at" timestamptz not null default now(), "updated_at" timestamptz not null default now(), "deleted_at" timestamptz null, constraint "vendor_product_pkey" primary key ("id"));`
    );
    this.addSql(
      `CREATE INDEX IF NOT EXISTS "IDX_vendor_product_vendor_id" ON "vendor_product" (vendor_id) WHERE deleted_at IS NULL;`
    );
    this.addSql(
      `CREATE INDEX IF NOT EXISTS "IDX_vendor_product_deleted_at" ON "vendor_product" (deleted_at) WHERE deleted_at IS NULL;`
    );
    this.addSql(
      `CREATE INDEX IF NOT EXISTS "IDX_vendor_product_sku" ON "vendor_product" (sku) WHERE deleted_at IS NULL AND sku IS NOT NULL;`
    );
    this.addSql(
      `CREATE INDEX IF NOT EXISTS "IDX_vendor_product_category" ON "vendor_product" (category) WHERE deleted_at IS NULL;`
    );
    this.addSql(
      `CREATE INDEX IF NOT EXISTS "IDX_vendor_product_vendor_category" ON "vendor_product" (vendor_id, category) WHERE deleted_at IS NULL;`
    );
    this.addSql(
      `CREATE INDEX IF NOT EXISTS "IDX_vendor_product_is_active" ON "vendor_product" (is_active) WHERE deleted_at IS NULL;`
    );

    this.addSql(
      `create table if not exists "vendor_product_pricing" ("id" text not null, "pricing_type" text check ("pricing_type" in ('fixed', 'tiered', 'seasonal', 'bulk_discount', 'time_based')) not null, "base_price" integer not null, "currency_code" text not null default 'USD', "tier_rules" jsonb null, "seasonal_rules" jsonb null, "time_rules" jsonb null, "bulk_discount_rules" jsonb null, "valid_from" timestamptz null, "valid_until" timestamptz null, "is_active" boolean not null default true, "product_id" text not null, "created_at" timestamptz not null default now(), "updated_at" timestamptz not null default now(), "deleted_at" timestamptz null, constraint "vendor_product_pricing_pkey" primary key ("id"));`
    );
    this.addSql(
      `CREATE INDEX IF NOT EXISTS "IDX_vendor_product_pricing_product_id" ON "vendor_product_pricing" (product_id) WHERE deleted_at IS NULL;`
    );
    this.addSql(
      `CREATE INDEX IF NOT EXISTS "IDX_vendor_product_pricing_deleted_at" ON "vendor_product_pricing" (deleted_at) WHERE deleted_at IS NULL;`
    );
    this.addSql(
      `CREATE INDEX IF NOT EXISTS "IDX_vendor_product_pricing_type" ON "vendor_product_pricing" (pricing_type) WHERE deleted_at IS NULL;`
    );
    this.addSql(
      `CREATE INDEX IF NOT EXISTS "IDX_vendor_product_pricing_active" ON "vendor_product_pricing" (is_active) WHERE deleted_at IS NULL;`
    );
    this.addSql(
      `CREATE INDEX IF NOT EXISTS "IDX_vendor_product_pricing_validity" ON "vendor_product_pricing" (valid_from, valid_until) WHERE deleted_at IS NULL;`
    );

    this.addSql(
      `create table if not exists "vendor_service" ("id" text not null, "name" text not null, "description" text null, "category" text not null, "duration_minutes" integer null, "capacity" integer null, "is_available" boolean not null default true, "availability_schedule" jsonb null, "requirements" jsonb null, "images" jsonb null, "vendor_id" text not null, "created_at" timestamptz not null default now(), "updated_at" timestamptz not null default now(), "deleted_at" timestamptz null, constraint "vendor_service_pkey" primary key ("id"));`
    );
    this.addSql(
      `CREATE INDEX IF NOT EXISTS "IDX_vendor_service_vendor_id" ON "vendor_service" (vendor_id) WHERE deleted_at IS NULL;`
    );
    this.addSql(
      `CREATE INDEX IF NOT EXISTS "IDX_vendor_service_deleted_at" ON "vendor_service" (deleted_at) WHERE deleted_at IS NULL;`
    );
    this.addSql(
      `CREATE INDEX IF NOT EXISTS "IDX_vendor_service_category" ON "vendor_service" (category) WHERE deleted_at IS NULL;`
    );
    this.addSql(
      `CREATE INDEX IF NOT EXISTS "IDX_vendor_service_vendor_category" ON "vendor_service" (vendor_id, category) WHERE deleted_at IS NULL;`
    );
    this.addSql(
      `CREATE INDEX IF NOT EXISTS "IDX_vendor_service_is_available" ON "vendor_service" (is_available) WHERE deleted_at IS NULL;`
    );

    this.addSql(
      `create table if not exists "vendor_service_pricing" ("id" text not null, "pricing_type" text check ("pricing_type" in ('fixed', 'hourly', 'daily', 'weekly', 'seasonal', 'age_based')) not null, "base_price" integer not null, "currency_code" text not null default 'USD', "age_rules" jsonb null, "hourly_rate" integer null, "daily_rate" integer null, "weekly_rate" integer null, "seasonal_multipliers" jsonb null, "peak_multiplier" integer not null default 1, "off_peak_multiplier" integer not null default 1, "valid_from" timestamptz null, "valid_until" timestamptz null, "is_active" boolean not null default true, "service_id" text not null, "created_at" timestamptz not null default now(), "updated_at" timestamptz not null default now(), "deleted_at" timestamptz null, constraint "vendor_service_pricing_pkey" primary key ("id"));`
    );
    this.addSql(
      `CREATE INDEX IF NOT EXISTS "IDX_vendor_service_pricing_service_id" ON "vendor_service_pricing" (service_id) WHERE deleted_at IS NULL;`
    );
    this.addSql(
      `CREATE INDEX IF NOT EXISTS "IDX_vendor_service_pricing_deleted_at" ON "vendor_service_pricing" (deleted_at) WHERE deleted_at IS NULL;`
    );
    this.addSql(
      `CREATE INDEX IF NOT EXISTS "IDX_vendor_service_pricing_type" ON "vendor_service_pricing" (pricing_type) WHERE deleted_at IS NULL;`
    );
    this.addSql(
      `CREATE INDEX IF NOT EXISTS "IDX_vendor_service_pricing_active" ON "vendor_service_pricing" (is_active) WHERE deleted_at IS NULL;`
    );
    this.addSql(
      `CREATE INDEX IF NOT EXISTS "IDX_vendor_service_pricing_validity" ON "vendor_service_pricing" (valid_from, valid_until) WHERE deleted_at IS NULL;`
    );

    // Clean up orphaned supplier_contact records before adding foreign key constraint
    this.addSql(
      `DELETE FROM supplier_contact WHERE supplier_id NOT IN (SELECT id FROM supplier);`
    );

    // Add foreign key constraints with IF NOT EXISTS logic
    this.addSql(`
      DO $$
      BEGIN
        IF NOT EXISTS (SELECT 1 FROM information_schema.table_constraints WHERE constraint_name = 'supplier_contact_supplier_id_foreign') THEN
          ALTER TABLE supplier_contact ADD CONSTRAINT supplier_contact_supplier_id_foreign FOREIGN KEY (supplier_id) REFERENCES supplier (id) ON UPDATE CASCADE ON DELETE CASCADE;
        END IF;
      END $$;
    `);

    // Clean up orphaned supplier_contact_history records before adding foreign key constraint
    this.addSql(
      `DELETE FROM supplier_contact_history WHERE supplier_id NOT IN (SELECT id FROM supplier);`
    );

    this.addSql(`
      DO $$
      BEGIN
        IF NOT EXISTS (SELECT 1 FROM information_schema.table_constraints WHERE constraint_name = 'supplier_contact_history_supplier_id_foreign') THEN
          ALTER TABLE supplier_contact_history ADD CONSTRAINT supplier_contact_history_supplier_id_foreign FOREIGN KEY (supplier_id) REFERENCES supplier (id) ON UPDATE CASCADE ON DELETE CASCADE;
        END IF;
      END $$;
    `);

    this.addSql(`
      DO $$
      BEGIN
        IF NOT EXISTS (SELECT 1 FROM information_schema.table_constraints WHERE constraint_name = 'supplier_contract_supplier_id_foreign') THEN
          ALTER TABLE supplier_contract ADD CONSTRAINT supplier_contract_supplier_id_foreign FOREIGN KEY (supplier_id) REFERENCES supplier (id) ON UPDATE CASCADE;
        END IF;
      END $$;
    `);

    this.addSql(`
      DO $$
      BEGIN
        IF NOT EXISTS (SELECT 1 FROM information_schema.table_constraints WHERE constraint_name = 'supplier_contract_payment_contract_id_foreign') THEN
          ALTER TABLE supplier_contract_payment ADD CONSTRAINT supplier_contract_payment_contract_id_foreign FOREIGN KEY (contract_id) REFERENCES supplier_contract (id) ON UPDATE CASCADE;
        END IF;
      END $$;
    `);

    this.addSql(`
      DO $$
      BEGIN
        IF NOT EXISTS (SELECT 1 FROM information_schema.table_constraints WHERE constraint_name = 'supplier_document_supplier_id_foreign') THEN
          ALTER TABLE supplier_document ADD CONSTRAINT supplier_document_supplier_id_foreign FOREIGN KEY (supplier_id) REFERENCES supplier (id) ON UPDATE CASCADE;
        END IF;
      END $$;
    `);

    this.addSql(`
      DO $$
      BEGIN
        IF NOT EXISTS (SELECT 1 FROM information_schema.table_constraints WHERE constraint_name = 'supplier_order_supplier_id_foreign') THEN
          ALTER TABLE supplier_order ADD CONSTRAINT supplier_order_supplier_id_foreign FOREIGN KEY (supplier_id) REFERENCES supplier (id) ON UPDATE CASCADE;
        END IF;
      END $$;
    `);

    this.addSql(`
      DO $$
      BEGIN
        IF NOT EXISTS (SELECT 1 FROM information_schema.table_constraints WHERE constraint_name = 'supplier_order_item_order_id_foreign') THEN
          ALTER TABLE supplier_order_item ADD CONSTRAINT supplier_order_item_order_id_foreign FOREIGN KEY (order_id) REFERENCES supplier_order (id) ON UPDATE CASCADE;
        END IF;
      END $$;
    `);

    this.addSql(`
      DO $$
      BEGIN
        IF NOT EXISTS (SELECT 1 FROM information_schema.table_constraints WHERE constraint_name = 'supplier_product_supplier_id_foreign') THEN
          ALTER TABLE supplier_product ADD CONSTRAINT supplier_product_supplier_id_foreign FOREIGN KEY (supplier_id) REFERENCES supplier (id) ON UPDATE CASCADE;
        END IF;
      END $$;
    `);

    this.addSql(`
      DO $$
      BEGIN
        IF NOT EXISTS (SELECT 1 FROM information_schema.table_constraints WHERE constraint_name = 'supplier_product_pricing_product_id_foreign') THEN
          ALTER TABLE supplier_product_pricing ADD CONSTRAINT supplier_product_pricing_product_id_foreign FOREIGN KEY (product_id) REFERENCES supplier_product (id) ON UPDATE CASCADE;
        END IF;
      END $$;
    `);

    this.addSql(`
      DO $$
      BEGIN
        IF NOT EXISTS (SELECT 1 FROM information_schema.table_constraints WHERE constraint_name = 'supplier_service_supplier_id_foreign') THEN
          ALTER TABLE supplier_service ADD CONSTRAINT supplier_service_supplier_id_foreign FOREIGN KEY (supplier_id) REFERENCES supplier (id) ON UPDATE CASCADE;
        END IF;
      END $$;
    `);

    this.addSql(`
      DO $$
      BEGIN
        IF NOT EXISTS (SELECT 1 FROM information_schema.table_constraints WHERE constraint_name = 'supplier_service_pricing_service_id_foreign') THEN
          ALTER TABLE supplier_service_pricing ADD CONSTRAINT supplier_service_pricing_service_id_foreign FOREIGN KEY (service_id) REFERENCES supplier_service (id) ON UPDATE CASCADE;
        END IF;
      END $$;
    `);

    // Vendor foreign key constraints with IF NOT EXISTS logic
    this.addSql(`
      DO $$
      BEGIN
        IF NOT EXISTS (SELECT 1 FROM information_schema.table_constraints WHERE constraint_name = 'vendor_contact_history_vendor_id_foreign') THEN
          ALTER TABLE vendor_contact_history ADD CONSTRAINT vendor_contact_history_vendor_id_foreign FOREIGN KEY (vendor_id) REFERENCES vendor (id) ON UPDATE CASCADE;
        END IF;
      END $$;
    `);

    this.addSql(`
      DO $$
      BEGIN
        IF NOT EXISTS (SELECT 1 FROM information_schema.table_constraints WHERE constraint_name = 'vendor_contract_vendor_id_foreign') THEN
          ALTER TABLE vendor_contract ADD CONSTRAINT vendor_contract_vendor_id_foreign FOREIGN KEY (vendor_id) REFERENCES vendor (id) ON UPDATE CASCADE;
        END IF;
      END $$;
    `);

    this.addSql(`
      DO $$
      BEGIN
        IF NOT EXISTS (SELECT 1 FROM information_schema.table_constraints WHERE constraint_name = 'vendor_contract_payment_contract_id_foreign') THEN
          ALTER TABLE vendor_contract_payment ADD CONSTRAINT vendor_contract_payment_contract_id_foreign FOREIGN KEY (contract_id) REFERENCES vendor_contract (id) ON UPDATE CASCADE;
        END IF;
      END $$;
    `);

    this.addSql(`
      DO $$
      BEGIN
        IF NOT EXISTS (SELECT 1 FROM information_schema.table_constraints WHERE constraint_name = 'vendor_document_vendor_id_foreign') THEN
          ALTER TABLE vendor_document ADD CONSTRAINT vendor_document_vendor_id_foreign FOREIGN KEY (vendor_id) REFERENCES vendor (id) ON UPDATE CASCADE;
        END IF;
      END $$;
    `);

    this.addSql(`
      DO $$
      BEGIN
        IF NOT EXISTS (SELECT 1 FROM information_schema.table_constraints WHERE constraint_name = 'vendor_document_parent_document_id_foreign') THEN
          ALTER TABLE vendor_document ADD CONSTRAINT vendor_document_parent_document_id_foreign FOREIGN KEY (parent_document_id) REFERENCES vendor_document (id) ON UPDATE CASCADE;
        END IF;
      END $$;
    `);

    this.addSql(`
      DO $$
      BEGIN
        IF NOT EXISTS (SELECT 1 FROM information_schema.table_constraints WHERE constraint_name = 'vendor_order_vendor_id_foreign') THEN
          ALTER TABLE vendor_order ADD CONSTRAINT vendor_order_vendor_id_foreign FOREIGN KEY (vendor_id) REFERENCES vendor (id) ON UPDATE CASCADE;
        END IF;
      END $$;
    `);

    this.addSql(`
      DO $$
      BEGIN
        IF NOT EXISTS (SELECT 1 FROM information_schema.table_constraints WHERE constraint_name = 'vendor_order_item_order_id_foreign') THEN
          ALTER TABLE vendor_order_item ADD CONSTRAINT vendor_order_item_order_id_foreign FOREIGN KEY (order_id) REFERENCES vendor_order (id) ON UPDATE CASCADE;
        END IF;
      END $$;
    `);

    this.addSql(`
      DO $$
      BEGIN
        IF NOT EXISTS (SELECT 1 FROM information_schema.table_constraints WHERE constraint_name = 'vendor_product_vendor_id_foreign') THEN
          ALTER TABLE vendor_product ADD CONSTRAINT vendor_product_vendor_id_foreign FOREIGN KEY (vendor_id) REFERENCES vendor (id) ON UPDATE CASCADE;
        END IF;
      END $$;
    `);

    this.addSql(`
      DO $$
      BEGIN
        IF NOT EXISTS (SELECT 1 FROM information_schema.table_constraints WHERE constraint_name = 'vendor_product_pricing_product_id_foreign') THEN
          ALTER TABLE vendor_product_pricing ADD CONSTRAINT vendor_product_pricing_product_id_foreign FOREIGN KEY (product_id) REFERENCES vendor_product (id) ON UPDATE CASCADE;
        END IF;
      END $$;
    `);

    this.addSql(`
      DO $$
      BEGIN
        IF NOT EXISTS (SELECT 1 FROM information_schema.table_constraints WHERE constraint_name = 'vendor_service_vendor_id_foreign') THEN
          ALTER TABLE vendor_service ADD CONSTRAINT vendor_service_vendor_id_foreign FOREIGN KEY (vendor_id) REFERENCES vendor (id) ON UPDATE CASCADE;
        END IF;
      END $$;
    `);

    this.addSql(`
      DO $$
      BEGIN
        IF NOT EXISTS (SELECT 1 FROM information_schema.table_constraints WHERE constraint_name = 'vendor_service_pricing_service_id_foreign') THEN
          ALTER TABLE vendor_service_pricing ADD CONSTRAINT vendor_service_pricing_service_id_foreign FOREIGN KEY (service_id) REFERENCES vendor_service (id) ON UPDATE CASCADE;
        END IF;
      END $$;
    `);
  }

  override async down(): Promise<void> {
    this.addSql(
      `alter table if exists "supplier_contact" drop constraint if exists "supplier_contact_supplier_id_foreign";`
    );

    this.addSql(
      `alter table if exists "supplier_contact_history" drop constraint if exists "supplier_contact_history_supplier_id_foreign";`
    );

    this.addSql(
      `alter table if exists "supplier_contract" drop constraint if exists "supplier_contract_supplier_id_foreign";`
    );

    this.addSql(
      `alter table if exists "supplier_document" drop constraint if exists "supplier_document_supplier_id_foreign";`
    );

    this.addSql(
      `alter table if exists "supplier_order" drop constraint if exists "supplier_order_supplier_id_foreign";`
    );

    this.addSql(
      `alter table if exists "supplier_product" drop constraint if exists "supplier_product_supplier_id_foreign";`
    );

    this.addSql(
      `alter table if exists "supplier_service" drop constraint if exists "supplier_service_supplier_id_foreign";`
    );

    this.addSql(
      `alter table if exists "supplier_contract_payment" drop constraint if exists "supplier_contract_payment_contract_id_foreign";`
    );

    this.addSql(
      `alter table if exists "supplier_order_item" drop constraint if exists "supplier_order_item_order_id_foreign";`
    );

    this.addSql(
      `alter table if exists "supplier_product_pricing" drop constraint if exists "supplier_product_pricing_product_id_foreign";`
    );

    this.addSql(
      `alter table if exists "supplier_service_pricing" drop constraint if exists "supplier_service_pricing_service_id_foreign";`
    );

    this.addSql(
      `alter table if exists "vendor_contact_history" drop constraint if exists "vendor_contact_history_vendor_id_foreign";`
    );

    this.addSql(
      `alter table if exists "vendor_contract" drop constraint if exists "vendor_contract_vendor_id_foreign";`
    );

    this.addSql(
      `alter table if exists "vendor_document" drop constraint if exists "vendor_document_vendor_id_foreign";`
    );

    this.addSql(
      `alter table if exists "vendor_order" drop constraint if exists "vendor_order_vendor_id_foreign";`
    );

    this.addSql(
      `alter table if exists "vendor_product" drop constraint if exists "vendor_product_vendor_id_foreign";`
    );

    this.addSql(
      `alter table if exists "vendor_service" drop constraint if exists "vendor_service_vendor_id_foreign";`
    );

    this.addSql(
      `alter table if exists "vendor_contract_payment" drop constraint if exists "vendor_contract_payment_contract_id_foreign";`
    );

    this.addSql(
      `alter table if exists "vendor_document" drop constraint if exists "vendor_document_parent_document_id_foreign";`
    );

    this.addSql(
      `alter table if exists "vendor_order_item" drop constraint if exists "vendor_order_item_order_id_foreign";`
    );

    this.addSql(
      `alter table if exists "vendor_product_pricing" drop constraint if exists "vendor_product_pricing_product_id_foreign";`
    );

    this.addSql(
      `alter table if exists "vendor_service_pricing" drop constraint if exists "vendor_service_pricing_service_id_foreign";`
    );

    this.addSql(`drop table if exists "supplier" cascade;`);

    this.addSql(`drop table if exists "supplier_contact" cascade;`);

    this.addSql(`drop table if exists "supplier_contact_history" cascade;`);

    this.addSql(`drop table if exists "supplier_contract" cascade;`);

    this.addSql(`drop table if exists "supplier_contract_payment" cascade;`);

    this.addSql(`drop table if exists "supplier_document" cascade;`);

    this.addSql(`drop table if exists "supplier_order" cascade;`);

    this.addSql(`drop table if exists "supplier_order_item" cascade;`);

    this.addSql(`drop table if exists "supplier_product" cascade;`);

    this.addSql(`drop table if exists "supplier_product_pricing" cascade;`);

    this.addSql(`drop table if exists "supplier_service" cascade;`);

    this.addSql(`drop table if exists "supplier_service_pricing" cascade;`);

    this.addSql(`drop table if exists "vendor" cascade;`);

    this.addSql(`drop table if exists "vendor_contact_history" cascade;`);

    this.addSql(`drop table if exists "vendor_contract" cascade;`);

    this.addSql(`drop table if exists "vendor_contract_payment" cascade;`);

    this.addSql(`drop table if exists "vendor_document" cascade;`);

    this.addSql(`drop table if exists "vendor_order" cascade;`);

    this.addSql(`drop table if exists "vendor_order_item" cascade;`);

    this.addSql(`drop table if exists "vendor_product" cascade;`);

    this.addSql(`drop table if exists "vendor_product_pricing" cascade;`);

    this.addSql(`drop table if exists "vendor_service" cascade;`);

    this.addSql(`drop table if exists "vendor_service_pricing" cascade;`);
  }
}
