import { Migration } from "@mikro-orm/migrations";

/**
 * Migration to consolidate address fields into single address column
 *
 * This migration:
 * 1. Adds a new 'address' text column to the supplier table
 * 2. Migrates data from individual address fields to the single address field
 * 3. Removes the old individual address fields (address_line_1, address_line_2, city, state, postal_code, country)
 * 
 * The migration consolidates:
 * - address_line_1
 * - address_line_2  
 * - city
 * - state
 * - postal_code
 * - country
 * 
 * Into a single 'address' text field for simplified address management.
 */
export class Migration20250719000000 extends Migration {
  async up(): Promise<void> {
    // 1. Drop the existing jsonb address column if it exists
    this.addSql(`ALTER TABLE "supplier" DROP COLUMN IF EXISTS "address";`);

    // 2. Add new text address column
    this.addSql(`ALTER TABLE "supplier" ADD COLUMN "address" text NULL;`);

    // 3. Migrate existing address data to the new address field
    // Concatenate all non-null address fields into a single address string
    this.addSql(`
      UPDATE "supplier"
      SET "address" = TRIM(
        CONCAT_WS(', ',
          NULLIF(TRIM("address_line_1"), ''),
          NULLIF(TRIM("address_line_2"), ''),
          NULLIF(TRIM("city"), ''),
          NULLIF(TRIM("state"), ''),
          NULLIF(TRIM("postal_code"), ''),
          NULLIF(TRIM("country"), '')
        )
      )
      WHERE (
        "address_line_1" IS NOT NULL OR
        "address_line_2" IS NOT NULL OR
        "city" IS NOT NULL OR
        "state" IS NOT NULL OR
        "postal_code" IS NOT NULL OR
        "country" IS NOT NULL
      )
      AND TRIM(
        CONCAT_WS(', ',
          NULLIF(TRIM(COALESCE("address_line_1", '')), ''),
          NULLIF(TRIM(COALESCE("address_line_2", '')), ''),
          NULLIF(TRIM(COALESCE("city", '')), ''),
          NULLIF(TRIM(COALESCE("state", '')), ''),
          NULLIF(TRIM(COALESCE("postal_code", '')), ''),
          NULLIF(TRIM(COALESCE("country", '')), '')
        )
      ) != '';
    `);

    // 3. Remove old individual address fields
    this.addSql(`ALTER TABLE "supplier" DROP COLUMN IF EXISTS "address_line_1";`);
    this.addSql(`ALTER TABLE "supplier" DROP COLUMN IF EXISTS "address_line_2";`);
    this.addSql(`ALTER TABLE "supplier" DROP COLUMN IF EXISTS "city";`);
    this.addSql(`ALTER TABLE "supplier" DROP COLUMN IF EXISTS "state";`);
    this.addSql(`ALTER TABLE "supplier" DROP COLUMN IF EXISTS "postal_code";`);
    this.addSql(`ALTER TABLE "supplier" DROP COLUMN IF EXISTS "country";`);
  }

  async down(): Promise<void> {
    // 1. Re-add the individual address fields
    this.addSql(`ALTER TABLE "supplier" ADD COLUMN IF NOT EXISTS "address_line_1" text NULL;`);
    this.addSql(`ALTER TABLE "supplier" ADD COLUMN IF NOT EXISTS "address_line_2" text NULL;`);
    this.addSql(`ALTER TABLE "supplier" ADD COLUMN IF NOT EXISTS "city" text NULL;`);
    this.addSql(`ALTER TABLE "supplier" ADD COLUMN IF NOT EXISTS "state" text NULL;`);
    this.addSql(`ALTER TABLE "supplier" ADD COLUMN IF NOT EXISTS "postal_code" text NULL;`);
    this.addSql(`ALTER TABLE "supplier" ADD COLUMN IF NOT EXISTS "country" text NULL;`);

    // 2. Attempt to migrate data back (this is a best-effort reverse migration)
    // Note: This won't perfectly restore the original data structure since we're
    // going from a single field back to multiple fields
    this.addSql(`
      UPDATE "supplier"
      SET "address_line_1" = "address"
      WHERE "address" IS NOT NULL AND TRIM("address") != '';
    `);

    // 3. Remove the text address field and restore the original jsonb address field
    this.addSql(`ALTER TABLE "supplier" DROP COLUMN IF EXISTS "address";`);
    this.addSql(`ALTER TABLE "supplier" ADD COLUMN "address" jsonb NULL;`);
  }
}
