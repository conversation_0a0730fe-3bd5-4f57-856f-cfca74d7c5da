import { Migration } from '@mikro-orm/migrations';

export class Migration20250708190000 extends Migration {

  override async up(): Promise<void> {
    // Drop existing foreign key constraints and recreate with CASCADE DELETE
    
    // Drop existing supplier_contact constraint
    this.addSql(`ALTER TABLE supplier_contact DROP CONSTRAINT IF EXISTS supplier_contact_supplier_id_foreign;`);
    
    // Recreate with CASCADE DELETE
    this.addSql(`ALTER TABLE supplier_contact ADD CONSTRAINT supplier_contact_supplier_id_foreign FOREIGN KEY (supplier_id) REFERENCES supplier (id) ON UPDATE CASCADE ON DELETE CASCADE;`);
    
    // Drop existing supplier_contact_history constraint
    this.addSql(`ALTER TABLE supplier_contact_history DROP CONSTRAINT IF EXISTS supplier_contact_history_supplier_id_foreign;`);
    
    // Recreate with CASCADE DELETE
    this.addSql(`ALTER TABLE supplier_contact_history ADD CONSTRAINT supplier_contact_history_supplier_id_foreign FOREIGN KEY (supplier_id) REFERENCES supplier (id) ON UPDATE CASCADE ON DELETE CASCADE;`);
    
    // Also update other supplier-related constraints to have CASCADE DELETE
    
    // supplier_contract
    this.addSql(`ALTER TABLE supplier_contract DROP CONSTRAINT IF EXISTS supplier_contract_supplier_id_foreign;`);
    this.addSql(`ALTER TABLE supplier_contract ADD CONSTRAINT supplier_contract_supplier_id_foreign FOREIGN KEY (supplier_id) REFERENCES supplier (id) ON UPDATE CASCADE ON DELETE CASCADE;`);
    
    // supplier_document
    this.addSql(`ALTER TABLE supplier_document DROP CONSTRAINT IF EXISTS supplier_document_supplier_id_foreign;`);
    this.addSql(`ALTER TABLE supplier_document ADD CONSTRAINT supplier_document_supplier_id_foreign FOREIGN KEY (supplier_id) REFERENCES supplier (id) ON UPDATE CASCADE ON DELETE CASCADE;`);
    
    // supplier_order
    this.addSql(`ALTER TABLE supplier_order DROP CONSTRAINT IF EXISTS supplier_order_supplier_id_foreign;`);
    this.addSql(`ALTER TABLE supplier_order ADD CONSTRAINT supplier_order_supplier_id_foreign FOREIGN KEY (supplier_id) REFERENCES supplier (id) ON UPDATE CASCADE ON DELETE CASCADE;`);
    
    // supplier_product
    this.addSql(`ALTER TABLE supplier_product DROP CONSTRAINT IF EXISTS supplier_product_supplier_id_foreign;`);
    this.addSql(`ALTER TABLE supplier_product ADD CONSTRAINT supplier_product_supplier_id_foreign FOREIGN KEY (supplier_id) REFERENCES supplier (id) ON UPDATE CASCADE ON DELETE CASCADE;`);
    
    // supplier_service
    this.addSql(`ALTER TABLE supplier_service DROP CONSTRAINT IF EXISTS supplier_service_supplier_id_foreign;`);
    this.addSql(`ALTER TABLE supplier_service ADD CONSTRAINT supplier_service_supplier_id_foreign FOREIGN KEY (supplier_id) REFERENCES supplier (id) ON UPDATE CASCADE ON DELETE CASCADE;`);
  }

  override async down(): Promise<void> {
    // Revert back to constraints without CASCADE DELETE
    
    // supplier_contact
    this.addSql(`ALTER TABLE supplier_contact DROP CONSTRAINT IF EXISTS supplier_contact_supplier_id_foreign;`);
    this.addSql(`ALTER TABLE supplier_contact ADD CONSTRAINT supplier_contact_supplier_id_foreign FOREIGN KEY (supplier_id) REFERENCES supplier (id) ON UPDATE CASCADE;`);
    
    // supplier_contact_history
    this.addSql(`ALTER TABLE supplier_contact_history DROP CONSTRAINT IF EXISTS supplier_contact_history_supplier_id_foreign;`);
    this.addSql(`ALTER TABLE supplier_contact_history ADD CONSTRAINT supplier_contact_history_supplier_id_foreign FOREIGN KEY (supplier_id) REFERENCES supplier (id) ON UPDATE CASCADE;`);
    
    // supplier_contract
    this.addSql(`ALTER TABLE supplier_contract DROP CONSTRAINT IF EXISTS supplier_contract_supplier_id_foreign;`);
    this.addSql(`ALTER TABLE supplier_contract ADD CONSTRAINT supplier_contract_supplier_id_foreign FOREIGN KEY (supplier_id) REFERENCES supplier (id) ON UPDATE CASCADE;`);
    
    // supplier_document
    this.addSql(`ALTER TABLE supplier_document DROP CONSTRAINT IF EXISTS supplier_document_supplier_id_foreign;`);
    this.addSql(`ALTER TABLE supplier_document ADD CONSTRAINT supplier_document_supplier_id_foreign FOREIGN KEY (supplier_id) REFERENCES supplier (id) ON UPDATE CASCADE;`);
    
    // supplier_order
    this.addSql(`ALTER TABLE supplier_order DROP CONSTRAINT IF EXISTS supplier_order_supplier_id_foreign;`);
    this.addSql(`ALTER TABLE supplier_order ADD CONSTRAINT supplier_order_supplier_id_foreign FOREIGN KEY (supplier_id) REFERENCES supplier (id) ON UPDATE CASCADE;`);
    
    // supplier_product
    this.addSql(`ALTER TABLE supplier_product DROP CONSTRAINT IF EXISTS supplier_product_supplier_id_foreign;`);
    this.addSql(`ALTER TABLE supplier_product ADD CONSTRAINT supplier_product_supplier_id_foreign FOREIGN KEY (supplier_id) REFERENCES supplier (id) ON UPDATE CASCADE;`);
    
    // supplier_service
    this.addSql(`ALTER TABLE supplier_service DROP CONSTRAINT IF EXISTS supplier_service_supplier_id_foreign;`);
    this.addSql(`ALTER TABLE supplier_service ADD CONSTRAINT supplier_service_supplier_id_foreign FOREIGN KEY (supplier_id) REFERENCES supplier (id) ON UPDATE CASCADE;`);
  }

}
