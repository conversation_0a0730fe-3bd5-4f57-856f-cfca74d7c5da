import { Migration } from "@mikro-orm/migrations";

/**
 * Migration to add preference field to supplier table
 *
 * Adds the preference field to distinguish between "Preferred" and "Backup" suppliers.
 * This field helps categorize suppliers based on their priority level for business operations.
 */
export class Migration20250710140000 extends Migration {
  async up(): Promise<void> {
    // Add preference field to supplier table
    this.addSql(
      `ALTER TABLE "supplier" ADD COLUMN IF NOT EXISTS "preference" text NULL;`
    );

    // Add check constraint to ensure only valid values
    this.addSql(
      `ALTER TABLE "supplier" ADD CONSTRAINT "supplier_preference_check" CHECK ("preference" IN ('Preferred', 'Backup') OR "preference" IS NULL);`
    );

    // Add index for preference-based queries
    this.addSql(
      `CREATE INDEX IF NOT EXISTS "IDX_supplier_preference" ON "supplier" ("preference") WHERE "deleted_at" IS NULL;`
    );
  }

  async down(): Promise<void> {
    // Drop the index
    this.addSql(`DROP INDEX IF EXISTS "IDX_supplier_preference";`);

    // Drop the check constraint
    this.addSql(
      `ALTER TABLE "supplier" DROP CONSTRAINT IF EXISTS "supplier_preference_check";`
    );

    // Drop the preference column
    this.addSql(`ALTER TABLE "supplier" DROP COLUMN IF EXISTS "preference";`);
  }
}
