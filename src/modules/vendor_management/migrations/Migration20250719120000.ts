import { Migration } from "@mikro-orm/migrations";

/**
 * Migration to remove email field from supplier table
 *
 * This migration removes the email field from the supplier table since
 * supplier contact information (including emails) will be managed through
 * the separate supplier_contact relationship rather than having a main
 * email on the supplier entity itself.
 */
export class Migration20250719120000 extends Migration {
  async up(): Promise<void> {
    // Remove the email column from the supplier table
    this.addSql(`ALTER TABLE "supplier" DROP COLUMN IF EXISTS "email";`);
  }

  async down(): Promise<void> {
    // Re-add the email column (as nullable since we can't restore the original data)
    this.addSql(`ALTER TABLE "supplier" ADD COLUMN "email" text NULL;`);
  }
}
