import { Migration } from '@mikro-orm/migrations';

export class Migration20250708191157 extends Migration {

  override async up(): Promise<void> {
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_supplier_name" ON "supplier" (name) WHERE deleted_at IS NULL;`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_supplier_categories" ON "supplier" USING gin (categories) WHERE deleted_at IS NULL;`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_supplier_status_business_type_name" ON "supplier" (status, business_type, name) WHERE deleted_at IS NULL;`);

    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_supplier_contact_email_primary" ON "supplier_contact" (email, is_primary) WHERE deleted_at IS NULL;`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_supplier_contact_name_primary" ON "supplier_contact" (name, is_primary) WHERE deleted_at IS NULL;`);
  }

  override async down(): Promise<void> {
    this.addSql(`drop index if exists "IDX_supplier_name";`);
    this.addSql(`drop index if exists "IDX_supplier_categories";`);
    this.addSql(`drop index if exists "IDX_supplier_status_business_type_name";`);

    this.addSql(`drop index if exists "IDX_supplier_contact_email_primary";`);
    this.addSql(`drop index if exists "IDX_supplier_contact_name_primary";`);
  }

}
