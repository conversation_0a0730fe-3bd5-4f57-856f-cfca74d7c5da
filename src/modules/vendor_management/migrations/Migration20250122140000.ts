import { Migration } from "@mikro-orm/migrations";

/**
 * Migration to create exchange_rate table
 *
 * This migration creates the exchange_rate table to store currency exchange rates
 * with columns: date, base_currency, selling_currency, and exchange_rate.
 * Includes proper indexes for performance optimization.
 */
export class Migration20250122140000 extends Migration {
  async up(): Promise<void> {
    // Create the exchange_rate table
    this.addSql(`
      CREATE TABLE "exchange_rate" (
        "id" text NOT NULL,
        "date" timestamptz NOT NULL,
        "base_currency" text NOT NULL,
        "selling_currency" text NOT NULL,
        "exchange_rate" numeric NOT NULL,
        "metadata" jsonb NULL,
        "created_at" timestamptz NOT NULL DEFAULT now(),
        "updated_at" timestamptz NOT NULL DEFAULT now(),
        "deleted_at" timestamptz NULL,
        CONSTRAINT "exchange_rate_pkey" PRIMARY KEY ("id")
      );
    `);

    // Create indexes for performance
    this.addSql(`CREATE INDEX "IDX_exchange_rate_date" ON "exchange_rate" ("date") WHERE "deleted_at" IS NULL;`);
    this.addSql(`CREATE INDEX "IDX_exchange_rate_currencies" ON "exchange_rate" ("base_currency", "selling_currency") WHERE "deleted_at" IS NULL;`);
    this.addSql(`CREATE UNIQUE INDEX "IDX_exchange_rate_date_currencies" ON "exchange_rate" ("date", "base_currency", "selling_currency") WHERE "deleted_at" IS NULL;`);
    this.addSql(`CREATE INDEX "IDX_exchange_rate_base_currency" ON "exchange_rate" ("base_currency") WHERE "deleted_at" IS NULL;`);
    this.addSql(`CREATE INDEX "IDX_exchange_rate_selling_currency" ON "exchange_rate" ("selling_currency") WHERE "deleted_at" IS NULL;`);
  }

  async down(): Promise<void> {
    // Drop the exchange_rate table and all its indexes
    this.addSql(`DROP TABLE IF EXISTS "exchange_rate";`);
  }
}
