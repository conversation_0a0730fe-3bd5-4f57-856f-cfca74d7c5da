import { Migration } from "@mikro-orm/migrations";

export class Migration20250716120000 extends Migration {
  async up(): Promise<void> {
    // Add order_name field to supplier_order table
    this.addSql(
      `ALTER TABLE "supplier_order" ADD COLUMN "order_name" TEXT NULL;`
    );

    // Add index for order_name for better search performance
    this.addSql(
      `CREATE INDEX "IDX_supplier_order_name" ON "supplier_order" ("order_name") WHERE "deleted_at" IS NULL;`
    );
  }

  async down(): Promise<void> {
    // Remove index
    this.addSql(`DROP INDEX IF EXISTS "IDX_supplier_order_name";`);

    // Remove order_name column
    this.addSql(`ALTER TABLE "supplier_order" DROP COLUMN IF EXISTS "order_name";`);
  }
}
