import { Migration } from "@mikro-orm/migrations";

/**
 * Migration to remove business_type column and related indexes from supplier table
 *
 * This migration removes:
 * - business_type column from supplier table
 * - All indexes that reference business_type
 *
 * WARNING: This will permanently delete all business_type data
 */
export class Migration1737297600000 extends Migration {
  async up(): Promise<void> {
    // Drop indexes that reference business_type column
    this.addSql(`
      DROP INDEX IF EXISTS "IDX_supplier_status_business_type"
    `);

    this.addSql(`
      DROP INDEX IF EXISTS "IDX_supplier_business_type"
    `);

    this.addSql(`
      DROP INDEX IF EXISTS "IDX_supplier_status_business_type_name"
    `);

    // Remove the business_type column from supplier table
    this.addSql(`
      ALTER TABLE "supplier" DROP COLUMN IF EXISTS "business_type"
    `);
  }

  async down(): Promise<void> {
    // Add back the business_type column
    this.addSql(`
      ALTER TABLE "supplier" ADD COLUMN "business_type" text
    `);

    // Recreate the indexes
    this.addSql(`
      CREATE INDEX "IDX_supplier_business_type"
      ON "supplier" ("business_type")
      WHERE "deleted_at" IS NULL
    `);

    this.addSql(`
      CREATE INDEX "IDX_supplier_status_business_type"
      ON "supplier" ("status", "business_type")
      WHERE "deleted_at" IS NULL
    `);

    this.addSql(`
      CREATE INDEX "IDX_supplier_status_business_type_name"
      ON "supplier" ("status", "business_type", "name")
      WHERE "deleted_at" IS NULL
    `);
  }
}
