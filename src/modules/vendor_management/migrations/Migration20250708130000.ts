import { Migration } from "@mikro-orm/migrations";

/**
 * Migration to add supplier contact table and additional supplier fields
 *
 * This migration:
 * 1. Creates supplier_contact table for managing multiple contacts per supplier
 * 2. Adds missing fields to supplier table (email, phone, region, timezone, etc.)
 * 3. Renames 'type' field to 'business_type' in supplier table
 * 4. Updates indexes to reflect field name changes
 */
export class Migration20250708130000 extends Migration {
  async up(): Promise<void> {
    // 1. Create supplier_contact table (simplified)
    this.addSql(`
      CREATE TABLE IF NOT EXISTS "supplier_contact" (
        "id" text NOT NULL,
        "supplier_id" text NOT NULL,
        "name" text NOT NULL,
        "email" text NOT NULL,
        "is_primary" boolean NOT NULL DEFAULT false,
        "created_at" timestamptz NOT NULL DEFAULT now(),
        "updated_at" timestamptz NOT NULL DEFAULT now(),
        "deleted_at" timestamptz NULL,
        CONSTRAINT "supplier_contact_pkey" PRIMARY KEY ("id")
      );
    `);

    // Create indexes for supplier_contact
    this.addSql(
      `CREATE INDEX IF NOT EXISTS "IDX_supplier_contact_supplier_id" ON "supplier_contact" ("supplier_id") WHERE "deleted_at" IS NULL;`
    );
    this.addSql(
      `CREATE INDEX IF NOT EXISTS "IDX_supplier_contact_email" ON "supplier_contact" ("email") WHERE "deleted_at" IS NULL;`
    );
    this.addSql(
      `CREATE INDEX IF NOT EXISTS "IDX_supplier_contact_is_primary" ON "supplier_contact" ("supplier_id", "is_primary") WHERE "deleted_at" IS NULL;`
    );

    // 2. Add new fields to supplier table
    this.addSql(
      `ALTER TABLE "supplier" ADD COLUMN IF NOT EXISTS "email" text NULL;`
    );
    this.addSql(
      `ALTER TABLE "supplier" ADD COLUMN IF NOT EXISTS "phone" text NULL;`
    );
    this.addSql(
      `ALTER TABLE "supplier" ADD COLUMN IF NOT EXISTS "region" text NULL;`
    );
    this.addSql(
      `ALTER TABLE "supplier" ADD COLUMN IF NOT EXISTS "timezone" text NULL;`
    );
    this.addSql(
      `ALTER TABLE "supplier" ADD COLUMN IF NOT EXISTS "language_preference" jsonb NULL;`
    );
    this.addSql(
      `ALTER TABLE "supplier" ADD COLUMN IF NOT EXISTS "payment_method" text NULL;`
    );
    this.addSql(
      `ALTER TABLE "supplier" ADD COLUMN IF NOT EXISTS "payout_terms" text NULL;`
    );
    this.addSql(
      `ALTER TABLE "supplier" ADD COLUMN IF NOT EXISTS "bank_account_details" jsonb NULL;`
    );
    this.addSql(
      `ALTER TABLE "supplier" ADD COLUMN IF NOT EXISTS "categories" jsonb NULL;`
    );

    // 3. Rename 'type' field to 'business_type' if it exists
    this.addSql(
      `ALTER TABLE "supplier" ADD COLUMN IF NOT EXISTS "business_type" text NULL;`
    );

    // Copy data from 'type' to 'business_type' if 'type' column exists
    this.addSql(`
      DO $$
      BEGIN
        IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'supplier' AND column_name = 'type') THEN
          UPDATE "supplier" SET "business_type" = "type" WHERE "business_type" IS NULL;
        END IF;
      END $$;
    `);

    // 4. Drop old indexes that reference 'type' field
    this.addSql(`DROP INDEX IF EXISTS "IDX_supplier_status_type";`);
    this.addSql(`DROP INDEX IF EXISTS "IDX_supplier_type";`);

    // 5. Create new indexes for business_type
    this.addSql(
      `CREATE INDEX IF NOT EXISTS "IDX_supplier_status_business_type" ON "supplier" ("status", "business_type") WHERE "deleted_at" IS NULL;`
    );
    this.addSql(
      `CREATE INDEX IF NOT EXISTS "IDX_supplier_business_type" ON "supplier" ("business_type") WHERE "deleted_at" IS NULL;`
    );

    // 6. Create indexes for new fields
    this.addSql(
      `CREATE INDEX IF NOT EXISTS "IDX_supplier_email" ON "supplier" ("email") WHERE "deleted_at" IS NULL;`
    );
    this.addSql(
      `CREATE INDEX IF NOT EXISTS "IDX_supplier_region" ON "supplier" ("region") WHERE "deleted_at" IS NULL;`
    );

    // 7. No check constraint for business_type since values come from lookup table

    // 8. Drop the old 'type' column if it exists (after data migration)
    this.addSql(`
      DO $$
      BEGIN
        IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'supplier' AND column_name = 'type') THEN
          ALTER TABLE "supplier" DROP COLUMN "type";
        END IF;
      END $$;
    `);

    // 9. Make email field required (after adding it)
    this.addSql(`
      DO $$
      BEGIN
        -- Update any NULL email values with a placeholder
        UPDATE "supplier" SET "email" = '<EMAIL>' WHERE "email" IS NULL;
        -- Make the field NOT NULL
        ALTER TABLE "supplier" ALTER COLUMN "email" SET NOT NULL;
      END $$;
    `);

    // 10. Remove legacy contact fields (no longer needed with separate contact table)
    this.addSql(
      `ALTER TABLE "supplier" DROP COLUMN IF EXISTS "primary_contact_name";`
    );
    this.addSql(
      `ALTER TABLE "supplier" DROP COLUMN IF EXISTS "primary_contact_email";`
    );
    this.addSql(
      `ALTER TABLE "supplier" DROP COLUMN IF EXISTS "primary_contact_phone";`
    );
    this.addSql(
      `ALTER TABLE "supplier" DROP COLUMN IF EXISTS "secondary_contact_name";`
    );
    this.addSql(
      `ALTER TABLE "supplier" DROP COLUMN IF EXISTS "secondary_contact_email";`
    );
    this.addSql(
      `ALTER TABLE "supplier" DROP COLUMN IF EXISTS "secondary_contact_phone";`
    );
  }

  async down(): Promise<void> {
    // Drop supplier_contact table
    this.addSql(`DROP TABLE IF EXISTS "supplier_contact" CASCADE;`);

    // Restore legacy contact fields
    this.addSql(
      `ALTER TABLE "supplier" ADD COLUMN IF NOT EXISTS "primary_contact_name" text NULL;`
    );
    this.addSql(
      `ALTER TABLE "supplier" ADD COLUMN IF NOT EXISTS "primary_contact_email" text NULL;`
    );
    this.addSql(
      `ALTER TABLE "supplier" ADD COLUMN IF NOT EXISTS "primary_contact_phone" text NULL;`
    );
    this.addSql(
      `ALTER TABLE "supplier" ADD COLUMN IF NOT EXISTS "secondary_contact_name" text NULL;`
    );
    this.addSql(
      `ALTER TABLE "supplier" ADD COLUMN IF NOT EXISTS "secondary_contact_email" text NULL;`
    );
    this.addSql(
      `ALTER TABLE "supplier" ADD COLUMN IF NOT EXISTS "secondary_contact_phone" text NULL;`
    );

    // Remove new fields from supplier table
    this.addSql(`ALTER TABLE "supplier" DROP COLUMN IF EXISTS "email";`);
    this.addSql(`ALTER TABLE "supplier" DROP COLUMN IF EXISTS "phone";`);
    this.addSql(`ALTER TABLE "supplier" DROP COLUMN IF EXISTS "region";`);
    this.addSql(`ALTER TABLE "supplier" DROP COLUMN IF EXISTS "timezone";`);
    this.addSql(
      `ALTER TABLE "supplier" DROP COLUMN IF EXISTS "language_preference";`
    );
    this.addSql(
      `ALTER TABLE "supplier" DROP COLUMN IF EXISTS "payment_method";`
    );
    this.addSql(`ALTER TABLE "supplier" DROP COLUMN IF EXISTS "payout_terms";`);
    this.addSql(
      `ALTER TABLE "supplier" DROP COLUMN IF EXISTS "bank_account_details";`
    );
    this.addSql(`ALTER TABLE "supplier" DROP COLUMN IF EXISTS "categories";`);

    // Restore 'type' field
    this.addSql(
      `ALTER TABLE "supplier" ADD COLUMN IF NOT EXISTS "type" text NULL;`
    );
    this.addSql(
      `UPDATE "supplier" SET "type" = "business_type" WHERE "type" IS NULL;`
    );
    this.addSql(
      `ALTER TABLE "supplier" DROP COLUMN IF EXISTS "business_type";`
    );

    // Drop new indexes
    this.addSql(`DROP INDEX IF EXISTS "IDX_supplier_status_business_type";`);
    this.addSql(`DROP INDEX IF EXISTS "IDX_supplier_business_type";`);
    this.addSql(`DROP INDEX IF EXISTS "IDX_supplier_email";`);
    this.addSql(`DROP INDEX IF EXISTS "IDX_supplier_region";`);

    // Restore old indexes
    this.addSql(
      `CREATE INDEX IF NOT EXISTS "IDX_supplier_status_type" ON "supplier" ("status", "type") WHERE "deleted_at" IS NULL;`
    );
    this.addSql(
      `CREATE INDEX IF NOT EXISTS "IDX_supplier_type" ON "supplier" ("type") WHERE "deleted_at" IS NULL;`
    );

    // No constraints to drop since business_type uses lookup table
  }
}
