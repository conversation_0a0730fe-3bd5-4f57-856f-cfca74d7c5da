import { Migration } from '@mikro-orm/migrations';

export class Migration20250708181000 extends Migration {

  override async up(): Promise<void> {
    // Drop the problematic index if it exists
    this.addSql(`DROP INDEX IF EXISTS "IDX_vendor_status_type";`);
    
    // Check if the vendor table exists and has the type column
    // If not, add it
    this.addSql(`
      DO $$ 
      BEGIN 
        IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'vendor') THEN
          IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'vendor' AND column_name = 'type') THEN
            ALTER TABLE vendor ADD COLUMN type text CHECK (type IN ('hotel_partner', 'service_provider', 'transport_provider', 'activity_provider', 'tour_operator')) NOT NULL DEFAULT 'hotel_partner';
          END IF;
          
          -- Now create the index safely
          CREATE INDEX IF NOT EXISTS "IDX_vendor_status_type" ON "vendor" (status, type) WHERE deleted_at IS NULL;
        END IF;
      END $$;
    `);
  }

  override async down(): Promise<void> {
    // Drop the index
    this.addSql(`DROP INDEX IF EXISTS "IDX_vendor_status_type";`);
    
    // Optionally remove the type column if it was added by this migration
    this.addSql(`
      DO $$ 
      BEGIN 
        IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'vendor' AND column_name = 'type') THEN
          ALTER TABLE vendor DROP COLUMN IF EXISTS type;
        END IF;
      END $$;
    `);
  }

}
