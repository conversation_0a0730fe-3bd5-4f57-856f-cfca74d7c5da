import { Migration } from "@mikro-orm/migrations";

/**
 * Migration to convert currency values from full descriptions back to 3-letter codes
 * 
 * This migration converts any existing currency values that might be stored as full descriptions
 * (e.g., "USD - US Dollar") back to 3-letter codes (e.g., "USD") for consistency.
 */
export class Migration20250711040000 extends Migration {
  async up(): Promise<void> {
    // Convert full currency descriptions back to 3-letter codes
    this.addSql(`
      UPDATE "supplier" 
      SET "default_currency" = 
        CASE 
          WHEN "default_currency" LIKE 'CHF%' THEN 'CHF'
          WHEN "default_currency" LIKE 'EUR%' THEN 'EUR'
          WHEN "default_currency" LIKE 'USD%' THEN 'USD'
          WHEN "default_currency" LIKE 'GBP%' THEN 'GBP'
          WHEN "default_currency" LIKE 'CAD%' THEN 'CAD'
          WHEN "default_currency" LIKE 'AUD%' THEN 'AUD'
          WHEN "default_currency" LIKE 'JPY%' THEN 'JPY'
          ELSE "default_currency"
        END
      WHERE "default_currency" IS NOT NULL 
        AND LENGTH("default_currency") > 3;
    `);

    // Also update supplier_offering table if it exists and has currency field
    this.addSql(`
      UPDATE "supplier_offering" 
      SET "currency" = 
        CASE 
          WHEN "currency" LIKE 'CHF%' THEN 'CHF'
          WHEN "currency" LIKE 'EUR%' THEN 'EUR'
          WHEN "currency" LIKE 'USD%' THEN 'USD'
          WHEN "currency" LIKE 'GBP%' THEN 'GBP'
          WHEN "currency" LIKE 'CAD%' THEN 'CAD'
          WHEN "currency" LIKE 'AUD%' THEN 'AUD'
          WHEN "currency" LIKE 'JPY%' THEN 'JPY'
          ELSE "currency"
        END
      WHERE "currency" IS NOT NULL 
        AND LENGTH("currency") > 3;
    `);
  }

  async down(): Promise<void> {
    // Convert 3-letter codes back to full descriptions
    this.addSql(`
      UPDATE "supplier" 
      SET "default_currency" = 
        CASE 
          WHEN "default_currency" = 'CHF' THEN 'CHF - Swiss Franc'
          WHEN "default_currency" = 'EUR' THEN 'EUR - Euro'
          WHEN "default_currency" = 'USD' THEN 'USD - US Dollar'
          WHEN "default_currency" = 'GBP' THEN 'GBP - British Pound'
          WHEN "default_currency" = 'CAD' THEN 'CAD - Canadian Dollar'
          WHEN "default_currency" = 'AUD' THEN 'AUD - Australian Dollar'
          WHEN "default_currency" = 'JPY' THEN 'JPY - Japanese Yen'
          ELSE "default_currency"
        END
      WHERE "default_currency" IS NOT NULL 
        AND LENGTH("default_currency") = 3;
    `);

    // Also revert supplier_offering table
    this.addSql(`
      UPDATE "supplier_offering" 
      SET "currency" = 
        CASE 
          WHEN "currency" = 'CHF' THEN 'CHF - Swiss Franc'
          WHEN "currency" = 'EUR' THEN 'EUR - Euro'
          WHEN "currency" = 'USD' THEN 'USD - US Dollar'
          WHEN "currency" = 'GBP' THEN 'GBP - British Pound'
          WHEN "currency" = 'CAD' THEN 'CAD - Canadian Dollar'
          WHEN "currency" = 'AUD' THEN 'AUD - Australian Dollar'
          WHEN "currency" = 'JPY' THEN 'JPY - Japanese Yen'
          ELSE "currency"
        END
      WHERE "currency" IS NOT NULL 
        AND LENGTH("currency") = 3;
    `);
  }
}
