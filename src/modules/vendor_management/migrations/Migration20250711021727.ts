import { Migration } from "@mikro-orm/migrations";

export class Migration20250711021727 extends Migration {
  override async up(): Promise<void> {
    this.addSql(
      `alter table if exists "supplier" drop constraint if exists "supplier_status_check";`
    );

    this.addSql(`drop index if exists "IDX_supplier_categories";`);
    this.addSql(
      `alter table if exists "supplier" drop column if exists "categories";`
    );

    this.addSql(
      `alter table if exists "supplier" add column if not exists "preference" text check ("preference" in ('Preferred', 'Backup')) null;`
    );

    // Update existing status values to match the new constraint before applying it
    this.addSql(
      `UPDATE "supplier" SET "status" = 'Active' WHERE "status" = 'active';`
    );
    this.addSql(
      `UPDATE "supplier" SET "status" = 'Inactive' WHERE "status" = 'inactive';`
    );
    this.addSql(
      `UPDATE "supplier" SET "status" = 'Pending Approval' WHERE "status" = 'pending_approval';`
    );
    this.addSql(
      `UPDATE "supplier" SET "status" = 'Suspended' WHERE "status" = 'suspended';`
    );
    this.addSql(
      `UPDATE "supplier" SET "status" = 'Terminated' WHERE "status" = 'terminated';`
    );

    // Now apply the constraint after updating the data
    this.addSql(
      `alter table if exists "supplier" add constraint "supplier_status_check" check("status" in ('Active', 'Inactive', 'Pending Approval', 'Suspended', 'Terminated'));`
    );

    this.addSql(`drop index if exists "IDX_supplier_document_type";`);
    this.addSql(`drop index if exists "IDX_supplier_document_status";`);
    this.addSql(`drop index if exists "IDX_supplier_document_expiry";`);
    this.addSql(
      `drop index if exists "IDX_supplier_document_current_version";`
    );
    this.addSql(
      `alter table if exists "supplier_document" drop column if exists "document_type", drop column if exists "description", drop column if exists "file_size", drop column if exists "file_type", drop column if exists "status", drop column if exists "expiry_date", drop column if exists "reminder_days_before", drop column if exists "reviewed_by", drop column if exists "reviewed_at", drop column if exists "review_notes", drop column if exists "version", drop column if exists "is_current_version", drop column if exists "parent_document_id";`
    );

    this.addSql(
      `alter table if exists "supplier_document" add column if not exists "rank" integer not null default 0;`
    );

    // Check if file_url column exists before renaming it
    this.addSql(`DO $$
      BEGIN
        IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'supplier_document' AND column_name = 'file_url') THEN
          ALTER TABLE "supplier_document" RENAME COLUMN "file_url" TO "url";
        ELSE
          -- If file_url doesn't exist, add url column if it doesn't exist
          ALTER TABLE "supplier_document" ADD COLUMN IF NOT EXISTS "url" text;
        END IF;
      END $$;`);
  }

  override async down(): Promise<void> {
    this.addSql(
      `alter table if exists "supplier" drop constraint if exists "supplier_status_check";`
    );

    this.addSql(
      `alter table if exists "supplier" drop column if exists "preference";`
    );

    this.addSql(
      `alter table if exists "supplier" add column if not exists "categories" jsonb null;`
    );
    this.addSql(
      `alter table if exists "supplier" add constraint "supplier_status_check" check("status" in ('active', 'inactive', 'pending_approval', 'suspended', 'terminated'));`
    );
    this.addSql(
      `CREATE INDEX IF NOT EXISTS "IDX_supplier_categories" ON "supplier" USING gin (categories) WHERE deleted_at IS NULL;`
    );

    this.addSql(
      `alter table if exists "supplier_document" drop column if exists "rank";`
    );

    this.addSql(
      `alter table if exists "supplier_document" add column if not exists "document_type" text check ("document_type" in ('contract', 'certificate', 'insurance', 'license', 'compliance', 'tax_document', 'bank_details', 'other')) not null, add column if not exists "description" text null, add column if not exists "file_size" integer null, add column if not exists "file_type" text null, add column if not exists "status" text check ("status" in ('pending_review', 'approved', 'rejected', 'expired')) not null default 'pending_review', add column if not exists "expiry_date" timestamptz null, add column if not exists "reminder_days_before" integer not null default 30, add column if not exists "reviewed_by" text null, add column if not exists "reviewed_at" timestamptz null, add column if not exists "review_notes" text null, add column if not exists "version" text not null default '1.0', add column if not exists "is_current_version" boolean not null default true, add column if not exists "parent_document_id" text null;`
    );
    // Check if url column exists before renaming it back to file_url
    this.addSql(`DO $$
      BEGIN
        IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'supplier_document' AND column_name = 'url') THEN
          ALTER TABLE "supplier_document" RENAME COLUMN "url" TO "file_url";
        END IF;
      END $$;`);
    this.addSql(
      `CREATE INDEX IF NOT EXISTS "IDX_supplier_document_type" ON "supplier_document" (document_type) WHERE deleted_at IS NULL;`
    );
    this.addSql(
      `CREATE INDEX IF NOT EXISTS "IDX_supplier_document_status" ON "supplier_document" (status) WHERE deleted_at IS NULL;`
    );
    this.addSql(
      `CREATE INDEX IF NOT EXISTS "IDX_supplier_document_expiry" ON "supplier_document" (expiry_date) WHERE deleted_at IS NULL AND expiry_date IS NOT NULL;`
    );
    this.addSql(
      `CREATE INDEX IF NOT EXISTS "IDX_supplier_document_current_version" ON "supplier_document" (is_current_version) WHERE deleted_at IS NULL;`
    );
  }
}
