import { Migration } from "@mikro-orm/migrations";

/**
 * Migration to remove individual address fields from supplier table
 *
 * This migration removes the individual address fields (address_line_1, 
 * address_line_2, city, state, postal_code, country) from the supplier 
 * table since we now use a single 'address' field for complete address 
 * information.
 */
export class Migration20250719130000 extends Migration {
  async up(): Promise<void> {
    // Remove individual address columns from the supplier table
    this.addSql(`ALTER TABLE "supplier" DROP COLUMN IF EXISTS "address_line_1";`);
    this.addSql(`ALTER TABLE "supplier" DROP COLUMN IF EXISTS "address_line_2";`);
    this.addSql(`ALTER TABLE "supplier" DROP COLUMN IF EXISTS "city";`);
    this.addSql(`ALTER TABLE "supplier" DROP COLUMN IF EXISTS "state";`);
    this.addSql(`ALTER TABLE "supplier" DROP COLUMN IF EXISTS "postal_code";`);
    this.addSql(`ALTER TABLE "supplier" DROP COLUMN IF EXISTS "country";`);
  }

  async down(): Promise<void> {
    // Re-add the individual address columns (as nullable since we can't restore the original data)
    this.addSql(`ALTER TABLE "supplier" ADD COLUMN "address_line_1" text NULL;`);
    this.addSql(`ALTER TABLE "supplier" ADD COLUMN "address_line_2" text NULL;`);
    this.addSql(`ALTER TABLE "supplier" ADD COLUMN "city" text NULL;`);
    this.addSql(`ALTER TABLE "supplier" ADD COLUMN "state" text NULL;`);
    this.addSql(`ALTER TABLE "supplier" ADD COLUMN "postal_code" text NULL;`);
    this.addSql(`ALTER TABLE "supplier" ADD COLUMN "country" text NULL;`);
  }
}
