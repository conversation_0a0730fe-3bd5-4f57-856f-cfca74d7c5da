import { Migration } from "@mikro-orm/migrations";

/**
 * Migration to add supplier_type field to supplier table
 *
 * Adds the supplier_type field to distinguish between company and individual suppliers.
 * This field is used in the frontend form but was missing from the database schema.
 */
export class Migration20250704120000 extends Migration {
  async up(): Promise<void> {
    // Add supplier_type field to supplier table
    this.addSql(
      `ALTER TABLE "supplier" ADD COLUMN IF NOT EXISTS "supplier_type" text NOT NULL DEFAULT 'Company';`
    );

    // Add check constraint to ensure only valid values
    this.addSql(
      `ALTER TABLE "supplier" ADD CONSTRAINT "supplier_type_check" CHECK ("supplier_type" IN ('Company', 'Individual'));`
    );

    // Add index for supplier_type queries
    this.addSql(
      `CREATE INDEX IF NOT EXISTS "IDX_supplier_supplier_type" ON "supplier" ("supplier_type") WHERE "deleted_at" IS NULL;`
    );
  }

  async down(): Promise<void> {
    // Drop the index
    this.addSql(`DROP INDEX IF EXISTS "IDX_supplier_supplier_type";`);

    // Drop the check constraint
    this.addSql(
      `ALTER TABLE "supplier" DROP CONSTRAINT IF EXISTS "supplier_type_check";`
    );

    // Drop the supplier_type column
    this.addSql(
      `ALTER TABLE "supplier" DROP COLUMN IF EXISTS "supplier_type";`
    );
  }
}
