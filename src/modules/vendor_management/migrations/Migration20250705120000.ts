import { Migration } from '@mikro-orm/migrations';

/**
 * Migration to add default_currency field to supplier table
 * 
 * Adds the default_currency field to store the supplier's preferred currency
 * for pricing and financial transactions.
 */
export class Migration20250705120000 extends Migration {
  async up(): Promise<void> {
    // Add default_currency field to supplier table
    this.addSql(`ALTER TABLE "supplier" ADD COLUMN IF NOT EXISTS "default_currency" text NULL;`);

    // Add index for currency-based queries
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_supplier_default_currency" ON "supplier" ("default_currency") WHERE "deleted_at" IS NULL;`);
  }

  async down(): Promise<void> {
    // Remove index
    this.addSql(`DROP INDEX IF EXISTS "IDX_supplier_default_currency";`);

    // Remove column
    this.addSql(`ALTER TABLE "supplier" DROP COLUMN IF EXISTS "default_currency";`);
  }
}
