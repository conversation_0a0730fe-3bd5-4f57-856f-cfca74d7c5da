import { MedusaService, Modules } from "@camped-ai/framework/utils";
import {
  Supplier,
  SupplierContact,
  SupplierProduct,
  SupplierService,
  SupplierProductPricing,
  SupplierServicePricing,
  SupplierOrder,
  SupplierOrderItem,
  SupplierContract,
  SupplierContractPayment,
  SupplierDocument,
  SupplierContactHistory,
  ExchangeRate,
} from "./models";
import { uploadFilesWorkflow } from "@camped-ai/medusa/core-flows";
import {
  CreateSupplierInput,
  UpdateSupplierInput,
  CreateSupplierProductInput,
  UpdateSupplierProductInput,
  CreateSupplierServiceInput,
  UpdateSupplierServiceInput,
  CreateSupplierOrderInput,
  CreateSupplierOrderItemInput,
  SupplierFilters,
  SupplierProductFilters,
  SupplierServiceFilters,
  SupplierOrderFilters,
  ListOptions,
  StockUpdateData,
  SupplierStatus,
  SupplierVerificationStatus,
  SupplierPerformanceMetrics,
} from "./types";
import { BOOKING_ADD_ONS_MODULE } from "../booking-add-ons";

class SupplierModuleService extends MedusaService({
  Supplier,
  SupplierContact,
  SupplierProduct,
  SupplierService,
  SupplierProductPricing,
  SupplierServicePricing,
  SupplierOrder,
  SupplierOrderItem,
  SupplierContract,
  SupplierContractPayment,
  SupplierDocument,
  SupplierContactHistory,
  ExchangeRate,
}) {
  protected container_: any;
  protected bookingAddOnService_: any;

  constructor(container: any) {
    super(container);
    this.container_ = container;

    // Resolve booking add-on service for direct synchronization
    try {
      this.bookingAddOnService_ = container.resolve(BOOKING_ADD_ONS_MODULE);
      console.log("✅ Booking add-on service resolved successfully");
    } catch (error) {
      console.warn("Could not resolve booking add-on service:", error.message);
      this.bookingAddOnService_ = null;
    }
  }

  private async emitEvent(eventName: string, data: any) {
    try {
      // Try to resolve event bus service at runtime
      let eventBusService = null;

      try {
        eventBusService = this.container_.resolve(Modules.EVENT_BUS);
      } catch (error) {
        try {
          eventBusService = this.container_.resolve("eventBusService");
        } catch (error2) {
          try {
            eventBusService = this.container_.resolve("eventBusModuleService");
          } catch (error3) {
            console.warn(
              "⚠️ Could not resolve event bus service, skipping event emission"
            );
            return;
          }
        }
      }

      if (eventBusService) {
        await eventBusService.emit({
          name: eventName,
          data: data,
        });
        console.log(`✅ Event '${eventName}' emitted successfully`);
      }
    } catch (error) {
      console.error(`❌ Failed to emit event '${eventName}':`, error);
    }
  }

  private async syncBookingAddOnStatuses(orderId: string, newStatus: string) {
    if (!this.bookingAddOnService_) {
      console.warn(
        "⚠️ Booking add-on service not available, skipping status synchronization"
      );
      return;
    }

    try {
      console.log(
        `🔄 Synchronizing booking add-on statuses for order ${orderId} to status: ${newStatus}`
      );

      // Find all booking add-ons associated with this supplier order
      const bookingAddOns = await this.bookingAddOnService_.list({
        supplier_order_id: orderId,
      });

      if (bookingAddOns && bookingAddOns.length > 0) {
        console.log(
          `📋 Found ${bookingAddOns.length} booking add-on(s) to update`
        );

        // Update each booking add-on's order_status
        for (const addOn of bookingAddOns) {
          await this.bookingAddOnService_.update(addOn.id, {
            order_status: newStatus,
          });
          console.log(
            `✅ Updated booking add-on ${addOn.id} status to: ${newStatus}`
          );
        }

        console.log(
          `🎉 Successfully synchronized ${bookingAddOns.length} booking add-on status(es)`
        );
      } else {
        console.log(
          `ℹ️ No booking add-ons found for supplier order ${orderId}`
        );
      }
    } catch (error) {
      console.error(`❌ Failed to synchronize booking add-on statuses:`, error);
    }
  }
  // Supplier Management Methods
  async createSupplierWithHandle(data: CreateSupplierInput): Promise<any> {
    console.log("Creating supplier with data:", data);
    console.log("Categories field in data:", data.categories);

    // Generate handle from name if not provided
    if (!data.handle && data.name) {
      data.handle = data.name
        .toLowerCase()
        .replace(/[^a-z0-9]+/g, "-")
        .replace(/^-+|-+$/g, "");
    }

    console.log("Generated handle:", data.handle);

    // Check if handle already exists
    const existingSuppliers = await this.listSuppliers({ handle: data.handle });
    console.log("Existing suppliers check:", existingSuppliers);

    if (
      existingSuppliers &&
      existingSuppliers.suppliers &&
      existingSuppliers.suppliers.length > 0
    ) {
      throw new Error(`Supplier with handle '${data.handle}' already exists`);
    }

    // Create supplier using the inherited MedusaService method
    try {
      console.log("Attempting to create supplier with createSuppliers method");

      // Extract contacts from data (if present)
      const { contacts, ...supplierData } = data as any;
      console.log("Extracted contacts:", contacts);
      console.log("Supplier data without contacts:", supplierData);

      // Transform data to match model expectations
      const transformedData: any = {
        ...supplierData,
      };

      console.log("Transformed data for createSuppliers:", transformedData);
      console.log(
        "Categories in transformed data:",
        transformedData.categories
      );

      const suppliers = await this.createSuppliers([transformedData]);
      const supplier = suppliers[0];
      console.log("Successfully created supplier:", supplier);

      // Create contacts in supplier_contact table if provided
      if (contacts && contacts.length > 0) {
        const contactsToCreate = contacts.map((contact: any) => {
          console.log("Processing contact for creation:", contact);
          const contactData = {
            name: contact.name,
            email: contact.email,
            phone_number: contact.phone_number || contact.phone, // Handle both phone_number and phone fields
            is_whatsapp: Boolean(contact.is_whatsapp),
            is_primary: Boolean(contact.is_primary),
            supplier_id: supplier.id,
          };
          console.log("Contact data for creation:", contactData);
          return contactData;
        });

        // Create contacts using custom method
        const createdContacts = await this.createContactsForSupplier(
          contactsToCreate
        );

        // Return supplier with contacts populated
        return {
          ...supplier,
          contacts: Array.isArray(createdContacts)
            ? createdContacts
            : [createdContacts],
        };
      }

      console.log("No contacts provided, returning supplier as-is");
      return supplier;
    } catch (error) {
      console.error("Error creating supplier:", error);
      throw new Error(`Failed to create supplier: ${error.message}`);
    }
  }

  async listSuppliers(
    filters: SupplierFilters = {},
    options: ListOptions = {}
  ): Promise<any> {
    try {
      // Set default pagination
      const limit = filters.limit || 20;
      const offset = filters.offset || 0;

      // Build query filters
      const queryFilters: any = {};

      if (filters.status) {
        queryFilters.status = filters.status;
      }



      if (filters.preference) {
        queryFilters.preference = filters.preference; // Filter by preference level
      }

      // Handle exact name filter (used for duplicate checking)
      if (filters.name) {
        queryFilters.name = filters.name;
      }

      // Handle exact email filter (used for duplicate checking)
      if (filters.email) {
        queryFilters.email = filters.email;
      }

      // Handle handle filter (used for duplicate checking)
      if (filters.handle) {
        queryFilters.handle = filters.handle;
      }

      if (filters.search) {
        queryFilters.$or = [
          { name: { $ilike: `%${filters.search}%` } },
          { description: { $ilike: `%${filters.search}%` } },
        ];
      }

      // Use the inherited MedusaService list method for Supplier entity
      const [suppliers, count] = await this.listAndCountSuppliers(
        queryFilters,
        {
          skip: offset,
          take: limit,
          order: { created_at: "DESC" },
          ...options,
        }
      );

      return {
        suppliers: suppliers || [],
        count: count || 0,
        limit,
        offset,
      };
    } catch (error) {
      console.error("Error in listSuppliers:", error);
      throw error;
    }
  }

  async updateSupplier(supplierId: string, data: any): Promise<any> {
    try {
      console.log("Updating supplier:", supplierId, "with data:", data);

      // Extract contacts from data (if present)
      const { id, contacts, ...updateData } = data as any;

      // Use the inherited updateSuppliers method with proper format
      const updatedSuppliers = await this.updateSuppliers({
        selector: { id: supplierId },
        data: {
          ...updateData,
          metadata: {
            ...updateData.metadata,
            updated_at: new Date().toISOString(),
          },
        },
      });

      console.log("Successfully updated supplier:", updatedSuppliers);

      let supplier;
      // If updateSuppliers returns an array, use the first item
      if (Array.isArray(updatedSuppliers) && updatedSuppliers.length > 0) {
        supplier = updatedSuppliers[0];
      } else if (updatedSuppliers && typeof updatedSuppliers === "object") {
        // If it returns a single object, use it
        supplier = updatedSuppliers;
      } else {
        // Fallback: retrieve the updated supplier
        console.log(
          "Update result was unexpected, retrieving supplier:",
          supplierId
        );
        supplier = await this.retrieveSupplier(supplierId);
      }

      // Handle contacts update if provided
      if (contacts && contacts.length > 0) {
        console.log("Updating contacts for supplier:", supplierId);

        // Delete existing contacts for this supplier
        try {
          const existingContacts = await this.getSupplierContacts(supplierId);
          if (existingContacts && existingContacts.length > 0) {
            const contactIds = existingContacts.map((contact) => contact.id);
            await this.removeSupplierContacts(contactIds);
          }
        } catch (deleteError) {
          console.warn("Error deleting existing contacts:", deleteError);
        }

        // Create new contacts
        const contactsToCreate = contacts.map((contact: any) => {
          console.log("Processing contact for creation:", contact);
          const contactData = {
            name: contact.name,
            email: contact.email,
            phone_number: contact.phone_number || contact.phone, // Handle both phone_number and phone fields
            is_whatsapp: Boolean(contact.is_whatsapp),
            is_primary: Boolean(contact.is_primary),
            supplier_id: supplierId,
          };
          console.log("Contact data for creation:", contactData);
          return contactData;
        });

        console.log("Creating contacts:", contactsToCreate);

        try {
          const createdContacts = await this.createContactsForSupplier(
            contactsToCreate
          );

          // Return supplier with updated contacts
          return {
            ...supplier,
            contacts: Array.isArray(createdContacts)
              ? createdContacts
              : [createdContacts],
          };
        } catch (contactError) {
          console.error("Error updating contacts:", contactError);
          // Return supplier even if contact update fails
          return supplier;
        }
      }

      return supplier;
    } catch (error) {
      console.error("Error updating supplier:", error);
      throw new Error(`Failed to update supplier: ${error.message}`);
    }
  }

  async deleteSupplier(supplierId: string): Promise<any> {
    try {
      console.log("Deleting supplier:", supplierId);

      // First check if supplier exists
      try {
        await this.retrieveSupplier(supplierId);
      } catch (retrieveError) {
        console.log(
          "Supplier not found, considering it already deleted:",
          supplierId
        );
        return { id: supplierId, deleted: true };
      }

      // Related contacts will be automatically deleted by database CASCADE DELETE
      console.log(
        "Deleting supplier - related contacts will be automatically removed by CASCADE DELETE"
      );

      // Now delete the supplier
      const result = await this.deleteSuppliers([supplierId]);

      console.log("Successfully deleted supplier:", result);
      return result;
    } catch (error) {
      console.error("Error deleting supplier:", error);
      throw new Error(`Failed to delete supplier: ${error.message}`);
    }
  }

  async createContactsForSupplier(contactsData: any[]): Promise<any[]> {
    // Use the auto-generated createSupplierContacts method now that SupplierContact is registered
    const createdContacts = await this.createSupplierContacts(contactsData);

    // Return the created contacts (handle both single object and array responses)
    return Array.isArray(createdContacts) ? createdContacts : [createdContacts];
  }

  async getSupplierContacts(supplierId: string): Promise<any[]> {
    try {
      // Use the auto-generated listSupplierContacts method
      const contacts = await this.listSupplierContacts({
        supplier_id: supplierId,
      });
      return Array.isArray(contacts) ? contacts : [contacts];
    } catch (error) {
      console.error("Error listing supplier contacts:", error);
      return [];
    }
  }

  async removeSupplierContacts(contactIds: string[]): Promise<any> {
    try {
      // Use the auto-generated deleteSupplierContacts method
      return await this.deleteSupplierContacts(contactIds);
    } catch (error) {
      console.error("Error deleting supplier contacts:", error);
      throw error;
    }
  }

  async updateSupplierStatus(
    supplierId: string,
    status: SupplierStatus
  ): Promise<any> {
    const supplier = await this.retrieveSupplier(supplierId);

    return await this.updateSupplier(supplierId, {
      status,
      metadata: {
        ...supplier.metadata,
        status_updated_at: new Date().toISOString(),
      },
    });
  }

  // Product Management Methods
  async createSupplierProduct(data: CreateSupplierProductInput): Promise<any> {
    // Validate supplier exists
    await this.retrieveSupplier(data.supplier_id);

    // Use the generated plural method with array
    const products = await this.createSupplierProducts([data]);
    return products[0];
  }

  async updateProductInventory(
    productId: string,
    stockData: StockUpdateData
  ): Promise<any> {
    const product = await this.retrieveSupplierProduct(productId);

    const updatedStock = {
      current_stock: stockData.current_stock ?? product.current_stock,
      minimum_stock: stockData.minimum_stock ?? product.minimum_stock,
      maximum_stock: stockData.maximum_stock ?? product.maximum_stock,
    };

    return await this.updateSupplierProduct(productId, updatedStock);
  }

  async getSupplierProducts(
    supplierId: string,
    filters: SupplierProductFilters = {}
  ): Promise<any[]> {
    return await this.listSupplierProducts({
      ...filters,
      supplier_id: supplierId,
    });
  }

  // Service Management Methods
  async createSupplierService(data) {
    // Validate supplier exists
    await this.retrieveSupplier(data.supplier_id);

    // Use the generated plural method with array
    const services = await this.createSupplierServices([data]);
    return services[0];
  }

  async updateServiceAvailability(serviceId, isAvailable) {
    const service = await this.retrieveSupplierService(serviceId);

    return await this.updateSupplierService(serviceId, {
      is_available: isAvailable,
      metadata: {
        ...service.metadata,
        availability_updated_at: new Date().toISOString(),
      },
    });
  }

  async getSupplierServices(supplierId, filters = {}) {
    return await this.listSupplierServices({
      ...filters,
      supplier_id: supplierId,
    });
  }

  // Pricing Management Methods
  async createProductPricing(data) {
    // Validate product exists
    await this.retrieveSupplierProduct(data.product_id);

    return await this.createSupplierProductPricing(data);
  }

  async createServicePricing(data) {
    // Validate service exists
    await this.retrieveSupplierService(data.service_id);

    return await this.createSupplierServicePricing(data);
  }

  async getActiveProductPricing(productId) {
    const now = new Date();

    const pricingList = await this.listSupplierProductPricings({
      product_id: productId,
      is_active: true,
    });

    return pricingList.filter((pricing) => {
      if (!pricing.valid_from && !pricing.valid_until) return true;
      if (pricing.valid_from && now < new Date(pricing.valid_from))
        return false;
      if (pricing.valid_until && now > new Date(pricing.valid_until))
        return false;
      return true;
    });
  }

  async getActiveServicePricing(serviceId) {
    const now = new Date();

    const pricingList = await this.listSupplierServicePricings({
      service_id: serviceId,
      is_active: true,
    });

    return pricingList.filter((pricing) => {
      if (!pricing.valid_from && !pricing.valid_until) return true;
      if (pricing.valid_from && now < new Date(pricing.valid_from))
        return false;
      if (pricing.valid_until && now > new Date(pricing.valid_until))
        return false;
      return true;
    });
  }

  // Order Management Methods
  async generateSupplierOrderNumber(): Promise<string> {
    const currentYear = new Date().getFullYear();
    const prefix = `SO-${currentYear}-`;

    try {
      // Get the latest order number for the current year
      const existingOrders = await this.listSupplierOrders({
        order_number: { $like: `${prefix}%` }
      }, {
        select: ["order_number"],
        order: { order_number: "DESC" },
        take: 1
      });

      let nextSequence = 1;

      if (existingOrders.length > 0) {
        const latestOrderNumber = existingOrders[0].order_number;
        // Extract the sequence number from SO-2025-00001 format
        const sequenceMatch = latestOrderNumber.match(/SO-\d{4}-(\d{5})$/);
        if (sequenceMatch) {
          nextSequence = parseInt(sequenceMatch[1], 10) + 1;
        }
      }

      // Format sequence number with leading zeros (5 digits)
      const sequenceStr = nextSequence.toString().padStart(5, '0');
      return `${prefix}${sequenceStr}`;
    } catch (error) {
      console.error('Error generating supplier order number:', error);
      // Fallback to timestamp-based number if there's an error
      const timestamp = Date.now();
      return `SO-${currentYear}-${timestamp.toString().slice(-5)}`;
    }
  }

  async createSupplierOrder(data) {
    // Validate supplier exists
    await this.retrieveSupplier(data.supplier_id);

    // Generate order number if not provided
    if (!data.order_number) {
      data.order_number = await this.generateSupplierOrderNumber();
    }

    // Use the generated plural method with array
    const orders = await this.createSupplierOrders([data]);
    return orders[0];
  }

  async createSupplierOrderItem(data) {
    // Use the generated plural method with array
    const orderItems = await this.createSupplierOrderItems([data]);
    return orderItems[0];
  }

  async getOrderItems(orderId: string) {
    // Get order items for a specific order
    return await this.listSupplierOrderItems({
      order_id: orderId,
    });
  }

  async updateOrderStatus(orderId, status) {
    const order = await this.retrieveSupplierOrder(orderId);
    const previousStatus = order.status;

    const updatedOrders = await this.updateSupplierOrders({
      selector: { id: orderId },
      data: {
        status,
        metadata: {
          ...order.metadata,
          status_updated_at: new Date().toISOString(),
          previous_status: previousStatus,
        },
      },
    });

    const updatedOrder = Array.isArray(updatedOrders)
      ? updatedOrders[0]
      : updatedOrders;

    // Emit event for status synchronization with booking add-ons
    if (previousStatus !== status) {
      console.log(`\n🔔 EMITTING SUPPLIER ORDER STATUS EVENT`);
      console.log(
        `📦 Order: ${updatedOrder.order_number} (${updatedOrder.id})`
      );
      console.log(`🔄 Status: ${previousStatus} → ${status}`);
      console.log(
        `📊 Metadata keys: [${Object.keys(updatedOrder.metadata || {}).join(
          ", "
        )}]`
      );
      console.log(
        `🔗 Booking add-on IDs: ${
          updatedOrder.metadata?.booking_addon_ids || "none"
        }`
      );

      // Emit event for other subscribers
      await this.emitEvent("supplier.order.updated", {
        order: updatedOrder,
        previous_status: previousStatus,
      });

      // Direct synchronization of booking add-on statuses
      await this.syncBookingAddOnStatuses(orderId, status);
    } else {
      console.log(`ℹ️ Status unchanged (${previousStatus}), no event emitted`);
    }

    return updatedOrder;
  }

  async updateOrderDetails(orderId, updateData) {
    const order = await this.retrieveSupplierOrder(orderId);
    const previousStatus = order.status;

    // Prepare update data
    const updatePayload = {
      ...updateData,
      metadata: {
        ...order.metadata,
        ...updateData.metadata,
        updated_at: new Date().toISOString(),
      },
    };

    const updatedOrders = await this.updateSupplierOrders({
      selector: { id: orderId },
      data: updatePayload,
    });

    const updatedOrder = Array.isArray(updatedOrders)
      ? updatedOrders[0]
      : updatedOrders;

    // Emit event for status synchronization if status changed
    if (updateData.status && previousStatus !== updateData.status) {
      console.log(
        `\n🔔 EMITTING SUPPLIER ORDER UPDATE EVENT (via updateOrderDetails)`
      );
      console.log(
        `📦 Order: ${updatedOrder.order_number} (${updatedOrder.id})`
      );
      console.log(`🔄 Status: ${previousStatus} → ${updateData.status}`);
      console.log(
        `📊 Metadata keys: [${Object.keys(updatedOrder.metadata || {}).join(
          ", "
        )}]`
      );
      console.log(
        `🔗 Booking add-on IDs: ${
          updatedOrder.metadata?.booking_addon_ids || "none"
        }`
      );

      // Emit event for other subscribers
      await this.emitEvent("supplier.order.updated", {
        order: updatedOrder,
        previous_status: previousStatus,
      });

      // Direct synchronization of booking add-on statuses (only if status changed)
      if (updateData.status && updateData.status !== previousStatus) {
        await this.syncBookingAddOnStatuses(orderId, updateData.status);
      }
    }

    return updatedOrder;
  }

  async addOrderItem(orderId, itemData) {
    const order = await this.retrieveSupplierOrder(orderId);

    // Validate item exists (product or service)
    if (itemData.item_type === "product") {
      await this.retrieveSupplierProduct(itemData.item_id);
    } else if (itemData.item_type === "service") {
      await this.retrieveSupplierService(itemData.item_id);
    }

    // Calculate total price
    itemData.total_price = itemData.quantity * itemData.unit_price;

    const orderItem = await this.createSupplierOrderItem({
      ...itemData,
      order_id: orderId,
    });

    // Update order totals
    const items = await this.getOrderItems(orderId);
    const subtotal = items.reduce((sum, item) => sum + item.total_price, 0);
    const total_amount = subtotal + (order.tax_amount || 0);

    await this.updateSupplierOrders({
      selector: { id: orderId },
      data: {
        subtotal,
        total_amount,
      },
    });

    return orderItem;
  }

  // Contract Management Methods
  async createSupplierContract(data) {
    // Validate supplier exists
    await this.retrieveSupplier(data.supplier_id);

    // Generate contract number if not provided
    if (!data.contract_number) {
      const timestamp = Date.now();
      data.contract_number = `SC-${timestamp}`;
    }

    // Use the generated plural method with array
    const contracts = await this.createSupplierContracts([data]);
    return contracts[0];
  }

  async updateContractStatus(contractId, status) {
    const contract = await this.retrieveSupplierContract(contractId);

    return await this.updateSupplierContract(contractId, {
      status,
      metadata: {
        ...contract.metadata,
        status_updated_at: new Date().toISOString(),
      },
    });
  }

  // Document Management Methods - Following hotel-image pattern
  async uploadSupplierDocuments(supplierId, files, container) {
    // First, upload the files using the file module
    const { result } = await uploadFilesWorkflow(container).run({
      input: {
        files: files.map((f) => ({
          filename: f.originalname,
          mimeType: f.mimetype,
          content: f.buffer.toString("binary"),
          access: "private", // Supplier documents should be private
        })),
      },
    });

    // Then create SupplierDocument records for each uploaded file
    const supplierDocuments = [];
    for (let i = 0; i < result.length; i++) {
      const file = result[i];
      const originalFile = files[i];

      const supplierDocument = await this.createSupplierDocument({
        supplier_id: supplierId,
        name: originalFile.originalname || `Document ${i + 1}`,
        url: file.url,
        metadata: {
          uploadedAt: new Date().toISOString(),
          originalName: originalFile.originalname,
          mimeType: originalFile.mimetype,
          size: originalFile.size,
          ...file, // Store all file properties in metadata
        },
      });
      supplierDocuments.push(supplierDocument);
    }

    return supplierDocuments;
  }

  // Create a single SupplierDocument record
  async createSupplierDocument(data) {
    // Get the current highest rank for this supplier's documents
    const existingDocuments = await this.listSupplierDocuments(
      {
        supplier_id: data.supplier_id,
      },
      {
        order: { rank: "DESC" },
        take: 1,
      }
    );

    const nextRank = existingDocuments.length
      ? existingDocuments[0].rank + 1
      : 0;

    // Create the supplier document
    return await this.createSupplierDocuments({
      name: data.name,
      url: data.url,
      metadata: data.metadata,
      rank: nextRank,
      supplier_id: data.supplier_id,
    });
  }

  async updateSupplierDocument(documentId, updateData) {
    const document = await this.retrieveSupplierDocument(documentId);

    return await this.updateSupplierDocuments(documentId, {
      name: updateData.name || document.name,
      url: updateData.url || document.url,
      rank: updateData.rank !== undefined ? updateData.rank : document.rank,
      metadata: {
        ...document.metadata,
        ...updateData.metadata,
        updatedAt: new Date().toISOString(),
      },
    });
  }

  async deleteSupplierDocument(documentId) {
    return await this.deleteSupplierDocuments([documentId]);
  }

  async getSupplierDocuments(supplierId, options = {}) {
    return await this.listSupplierDocuments(
      {
        supplier_id: supplierId,
      },
      options
    );
  }

  // Contact History Methods
  async createContactHistory(data) {
    // Validate supplier exists
    await this.retrieveSupplier(data.supplier_id);

    return await this.createSupplierContactHistory(data);
  }

  async getSupplierContactHistory(supplierId, filters = {}) {
    return await this.listSupplierContactHistories({
      ...filters,
      supplier_id: supplierId,
    });
  }

  // Analytics and Reporting Methods
  async getSupplierPerformanceMetrics(supplierId) {
    const supplier = await this.retrieveSupplier(supplierId);

    // Get orders for performance calculation
    const orders = await this.listSupplierOrders({
      supplier_id: supplierId,
    });

    const totalOrders = orders.length;
    const completedOrders = orders.filter(
      (order) => order.status === "completed"
    ).length;
    const cancelledOrders = orders.filter(
      (order) => order.status === "cancelled"
    ).length;

    const fulfillmentRate =
      totalOrders > 0 ? (completedOrders / totalOrders) * 100 : 0;
    const cancellationRate =
      totalOrders > 0 ? (cancelledOrders / totalOrders) * 100 : 0;

    // Calculate average order value
    const completedOrderValues = orders
      .filter((order) => order.status === "completed")
      .map((order) => order.total_amount);

    const averageOrderValue =
      completedOrderValues.length > 0
        ? completedOrderValues.reduce((sum, value) => sum + value, 0) /
          completedOrderValues.length
        : 0;

    return {
      supplier_id: supplierId,
      total_orders: totalOrders,
      completed_orders: completedOrders,
      cancelled_orders: cancelledOrders,
      fulfillment_rate: fulfillmentRate,
      cancellation_rate: cancellationRate,
      average_order_value: averageOrderValue,
      last_updated: new Date().toISOString(),
    };
  }

  // Exchange Rate Management Methods
  async createExchangeRate(data: any): Promise<any> {
    try {
      console.log("Creating exchange rate with data:", data);

      // Validate required fields
      if (!data.date || !data.base_currency || !data.selling_currency || !data.exchange_rate) {
        throw new Error("Missing required fields: date, base_currency, selling_currency, exchange_rate");
      }

      // Check for duplicate exchange rate for the same date and currency pair
      const existingRates = await this.listExchangeRates({
        date: data.date,
        base_currency: data.base_currency,
        selling_currency: data.selling_currency,
      });

      if (existingRates && existingRates.length > 0) {
        throw new Error(`Exchange rate already exists for ${data.base_currency}/${data.selling_currency} on ${data.date}`);
      }

      // Create the exchange rate using the inherited method
      const exchangeRates = await this.createExchangeRates([data]);
      const exchangeRate = exchangeRates[0];

      console.log("Successfully created exchange rate:", exchangeRate);
      return exchangeRate;
    } catch (error) {
      console.error("Error creating exchange rate:", error);
      throw error;
    }
  }

  async listExchangeRates(filters: any = {}, options: any = {}): Promise<any> {
    try {
      // Set default pagination
      const limit = filters.limit || 50;
      const offset = filters.offset || 0;

      // Build query filters
      const queryFilters: any = {};

      if (filters.date) {
        queryFilters.date = filters.date;
      }

      if (filters.base_currency) {
        queryFilters.base_currency = filters.base_currency;
      }

      if (filters.selling_currency) {
        queryFilters.selling_currency = filters.selling_currency;
      }

      if (filters.date_from && filters.date_to) {
        queryFilters.date = {
          $gte: filters.date_from,
          $lte: filters.date_to,
        };
      } else if (filters.date_from) {
        queryFilters.date = { $gte: filters.date_from };
      } else if (filters.date_to) {
        queryFilters.date = { $lte: filters.date_to };
      }

      // Use the inherited MedusaService list method for ExchangeRate entity
      const [exchangeRates, count] = await this.listAndCountExchangeRates(
        queryFilters,
        {
          skip: offset,
          take: limit,
          order: { date: "DESC", created_at: "DESC" },
          ...options,
        }
      );

      return {
        exchange_rates: exchangeRates || [],
        count: count || 0,
        limit,
        offset,
      };
    } catch (error) {
      console.error("Error in listExchangeRates:", error);
      throw error;
    }
  }

  async updateExchangeRate(exchangeRateId: string, data: any): Promise<any> {
    try {
      console.log("Updating exchange rate:", exchangeRateId, "with data:", data);

      // Retrieve existing exchange rate
      const existingRate = await this.retrieveExchangeRate(exchangeRateId);
      if (!existingRate) {
        throw new Error(`Exchange rate with id ${exchangeRateId} not found`);
      }

      // Check for duplicate if date or currencies are being changed
      if (data.date || data.base_currency || data.selling_currency) {
        const checkDate = data.date || existingRate.date;
        const checkBaseCurrency = data.base_currency || existingRate.base_currency;
        const checkSellingCurrency = data.selling_currency || existingRate.selling_currency;

        const duplicateRates = await this.listExchangeRates({
          date: checkDate,
          base_currency: checkBaseCurrency,
          selling_currency: checkSellingCurrency,
        });

        // Check if there's a duplicate that's not the current record
        const hasDuplicate = duplicateRates.exchange_rates?.some(
          (rate: any) => rate.id !== exchangeRateId
        );

        if (hasDuplicate) {
          throw new Error(`Exchange rate already exists for ${checkBaseCurrency}/${checkSellingCurrency} on ${checkDate}`);
        }
      }

      // Use the inherited updateExchangeRates method
      const updatedRates = await this.updateExchangeRates({
        selector: { id: exchangeRateId },
        data: {
          ...data,
          metadata: {
            ...existingRate.metadata,
            ...data.metadata,
            updated_at: new Date().toISOString(),
          },
        },
      });

      const updatedRate = Array.isArray(updatedRates) ? updatedRates[0] : updatedRates;
      console.log("Successfully updated exchange rate:", updatedRate);
      return updatedRate;
    } catch (error) {
      console.error("Error updating exchange rate:", error);
      throw error;
    }
  }

  async deleteExchangeRate(exchangeRateId: string): Promise<any> {
    try {
      console.log("Deleting exchange rate:", exchangeRateId);

      // Verify exchange rate exists
      const exchangeRate = await this.retrieveExchangeRate(exchangeRateId);
      if (!exchangeRate) {
        throw new Error(`Exchange rate with id ${exchangeRateId} not found`);
      }

      // Delete the exchange rate
      const result = await this.deleteExchangeRates([exchangeRateId]);

      console.log("Successfully deleted exchange rate:", result);
      return result;
    } catch (error) {
      console.error("Error deleting exchange rate:", error);
      throw new Error(`Failed to delete exchange rate: ${error.message}`);
    }
  }

  async getExchangeRateForCurrencyPair(
    baseCurrency: string,
    sellingCurrency: string,
    date?: string
  ): Promise<any> {
    try {
      const filters: any = {
        base_currency: baseCurrency,
        selling_currency: sellingCurrency,
      };

      if (date) {
        filters.date = date;
      }

      const result = await this.listExchangeRates(filters, {
        take: 1,
        order: { date: "DESC" },
      });

      return result.exchange_rates?.[0] || null;
    } catch (error) {
      console.error("Error getting exchange rate for currency pair:", error);
      throw error;
    }
  }
}

export default SupplierModuleService;
