import { model } from "@camped-ai/framework/utils";

export const ExchangeRate = model
  .define("exchange_rate", {
    id: model.id({ prefix: "exr" }).primaryKey(),
    
    // Exchange rate information
    date: model.dateTime(), // Date for which the exchange rate is valid
    base_currency: model.text(), // Base currency code (e.g., "USD", "EUR", "CHF")
    selling_currency: model.text(), // Selling currency code (e.g., "USD", "EUR", "CHF")
    exchange_rate: model.number(), // Exchange rate value (e.g., 1.0850)
    
    // Metadata for additional information
    metadata: model.json().nullable(),
  })
  .indexes([
    {
      name: "IDX_exchange_rate_date",
      on: ["date"],
      unique: false,
      where: "deleted_at IS NULL",
    },
    {
      name: "IDX_exchange_rate_currencies",
      on: ["base_currency", "selling_currency"],
      unique: false,
      where: "deleted_at IS NULL",
    },
    {
      name: "IDX_exchange_rate_date_currencies",
      on: ["date", "base_currency", "selling_currency"],
      unique: true,
      where: "deleted_at IS NULL",
    },
    {
      name: "IDX_exchange_rate_base_currency",
      on: ["base_currency"],
      unique: false,
      where: "deleted_at IS NULL",
    },
    {
      name: "IDX_exchange_rate_selling_currency",
      on: ["selling_currency"],
      unique: false,
      where: "deleted_at IS NULL",
    },
  ]);
