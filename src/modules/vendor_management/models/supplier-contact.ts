import { model } from "@camped-ai/framework/utils";

export const SupplierContact = model
  .define("supplier_contact", {
    id: model.id({ prefix: "scontact" }).primaryKey(),

    // Contact Information
    name: model.text(),
    email: model.text(),
    phone_number: model.text().nullable(),
    is_whatsapp: model.boolean().default(false),
    is_primary: model.boolean().default(false),

    // Relationships
    supplier: model.belongsTo(() => require("./supplier").Supplier, {
      foreignKey: "supplier_id",
    }),
  })
  .indexes([
    {
      name: "IDX_supplier_contact_supplier_id",
      on: ["supplier_id"],
      unique: false,
      where: "deleted_at IS NULL",
    },
    {
      name: "IDX_supplier_contact_email",
      on: ["email"],
      unique: false,
      where: "deleted_at IS NULL",
    },
    {
      name: "IDX_supplier_contact_phone",
      on: ["phone_number"],
      unique: false,
      where: "deleted_at IS NULL AND phone_number IS NOT NULL",
    },
    {
      name: "IDX_supplier_contact_is_primary",
      on: ["supplier_id", "is_primary"],
      unique: false,
      where: "deleted_at IS NULL",
    },
    // New indexes for contact filtering
    {
      name: "IDX_supplier_contact_email_primary",
      on: ["email", "is_primary"],
      unique: false,
      where: "deleted_at IS NULL",
    },
    {
      name: "IDX_supplier_contact_name_primary",
      on: ["name", "is_primary"],
      unique: false,
      where: "deleted_at IS NULL",
    },
  ]);
