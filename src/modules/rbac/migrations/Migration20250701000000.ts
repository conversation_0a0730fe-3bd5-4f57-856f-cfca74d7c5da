import { Migration } from '@mikro-orm/migrations';

export class Migration20250701000000 extends Migration {
  override async up(): Promise<void> {
    this.addSql(`
      create table if not exists "role" (
        "id" text not null,
        "name" text not null,
        "description" text,
        "permissions" jsonb not null default '[]',
        "is_system_role" boolean not null default false,
        "is_active" boolean not null default true,
        "created_by" text not null,
        "updated_by" text,
        "created_at" timestamptz not null default now(),
        "updated_at" timestamptz not null default now(),
        "deleted_at" timestamptz,
        constraint "role_pkey" primary key ("id")
      );
    `);

    // Create unique index on name
    this.addSql(`
      create unique index if not exists "role_name_unique" 
      on "role" ("name") 
      where "is_active" = true;
    `);

    // Create index on is_system_role for faster queries
    this.addSql(`
      create index if not exists "role_is_system_role_idx" 
      on "role" ("is_system_role");
    `);

    // Insert the admin role as a system role
    this.addSql(`
      insert into "role" (
        "id",
        "name",
        "description",
        "permissions",
        "is_system_role",
        "is_active",
        "created_by"
      ) values (
        'admin',
        'System Administrator',
        'Full system access with all permissions',
        '[]'::jsonb,
        true,
        true,
        'system'
      ) on conflict (id) do nothing;
    `);
  }

  override async down(): Promise<void> {
    this.addSql(`drop table if exists "role" cascade;`);
  }
}
