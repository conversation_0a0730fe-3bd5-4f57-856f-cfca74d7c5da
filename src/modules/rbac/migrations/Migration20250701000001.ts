import { Migration } from '@mikro-orm/migrations';

export class Migration20250701000001 extends Migration {
  override async up(): Promise<void> {
    // Add deleted_at column to role table for soft deletes
    this.addSql(`
      ALTER TABLE "role" 
      ADD COLUMN IF NOT EXISTS "deleted_at" timestamptz;
    `);
  }

  override async down(): Promise<void> {
    // Remove deleted_at column
    this.addSql(`
      ALTER TABLE "role" 
      DROP COLUMN IF EXISTS "deleted_at";
    `);
  }
}
