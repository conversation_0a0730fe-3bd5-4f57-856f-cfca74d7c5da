import { model } from "@camped-ai/framework/utils";

export const Role = model.define("role", {
  id: model.id().primary<PERSON>ey(),
  name: model.text(),
  description: model.text().nullable(),
  permissions: model.json(), // Store array of permissions as JSON
  is_system_role: model.boolean().default(false),
  is_active: model.boolean().default(true),
  created_by: model.text(),
  updated_by: model.text().nullable(),
  // created_at, updated_at, and deleted_at are automatically added by <PERSON><PERSON>a
});
