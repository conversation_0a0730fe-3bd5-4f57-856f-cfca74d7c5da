import { LoaderOptions } from "@camped-ai/framework/types";
import { asClass, asValue } from "awilix";

// Import all the price-related services
import PriceComparisonService from "../../../services/price-comparison-service";
import BasePriceAutoPopulationService from "../../../services/base-price-auto-population-service";
import PriceFlagService from "../../../services/price-flag-service";
import PriceSynchronizationService from "../../../services/price-synchronization-service";
import PriceFlagCleanupService from "../../../services/price-flag-cleanup-service";
import { AddOnSyncService } from "../../../services/add-on-sync-service";
import { SUPPLIER_PRODUCTS_SERVICES_MODULE } from "../../supplier-products-services/index";

export default async ({ container }: LoaderOptions): Promise<void> => {
  console.log("🚀 [PRICE-SERVICES-MODULE] Starting price services module loader...");

  try {
    // Register price comparison service
    container.register({
      priceComparisonService: asClass(PriceComparisonService).singleton(),
    });
    console.log("✅ [PRICE-SERVICES-MODULE] PriceComparisonService registered");

    // Register base price auto-population service
    container.register({
      basePriceAutoPopulationService: asClass(BasePriceAutoPopulationService).singleton(),
    });
    console.log("✅ [PRICE-SERVICES-MODULE] BasePriceAutoPopulationService registered");

    // Register price flag service
    container.register({
      priceFlagService: asClass(PriceFlagService).singleton(),
    });
    console.log("✅ [PRICE-SERVICES-MODULE] PriceFlagService registered");

    // Register price synchronization service
    container.register({
      priceSynchronizationService: asClass(PriceSynchronizationService).singleton(),
    });
    console.log("✅ [PRICE-SERVICES-MODULE] PriceSynchronizationService registered");

    // Register price flag cleanup service
    container.register({
      priceFlagCleanupService: asClass(PriceFlagCleanupService).singleton(),
    });
    console.log("✅ [PRICE-SERVICES-MODULE] PriceFlagCleanupService registered");

    // Register add-on sync service
    container.register({
      addOnSyncService: asClass(AddOnSyncService).singleton(),
    });
    console.log("✅ [PRICE-SERVICES-MODULE] AddOnSyncService registered");

    // Register alias for supplier products services module service
    try {
      const supplierProductsServicesModule = container.resolve(SUPPLIER_PRODUCTS_SERVICES_MODULE);
      container.register({
        supplierProductsServicesModuleService: asValue(supplierProductsServicesModule),
      });
      console.log("✅ [PRICE-SERVICES-MODULE] SupplierProductsServicesModuleService alias registered");
    } catch (error) {
      console.warn("⚠️ [PRICE-SERVICES-MODULE] Could not register SupplierProductsServicesModuleService alias:", error.message);
    }

    console.log("✅ [PRICE-SERVICES-MODULE] All price-related services loaded successfully");
  } catch (error) {
    console.error("❌ [PRICE-SERVICES-MODULE] Error loading price-related services:", error);
    throw error;
  }
};
