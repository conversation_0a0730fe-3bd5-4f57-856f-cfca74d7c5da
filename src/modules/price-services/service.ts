import { MedusaService } from "@camped-ai/framework/utils";

class PriceServicesModuleService extends MedusaService({}) {
  constructor(container: any) {
    super(container);
  }

  // Simple service - the actual registration happens in the loader
  async getServiceStatus() {
    return {
      priceComparisonService: this.container_.hasRegistration("priceComparisonService"),
      basePriceAutoPopulationService: this.container_.hasRegistration("basePriceAutoPopulationService"),
      priceFlagService: this.container_.hasRegistration("priceFlagService"),
      addOnSyncService: this.container_.hasRegistration("addOnSyncService"),
    };
  }
}

export default PriceServicesModuleService;
