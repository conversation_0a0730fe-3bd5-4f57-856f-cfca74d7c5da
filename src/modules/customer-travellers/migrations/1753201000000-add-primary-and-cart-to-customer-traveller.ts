import { Migration } from '@mikro-orm/migrations';

export class Migration1753201000000 extends Migration {
  override async up(): Promise<void> {
    // Add new columns
    this.addSql(`
      ALTER TABLE "customer_traveller"
      ADD COLUMN IF NOT EXISTS "is_primary" boolean NOT NULL DEFAULT false;
    `);

    this.addSql(`
      ALTER TABLE "customer_traveller"
      ADD COLUMN IF NOT EXISTS "cart_id" text NULL;
    `);

    // Create partial unique index to enforce only one primary per customer (Postgres)
    this.addSql(`
      CREATE UNIQUE INDEX IF NOT EXISTS "UQ_customer_traveller_primary"
      ON "customer_traveller" ("customer_id")
      WHERE is_primary = true;
    `);
  }

  override async down(): Promise<void> {
    // Drop partial unique index
    this.addSql(`
      DROP INDEX IF EXISTS "UQ_customer_traveller_primary";
    `);

    // Drop added columns (note: dropping a column with default is safe)
    this.addSql(`
      ALTER TABLE "customer_traveller"
      DROP COLUMN IF EXISTS "cart_id";
    `);

    this.addSql(`
      ALTER TABLE "customer_traveller"
      DROP COLUMN IF EXISTS "is_primary";
    `);
  }
}
