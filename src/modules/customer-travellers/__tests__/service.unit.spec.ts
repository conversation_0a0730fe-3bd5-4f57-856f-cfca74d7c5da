import { describe, it, expect, beforeEach, jest } from "@jest/globals";
import { EntityManager } from "@mikro-orm/postgresql";
import { MedusaError } from "@camped-ai/utils";
import CustomerTravellerService, { CreateTravellerInput } from "../service";
import { Relationship } from "../models/customer-traveller";

// Mock EntityManager
const mockEntityManager = {
  execute: jest.fn(),
  transactional: jest.fn(),
} as jest.Mocked<Partial<EntityManager>>;

describe("CustomerTravellerService", () => {
  let service: CustomerTravellerService;

  beforeEach(() => {
    jest.clearAllMocks();
    service = new CustomerTravellerService({ manager: mockEntityManager as EntityManager });
    
    // Mock transactional to execute the callback immediately
    mockEntityManager.transactional!.mockImplementation(async (callback) => {
      return await callback(mockEntityManager as EntityManager);
    });
  });

  describe("listByCart", () => {
    it("should return profiles for a cart", async () => {
      const mockProfiles = [
        {
          id: "cust_trav_123",
          customer_id: "cus_123",
          cart_id: "cart_123",
          first_name: "John",
          last_name: "Doe",
          is_primary: true,
        }
      ];

      mockEntityManager.execute!.mockResolvedValue(mockProfiles);

      const result = await service.listByCart("cart_123");

      expect(mockEntityManager.execute).toHaveBeenCalledWith(
        "SELECT * FROM customer_traveller WHERE cart_id = ? AND deleted_at IS NULL ORDER BY created_at ASC",
        ["cart_123"]
      );
      expect(result).toEqual(mockProfiles);
    });

    it("should return empty array when no profiles found", async () => {
      mockEntityManager.execute!.mockResolvedValue([]);

      const result = await service.listByCart("cart_123");

      expect(result).toEqual([]);
    });
  });

  describe("listByCustomer", () => {
    it("should return profiles for a customer", async () => {
      const mockProfiles = [
        {
          id: "cust_trav_123",
          customer_id: "cus_123",
          first_name: "John",
          last_name: "Doe",
        }
      ];

      mockEntityManager.execute!.mockResolvedValue(mockProfiles);

      const result = await service.listByCustomer("cus_123");

      expect(mockEntityManager.execute).toHaveBeenCalledWith(
        "SELECT * FROM customer_traveller WHERE customer_id = ? AND deleted_at IS NULL ORDER BY created_at ASC",
        ["cus_123"]
      );
      expect(result).toEqual(mockProfiles);
    });
  });

  describe("getByIdForCustomer", () => {
    it("should return profile when found", async () => {
      const mockProfile = {
        id: "cust_trav_123",
        customer_id: "cus_123",
        first_name: "John",
        last_name: "Doe",
      };

      mockEntityManager.execute!.mockResolvedValue([mockProfile]);

      const result = await service.getByIdForCustomer("cust_trav_123", "cus_123");

      expect(mockEntityManager.execute).toHaveBeenCalledWith(
        "SELECT * FROM customer_traveller WHERE id = ? AND customer_id = ? AND deleted_at IS NULL LIMIT 1",
        ["cust_trav_123", "cus_123"]
      );
      expect(result).toEqual(mockProfile);
    });

    it("should throw error when profile not found", async () => {
      mockEntityManager.execute!.mockResolvedValue([]);

      await expect(service.getByIdForCustomer("cust_trav_123", "cus_123"))
        .rejects.toThrow(MedusaError);
    });
  });

  describe("create", () => {
    const validInput: CreateTravellerInput = {
      customer_id: "cus_123",
      cart_id: "cart_123",
      first_name: "John",
      last_name: "Doe",
      date_of_birth: "1990-01-01",
      gender: "male",
      is_primary: true,
    };

    it("should create primary traveller successfully", async () => {
      const mockCreated = {
        id: "cust_trav_123",
        ...validInput,
        created_at: new Date(),
        updated_at: new Date(),
      };

      // Mock constraint check (no existing profiles)
      mockEntityManager.execute!
        .mockResolvedValueOnce([]) // listAllByCustomerTx
        .mockResolvedValueOnce([mockCreated]); // insert

      const result = await service.create(validInput);

      expect(result).toEqual(mockCreated);
    });

    it("should create non-primary traveller with relationship", async () => {
      const nonPrimaryInput: CreateTravellerInput = {
        ...validInput,
        is_primary: false,
        relationship: Relationship.SPOUSE,
      };

      const mockCreated = {
        id: "cust_trav_456",
        ...nonPrimaryInput,
        created_at: new Date(),
        updated_at: new Date(),
      };

      // Mock constraint check (existing primary)
      mockEntityManager.execute!
        .mockResolvedValueOnce([{ id: "cust_trav_123", is_primary: true }]) // listAllByCustomerTx
        .mockResolvedValueOnce([mockCreated]); // insert

      const result = await service.create(nonPrimaryInput);

      expect(result).toEqual(mockCreated);
    });

    it("should reject duplicate primary traveller", async () => {
      // Mock existing primary traveller
      mockEntityManager.execute!.mockResolvedValueOnce([
        { id: "cust_trav_existing", is_primary: true }
      ]);

      await expect(service.create(validInput))
        .rejects.toThrow("Primary traveller already exists for this customer");
    });

    it("should reject non-primary without relationship", async () => {
      const invalidInput: CreateTravellerInput = {
        ...validInput,
        is_primary: false,
        relationship: undefined,
      };

      // Mock no existing profiles
      mockEntityManager.execute!.mockResolvedValueOnce([]);

      await expect(service.create(invalidInput))
        .rejects.toThrow("Relationship is required for additional travellers");
    });

    it("should reject when max profiles reached", async () => {
      // Mock 10 existing profiles
      const existingProfiles = Array.from({ length: 10 }, (_, i) => ({
        id: `cust_trav_${i}`,
        is_primary: i === 0,
      }));

      mockEntityManager.execute!.mockResolvedValueOnce(existingProfiles);

      await expect(service.create(validInput))
        .rejects.toThrow("Maximum number of traveller profiles reached");
    });
  });

  describe("update", () => {
    const existingProfile = {
      id: "cust_trav_123",
      customer_id: "cus_123",
      first_name: "John",
      last_name: "Doe",
      is_primary: false,
      relationship: Relationship.SPOUSE,
    };

    it("should update profile successfully", async () => {
      const updateInput = {
        id: "cust_trav_123",
        first_name: "Jane",
      };

      const updatedProfile = {
        ...existingProfile,
        first_name: "Jane",
        updated_at: new Date(),
      };

      mockEntityManager.execute!
        .mockResolvedValueOnce([existingProfile]) // get existing
        .mockResolvedValueOnce([updatedProfile]); // update

      const result = await service.update("cus_123", "cust_trav_123", updateInput);

      expect(result).toEqual(updatedProfile);
    });

    it("should reject making duplicate primary", async () => {
      const updateInput = {
        id: "cust_trav_123",
        is_primary: true,
      };

      mockEntityManager.execute!
        .mockResolvedValueOnce([existingProfile]) // get existing
        .mockResolvedValueOnce([{ count: 1 }]); // count existing primary

      await expect(service.update("cus_123", "cust_trav_123", updateInput))
        .rejects.toThrow("Primary traveller already exists for this customer");
    });

    it("should reject non-primary without relationship", async () => {
      const updateInput = {
        id: "cust_trav_123",
        is_primary: false,
        relationship: null,
      };

      const profileWithoutRelationship = {
        ...existingProfile,
        relationship: null,
      };

      mockEntityManager.execute!.mockResolvedValueOnce([profileWithoutRelationship]);

      await expect(service.update("cus_123", "cust_trav_123", updateInput))
        .rejects.toThrow("Relationship is required for additional travellers");
    });
  });

  describe("delete", () => {
    it("should soft delete profile", async () => {
      mockEntityManager.execute!.mockResolvedValue(undefined);

      await service.delete("cus_123", "cust_trav_123");

      expect(mockEntityManager.execute).toHaveBeenCalledWith(
        expect.stringContaining("UPDATE customer_traveller SET"),
        ["cust_trav_123", "cus_123"]
      );
    });
  });
});
