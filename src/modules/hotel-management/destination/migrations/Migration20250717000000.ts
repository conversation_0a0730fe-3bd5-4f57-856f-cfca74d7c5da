import { Migration } from '@mikro-orm/migrations';

export class Migration20250717000000 extends Migration {

  override async up(): Promise<void> {
    // Add unique constraint on handle field for destinations
    this.addSql(`CREATE UNIQUE INDEX IF NOT EXISTS "IDX_destination_handle_unique" ON "destination" (handle) WHERE deleted_at IS NULL;`);
  }

  override async down(): Promise<void> {
    // Remove unique constraint on handle field
    this.addSql(`DROP INDEX IF EXISTS "IDX_destination_handle_unique";`);
  }

}
