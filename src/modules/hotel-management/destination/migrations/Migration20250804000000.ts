import { Migration } from '@mikro-orm/migrations';

export class Migration20250804000000 extends Migration {

  override async up(): Promise<void> {
    // Add new fields to destination table
    this.addSql(`
      ALTER TABLE IF EXISTS "destination" 
      ADD COLUMN IF NOT EXISTS "internal_web_link" text NULL,
      ADD COLUMN IF NOT EXISTS "external_web_link" text NULL,
      ADD COLUMN IF NOT EXISTS "currency" text NULL,
      ADD COLUMN IF NOT EXISTS "margin" decimal(5,2) NULL;
    `);

    // Add indexes for the new fields
    this.addSql(`
      CREATE INDEX IF NOT EXISTS "IDX_destination_currency" 
      ON "destination" ("currency") 
      WHERE "deleted_at" IS NULL;
    `);
  }

  override async down(): Promise<void> {
    // Remove indexes first
    this.addSql(`DROP INDEX IF EXISTS "IDX_destination_currency";`);
    
    // Remove the new columns
    this.addSql(`
      ALTER TABLE IF EXISTS "destination" 
      DROP COLUMN IF EXISTS "internal_web_link",
      DROP COLUMN IF EXISTS "external_web_link", 
      DROP COLUMN IF EXISTS "currency",
      DROP COLUMN IF EXISTS "margin";
    `);
  }

}
