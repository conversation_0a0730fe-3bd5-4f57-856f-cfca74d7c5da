import { Migration } from '@mikro-orm/migrations';

export class Migration20250804120000 extends Migration {

  override async up(): Promise<void> {
    // Add margin field to hotel table (currency field already exists)
    this.addSql(`
      ALTER TABLE IF EXISTS "hotel" 
      ADD COLUMN IF NOT EXISTS "margin" decimal(5,2) NULL;
    `);
  }

  override async down(): Promise<void> {
    // Remove the margin column
    this.addSql(`
      ALTER TABLE IF EXISTS "hotel" 
      DROP COLUMN IF EXISTS "margin";
    `);
  }

}
