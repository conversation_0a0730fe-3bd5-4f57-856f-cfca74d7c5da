import { model } from "@camped-ai/framework/utils";
import { HotelImage } from "./hotel-image";
import { RoomConfig } from "./room-config";

export const Hotel = model.define("hotel", {
  id: model.id().primary<PERSON>ey(),
  category_id: model.text(),
  name: model.text(),
  handle: model.text(),
  description: model.text().nullable(),
  is_active: model.boolean().default(true),
  website: model.text().nullable(),
  email: model.text().nullable(),
  destination_id: model.text(),
  rating: model.number().nullable(),
  total_reviews: model.number().nullable(),
  notes: model.text().nullable(),
  location: model.text().nullable(),
  address: model.text().nullable(),
  phone_number: model.text().nullable(),
  timezone: model.text().nullable(),
  available_languages: model.json().nullable(),
  tax_type: model.text().nullable(),
  tax_number: model.text().nullable(),
  tags: model.json().nullable(),
  amenities: model.json().nullable(),
  rules: model.json().nullable(),
  safety_measures: model.json().nullable(),
  currency: model.text().nullable(),
  margin: model.number().nullable(), // New field for margin
  check_in_time: model.text().nullable(),
  check_out_time: model.text().nullable(),
  is_featured: model.boolean().default(false),
  is_pets_allowed: model.boolean().default(false),
  ai_content: model.text().nullable(),
  metadata: model.json().nullable(),
  images: model.hasMany(() => HotelImage, {
    foreignKey: "hotel_id",
    localKey: "id",
  }),
  roomConfigs: model.hasMany(() => RoomConfig),
});
