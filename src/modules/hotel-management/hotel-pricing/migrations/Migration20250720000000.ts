import { Migration } from '@mikro-orm/migrations';

/**
 * Migration to add cost and margin management fields to base_price_rule table
 * 
 * This migration adds:
 * - Default value fields for gross cost, fixed margin, margin percentage, and total
 * - Day-specific cost and margin fields for all 7 days of the week
 * - All fields are nullable to maintain backward compatibility with existing pricing data
 */
export class Migration20250720000000 extends Migration {

  override async up(): Promise<void> {
    // Add default value fields
    this.addSql(`
      ALTER TABLE IF EXISTS "base_price_rule" 
      ADD COLUMN IF NOT EXISTS "default_gross_cost" numeric NULL,
      ADD COLUMN IF NOT EXISTS "default_fixed_margin" numeric NULL,
      ADD COLUMN IF NOT EXISTS "default_margin_percentage" numeric NULL,
      ADD COLUMN IF NOT EXISTS "default_total" numeric NULL
    `);

    // Add Monday cost and margin fields
    this.addSql(`
      ALTER TABLE IF EXISTS "base_price_rule"
      ADD COLUMN IF NOT EXISTS "monday_gross_cost" numeric NULL,
      ADD COLUMN IF NOT EXISTS "monday_fixed_margin" numeric NULL,
      ADD COLUMN IF NOT EXISTS "monday_margin_percentage" numeric NULL
    `);

    // Add Tuesday cost and margin fields
    this.addSql(`
      ALTER TABLE IF EXISTS "base_price_rule"
      ADD COLUMN IF NOT EXISTS "tuesday_gross_cost" numeric NULL,
      ADD COLUMN IF NOT EXISTS "tuesday_fixed_margin" numeric NULL,
      ADD COLUMN IF NOT EXISTS "tuesday_margin_percentage" numeric NULL
    `);

    // Add Wednesday cost and margin fields
    this.addSql(`
      ALTER TABLE IF EXISTS "base_price_rule"
      ADD COLUMN IF NOT EXISTS "wednesday_gross_cost" numeric NULL,
      ADD COLUMN IF NOT EXISTS "wednesday_fixed_margin" numeric NULL,
      ADD COLUMN IF NOT EXISTS "wednesday_margin_percentage" numeric NULL
    `);

    // Add Thursday cost and margin fields
    this.addSql(`
      ALTER TABLE IF EXISTS "base_price_rule"
      ADD COLUMN IF NOT EXISTS "thursday_gross_cost" numeric NULL,
      ADD COLUMN IF NOT EXISTS "thursday_fixed_margin" numeric NULL,
      ADD COLUMN IF NOT EXISTS "thursday_margin_percentage" numeric NULL
    `);

    // Add Friday cost and margin fields
    this.addSql(`
      ALTER TABLE IF EXISTS "base_price_rule"
      ADD COLUMN IF NOT EXISTS "friday_gross_cost" numeric NULL,
      ADD COLUMN IF NOT EXISTS "friday_fixed_margin" numeric NULL,
      ADD COLUMN IF NOT EXISTS "friday_margin_percentage" numeric NULL
    `);

    // Add Saturday cost and margin fields
    this.addSql(`
      ALTER TABLE IF EXISTS "base_price_rule"
      ADD COLUMN IF NOT EXISTS "saturday_gross_cost" numeric NULL,
      ADD COLUMN IF NOT EXISTS "saturday_fixed_margin" numeric NULL,
      ADD COLUMN IF NOT EXISTS "saturday_margin_percentage" numeric NULL
    `);

    // Add Sunday cost and margin fields
    this.addSql(`
      ALTER TABLE IF EXISTS "base_price_rule"
      ADD COLUMN IF NOT EXISTS "sunday_gross_cost" numeric NULL,
      ADD COLUMN IF NOT EXISTS "sunday_fixed_margin" numeric NULL,
      ADD COLUMN IF NOT EXISTS "sunday_margin_percentage" numeric NULL
    `);

    // Add indexes for performance on frequently queried cost fields
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_base_price_rule_default_gross_cost" ON "base_price_rule" ("default_gross_cost") WHERE deleted_at IS NULL AND default_gross_cost IS NOT NULL;`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_base_price_rule_default_total" ON "base_price_rule" ("default_total") WHERE deleted_at IS NULL AND default_total IS NOT NULL;`);
  }

  override async down(): Promise<void> {
    // Drop indexes first
    this.addSql(`DROP INDEX IF EXISTS "IDX_base_price_rule_default_gross_cost";`);
    this.addSql(`DROP INDEX IF EXISTS "IDX_base_price_rule_default_total";`);

    // Drop all cost and margin fields
    this.addSql(`
      ALTER TABLE IF EXISTS "base_price_rule"
      DROP COLUMN IF EXISTS "default_gross_cost",
      DROP COLUMN IF EXISTS "default_fixed_margin",
      DROP COLUMN IF EXISTS "default_margin_percentage",
      DROP COLUMN IF EXISTS "default_total",
      DROP COLUMN IF EXISTS "monday_gross_cost",
      DROP COLUMN IF EXISTS "monday_fixed_margin",
      DROP COLUMN IF EXISTS "monday_margin_percentage",
      DROP COLUMN IF EXISTS "tuesday_gross_cost",
      DROP COLUMN IF EXISTS "tuesday_fixed_margin",
      DROP COLUMN IF EXISTS "tuesday_margin_percentage",
      DROP COLUMN IF EXISTS "wednesday_gross_cost",
      DROP COLUMN IF EXISTS "wednesday_fixed_margin",
      DROP COLUMN IF EXISTS "wednesday_margin_percentage",
      DROP COLUMN IF EXISTS "thursday_gross_cost",
      DROP COLUMN IF EXISTS "thursday_fixed_margin",
      DROP COLUMN IF EXISTS "thursday_margin_percentage",
      DROP COLUMN IF EXISTS "friday_gross_cost",
      DROP COLUMN IF EXISTS "friday_fixed_margin",
      DROP COLUMN IF EXISTS "friday_margin_percentage",
      DROP COLUMN IF EXISTS "saturday_gross_cost",
      DROP COLUMN IF EXISTS "saturday_fixed_margin",
      DROP COLUMN IF EXISTS "saturday_margin_percentage",
      DROP COLUMN IF EXISTS "sunday_gross_cost",
      DROP COLUMN IF EXISTS "sunday_fixed_margin",
      DROP COLUMN IF EXISTS "sunday_margin_percentage"
    `);
  }
}
