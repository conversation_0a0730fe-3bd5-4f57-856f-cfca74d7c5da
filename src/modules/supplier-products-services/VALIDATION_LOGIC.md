# Supplier Offering Validation Logic

## Overview

The supplier offering validation system has been enhanced to prevent date overlaps only for offerings that are considered "identical" based on their mandatory field values. This ensures that offerings with different characteristics can have overlapping validity periods while preventing true duplicates.

## Key Concepts

### "Same Offering" Definition

Two supplier offerings are considered "the same offering" (identical) when:

1. **Same Supplier and Product/Service**: They have the same `supplier_id` and `product_service_id`
2. **Same Mandatory Custom Fields**: All mandatory custom fields that are marked with:
   - `required: true`
   - `used_in_supplier_offering: true`
   - `field_context: "supplier"` (or no field_context)

### Validation Rules

#### 1. Date Overlap Prevention
- **When**: Only applied to offerings that are considered "identical"
- **Rule**: Identical offerings cannot have overlapping validity periods (`active_from` to `active_to`)
- **Includes**: Open-ended date ranges (null `active_to` dates)

#### 2. Different Offerings Allowed
- **When**: Offerings have different mandatory field values
- **Rule**: Can have overlapping date ranges since they represent different offerings
- **Example**: Same hotel product but different room types, locations, or other mandatory characteristics

## Implementation Details

### Server-Side Validation

Located in: `src/modules/supplier-products-services/service.ts`

#### Key Methods:

1. **`validateSupplierOfferingUniqueness()`**
   - Main validation entry point
   - Called during create and update operations

2. **`areOfferingsIdentical()`**
   - Compares mandatory custom fields between offerings
   - Returns true if all mandatory fields match

3. **`normalizeFieldValue()`**
   - Normalizes field values for consistent comparison
   - Handles arrays, strings, null values

4. **`areFieldValuesEqual()`**
   - Compares normalized field values
   - Handles different data types appropriately

5. **`doDateRangesOverlap()`**
   - Checks if two date ranges overlap
   - Handles open-ended ranges (null end dates)

### Client-Side Validation

The client-side validation already properly validates required custom fields:

- **Create Form**: `src/admin/routes/supplier-management/supplier-offerings/create/page.tsx`
- **Edit Form**: `src/admin/routes/supplier-management/supplier-offerings/[id]/edit/page.tsx`
- **Import Validation**: `src/admin/hooks/supplier-products-services/use-supplier-offerings.ts`

## Usage Examples

### Example 1: Different Locations (Allowed)

```javascript
// Existing offering
{
  supplier_id: "supplier_123",
  product_service_id: "hotel_456",
  custom_fields: { location: "Paris", room_type: "Standard" },
  active_from: "2024-06-01",
  active_to: "2024-08-31"
}

// New offering (ALLOWED - different location)
{
  supplier_id: "supplier_123",
  product_service_id: "hotel_456", 
  custom_fields: { location: "London", room_type: "Standard" },
  active_from: "2024-07-01", // Overlapping dates OK
  active_to: "2024-09-30"
}
```

### Example 2: Same Characteristics (Blocked)

```javascript
// Existing offering
{
  supplier_id: "supplier_123",
  product_service_id: "hotel_456",
  custom_fields: { location: "Paris", room_type: "Standard" },
  active_from: "2024-06-01",
  active_to: "2024-08-31"
}

// New offering (BLOCKED - identical mandatory fields)
{
  supplier_id: "supplier_123",
  product_service_id: "hotel_456",
  custom_fields: { location: "Paris", room_type: "Standard" },
  active_from: "2024-07-01", // Overlapping dates NOT allowed
  active_to: "2024-09-30"
}
```

## Error Messages

When validation fails, users receive clear error messages:

```
"Date range conflict: An identical offering already exists with overlapping validity period from 2024-06-01 to 2024-08-31. Offerings with the same mandatory field values cannot have overlapping date ranges."
```

## Import Functionality

The same validation logic applies to Excel/CSV imports:

- Each row is validated using the same rules
- Import errors include row numbers and specific validation messages
- Partial imports are supported (successful rows are imported, failed rows are reported)

## Testing

Comprehensive unit tests are available in:
`src/modules/supplier-products-services/__tests__/supplier-offering-validation.unit.spec.ts`

Tests cover:
- Field value normalization and comparison
- Offering identity detection
- Date overlap scenarios
- Import validation
- Edge cases (open-ended dates, missing fields, etc.)

## Migration Notes

This change is backward compatible:
- Existing offerings are not affected
- No database schema changes required
- Client-side validation already handles required custom fields
- Import functionality uses the same validation logic
