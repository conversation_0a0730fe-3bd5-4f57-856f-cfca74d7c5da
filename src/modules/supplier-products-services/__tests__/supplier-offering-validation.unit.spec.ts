import { MedusaError } from "@camped-ai/framework/utils";

// Create a mock service class for testing the validation methods
class MockSupplierProductsServicesService {
  // Mock the listSupplierOfferingsWithFilters method
  listSupplierOfferingsWithFilters = jest.fn();

  // Copy the validation methods from the actual service
  private doDateRangesOverlap(
    newStart: Date | null,
    newEnd: Date | null,
    existingStart: Date | null,
    existingEnd: Date | null
  ): boolean {
    // Two ranges overlap if: newStart < existingEnd AND newEnd > existingStart
    // Handle null dates as open-ended (extends indefinitely)

    // If both ranges are completely open-ended, they overlap
    if (!newStart && !newEnd && !existingStart && !existingEnd) {
      return true;
    }

    // Check for non-overlapping conditions first (easier to reason about)
    // Case 1: New range ends before existing range starts
    if (newEnd && existingStart && newEnd < existingStart) {
      return false;
    }

    // Case 2: New range starts after existing range ends
    if (newStart && existingEnd && newStart > existingEnd) {
      return false;
    }

    // If we reach here, the ranges overlap
    return true;
  }

  private areDatesIdentical(date1?: Date, date2?: Date): boolean {
    if (!date1 && !date2) return true;
    if (!date1 || !date2) return false;
    return new Date(date1).getTime() === new Date(date2).getTime();
  }

  // Helper method to check if two offerings are identical based on mandatory fields
  private areOfferingsIdentical(
    newCustomFields: Record<string, any>,
    existingCustomFields: Record<string, any>,
    dynamicFieldSchema: any[]
  ): boolean {
    // Get mandatory custom fields that are used in supplier offerings
    const mandatoryFields = dynamicFieldSchema.filter(
      (field) =>
        field.used_in_supplier_offering &&
        field.required === true &&
        (!field.field_context || field.field_context === "supplier")
    );

    // Compare each mandatory custom field
    for (const field of mandatoryFields) {
      const newValue = newCustomFields[field.key];
      const existingValue = existingCustomFields[field.key];

      // Normalize values for comparison
      const normalizedNewValue = this.normalizeFieldValue(newValue);
      const normalizedExistingValue = this.normalizeFieldValue(existingValue);

      // If values don't match, offerings are different
      if (!this.areFieldValuesEqual(normalizedNewValue, normalizedExistingValue)) {
        return false;
      }
    }

    // All mandatory fields match - offerings are identical
    return true;
  }

  // Helper method to normalize field values for comparison
  private normalizeFieldValue(value: any): any {
    if (value === null || value === undefined || value === "") {
      return null;
    }

    // Handle arrays by sorting them for consistent comparison
    if (Array.isArray(value)) {
      return value.slice().sort();
    }

    // Handle strings by trimming whitespace
    if (typeof value === "string") {
      return value.trim();
    }

    return value;
  }

  // Helper method to compare field values
  private areFieldValuesEqual(value1: any, value2: any): boolean {
    // Both null/undefined/empty
    if (value1 === null && value2 === null) {
      return true;
    }

    // One null, one not
    if (value1 === null || value2 === null) {
      return false;
    }

    // Array comparison
    if (Array.isArray(value1) && Array.isArray(value2)) {
      if (value1.length !== value2.length) {
        return false;
      }
      return value1.every((item, index) => item === value2[index]);
    }

    // Direct comparison for other types
    return value1 === value2;
  }

  // Mock the full validation method to test business rules
  async validateSupplierOfferingUniqueness(
    supplierId: string,
    productServiceId: string,
    activeFrom?: Date,
    activeTo?: Date,
    customFields: Record<string, any> = {},
    dynamicFieldSchema: any[] = [],
    excludeId?: string
  ): Promise<void> {
    const existingOfferings = await this.listSupplierOfferingsWithFilters({
      product_service_id: productServiceId,
      supplier_id: supplierId,
    });

    if (existingOfferings?.data?.length > 0) {
      // Check each existing offering for conflicts
      for (const existingOffering of existingOfferings.data) {
        // Skip the current offering if we're updating
        if (excludeId && existingOffering.id === excludeId) {
          continue;
        }

        // Check if this is the "same offering" by comparing all mandatory fields
        const isSameOffering = this.areOfferingsIdentical(
          customFields,
          existingOffering.custom_fields || {},
          dynamicFieldSchema
        );

        // Only check for date overlaps if the offerings are considered identical
        if (isSameOffering) {
          // Prepare date variables for validation
          const newStart = activeFrom ? new Date(activeFrom) : null;
          const newEnd = activeTo ? new Date(activeTo) : null;
          const existingStart = existingOffering.active_from
            ? new Date(existingOffering.active_from)
            : null;
          const existingEnd = existingOffering.active_to
            ? new Date(existingOffering.active_to)
            : null;

          // Check for date range overlaps
          const hasOverlap = this.doDateRangesOverlap(
            newStart,
            newEnd,
            existingStart,
            existingEnd
          );

          if (hasOverlap) {
            const formatDate = (date?: Date | null) =>
              date ? date.toISOString().split("T")[0] : "open-ended";

            const errorMessage = `Date range conflict: An identical offering already exists with overlapping validity period from ${formatDate(
              existingStart
            )} to ${formatDate(existingEnd)}. Offerings with the same mandatory field values cannot have overlapping date ranges.`;

            throw new MedusaError(
              MedusaError.Types.DUPLICATE_ERROR,
              errorMessage
            );
          }
        }
      }
    }
  }

  // Expose methods for testing
  public testDoDateRangesOverlap = this.doDateRangesOverlap;
  public testAreDatesIdentical = this.areDatesIdentical;
  public testAreOfferingsIdentical = this.areOfferingsIdentical;
  public testNormalizeFieldValue = this.normalizeFieldValue;
  public testAreFieldValuesEqual = this.areFieldValuesEqual;
  public testValidateSupplierOfferingUniqueness = this.validateSupplierOfferingUniqueness;
}

describe("SupplierProductsServicesService - Supplier Offering Validation", () => {
  let service: MockSupplierProductsServicesService;

  beforeEach(() => {
    service = new MockSupplierProductsServicesService();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe("Field Value Comparison", () => {
    it("should normalize field values correctly", () => {
      expect(service.testNormalizeFieldValue(null)).toBe(null);
      expect(service.testNormalizeFieldValue(undefined)).toBe(null);
      expect(service.testNormalizeFieldValue("")).toBe(null);
      expect(service.testNormalizeFieldValue("  test  ")).toBe("test");
      expect(service.testNormalizeFieldValue([3, 1, 2])).toEqual([1, 2, 3]);
      expect(service.testNormalizeFieldValue(123)).toBe(123);
    });

    it("should compare field values correctly", () => {
      expect(service.testAreFieldValuesEqual(null, null)).toBe(true);
      expect(service.testAreFieldValuesEqual(null, "test")).toBe(false);
      expect(service.testAreFieldValuesEqual("test", "test")).toBe(true);
      expect(service.testAreFieldValuesEqual([1, 2], [1, 2])).toBe(true);
      expect(service.testAreFieldValuesEqual([1, 2], [2, 1])).toBe(false);
      expect(service.testAreFieldValuesEqual([1, 2], [1, 2, 3])).toBe(false);
    });

    it("should identify identical offerings based on mandatory fields", () => {
      const dynamicFieldSchema = [
        { key: "location", required: true, used_in_supplier_offering: true, field_context: "supplier" },
        { key: "optional_field", required: false, used_in_supplier_offering: true, field_context: "supplier" },
        { key: "not_used", required: true, used_in_supplier_offering: false, field_context: "supplier" }
      ];

      // Same mandatory fields
      expect(service.testAreOfferingsIdentical(
        { location: "Paris", optional_field: "test" },
        { location: "Paris", optional_field: "different" },
        dynamicFieldSchema
      )).toBe(true);

      // Different mandatory fields
      expect(service.testAreOfferingsIdentical(
        { location: "Paris" },
        { location: "London" },
        dynamicFieldSchema
      )).toBe(false);

      // No mandatory fields
      expect(service.testAreOfferingsIdentical(
        { optional_field: "test" },
        { optional_field: "different" },
        []
      )).toBe(true);
    });
  });

  describe("Enhanced Validation Logic", () => {
    it("should allow overlapping dates for offerings with different mandatory field values", async () => {
      const existingOffering = {
        id: "existing_1",
        supplier_id: "supplier_123",
        product_service_id: "product_456",
        status: "active",
        active_from: new Date("2024-06-01"),
        active_to: new Date("2024-08-31"),
        custom_fields: { location: "Paris" }
      };

      service.listSupplierOfferingsWithFilters.mockResolvedValue({
        data: [existingOffering],
      });

      const dynamicFieldSchema = [
        { key: "location", required: true, used_in_supplier_offering: true, field_context: "supplier" }
      ];

      // Should not throw - different location values make these different offerings
      await expect(
        service.testValidateSupplierOfferingUniqueness(
          "supplier_123",
          "product_456",
          new Date("2024-07-01"), // Overlapping dates
          new Date("2024-09-30"),
          { location: "London" }, // Different location
          dynamicFieldSchema
        )
      ).resolves.not.toThrow();
    });

    it("should block overlapping dates for offerings with identical mandatory field values", async () => {
      const existingOffering = {
        id: "existing_1",
        supplier_id: "supplier_123",
        product_service_id: "product_456",
        status: "active",
        active_from: new Date("2024-06-01"),
        active_to: new Date("2024-08-31"),
        custom_fields: { location: "Paris" }
      };

      service.listSupplierOfferingsWithFilters.mockResolvedValue({
        data: [existingOffering],
      });

      const dynamicFieldSchema = [
        { key: "location", required: true, used_in_supplier_offering: true, field_context: "supplier" }
      ];

      // Should throw - same location values with overlapping dates
      await expect(
        service.testValidateSupplierOfferingUniqueness(
          "supplier_123",
          "product_456",
          new Date("2024-07-01"), // Overlapping dates
          new Date("2024-09-30"),
          { location: "Paris" }, // Same location
          dynamicFieldSchema
        )
      ).rejects.toThrow("Date range conflict: An identical offering already exists with overlapping validity period");
    });
  });

  describe("Legacy Tests (Updated for New Logic)", () => {
    it("should allow creation when no mandatory fields are defined (backward compatibility)", async () => {
      const existingOffering = {
        id: "existing_1",
        supplier_id: "supplier_123",
        product_service_id: "product_456",
        status: "active",
        active_from: new Date("2024-01-01"),
        active_to: null, // Open-ended
        custom_fields: {}
      };

      service.listSupplierOfferingsWithFilters.mockResolvedValue({
        data: [existingOffering],
      });

      // With no mandatory fields defined, offerings are considered identical
      // and overlapping dates should be blocked
      await expect(
        service.testValidateSupplierOfferingUniqueness(
          "supplier_123",
          "product_456",
          new Date("2024-06-01"), // Overlaps with open-ended existing offering
          new Date("2024-12-31"),
          {}, // No custom fields
          [] // No dynamic field schema
        )
      ).rejects.toThrow("Date range conflict: An identical offering already exists with overlapping validity period");
    });

    it("should allow creation when dates don't overlap (non-overlapping ranges)", async () => {
      const existingOffering = {
        id: "existing_1",
        supplier_id: "supplier_123",
        product_service_id: "product_456",
        status: "active",
        active_from: new Date("2024-01-01"),
        active_to: new Date("2024-05-31"), // Non-overlapping date range
        custom_fields: {}
      };

      service.listSupplierOfferingsWithFilters.mockResolvedValue({
        data: [existingOffering],
      });

      // Should not throw - dates don't overlap
      await expect(
        service.testValidateSupplierOfferingUniqueness(
          "supplier_123",
          "product_456",
          new Date("2024-06-01"),
          new Date("2024-12-31"),
          {}, // Same custom fields (empty)
          [] // No mandatory fields
        )
      ).resolves.not.toThrow();
    });

    it("should allow creation when existing offering has different supplier/product", async () => {
      const existingOffering = {
        id: "existing_1",
        supplier_id: "supplier_123",
        product_service_id: "product_456",
        status: "active",
        active_from: new Date("2024-01-01"),
        active_to: new Date("2024-12-31"),
        custom_fields: {}
      };

      service.listSupplierOfferingsWithFilters.mockResolvedValue({
        data: [], // No matching offerings for different supplier/product
      });

      // Should not throw - different supplier/product combination
      await expect(
        service.testValidateSupplierOfferingUniqueness(
          "supplier_456", // Different supplier
          "product_789", // Different product
          new Date("2024-06-01"),
          new Date("2024-12-31"),
          {},
          []
        )
      ).resolves.not.toThrow();
    });
  });

  describe("Import Validation", () => {
    it("should apply the same validation logic during import operations", async () => {
      const existingOffering = {
        id: "existing_1",
        supplier_id: "supplier_123",
        product_service_id: "product_456",
        status: "active",
        active_from: new Date("2024-06-01"),
        active_to: new Date("2024-08-31"),
        custom_fields: { location: "Paris", duration: "3 days" }
      };

      service.listSupplierOfferingsWithFilters.mockResolvedValue({
        data: [existingOffering],
      });

      const dynamicFieldSchema = [
        { key: "location", required: true, used_in_supplier_offering: true, field_context: "supplier" },
        { key: "duration", required: true, used_in_supplier_offering: true, field_context: "supplier" }
      ];

      // Import with identical mandatory fields and overlapping dates should fail
      await expect(
        service.testValidateSupplierOfferingUniqueness(
          "supplier_123",
          "product_456",
          new Date("2024-07-01"), // Overlapping dates
          new Date("2024-09-30"),
          { location: "Paris", duration: "3 days" }, // Identical mandatory fields
          dynamicFieldSchema
        )
      ).rejects.toThrow("Date range conflict: An identical offering already exists with overlapping validity period");

      // Import with different mandatory fields should succeed even with overlapping dates
      await expect(
        service.testValidateSupplierOfferingUniqueness(
          "supplier_123",
          "product_456",
          new Date("2024-07-01"), // Overlapping dates
          new Date("2024-09-30"),
          { location: "London", duration: "3 days" }, // Different location
          dynamicFieldSchema
        )
      ).resolves.not.toThrow();
    });
  });

  describe("Date Range Overlap Detection", () => {
    it("should block overlapping date ranges", async () => {
      const existingOffering = {
        id: "existing_1",
        supplier_id: "supplier_123",
        product_service_id: "product_456",
        status: "active",
        active_from: new Date("2024-06-01"),
        active_to: new Date("2024-08-31"),
      };

      service.listSupplierOfferingsWithFilters.mockResolvedValue({
        data: [existingOffering],
      });

      await expect(
        service.testValidateSupplierOfferingUniqueness(
          "supplier_123",
          "product_456",
          new Date("2024-07-01"), // Overlaps with existing range
          new Date("2024-09-30")
        )
      ).rejects.toThrow("Date range conflict: Validity period overlaps with existing offering from 2024-06-01 to 2024-08-31");
    });

    it("should allow non-overlapping date ranges", async () => {
      const existingOffering = {
        id: "existing_1",
        supplier_id: "supplier_123",
        product_service_id: "product_456",
        status: "active",
        active_from: new Date("2024-01-01"),
        active_to: new Date("2024-03-31"),
      };

      service.listSupplierOfferingsWithFilters.mockResolvedValue({
        data: [existingOffering],
      });

      // Should not throw - ranges don't overlap
      await expect(
        service.testValidateSupplierOfferingUniqueness(
          "supplier_123",
          "product_456",
          new Date("2024-06-01"),
          new Date("2024-08-31")
        )
      ).resolves.not.toThrow();
    });

    it("should handle open-ended ranges in overlap detection (for updates)", async () => {
      const existingOffering = {
        id: "existing_1",
        supplier_id: "supplier_123",
        product_service_id: "product_456",
        status: "active",
        active_from: new Date("2024-06-01"),
        active_to: null, // Open-ended
      };

      service.listSupplierOfferingsWithFilters.mockResolvedValue({
        data: [existingOffering],
      });

      // For update operations, Rule 1 doesn't apply, so we should get Rule 2 error
      await expect(
        service.testValidateSupplierOfferingUniqueness(
          "supplier_123",
          "product_456",
          new Date("2024-07-01"), // Overlaps with open-ended range
          new Date("2024-09-30"),
          {},
          [],
          "some_other_offering_id" // This is an update operation
        )
      ).rejects.toThrow("Date range conflict: Validity period overlaps with existing offering from 2024-06-01 to open-ended");
    });
  });

  describe("Rule 3: Update Operation - Block Identical Date Ranges", () => {
    it("should block updating to identical date ranges", async () => {
      const existingOffering = {
        id: "offering_123",
        supplier_id: "supplier_123",
        product_service_id: "product_456",
        status: "active",
        active_from: new Date("2024-01-01"),
        active_to: new Date("2024-12-31"),
      };

      service.listSupplierOfferingsWithFilters.mockResolvedValue({
        data: [existingOffering],
      });

      await expect(
        service.testValidateSupplierOfferingUniqueness(
          "supplier_123",
          "product_456",
          new Date("2024-01-01"), // Same dates
          new Date("2024-12-31"),
          {},
          [],
          "offering_123" // Updating this offering
        )
      ).rejects.toThrow("No changes detected: The offering already has the same validity period");
    });

    it("should allow updating to different date ranges", async () => {
      const existingOffering = {
        id: "offering_123",
        supplier_id: "supplier_123",
        product_service_id: "product_456",
        status: "active",
        active_from: new Date("2024-01-01"),
        active_to: new Date("2024-12-31"),
      };

      service.listSupplierOfferingsWithFilters.mockResolvedValue({
        data: [existingOffering],
      });

      // Should not throw - different dates
      await expect(
        service.testValidateSupplierOfferingUniqueness(
          "supplier_123",
          "product_456",
          new Date("2024-02-01"), // Different start date
          new Date("2024-12-31"),
          {},
          [],
          "offering_123"
        )
      ).resolves.not.toThrow();
    });
  });

  describe("Helper method validation", () => {
    it("should correctly identify identical dates", () => {
      const date1 = new Date("2024-01-01");
      const date2 = new Date("2024-01-01");
      const date3 = new Date("2024-01-02");

      expect(service.testAreDatesIdentical(date1, date2)).toBe(true);
      expect(service.testAreDatesIdentical(date1, date3)).toBe(false);
      expect(service.testAreDatesIdentical(undefined, undefined)).toBe(true);
      expect(service.testAreDatesIdentical(date1, undefined)).toBe(false);
    });
  });

  describe("Date range overlap detection", () => {
    it("should detect overlap when new range starts before existing ends", () => {
      const result = service.testDoDateRangesOverlap(
        new Date("2024-06-01"), // New start
        new Date("2024-08-31"), // New end
        new Date("2024-07-01"), // Existing start
        new Date("2024-09-30")  // Existing end
      );
      expect(result).toBe(true);
    });

    it("should detect overlap when new range ends after existing starts", () => {
      const result = service.testDoDateRangesOverlap(
        new Date("2024-07-01"), // New start
        new Date("2024-09-30"), // New end
        new Date("2024-06-01"), // Existing start
        new Date("2024-08-31")  // Existing end
      );
      expect(result).toBe(true);
    });

    it("should not detect overlap when ranges are separate", () => {
      const result = service.testDoDateRangesOverlap(
        new Date("2024-01-01"), // New start
        new Date("2024-03-31"), // New end
        new Date("2024-06-01"), // Existing start
        new Date("2024-08-31")  // Existing end
      );
      expect(result).toBe(false);
    });

    it("should handle open-ended ranges (null end dates)", () => {
      const result = service.testDoDateRangesOverlap(
        new Date("2024-06-01"), // New start
        null,                   // New end (open)
        new Date("2024-07-01"), // Existing start
        new Date("2024-08-31")  // Existing end
      );
      expect(result).toBe(true);
    });

    it("should handle both ranges being open-ended", () => {
      const result = service.testDoDateRangesOverlap(
        new Date("2024-06-01"), // New start
        null,                   // New end (open)
        new Date("2024-07-01"), // Existing start
        null                    // Existing end (open)
      );
      expect(result).toBe(true);
    });
  });


});
