/**
 * Integration tests for dynamic naming and uniqueness logic
 * These tests verify that the service layer correctly implements
 * the dynamic naming and uniqueness constraints.
 */

import { MedusaError } from "@camped-ai/framework/utils";

// Mock the service and dependencies
const mockService = {
  listCategories: jest.fn(),
  listProductServices: jest.fn(),
  createProductServices: jest.fn(),
  listSupplierOfferingsWithFilters: jest.fn(),
  createSupplierOfferings: jest.fn(),
};

// Mock the name generator utilities
jest.mock("../utils/name-generator", () => ({
  generateProductServiceName: jest.fn(),
  generateProductServiceUniqueKey: jest.fn(),
  generateSupplierOfferingUniqueKey: jest.fn(),
}));

import {
  generateProductServiceName,
  generateProductServiceUniqueKey,
  generateSupplierOfferingUniqueKey,
} from "../utils/name-generator";

describe("Dynamic Naming Integration Tests", () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe("Product Service Creation", () => {
    it("should auto-generate name based on category and mandatory fields", async () => {
      // Mock category data
      const mockCategory = {
        id: "cat_123",
        name: "Taxi Transfer",
        dynamic_field_schema: [
          {
            label: "Vehicle Type",
            key: "vehicle_type",
            type: "dropdown",
            required: true,
            used_in_product: true,
          },
        ],
      };

      // Mock generated name
      (generateProductServiceName as jest.Mock).mockReturnValue(
        "Taxi Transfer – Minivan"
      );
      (generateProductServiceUniqueKey as jest.Mock).mockReturnValue(
        "cat_123||vehicle_type:minivan"
      );

      mockService.listCategories.mockResolvedValue([mockCategory]);
      mockService.listProductServices.mockResolvedValue([]);
      mockService.createProductServices.mockResolvedValue([
        {
          id: "ps_123",
          name: "Taxi Transfer – Minivan",
          type: "Service",
          category_id: "cat_123",
        },
      ]);

      // This would be the actual service call in a real test
      // const result = await supplierProductsServicesService.createProductService({
      //   type: "Service",
      //   category_id: "cat_123",
      //   unit_type_id: "ut_123",
      //   custom_fields: { vehicle_type: "Minivan" },
      // });

      expect(generateProductServiceName).toHaveBeenCalledWith(
        "Taxi Transfer",
        { vehicle_type: "Minivan" },
        mockCategory.dynamic_field_schema
      );
    });

    it("should prevent duplicate products with same configuration", async () => {
      const mockCategory = {
        id: "cat_123",
        name: "Taxi Transfer",
        dynamic_field_schema: [
          {
            label: "Vehicle Type",
            key: "vehicle_type",
            type: "dropdown",
            required: true,
            used_in_product: true,
          },
        ],
      };

      const existingProduct = {
        id: "ps_existing",
        category_id: "cat_123",
        custom_fields: { vehicle_type: "Minivan" },
      };

      (generateProductServiceUniqueKey as jest.Mock)
        .mockReturnValueOnce("cat_123||vehicle_type:minivan") // for new product
        .mockReturnValueOnce("cat_123||vehicle_type:minivan"); // for existing product

      mockService.listCategories.mockResolvedValue([mockCategory]);
      mockService.listProductServices.mockResolvedValue([existingProduct]);

      // This would throw a MedusaError in the actual service
      // expect(async () => {
      //   await supplierProductsServicesService.createProductService({
      //     type: "Service",
      //     category_id: "cat_123",
      //     unit_type_id: "ut_123",
      //     custom_fields: { vehicle_type: "Minivan" },
      //   });
      // }).rejects.toThrow("A product with this configuration already exists.");

      expect(generateProductServiceUniqueKey).toHaveBeenCalledTimes(2);
    });
  });

  describe("Supplier Offering Creation", () => {
    it("should prevent duplicate offerings with same configuration and dates", async () => {
      const mockCategory = {
        id: "cat_123",
        dynamic_field_schema: [
          {
            label: "Vehicle Type",
            key: "vehicle_type",
            type: "dropdown",
            required: true,
            used_in_supplier_offering: true,
          },
        ],
      };

      const mockProductService = {
        id: "ps_123",
        category_id: "cat_123",
      };

      const existingOffering = {
        id: "so_existing",
        supplier_id: "supplier_123",
        product_service_id: "ps_123",
        active_from: "2024-01-01",
        active_to: "2024-12-31",
        custom_fields: { vehicle_type: "Minivan" },
      };

      (generateSupplierOfferingUniqueKey as jest.Mock)
        .mockReturnValueOnce("supplier_123||ps_123||2024-01-01:2024-12-31||vehicle_type:minivan") // for new offering
        .mockReturnValueOnce("supplier_123||ps_123||2024-01-01:2024-12-31||vehicle_type:minivan"); // for existing offering

      mockService.listCategories.mockResolvedValue([mockCategory]);
      mockService.listSupplierOfferingsWithFilters.mockResolvedValue({
        data: [existingOffering],
      });

      // This would throw a MedusaError in the actual service
      // expect(async () => {
      //   await supplierProductsServicesService.createSupplierOffering({
      //     supplier_id: "supplier_123",
      //     product_service_id: "ps_123",
      //     active_from: new Date("2024-01-01"),
      //     active_to: new Date("2024-12-31"),
      //     custom_fields: { vehicle_type: "Minivan" },
      //     cost: 100,
      //     currency: "CHF",
      //   });
      // }).rejects.toThrow("This supplier offering already exists for the selected configuration and dates.");

      expect(generateSupplierOfferingUniqueKey).toHaveBeenCalledTimes(2);
    });

    it("should allow offerings with different configurations", async () => {
      const mockCategory = {
        id: "cat_123",
        dynamic_field_schema: [
          {
            label: "Vehicle Type",
            key: "vehicle_type",
            type: "dropdown",
            required: true,
            used_in_supplier_offering: true,
          },
        ],
      };

      const existingOffering = {
        id: "so_existing",
        supplier_id: "supplier_123",
        product_service_id: "ps_123",
        active_from: "2024-01-01",
        active_to: "2024-12-31",
        custom_fields: { vehicle_type: "Sedan" },
      };

      (generateSupplierOfferingUniqueKey as jest.Mock)
        .mockReturnValueOnce("supplier_123||ps_123||2024-01-01:2024-12-31||vehicle_type:minivan") // for new offering
        .mockReturnValueOnce("supplier_123||ps_123||2024-01-01:2024-12-31||vehicle_type:sedan"); // for existing offering

      mockService.listCategories.mockResolvedValue([mockCategory]);
      mockService.listSupplierOfferingsWithFilters.mockResolvedValue({
        data: [existingOffering],
      });
      mockService.createSupplierOfferings.mockResolvedValue([
        {
          id: "so_new",
          supplier_id: "supplier_123",
          product_service_id: "ps_123",
          custom_fields: { vehicle_type: "Minivan" },
        },
      ]);

      // This should succeed as the configurations are different
      // const result = await supplierProductsServicesService.createSupplierOffering({
      //   supplier_id: "supplier_123",
      //   product_service_id: "ps_123",
      //   active_from: new Date("2024-01-01"),
      //   active_to: new Date("2024-12-31"),
      //   custom_fields: { vehicle_type: "Minivan" },
      //   cost: 100,
      //   currency: "CHF",
      // });

      expect(generateSupplierOfferingUniqueKey).toHaveBeenCalledTimes(2);
    });
  });
});

// Example of how to run these tests:
// npm test -- --testPathPattern=dynamic-naming.integration.test.ts
