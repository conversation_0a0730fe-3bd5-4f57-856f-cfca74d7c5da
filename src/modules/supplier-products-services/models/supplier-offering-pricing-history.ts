import { model } from "@camped-ai/framework/utils";
import { SupplierOffering } from "./supplier-offering";

export const SupplierOfferingPricingHistory = model
  .define("supplier_offering_pricing_history", {
    id: model.id({ prefix: "soph" }).primaryKey(),
    
    // Relationships
    supplier_offering: model.belongsTo(() => SupplierOffering, {
      foreignKey: "supplier_offering_id",
    }),
    
    // Previous Values
    previous_commission: model.number().nullable(),
    previous_public_price: model.number().nullable(),
    previous_supplier_price: model.number().nullable(),
    previous_net_price: model.number().nullable(),
    previous_margin_rate: model.number().nullable(),
    previous_selling_price: model.number().nullable(),
    previous_custom_prices: model.json().nullable(), // Array of {name: string, price: number}
    previous_currency: model.text().nullable(),
    
    // New Values
    new_commission: model.number().nullable(),
    new_public_price: model.number().nullable(),
    new_supplier_price: model.number().nullable(),
    new_net_price: model.number().nullable(),
    new_margin_rate: model.number().nullable(),
    new_selling_price: model.number().nullable(),
    new_custom_prices: model.json().nullable(), // Array of {name: string, price: number}
    new_currency: model.text().nullable(),
    
    // Change Details
    change_type: model.enum([
      "commission_update",
      "public_price_update", 
      "margin_rate_update",
      "custom_prices_update",
      "currency_update",
      "bulk_pricing_update",
      "initial_creation",
      "legacy_cost_migration"
    ]),
    change_reason: model.text().nullable(), // Optional reason for the change
    changed_by_user_id: model.text().nullable(), // User who made the change
    
    // Calculated Change Metrics
    commission_change_percent: model.number().nullable(), // Percentage change in commission
    public_price_change_percent: model.number().nullable(), // Percentage change in public price
    margin_rate_change_percent: model.number().nullable(), // Percentage change in margin rate
    selling_price_change_percent: model.number().nullable(), // Percentage change in selling price
    selling_price_change_amount: model.number().nullable(), // Absolute change in selling price
  })
  .indexes([
    {
      name: "IDX_supplier_offering_pricing_history_offering_id",
      on: ["supplier_offering_id"],
      unique: false,
      where: "deleted_at IS NULL",
    },
    {
      name: "IDX_supplier_offering_pricing_history_change_type",
      on: ["change_type"],
      unique: false,
      where: "deleted_at IS NULL",
    },
    {
      name: "IDX_supplier_offering_pricing_history_changed_by",
      on: ["changed_by_user_id"],
      unique: false,
      where: "deleted_at IS NULL",
    },
    {
      name: "IDX_supplier_offering_pricing_history_created_at",
      on: ["created_at"],
      unique: false,
      where: "deleted_at IS NULL",
    },
    {
      name: "IDX_supplier_offering_pricing_history_offering_date",
      on: ["supplier_offering_id", "created_at"],
      unique: false,
      where: "deleted_at IS NULL",
    },
  ]);
