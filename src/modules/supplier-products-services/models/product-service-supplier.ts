import { model } from "@camped-ai/framework/utils";
import { ProductService } from "./product-service";

export const ProductServiceSupplier = model
  .define("product_service_supplier", {
    id: model.id({ prefix: "pss" }).primaryKey(),
    
    // Relationships
    product_service: model.belongsTo(() => ProductService, {
      foreignKey: "product_service_id",
    }),
    
    // Supplier reference (foreign key to vendor_management.supplier)
    supplier_id: model.text(), // References supplier.id from vendor_management module
    
    // Pricing Information
    cost: model.number(), // Cost in CHF
    currency_code: model.text().default("CHF"),
    
    // Availability Information
    availability: model.text(), // e.g., "10/day", "Weekends", "On demand"
    max_capacity: model.number().nullable(), // Maximum capacity if applicable
    
    // Seasonal Information
    season: model.text().nullable(), // e.g., "Dec – Mar", "Year-round", "Summer only"
    valid_from: model.dateTime().nullable(),
    valid_until: model.dateTime().nullable(),
    
    // Additional Details
    notes: model.text().nullable(), // Internal notes about this supplier relationship
    lead_time_days: model.number().nullable(), // How many days notice needed
    minimum_order: model.number().nullable(), // Minimum order quantity
    
    // Status
    is_active: model.boolean().default(true),
    is_preferred: model.boolean().default(false), // Mark as preferred supplier
    
    // Metadata
    created_by: model.text().nullable(), // User who created this link
    updated_by: model.text().nullable(), // User who last updated this link
  })
  .indexes([
    {
      name: "IDX_product_service_supplier_ps_id",
      on: ["product_service_id"],
      unique: false,
      where: "deleted_at IS NULL",
    },
    {
      name: "IDX_product_service_supplier_supplier_id",
      on: ["supplier_id"],
      unique: false,
      where: "deleted_at IS NULL",
    },
    {
      name: "IDX_product_service_supplier_unique",
      on: ["product_service_id", "supplier_id"],
      unique: true,
      where: "deleted_at IS NULL",
    },
    {
      name: "IDX_product_service_supplier_is_active",
      on: ["is_active"],
      unique: false,
      where: "deleted_at IS NULL",
    },
    {
      name: "IDX_product_service_supplier_is_preferred",
      on: ["is_preferred"],
      unique: false,
      where: "deleted_at IS NULL",
    },
    {
      name: "IDX_product_service_supplier_season",
      on: ["valid_from", "valid_until"],
      unique: false,
      where: "deleted_at IS NULL",
    },
  ]);
