import { model } from "@camped-ai/framework/utils";
import { ProductService } from "./product-service";

export const ProductServiceCostHistory = model
  .define("product_service_cost_history", {
    id: model.id({ prefix: "psch" }).primaryKey(),
    
    // Relationships
    product_service: model.belongsTo(() => ProductService, {
      foreignKey: "product_service_id",
    }),
    
    // Cost Changes
    previous_cost: model.number().nullable(), // Previous base_cost value
    new_cost: model.number().nullable(), // New base_cost value
    
    // Change Details
    change_reason: model.text().nullable(), // Optional reason for the change
    changed_by_user_id: model.text().nullable(), // User who made the change
  })
  .indexes([
    {
      name: "IDX_product_service_cost_history_product_service_id",
      on: ["product_service_id"],
      unique: false,
      where: "deleted_at IS NULL",
    },
    {
      name: "IDX_product_service_cost_history_changed_by",
      on: ["changed_by_user_id"],
      unique: false,
      where: "deleted_at IS NULL",
    },
    {
      name: "IDX_product_service_cost_history_created_at",
      on: ["created_at"],
      unique: false,
      where: "deleted_at IS NULL",
    },
    {
      name: "IDX_product_service_cost_history_cost_change",
      on: ["previous_cost", "new_cost"],
      unique: false,
      where: "deleted_at IS NULL",
    },
    {
      name: "IDX_product_service_cost_history_product_service_created",
      on: ["product_service_id", "created_at"],
      unique: false,
      where: "deleted_at IS NULL",
    },
  ]);
