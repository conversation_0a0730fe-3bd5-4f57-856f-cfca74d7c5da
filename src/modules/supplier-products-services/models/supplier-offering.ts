import { model } from "@camped-ai/framework/utils";
import { ProductService } from "./product-service";

export const SupplierOffering = model
  .define("supplier_offering", {
    id: model.id({ prefix: "so" }).primaryKey(),

    // Relationships
    product_service: model.belongsTo(() => ProductService, {
      foreignKey: "product_service_id",
    }),

    // Supplier reference (foreign key to vendor_management.supplier)
    supplier_id: model.text(), // References supplier.id from vendor_management module

    // Validity Period
    active_from: model.dateTime().nullable(), // Optional (default: today)
    active_to: model.dateTime().nullable(), // Optional

    // Availability and Notes
    availability_notes: model.text().nullable(), // Optional notes shown in internal tools

    // Legacy Cost Field (kept for backward compatibility)
    cost: model.number().nullable(), // Legacy cost field - use public_price instead

    // Enhanced Pricing Fields
    commission: model.number().nullable(), // Commission percentage (e.g., 0.1 for 10%)
    gross_price: model.number().nullable(), // Gross price (renamed from public_price)
    net_cost: model.number().nullable(), // Net cost to supplier: Gross Price - (Gross Price × Commission)
    margin_rate: model.number().nullable(), // Margin rate percentage (e.g., 0.1 for 10%)
    selling_price: model.number().nullable(), // Calculated: supplier_price ÷ (1 - Margin Rate)

    // Currency Fields
    currency: model.text().nullable(), // Cost currency code (CHF, EUR, USD, etc.)
    currency_override: model.boolean().default(false), // Whether currency is overridden from supplier default

    // Selling Currency Fields
    selling_currency: model.text().nullable(), // Selling currency code (can be different from cost currency)
    selling_price_selling_currency: model.number().nullable(), // Selling price in selling currency
    exchange_rate: model.number().nullable(), // Exchange rate from cost currency to selling currency
    exchange_rate_date: model.dateTime().nullable(), // When the exchange rate was last updated

    // Status
    status: model.enum(["active", "inactive"]).default("active"),

    // Category-specific Custom Fields (JSON storage for dynamic field values)
    custom_fields: model.json().nullable(), // JSON storage for category-specific fields

    // Add-ons Configuration (JSON storage for addon line items with mandatory/optional settings)
    add_ons: model.json().nullable(), // JSON storage for addon line items with pricing and mandatory flags

    // Audit Fields
    created_by: model.text().nullable(), // User who created this offering
    updated_by: model.text().nullable(), // User who last updated this offering
  })
  .indexes([
    // Note: Uniqueness is enforced at the application level based on:
    // supplier_id + product_service_id + mandatory_dynamic_fields + active_from + active_to
    // This allows multiple offerings for the same supplier/product with different configurations
    {
      name: "IDX_supplier_offering_product_service_id",
      on: ["product_service_id"],
      unique: false,
      where: "deleted_at IS NULL",
    },
    {
      name: "IDX_supplier_offering_supplier_id",
      on: ["supplier_id"],
      unique: false,
      where: "deleted_at IS NULL",
    },
    {
      name: "IDX_supplier_offering_supplier_product",
      on: ["supplier_id", "product_service_id"],
      unique: false,
      where: "deleted_at IS NULL",
    },
    {
      name: "IDX_supplier_offering_status",
      on: ["status"],
      unique: false,
      where: "deleted_at IS NULL",
    },
    {
      name: "IDX_supplier_offering_validity",
      on: ["active_from", "active_to"],
      unique: false,
      where: "deleted_at IS NULL",
    },
    {
      name: "IDX_supplier_offering_created_by",
      on: ["created_by"],
      unique: false,
      where: "deleted_at IS NULL",
    },
    {
      name: "IDX_supplier_offering_dates",
      on: ["active_from", "active_to"],
      unique: false,
      where: "deleted_at IS NULL",
    },
    {
      name: "IDX_supplier_offering_custom_fields",
      on: ["custom_fields"],
      unique: false,
      where: "deleted_at IS NULL",
    },
    {
      name: "IDX_supplier_offering_uniqueness_check",
      on: ["supplier_id", "product_service_id", "active_from", "active_to"],
      unique: false,
      where: "deleted_at IS NULL",
    },
  ]);
