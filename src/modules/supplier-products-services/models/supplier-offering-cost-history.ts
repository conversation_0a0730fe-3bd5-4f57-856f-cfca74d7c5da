import { model } from "@camped-ai/framework/utils";
import { SupplierOffering } from "./supplier-offering";

export const SupplierOfferingCostHistory = model
  .define("supplier_offering_cost_history", {
    id: model.id({ prefix: "soch" }).primaryKey(),
    
    // Relationships
    supplier_offering: model.belongsTo(() => SupplierOffering, {
      foreignKey: "supplier_offering_id",
    }),
    
    // Cost Changes
    previous_cost: model.number().nullable(), // Previous cost value
    new_cost: model.number().nullable(), // New cost value
    
    // Currency Changes
    previous_currency: model.text().nullable(), // Previous currency code (CHF, EUR, USD, etc.)
    new_currency: model.text().nullable(), // New currency code (CHF, EUR, USD, etc.)
    
    // Change Details
    change_reason: model.text().nullable(), // Optional reason for the change
    changed_by_user_id: model.text().nullable(), // User who made the change
  })
  .indexes([
    {
      name: "IDX_supplier_offering_cost_history_offering_id",
      on: ["supplier_offering_id"],
      unique: false,
      where: "deleted_at IS NULL",
    },
    {
      name: "IDX_supplier_offering_cost_history_changed_by",
      on: ["changed_by_user_id"],
      unique: false,
      where: "deleted_at IS NULL",
    },
    {
      name: "IDX_supplier_offering_cost_history_created_at",
      on: ["created_at"],
      unique: false,
      where: "deleted_at IS NULL",
    },
    {
      name: "IDX_supplier_offering_cost_history_cost_change",
      on: ["previous_cost", "new_cost"],
      unique: false,
      where: "deleted_at IS NULL",
    },
    {
      name: "IDX_supplier_offering_cost_history_currency_change",
      on: ["previous_currency", "new_currency"],
      unique: false,
      where: "deleted_at IS NULL",
    },
    {
      name: "IDX_supplier_offering_cost_history_offering_created",
      on: ["supplier_offering_id", "created_at"],
      unique: false,
      where: "deleted_at IS NULL",
    },
  ]);
