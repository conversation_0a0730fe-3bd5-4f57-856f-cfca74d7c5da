import { Migration } from '@mikro-orm/migrations';

export class Migration20250703070000 extends Migration {

  override async up(): Promise<void> {
    // Fix base_cost column type to match Medusa conventions (numeric instead of DECIMAL)
    this.addSql(`
      DO $$
      BEGIN
        -- Check if base_cost column exists and alter its type
        IF EXISTS (
          SELECT 1 FROM information_schema.columns
          WHERE table_name = 'product_service' AND column_name = 'base_cost'
        ) THEN
          -- Alter column type from DECIMAL to numeric
          ALTER TABLE product_service ALTER COLUMN base_cost TYPE numeric USING base_cost::numeric;
        END IF;
      END
      $$;
    `);
  }

  override async down(): Promise<void> {
    // Revert base_cost column type back to DECIMAL
    this.addSql(`
      DO $$
      BEGIN
        IF EXISTS (
          SELECT 1 FROM information_schema.columns
          WHERE table_name = 'product_service' AND column_name = 'base_cost'
        ) THEN
          ALTER TABLE product_service ALTER COLUMN base_cost TYPE DECIMAL(10,2) USING base_cost::DECIMAL(10,2);
        END IF;
      END
      $$;
    `);
  }
}
