import { Migration } from '@mikro-orm/migrations';

export class Migration20250710130000 extends Migration {

  override async up(): Promise<void> {
    // Add hotel_id column to product_service table
    this.addSql(`
      ALTER TABLE "product_service" 
      ADD COLUMN IF NOT EXISTS "hotel_id" text NULL;
    `);

    // Update existing records to use 'hotel' instead of 'general' for service_level
    this.addSql(`
      UPDATE "product_service" 
      SET "service_level" = 'hotel' 
      WHERE "service_level" = 'general';
    `);

    // Update the default value for service_level column
    this.addSql(`
      ALTER TABLE "product_service" 
      ALTER COLUMN "service_level" SET DEFAULT 'hotel';
    `);

    // Add indexes for the new hotel_id column
    this.addSql(`
      CREATE INDEX IF NOT EXISTS "IDX_product_service_hotel_id" 
      ON "product_service" (hotel_id) 
      WHERE deleted_at IS NULL AND hotel_id IS NOT NULL;
    `);

    this.addSql(`
      CREATE INDEX IF NOT EXISTS "IDX_product_service_service_level_hotel" 
      ON "product_service" (service_level, hotel_id) 
      WHERE deleted_at IS NULL;
    `);
  }

  override async down(): Promise<void> {
    // Drop indexes first
    this.addSql(`DROP INDEX IF EXISTS "IDX_product_service_service_level_hotel";`);
    this.addSql(`DROP INDEX IF EXISTS "IDX_product_service_hotel_id";`);

    // Revert service_level values back to 'general'
    this.addSql(`
      UPDATE "product_service" 
      SET "service_level" = 'general' 
      WHERE "service_level" = 'hotel';
    `);

    // Revert the default value for service_level column
    this.addSql(`
      ALTER TABLE "product_service" 
      ALTER COLUMN "service_level" SET DEFAULT 'general';
    `);

    // Drop hotel_id column
    this.addSql(`
      ALTER TABLE "product_service" 
      DROP COLUMN IF EXISTS "hotel_id";
    `);
  }
}
