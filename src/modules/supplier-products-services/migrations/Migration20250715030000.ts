import { Migration } from "@mikro-orm/migrations";

export class Migration20250715030000 extends Migration {
  override async up(): Promise<void> {
    // Fix cost history tables data types to use DECIMAL instead of integer
    // This fixes the issue where decimal cost values can't be stored in integer columns
    
    // Fix product_service_cost_history table
    this.addSql(`
      ALTER TABLE "product_service_cost_history" 
      ALTER COLUMN "previous_cost" TYPE DECIMAL(10,2),
      ALTER COLUMN "new_cost" TYPE DECIMAL(10,2);
    `);
    
    // Fix supplier_offering_cost_history table  
    this.addSql(`
      ALTER TABLE "supplier_offering_cost_history" 
      ALTER COLUMN "previous_cost" TYPE DECIMAL(10,2),
      ALTER COLUMN "new_cost" TYPE DECIMAL(10,2);
    `);
  }

  override async down(): Promise<void> {
    // Revert back to integer (this will truncate decimal values)
    this.addSql(`
      ALTER TABLE "product_service_cost_history" 
      ALTER COLUMN "previous_cost" TYPE integer,
      ALTER COLUMN "new_cost" TYPE integer;
    `);
    
    this.addSql(`
      ALTER TABLE "supplier_offering_cost_history" 
      ALTER COLUMN "previous_cost" TYPE integer,
      ALTER COLUMN "new_cost" TYPE integer;
    `);
  }
}
