import { Migration } from "@mikro-orm/migrations";

export class Migration20250703064900 extends Migration {
  override async up(): Promise<void> {
    // First create all the dependency tables
    this.addSql(
      `create table if not exists "product_service_category" ("id" text not null, "name" text not null, "description" text null, "category_type" text check ("category_type" in ('Product', 'Service', 'Both')) not null default 'Both', "icon" text null, "dynamic_field_schema" jsonb null, "is_active" boolean not null default true, "created_at" timestamptz not null default now(), "updated_at" timestamptz not null default now(), "deleted_at" timestamptz null, constraint "product_service_category_pkey" primary key ("id"));`
    );
    this.addSql(
      `CREATE INDEX IF NOT EXISTS "IDX_product_service_category_deleted_at" ON "product_service_category" (deleted_at) WHERE deleted_at IS NULL;`
    );
    this.addSql(
      `CREATE UNIQUE INDEX IF NOT EXISTS "IDX_product_service_category_name" ON "product_service_category" (name) WHERE deleted_at IS NULL;`
    );
    this.addSql(
      `CREATE INDEX IF NOT EXISTS "IDX_product_service_category_is_active" ON "product_service_category" (is_active) WHERE deleted_at IS NULL;`
    );
    this.addSql(
      `CREATE INDEX IF NOT EXISTS "IDX_product_service_category_type" ON "product_service_category" (category_type) WHERE deleted_at IS NULL;`
    );

    this.addSql(
      `create table if not exists "product_service_unit_type" ("id" text not null, "name" text not null, "description" text null, "is_active" boolean not null default true, "created_at" timestamptz not null default now(), "updated_at" timestamptz not null default now(), "deleted_at" timestamptz null, constraint "product_service_unit_type_pkey" primary key ("id"));`
    );
    this.addSql(
      `CREATE INDEX IF NOT EXISTS "IDX_product_service_unit_type_deleted_at" ON "product_service_unit_type" (deleted_at) WHERE deleted_at IS NULL;`
    );
    this.addSql(
      `CREATE UNIQUE INDEX IF NOT EXISTS "IDX_product_service_unit_type_name" ON "product_service_unit_type" (name) WHERE deleted_at IS NULL;`
    );
    this.addSql(
      `CREATE INDEX IF NOT EXISTS "IDX_product_service_unit_type_is_active" ON "product_service_unit_type" (is_active) WHERE deleted_at IS NULL;`
    );

    // Create the main table with all columns including the new ones
    this.addSql(
      `create table if not exists "product_service" ("id" text not null, "name" text not null, "type" text not null, "description" text null, "base_cost" numeric null, "custom_fields" jsonb null, "status" text not null default 'active', "category_id" text not null, "unit_type_id" text not null, "created_at" timestamptz not null default now(), "updated_at" timestamptz not null default now(), "deleted_at" timestamptz null, constraint "product_service_pkey" primary key ("id"));`
    );

    // Add missing columns to existing table if they don't exist
    this.addSql(`
      DO $$
      BEGIN
        -- Add base_cost column if it doesn't exist (using numeric for consistency with Medusa)
        IF NOT EXISTS (
          SELECT 1 FROM information_schema.columns
          WHERE table_name = 'product_service' AND column_name = 'base_cost'
        ) THEN
          ALTER TABLE product_service ADD COLUMN base_cost numeric null;
        END IF;

        -- Add custom_fields column if it doesn't exist
        IF NOT EXISTS (
          SELECT 1 FROM information_schema.columns
          WHERE table_name = 'product_service' AND column_name = 'custom_fields'
        ) THEN
          ALTER TABLE product_service ADD COLUMN custom_fields jsonb null;
        END IF;
      END
      $$;
    `);

    // Create indexes for all columns
    this.addSql(
      `CREATE INDEX IF NOT EXISTS "IDX_product_service_base_cost" ON "product_service" (base_cost) WHERE deleted_at IS NULL AND base_cost IS NOT NULL;`
    );

    // Drop and recreate constraints to ensure they're properly set (only if they exist)
    this.addSql(
      `alter table if exists "product_service_custom_field_junction" drop constraint if exists "product_service_custom_field_junction_unique";`
    );
    this.addSql(
      `alter table if exists "product_service_supplier" drop constraint if exists "product_service_supplier_unique";`
    );
    this.addSql(
      `alter table if exists "product_service_tag_junction" drop constraint if exists "product_service_tag_junction_unique";`
    );

    // Create remaining tables (dependencies already created above)

    this.addSql(
      `create table if not exists "product_service_custom_field" ("id" text not null, "name" text not null, "field_type" text not null, "options" jsonb null, "is_required" boolean not null default false, "default_value" text null, "validation_rules" jsonb null, "is_active" boolean not null default true, "created_at" timestamptz not null default now(), "updated_at" timestamptz not null default now(), "deleted_at" timestamptz null, constraint "product_service_custom_field_pkey" primary key ("id"));`
    );
    this.addSql(
      `CREATE INDEX IF NOT EXISTS "IDX_product_service_custom_field_deleted_at" ON "product_service_custom_field" (deleted_at) WHERE deleted_at IS NULL;`
    );
    this.addSql(
      `CREATE UNIQUE INDEX IF NOT EXISTS "IDX_product_service_custom_field_name" ON "product_service_custom_field" (name) WHERE deleted_at IS NULL;`
    );
    this.addSql(
      `CREATE INDEX IF NOT EXISTS "IDX_product_service_custom_field_type" ON "product_service_custom_field" (field_type) WHERE deleted_at IS NULL;`
    );
    this.addSql(
      `CREATE INDEX IF NOT EXISTS "IDX_product_service_custom_field_is_active" ON "product_service_custom_field" (is_active) WHERE deleted_at IS NULL;`
    );

    this.addSql(
      `create table if not exists "product_service_tag" ("id" text not null, "name" text not null, "color" text null, "is_active" boolean not null default true, "created_at" timestamptz not null default now(), "updated_at" timestamptz not null default now(), "deleted_at" timestamptz null, constraint "product_service_tag_pkey" primary key ("id"));`
    );
    this.addSql(
      `CREATE INDEX IF NOT EXISTS "IDX_product_service_tag_deleted_at" ON "product_service_tag" (deleted_at) WHERE deleted_at IS NULL;`
    );
    this.addSql(
      `CREATE UNIQUE INDEX IF NOT EXISTS "IDX_product_service_tag_name" ON "product_service_tag" (name) WHERE deleted_at IS NULL;`
    );
    this.addSql(
      `CREATE INDEX IF NOT EXISTS "IDX_product_service_tag_is_active" ON "product_service_tag" (is_active) WHERE deleted_at IS NULL;`
    );

    // Tables already created above, now create indexes for product_service

    // Create indexes for existing columns
    this.addSql(
      `CREATE INDEX IF NOT EXISTS "IDX_product_service_category_id" ON "product_service" (category_id) WHERE deleted_at IS NULL;`
    );
    this.addSql(
      `CREATE INDEX IF NOT EXISTS "IDX_product_service_unit_type_id" ON "product_service" (unit_type_id) WHERE deleted_at IS NULL;`
    );
    this.addSql(
      `CREATE INDEX IF NOT EXISTS "IDX_product_service_deleted_at" ON "product_service" (deleted_at) WHERE deleted_at IS NULL;`
    );
    this.addSql(
      `CREATE INDEX IF NOT EXISTS "IDX_product_service_name" ON "product_service" (name) WHERE deleted_at IS NULL;`
    );
    this.addSql(
      `CREATE INDEX IF NOT EXISTS "IDX_product_service_type" ON "product_service" (type) WHERE deleted_at IS NULL;`
    );
    this.addSql(
      `CREATE INDEX IF NOT EXISTS "IDX_product_service_status" ON "product_service" (status) WHERE deleted_at IS NULL;`
    );
    this.addSql(
      `CREATE INDEX IF NOT EXISTS "IDX_product_service_type_category" ON "product_service" (type, category_id) WHERE deleted_at IS NULL;`
    );
    this.addSql(
      `CREATE INDEX IF NOT EXISTS "IDX_product_service_status_type" ON "product_service" (status, type) WHERE deleted_at IS NULL;`
    );

    this.addSql(
      `create table if not exists "product_service_tag_junction" ("id" text not null, "product_service_id" text not null, "tag_id" text not null, "created_at" timestamptz not null default now(), "updated_at" timestamptz not null default now(), "deleted_at" timestamptz null, constraint "product_service_tag_junction_pkey" primary key ("id"));`
    );
    this.addSql(
      `CREATE INDEX IF NOT EXISTS "IDX_product_service_tag_junction_product_service_id" ON "product_service_tag_junction" (product_service_id) WHERE deleted_at IS NULL;`
    );
    this.addSql(
      `CREATE INDEX IF NOT EXISTS "IDX_product_service_tag_junction_tag_id" ON "product_service_tag_junction" (tag_id) WHERE deleted_at IS NULL;`
    );
    this.addSql(
      `CREATE INDEX IF NOT EXISTS "IDX_product_service_tag_junction_deleted_at" ON "product_service_tag_junction" (deleted_at) WHERE deleted_at IS NULL;`
    );
    this.addSql(
      `CREATE INDEX IF NOT EXISTS "IDX_product_service_tag_junction_ps_id" ON "product_service_tag_junction" (product_service_id) WHERE deleted_at IS NULL;`
    );
    this.addSql(
      `CREATE UNIQUE INDEX IF NOT EXISTS "IDX_product_service_tag_junction_unique" ON "product_service_tag_junction" (product_service_id, tag_id) WHERE deleted_at IS NULL;`
    );

    this.addSql(
      `create table if not exists "product_service_supplier" ("id" text not null, "product_service_id" text not null, "supplier_id" text not null, "cost" integer not null, "currency_code" text not null default 'CHF', "availability" text not null, "max_capacity" integer null, "season" text null, "valid_from" timestamptz null, "valid_until" timestamptz null, "notes" text null, "lead_time_days" integer null, "minimum_order" integer null, "is_active" boolean not null default true, "is_preferred" boolean not null default false, "created_by" text null, "updated_by" text null, "created_at" timestamptz not null default now(), "updated_at" timestamptz not null default now(), "deleted_at" timestamptz null, constraint "product_service_supplier_pkey" primary key ("id"));`
    );
    this.addSql(
      `CREATE INDEX IF NOT EXISTS "IDX_product_service_supplier_product_service_id" ON "product_service_supplier" (product_service_id) WHERE deleted_at IS NULL;`
    );
    this.addSql(
      `CREATE INDEX IF NOT EXISTS "IDX_product_service_supplier_deleted_at" ON "product_service_supplier" (deleted_at) WHERE deleted_at IS NULL;`
    );
    this.addSql(
      `CREATE INDEX IF NOT EXISTS "IDX_product_service_supplier_ps_id" ON "product_service_supplier" (product_service_id) WHERE deleted_at IS NULL;`
    );
    this.addSql(
      `CREATE INDEX IF NOT EXISTS "IDX_product_service_supplier_supplier_id" ON "product_service_supplier" (supplier_id) WHERE deleted_at IS NULL;`
    );
    this.addSql(
      `CREATE UNIQUE INDEX IF NOT EXISTS "IDX_product_service_supplier_unique" ON "product_service_supplier" (product_service_id, supplier_id) WHERE deleted_at IS NULL;`
    );
    this.addSql(
      `CREATE INDEX IF NOT EXISTS "IDX_product_service_supplier_is_active" ON "product_service_supplier" (is_active) WHERE deleted_at IS NULL;`
    );
    this.addSql(
      `CREATE INDEX IF NOT EXISTS "IDX_product_service_supplier_is_preferred" ON "product_service_supplier" (is_preferred) WHERE deleted_at IS NULL;`
    );
    this.addSql(
      `CREATE INDEX IF NOT EXISTS "IDX_product_service_supplier_season" ON "product_service_supplier" (valid_from, valid_until) WHERE deleted_at IS NULL;`
    );

    this.addSql(
      `create table if not exists "product_service_custom_field_junction" ("id" text not null, "value" text null, "product_service_id" text not null, "custom_field_id" text not null, "created_at" timestamptz not null default now(), "updated_at" timestamptz not null default now(), "deleted_at" timestamptz null, constraint "product_service_custom_field_junction_pkey" primary key ("id"));`
    );
    this.addSql(
      `CREATE INDEX IF NOT EXISTS "IDX_product_service_custom_field_junction_product_service_id" ON "product_service_custom_field_junction" (product_service_id) WHERE deleted_at IS NULL;`
    );
    this.addSql(
      `CREATE INDEX IF NOT EXISTS "IDX_product_service_custom_field_junction_custom_field_id" ON "product_service_custom_field_junction" (custom_field_id) WHERE deleted_at IS NULL;`
    );
    this.addSql(
      `CREATE INDEX IF NOT EXISTS "IDX_product_service_custom_field_junction_deleted_at" ON "product_service_custom_field_junction" (deleted_at) WHERE deleted_at IS NULL;`
    );
    this.addSql(
      `CREATE INDEX IF NOT EXISTS "IDX_product_service_custom_field_junction_ps_id" ON "product_service_custom_field_junction" (product_service_id) WHERE deleted_at IS NULL;`
    );
    this.addSql(
      `CREATE INDEX IF NOT EXISTS "IDX_product_service_custom_field_junction_cf_id" ON "product_service_custom_field_junction" (custom_field_id) WHERE deleted_at IS NULL;`
    );
    this.addSql(
      `CREATE UNIQUE INDEX IF NOT EXISTS "IDX_product_service_custom_field_junction_unique" ON "product_service_custom_field_junction" (product_service_id, custom_field_id) WHERE deleted_at IS NULL;`
    );

    // Add foreign key constraints only if they don't exist
    this.addSql(`
      DO $$
      BEGIN
        -- Add product_service foreign key constraints
        IF NOT EXISTS (
          SELECT 1 FROM information_schema.table_constraints
          WHERE constraint_name = 'product_service_category_id_foreign'
          AND table_name = 'product_service'
        ) THEN
          ALTER TABLE product_service ADD CONSTRAINT "product_service_category_id_foreign"
          FOREIGN KEY ("category_id") REFERENCES "product_service_category" ("id") ON UPDATE CASCADE;
        END IF;

        IF NOT EXISTS (
          SELECT 1 FROM information_schema.table_constraints
          WHERE constraint_name = 'product_service_unit_type_id_foreign'
          AND table_name = 'product_service'
        ) THEN
          ALTER TABLE product_service ADD CONSTRAINT "product_service_unit_type_id_foreign"
          FOREIGN KEY ("unit_type_id") REFERENCES "product_service_unit_type" ("id") ON UPDATE CASCADE;
        END IF;

        -- Add product_service_supplier foreign key constraint
        IF NOT EXISTS (
          SELECT 1 FROM information_schema.table_constraints
          WHERE constraint_name = 'product_service_supplier_product_service_id_foreign'
          AND table_name = 'product_service_supplier'
        ) THEN
          ALTER TABLE product_service_supplier ADD CONSTRAINT "product_service_supplier_product_service_id_foreign"
          FOREIGN KEY ("product_service_id") REFERENCES "product_service" ("id") ON UPDATE CASCADE;
        END IF;
      END
      $$;
    `);
  }

  override async down(): Promise<void> {
    this.addSql(
      `alter table if exists "product_service" drop constraint if exists "product_service_category_id_foreign";`
    );

    this.addSql(
      `alter table if exists "product_service_custom_field_junction" drop constraint if exists "product_service_custom_field_junction_custom_field_id_foreign";`
    );

    this.addSql(
      `alter table if exists "product_service_tag_junction" drop constraint if exists "product_service_tag_junction_tag_id_foreign";`
    );

    this.addSql(
      `alter table if exists "product_service" drop constraint if exists "product_service_unit_type_id_foreign";`
    );

    this.addSql(
      `alter table if exists "product_service_tag_junction" drop constraint if exists "product_service_tag_junction_product_service_id_foreign";`
    );

    this.addSql(
      `alter table if exists "product_service_supplier" drop constraint if exists "product_service_supplier_product_service_id_foreign";`
    );

    this.addSql(
      `alter table if exists "product_service_custom_field_junction" drop constraint if exists "product_service_custom_field_junction_product_se_3c156_foreign";`
    );

    this.addSql(`drop table if exists "product_service_category" cascade;`);

    this.addSql(`drop table if exists "product_service_custom_field" cascade;`);

    this.addSql(`drop table if exists "product_service_tag" cascade;`);

    this.addSql(`drop table if exists "product_service_unit_type" cascade;`);

    this.addSql(`drop table if exists "product_service" cascade;`);

    this.addSql(`drop table if exists "product_service_tag_junction" cascade;`);

    this.addSql(`drop table if exists "product_service_supplier" cascade;`);

    this.addSql(
      `drop table if exists "product_service_custom_field_junction" cascade;`
    );
  }
}
