import { Migration } from '@mikro-orm/migrations';

export class Migration20241213123456 extends Migration {

  async up(): Promise<void> {
    // Add price tracking fields to product_service table
    this.addSql(`
      ALTER TABLE "product_service" 
      ADD COLUMN "highest_price" numeric NULL,
      ADD COLUMN "highest_price_currency" text NULL,
      ADD COLUMN "price_flag_active" boolean NOT NULL DEFAULT false,
      ADD COLUMN "price_flag_created_at" timestamptz NULL,
      ADD COLUMN "price_flag_supplier_offering_id" text NULL;
    `);

    // Add indexes for performance
    this.addSql(`
      CREATE INDEX "IDX_product_service_price_flag_active" 
      ON "product_service" ("price_flag_active") 
      WHERE "deleted_at" IS NULL;
    `);

    this.addSql(`
      CREATE INDEX "IDX_product_service_highest_price" 
      ON "product_service" ("highest_price", "highest_price_currency") 
      WHERE "deleted_at" IS NULL AND "highest_price" IS NOT NULL;
    `);

    this.addSql(`
      CREATE INDEX "IDX_product_service_price_flag_created_at" 
      ON "product_service" ("price_flag_created_at") 
      WHERE "deleted_at" IS NULL AND "price_flag_active" = true;
    `);

    // Add foreign key constraint for supplier offering reference
    this.addSql(`
      CREATE INDEX "IDX_product_service_price_flag_supplier_offering" 
      ON "product_service" ("price_flag_supplier_offering_id") 
      WHERE "deleted_at" IS NULL AND "price_flag_supplier_offering_id" IS NOT NULL;
    `);

    // Add comment to document the purpose of these fields
    this.addSql(`
      COMMENT ON COLUMN "product_service"."highest_price" IS 'Highest price seen from supplier offerings for price flagging system';
    `);
    
    this.addSql(`
      COMMENT ON COLUMN "product_service"."price_flag_active" IS 'Whether a price flag is currently active indicating a higher price is available';
    `);
    
    this.addSql(`
      COMMENT ON COLUMN "product_service"."price_flag_supplier_offering_id" IS 'ID of the supplier offering that triggered the current price flag';
    `);
  }

  async down(): Promise<void> {
    // Drop indexes first
    this.addSql('DROP INDEX IF EXISTS "IDX_product_service_price_flag_active";');
    this.addSql('DROP INDEX IF EXISTS "IDX_product_service_highest_price";');
    this.addSql('DROP INDEX IF EXISTS "IDX_product_service_price_flag_created_at";');
    this.addSql('DROP INDEX IF EXISTS "IDX_product_service_price_flag_supplier_offering";');

    // Remove columns
    this.addSql(`
      ALTER TABLE "product_service" 
      DROP COLUMN IF EXISTS "highest_price",
      DROP COLUMN IF EXISTS "highest_price_currency",
      DROP COLUMN IF EXISTS "price_flag_active",
      DROP COLUMN IF EXISTS "price_flag_created_at",
      DROP COLUMN IF EXISTS "price_flag_supplier_offering_id";
    `);
  }
}
