import { Migration } from "@mikro-orm/migrations";

export class Migration20250717120100 extends Migration {
  override async up(): Promise<void> {
    // Create supplier_offering_pricing_history table for enhanced pricing tracking
    
    this.addSql(`
      CREATE TABLE IF NOT EXISTS "supplier_offering_pricing_history" (
        "id" VARCHAR(255) NOT NULL,
        "supplier_offering_id" VARCHAR(255) NOT NULL,
        
        -- Previous values
        "previous_commission" DECIMAL(5,4) NULL,
        "previous_public_price" DECIMAL(10,2) NULL,
        "previous_supplier_price" DECIMAL(10,2) NULL,
        "previous_net_price" DECIMAL(10,2) NULL,
        "previous_margin_rate" DECIMAL(5,4) NULL,
        "previous_selling_price" DECIMAL(10,2) NULL,
        "previous_custom_prices" JSONB NULL,
        "previous_currency" VARCHAR(3) NULL,
        
        -- New values
        "new_commission" DECIMAL(5,4) NULL,
        "new_public_price" DECIMAL(10,2) NULL,
        "new_supplier_price" DECIMAL(10,2) NULL,
        "new_net_price" DECIMAL(10,2) NULL,
        "new_margin_rate" DECIMAL(5,4) NULL,
        "new_selling_price" DECIMAL(10,2) NULL,
        "new_custom_prices" JSONB NULL,
        "new_currency" VARCHAR(3) NULL,
        
        -- Change details
        "change_type" VARCHAR(50) NOT NULL CHECK ("change_type" IN (
          'commission_update',
          'public_price_update', 
          'margin_rate_update',
          'custom_prices_update',
          'currency_update',
          'bulk_pricing_update',
          'initial_creation',
          'legacy_cost_migration'
        )),
        "change_reason" TEXT NULL,
        "changed_by_user_id" VARCHAR(255) NULL,
        
        -- Calculated change metrics
        "commission_change_percent" DECIMAL(10,4) NULL,
        "public_price_change_percent" DECIMAL(10,4) NULL,
        "margin_rate_change_percent" DECIMAL(10,4) NULL,
        "selling_price_change_percent" DECIMAL(10,4) NULL,
        "selling_price_change_amount" DECIMAL(10,2) NULL,
        
        -- Standard fields
        "created_at" TIMESTAMPTZ NOT NULL DEFAULT NOW(),
        "updated_at" TIMESTAMPTZ NOT NULL DEFAULT NOW(),
        "deleted_at" TIMESTAMPTZ NULL,
        
        CONSTRAINT "PK_supplier_offering_pricing_history" PRIMARY KEY ("id")
      );
    `);

    // Add foreign key constraint
    this.addSql(`
      ALTER TABLE "supplier_offering_pricing_history" 
      ADD CONSTRAINT "FK_supplier_offering_pricing_history_offering" 
      FOREIGN KEY ("supplier_offering_id") 
      REFERENCES "supplier_offering" ("id") 
      ON DELETE CASCADE;
    `);

    // Add indexes for performance
    this.addSql(`
      CREATE INDEX "IDX_supplier_offering_pricing_history_offering_id" 
      ON "supplier_offering_pricing_history"("supplier_offering_id") 
      WHERE "deleted_at" IS NULL;
    `);

    this.addSql(`
      CREATE INDEX "IDX_supplier_offering_pricing_history_change_type" 
      ON "supplier_offering_pricing_history"("change_type") 
      WHERE "deleted_at" IS NULL;
    `);

    this.addSql(`
      CREATE INDEX "IDX_supplier_offering_pricing_history_changed_by" 
      ON "supplier_offering_pricing_history"("changed_by_user_id") 
      WHERE "deleted_at" IS NULL;
    `);

    this.addSql(`
      CREATE INDEX "IDX_supplier_offering_pricing_history_created_at" 
      ON "supplier_offering_pricing_history"("created_at") 
      WHERE "deleted_at" IS NULL;
    `);

    this.addSql(`
      CREATE INDEX "IDX_supplier_offering_pricing_history_offering_date" 
      ON "supplier_offering_pricing_history"("supplier_offering_id", "created_at") 
      WHERE "deleted_at" IS NULL;
    `);

    // Create trigger for updated_at
    this.addSql(`
      CREATE OR REPLACE FUNCTION update_supplier_offering_pricing_history_updated_at()
      RETURNS TRIGGER AS $$
      BEGIN
        NEW.updated_at = NOW();
        RETURN NEW;
      END;
      $$ LANGUAGE plpgsql;
    `);

    this.addSql(`
      CREATE TRIGGER trigger_update_supplier_offering_pricing_history_updated_at
      BEFORE UPDATE ON "supplier_offering_pricing_history"
      FOR EACH ROW
      EXECUTE FUNCTION update_supplier_offering_pricing_history_updated_at();
    `);
  }

  override async down(): Promise<void> {
    // Drop trigger and function
    this.addSql(`
      DROP TRIGGER IF EXISTS trigger_update_supplier_offering_pricing_history_updated_at 
      ON "supplier_offering_pricing_history";
    `);
    
    this.addSql(`
      DROP FUNCTION IF EXISTS update_supplier_offering_pricing_history_updated_at();
    `);

    // Drop indexes
    this.addSql(`DROP INDEX IF EXISTS "IDX_supplier_offering_pricing_history_offering_id";`);
    this.addSql(`DROP INDEX IF EXISTS "IDX_supplier_offering_pricing_history_change_type";`);
    this.addSql(`DROP INDEX IF EXISTS "IDX_supplier_offering_pricing_history_changed_by";`);
    this.addSql(`DROP INDEX IF EXISTS "IDX_supplier_offering_pricing_history_created_at";`);
    this.addSql(`DROP INDEX IF EXISTS "IDX_supplier_offering_pricing_history_offering_date";`);

    // Drop foreign key constraint
    this.addSql(`
      ALTER TABLE "supplier_offering_pricing_history" 
      DROP CONSTRAINT IF EXISTS "FK_supplier_offering_pricing_history_offering";
    `);

    // Drop table
    this.addSql(`DROP TABLE IF EXISTS "supplier_offering_pricing_history";`);
  }
}
