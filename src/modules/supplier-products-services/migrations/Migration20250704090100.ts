import { Migration } from '@mikro-orm/migrations';

export class Migration20250704090100 extends Migration {

  override async up(): Promise<void> {
    // Remove any remaining unique constraints that might be causing conflicts
    // Check for various possible constraint names that might exist
    this.addSql(`DROP INDEX IF EXISTS "IDX_supplier_offering_unique";`);
    this.addSql(`DROP INDEX IF EXISTS "idx_supplier_offering_unique";`);
    this.addSql(`DROP INDEX IF EXISTS "IDX_supplier_offering_supplier_product";`);
    this.addSql(`DROP INDEX IF EXISTS "idx_supplier_offering_supplier_product";`);

    // Also check for any table-level unique constraints
    this.addSql(`
      DO $$
      BEGIN
        -- Remove any unique constraints on the combination of supplier_id and product_service_id
        IF EXISTS (
          SELECT 1 FROM information_schema.table_constraints
          WHERE constraint_name LIKE '%supplier_offering%unique%'
          AND table_name = 'supplier_offering'
        ) THEN
          ALTER TABLE supplier_offering DROP CONSTRAINT IF EXISTS supplier_offering_unique;
          ALTER TABLE supplier_offering DROP CONSTRAINT IF EXISTS supplier_offering_supplier_product_unique;
        END IF;
      END
      $$;
    `);

    // Also soft delete any existing conflicting records to allow the new one to be created
    this.addSql(`
      UPDATE supplier_offering
      SET deleted_at = NOW()
      WHERE supplier_id = 'supplier_01JZ9YK8W6NMXBQ6GARDG9JNXT'
      AND product_service_id = 'ps_01JZA6YSS59XGDZC7MK7697V4E'
      AND deleted_at IS NULL;
    `);

    console.log('Removed all unique constraints from supplier_offering table and cleaned up conflicting records');
  }

  override async down(): Promise<void> {
    // Re-create the unique constraint if rolling back
    this.addSql(`
      CREATE UNIQUE INDEX IF NOT EXISTS "IDX_supplier_offering_unique" 
      ON "supplier_offering" ("product_service_id", "supplier_id") 
      WHERE "deleted_at" IS NULL;
    `);
  }
}
