import { Migration } from '@mikro-orm/migrations';

export class Migration20250704090200 extends Migration {

  override async up(): Promise<void> {
    // Clean up any existing conflicting supplier offering records
    // This will soft delete any existing records that match the exact combination
    // that's causing the conflict
    this.addSql(`
      UPDATE supplier_offering 
      SET deleted_at = NOW() 
      WHERE supplier_id = 'supplier_01JZ9YK8W6NMXBQ6GARDG9JNXT' 
      AND product_service_id = 'ps_01JZA6YSS59XGDZC7MK7697V4E'
      AND deleted_at IS NULL;
    `);
    
    // Double-check that all unique constraints are removed
    this.addSql(`DROP INDEX IF EXISTS "IDX_supplier_offering_unique";`);
    this.addSql(`DROP INDEX IF EXISTS "idx_supplier_offering_unique";`);
    
    console.log('Cleaned up conflicting supplier offering records');
  }

  override async down(): Promise<void> {
    // This migration is for cleanup, no rollback needed
    console.log('No rollback needed for cleanup migration');
  }
}
