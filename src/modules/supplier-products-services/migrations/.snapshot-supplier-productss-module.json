{"namespaces": ["public"], "name": "public", "tables": [{"columns": {"id": {"name": "id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "name": {"name": "name", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "description": {"name": "description", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "text"}, "category_type": {"name": "category_type", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "default": "'Both'", "enumItems": ["Product", "Service", "Both"], "mappedType": "enum"}, "icon": {"name": "icon", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "text"}, "dynamic_field_schema": {"name": "dynamic_field_schema", "type": "jsonb", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "json"}, "is_active": {"name": "is_active", "type": "boolean", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "default": "true", "mappedType": "boolean"}, "created_at": {"name": "created_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "default": "now()", "mappedType": "datetime"}, "updated_at": {"name": "updated_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "default": "now()", "mappedType": "datetime"}, "deleted_at": {"name": "deleted_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 6, "mappedType": "datetime"}}, "name": "product_service_category", "schema": "public", "indexes": [{"keyName": "IDX_product_service_category_deleted_at", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_product_service_category_deleted_at\" ON \"product_service_category\" (deleted_at) WHERE deleted_at IS NULL"}, {"keyName": "IDX_product_service_category_name", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE UNIQUE INDEX IF NOT EXISTS \"IDX_product_service_category_name\" ON \"product_service_category\" (name) WHERE deleted_at IS NULL"}, {"keyName": "IDX_product_service_category_is_active", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_product_service_category_is_active\" ON \"product_service_category\" (is_active) WHERE deleted_at IS NULL"}, {"keyName": "IDX_product_service_category_type", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_product_service_category_type\" ON \"product_service_category\" (category_type) WHERE deleted_at IS NULL"}, {"keyName": "product_service_category_pkey", "columnNames": ["id"], "composite": false, "constraint": true, "primary": true, "unique": true}], "checks": [], "foreignKeys": {}, "nativeEnums": {}}, {"columns": {"id": {"name": "id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "name": {"name": "name", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "field_type": {"name": "field_type", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "options": {"name": "options", "type": "jsonb", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "json"}, "is_required": {"name": "is_required", "type": "boolean", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "default": "false", "mappedType": "boolean"}, "default_value": {"name": "default_value", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "text"}, "validation_rules": {"name": "validation_rules", "type": "jsonb", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "json"}, "is_active": {"name": "is_active", "type": "boolean", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "default": "true", "mappedType": "boolean"}, "created_at": {"name": "created_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "default": "now()", "mappedType": "datetime"}, "updated_at": {"name": "updated_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "default": "now()", "mappedType": "datetime"}, "deleted_at": {"name": "deleted_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 6, "mappedType": "datetime"}}, "name": "product_service_custom_field", "schema": "public", "indexes": [{"keyName": "IDX_product_service_custom_field_deleted_at", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_product_service_custom_field_deleted_at\" ON \"product_service_custom_field\" (deleted_at) WHERE deleted_at IS NULL"}, {"keyName": "IDX_product_service_custom_field_name", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE UNIQUE INDEX IF NOT EXISTS \"IDX_product_service_custom_field_name\" ON \"product_service_custom_field\" (name) WHERE deleted_at IS NULL"}, {"keyName": "IDX_product_service_custom_field_type", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_product_service_custom_field_type\" ON \"product_service_custom_field\" (field_type) WHERE deleted_at IS NULL"}, {"keyName": "IDX_product_service_custom_field_is_active", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_product_service_custom_field_is_active\" ON \"product_service_custom_field\" (is_active) WHERE deleted_at IS NULL"}, {"keyName": "product_service_custom_field_pkey", "columnNames": ["id"], "composite": false, "constraint": true, "primary": true, "unique": true}], "checks": [], "foreignKeys": {}, "nativeEnums": {}}, {"columns": {"id": {"name": "id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "name": {"name": "name", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "color": {"name": "color", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "text"}, "is_active": {"name": "is_active", "type": "boolean", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "default": "true", "mappedType": "boolean"}, "created_at": {"name": "created_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "default": "now()", "mappedType": "datetime"}, "updated_at": {"name": "updated_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "default": "now()", "mappedType": "datetime"}, "deleted_at": {"name": "deleted_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 6, "mappedType": "datetime"}}, "name": "product_service_tag", "schema": "public", "indexes": [{"keyName": "IDX_product_service_tag_deleted_at", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_product_service_tag_deleted_at\" ON \"product_service_tag\" (deleted_at) WHERE deleted_at IS NULL"}, {"keyName": "IDX_product_service_tag_name", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE UNIQUE INDEX IF NOT EXISTS \"IDX_product_service_tag_name\" ON \"product_service_tag\" (name) WHERE deleted_at IS NULL"}, {"keyName": "IDX_product_service_tag_is_active", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_product_service_tag_is_active\" ON \"product_service_tag\" (is_active) WHERE deleted_at IS NULL"}, {"keyName": "product_service_tag_pkey", "columnNames": ["id"], "composite": false, "constraint": true, "primary": true, "unique": true}], "checks": [], "foreignKeys": {}, "nativeEnums": {}}, {"columns": {"id": {"name": "id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "name": {"name": "name", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "description": {"name": "description", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "text"}, "is_active": {"name": "is_active", "type": "boolean", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "default": "true", "mappedType": "boolean"}, "created_at": {"name": "created_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "default": "now()", "mappedType": "datetime"}, "updated_at": {"name": "updated_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "default": "now()", "mappedType": "datetime"}, "deleted_at": {"name": "deleted_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 6, "mappedType": "datetime"}}, "name": "product_service_unit_type", "schema": "public", "indexes": [{"keyName": "IDX_product_service_unit_type_deleted_at", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_product_service_unit_type_deleted_at\" ON \"product_service_unit_type\" (deleted_at) WHERE deleted_at IS NULL"}, {"keyName": "IDX_product_service_unit_type_name", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE UNIQUE INDEX IF NOT EXISTS \"IDX_product_service_unit_type_name\" ON \"product_service_unit_type\" (name) WHERE deleted_at IS NULL"}, {"keyName": "IDX_product_service_unit_type_is_active", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_product_service_unit_type_is_active\" ON \"product_service_unit_type\" (is_active) WHERE deleted_at IS NULL"}, {"keyName": "product_service_unit_type_pkey", "columnNames": ["id"], "composite": false, "constraint": true, "primary": true, "unique": true}], "checks": [], "foreignKeys": {}, "nativeEnums": {}}, {"columns": {"id": {"name": "id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "name": {"name": "name", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "type": {"name": "type", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "description": {"name": "description", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "text"}, "base_cost": {"name": "base_cost", "type": "integer", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "integer"}, "highest_price": {"name": "highest_price", "type": "integer", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "integer"}, "highest_price_currency": {"name": "highest_price_currency", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "text"}, "price_flag_active": {"name": "price_flag_active", "type": "boolean", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "default": "false", "mappedType": "boolean"}, "price_flag_created_at": {"name": "price_flag_created_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 6, "mappedType": "datetime"}, "price_flag_supplier_offering_id": {"name": "price_flag_supplier_offering_id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "text"}, "custom_fields": {"name": "custom_fields", "type": "jsonb", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "json"}, "status": {"name": "status", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "default": "'active'", "mappedType": "text"}, "service_level": {"name": "service_level", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "default": "'hotel'", "mappedType": "text"}, "hotel_id": {"name": "hotel_id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "text"}, "destination_id": {"name": "destination_id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "text"}, "category_id": {"name": "category_id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "unit_type_id": {"name": "unit_type_id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "created_at": {"name": "created_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "default": "now()", "mappedType": "datetime"}, "updated_at": {"name": "updated_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "default": "now()", "mappedType": "datetime"}, "deleted_at": {"name": "deleted_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 6, "mappedType": "datetime"}}, "name": "product_service", "schema": "public", "indexes": [{"keyName": "IDX_product_service_category_id", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_product_service_category_id\" ON \"product_service\" (category_id) WHERE deleted_at IS NULL"}, {"keyName": "IDX_product_service_unit_type_id", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_product_service_unit_type_id\" ON \"product_service\" (unit_type_id) WHERE deleted_at IS NULL"}, {"keyName": "IDX_product_service_deleted_at", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_product_service_deleted_at\" ON \"product_service\" (deleted_at) WHERE deleted_at IS NULL"}, {"keyName": "IDX_product_service_name", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_product_service_name\" ON \"product_service\" (name) WHERE deleted_at IS NULL"}, {"keyName": "IDX_product_service_type", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_product_service_type\" ON \"product_service\" (type) WHERE deleted_at IS NULL"}, {"keyName": "IDX_product_service_status", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_product_service_status\" ON \"product_service\" (status) WHERE deleted_at IS NULL"}, {"keyName": "IDX_product_service_type_category", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_product_service_type_category\" ON \"product_service\" (type, category_id) WHERE deleted_at IS NULL"}, {"keyName": "IDX_product_service_status_type", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_product_service_status_type\" ON \"product_service\" (status, type) WHERE deleted_at IS NULL"}, {"keyName": "IDX_product_service_base_cost", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_product_service_base_cost\" ON \"product_service\" (base_cost) WHERE deleted_at IS NULL AND base_cost IS NOT NULL"}, {"keyName": "IDX_product_service_service_level", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_product_service_service_level\" ON \"product_service\" (service_level) WHERE deleted_at IS NULL"}, {"keyName": "IDX_product_service_destination_id", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_product_service_destination_id\" ON \"product_service\" (destination_id) WHERE deleted_at IS NULL AND destination_id IS NOT NULL"}, {"keyName": "IDX_product_service_hotel_id", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_product_service_hotel_id\" ON \"product_service\" (hotel_id) WHERE deleted_at IS NULL AND hotel_id IS NOT NULL"}, {"keyName": "IDX_product_service_service_level_destination", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_product_service_service_level_destination\" ON \"product_service\" (service_level, destination_id) WHERE deleted_at IS NULL"}, {"keyName": "IDX_product_service_service_level_hotel", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_product_service_service_level_hotel\" ON \"product_service\" (service_level, hotel_id) WHERE deleted_at IS NULL"}, {"keyName": "product_service_pkey", "columnNames": ["id"], "composite": false, "constraint": true, "primary": true, "unique": true}], "checks": [], "foreignKeys": {"product_service_category_id_foreign": {"constraintName": "product_service_category_id_foreign", "columnNames": ["category_id"], "localTableName": "public.product_service", "referencedColumnNames": ["id"], "referencedTableName": "public.product_service_category", "updateRule": "cascade"}, "product_service_unit_type_id_foreign": {"constraintName": "product_service_unit_type_id_foreign", "columnNames": ["unit_type_id"], "localTableName": "public.product_service", "referencedColumnNames": ["id"], "referencedTableName": "public.product_service_unit_type", "updateRule": "cascade"}}, "nativeEnums": {}}, {"columns": {"id": {"name": "id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "product_service_id": {"name": "product_service_id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "tag_id": {"name": "tag_id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "created_at": {"name": "created_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "default": "now()", "mappedType": "datetime"}, "updated_at": {"name": "updated_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "default": "now()", "mappedType": "datetime"}, "deleted_at": {"name": "deleted_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 6, "mappedType": "datetime"}}, "name": "product_service_tag_junction", "schema": "public", "indexes": [{"keyName": "IDX_product_service_tag_junction_product_service_id", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_product_service_tag_junction_product_service_id\" ON \"product_service_tag_junction\" (product_service_id) WHERE deleted_at IS NULL"}, {"keyName": "IDX_product_service_tag_junction_tag_id", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_product_service_tag_junction_tag_id\" ON \"product_service_tag_junction\" (tag_id) WHERE deleted_at IS NULL"}, {"keyName": "IDX_product_service_tag_junction_deleted_at", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_product_service_tag_junction_deleted_at\" ON \"product_service_tag_junction\" (deleted_at) WHERE deleted_at IS NULL"}, {"keyName": "IDX_product_service_tag_junction_ps_id", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_product_service_tag_junction_ps_id\" ON \"product_service_tag_junction\" (product_service_id) WHERE deleted_at IS NULL"}, {"keyName": "IDX_product_service_tag_junction_unique", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE UNIQUE INDEX IF NOT EXISTS \"IDX_product_service_tag_junction_unique\" ON \"product_service_tag_junction\" (product_service_id, tag_id) WHERE deleted_at IS NULL"}, {"keyName": "product_service_tag_junction_pkey", "columnNames": ["id"], "composite": false, "constraint": true, "primary": true, "unique": true}], "checks": [], "foreignKeys": {"product_service_tag_junction_product_service_id_foreign": {"constraintName": "product_service_tag_junction_product_service_id_foreign", "columnNames": ["product_service_id"], "localTableName": "public.product_service_tag_junction", "referencedColumnNames": ["id"], "referencedTableName": "public.product_service", "updateRule": "cascade"}, "product_service_tag_junction_tag_id_foreign": {"constraintName": "product_service_tag_junction_tag_id_foreign", "columnNames": ["tag_id"], "localTableName": "public.product_service_tag_junction", "referencedColumnNames": ["id"], "referencedTableName": "public.product_service_tag", "updateRule": "cascade"}}, "nativeEnums": {}}, {"columns": {"id": {"name": "id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "product_service_id": {"name": "product_service_id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "supplier_id": {"name": "supplier_id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "cost": {"name": "cost", "type": "integer", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "integer"}, "currency_code": {"name": "currency_code", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "default": "'CHF'", "mappedType": "text"}, "availability": {"name": "availability", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "max_capacity": {"name": "max_capacity", "type": "integer", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "integer"}, "season": {"name": "season", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "text"}, "valid_from": {"name": "valid_from", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 6, "mappedType": "datetime"}, "valid_until": {"name": "valid_until", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 6, "mappedType": "datetime"}, "notes": {"name": "notes", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "text"}, "lead_time_days": {"name": "lead_time_days", "type": "integer", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "integer"}, "minimum_order": {"name": "minimum_order", "type": "integer", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "integer"}, "is_active": {"name": "is_active", "type": "boolean", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "default": "true", "mappedType": "boolean"}, "is_preferred": {"name": "is_preferred", "type": "boolean", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "default": "false", "mappedType": "boolean"}, "created_by": {"name": "created_by", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "text"}, "updated_by": {"name": "updated_by", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "text"}, "created_at": {"name": "created_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "default": "now()", "mappedType": "datetime"}, "updated_at": {"name": "updated_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "default": "now()", "mappedType": "datetime"}, "deleted_at": {"name": "deleted_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 6, "mappedType": "datetime"}}, "name": "product_service_supplier", "schema": "public", "indexes": [{"keyName": "IDX_product_service_supplier_product_service_id", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_product_service_supplier_product_service_id\" ON \"product_service_supplier\" (product_service_id) WHERE deleted_at IS NULL"}, {"keyName": "IDX_product_service_supplier_deleted_at", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_product_service_supplier_deleted_at\" ON \"product_service_supplier\" (deleted_at) WHERE deleted_at IS NULL"}, {"keyName": "IDX_product_service_supplier_ps_id", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_product_service_supplier_ps_id\" ON \"product_service_supplier\" (product_service_id) WHERE deleted_at IS NULL"}, {"keyName": "IDX_product_service_supplier_supplier_id", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_product_service_supplier_supplier_id\" ON \"product_service_supplier\" (supplier_id) WHERE deleted_at IS NULL"}, {"keyName": "IDX_product_service_supplier_unique", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE UNIQUE INDEX IF NOT EXISTS \"IDX_product_service_supplier_unique\" ON \"product_service_supplier\" (product_service_id, supplier_id) WHERE deleted_at IS NULL"}, {"keyName": "IDX_product_service_supplier_is_active", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_product_service_supplier_is_active\" ON \"product_service_supplier\" (is_active) WHERE deleted_at IS NULL"}, {"keyName": "IDX_product_service_supplier_is_preferred", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_product_service_supplier_is_preferred\" ON \"product_service_supplier\" (is_preferred) WHERE deleted_at IS NULL"}, {"keyName": "IDX_product_service_supplier_season", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_product_service_supplier_season\" ON \"product_service_supplier\" (valid_from, valid_until) WHERE deleted_at IS NULL"}, {"keyName": "product_service_supplier_pkey", "columnNames": ["id"], "composite": false, "constraint": true, "primary": true, "unique": true}], "checks": [], "foreignKeys": {"product_service_supplier_product_service_id_foreign": {"constraintName": "product_service_supplier_product_service_id_foreign", "columnNames": ["product_service_id"], "localTableName": "public.product_service_supplier", "referencedColumnNames": ["id"], "referencedTableName": "public.product_service", "updateRule": "cascade"}}, "nativeEnums": {}}, {"columns": {"id": {"name": "id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "value": {"name": "value", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "text"}, "product_service_id": {"name": "product_service_id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "custom_field_id": {"name": "custom_field_id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "created_at": {"name": "created_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "default": "now()", "mappedType": "datetime"}, "updated_at": {"name": "updated_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "default": "now()", "mappedType": "datetime"}, "deleted_at": {"name": "deleted_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 6, "mappedType": "datetime"}}, "name": "product_service_custom_field_junction", "schema": "public", "indexes": [{"keyName": "IDX_product_service_custom_field_junction_product_service_id", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_product_service_custom_field_junction_product_service_id\" ON \"product_service_custom_field_junction\" (product_service_id) WHERE deleted_at IS NULL"}, {"keyName": "IDX_product_service_custom_field_junction_custom_field_id", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_product_service_custom_field_junction_custom_field_id\" ON \"product_service_custom_field_junction\" (custom_field_id) WHERE deleted_at IS NULL"}, {"keyName": "IDX_product_service_custom_field_junction_deleted_at", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_product_service_custom_field_junction_deleted_at\" ON \"product_service_custom_field_junction\" (deleted_at) WHERE deleted_at IS NULL"}, {"keyName": "IDX_product_service_custom_field_junction_ps_id", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_product_service_custom_field_junction_ps_id\" ON \"product_service_custom_field_junction\" (product_service_id) WHERE deleted_at IS NULL"}, {"keyName": "IDX_product_service_custom_field_junction_cf_id", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_product_service_custom_field_junction_cf_id\" ON \"product_service_custom_field_junction\" (custom_field_id) WHERE deleted_at IS NULL"}, {"keyName": "IDX_product_service_custom_field_junction_unique", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE UNIQUE INDEX IF NOT EXISTS \"IDX_product_service_custom_field_junction_unique\" ON \"product_service_custom_field_junction\" (product_service_id, custom_field_id) WHERE deleted_at IS NULL"}, {"keyName": "product_service_custom_field_junction_pkey", "columnNames": ["id"], "composite": false, "constraint": true, "primary": true, "unique": true}], "checks": [], "foreignKeys": {"product_service_custom_field_junction_product_se_3c156_foreign": {"constraintName": "product_service_custom_field_junction_product_se_3c156_foreign", "columnNames": ["product_service_id"], "localTableName": "public.product_service_custom_field_junction", "referencedColumnNames": ["id"], "referencedTableName": "public.product_service", "updateRule": "cascade"}, "product_service_custom_field_junction_custom_field_id_foreign": {"constraintName": "product_service_custom_field_junction_custom_field_id_foreign", "columnNames": ["custom_field_id"], "localTableName": "public.product_service_custom_field_junction", "referencedColumnNames": ["id"], "referencedTableName": "public.product_service_custom_field", "updateRule": "cascade"}}, "nativeEnums": {}}, {"columns": {"id": {"name": "id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "product_service_id": {"name": "product_service_id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "previous_cost": {"name": "previous_cost", "type": "integer", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "integer"}, "new_cost": {"name": "new_cost", "type": "integer", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "integer"}, "change_reason": {"name": "change_reason", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "text"}, "changed_by_user_id": {"name": "changed_by_user_id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "text"}, "created_at": {"name": "created_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "default": "now()", "mappedType": "datetime"}, "updated_at": {"name": "updated_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "default": "now()", "mappedType": "datetime"}, "deleted_at": {"name": "deleted_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 6, "mappedType": "datetime"}}, "name": "product_service_cost_history", "schema": "public", "indexes": [{"keyName": "IDX_product_service_cost_history_product_service_id", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_product_service_cost_history_product_service_id\" ON \"product_service_cost_history\" (product_service_id) WHERE deleted_at IS NULL"}, {"keyName": "IDX_product_service_cost_history_deleted_at", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_product_service_cost_history_deleted_at\" ON \"product_service_cost_history\" (deleted_at) WHERE deleted_at IS NULL"}, {"keyName": "IDX_product_service_cost_history_changed_by", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_product_service_cost_history_changed_by\" ON \"product_service_cost_history\" (changed_by_user_id) WHERE deleted_at IS NULL"}, {"keyName": "IDX_product_service_cost_history_created_at", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_product_service_cost_history_created_at\" ON \"product_service_cost_history\" (created_at) WHERE deleted_at IS NULL"}, {"keyName": "IDX_product_service_cost_history_cost_change", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_product_service_cost_history_cost_change\" ON \"product_service_cost_history\" (previous_cost, new_cost) WHERE deleted_at IS NULL"}, {"keyName": "IDX_product_service_cost_history_product_service_created", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_product_service_cost_history_product_service_created\" ON \"product_service_cost_history\" (product_service_id, created_at) WHERE deleted_at IS NULL"}, {"keyName": "product_service_cost_history_pkey", "columnNames": ["id"], "composite": false, "constraint": true, "primary": true, "unique": true}], "checks": [], "foreignKeys": {"product_service_cost_history_product_service_id_foreign": {"constraintName": "product_service_cost_history_product_service_id_foreign", "columnNames": ["product_service_id"], "localTableName": "public.product_service_cost_history", "referencedColumnNames": ["id"], "referencedTableName": "public.product_service", "updateRule": "cascade"}}, "nativeEnums": {}}, {"columns": {"id": {"name": "id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "product_service_id": {"name": "product_service_id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "supplier_id": {"name": "supplier_id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "active_from": {"name": "active_from", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 6, "mappedType": "datetime"}, "active_to": {"name": "active_to", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 6, "mappedType": "datetime"}, "availability_notes": {"name": "availability_notes", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "text"}, "cost": {"name": "cost", "type": "integer", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "integer"}, "currency": {"name": "currency", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "text"}, "currency_override": {"name": "currency_override", "type": "boolean", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "default": "false", "mappedType": "boolean"}, "status": {"name": "status", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "default": "'active'", "enumItems": ["active", "inactive"], "mappedType": "enum"}, "custom_fields": {"name": "custom_fields", "type": "jsonb", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "json"}, "created_by": {"name": "created_by", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "text"}, "updated_by": {"name": "updated_by", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "text"}, "created_at": {"name": "created_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "default": "now()", "mappedType": "datetime"}, "updated_at": {"name": "updated_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "default": "now()", "mappedType": "datetime"}, "deleted_at": {"name": "deleted_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 6, "mappedType": "datetime"}}, "name": "supplier_offering", "schema": "public", "indexes": [{"keyName": "IDX_supplier_offering_product_service_id", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_supplier_offering_product_service_id\" ON \"supplier_offering\" (product_service_id) WHERE deleted_at IS NULL"}, {"keyName": "IDX_supplier_offering_deleted_at", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_supplier_offering_deleted_at\" ON \"supplier_offering\" (deleted_at) WHERE deleted_at IS NULL"}, {"keyName": "IDX_supplier_offering_supplier_id", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_supplier_offering_supplier_id\" ON \"supplier_offering\" (supplier_id) WHERE deleted_at IS NULL"}, {"keyName": "IDX_supplier_offering_supplier_product", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_supplier_offering_supplier_product\" ON \"supplier_offering\" (supplier_id, product_service_id) WHERE deleted_at IS NULL"}, {"keyName": "IDX_supplier_offering_status", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_supplier_offering_status\" ON \"supplier_offering\" (status) WHERE deleted_at IS NULL"}, {"keyName": "IDX_supplier_offering_validity", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_supplier_offering_validity\" ON \"supplier_offering\" (active_from, active_to) WHERE deleted_at IS NULL"}, {"keyName": "IDX_supplier_offering_created_by", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_supplier_offering_created_by\" ON \"supplier_offering\" (created_by) WHERE deleted_at IS NULL"}, {"keyName": "IDX_supplier_offering_dates", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_supplier_offering_dates\" ON \"supplier_offering\" (active_from, active_to) WHERE deleted_at IS NULL"}, {"keyName": "IDX_supplier_offering_custom_fields", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_supplier_offering_custom_fields\" ON \"supplier_offering\" (custom_fields) WHERE deleted_at IS NULL"}, {"keyName": "IDX_supplier_offering_uniqueness_check", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_supplier_offering_uniqueness_check\" ON \"supplier_offering\" (supplier_id, product_service_id, active_from, active_to) WHERE deleted_at IS NULL"}, {"keyName": "supplier_offering_pkey", "columnNames": ["id"], "composite": false, "constraint": true, "primary": true, "unique": true}], "checks": [], "foreignKeys": {"supplier_offering_product_service_id_foreign": {"constraintName": "supplier_offering_product_service_id_foreign", "columnNames": ["product_service_id"], "localTableName": "public.supplier_offering", "referencedColumnNames": ["id"], "referencedTableName": "public.product_service", "updateRule": "cascade"}}, "nativeEnums": {}}, {"columns": {"id": {"name": "id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "supplier_offering_id": {"name": "supplier_offering_id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "previous_cost": {"name": "previous_cost", "type": "integer", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "integer"}, "new_cost": {"name": "new_cost", "type": "integer", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "integer"}, "previous_currency": {"name": "previous_currency", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "text"}, "new_currency": {"name": "new_currency", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "text"}, "change_reason": {"name": "change_reason", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "text"}, "changed_by_user_id": {"name": "changed_by_user_id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "text"}, "created_at": {"name": "created_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "default": "now()", "mappedType": "datetime"}, "updated_at": {"name": "updated_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "default": "now()", "mappedType": "datetime"}, "deleted_at": {"name": "deleted_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 6, "mappedType": "datetime"}}, "name": "supplier_offering_cost_history", "schema": "public", "indexes": [{"keyName": "IDX_supplier_offering_cost_history_supplier_offering_id", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_supplier_offering_cost_history_supplier_offering_id\" ON \"supplier_offering_cost_history\" (supplier_offering_id) WHERE deleted_at IS NULL"}, {"keyName": "IDX_supplier_offering_cost_history_deleted_at", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_supplier_offering_cost_history_deleted_at\" ON \"supplier_offering_cost_history\" (deleted_at) WHERE deleted_at IS NULL"}, {"keyName": "IDX_supplier_offering_cost_history_offering_id", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_supplier_offering_cost_history_offering_id\" ON \"supplier_offering_cost_history\" (supplier_offering_id) WHERE deleted_at IS NULL"}, {"keyName": "IDX_supplier_offering_cost_history_changed_by", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_supplier_offering_cost_history_changed_by\" ON \"supplier_offering_cost_history\" (changed_by_user_id) WHERE deleted_at IS NULL"}, {"keyName": "IDX_supplier_offering_cost_history_created_at", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_supplier_offering_cost_history_created_at\" ON \"supplier_offering_cost_history\" (created_at) WHERE deleted_at IS NULL"}, {"keyName": "IDX_supplier_offering_cost_history_cost_change", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_supplier_offering_cost_history_cost_change\" ON \"supplier_offering_cost_history\" (previous_cost, new_cost) WHERE deleted_at IS NULL"}, {"keyName": "IDX_supplier_offering_cost_history_currency_change", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_supplier_offering_cost_history_currency_change\" ON \"supplier_offering_cost_history\" (previous_currency, new_currency) WHERE deleted_at IS NULL"}, {"keyName": "IDX_supplier_offering_cost_history_offering_created", "columnNames": [], "composite": false, "constraint": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_supplier_offering_cost_history_offering_created\" ON \"supplier_offering_cost_history\" (supplier_offering_id, created_at) WHERE deleted_at IS NULL"}, {"keyName": "supplier_offering_cost_history_pkey", "columnNames": ["id"], "composite": false, "constraint": true, "primary": true, "unique": true}], "checks": [], "foreignKeys": {"supplier_offering_cost_history_supplier_offering_id_foreign": {"constraintName": "supplier_offering_cost_history_supplier_offering_id_foreign", "columnNames": ["supplier_offering_id"], "localTableName": "public.supplier_offering_cost_history", "referencedColumnNames": ["id"], "referencedTableName": "public.supplier_offering", "updateRule": "cascade"}}, "nativeEnums": {}}], "nativeEnums": {}}