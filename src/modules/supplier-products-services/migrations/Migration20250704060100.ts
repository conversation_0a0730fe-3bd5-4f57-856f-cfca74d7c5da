import { Migration } from '@mikro-orm/migrations';

export class Migration20250704060100 extends Migration {

  override async up(): Promise<void> {
    // Add missing raw_cost column to supplier_offering table
    // This is required for bigNumber fields in Medusa/MikroORM
    this.addSql(`
      ALTER TABLE "supplier_offering" 
      ADD COLUMN IF NOT EXISTS "raw_cost" numeric NULL;
    `);

    // Create index for raw_cost column for better performance on raw cost-based queries
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_supplier_offering_raw_cost" ON "supplier_offering" ("raw_cost") WHERE "deleted_at" IS NULL;`);
  }

  override async down(): Promise<void> {
    // Drop index first
    this.addSql(`DROP INDEX IF EXISTS "IDX_supplier_offering_raw_cost";`);
    
    // Drop the raw_cost column
    this.addSql(`
      ALTER TABLE "supplier_offering" 
      DROP COLUMN IF EXISTS "raw_cost";
    `);
  }
}
