import { Migration } from "@mikro-orm/migrations";

export class Migration20250119000000 extends Migration {
  override async up(): Promise<void> {
    // 1. Rename public_price to gross_price for better clarity
    this.addSql(`
      ALTER TABLE "supplier_offering" 
      RENAME COLUMN "public_price" TO "gross_price";
    `);

    // 2. Remove net_price column (redundant - supplier_price IS the net cost)
    this.addSql(`
      ALTER TABLE "supplier_offering" 
      DROP COLUMN IF EXISTS "net_price";
    `);

    // 3. Remove custom_prices column (feature removed)
    this.addSql(`
      ALTER TABLE "supplier_offering" 
      DROP COLUMN IF EXISTS "custom_prices";
    `);

    // 4. Update indexes - drop old public_price index
    this.addSql(`
      DROP INDEX IF EXISTS "IDX_supplier_offering_public_price";
    `);

    // 5. Create new gross_price index
    this.addSql(`
      CREATE INDEX IF NOT EXISTS "IDX_supplier_offering_gross_price" 
      ON "supplier_offering"("gross_price") 
      WHERE "deleted_at" IS NULL;
    `);

    // 6. Update constraints - remove old public_price constraint
    this.addSql(`
      ALTER TABLE "supplier_offering" 
      DROP CONSTRAINT IF EXISTS "CHK_supplier_offering_public_price_positive";
    `);

    // 7. Add new gross_price constraint (if not exists)
    this.addSql(`
      DO $$
      BEGIN
        IF NOT EXISTS (
          SELECT 1 FROM information_schema.table_constraints
          WHERE constraint_name = 'CHK_supplier_offering_gross_price_positive'
          AND table_name = 'supplier_offering'
        ) THEN
          ALTER TABLE "supplier_offering"
          ADD CONSTRAINT "CHK_supplier_offering_gross_price_positive"
          CHECK ("gross_price" IS NULL OR "gross_price" >= 0);
        END IF;
      END $$;
    `);

    // 8. Add selling currency fields if they don't exist
    this.addSql(`
      ALTER TABLE "supplier_offering" 
      ADD COLUMN IF NOT EXISTS "selling_currency" VARCHAR(3),
      ADD COLUMN IF NOT EXISTS "selling_price_selling_currency" DECIMAL(10,2),
      ADD COLUMN IF NOT EXISTS "exchange_rate" DECIMAL(10,6),
      ADD COLUMN IF NOT EXISTS "exchange_rate_date" TIMESTAMPTZ;
    `);

    // 9. Add indexes for selling currency fields
    this.addSql(`
      CREATE INDEX IF NOT EXISTS "IDX_supplier_offering_selling_currency" 
      ON "supplier_offering"("selling_currency") 
      WHERE "deleted_at" IS NULL;
    `);

    this.addSql(`
      CREATE INDEX IF NOT EXISTS "IDX_supplier_offering_exchange_rate_date" 
      ON "supplier_offering"("exchange_rate_date") 
      WHERE "deleted_at" IS NULL;
    `);

    // 10. Add constraint for exchange rate (if not exists)
    this.addSql(`
      DO $$
      BEGIN
        IF NOT EXISTS (
          SELECT 1 FROM information_schema.table_constraints
          WHERE constraint_name = 'CHK_supplier_offering_exchange_rate_positive'
          AND table_name = 'supplier_offering'
        ) THEN
          ALTER TABLE "supplier_offering"
          ADD CONSTRAINT "CHK_supplier_offering_exchange_rate_positive"
          CHECK ("exchange_rate" IS NULL OR "exchange_rate" > 0);
        END IF;
      END $$;
    `);

    // 11. Update column comments to reflect new structure
    this.addSql(`
      COMMENT ON COLUMN "supplier_offering"."commission" IS 'Commission percentage as decimal (e.g., 0.1 for 10%)';
    `);

    this.addSql(`
      COMMENT ON COLUMN "supplier_offering"."gross_price" IS 'Original gross price (renamed from public_price)';
    `);

    this.addSql(`
      COMMENT ON COLUMN "supplier_offering"."supplier_price" IS 'Net cost to supplier: Gross Price - (Gross Price × Commission)';
    `);

    this.addSql(`
      COMMENT ON COLUMN "supplier_offering"."margin_rate" IS 'Margin rate percentage for selling price calculation (e.g., 0.15 for 15%)';
    `);

    this.addSql(`
      COMMENT ON COLUMN "supplier_offering"."selling_price" IS 'Final selling price: supplier_price ÷ (1 - margin_rate)';
    `);

    this.addSql(`
      COMMENT ON COLUMN "supplier_offering"."selling_currency" IS 'Currency for selling price (can be different from cost currency)';
    `);

    this.addSql(`
      COMMENT ON COLUMN "supplier_offering"."exchange_rate" IS 'Exchange rate from cost currency to selling currency';
    `);
  }

  override async down(): Promise<void> {
    // Reverse all changes

    // 1. Rename gross_price back to public_price
    this.addSql(`
      ALTER TABLE "supplier_offering" 
      RENAME COLUMN "gross_price" TO "public_price";
    `);

    // 2. Add back net_price column
    this.addSql(`
      ALTER TABLE "supplier_offering" 
      ADD COLUMN IF NOT EXISTS "net_price" DECIMAL(10,2) NULL;
    `);

    // 3. Add back custom_prices column
    this.addSql(`
      ALTER TABLE "supplier_offering" 
      ADD COLUMN IF NOT EXISTS "custom_prices" JSONB NULL;
    `);

    // 4. Restore old indexes and constraints
    this.addSql(`
      DROP INDEX IF EXISTS "IDX_supplier_offering_gross_price";
    `);

    this.addSql(`
      CREATE INDEX IF NOT EXISTS "IDX_supplier_offering_public_price" 
      ON "supplier_offering"("public_price") 
      WHERE "deleted_at" IS NULL;
    `);

    this.addSql(`
      ALTER TABLE "supplier_offering" 
      DROP CONSTRAINT IF EXISTS "CHK_supplier_offering_gross_price_positive";
    `);

    this.addSql(`
      ALTER TABLE "supplier_offering" 
      ADD CONSTRAINT "CHK_supplier_offering_public_price_positive" 
      CHECK ("public_price" IS NULL OR "public_price" >= 0);
    `);

    // 5. Remove selling currency fields
    this.addSql(`
      ALTER TABLE "supplier_offering" 
      DROP COLUMN IF EXISTS "selling_currency",
      DROP COLUMN IF EXISTS "selling_price_selling_currency",
      DROP COLUMN IF EXISTS "exchange_rate",
      DROP COLUMN IF EXISTS "exchange_rate_date";
    `);

    // 6. Drop selling currency indexes and constraints
    this.addSql(`
      DROP INDEX IF EXISTS "IDX_supplier_offering_selling_currency";
    `);

    this.addSql(`
      DROP INDEX IF EXISTS "IDX_supplier_offering_exchange_rate_date";
    `);

    this.addSql(`
      ALTER TABLE "supplier_offering" 
      DROP CONSTRAINT IF EXISTS "CHK_supplier_offering_exchange_rate_positive";
    `);
  }
}
