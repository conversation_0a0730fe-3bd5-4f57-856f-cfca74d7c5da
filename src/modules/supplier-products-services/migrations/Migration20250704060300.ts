import { Migration } from '@mikro-orm/migrations';

export class Migration20250704060300 extends Migration {

  override async up(): Promise<void> {
    // Fix the raw_cost column type - it should be jsonb for bigNumber fields in Medusa
    this.addSql(`
      ALTER TABLE "supplier_offering" 
      ALTER COLUMN "raw_cost" TYPE jsonb USING "raw_cost"::text::jsonb;
    `);
  }

  override async down(): Promise<void> {
    // Revert raw_cost column back to numeric
    this.addSql(`
      ALTER TABLE "supplier_offering" 
      ALTER COLUMN "raw_cost" TYPE numeric USING ("raw_cost"->>'value')::numeric;
    `);
  }
}
