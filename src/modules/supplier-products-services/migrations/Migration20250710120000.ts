import { Migration } from '@mikro-orm/migrations';

export class Migration20250710120000 extends Migration {

  override async up(): Promise<void> {
    // Add service_level and destination_id columns to product_service table
    this.addSql(`
      ALTER TABLE "product_service" 
      ADD COLUMN IF NOT EXISTS "service_level" text NOT NULL DEFAULT 'general',
      ADD COLUMN IF NOT EXISTS "destination_id" text NULL;
    `);

    // Add indexes for the new columns
    this.addSql(`
      CREATE INDEX IF NOT EXISTS "IDX_product_service_service_level" 
      ON "product_service" (service_level) 
      WHERE deleted_at IS NULL;
    `);

    this.addSql(`
      CREATE INDEX IF NOT EXISTS "IDX_product_service_destination_id" 
      ON "product_service" (destination_id) 
      WHERE deleted_at IS NULL AND destination_id IS NOT NULL;
    `);

    this.addSql(`
      CREATE INDEX IF NOT EXISTS "IDX_product_service_service_level_destination" 
      ON "product_service" (service_level, destination_id) 
      WHERE deleted_at IS NULL;
    `);
  }

  override async down(): Promise<void> {
    // Drop indexes first
    this.addSql(`DROP INDEX IF EXISTS "IDX_product_service_service_level_destination";`);
    this.addSql(`DROP INDEX IF EXISTS "IDX_product_service_destination_id";`);
    this.addSql(`DROP INDEX IF EXISTS "IDX_product_service_service_level";`);

    // Drop columns
    this.addSql(`
      ALTER TABLE "product_service" 
      DROP COLUMN IF EXISTS "destination_id",
      DROP COLUMN IF EXISTS "service_level";
    `);
  }
}
