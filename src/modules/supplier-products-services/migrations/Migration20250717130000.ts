import { Migration } from "@mikro-orm/migrations";

export class Migration20250717130000 extends Migration {
  override async up(): Promise<void> {
    // Add selling currency and related fields to supplier_offering table
    
    // Add selling_currency field
    this.addSql(`
      ALTER TABLE "supplier_offering" 
      ADD COLUMN IF NOT EXISTS "selling_currency" VARCHAR(3) NULL;
    `);

    // Add selling_price_selling_currency field
    this.addSql(`
      ALTER TABLE "supplier_offering" 
      ADD COLUMN IF NOT EXISTS "selling_price_selling_currency" DECIMAL(10,2) NULL;
    `);

    // Add exchange_rate field
    this.addSql(`
      ALTER TABLE "supplier_offering" 
      ADD COLUMN IF NOT EXISTS "exchange_rate" DECIMAL(10,6) NULL;
    `);

    // Add exchange_rate_date field
    this.addSql(`
      ALTER TABLE "supplier_offering" 
      ADD COLUMN IF NOT EXISTS "exchange_rate_date" TIMESTAMPTZ NULL;
    `);

    // Set default selling_currency to match currency for existing records
    this.addSql(`
      UPDATE "supplier_offering" 
      SET "selling_currency" = "currency"
      WHERE "selling_currency" IS NULL AND "currency" IS NOT NULL;
    `);

    // Set default exchange_rate to 1.0 for same currency pairs
    this.addSql(`
      UPDATE "supplier_offering" 
      SET "exchange_rate" = 1.0,
          "exchange_rate_date" = NOW()
      WHERE "selling_currency" = "currency" 
        AND "exchange_rate" IS NULL;
    `);

    // Set selling_price_selling_currency equal to selling_price for same currency
    this.addSql(`
      UPDATE "supplier_offering" 
      SET "selling_price_selling_currency" = "selling_price"
      WHERE "selling_currency" = "currency" 
        AND "selling_price" IS NOT NULL 
        AND "selling_price_selling_currency" IS NULL;
    `);

    // Add indexes for performance
    this.addSql(`
      CREATE INDEX IF NOT EXISTS "IDX_supplier_offering_selling_currency" 
      ON "supplier_offering"("selling_currency") 
      WHERE "deleted_at" IS NULL;
    `);

    this.addSql(`
      CREATE INDEX IF NOT EXISTS "IDX_supplier_offering_selling_price_selling_currency" 
      ON "supplier_offering"("selling_price_selling_currency") 
      WHERE "deleted_at" IS NULL;
    `);

    this.addSql(`
      CREATE INDEX IF NOT EXISTS "IDX_supplier_offering_exchange_rate_date" 
      ON "supplier_offering"("exchange_rate_date") 
      WHERE "deleted_at" IS NULL;
    `);

    // Add constraints for data validation
    this.addSql(`
      ALTER TABLE "supplier_offering" 
      ADD CONSTRAINT "CHK_supplier_offering_selling_currency_format" 
      CHECK ("selling_currency" IS NULL OR "selling_currency" ~ '^[A-Z]{3}$');
    `);

    this.addSql(`
      ALTER TABLE "supplier_offering" 
      ADD CONSTRAINT "CHK_supplier_offering_selling_price_selling_currency_positive" 
      CHECK ("selling_price_selling_currency" IS NULL OR "selling_price_selling_currency" >= 0);
    `);

    this.addSql(`
      ALTER TABLE "supplier_offering" 
      ADD CONSTRAINT "CHK_supplier_offering_exchange_rate_positive" 
      CHECK ("exchange_rate" IS NULL OR "exchange_rate" > 0);
    `);
  }

  override async down(): Promise<void> {
    // Remove constraints
    this.addSql(`
      ALTER TABLE "supplier_offering" DROP CONSTRAINT IF EXISTS "CHK_supplier_offering_selling_currency_format";
    `);
    this.addSql(`
      ALTER TABLE "supplier_offering" DROP CONSTRAINT IF EXISTS "CHK_supplier_offering_selling_price_selling_currency_positive";
    `);
    this.addSql(`
      ALTER TABLE "supplier_offering" DROP CONSTRAINT IF EXISTS "CHK_supplier_offering_exchange_rate_positive";
    `);

    // Remove indexes
    this.addSql(`DROP INDEX IF EXISTS "IDX_supplier_offering_selling_currency";`);
    this.addSql(`DROP INDEX IF EXISTS "IDX_supplier_offering_selling_price_selling_currency";`);
    this.addSql(`DROP INDEX IF EXISTS "IDX_supplier_offering_exchange_rate_date";`);

    // Remove columns
    this.addSql(`ALTER TABLE "supplier_offering" DROP COLUMN IF EXISTS "exchange_rate_date";`);
    this.addSql(`ALTER TABLE "supplier_offering" DROP COLUMN IF EXISTS "exchange_rate";`);
    this.addSql(`ALTER TABLE "supplier_offering" DROP COLUMN IF EXISTS "selling_price_selling_currency";`);
    this.addSql(`ALTER TABLE "supplier_offering" DROP COLUMN IF EXISTS "selling_currency";`);
  }
}
