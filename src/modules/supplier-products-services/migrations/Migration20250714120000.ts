import { Migration } from "@mikro-orm/migrations";

export class Migration20250714120000 extends Migration {
  override async up(): Promise<void> {
    // Create product_service_cost_history table
    this.addSql(`
      CREATE TABLE IF NOT EXISTS product_service_cost_history (
        id text NOT NULL,
        product_service_id text NOT NULL,
        previous_cost integer NULL,
        new_cost integer NULL,
        change_reason text NULL,
        changed_by_user_id text NULL,
        created_at timestamptz NOT NULL DEFAULT now(),
        updated_at timestamptz NOT NULL DEFAULT now(),
        deleted_at timestamptz NULL,
        CONSTRAINT product_service_cost_history_pkey PRIMARY KEY (id),
        CONSTRAINT product_service_cost_history_product_service_id_fkey
          FOREIGN KEY (product_service_id) REFERENCES product_service (id) ON DELETE CASCADE
      );
    `);

    // Create indexes for better performance
    this.addSql(`
      CREATE INDEX IF NOT EXISTS "IDX_product_service_cost_history_product_service_id"
      ON product_service_cost_history (product_service_id)
      WHERE deleted_at IS NULL;
    `);

    this.addSql(`
      CREATE INDEX IF NOT EXISTS "IDX_product_service_cost_history_changed_by"
      ON product_service_cost_history (changed_by_user_id)
      WHERE deleted_at IS NULL;
    `);

    this.addSql(`
      CREATE INDEX IF NOT EXISTS "IDX_product_service_cost_history_created_at"
      ON product_service_cost_history (created_at)
      WHERE deleted_at IS NULL;
    `);

    this.addSql(`
      CREATE INDEX IF NOT EXISTS "IDX_product_service_cost_history_cost_change"
      ON product_service_cost_history (previous_cost, new_cost)
      WHERE deleted_at IS NULL;
    `);

    this.addSql(`
      CREATE INDEX IF NOT EXISTS "IDX_product_service_cost_history_product_service_created"
      ON product_service_cost_history (product_service_id, created_at)
      WHERE deleted_at IS NULL;
    `);
  }

  override async down(): Promise<void> {
    // Drop indexes first
    this.addSql(
      `DROP INDEX IF EXISTS "IDX_product_service_cost_history_product_service_created";`
    );
    this.addSql(
      `DROP INDEX IF EXISTS "IDX_product_service_cost_history_cost_change";`
    );
    this.addSql(
      `DROP INDEX IF EXISTS "IDX_product_service_cost_history_created_at";`
    );
    this.addSql(
      `DROP INDEX IF EXISTS "IDX_product_service_cost_history_changed_by";`
    );
    this.addSql(
      `DROP INDEX IF EXISTS "IDX_product_service_cost_history_product_service_id";`
    );

    // Drop the table
    this.addSql(`DROP TABLE IF EXISTS product_service_cost_history CASCADE;`);
  }
}
