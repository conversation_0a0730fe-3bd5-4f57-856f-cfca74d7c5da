import { Migration } from '@mikro-orm/migrations';

export class Migration20250703090000 extends Migration {

  override async up(): Promise<void> {
    // Create supplier_offering table if it doesn't exist
    this.addSql(`
      create table if not exists "supplier_offering" (
        "id" text not null,
        "product_service_id" text not null,
        "supplier_id" text not null,
        "active_from" timestamptz null,
        "active_to" timestamptz null,
        "availability_notes" text null,
        "status" text not null default 'active',
        "custom_fields" jsonb null,
        "created_by" text null,
        "updated_by" text null,
        "created_at" timestamptz not null default now(),
        "updated_at" timestamptz not null default now(),
        "deleted_at" timestamptz null,
        constraint "supplier_offering_pkey" primary key ("id")
      );
    `);

    // Create indexes for better performance
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_supplier_offering_product_service_id" ON "supplier_offering" ("product_service_id") WHERE "deleted_at" IS NULL;`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_supplier_offering_supplier_id" ON "supplier_offering" ("supplier_id") WHERE "deleted_at" IS NULL;`);
    this.addSql(`CREATE UNIQUE INDEX IF NOT EXISTS "IDX_supplier_offering_unique" ON "supplier_offering" ("product_service_id", "supplier_id") WHERE "deleted_at" IS NULL;`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_supplier_offering_status" ON "supplier_offering" ("status") WHERE "deleted_at" IS NULL;`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_supplier_offering_validity" ON "supplier_offering" ("active_from", "active_to") WHERE "deleted_at" IS NULL;`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_supplier_offering_created_by" ON "supplier_offering" ("created_by") WHERE "deleted_at" IS NULL;`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_supplier_offering_deleted_at" ON "supplier_offering" ("deleted_at") WHERE "deleted_at" IS NULL;`);

    // Add foreign key constraint to product_service table
    this.addSql(`
      DO $$
      BEGIN
        -- Add supplier_offering foreign key constraint to product_service
        IF NOT EXISTS (
          SELECT 1 FROM information_schema.table_constraints
          WHERE constraint_name = 'supplier_offering_product_service_id_foreign'
          AND table_name = 'supplier_offering'
        ) THEN
          ALTER TABLE supplier_offering ADD CONSTRAINT "supplier_offering_product_service_id_foreign"
          FOREIGN KEY ("product_service_id") REFERENCES "product_service" ("id") ON UPDATE CASCADE ON DELETE CASCADE;
        END IF;
      END
      $$;
    `);
  }

  override async down(): Promise<void> {
    // Drop foreign key constraint first
    this.addSql(`alter table if exists "supplier_offering" drop constraint if exists "supplier_offering_product_service_id_foreign";`);
    
    // Drop the table
    this.addSql(`drop table if exists "supplier_offering" cascade;`);
  }
}
