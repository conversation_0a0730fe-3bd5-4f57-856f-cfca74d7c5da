import { Migration } from '@mikro-orm/migrations';

export class Migration20250704090000 extends Migration {

  override async up(): Promise<void> {
    // Remove the overly restrictive unique constraint that only considers supplier_id + product_service_id
    // This constraint prevents multiple offerings for the same supplier/product with different configurations
    this.addSql(`DROP INDEX IF EXISTS "IDX_supplier_offering_unique";`);
    
    // The uniqueness validation is now handled at the application level in the service layer
    // which considers supplier_id + product_service_id + mandatory_dynamic_fields + active_from + active_to
    // This allows multiple offerings for the same supplier/product with different configurations or time periods
  }

  override async down(): Promise<void> {
    // Re-create the unique constraint if rolling back
    // Note: This may fail if there are existing records that would violate the constraint
    this.addSql(`
      CREATE UNIQUE INDEX IF NOT EXISTS "IDX_supplier_offering_unique" 
      ON "supplier_offering" ("product_service_id", "supplier_id") 
      WHERE "deleted_at" IS NULL;
    `);
  }
}
