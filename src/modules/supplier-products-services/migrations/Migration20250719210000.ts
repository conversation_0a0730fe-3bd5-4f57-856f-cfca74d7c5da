import { Migration } from "@mikro-orm/migrations";

export class Migration20250719210000 extends Migration {
  async up(): Promise<void> {
    // Add add_ons column to supplier_offering table
    this.addSql(
      'ALTER TABLE "supplier_offering" ADD COLUMN "add_ons" JSONB NULL;'
    );

    // Add index for add_ons queries (optional but recommended for performance)
    this.addSql(
      'CREATE INDEX "IDX_supplier_offering_add_ons" ON "supplier_offering" USING GIN ("add_ons") WHERE "deleted_at" IS NULL AND "add_ons" IS NOT NULL;'
    );
  }

  async down(): Promise<void> {
    // Remove the index first
    this.addSql('DROP INDEX IF EXISTS "IDX_supplier_offering_add_ons";');

    // Remove the add_ons column
    this.addSql('ALTER TABLE "supplier_offering" DROP COLUMN "add_ons";');
  }
}
