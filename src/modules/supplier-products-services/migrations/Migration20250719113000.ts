import { Migration } from '@mikro-orm/migrations';

export class Migration20250719113000 extends Migration {

  async up(): Promise<void> {
    // Rename supplier_price column to net_cost in supplier_offering table
    this.addSql('ALTER TABLE "supplier_offering" RENAME COLUMN "supplier_price" TO "net_cost";');
  }

  async down(): Promise<void> {
    // Revert: rename net_cost column back to supplier_price
    this.addSql('ALTER TABLE "supplier_offering" RENAME COLUMN "net_cost" TO "supplier_price";');
  }

}
