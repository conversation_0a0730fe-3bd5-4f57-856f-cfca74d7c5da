import { Migration } from '@mikro-orm/migrations';

export class Migration20250714140944 extends Migration {

  override async up(): Promise<void> {
    this.addSql(`alter table if exists "product_service_custom_field_junction" drop constraint if exists "product_service_custom_field_junction_unique";`);
    this.addSql(`alter table if exists "product_service_supplier" drop constraint if exists "product_service_supplier_unique";`);
    this.addSql(`alter table if exists "product_service_tag_junction" drop constraint if exists "product_service_tag_junction_unique";`);
    this.addSql(`create table if not exists "product_service_category" ("id" text not null, "name" text not null, "description" text null, "category_type" text check ("category_type" in ('Product', 'Service', 'Both')) not null default 'Both', "icon" text null, "dynamic_field_schema" jsonb null, "is_active" boolean not null default true, "created_at" timestamptz not null default now(), "updated_at" timestamptz not null default now(), "deleted_at" timestamptz null, constraint "product_service_category_pkey" primary key ("id"));`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_product_service_category_deleted_at" ON "product_service_category" (deleted_at) WHERE deleted_at IS NULL;`);
    this.addSql(`CREATE UNIQUE INDEX IF NOT EXISTS "IDX_product_service_category_name" ON "product_service_category" (name) WHERE deleted_at IS NULL;`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_product_service_category_is_active" ON "product_service_category" (is_active) WHERE deleted_at IS NULL;`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_product_service_category_type" ON "product_service_category" (category_type) WHERE deleted_at IS NULL;`);

    this.addSql(`create table if not exists "product_service_custom_field" ("id" text not null, "name" text not null, "field_type" text not null, "options" jsonb null, "is_required" boolean not null default false, "default_value" text null, "validation_rules" jsonb null, "is_active" boolean not null default true, "created_at" timestamptz not null default now(), "updated_at" timestamptz not null default now(), "deleted_at" timestamptz null, constraint "product_service_custom_field_pkey" primary key ("id"));`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_product_service_custom_field_deleted_at" ON "product_service_custom_field" (deleted_at) WHERE deleted_at IS NULL;`);
    this.addSql(`CREATE UNIQUE INDEX IF NOT EXISTS "IDX_product_service_custom_field_name" ON "product_service_custom_field" (name) WHERE deleted_at IS NULL;`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_product_service_custom_field_type" ON "product_service_custom_field" (field_type) WHERE deleted_at IS NULL;`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_product_service_custom_field_is_active" ON "product_service_custom_field" (is_active) WHERE deleted_at IS NULL;`);

    this.addSql(`create table if not exists "product_service_tag" ("id" text not null, "name" text not null, "color" text null, "is_active" boolean not null default true, "created_at" timestamptz not null default now(), "updated_at" timestamptz not null default now(), "deleted_at" timestamptz null, constraint "product_service_tag_pkey" primary key ("id"));`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_product_service_tag_deleted_at" ON "product_service_tag" (deleted_at) WHERE deleted_at IS NULL;`);
    this.addSql(`CREATE UNIQUE INDEX IF NOT EXISTS "IDX_product_service_tag_name" ON "product_service_tag" (name) WHERE deleted_at IS NULL;`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_product_service_tag_is_active" ON "product_service_tag" (is_active) WHERE deleted_at IS NULL;`);

    this.addSql(`create table if not exists "product_service_unit_type" ("id" text not null, "name" text not null, "description" text null, "is_active" boolean not null default true, "created_at" timestamptz not null default now(), "updated_at" timestamptz not null default now(), "deleted_at" timestamptz null, constraint "product_service_unit_type_pkey" primary key ("id"));`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_product_service_unit_type_deleted_at" ON "product_service_unit_type" (deleted_at) WHERE deleted_at IS NULL;`);
    this.addSql(`CREATE UNIQUE INDEX IF NOT EXISTS "IDX_product_service_unit_type_name" ON "product_service_unit_type" (name) WHERE deleted_at IS NULL;`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_product_service_unit_type_is_active" ON "product_service_unit_type" (is_active) WHERE deleted_at IS NULL;`);

    this.addSql(`create table if not exists "product_service" ("id" text not null, "name" text not null, "type" text not null, "description" text null, "base_cost" integer null, "highest_price" integer null, "highest_price_currency" text null, "price_flag_active" boolean not null default false, "price_flag_created_at" timestamptz null, "price_flag_supplier_offering_id" text null, "custom_fields" jsonb null, "status" text not null default 'active', "service_level" text not null default 'hotel', "hotel_id" text null, "destination_id" text null, "category_id" text not null, "unit_type_id" text not null, "created_at" timestamptz not null default now(), "updated_at" timestamptz not null default now(), "deleted_at" timestamptz null, constraint "product_service_pkey" primary key ("id"));`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_product_service_category_id" ON "product_service" (category_id) WHERE deleted_at IS NULL;`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_product_service_unit_type_id" ON "product_service" (unit_type_id) WHERE deleted_at IS NULL;`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_product_service_deleted_at" ON "product_service" (deleted_at) WHERE deleted_at IS NULL;`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_product_service_name" ON "product_service" (name) WHERE deleted_at IS NULL;`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_product_service_type" ON "product_service" (type) WHERE deleted_at IS NULL;`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_product_service_status" ON "product_service" (status) WHERE deleted_at IS NULL;`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_product_service_type_category" ON "product_service" (type, category_id) WHERE deleted_at IS NULL;`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_product_service_status_type" ON "product_service" (status, type) WHERE deleted_at IS NULL;`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_product_service_base_cost" ON "product_service" (base_cost) WHERE deleted_at IS NULL AND base_cost IS NOT NULL;`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_product_service_service_level" ON "product_service" (service_level) WHERE deleted_at IS NULL;`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_product_service_destination_id" ON "product_service" (destination_id) WHERE deleted_at IS NULL AND destination_id IS NOT NULL;`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_product_service_hotel_id" ON "product_service" (hotel_id) WHERE deleted_at IS NULL AND hotel_id IS NOT NULL;`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_product_service_service_level_destination" ON "product_service" (service_level, destination_id) WHERE deleted_at IS NULL;`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_product_service_service_level_hotel" ON "product_service" (service_level, hotel_id) WHERE deleted_at IS NULL;`);

    this.addSql(`create table if not exists "product_service_tag_junction" ("id" text not null, "product_service_id" text not null, "tag_id" text not null, "created_at" timestamptz not null default now(), "updated_at" timestamptz not null default now(), "deleted_at" timestamptz null, constraint "product_service_tag_junction_pkey" primary key ("id"));`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_product_service_tag_junction_product_service_id" ON "product_service_tag_junction" (product_service_id) WHERE deleted_at IS NULL;`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_product_service_tag_junction_tag_id" ON "product_service_tag_junction" (tag_id) WHERE deleted_at IS NULL;`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_product_service_tag_junction_deleted_at" ON "product_service_tag_junction" (deleted_at) WHERE deleted_at IS NULL;`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_product_service_tag_junction_ps_id" ON "product_service_tag_junction" (product_service_id) WHERE deleted_at IS NULL;`);
    this.addSql(`CREATE UNIQUE INDEX IF NOT EXISTS "IDX_product_service_tag_junction_unique" ON "product_service_tag_junction" (product_service_id, tag_id) WHERE deleted_at IS NULL;`);

    this.addSql(`create table if not exists "product_service_supplier" ("id" text not null, "product_service_id" text not null, "supplier_id" text not null, "cost" integer not null, "currency_code" text not null default 'CHF', "availability" text not null, "max_capacity" integer null, "season" text null, "valid_from" timestamptz null, "valid_until" timestamptz null, "notes" text null, "lead_time_days" integer null, "minimum_order" integer null, "is_active" boolean not null default true, "is_preferred" boolean not null default false, "created_by" text null, "updated_by" text null, "created_at" timestamptz not null default now(), "updated_at" timestamptz not null default now(), "deleted_at" timestamptz null, constraint "product_service_supplier_pkey" primary key ("id"));`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_product_service_supplier_product_service_id" ON "product_service_supplier" (product_service_id) WHERE deleted_at IS NULL;`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_product_service_supplier_deleted_at" ON "product_service_supplier" (deleted_at) WHERE deleted_at IS NULL;`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_product_service_supplier_ps_id" ON "product_service_supplier" (product_service_id) WHERE deleted_at IS NULL;`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_product_service_supplier_supplier_id" ON "product_service_supplier" (supplier_id) WHERE deleted_at IS NULL;`);
    this.addSql(`CREATE UNIQUE INDEX IF NOT EXISTS "IDX_product_service_supplier_unique" ON "product_service_supplier" (product_service_id, supplier_id) WHERE deleted_at IS NULL;`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_product_service_supplier_is_active" ON "product_service_supplier" (is_active) WHERE deleted_at IS NULL;`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_product_service_supplier_is_preferred" ON "product_service_supplier" (is_preferred) WHERE deleted_at IS NULL;`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_product_service_supplier_season" ON "product_service_supplier" (valid_from, valid_until) WHERE deleted_at IS NULL;`);

    this.addSql(`create table if not exists "product_service_custom_field_junction" ("id" text not null, "value" text null, "product_service_id" text not null, "custom_field_id" text not null, "created_at" timestamptz not null default now(), "updated_at" timestamptz not null default now(), "deleted_at" timestamptz null, constraint "product_service_custom_field_junction_pkey" primary key ("id"));`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_product_service_custom_field_junction_product_service_id" ON "product_service_custom_field_junction" (product_service_id) WHERE deleted_at IS NULL;`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_product_service_custom_field_junction_custom_field_id" ON "product_service_custom_field_junction" (custom_field_id) WHERE deleted_at IS NULL;`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_product_service_custom_field_junction_deleted_at" ON "product_service_custom_field_junction" (deleted_at) WHERE deleted_at IS NULL;`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_product_service_custom_field_junction_ps_id" ON "product_service_custom_field_junction" (product_service_id) WHERE deleted_at IS NULL;`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_product_service_custom_field_junction_cf_id" ON "product_service_custom_field_junction" (custom_field_id) WHERE deleted_at IS NULL;`);
    this.addSql(`CREATE UNIQUE INDEX IF NOT EXISTS "IDX_product_service_custom_field_junction_unique" ON "product_service_custom_field_junction" (product_service_id, custom_field_id) WHERE deleted_at IS NULL;`);

    this.addSql(`create table if not exists "product_service_cost_history" ("id" text not null, "product_service_id" text not null, "previous_cost" integer null, "new_cost" integer null, "change_reason" text null, "changed_by_user_id" text null, "created_at" timestamptz not null default now(), "updated_at" timestamptz not null default now(), "deleted_at" timestamptz null, constraint "product_service_cost_history_pkey" primary key ("id"));`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_product_service_cost_history_product_service_id" ON "product_service_cost_history" (product_service_id) WHERE deleted_at IS NULL;`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_product_service_cost_history_deleted_at" ON "product_service_cost_history" (deleted_at) WHERE deleted_at IS NULL;`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_product_service_cost_history_changed_by" ON "product_service_cost_history" (changed_by_user_id) WHERE deleted_at IS NULL;`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_product_service_cost_history_created_at" ON "product_service_cost_history" (created_at) WHERE deleted_at IS NULL;`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_product_service_cost_history_cost_change" ON "product_service_cost_history" (previous_cost, new_cost) WHERE deleted_at IS NULL;`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_product_service_cost_history_product_service_created" ON "product_service_cost_history" (product_service_id, created_at) WHERE deleted_at IS NULL;`);

    this.addSql(`create table if not exists "supplier_offering" ("id" text not null, "product_service_id" text not null, "supplier_id" text not null, "active_from" timestamptz null, "active_to" timestamptz null, "availability_notes" text null, "cost" integer null, "currency" text null, "currency_override" boolean not null default false, "status" text check ("status" in ('active', 'inactive')) not null default 'active', "custom_fields" jsonb null, "created_by" text null, "updated_by" text null, "created_at" timestamptz not null default now(), "updated_at" timestamptz not null default now(), "deleted_at" timestamptz null, constraint "supplier_offering_pkey" primary key ("id"));`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_supplier_offering_product_service_id" ON "supplier_offering" (product_service_id) WHERE deleted_at IS NULL;`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_supplier_offering_deleted_at" ON "supplier_offering" (deleted_at) WHERE deleted_at IS NULL;`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_supplier_offering_supplier_id" ON "supplier_offering" (supplier_id) WHERE deleted_at IS NULL;`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_supplier_offering_supplier_product" ON "supplier_offering" (supplier_id, product_service_id) WHERE deleted_at IS NULL;`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_supplier_offering_status" ON "supplier_offering" (status) WHERE deleted_at IS NULL;`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_supplier_offering_validity" ON "supplier_offering" (active_from, active_to) WHERE deleted_at IS NULL;`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_supplier_offering_created_by" ON "supplier_offering" (created_by) WHERE deleted_at IS NULL;`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_supplier_offering_dates" ON "supplier_offering" (active_from, active_to) WHERE deleted_at IS NULL;`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_supplier_offering_custom_fields" ON "supplier_offering" (custom_fields) WHERE deleted_at IS NULL;`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_supplier_offering_uniqueness_check" ON "supplier_offering" (supplier_id, product_service_id, active_from, active_to) WHERE deleted_at IS NULL;`);

    this.addSql(`create table if not exists "supplier_offering_cost_history" ("id" text not null, "supplier_offering_id" text not null, "previous_cost" integer null, "new_cost" integer null, "previous_currency" text null, "new_currency" text null, "change_reason" text null, "changed_by_user_id" text null, "created_at" timestamptz not null default now(), "updated_at" timestamptz not null default now(), "deleted_at" timestamptz null, constraint "supplier_offering_cost_history_pkey" primary key ("id"));`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_supplier_offering_cost_history_supplier_offering_id" ON "supplier_offering_cost_history" (supplier_offering_id) WHERE deleted_at IS NULL;`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_supplier_offering_cost_history_deleted_at" ON "supplier_offering_cost_history" (deleted_at) WHERE deleted_at IS NULL;`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_supplier_offering_cost_history_offering_id" ON "supplier_offering_cost_history" (supplier_offering_id) WHERE deleted_at IS NULL;`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_supplier_offering_cost_history_changed_by" ON "supplier_offering_cost_history" (changed_by_user_id) WHERE deleted_at IS NULL;`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_supplier_offering_cost_history_created_at" ON "supplier_offering_cost_history" (created_at) WHERE deleted_at IS NULL;`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_supplier_offering_cost_history_cost_change" ON "supplier_offering_cost_history" (previous_cost, new_cost) WHERE deleted_at IS NULL;`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_supplier_offering_cost_history_currency_change" ON "supplier_offering_cost_history" (previous_currency, new_currency) WHERE deleted_at IS NULL;`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_supplier_offering_cost_history_offering_created" ON "supplier_offering_cost_history" (supplier_offering_id, created_at) WHERE deleted_at IS NULL;`);

    this.addSql(`alter table if exists "product_service" add constraint "product_service_category_id_foreign" foreign key ("category_id") references "product_service_category" ("id") on update cascade;`);
    this.addSql(`alter table if exists "product_service" add constraint "product_service_unit_type_id_foreign" foreign key ("unit_type_id") references "product_service_unit_type" ("id") on update cascade;`);

    this.addSql(`alter table if exists "product_service_tag_junction" add constraint "product_service_tag_junction_product_service_id_foreign" foreign key ("product_service_id") references "product_service" ("id") on update cascade;`);
    this.addSql(`alter table if exists "product_service_tag_junction" add constraint "product_service_tag_junction_tag_id_foreign" foreign key ("tag_id") references "product_service_tag" ("id") on update cascade;`);

    this.addSql(`alter table if exists "product_service_supplier" add constraint "product_service_supplier_product_service_id_foreign" foreign key ("product_service_id") references "product_service" ("id") on update cascade;`);

    this.addSql(`alter table if exists "product_service_custom_field_junction" add constraint "product_service_custom_field_junction_product_se_3c156_foreign" foreign key ("product_service_id") references "product_service" ("id") on update cascade;`);
    this.addSql(`alter table if exists "product_service_custom_field_junction" add constraint "product_service_custom_field_junction_custom_field_id_foreign" foreign key ("custom_field_id") references "product_service_custom_field" ("id") on update cascade;`);

    this.addSql(`alter table if exists "product_service_cost_history" add constraint "product_service_cost_history_product_service_id_foreign" foreign key ("product_service_id") references "product_service" ("id") on update cascade;`);

    this.addSql(`alter table if exists "supplier_offering" add constraint "supplier_offering_product_service_id_foreign" foreign key ("product_service_id") references "product_service" ("id") on update cascade;`);

    this.addSql(`alter table if exists "supplier_offering_cost_history" add constraint "supplier_offering_cost_history_supplier_offering_id_foreign" foreign key ("supplier_offering_id") references "supplier_offering" ("id") on update cascade;`);
  }

  override async down(): Promise<void> {
    this.addSql(`alter table if exists "product_service" drop constraint if exists "product_service_category_id_foreign";`);

    this.addSql(`alter table if exists "product_service_custom_field_junction" drop constraint if exists "product_service_custom_field_junction_custom_field_id_foreign";`);

    this.addSql(`alter table if exists "product_service_tag_junction" drop constraint if exists "product_service_tag_junction_tag_id_foreign";`);

    this.addSql(`alter table if exists "product_service" drop constraint if exists "product_service_unit_type_id_foreign";`);

    this.addSql(`alter table if exists "product_service_tag_junction" drop constraint if exists "product_service_tag_junction_product_service_id_foreign";`);

    this.addSql(`alter table if exists "product_service_supplier" drop constraint if exists "product_service_supplier_product_service_id_foreign";`);

    this.addSql(`alter table if exists "product_service_custom_field_junction" drop constraint if exists "product_service_custom_field_junction_product_se_3c156_foreign";`);

    this.addSql(`alter table if exists "product_service_cost_history" drop constraint if exists "product_service_cost_history_product_service_id_foreign";`);

    this.addSql(`alter table if exists "supplier_offering" drop constraint if exists "supplier_offering_product_service_id_foreign";`);

    this.addSql(`alter table if exists "supplier_offering_cost_history" drop constraint if exists "supplier_offering_cost_history_supplier_offering_id_foreign";`);

    this.addSql(`drop table if exists "product_service_category" cascade;`);

    this.addSql(`drop table if exists "product_service_custom_field" cascade;`);

    this.addSql(`drop table if exists "product_service_tag" cascade;`);

    this.addSql(`drop table if exists "product_service_unit_type" cascade;`);

    this.addSql(`drop table if exists "product_service" cascade;`);

    this.addSql(`drop table if exists "product_service_tag_junction" cascade;`);

    this.addSql(`drop table if exists "product_service_supplier" cascade;`);

    this.addSql(`drop table if exists "product_service_custom_field_junction" cascade;`);

    this.addSql(`drop table if exists "product_service_cost_history" cascade;`);

    this.addSql(`drop table if exists "supplier_offering" cascade;`);

    this.addSql(`drop table if exists "supplier_offering_cost_history" cascade;`);
  }

}
