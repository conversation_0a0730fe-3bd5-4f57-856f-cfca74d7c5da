import { Migration } from "@mikro-orm/migrations";

export class Migration20250714152000 extends Migration {
  override async up(): Promise<void> {
    // Add field_context column to product_service_custom_field table
    this.addSql(`
      ALTER TABLE "product_service_custom_field" 
      ADD COLUMN IF NOT EXISTS "field_context" text 
      CHECK ("field_context" IN ('supplier', 'customer')) 
      DEFAULT 'supplier';
    `);

    // Create index for the new field_context column
    this.addSql(`
      CREATE INDEX IF NOT EXISTS "IDX_product_service_custom_field_context" 
      ON "product_service_custom_field" ("field_context") 
      WHERE "deleted_at" IS NULL;
    `);

    // Update existing records to have 'supplier' context (they are all supplier fields)
    this.addSql(`
      UPDATE "product_service_custom_field" 
      SET "field_context" = 'supplier' 
      WHERE "field_context" IS NULL;
    `);
  }

  override async down(): Promise<void> {
    // Drop the index first
    this.addSql(`DROP INDEX IF EXISTS "IDX_product_service_custom_field_context";`);
    
    // Drop the field_context column
    this.addSql(`ALTER TABLE "product_service_custom_field" DROP COLUMN IF EXISTS "field_context";`);
  }
}
