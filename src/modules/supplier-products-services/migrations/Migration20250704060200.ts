import { Migration } from '@mikro-orm/migrations';

export class Migration20250704060200 extends Migration {

  override async up(): Promise<void> {
    // Add all missing columns to supplier_offering table
    // These columns were missing from the original table creation
    this.addSql(`
      ALTER TABLE "supplier_offering" 
      ADD COLUMN IF NOT EXISTS "cost" numeric NULL,
      ADD COLUMN IF NOT EXISTS "raw_cost" numeric NULL,
      ADD COLUMN IF NOT EXISTS "currency" text NULL,
      ADD COLUMN IF NOT EXISTS "currency_override" boolean NOT NULL DEFAULT false;
    `);

    // Create indexes for better performance on cost and currency-based queries
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_supplier_offering_cost" ON "supplier_offering" ("cost") WHERE "deleted_at" IS NULL;`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_supplier_offering_raw_cost" ON "supplier_offering" ("raw_cost") WHERE "deleted_at" IS NULL;`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_supplier_offering_currency" ON "supplier_offering" ("currency") WHERE "deleted_at" IS NULL;`);
  }

  override async down(): Promise<void> {
    // Drop indexes first
    this.addSql(`DROP INDEX IF EXISTS "IDX_supplier_offering_cost";`);
    this.addSql(`DROP INDEX IF EXISTS "IDX_supplier_offering_raw_cost";`);
    this.addSql(`DROP INDEX IF EXISTS "IDX_supplier_offering_currency";`);
    
    // Drop the columns
    this.addSql(`
      ALTER TABLE "supplier_offering" 
      DROP COLUMN IF EXISTS "cost",
      DROP COLUMN IF EXISTS "raw_cost",
      DROP COLUMN IF EXISTS "currency",
      DROP COLUMN IF EXISTS "currency_override";
    `);
  }
}
