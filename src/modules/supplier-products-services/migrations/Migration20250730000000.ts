import { Migration } from '@mikro-orm/migrations';

export class Migration20250730000000 extends Migration {

  override async up(): Promise<void> {
    // Add product_id and product_variant_id columns to product_service table
    this.addSql(`
      ALTER TABLE "product_service" 
      ADD COLUMN IF NOT EXISTS "product_id" text NULL,
      ADD COLUMN IF NOT EXISTS "product_variant_id" text NULL;
    `);

    // Add indexes for the new columns
    this.addSql(`
      CREATE INDEX IF NOT EXISTS "IDX_product_service_product_id" 
      ON "product_service" (product_id) 
      WHERE deleted_at IS NULL AND product_id IS NOT NULL;
    `);

    this.addSql(`
      CREATE INDEX IF NOT EXISTS "IDX_product_service_product_variant_id" 
      ON "product_service" (product_variant_id) 
      WHERE deleted_at IS NULL AND product_variant_id IS NOT NULL;
    `);

    // Add composite index for both fields
    this.addSql(`
      CREATE INDEX IF NOT EXISTS "IDX_product_service_product_variant_sync" 
      ON "product_service" (product_id, product_variant_id) 
      WHERE deleted_at IS NULL AND product_id IS NOT NULL AND product_variant_id IS NOT NULL;
    `);

    // Add foreign key constraints
    // Note: We don't add foreign key constraints to product and product_variant tables 
    // since they are in the Medusa core module and may not be available during migration
    // The relationships will be validated at the application level
  }

  override async down(): Promise<void> {
    // Drop indexes first
    this.addSql(`DROP INDEX IF EXISTS "IDX_product_service_product_variant_sync";`);
    this.addSql(`DROP INDEX IF EXISTS "IDX_product_service_product_variant_id";`);
    this.addSql(`DROP INDEX IF EXISTS "IDX_product_service_product_id";`);

    // Drop columns
    this.addSql(`
      ALTER TABLE "product_service" 
      DROP COLUMN IF EXISTS "product_variant_id",
      DROP COLUMN IF EXISTS "product_id";
    `);
  }
}
