import { Migration } from '@mikro-orm/migrations';

export class Migration20250703071500 extends Migration {

  override async up(): Promise<void> {
    // Drop and recreate base_cost column with correct type to match other Medusa models
    this.addSql(`
      DO $$
      BEGIN
        -- Drop the base_cost column if it exists
        IF EXISTS (
          SELECT 1 FROM information_schema.columns
          WHERE table_name = 'product_service' AND column_name = 'base_cost'
        ) THEN
          ALTER TABLE product_service DROP COLUMN base_cost;
        END IF;
        
        -- Add base_cost column with correct numeric type (same as other Medusa models)
        ALTER TABLE product_service ADD COLUMN base_cost numeric null;
      END
      $$;
    `);
  }

  override async down(): Promise<void> {
    // Revert by dropping the column
    this.addSql(`
      DO $$
      BEGIN
        IF EXISTS (
          SELECT 1 FROM information_schema.columns
          WHERE table_name = 'product_service' AND column_name = 'base_cost'
        ) THEN
          ALTER TABLE product_service DROP COLUMN base_cost;
        END IF;
      END
      $$;
    `);
  }
}
