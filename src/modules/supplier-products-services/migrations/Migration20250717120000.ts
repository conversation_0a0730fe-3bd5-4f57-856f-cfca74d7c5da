import { Migration } from "@mikro-orm/migrations";

export class Migration20250717120000 extends Migration {
  override async up(): Promise<void> {
    // Add enhanced pricing fields to supplier_offering table
    
    // Add commission field (percentage as decimal, e.g., 0.1 for 10%)
    this.addSql(`
      ALTER TABLE "supplier_offering" 
      ADD COLUMN IF NOT EXISTS "commission" DECIMAL(5,4) NULL;
    `);

    // Add public_price field
    this.addSql(`
      ALTER TABLE "supplier_offering" 
      ADD COLUMN IF NOT EXISTS "public_price" DECIMAL(10,2) NULL;
    `);

    // Add supplier_price field (calculated: commission * public_price)
    this.addSql(`
      ALTER TABLE "supplier_offering" 
      ADD COLUMN IF NOT EXISTS "supplier_price" DECIMAL(10,2) NULL;
    `);

    // Add net_price field (calculated: supplier_price + sum of custom prices)
    this.addSql(`
      ALTER TABLE "supplier_offering" 
      ADD COLUMN IF NOT EXISTS "net_price" DECIMAL(10,2) NULL;
    `);

    // Add margin_rate field (percentage as decimal, e.g., 0.1 for 10%)
    this.addSql(`
      ALTER TABLE "supplier_offering" 
      ADD COLUMN IF NOT EXISTS "margin_rate" DECIMAL(5,4) NULL;
    `);

    // Add selling_price field (calculated: net_price / (1 - margin_rate))
    this.addSql(`
      ALTER TABLE "supplier_offering" 
      ADD COLUMN IF NOT EXISTS "selling_price" DECIMAL(10,2) NULL;
    `);

    // Add custom_prices field (JSON array of {name: string, price: number})
    this.addSql(`
      ALTER TABLE "supplier_offering" 
      ADD COLUMN IF NOT EXISTS "custom_prices" JSONB NULL;
    `);

    // Data migration: migrate existing cost data to public_price
    this.addSql(`
      UPDATE "supplier_offering" 
      SET 
        "public_price" = "cost",
        "commission" = 1.0,
        "supplier_price" = "cost"
      WHERE "cost" IS NOT NULL AND "public_price" IS NULL;
    `);

    // Set default margin rate for existing records
    this.addSql(`
      UPDATE "supplier_offering" 
      SET "margin_rate" = 0.1
      WHERE "margin_rate" IS NULL;
    `);

    // Calculate net_price for existing records (same as supplier_price initially)
    this.addSql(`
      UPDATE "supplier_offering" 
      SET "net_price" = "supplier_price"
      WHERE "supplier_price" IS NOT NULL AND "net_price" IS NULL;
    `);

    // Calculate selling_price for existing records
    this.addSql(`
      UPDATE "supplier_offering" 
      SET "selling_price" = "net_price" / (1 - "margin_rate")
      WHERE "net_price" IS NOT NULL 
        AND "margin_rate" IS NOT NULL 
        AND "margin_rate" < 1 
        AND "selling_price" IS NULL;
    `);

    // Add indexes for performance
    this.addSql(`
      CREATE INDEX IF NOT EXISTS "IDX_supplier_offering_commission" 
      ON "supplier_offering"("commission") 
      WHERE "deleted_at" IS NULL;
    `);

    this.addSql(`
      CREATE INDEX IF NOT EXISTS "IDX_supplier_offering_public_price" 
      ON "supplier_offering"("public_price") 
      WHERE "deleted_at" IS NULL;
    `);

    this.addSql(`
      CREATE INDEX IF NOT EXISTS "IDX_supplier_offering_margin_rate" 
      ON "supplier_offering"("margin_rate") 
      WHERE "deleted_at" IS NULL;
    `);

    this.addSql(`
      CREATE INDEX IF NOT EXISTS "IDX_supplier_offering_selling_price" 
      ON "supplier_offering"("selling_price") 
      WHERE "deleted_at" IS NULL;
    `);

    // Add constraints for data validation
    this.addSql(`
      ALTER TABLE "supplier_offering" 
      ADD CONSTRAINT "CHK_supplier_offering_commission_range" 
      CHECK ("commission" IS NULL OR ("commission" >= 0 AND "commission" <= 1));
    `);

    this.addSql(`
      ALTER TABLE "supplier_offering" 
      ADD CONSTRAINT "CHK_supplier_offering_margin_rate_range" 
      CHECK ("margin_rate" IS NULL OR ("margin_rate" >= 0 AND "margin_rate" < 1));
    `);

    this.addSql(`
      ALTER TABLE "supplier_offering" 
      ADD CONSTRAINT "CHK_supplier_offering_public_price_positive" 
      CHECK ("public_price" IS NULL OR "public_price" >= 0);
    `);

    this.addSql(`
      ALTER TABLE "supplier_offering" 
      ADD CONSTRAINT "CHK_supplier_offering_supplier_price_positive" 
      CHECK ("supplier_price" IS NULL OR "supplier_price" >= 0);
    `);

    this.addSql(`
      ALTER TABLE "supplier_offering" 
      ADD CONSTRAINT "CHK_supplier_offering_net_price_positive" 
      CHECK ("net_price" IS NULL OR "net_price" >= 0);
    `);

    this.addSql(`
      ALTER TABLE "supplier_offering" 
      ADD CONSTRAINT "CHK_supplier_offering_selling_price_positive" 
      CHECK ("selling_price" IS NULL OR "selling_price" >= 0);
    `);
  }

  override async down(): Promise<void> {
    // Remove constraints
    this.addSql(`
      ALTER TABLE "supplier_offering" DROP CONSTRAINT IF EXISTS "CHK_supplier_offering_commission_range";
    `);
    this.addSql(`
      ALTER TABLE "supplier_offering" DROP CONSTRAINT IF EXISTS "CHK_supplier_offering_margin_rate_range";
    `);
    this.addSql(`
      ALTER TABLE "supplier_offering" DROP CONSTRAINT IF EXISTS "CHK_supplier_offering_public_price_positive";
    `);
    this.addSql(`
      ALTER TABLE "supplier_offering" DROP CONSTRAINT IF EXISTS "CHK_supplier_offering_supplier_price_positive";
    `);
    this.addSql(`
      ALTER TABLE "supplier_offering" DROP CONSTRAINT IF EXISTS "CHK_supplier_offering_net_price_positive";
    `);
    this.addSql(`
      ALTER TABLE "supplier_offering" DROP CONSTRAINT IF EXISTS "CHK_supplier_offering_selling_price_positive";
    `);

    // Remove indexes
    this.addSql(`DROP INDEX IF EXISTS "IDX_supplier_offering_commission";`);
    this.addSql(`DROP INDEX IF EXISTS "IDX_supplier_offering_public_price";`);
    this.addSql(`DROP INDEX IF EXISTS "IDX_supplier_offering_margin_rate";`);
    this.addSql(`DROP INDEX IF EXISTS "IDX_supplier_offering_selling_price";`);

    // Remove columns (in reverse order)
    this.addSql(`ALTER TABLE "supplier_offering" DROP COLUMN IF EXISTS "custom_prices";`);
    this.addSql(`ALTER TABLE "supplier_offering" DROP COLUMN IF EXISTS "selling_price";`);
    this.addSql(`ALTER TABLE "supplier_offering" DROP COLUMN IF EXISTS "margin_rate";`);
    this.addSql(`ALTER TABLE "supplier_offering" DROP COLUMN IF EXISTS "net_price";`);
    this.addSql(`ALTER TABLE "supplier_offering" DROP COLUMN IF EXISTS "supplier_price";`);
    this.addSql(`ALTER TABLE "supplier_offering" DROP COLUMN IF EXISTS "public_price";`);
    this.addSql(`ALTER TABLE "supplier_offering" DROP COLUMN IF EXISTS "commission";`);
  }
}
