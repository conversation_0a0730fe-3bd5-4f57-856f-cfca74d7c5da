import { Migration } from "@mikro-orm/migrations";

export class Migration20250713120000 extends Migration {
  override async up(): Promise<void> {
    // Create supplier_offering_cost_history table
    this.addSql(`
      CREATE TABLE IF NOT EXISTS supplier_offering_cost_history (
        id varchar(255) NOT NULL,
        supplier_offering_id varchar(255) NOT NULL,
        previous_cost numeric NULL,
        new_cost numeric NULL,
        previous_currency varchar(3) NULL,
        new_currency varchar(3) NULL,
        change_reason text NULL,
        changed_by_user_id varchar(255) NULL,
        created_at timestamptz NOT NULL DEFAULT now(),
        updated_at timestamptz NOT NULL DEFAULT now(),
        deleted_at timestamptz NULL,
        CONSTRAINT supplier_offering_cost_history_pkey PRIMARY KEY (id),
        CONSTRAINT supplier_offering_cost_history_supplier_offering_id_fkey 
          FOREIGN KEY (supplier_offering_id) REFERENCES supplier_offering(id) ON DELETE CASCADE
      );
    `);

    // Add indexes for performance
    this.addSql(`
      CREATE INDEX IF NOT EXISTS "IDX_supplier_offering_cost_history_offering_id" 
      ON supplier_offering_cost_history (supplier_offering_id) 
      WHERE deleted_at IS NULL;
    `);

    this.addSql(`
      CREATE INDEX IF NOT EXISTS "IDX_supplier_offering_cost_history_changed_by" 
      ON supplier_offering_cost_history (changed_by_user_id) 
      WHERE deleted_at IS NULL;
    `);

    this.addSql(`
      CREATE INDEX IF NOT EXISTS "IDX_supplier_offering_cost_history_created_at" 
      ON supplier_offering_cost_history (created_at) 
      WHERE deleted_at IS NULL;
    `);

    this.addSql(`
      CREATE INDEX IF NOT EXISTS "IDX_supplier_offering_cost_history_cost_change" 
      ON supplier_offering_cost_history (previous_cost, new_cost) 
      WHERE deleted_at IS NULL;
    `);

    this.addSql(`
      CREATE INDEX IF NOT EXISTS "IDX_supplier_offering_cost_history_currency_change" 
      ON supplier_offering_cost_history (previous_currency, new_currency) 
      WHERE deleted_at IS NULL;
    `);

    this.addSql(`
      CREATE INDEX IF NOT EXISTS "IDX_supplier_offering_cost_history_offering_created" 
      ON supplier_offering_cost_history (supplier_offering_id, created_at) 
      WHERE deleted_at IS NULL;
    `);

    // Add constraints to ensure currency is a valid 3-letter code when provided
    this.addSql(`
      ALTER TABLE supplier_offering_cost_history
      ADD CONSTRAINT supplier_offering_cost_history_previous_currency_format_check
      CHECK (previous_currency IS NULL OR (previous_currency ~ '^[A-Z]{3}$'));
    `);

    this.addSql(`
      ALTER TABLE supplier_offering_cost_history
      ADD CONSTRAINT supplier_offering_cost_history_new_currency_format_check
      CHECK (new_currency IS NULL OR (new_currency ~ '^[A-Z]{3}$'));
    `);

    // Add constraints to ensure cost is non-negative when provided
    this.addSql(`
      ALTER TABLE supplier_offering_cost_history
      ADD CONSTRAINT supplier_offering_cost_history_previous_cost_positive_check
      CHECK (previous_cost IS NULL OR previous_cost >= 0);
    `);

    this.addSql(`
      ALTER TABLE supplier_offering_cost_history
      ADD CONSTRAINT supplier_offering_cost_history_new_cost_positive_check
      CHECK (new_cost IS NULL OR new_cost >= 0);
    `);
  }

  override async down(): Promise<void> {
    // Drop indexes first
    this.addSql(
      `DROP INDEX IF EXISTS "IDX_supplier_offering_cost_history_offering_created";`
    );
    this.addSql(
      `DROP INDEX IF EXISTS "IDX_supplier_offering_cost_history_currency_change";`
    );
    this.addSql(
      `DROP INDEX IF EXISTS "IDX_supplier_offering_cost_history_cost_change";`
    );
    this.addSql(
      `DROP INDEX IF EXISTS "IDX_supplier_offering_cost_history_created_at";`
    );
    this.addSql(
      `DROP INDEX IF EXISTS "IDX_supplier_offering_cost_history_changed_by";`
    );
    this.addSql(
      `DROP INDEX IF EXISTS "IDX_supplier_offering_cost_history_offering_id";`
    );

    // Drop table
    this.addSql(`DROP TABLE IF EXISTS supplier_offering_cost_history;`);
  }
}
