import { NextFunction } from "express";
import {
  AuthenticatedMedusaRequest,
  MedusaRequest,
  MedusaResponse,
} from "@camped-ai/framework/http";
import { Modules } from "@camped-ai/framework/utils";
import { isUserActive } from "../../admin/utils/userStatus";

/**
 * Middleware to check user status during authentication flows
 * This prevents deactivated users from accessing the system even after login
 */
export const checkUserStatusOnAuth = async (
  req: MedusaRequest,
  res: MedusaResponse,
  next: NextFunction
) => {
  try {
    const authReq = req as AuthenticatedMedusaRequest;

    // Only check if user is authenticated
    if (!authReq.auth_context?.actor_id) {
      return next();
    }

    // Get user service
    const userService = req.scope.resolve(Modules.USER);
    
    // Get user data
    const user = await userService.retrieveUser(authReq.auth_context.actor_id);
    
    if (!user) {
      return next();
    }

    // Check if user is active
    if (!isUserActive(user)) {
      console.log(`🔒 Blocking access for deactivated user: ${user.email}`);
      
      return res.status(403).json({
        type: "account_deactivated",
        message: "Your account has been deactivated. Please contact an administrator for assistance.",
      });
    }

    // User is active, continue
    next();
  } catch (error) {
    console.error("Error in auth status check middleware:", error);
    // Don't block on errors, let the request continue
    next();
  }
};

/**
 * Middleware specifically for session-based routes
 * This runs on routes that validate existing sessions
 */
export const checkUserStatusOnSession = async (
  req: MedusaRequest,
  res: MedusaResponse,
  next: NextFunction
) => {
  try {
    const authReq = req as AuthenticatedMedusaRequest;

    // Only check if user is authenticated
    if (!authReq.auth_context?.actor_id) {
      return next();
    }

    // Get user service
    const userService = req.scope.resolve(Modules.USER);
    
    // Get user data
    const user = await userService.retrieveUser(authReq.auth_context.actor_id);
    
    if (!user) {
      return next();
    }

    // Check if user is active
    if (!isUserActive(user)) {
      console.log(`🔒 Session validation failed for deactivated user: ${user.email}`);
      
      // Clear session/auth data by returning 401 (unauthorized)
      // This will force the frontend to redirect to login
      return res.status(401).json({
        type: "account_deactivated",
        message: "Your account has been deactivated. Please contact an administrator for assistance.",
      });
    }

    // User is active, continue
    next();
  } catch (error) {
    console.error("Error in session status check middleware:", error);
    // Don't block on errors, let the request continue
    next();
  }
};
