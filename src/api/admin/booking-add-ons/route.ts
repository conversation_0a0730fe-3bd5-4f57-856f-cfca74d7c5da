import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";
import { BOOKING_ADD_ONS_MODULE } from "../../../modules/booking-add-ons";

/**
 * Test endpoint to debug booking add-ons service
 */
export async function GET(req: MedusaRequest, res: MedusaResponse) {
  try {
    console.log("🔍 Testing booking add-ons service resolution...");

    // Resolve the service using the module name
    const bookingAddOnService = req.scope.resolve(BOOKING_ADD_ONS_MODULE);
    console.log("✅ Service resolved successfully:", !!bookingAddOnService);

    // Test service method
    const testResult = await bookingAddOnService.getAvailableAddOns();
    console.log("✅ Service method called successfully:", testResult);

    return res.json({
      success: true,
      message: "Booking add-ons service is working",
      serviceResolved: !!bookingAddOnService,
      testResult,
    });
  } catch (error) {
    console.error("❌ Error testing booking add-ons service:", error);
    return res.status(500).json({
      success: false,
      message: "Failed to resolve booking add-ons service",
      error: error.message,
    });
  }
}

/**
 * Test endpoint to create a booking add-on
 */
export async function POST(req: MedusaRequest, res: MedusaResponse) {
  try {
    console.log("🔍 Testing booking add-on creation...");
    console.log("Request body:", req.body);

    // Resolve the service using the module name
    const bookingAddOnService = req.scope.resolve(BOOKING_ADD_ONS_MODULE);
    console.log("✅ Service resolved successfully");

    // Test data
    const body = req.body as any;
    const testAddOnData = {
      order_id: body.order_id || "test_order_123",
      add_on_variant_id: body.add_on_variant_id || "test_variant_456",
      add_on_name: body.add_on_name || "Test Add-on",
      quantity: body.quantity || 1,
      unit_price: body.unit_price || 50,
      currency_code: body.currency_code || "CHF",
      customer_field_responses: body.customer_field_responses || {},
      add_on_metadata: body.add_on_metadata || {},
    };

    console.log("Creating add-on with data:", testAddOnData);

    // Test service method
    const createdAddOn = await bookingAddOnService.createBookingAddOn(
      testAddOnData
    );
    console.log("✅ Add-on created successfully:", createdAddOn);

    return res.json({
      success: true,
      message: "Booking add-on created successfully",
      addOn: createdAddOn,
    });
  } catch (error) {
    console.error("❌ Error creating booking add-on:", error);
    return res.status(500).json({
      success: false,
      message: "Failed to create booking add-on",
      error: error.message,
    });
  }
}
