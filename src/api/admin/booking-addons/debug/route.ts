import type { MedusaRequest, MedusaResponse } from "@medusajs/framework";

export const GET = async (req: MedusaRequest, res: MedusaResponse) => {
  try {
    const bookingAddOnService = req.scope.resolve("bookingAddOnsModuleService");
    const supplierService = req.scope.resolve("supplierModuleService");

    // Get the specific booking add-on ID from query params
    const { addon_id } = req.query;

    if (!addon_id) {
      return res.status(400).json({
        error: "addon_id query parameter is required",
      });
    }

    console.log(`🔍 Debug: Investigating booking add-on ${addon_id}`);

    // 1. Get the booking add-on details
    let bookingAddOn;
    try {
      bookingAddOn = await bookingAddOnService.retrieveBookingAddOn(addon_id);
      console.log(`✅ Found booking add-on:`, {
        id: bookingAddOn.id,
        add_on_name: bookingAddOn.add_on_name,
        supplier_order_id: bookingAddOn.supplier_order_id,
        order_status: bookingAddOn.order_status,
        created_at: bookingAddOn.created_at,
        updated_at: bookingAddOn.updated_at,
      });
    } catch (error) {
      console.error(`❌ Booking add-on not found: ${addon_id}`);
      return res.status(404).json({
        error: `Booking add-on ${addon_id} not found`,
        details: error.message,
      });
    }

    // 2. Check if there are any supplier orders that reference this booking add-on
    let relatedSupplierOrders = [];
    try {
      const allOrders = await supplierService.getAllSupplierOrders({
        limit: 1000, // Get all orders to search through metadata
      });

      relatedSupplierOrders = allOrders.supplier_orders.filter((order) => {
        const bookingAddonIds = order.metadata?.booking_addon_ids;
        return (
          bookingAddonIds &&
          Array.isArray(bookingAddonIds) &&
          bookingAddonIds.includes(addon_id)
        );
      });

      console.log(
        `🔍 Found ${relatedSupplierOrders.length} supplier orders referencing this booking add-on`
      );

      relatedSupplierOrders.forEach((order) => {
        console.log(`📦 Supplier Order:`, {
          id: order.id,
          order_number: order.order_number,
          status: order.status,
          booking_addon_ids: order.metadata?.booking_addon_ids,
          created_at: order.created_at,
        });
      });
    } catch (error) {
      console.error(`❌ Error fetching supplier orders:`, error);
    }

    // 3. Check database consistency
    const analysis = {
      booking_addon: {
        id: bookingAddOn.id,
        add_on_name: bookingAddOn.add_on_name,
        supplier_order_id: bookingAddOn.supplier_order_id,
        order_status: bookingAddOn.order_status,
        created_at: bookingAddOn.created_at,
        updated_at: bookingAddOn.updated_at,
      },
      related_supplier_orders: relatedSupplierOrders.map((order) => ({
        id: order.id,
        order_number: order.order_number,
        status: order.status,
        booking_addon_ids: order.metadata?.booking_addon_ids,
        created_at: order.created_at,
      })),
      sync_status: {
        has_supplier_order_id: !!bookingAddOn.supplier_order_id,
        has_order_status: !!bookingAddOn.order_status,
        found_related_orders: relatedSupplierOrders.length,
        is_synced: false,
        issues: [],
      },
    };

    // 4. Analyze sync issues
    if (relatedSupplierOrders.length > 0 && !bookingAddOn.supplier_order_id) {
      analysis.sync_status.issues.push(
        "Supplier orders exist but booking add-on has no supplier_order_id"
      );
    }

    if (relatedSupplierOrders.length > 0 && !bookingAddOn.order_status) {
      analysis.sync_status.issues.push(
        "Supplier orders exist but booking add-on has no order_status"
      );
    }

    if (bookingAddOn.supplier_order_id && relatedSupplierOrders.length === 0) {
      analysis.sync_status.issues.push(
        "Booking add-on has supplier_order_id but no related orders found"
      );
    }

    if (relatedSupplierOrders.length > 0) {
      const latestOrder = relatedSupplierOrders.sort(
        (a, b) =>
          new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
      )[0];

      if (bookingAddOn.supplier_order_id !== latestOrder.id) {
        analysis.sync_status.issues.push(
          `Booking add-on supplier_order_id (${bookingAddOn.supplier_order_id}) doesn't match latest order (${latestOrder.id})`
        );
      }

      if (bookingAddOn.order_status !== latestOrder.status) {
        analysis.sync_status.issues.push(
          `Booking add-on order_status (${bookingAddOn.order_status}) doesn't match latest order status (${latestOrder.status})`
        );
      }
    }

    analysis.sync_status.is_synced = analysis.sync_status.issues.length === 0;

    // 5. Provide recommendations
    const recommendations = [];

    if (analysis.sync_status.issues.length > 0) {
      recommendations.push(
        "Run manual sync: POST /admin/booking-addons/sync-status"
      );
    }

    if (relatedSupplierOrders.length === 0) {
      recommendations.push(
        "Create a supplier order from this booking add-on to test the sync"
      );
    }

    if (relatedSupplierOrders.length > 0 && !analysis.sync_status.is_synced) {
      recommendations.push("Check server logs for workflow execution errors");
      recommendations.push(
        "Verify supplier-order-status-sync subscriber is registered"
      );
    }

    console.log(`📊 Analysis complete:`, {
      is_synced: analysis.sync_status.is_synced,
      issues_count: analysis.sync_status.issues.length,
      recommendations_count: recommendations.length,
    });

    return res.json({
      success: true,
      analysis,
      recommendations,
      debug_info: {
        timestamp: new Date().toISOString(),
        addon_id: addon_id,
        server_logs: "Check console for detailed logs",
      },
    });
  } catch (error) {
    console.error("❌ Debug endpoint error:", error);
    return res.status(500).json({
      error: "Debug analysis failed",
      details: error.message,
    });
  }
};
