import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";
import { z } from "zod";
import { BOOKING_ADD_ONS_MODULE } from "../../../../modules/booking-add-ons";

// Validation schema for updating booking add-ons
const UpdateBookingAddOnSchema = z.object({
  quantity: z.number().int().min(1, "Quantity must be at least 1").optional(),
  unit_price: z.number().min(0, "Unit price must be non-negative").optional(),
  description: z.string().optional(),
  customer_field_responses: z.record(z.any()).optional(),
  currency_code: z.string().optional(),
  supplier_order_id: z.string().nullable().optional(),
  order_status: z
    .enum(["pending", "confirmed", "in_progress", "completed", "cancelled"])
    .nullable()
    .optional(),
});

type UpdateBookingAddOnType = z.infer<typeof UpdateBookingAddOnSchema>;

/**
 * PATCH /admin/booking-addons/[id]
 * Update a specific booking add-on
 */
export async function PATCH(req: MedusaRequest, res: MedusaResponse) {
  try {
    const { id } = req.params;
    console.log(`📝 PATCH /admin/booking-addons/${id} - Updating booking add-on`);

    if (!id) {
      return res.status(400).json({
        success: false,
        message: "Booking add-on ID is required",
      });
    }

    // Validate request body
    const validationResult = UpdateBookingAddOnSchema.safeParse(req.body);
    if (!validationResult.success) {
      console.error("❌ Validation failed:", validationResult.error.errors);
      return res.status(400).json({
        success: false,
        message: "Invalid request data",
        errors: validationResult.error.errors,
      });
    }

    const updateData = validationResult.data;
    console.log("📊 Update data:", updateData);

    // Resolve the booking add-on service
    const bookingAddOnService = req.scope.resolve(BOOKING_ADD_ONS_MODULE);

    // Check if the booking add-on exists
    let existingAddOn;
    try {
      existingAddOn = await bookingAddOnService.retrieveBookingAddOn(id, req.scope);
    } catch (error) {
      console.error(`❌ Booking add-on ${id} not found:`, error);
      return res.status(404).json({
        success: false,
        message: `Booking add-on with ID ${id} not found`,
      });
    }

    // Prepare update data with calculated fields
    const updatePayload: any = { ...updateData };

    // If quantity or unit_price is updated, recalculate total_price
    if (updateData.quantity !== undefined || updateData.unit_price !== undefined) {
      const newQuantity = updateData.quantity ?? existingAddOn.quantity;
      const newUnitPrice = updateData.unit_price ?? existingAddOn.unit_price;
      updatePayload.total_price = newQuantity * newUnitPrice;
      
      console.log(`💰 Recalculating total price: ${newQuantity} × ${newUnitPrice} = ${updatePayload.total_price}`);
    }

    // Update the booking add-on
    const updatedAddOn = await bookingAddOnService.updateBookingAddOn(id, updatePayload, req.scope);

    console.log(`✅ Booking add-on ${id} updated successfully`);

    return res.json({
      success: true,
      booking_add_on: updatedAddOn,
      message: "Booking add-on updated successfully",
    });
  } catch (error) {
    console.error("❌ Error updating booking add-on:", error);
    return res.status(500).json({
      success: false,
      message: "Failed to update booking add-on",
      error: error.message,
    });
  }
}

/**
 * GET /admin/booking-addons/[id]
 * Retrieve a specific booking add-on
 */
export async function GET(req: MedusaRequest, res: MedusaResponse) {
  try {
    const { id } = req.params;
    console.log(`🔍 GET /admin/booking-addons/${id} - Retrieving booking add-on`);

    if (!id) {
      return res.status(400).json({
        success: false,
        message: "Booking add-on ID is required",
      });
    }

    // Resolve the booking add-on service
    const bookingAddOnService = req.scope.resolve(BOOKING_ADD_ONS_MODULE);

    // Retrieve the booking add-on
    const bookingAddOn = await bookingAddOnService.retrieveBookingAddOn(id, req.scope);

    if (!bookingAddOn) {
      return res.status(404).json({
        success: false,
        message: `Booking add-on with ID ${id} not found`,
      });
    }

    console.log(`✅ Booking add-on ${id} retrieved successfully`);

    return res.json({
      success: true,
      booking_add_on: bookingAddOn,
    });
  } catch (error) {
    console.error("❌ Error retrieving booking add-on:", error);
    return res.status(500).json({
      success: false,
      message: "Failed to retrieve booking add-on",
      error: error.message,
    });
  }
}

/**
 * DELETE /admin/booking-addons/[id]
 * Delete a specific booking add-on
 */
export async function DELETE(req: MedusaRequest, res: MedusaResponse) {
  try {
    const { id } = req.params;
    console.log(`🗑️ DELETE /admin/booking-addons/${id} - Deleting booking add-on`);

    if (!id) {
      return res.status(400).json({
        success: false,
        message: "Booking add-on ID is required",
      });
    }

    // Resolve the booking add-on service
    const bookingAddOnService = req.scope.resolve(BOOKING_ADD_ONS_MODULE);

    // Check if the booking add-on exists
    try {
      await bookingAddOnService.retrieveBookingAddOn(id, req.scope);
    } catch (error) {
      console.error(`❌ Booking add-on ${id} not found:`, error);
      return res.status(404).json({
        success: false,
        message: `Booking add-on with ID ${id} not found`,
      });
    }

    // Delete the booking add-on
    await bookingAddOnService.deleteBookingAddOn(id, req.scope);

    console.log(`✅ Booking add-on ${id} deleted successfully`);

    return res.json({
      success: true,
      message: "Booking add-on deleted successfully",
    });
  } catch (error) {
    console.error("❌ Error deleting booking add-on:", error);
    return res.status(500).json({
      success: false,
      message: "Failed to delete booking add-on",
      error: error.message,
    });
  }
}
