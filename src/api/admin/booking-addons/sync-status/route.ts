import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";
import { BOOKING_ADD_ONS_MODULE } from "../../../../modules/booking-add-ons";
import { SUPPLIER_MANAGEMENT_MODULE } from "../../../../modules/vendor_management";
import BookingAddOnService from "../../../../modules/booking-add-ons/service";
import SupplierModuleService from "../../../../modules/vendor_management/supplier-service";

/**
 * POST /admin/booking-addons/sync-status
 * 
 * Manually sync booking add-on status with their linked supplier orders.
 * This is useful for fixing any status inconsistencies or for initial setup.
 */
export async function POST(req: MedusaRequest, res: MedusaResponse) {
  try {
    const bookingAddOnService: BookingAddOnService = req.scope.resolve(BOOKING_ADD_ONS_MODULE);
    const supplierService: SupplierModuleService = req.scope.resolve(SUPPLIER_MANAGEMENT_MODULE);
    
    console.log("🔄 Starting booking add-on status synchronization...");
    
    // Get all booking add-ons that have supplier orders
    const bookingAddons = await bookingAddOnService.listBookingAddOns({
      limit: 1000, // Adjust as needed
    });
    
    const addonsWithOrders = bookingAddons.booking_addons.filter(
      addon => addon.supplier_order_id
    );
    
    console.log(`📊 Found ${addonsWithOrders.length} booking add-ons with supplier orders`);
    
    const syncResults = {
      total: addonsWithOrders.length,
      synced: 0,
      errors: 0,
      details: [] as any[],
    };
    
    // Process each booking add-on
    for (const addon of addonsWithOrders) {
      try {
        // Get the current supplier order status
        const supplierOrder = await supplierService.retrieveSupplierOrder(
          addon.supplier_order_id!
        );
        
        // Check if status needs updating
        if (addon.order_status !== supplierOrder.status) {
          console.log(
            `🔄 Syncing addon ${addon.id}: ${addon.order_status} → ${supplierOrder.status}`
          );
          
          await bookingAddOnService.updateBookingAddOn(addon.id, {
            order_status: supplierOrder.status as any,
          });
          
          syncResults.synced++;
          syncResults.details.push({
            addon_id: addon.id,
            supplier_order_id: addon.supplier_order_id,
            old_status: addon.order_status,
            new_status: supplierOrder.status,
            action: "updated",
          });
        } else {
          syncResults.details.push({
            addon_id: addon.id,
            supplier_order_id: addon.supplier_order_id,
            status: addon.order_status,
            action: "no_change",
          });
        }
      } catch (error) {
        console.error(`❌ Failed to sync addon ${addon.id}:`, error);
        syncResults.errors++;
        syncResults.details.push({
          addon_id: addon.id,
          supplier_order_id: addon.supplier_order_id,
          error: error.message,
          action: "error",
        });
      }
    }
    
    console.log(`✅ Sync completed: ${syncResults.synced} updated, ${syncResults.errors} errors`);
    
    return res.json({
      success: true,
      message: "Booking add-on status synchronization completed",
      results: syncResults,
    });
    
  } catch (error) {
    console.error("❌ Failed to sync booking add-on status:", error);
    return res.status(500).json({
      success: false,
      message: "Failed to sync booking add-on status",
      error: error.message,
    });
  }
}
