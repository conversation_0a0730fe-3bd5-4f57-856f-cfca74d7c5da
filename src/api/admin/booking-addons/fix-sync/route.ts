import type { MedusaRequest, MedusaResponse } from "@medusajs/framework";

export const POST = async (req: MedusaRequest, res: MedusaResponse) => {
  try {
    const bookingAddOnService = req.scope.resolve("bookingAddOnsModuleService");
    const supplierService = req.scope.resolve("supplierModuleService");

    const { addon_id } = req.body;

    if (!addon_id) {
      return res.status(400).json({
        error: "addon_id is required in request body",
      });
    }

    console.log(
      `🔧 Fix Sync: Starting manual sync for booking add-on ${addon_id}`
    );

    // 1. Get the booking add-on
    let bookingAddOn;
    try {
      bookingAddOn = await bookingAddOnService.retrieveBookingAddOn(addon_id);
    } catch (error) {
      return res.status(404).json({
        error: `Booking add-on ${addon_id} not found`,
        details: error.message,
      });
    }

    // 2. Find related supplier orders
    const allOrders = await supplierService.getAllSupplierOrders({
      limit: 1000,
    });

    const relatedOrders = allOrders.supplier_orders.filter((order) => {
      const bookingAddonIds = order.metadata?.booking_addon_ids;
      return (
        bookingAddonIds &&
        Array.isArray(bookingAddonIds) &&
        bookingAddonIds.includes(addon_id)
      );
    });

    console.log(`🔍 Found ${relatedOrders.length} related supplier orders`);

    if (relatedOrders.length === 0) {
      return res.json({
        success: true,
        message: "No supplier orders found for this booking add-on",
        action: "none",
        booking_addon: {
          id: bookingAddOn.id,
          supplier_order_id: bookingAddOn.supplier_order_id,
          order_status: bookingAddOn.order_status,
        },
      });
    }

    // 3. Get the latest supplier order (most recent)
    const latestOrder = relatedOrders.sort(
      (a, b) =>
        new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
    )[0];

    console.log(`📦 Latest supplier order:`, {
      id: latestOrder.id,
      order_number: latestOrder.order_number,
      status: latestOrder.status,
      created_at: latestOrder.created_at,
    });

    // 4. Check if sync is needed
    const needsSync =
      bookingAddOn.supplier_order_id !== latestOrder.id ||
      bookingAddOn.order_status !== latestOrder.status;

    if (!needsSync) {
      return res.json({
        success: true,
        message: "Booking add-on is already in sync",
        action: "none",
        booking_addon: {
          id: bookingAddOn.id,
          supplier_order_id: bookingAddOn.supplier_order_id,
          order_status: bookingAddOn.order_status,
        },
        supplier_order: {
          id: latestOrder.id,
          order_number: latestOrder.order_number,
          status: latestOrder.status,
        },
      });
    }

    // 5. Perform the sync
    console.log(`🔄 Syncing booking add-on with latest supplier order...`);

    const updateData = {
      supplier_order_id: latestOrder.id,
      order_status: latestOrder.status,
    };

    const updatedAddOn = await bookingAddOnService.updateBookingAddOn(
      addon_id,
      updateData
    );

    console.log(`✅ Successfully synced booking add-on ${addon_id}`);

    return res.json({
      success: true,
      message: "Booking add-on successfully synced",
      action: "synced",
      changes: {
        supplier_order_id: {
          from: bookingAddOn.supplier_order_id,
          to: latestOrder.id,
        },
        order_status: {
          from: bookingAddOn.order_status,
          to: latestOrder.status,
        },
      },
      booking_addon: {
        id: updatedAddOn.id,
        supplier_order_id: updatedAddOn.supplier_order_id,
        order_status: updatedAddOn.order_status,
        updated_at: updatedAddOn.updated_at,
      },
      supplier_order: {
        id: latestOrder.id,
        order_number: latestOrder.order_number,
        status: latestOrder.status,
      },
    });
  } catch (error) {
    console.error("❌ Fix sync error:", error);
    return res.status(500).json({
      error: "Failed to fix sync",
      details: error.message,
    });
  }
};
