import { MedusaRequest, MedusaResponse } from "@camped-ai/framework";

export const GET = async (req: MedusaRequest, res: MedusaResponse) => {
  try {
    const { id: customerId } = req.params;
    const { limit = 20, offset = 0 } = req.query || {};
    
    const query = req.scope.resolve("query");
    
    // Query orders for this customer
    const { data: orders } = await query.graph({
      entity: "order",
      filters: {
        customer_id: customerId,
      },
      fields: [
        "id",
        "status", 
        "email",
        "currency_code",
        "payment_status",
        "customer_id",
        "metadata",
        "created_at",
        "updated_at",
        "total",
      ],
    });

    // Filter orders that have booking/hotel data
    const bookingOrders = orders.filter((order: any) => {
      return (
        order.metadata &&
        (order.metadata.hotel_id ||
          order.metadata.hotel_name ||
          order.metadata.room_id ||
          order.metadata.check_in_date)
      );
    });

    // Transform orders into booking format
    const bookings = bookingOrders.map((order: any) => {
      const metadata = order.metadata || {};
      
      // Calculate nights if we have both dates
      let nights = 0;
      if (metadata.check_in_date && metadata.check_out_date) {
        const checkIn = new Date(metadata.check_in_date);
        const checkOut = new Date(metadata.check_out_date);
        nights = Math.ceil(
          (checkOut.getTime() - checkIn.getTime()) / (1000 * 60 * 60 * 24)
        );
      }

      return {
        order_id: order.id,
        booking_id: order.id,
        customer_id: order.customer_id,
        status: order.status || "pending",
        payment_status: order.payment_status || "awaiting_payment",
        guest_name: metadata.guest_name || "Guest",
        guest_email: metadata.guest_email || order.email,
        guest_phone: metadata.guest_phone,
        hotel_id: metadata.hotel_id || "",
        hotel_name: metadata.hotel_name || "Hotel",
        room_id: metadata.room_id || "",
        room_type: metadata.room_type || "Standard",
        room_config_id: metadata.room_config_id || "",
        room_config_name:
          metadata.room_config_name || metadata.room_type || "Standard",
        check_in_date: metadata.check_in_date,
        check_out_date: metadata.check_out_date,
        check_in_time: metadata.check_in_time || "15:00",
        check_out_time: metadata.check_out_time || "11:00",
        nights,
        number_of_guests: metadata.number_of_guests || metadata.adults || 1,
        adults: metadata.adults || 1,
        children: metadata.children || 0,
        infants: metadata.infants || 0,
        number_of_rooms: metadata.number_of_rooms || 1,
        total_amount: order.total || metadata.total_amount || 0,
        currency_code: order.currency_code || metadata.currency_code || "USD",
        special_requests: metadata.special_requests,
        notes: metadata.notes,
        created_at: order.created_at,
        updated_at: order.updated_at,
        metadata: metadata,
      };
    });

    // Apply pagination
    const totalCount = bookings.length;
    const startIndex = Number(offset);
    const endIndex = startIndex + Number(limit);
    const paginatedBookings = bookings.slice(startIndex, endIndex);

    res.json({
      bookings: paginatedBookings,
      count: totalCount,
      limit: Number(limit),
      offset: Number(offset),
    });
  } catch (error) {
    console.error("Error fetching customer bookings:", error);
    res.status(500).json({
      type: "server_error",
      message: "Failed to fetch customer bookings",
      details: error instanceof Error ? error.message : "Unknown error",
    });
  }
};
