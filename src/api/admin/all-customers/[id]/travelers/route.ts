import { MedusaRequest, MedusaResponse } from "@camped-ai/framework";

export const GET = async (req: MedusaRequest, res: MedusaResponse) => {
  try {
    const { id: customerId } = req.params;
    const { limit = 20, offset = 0 } = req.query || {};

    const query = req.scope.resolve("query");

    // Query travelers for this customer
    const { data: travelers } = await query.graph({
      entity: "traveler",
      filters: {
        customer_id: customerId,
      },
      fields: [
        "id",
        "customer_id",
        "first_name",
        "last_name", 
        "gender",
        "date_of_birth",
        "relationship",
        "created_at",
        "updated_at"
      ],
    });

    // Apply pagination
    const totalCount = travelers.length;
    const startIndex = Number(offset);
    const endIndex = startIndex + Number(limit);
    const paginatedTravelers = travelers.slice(startIndex, endIndex);

    res.json({
      travelers: paginatedTravelers,
      count: totalCount,
      limit: Number(limit),
      offset: Number(offset),
    });
  } catch (error) {
    console.error("Error fetching customer travelers:", error);
    res.status(500).json({
      type: "server_error",
      message: "Failed to fetch customer travelers",
      details: error instanceof Error ? error.message : "Unknown error",
    });
  }
};
