import {
  AuthenticatedMedusaRequest,
  MedusaResponse
} from "@camped-ai/framework";
import { ContainerRegistrationKeys } from "@camped-ai/framework/utils";
import { z } from "zod";
import { updateCustomersWorkflow } from "@camped-ai/medusa/core-flows";

// Validation schema for updating customers
const UpdateCustomerSchema = z.object({
  first_name: z.string().min(1, "First name is required").optional(),
  last_name: z.string().min(1, "Last name is required").optional(),
  email: z.string().email("Invalid email address").optional(),
  phone: z.string().optional(),
  company_name: z.string().optional(),
});

type UpdateCustomerInput = z.infer<typeof UpdateCustomerSchema>;

/**
 * GET /admin/customers/[id]
 * Get a specific customer by ID
 */
export const GET = async (
  req: AuthenticatedMedusaRequest,
  res: MedusaResponse
) => {
  try {
    const query = req.scope.resolve(ContainerRegistrationKeys.QUERY);
    const { id } = req.params;

    if (!id) {
      return res.status(400).json({
        type: "invalid_request",
        message: "Customer ID is required",
      });
    }

    // Query customer from database
    const {
      data: customers,
    } = await query.graph({
      entity: "customer",
      fields: [
        "id",
        "company_name",
        "first_name", 
        "last_name",
        "email",
        "phone",
        "metadata",
        "has_account",
        "created_by",
        "created_at",
        "updated_at",
        "deleted_at"
      ],
      filters: {
        id: id
      }
    });

    if (!customers || customers.length === 0) {
      return res.status(404).json({
        type: "not_found",
        message: "Customer not found",
      });
    }

    const customer = customers[0];

    res.json({
      customer,
    });
  } catch (error) {
    console.error("Error fetching customer:", error);
    res.status(500).json({
      type: "server_error",
      message: "Failed to fetch customer",
      details: error instanceof Error ? error.message : "Unknown error",
    });
  }
};

/**
 * PUT /admin/all-customers/[id]
 * Update a specific customer by ID
 */
export const PUT = async (
  req: AuthenticatedMedusaRequest<UpdateCustomerInput>,
  res: MedusaResponse
) => {
  try {
    const validatedData = UpdateCustomerSchema.parse(req.body);
    const query = req.scope.resolve(ContainerRegistrationKeys.QUERY);
    const { id } = req.params;

    if (!id) {
      return res.status(400).json({
        type: "invalid_request",
        message: "Customer ID is required",
      });
    }

    // First check if customer exists
    const {
      data: existingCustomers,
    } = await query.graph({
      entity: "customer",
      fields: ["id"],
      filters: {
        id: id
      }
    });

    if (!existingCustomers || existingCustomers.length === 0) {
      return res.status(404).json({
        type: "not_found",
        message: "Customer not found",
      });
    }

    // Update customer using the updateCustomersWorkflow
    const updateWorkflow = updateCustomersWorkflow(req.scope);
    const { result: updatedCustomers } = await updateWorkflow.run({
      input: {
        selector: { id: [id] },
        update: validatedData,
      },
    });

    const updatedCustomer = updatedCustomers[0];

    res.json({
      customer: updatedCustomer,
      success: true,
    });
  } catch (error) {
    if (error instanceof z.ZodError) {
      res.status(400).json({
        type: "validation_error",
        message: "Validation error",
        errors: error.errors,
      });
    } else {
      console.error("Error updating customer:", error);
      res.status(500).json({
        type: "server_error",
        message: "Failed to update customer",
        details: error instanceof Error ? error.message : "Unknown error",
      });
    }
  }
};
