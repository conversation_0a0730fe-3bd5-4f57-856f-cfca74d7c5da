import {
  AuthenticatedMedusaRequest,
  MedusaResponse
} from "@camped-ai/framework";
import { ContainerRegistration<PERSON><PERSON><PERSON>, Modu<PERSON> } from "@camped-ai/framework/utils";
import { z } from "zod";
import { ICustomerModuleService } from "@camped-ai/framework/types";

// Validation schema for query parameters
export const GetAllCustomersQuery = z.object({
  limit: z.coerce.number().default(10),
  offset: z.coerce.number().default(0),
  search: z.string().optional(),
  sort_by: z.enum(["name", "email", "created_at", "updated_at"]).default("created_at"),
  sort_order: z.enum(["asc", "desc"]).default("desc"),
});

export type GetAllCustomersQueryType = z.infer<typeof GetAllCustomersQuery>;

// Validation schema for creating customers
const CreateCustomerSchema = z.object({
  first_name: z.string().min(1, "First name is required"),
  last_name: z.string().min(1, "Last name is required"),
  email: z.string().email("Invalid email address"),
  phone: z.string().optional(),
  company_name: z.string().optional(),
});

type CreateCustomerInput = z.infer<typeof CreateCustomerSchema>;

/**
 * GET /admin/all-customers
 * List all customers with search and pagination
 */
export const GET = async (
  req: AuthenticatedMedusaRequest,
  res: MedusaResponse
) => {
  try {
    const query = req.scope.resolve(ContainerRegistrationKeys.QUERY);
    const validatedQuery = GetAllCustomersQuery.parse(req.query);

    const {
      limit,
      offset,
      search,
      sort_by,
      sort_order,
    } = validatedQuery;

    // Build filters
    const filters: Record<string, any> = {};

    // Search filter - search by first name, last name, or email
    if (search) {
      filters.$or = [
        { first_name: { $ilike: `%${search}%` } },
        { last_name: { $ilike: `%${search}%` } },
        { email: { $ilike: `%${search}%` } },
      ];
    }

    // Build order object based on sort parameters
    const buildOrderObject = (sortBy: string, sortOrder: string) => {
      const order: Record<string, string> = {};

      switch (sortBy) {
        case "name":
          // For name sorting, we'll use first_name as primary and last_name as secondary
          order.first_name = sortOrder.toUpperCase();
          order.last_name = sortOrder.toUpperCase();
          break;
        case "email":
          order.email = sortOrder.toUpperCase();
          break;
        case "created_at":
          order.created_at = sortOrder.toUpperCase();
          break;
        case "updated_at":
          order.updated_at = sortOrder.toUpperCase();
          break;
        default:
          order.created_at = "DESC";
      }

      return order;
    };

    const orderObject = buildOrderObject(sort_by, sort_order);

    // Query customers from database
    const {
      data: customers,
      metadata: { count, take, skip },
    } = await query.graph({
      entity: "customer",
      fields: [
        "id",
        "company_name",
        "first_name", 
        "last_name",
        "email",
        "phone",
        "metadata",
        "has_account",
        "created_by",
        "created_at",
        "updated_at",
        "deleted_at"
      ],
      filters,
      pagination: {
        skip: offset,
        take: limit,
        order: orderObject
      }
    });

    res.json({
      customers,
      count,
      limit: take,
      offset: skip,
    });
  } catch (error) {
    console.error("Error fetching customers:", error);
    res.status(500).json({
      type: "server_error",
      message: "Failed to fetch customers",
      details: error instanceof Error ? error.message : "Unknown error",
    });
  }
};

/**
 * POST /admin/all-customers
 * Create a new customer
 */
export const POST = async (
  req: AuthenticatedMedusaRequest<CreateCustomerInput>,
  res: MedusaResponse
) => {
  try {
    const validatedData = CreateCustomerSchema.parse(req.body);

    // Get the customer service
    const customerService: ICustomerModuleService = req.scope.resolve(Modules.CUSTOMER);

    // Create customer directly using the customer service (without authentication)
    const customers = await customerService.createCustomers([{
      first_name: validatedData.first_name,
      last_name: validatedData.last_name,
      email: validatedData.email,
      phone: validatedData.phone,
      company_name: validatedData.company_name,
      metadata: {
        source: "admin_creation",
        created_by: req.auth_context?.actor_id || "admin",
        created_at: new Date().toISOString(),
      },
    }]);

    const customer = customers[0]; // Get the first (and only) customer from the result array

    res.status(201).json({
      customer: customer,
      success: true,
    });
  } catch (error) {
    if (error instanceof z.ZodError) {
      res.status(400).json({
        type: "validation_error",
        message: "Validation error",
        errors: error.errors,
      });
    } else {
      console.error("Error creating customer:", error);
      res.status(500).json({
        type: "server_error",
        message: "Failed to create customer",
        details: error instanceof Error ? error.message : "Unknown error",
      });
    }
  }
};
