import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";
import { Modules } from "@camped-ai/framework/utils";

/**
 * Test endpoint to verify order service resolution
 */
export const GET = async (req: MedusaRequest, res: MedusaResponse) => {
  try {
    console.log("[TEST] Testing order service resolution...");
    
    // Try to resolve the order service
    const orderService = req.scope.resolve(Modules.ORDER);
    console.log("[TEST] Order service resolved:", !!orderService);
    
    // Try to list orders
    const orders = await orderService.listOrders({}, { take: 5 });
    console.log("[TEST] Found orders:", orders.length);
    
    // If we have orders, try to retrieve one with relations
    let testOrder = null;
    if (orders.length > 0) {
      const firstOrderId = orders[0].id;
      console.log("[TEST] Testing retrieveOrder with ID:", firstOrderId);
      
      testOrder = await orderService.retrieveOrder(firstOrderId, {
        relations: [
          "customer",
          "items",
          "shipping_address",
          "billing_address"
        ],
      });
      console.log("[TEST] Retrieved order successfully:", !!testOrder);
    }
    
    return res.json({
      success: true,
      orderServiceAvailable: !!orderService,
      totalOrders: orders.length,
      testOrder: testOrder ? {
        id: testOrder.id,
        email: testOrder.email,
        hasCustomer: !!testOrder.customer,
        hasItems: !!testOrder.items,
        itemsCount: testOrder.items?.length || 0
      } : null,
      orderIds: orders.map(o => o.id).slice(0, 5)
    });
    
  } catch (error) {
    console.error("[TEST] Error testing order service:", error);
    return res.status(500).json({
      success: false,
      error: error.message,
      stack: error.stack
    });
  }
};
