import { AuthenticatedMedusaRequest, MedusaResponse } from "@camped-ai/framework/http";
import { z } from "zod";
import { RBAC_MODULE } from "../../../modules/rbac";
import RbacModuleService from "../../../modules/rbac/service";
import { ScreenPermission } from "../../../modules/rbac/types";

// Validation schema for listing role templates
export const GetAdminRoleTemplates = z.object({
  suggested_for: z.string().optional(),
});

export type GetAdminRoleTemplatesType = z.infer<typeof GetAdminRoleTemplates>;

// Validation schema for creating role from template
export const PostAdminCreateRoleFromTemplate = z.object({
  template_id: z.string().min(1, "Template ID is required"),
  name: z.string().min(1, "Role name is required").max(100, "Role name too long"),
  description: z.string().optional(),
  customize_permissions: z.array(z.nativeEnum(ScreenPermission)).optional(),
});

export type PostAdminCreateRoleFromTemplateType = z.infer<typeof PostAdminCreateRoleFromTemplate>;

/**
 * GET /admin/role-templates
 * List all available role templates
 */
export async function GET(
  req: AuthenticatedMedusaRequest,
  res: MedusaResponse
) {
  try {
    console.log("GET /admin/role-templates - Starting request");

    if (!req.scope.hasRegistration(RBAC_MODULE)) {
      console.error("RBAC module not registered in container");
      return res.status(500).json({
        type: "server_error",
        message: "RBAC module not available",
      });
    }

    const rbacService: RbacModuleService = req.scope.resolve(RBAC_MODULE);
    console.log("RBAC service resolved successfully");

    const suggested_for = req.query.suggested_for as string;
    console.log("Query params:", { suggested_for });

    // TODO: Re-enable permission checking once basic functionality works
    /*
    const userId = req.auth_context?.actor_id;
    if (userId) {
      const userService = req.scope.resolve(Modules.USER);
      const user = await userService.retrieveUser(userId);

      if (!rbacService.hasPermission(user, ScreenPermission.ROLE_MANAGEMENT_VIEW)) {
        return res.status(403).json({
          type: "permission_denied",
          message: "You don't have permission to view role templates",
        });
      }
    }
    */

    // Get all role templates
    let templates = rbacService.getRoleTemplates();
    
    // Filter by suggested_for if specified
    if (suggested_for) {
      templates = templates.filter(template => 
        template.suggested_for.some(suggestion => 
          suggestion.toLowerCase().includes(suggested_for.toLowerCase())
        )
      );
    }

    return res.status(200).json({
      templates,
      count: templates.length,
    });
  } catch (error) {
    console.error("Error listing role templates:", error);
    return res.status(500).json({
      type: "server_error",
      message: "Failed to list role templates",
    });
  }
}

/**
 * POST /admin/role-templates
 * Create a new role from a template
 */
export async function POST(
  req: AuthenticatedMedusaRequest,
  res: MedusaResponse
) {
  try {
    const rbacService: RbacModuleService = req.scope.resolve(RBAC_MODULE);

    const { template_id, name, description, customize_permissions } = req.body;
    const userId = req.auth_context?.actor_id;

    if (!userId) {
      return res.status(401).json({
        type: "unauthorized",
        message: "User not authenticated",
      });
    }

    // TODO: Re-enable permission checking once basic functionality works
    /*
    const userService = req.scope.resolve(Modules.USER);
    const user = await userService.retrieveUser(userId);

    if (!rbacService.hasPermission(user, ScreenPermission.ROLE_MANAGEMENT_CREATE)) {
      return res.status(403).json({
        type: "permission_denied",
        message: "You don't have permission to create roles",
      });
    }
    */

    // Find the template
    const templates = rbacService.getRoleTemplates();
    const template = templates.find(t => t.id === template_id);

    if (!template) {
      return res.status(404).json({
        type: "not_found",
        message: "Role template not found",
      });
    }

    // Use template permissions or custom permissions
    const permissions = customize_permissions || template.permissions;
    const roleDescription = description || template.description;

    // Create the role
    const role = await rbacService.createCustomRole({
      name,
      description: roleDescription,
      permissions,
      created_by: userId,
    });

    return res.status(201).json({
      role,
      template_used: template,
      message: "Role created successfully from template",
    });
  } catch (error) {
    console.error("Error creating role from template:", error);
    
    if (error.type === "invalid_data") {
      return res.status(400).json({
        type: error.type,
        message: error.message,
      });
    }

    return res.status(500).json({
      type: "server_error",
      message: "Failed to create role from template",
    });
  }
}
