import { AuthenticatedMedusaRequest, MedusaResponse } from "@camped-ai/framework/http";
import { z } from "zod";
import { RBAC_MODULE } from "../../../modules/rbac";
import RbacModuleService from "../../../modules/rbac/service";
// import { ScreenPermission } from "../../../modules/rbac/types"; // Commented out for now

// Validation schema for listing permissions
export const GetAdminPermissions = z.object({
  group: z.string().optional(),
  include_metadata: z.boolean().optional().default(true),
});

export type GetAdminPermissionsType = z.infer<typeof GetAdminPermissions>;

/**
 * GET /admin/permissions
 * List all available permissions with metadata
 */
export async function GET(
  req: AuthenticatedMedusaRequest,
  res: MedusaResponse
) {
  try {
    console.log("GET /admin/permissions - Starting request");

    // Check if RBAC module is available
    if (!req.scope.hasRegistration(RBAC_MODULE)) {
      console.error("RBAC module not registered in container");
      return res.status(500).json({
        type: "server_error",
        message: "RBAC module not available",
      });
    }

    const rbacService: RbacModuleService = req.scope.resolve(RBAC_MODULE);
    console.log("RBAC service resolved successfully");

    // Parse query parameters with defaults
    const group = req.query.group as string;
    const include_metadata = req.query.include_metadata !== 'false'; // default true

    console.log("Query params:", { group, include_metadata });

    // For now, skip permission checking to debug the core functionality
    // TODO: Re-enable permission checking once the basic functionality works
    /*
    const userId = req.auth_context?.actor_id;
    if (userId) {
      const userService = req.scope.resolve(Modules.USER);
      const user = await userService.retrieveUser(userId);

      if (!rbacService.hasPermission(user, ScreenPermission.ROLE_MANAGEMENT_VIEW)) {
        return res.status(403).json({
          type: "permission_denied",
          message: "You don't have permission to view permissions",
        });
      }
    }
    */

    // Get all permissions
    console.log("Getting all permissions...");
    const allPermissions = rbacService.getAllPermissions();
    console.log(`Retrieved ${allPermissions.length} permissions`);

    if (include_metadata) {
      console.log("Including metadata...");
      const permissionMetadata = rbacService.getPermissionMetadata();
      const permissionGroups = rbacService.getPermissionGroups();
      console.log(`Retrieved ${permissionGroups.length} permission groups`);

      // Filter by group if specified
      let filteredPermissions = allPermissions;
      if (group) {
        const groupPermissions = permissionGroups.find(g => g.id === group)?.permissions || [];
        filteredPermissions = allPermissions.filter(p => groupPermissions.includes(p));
        console.log(`Filtered to ${filteredPermissions.length} permissions for group: ${group}`);
      }

      // Build response with metadata
      const permissionsWithMetadata = filteredPermissions.map(permission => ({
        permission,
        ...permissionMetadata[permission],
      }));

      console.log("Returning response with metadata");
      return res.status(200).json({
        permissions: permissionsWithMetadata,
        groups: permissionGroups,
        count: filteredPermissions.length,
        total: allPermissions.length,
      });
    } else {
      console.log("Returning simple list without metadata");
      // Simple list without metadata
      let filteredPermissions = allPermissions;
      if (group) {
        const permissionGroups = rbacService.getPermissionGroups();
        const groupPermissions = permissionGroups.find(g => g.id === group)?.permissions || [];
        filteredPermissions = allPermissions.filter(p => groupPermissions.includes(p));
        console.log(`Filtered to ${filteredPermissions.length} permissions for group: ${group}`);
      }

      return res.status(200).json({
        permissions: filteredPermissions,
        count: filteredPermissions.length,
        total: allPermissions.length,
      });
    }
  } catch (error) {
    console.error("Error listing permissions:", error);
    console.error("Error stack:", error.stack);
    return res.status(500).json({
      type: "server_error",
      message: "Failed to list permissions",
      error: error.message,
    });
  }
}
