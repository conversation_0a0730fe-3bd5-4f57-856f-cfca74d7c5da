import { AuthenticatedMedusaRequest, MedusaResponse } from "@camped-ai/framework/http";
import { Modules } from "@camped-ai/framework/utils";
import { z } from "zod";
import { RBAC_MODULE } from "../../../../modules/rbac";
import RbacModuleService from "../../../../modules/rbac/service";
import { ScreenPermission } from "../../../../modules/rbac/types";

// Validation schema for updating roles
export const PutAdminUpdateRole = z.object({
  name: z.string().min(1, "Role name is required").max(100, "Role name too long").optional(),
  description: z.string().min(1, "Role description is required").max(500, "Description too long").optional(),
  permissions: z.array(z.nativeEnum(ScreenPermission)).min(1, "At least one permission is required").optional(),
  is_active: z.boolean().optional(),
});

export type PutAdminUpdateRoleType = z.infer<typeof PutAdminUpdateRole>;

/**
 * GET /admin/roles/:id
 * Get a specific role by ID
 */
export async function GET(
  req: AuthenticatedMedusaRequest,
  res: MedusaResponse
) {
  try {
    const rbacService: RbacModuleService = req.scope.resolve(RBAC_MODULE);
    const roleId = req.params.id;
    const userId = req.auth_context?.actor_id;

    if (!roleId) {
      return res.status(400).json({
        type: "invalid_data",
        message: "Role ID is required",
      });
    }

    if (!userId) {
      return res.status(401).json({
        type: "unauthorized",
        message: "User not authenticated",
      });
    }

    // Check if user is admin or requesting their own role
    const userService = req.scope.resolve(Modules.USER);
    const user = await userService.retrieveUser(userId);
    const userRbacData = user.metadata?.rbac as any;

    // Allow if user is admin
    const isAdmin = userRbacData?.role === "admin";

    // Allow if user is requesting their own role
    const isOwnRole = userRbacData?.role_id === roleId;

    if (!isAdmin && !isOwnRole) {
      return res.status(403).json({
        type: "permission_denied",
        message: "You can only view your own role or must be an admin",
      });
    }

    const role = await rbacService.getRole(roleId);

    if (!role) {
      return res.status(404).json({
        type: "not_found",
        message: "Role not found",
      });
    }

    return res.status(200).json({
      role,
    });
  } catch (error) {
    console.error("Error getting role:", error);
    return res.status(500).json({
      type: "server_error",
      message: "Failed to get role",
    });
  }
}

/**
 * PUT /admin/roles/:id
 * Update a specific role
 */
export async function PUT(
  req: AuthenticatedMedusaRequest<PutAdminUpdateRoleType>,
  res: MedusaResponse
) {
  try {
    const rbacService: RbacModuleService = req.scope.resolve(RBAC_MODULE);
    const roleId = req.params.id;
    const userId = req.auth_context?.actor_id;

    if (!roleId) {
      return res.status(400).json({
        type: "invalid_data",
        message: "Role ID is required",
      });
    }

    if (!userId) {
      return res.status(401).json({
        type: "unauthorized",
        message: "User not authenticated",
      });
    }

    // TODO: Re-enable permission checking once basic functionality works
    /*
    const userService = req.scope.resolve(Modules.USER);
    const user = await userService.retrieveUser(userId);

    if (!rbacService.hasPermission(user, ScreenPermission.ROLE_MANAGEMENT_EDIT)) {
      return res.status(403).json({
        type: "permission_denied",
        message: "You don't have permission to edit roles",
      });
    }
    */

    // Update the role
    const updatedRole = await rbacService.updateRole({
      id: roleId,
      ...req.validatedBody,
      updated_by: userId,
    });

    return res.status(200).json({
      role: updatedRole,
      message: "Role updated successfully",
    });
  } catch (error) {
    console.error("Error updating role:", error);
    console.error("Error details:", {
      message: error.message,
      type: error.type,
      stack: error.stack,
      name: error.name,
      cause: error.cause
    });

    // Handle validation errors
    if (error.name === "ZodError") {
      return res.status(400).json({
        type: "validation_error",
        message: "Validation failed",
        details: error.errors || error.message,
      });
    }

    // Handle database constraint violations
    if (error.message && error.message.includes("duplicate key value violates unique constraint")) {
      return res.status(409).json({
        type: "conflict",
        message: "A role with this name already exists",
      });
    }

    if (error.type === "invalid_data") {
      return res.status(400).json({
        type: error.type,
        message: error.message,
      });
    }

    if (error.type === "not_found") {
      return res.status(404).json({
        type: error.type,
        message: error.message,
      });
    }

    if (error.type === "not_allowed") {
      return res.status(403).json({
        type: error.type,
        message: error.message,
      });
    }

    // Return more detailed error information for debugging
    return res.status(500).json({
      type: "server_error",
      message: "Failed to update role",
      details: process.env.NODE_ENV === "development" ? {
        error: error.message,
        type: error.type,
        name: error.name
      } : undefined,
    });
  }
}

/**
 * DELETE /admin/roles/:id
 * Delete a specific role
 */
export async function DELETE(
  req: AuthenticatedMedusaRequest,
  res: MedusaResponse
) {
  try {
    const rbacService: RbacModuleService = req.scope.resolve(RBAC_MODULE);
    const roleId = req.params.id;
    const userId = req.auth_context?.actor_id;

    if (!roleId) {
      return res.status(400).json({
        type: "invalid_data",
        message: "Role ID is required",
      });
    }

    if (!userId) {
      return res.status(401).json({
        type: "unauthorized",
        message: "User not authenticated",
      });
    }

    // TODO: Re-enable permission checking once basic functionality works
    /*
    const userService = req.scope.resolve(Modules.USER);
    const user = await userService.retrieveUser(userId);

    if (!rbacService.hasPermission(user, ScreenPermission.ROLE_MANAGEMENT_DELETE)) {
      return res.status(403).json({
        type: "permission_denied",
        message: "You don't have permission to delete roles",
      });
    }
    */

    // Delete the role
    await rbacService.deleteRole(roleId, userId);

    return res.status(200).json({
      message: "Role deleted successfully",
    });
  } catch (error) {
    console.error("Error deleting role:", error);
    
    if (error.type === "not_found") {
      return res.status(404).json({
        type: error.type,
        message: error.message,
      });
    }

    if (error.type === "not_allowed") {
      return res.status(403).json({
        type: error.type,
        message: error.message,
      });
    }

    return res.status(500).json({
      type: "server_error",
      message: "Failed to delete role",
    });
  }
}
