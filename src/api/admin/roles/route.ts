import {
  AuthenticatedMedusaRequest,
  MedusaResponse,
} from "@camped-ai/framework/http";
import { z } from "zod";
import { RBAC_MODULE } from "../../../modules/rbac";
import RbacModuleService from "../../../modules/rbac/service";
import { ScreenPermission } from "../../../modules/rbac/types";

// Validation schema for creating custom roles
export const PostAdminCreateRole = z.object({
  name: z
    .string()
    .min(1, "Role name is required")
    .max(100, "Role name too long"),
  description: z
    .string()
    .min(1, "Role description is required")
    .max(500, "Description too long"),
  permissions: z
    .array(z.nativeEnum(ScreenPermission))
    .min(1, "At least one permission is required"),
});

export type PostAdminCreateRoleType = z.infer<typeof PostAdminCreateRole>;

// Validation schema for listing roles
export const GetAdminRoles = z.object({
  include_system_roles: z.boolean().optional().default(true),
  is_active: z.boolean().optional(),
  limit: z.number().min(1).max(100).optional().default(20),
  offset: z.number().min(0).optional().default(0),
});

export type GetAdminRolesType = z.infer<typeof GetAdminRoles>;

/**
 * GET /admin/roles
 * List all custom roles
 */
export async function GET(
  req: AuthenticatedMedusaRequest,
  res: MedusaResponse
) {
  try {
    // Check if RBAC module is available
    if (!req.scope.hasRegistration(RBAC_MODULE)) {
      return res.status(500).json({
        type: "server_error",
        message: "RBAC module not available",
        debug: {
          rbac_module_name: RBAC_MODULE,
          available_registrations: Object.keys(req.scope.registrations || {}),
        },
      });
    }

    const rbacService: RbacModuleService = req.scope.resolve(RBAC_MODULE);

    // Parse query parameters with defaults
    const include_system_roles = req.query.include_system_roles !== "false"; // default true
    const is_active = req.query.is_active
      ? req.query.is_active === "true"
      : undefined;
    const limit = parseInt(req.query.limit as string) || 20;
    const offset = parseInt(req.query.offset as string) || 0;

    // Get all roles
    let roles = await rbacService.listAllRoles(include_system_roles);

    // Filter by active status if specified
    if (is_active !== undefined) {
      roles = roles.filter((role) => role.is_active === is_active);
    }

    // Apply pagination
    const total = roles.length;
    const paginatedRoles = roles.slice(offset, offset + limit);

    return res.status(200).json({
      roles: paginatedRoles,
      count: paginatedRoles.length,
      total,
      offset,
      limit,
    });
  } catch (error) {
    return res.status(500).json({
      type: "server_error",
      message: "Failed to list roles",
      error: error.message,
    });
  }
}

/**
 * POST /admin/roles
 * Create a new custom role
 */
export async function POST(
  req: AuthenticatedMedusaRequest<PostAdminCreateRoleType>,
  res: MedusaResponse
) {
  try {
    const rbacService: RbacModuleService = req.scope.resolve(RBAC_MODULE);

    const { name, description, permissions } = req.validatedBody;
    const userId = req.auth_context?.actor_id;

    if (!userId) {
      return res.status(401).json({
        type: "unauthorized",
        message: "User not authenticated",
      });
    }

    // TODO: Re-enable permission checking once basic functionality works
    /*
    const userService = req.scope.resolve(Modules.USER);
    const user = await userService.retrieveUser(userId);

    if (!rbacService.hasPermission(user, ScreenPermission.ROLE_MANAGEMENT_CREATE)) {
      return res.status(403).json({
        type: "permission_denied",
        message: "You don't have permission to create roles",
      });
    }
    */

    // Create the role
    const role = await rbacService.createRole({
      name,
      description,
      permissions,
      created_by: userId,
    });

    return res.status(201).json({
      role,
      message: "Role created successfully",
    });
  } catch (error) {
    if (error.type === "invalid_data") {
      return res.status(400).json({
        type: error.type,
        message: error.message,
      });
    }

    return res.status(500).json({
      type: "server_error",
      message: "Failed to create role",
    });
  }
}
