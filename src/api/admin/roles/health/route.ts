import { AuthenticatedMedusaRequest, MedusaResponse } from "@camped-ai/framework/http";
import { RBAC_MODULE } from "../../../../modules/rbac";
import RbacModuleService from "../../../../modules/rbac/service";

/**
 * GET /admin/roles/health
 * Health check for RBAC module
 */
export async function GET(
  req: AuthenticatedMedusaRequest,
  res: MedusaResponse
) {
  try {
    console.log("Health check - Starting");
    
    // Check if RBAC module is registered
    const hasRbacModule = req.scope.hasRegistration(RBAC_MODULE);
    console.log("RBAC module registered:", hasRbacModule);
    
    if (!hasRbacModule) {
      return res.status(500).json({
        status: "error",
        message: "RBAC module not registered",
        rbac_module_name: RBAC_MODULE,
        available_registrations: Object.keys(req.scope.registrations || {}),
      });
    }

    // Try to resolve the service
    const rbacService: RbacModuleService = req.scope.resolve(RBAC_MODULE);
    console.log("RBAC service resolved");

    // Test basic functionality
    const systemRoles = rbacService.getSystemRoleAsCustomRole ? "available" : "not available";
    const permissionGroups = rbacService.getPermissionGroups();
    const allPermissions = rbacService.getAllPermissions();

    return res.status(200).json({
      status: "healthy",
      rbac_module: RBAC_MODULE,
      service_resolved: true,
      system_roles: systemRoles,
      permission_groups_count: permissionGroups.length,
      total_permissions: allPermissions.length,
      sample_permissions: allPermissions.slice(0, 5),
    });
  } catch (error) {
    console.error("Health check error:", error);
    return res.status(500).json({
      status: "error",
      message: error.message,
      stack: error.stack,
    });
  }
}
