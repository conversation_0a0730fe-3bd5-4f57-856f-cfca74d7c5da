import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";
import * as ExcelJS from 'exceljs';
import { DESTINATION_MODULE } from "../../../../modules/hotel-management/destination";
import DestinationModuleService from "../../../../modules/hotel-management/destination/service";

/**
 * GET endpoint to download a template for bulk hotel import
 */
export const GET = async (req: MedusaRequest, res: MedusaResponse) => {
  try {
    // Get destination service to fetch destinations for the dropdown
    const destinationService: DestinationModuleService = req.scope.resolve(DESTINATION_MODULE);

    // Fetch all destinations to provide as reference
    const destinations = await destinationService.listDestinations({});

    // Create a new workbook
    const workbook = new ExcelJS.Workbook();

    // Add a worksheet for hotels
    const worksheet = workbook.addWorksheet('Hotels');

    // Define columns
    worksheet.columns = [
      { header: 'Name*', key: 'name', width: 30 },
      { header: 'Destination*', key: 'destination', width: 30 },
      { header: 'Description', key: 'description', width: 50 },
      { header: 'Is Active', key: 'is_active', width: 15 },
      { header: 'Featured', key: 'is_featured', width: 15 },
      { header: 'Address', key: 'address', width: 40 },
      { header: 'City', key: 'city', width: 20 },
      { header: 'Postal Code', key: 'postal_code', width: 15 },
      { header: 'Phone Number', key: 'phone_number', width: 20 },
      { header: 'Email', key: 'email', width: 30 },
      { header: 'Website', key: 'website', width: 30 },
      { header: 'Check In Time', key: 'check_in_time', width: 15 },
      { header: 'Check Out Time', key: 'check_out_time', width: 15 },
      { header: 'Rating (1-5)', key: 'rating', width: 15 },
      { header: 'Pets Allowed', key: 'is_pets_allowed', width: 15 },
      { header: 'Amenities (comma separated)', key: 'amenities', width: 40 },
      { header: 'Rules (comma separated)', key: 'rules', width: 40 },
      { header: 'Safety Measures (comma separated)', key: 'safety_measures', width: 40 },
      { header: 'Notes', key: 'notes', width: 50 },
      { header: 'Tags (comma separated)', key: 'tags', width: 30 },
    ];

    // Style the header row
    worksheet.getRow(1).font = { bold: true };
    worksheet.getRow(1).fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: 'FFE0E0E0' }
    };

    // Add a second worksheet with destination reference data
    const destinationSheet = workbook.addWorksheet('Destinations Reference');

    // Define columns for the destination reference sheet
    destinationSheet.columns = [
      { header: 'id', key: 'id', width: 40 },
      { header: 'name', key: 'name', width: 30 },
      { header: 'country', key: 'country', width: 20 },
    ];

    // Style the header row
    destinationSheet.getRow(1).font = { bold: true };
    destinationSheet.getRow(1).fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: 'FFE0E0E0' }
    };

    // Add destination data
    destinations.forEach(destination => {
      destinationSheet.addRow({
        id: destination.id,
        name: destination.name,
        country: destination.country
      });
    });

    // Add a sample row to the hotels sheet
    if (destinations.length > 0) {
      const destination = destinations[0];
      worksheet.addRow({
        name: 'Luxury Beach Resort',
        destination: destination.name,
        description: 'A beautiful beachfront resort with stunning ocean views and world-class amenities',
        is_active: 'TRUE',
        is_featured: 'TRUE',
        address: '123 Ocean Drive',
        city: 'Paradise Bay',
        postal_code: '12345',
        phone_number: '******-123-4567',
        email: '<EMAIL>',
        website: 'https://www.luxurybeachresort.com',
        check_in_time: '15:00',
        check_out_time: '11:00',
        rating: '5',
        is_pets_allowed: 'TRUE',
        amenities: 'WiFi,Pool,Spa,Restaurant,Beach Access,Fitness Center,Room Service',
        rules: 'No smoking,Quiet hours 10PM-7AM,Pool closes at midnight',
        safety_measures: 'CCTV surveillance,24/7 security,Fire safety systems,Emergency exits clearly marked',
        notes: 'Popular for weddings and honeymoons. Book early during peak season.',
        tags: 'luxury,beachfront,family-friendly,romantic',
      });
    }

    // Add instructions sheet
    const instructionsSheet = workbook.addWorksheet('Instructions');
    instructionsSheet.columns = [
      { header: 'Field', key: 'field', width: 20 },
      { header: 'Description', key: 'description', width: 60 },
      { header: 'Required', key: 'required', width: 15 },
      { header: 'Format', key: 'format', width: 30 },
    ];

    // Style the header row
    instructionsSheet.getRow(1).font = { bold: true };
    instructionsSheet.getRow(1).fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: 'FFE0E0E0' }
    };

    // Add instructions for each field
    const instructions = [
      { field: 'Name*', description: 'The name of the hotel. A URL-friendly handle will be automatically generated from this name (e.g., "Luxury Beach Resort" becomes "luxury-beach-resort"). Single value required.', required: 'Yes', format: 'Text (Single value)' },
      { field: 'Destination*', description: 'The name of the destination where the hotel is located. Must match a destination name exactly. Single value required.', required: 'Yes', format: 'Text (Single value, exact match)' },
      { field: 'Description', description: 'A detailed description of the hotel. Single value only.', required: 'No', format: 'Text (Single value)' },
      { field: 'Is Active', description: 'Whether the hotel is active and visible. Single value only.', required: 'No', format: 'TRUE or FALSE (Single value)' },
      { field: 'Featured', description: 'Whether the hotel is featured/highlighted. Single value only.', required: 'No', format: 'TRUE or FALSE (Single value)' },
      { field: 'Address', description: 'The street address of the hotel. Single value only.', required: 'No', format: 'Text (Single value)' },
      { field: 'City', description: 'The city where the hotel is located. Single value only.', required: 'No', format: 'Text (Single value)' },
      { field: 'Postal Code', description: 'The postal/zip code of the hotel. Single value only.', required: 'No', format: 'Text (Single value)' },
      { field: 'Phone Number', description: 'Contact phone number for the hotel. Single value only.', required: 'No', format: 'Text (Single value, with country code)' },
      { field: 'Email', description: 'Contact email for the hotel. Single value only.', required: 'No', format: 'Email address (Single value)' },
      { field: 'Website', description: 'Website URL for the hotel. Single value only.', required: 'No', format: 'URL (Single value, https://...)' },
      { field: 'Check In Time', description: 'Standard check-in time. Single value only.', required: 'No', format: 'HH:MM (Single value, 24-hour format)' },
      { field: 'Check Out Time', description: 'Standard check-out time. Single value only.', required: 'No', format: 'HH:MM (Single value, 24-hour format)' },
      { field: 'Rating (1-5)', description: 'Hotel rating from 1 to 5 stars. Single value only.', required: 'No', format: 'Number (Single value, 1-5)' },
      { field: 'Pets Allowed', description: 'Whether pets are allowed in the hotel. Single value only.', required: 'No', format: 'TRUE or FALSE (Single value)' },
      { field: 'Amenities', description: 'List of amenities offered by the hotel. Multiple values allowed.', required: 'No', format: 'Multiple values (comma-separated: WiFi,Pool,Spa)' },
      { field: 'Rules', description: 'Hotel rules and policies. Multiple values allowed.', required: 'No', format: 'Multiple values (comma-separated)' },
      { field: 'Safety Measures', description: 'Safety and security measures in place. Multiple values allowed.', required: 'No', format: 'Multiple values (comma-separated)' },
      { field: 'Notes', description: 'Additional notes or special information about the hotel. Single value only.', required: 'No', format: 'Text (Single value)' },
      { field: 'Tags', description: 'Tags to categorize the hotel. Multiple values allowed.', required: 'No', format: 'Multiple values (comma-separated: luxury,family-friendly)' },
    ];

    instructions.forEach(instruction => {
      instructionsSheet.addRow(instruction);
    });

    // Set content type and headers for Excel file download
    res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    res.setHeader('Content-Disposition', 'attachment; filename=hotel-import-template.xlsx');

    // Write the workbook to the response
    await workbook.xlsx.write(res);

  } catch (error) {
    console.error('Error generating template:', error);
    res.status(500).json({ message: 'Error generating template' });
  }
};
