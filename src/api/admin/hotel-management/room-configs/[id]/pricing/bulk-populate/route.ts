import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";
import { z } from "zod";
import { HOTEL_PRICING_MODULE } from "../../../../../../../modules/hotel-management/hotel-pricing";
import {
  applyDefaultsToAllDays,
  validateCostMarginData,
  type BasePriceRuleData
} from "../../../../../../../modules/hotel-management/hotel-pricing/utils/cost-margin-calculator";

// Validation schema for bulk population
export const PostAdminBulkPopulatePricing = z.object({
  rule_ids: z.array(z.string()).min(1, "At least one rule ID is required"),
  apply_gross_cost: z.boolean().default(true),
  apply_fixed_margin: z.boolean().default(true),
  apply_margin_percentage: z.boolean().default(true),
  // Optional: override default values for this operation
  override_defaults: z.object({
    gross_cost: z.number().min(0, "Gross cost must be non-negative").nullable().optional(),
    fixed_margin: z.number().nullable().optional(),
    margin_percentage: z.number().min(0, "Margin percentage must be non-negative").max(1000, "Margin percentage cannot exceed 1000%").nullable().optional(),
  }).optional(),
});

export type PostAdminBulkPopulatePricingType = z.infer<typeof PostAdminBulkPopulatePricing>;

/**
 * POST /admin/hotel-management/room-configs/:id/pricing/bulk-populate
 *
 * Apply default cost and margin values to all days of the week for specified pricing rules
 */
export const POST = async (req: MedusaRequest<PostAdminBulkPopulatePricingType>, res: MedusaResponse) => {
  try {
    const roomConfigId = req.params.id;

    if (!roomConfigId) {
      return res.status(400).json({ message: "Room configuration ID is required" });
    }

    const { 
      rule_ids, 
      apply_gross_cost, 
      apply_fixed_margin, 
      apply_margin_percentage,
      override_defaults 
    } = req.body;

    console.log(`Bulk populating pricing for ${rule_ids.length} rules in room config: ${roomConfigId}`);

    // Get the hotel pricing service
    const hotelPricingService = req.scope.resolve(HOTEL_PRICING_MODULE);

    const updatedRules = [];
    const errors = [];

    try {
      // Process each rule
      for (const ruleId of rule_ids) {
        try {
          // Retrieve the existing rule
          const existingRule = await hotelPricingService.retrieveBasePriceRule(ruleId);

          if (!existingRule) {
            errors.push({ rule_id: ruleId, error: "Rule not found" });
            continue;
          }

          // Verify the rule belongs to the specified room config
          if (existingRule.room_config_id !== roomConfigId) {
            errors.push({ rule_id: ruleId, error: "Rule does not belong to the specified room configuration" });
            continue;
          }

          // Prepare the rule data for bulk population
          const ruleData: BasePriceRuleData = {
            // Use override defaults if provided, otherwise use existing defaults
            default_gross_cost: override_defaults?.gross_cost ?? existingRule.default_gross_cost,
            default_fixed_margin: override_defaults?.fixed_margin ?? existingRule.default_fixed_margin,
            default_margin_percentage: override_defaults?.margin_percentage ?? existingRule.default_margin_percentage,
            
            // Include existing day-specific values
            monday_gross_cost: existingRule.monday_gross_cost,
            monday_fixed_margin: existingRule.monday_fixed_margin,
            monday_margin_percentage: existingRule.monday_margin_percentage,
            tuesday_gross_cost: existingRule.tuesday_gross_cost,
            tuesday_fixed_margin: existingRule.tuesday_fixed_margin,
            tuesday_margin_percentage: existingRule.tuesday_margin_percentage,
            wednesday_gross_cost: existingRule.wednesday_gross_cost,
            wednesday_fixed_margin: existingRule.wednesday_fixed_margin,
            wednesday_margin_percentage: existingRule.wednesday_margin_percentage,
            thursday_gross_cost: existingRule.thursday_gross_cost,
            thursday_fixed_margin: existingRule.thursday_fixed_margin,
            thursday_margin_percentage: existingRule.thursday_margin_percentage,
            friday_gross_cost: existingRule.friday_gross_cost,
            friday_fixed_margin: existingRule.friday_fixed_margin,
            friday_margin_percentage: existingRule.friday_margin_percentage,
            saturday_gross_cost: existingRule.saturday_gross_cost,
            saturday_fixed_margin: existingRule.saturday_fixed_margin,
            saturday_margin_percentage: existingRule.saturday_margin_percentage,
            sunday_gross_cost: existingRule.sunday_gross_cost,
            sunday_fixed_margin: existingRule.sunday_fixed_margin,
            sunday_margin_percentage: existingRule.sunday_margin_percentage,
          };

          // Check if we have default values to apply
          if (!ruleData.default_gross_cost && !override_defaults?.gross_cost) {
            errors.push({ rule_id: ruleId, error: "No default gross cost available to apply" });
            continue;
          }

          // Validate the data before applying
          try {
            validateCostMarginData(ruleData);
          } catch (validationError) {
            errors.push({ 
              rule_id: ruleId, 
              error: `Validation failed: ${validationError.message}` 
            });
            continue;
          }

          // Apply defaults to all days
          const updatedRuleData = applyDefaultsToAllDays(
            ruleData,
            apply_gross_cost,
            apply_fixed_margin,
            apply_margin_percentage
          );

          // Create history entry
          const historyEntry = {
            timestamp: new Date().toISOString(),
            action: "bulk_populated",
            applied_fields: {
              gross_cost: apply_gross_cost,
              fixed_margin: apply_fixed_margin,
              margin_percentage: apply_margin_percentage,
            },
            default_values_used: {
              gross_cost: ruleData.default_gross_cost,
              fixed_margin: ruleData.default_fixed_margin,
              margin_percentage: ruleData.default_margin_percentage,
            },
            override_defaults: override_defaults || null,
          };

          // Prepare update data
          const updateData = {
            id: ruleId,
            // Update only the cost/margin fields that were applied
            ...(apply_gross_cost && {
              monday_gross_cost: updatedRuleData.monday_gross_cost,
              tuesday_gross_cost: updatedRuleData.tuesday_gross_cost,
              wednesday_gross_cost: updatedRuleData.wednesday_gross_cost,
              thursday_gross_cost: updatedRuleData.thursday_gross_cost,
              friday_gross_cost: updatedRuleData.friday_gross_cost,
              saturday_gross_cost: updatedRuleData.saturday_gross_cost,
              sunday_gross_cost: updatedRuleData.sunday_gross_cost,
            }),
            ...(apply_fixed_margin && {
              monday_fixed_margin: updatedRuleData.monday_fixed_margin,
              tuesday_fixed_margin: updatedRuleData.tuesday_fixed_margin,
              wednesday_fixed_margin: updatedRuleData.wednesday_fixed_margin,
              thursday_fixed_margin: updatedRuleData.thursday_fixed_margin,
              friday_fixed_margin: updatedRuleData.friday_fixed_margin,
              saturday_fixed_margin: updatedRuleData.saturday_fixed_margin,
              sunday_fixed_margin: updatedRuleData.sunday_fixed_margin,
            }),
            ...(apply_margin_percentage && {
              monday_margin_percentage: updatedRuleData.monday_margin_percentage,
              tuesday_margin_percentage: updatedRuleData.tuesday_margin_percentage,
              wednesday_margin_percentage: updatedRuleData.wednesday_margin_percentage,
              thursday_margin_percentage: updatedRuleData.thursday_margin_percentage,
              friday_margin_percentage: updatedRuleData.friday_margin_percentage,
              saturday_margin_percentage: updatedRuleData.saturday_margin_percentage,
              sunday_margin_percentage: updatedRuleData.sunday_margin_percentage,
            }),
            // Update override defaults if provided
            ...(override_defaults && {
              default_gross_cost: override_defaults.gross_cost,
              default_fixed_margin: override_defaults.fixed_margin,
              default_margin_percentage: override_defaults.margin_percentage,
            }),
            metadata: {
              ...(existingRule.metadata || {}),
              last_updated: new Date().toISOString(),
              history: [...(existingRule.metadata?.history || []), historyEntry]
            }
          };

          // Update the rule
          const updatedRule = await hotelPricingService.updateBasePriceRules([updateData]);
          updatedRules.push(updatedRule[0]);

          console.log(`Successfully bulk populated rule ${ruleId}`);

        } catch (error) {
          console.error(`Error processing rule ${ruleId}:`, error);
          errors.push({ 
            rule_id: ruleId, 
            error: error instanceof Error ? error.message : "Unknown error" 
          });
        }
      }

      // Return results
      res.json({
        success: true,
        updated_rules: updatedRules,
        errors: errors,
        summary: {
          total_rules: rule_ids.length,
          successful_updates: updatedRules.length,
          failed_updates: errors.length,
          applied_fields: {
            gross_cost: apply_gross_cost,
            fixed_margin: apply_fixed_margin,
            margin_percentage: apply_margin_percentage,
          }
        }
      });

    } catch (error) {
      console.error("Error in bulk populate operation:", error);
      res.status(500).json({
        message: "Failed to bulk populate pricing rules",
        error: error instanceof Error ? error.message : "Unknown error"
      });
    }

  } catch (error) {
    console.error("Error in bulk populate endpoint:", error);
    res.status(400).json({
      message: error instanceof Error ? error.message : "Failed to bulk populate pricing rules",
    });
  }
};
