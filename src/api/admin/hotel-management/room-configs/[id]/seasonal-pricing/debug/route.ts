import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";
import { HOTEL_PRICING_MODULE } from "../../../../../../../modules/hotel-management/hotel-pricing";

/**
 * GET /admin/hotel-management/room-configs/:id/seasonal-pricing/debug
 * 
 * Debug endpoint to check all seasonal pricing rules for a room config
 */
export const GET = async (req: MedusaRequest, res: MedusaResponse) => {
  try {
    const roomConfigId = req.params.id;
    const currency_code = (req.query.currency_code as string) || 'GBP';

    if (!roomConfigId) {
      return res.status(400).json({ message: "Room configuration ID is required" });
    }

    console.log(`🔍 DEBUG: Checking seasonal pricing for room config: ${roomConfigId}`);

    // Get the hotel pricing service
    const hotelPricingService = req.scope.resolve(HOTEL_PRICING_MODULE);

    // Get all base price rules for this room config
    const basePriceRules = await hotelPricingService.listBasePriceRules({
      room_config_id: roomConfigId,
    });

    console.log(`Found ${basePriceRules.length} base price rules`);

    const debugInfo = {
      room_config_id: roomConfigId,
      currency_code: currency_code,
      base_price_rules_count: basePriceRules.length,
      seasonal_rules: [],
      unnamed_seasons: [],
      target_date_matches: []
    };

    // Check each base price rule for seasonal overrides
    for (const basePriceRule of basePriceRules) {
      const seasonalOverrides = await hotelPricingService.listSeasonalPriceRules({
        base_price_rule_id: basePriceRule.id,
      });

      console.log(`Base rule ${basePriceRule.id}: Found ${seasonalOverrides.length} seasonal overrides`);

      for (const override of seasonalOverrides) {
        const metadata = override.metadata as any;
        const seasonName = override.name || metadata?.name || "Unnamed Season";
        const overrideCurrency = override.currency_code || basePriceRule.currency_code;

        const seasonInfo = {
          seasonal_rule_id: override.id,
          base_price_rule_id: basePriceRule.id,
          occupancy_type_id: basePriceRule.occupancy_type_id,
          meal_plan_id: basePriceRule.meal_plan_id,
          season_name: seasonName,
          original_name_field: override.name,
          description_field: override.description,
          start_date: override.start_date,
          end_date: override.end_date,
          currency_code: overrideCurrency,
          amount: override.amount,
          priority: override.priority,
          metadata: metadata,
          created_at: override.created_at,
          updated_at: override.updated_at
        };

        debugInfo.seasonal_rules.push(seasonInfo);

        // Check if this is an unnamed season
        if (!override.name || override.name.trim() === '' || seasonName === 'Unnamed Season') {
          debugInfo.unnamed_seasons.push(seasonInfo);
        }

        // Check if this matches the target dates (2025-07-22 to 2025-07-29)
        const startDateStr = override.start_date.toISOString().split('T')[0];
        const endDateStr = override.end_date.toISOString().split('T')[0];
        
        if (startDateStr === '2025-07-22' && endDateStr === '2025-07-29') {
          debugInfo.target_date_matches.push({
            ...seasonInfo,
            is_target_match: true,
            reason: 'Exact date match for 2025-07-22 to 2025-07-29'
          });
        }
      }
    }

    // Summary
    const summary = {
      total_seasonal_rules: debugInfo.seasonal_rules.length,
      unnamed_seasons_count: debugInfo.unnamed_seasons.length,
      target_date_matches_count: debugInfo.target_date_matches.length,
      has_unnamed_season: debugInfo.unnamed_seasons.length > 0,
      has_target_date_conflict: debugInfo.target_date_matches.length > 0
    };

    console.log('🎯 DEBUG SUMMARY:', summary);

    res.json({
      success: true,
      summary,
      debug_info: debugInfo,
      recommendations: {
        can_create_new_season: debugInfo.target_date_matches.length === 0,
        should_update_existing: debugInfo.target_date_matches.length > 0,
        conflicting_seasons: debugInfo.target_date_matches.map(match => ({
          name: match.season_name,
          id: match.seasonal_rule_id,
          suggestion: match.season_name === 'Unnamed Season' 
            ? 'Update this unnamed season with your desired name'
            : 'This season already has a name, consider using a different date range'
        }))
      }
    });

  } catch (error) {
    console.error("Error in seasonal pricing debug:", error);
    res.status(500).json({
      success: false,
      message: "An error occurred while debugging seasonal pricing",
      error: error instanceof Error ? error.message : String(error),
    });
  }
};
