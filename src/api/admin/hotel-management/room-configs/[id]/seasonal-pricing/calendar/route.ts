import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";
import { z } from "zod";
import { HOTEL_PRICING_MODULE } from "../../../../../../../modules/hotel-management/hotel-pricing";

// Validation schema for calendar seasonal pricing query
export const GetAdminCalendarSeasonalPricing = z.object({
  start_date: z.string().regex(/^\d{4}-\d{2}-\d{2}$/, "Start date must be in YYYY-MM-DD format"),
  end_date: z.string().regex(/^\d{4}-\d{2}-\d{2}$/, "End date must be in YYYY-MM-DD format"),
  occupancy_type_id: z.string().optional(),
  meal_plan_id: z.string().optional(),
});

export type GetAdminCalendarSeasonalPricingType = z.infer<typeof GetAdminCalendarSeasonalPricing>;

/**
 * GET /admin/hotel-management/room-configs/:id/seasonal-pricing/calendar
 * 
 * Fetch seasonal pricing rules for a specific room configuration within a date range
 * Optimized for calendar view - only returns seasonal rules that overlap with the specified date range
 */
export const GET = async (req: MedusaRequest, res: MedusaResponse) => {
  console.log(`[CALENDAR SEASONAL API] 🗓️ Calendar seasonal pricing request - Room Config: ${req.params.id}`);
  
  try {
    const roomConfigId = req.params.id;

    if (!roomConfigId) {
      return res.status(400).json({ message: "Room configuration ID is required" });
    }

    // Parse and validate query parameters
    const queryValidation = GetAdminCalendarSeasonalPricing.safeParse(req.query);
    if (!queryValidation.success) {
      return res.status(400).json({
        message: "Invalid query parameters",
        errors: queryValidation.error.errors,
      });
    }

    const { start_date, end_date, occupancy_type_id, meal_plan_id } = queryValidation.data;

    console.log(`[CALENDAR SEASONAL API] 📅 Date range: ${start_date} to ${end_date}`);
    console.log(`[CALENDAR SEASONAL API] 🎯 Filters: occupancy=${occupancy_type_id}, meal_plan=${meal_plan_id}`);

    // Get the hotel pricing service
    const hotelPricingService = req.scope.resolve(HOTEL_PRICING_MODULE);

    // Get all base price rules for this room config
    const basePriceRules = await hotelPricingService.listBasePriceRules({
      room_config_id: roomConfigId,
    });

    console.log(`[CALENDAR SEASONAL API] 📋 Found ${basePriceRules.length} base price rules`);

    // Filter base price rules by occupancy and meal plan if specified
    const filteredBasePriceRules = basePriceRules.filter(rule => {
      if (occupancy_type_id && rule.occupancy_type_id !== occupancy_type_id) {
        return false;
      }
      if (meal_plan_id && rule.meal_plan_id !== meal_plan_id) {
        return false;
      }
      return true;
    });

    console.log(`[CALENDAR SEASONAL API] 🔍 Filtered to ${filteredBasePriceRules.length} base price rules`);

    // Collect all seasonal rules for the filtered base price rules
    const allSeasonalRules = [];
    
    for (const basePriceRule of filteredBasePriceRules) {
      const seasonalRules = await hotelPricingService.listSeasonalPriceRules({
        base_price_rule_id: basePriceRule.id,
      });

      // Filter seasonal rules to only include those that overlap with the requested date range
      const overlappingRules = seasonalRules.filter(rule => {
        const ruleStart = new Date(rule.start_date);
        const ruleEnd = new Date(rule.end_date);
        const queryStart = new Date(start_date);
        const queryEnd = new Date(end_date);

        // Check if the rule overlaps with the query range
        // Rule overlaps if: rule_start <= query_end AND rule_end >= query_start
        return ruleStart <= queryEnd && ruleEnd >= queryStart;
      });

      // Add base price rule context and extract metadata to each seasonal rule
      const enrichedRules = overlappingRules.map(rule => {
        // Extract weekday prices and cost/margin data from metadata (same as bulk API)
        const metadata = rule.metadata as any;
        const weekdayPrices = metadata?.weekday_prices || {};
        const defaultValues = metadata?.default_values || {};
        const weekdayValues = metadata?.weekday_values || {};

        console.log(`[CALENDAR SEASONAL API] 🔍 Processing rule ${rule.id}:`, {
          hasMetadata: !!metadata,
          weekdayPrices,
          defaultValues,
          weekdayValues
        });

        return {
          ...rule,
          // Enhanced: Include weekday prices with proper zero value handling (keep in cents for consistency)
          weekday_prices: {
            mon: weekdayPrices.mon !== null && weekdayPrices.mon !== undefined ? weekdayPrices.mon : (rule.amount || 0),
            tue: weekdayPrices.tue !== null && weekdayPrices.tue !== undefined ? weekdayPrices.tue : (rule.amount || 0),
            wed: weekdayPrices.wed !== null && weekdayPrices.wed !== undefined ? weekdayPrices.wed : (rule.amount || 0),
            thu: weekdayPrices.thu !== null && weekdayPrices.thu !== undefined ? weekdayPrices.thu : (rule.amount || 0),
            fri: weekdayPrices.fri !== null && weekdayPrices.fri !== undefined ? weekdayPrices.fri : (rule.amount || 0),
            sat: weekdayPrices.sat !== null && weekdayPrices.sat !== undefined ? weekdayPrices.sat : (rule.amount || 0),
            sun: weekdayPrices.sun !== null && weekdayPrices.sun !== undefined ? weekdayPrices.sun : (rule.amount || 0),
          },
          // Enhanced: Include default cost/margin values (keep in cents for consistency)
          default_values: {
            gross_cost: defaultValues.gross_cost || 0,
            fixed_margin: defaultValues.fixed_margin || 0,
            margin_percentage: defaultValues.margin_percentage || 0,
            total: defaultValues.total || rule.amount || 0,
          },
          // Enhanced: Include weekday-specific cost/margin values (keep in cents for consistency)
          weekday_values: {
            mon: weekdayValues.mon ? {
              gross_cost: weekdayValues.mon.gross_cost || 0,
              fixed_margin: weekdayValues.mon.fixed_margin || 0,
              margin_percentage: weekdayValues.mon.margin_percentage || 0
            } : { gross_cost: 0, fixed_margin: 0, margin_percentage: 0 },
            tue: weekdayValues.tue ? {
              gross_cost: weekdayValues.tue.gross_cost || 0,
              fixed_margin: weekdayValues.tue.fixed_margin || 0,
              margin_percentage: weekdayValues.tue.margin_percentage || 0
            } : { gross_cost: 0, fixed_margin: 0, margin_percentage: 0 },
            wed: weekdayValues.wed ? {
              gross_cost: weekdayValues.wed.gross_cost || 0,
              fixed_margin: weekdayValues.wed.fixed_margin || 0,
              margin_percentage: weekdayValues.wed.margin_percentage || 0
            } : { gross_cost: 0, fixed_margin: 0, margin_percentage: 0 },
            thu: weekdayValues.thu ? {
              gross_cost: weekdayValues.thu.gross_cost || 0,
              fixed_margin: weekdayValues.thu.fixed_margin || 0,
              margin_percentage: weekdayValues.thu.margin_percentage || 0
            } : { gross_cost: 0, fixed_margin: 0, margin_percentage: 0 },
            fri: weekdayValues.fri ? {
              gross_cost: weekdayValues.fri.gross_cost || 0,
              fixed_margin: weekdayValues.fri.fixed_margin || 0,
              margin_percentage: weekdayValues.fri.margin_percentage || 0
            } : { gross_cost: 0, fixed_margin: 0, margin_percentage: 0 },
            sat: weekdayValues.sat ? {
              gross_cost: weekdayValues.sat.gross_cost || 0,
              fixed_margin: weekdayValues.sat.fixed_margin || 0,
              margin_percentage: weekdayValues.sat.margin_percentage || 0
            } : { gross_cost: 0, fixed_margin: 0, margin_percentage: 0 },
            sun: weekdayValues.sun ? {
              gross_cost: weekdayValues.sun.gross_cost || 0,
              fixed_margin: weekdayValues.sun.fixed_margin || 0,
              margin_percentage: weekdayValues.sun.margin_percentage || 0
            } : { gross_cost: 0, fixed_margin: 0, margin_percentage: 0 },
          },
          base_price_rule: {
            id: basePriceRule.id,
            room_config_id: basePriceRule.room_config_id,
            occupancy_type_id: basePriceRule.occupancy_type_id,
            meal_plan_id: basePriceRule.meal_plan_id,
            currency_code: basePriceRule.currency_code,
          }
        };
      });

      allSeasonalRules.push(...enrichedRules);
    }

    console.log(`[CALENDAR SEASONAL API] 🎯 Found ${allSeasonalRules.length} overlapping seasonal rules`);

    // Sort by priority (highest first) and start date
    allSeasonalRules.sort((a, b) => {
      // First sort by priority (higher priority first)
      const priorityDiff = (b.priority || 100) - (a.priority || 100);
      if (priorityDiff !== 0) return priorityDiff;
      
      // Then sort by start date (earlier first)
      return new Date(a.start_date).getTime() - new Date(b.start_date).getTime();
    });

    const response = {
      room_config_id: roomConfigId,
      date_range: {
        start_date,
        end_date,
      },
      filters: {
        occupancy_type_id: occupancy_type_id || null,
        meal_plan_id: meal_plan_id || null,
      },
      seasonal_rules: allSeasonalRules,
      total_count: allSeasonalRules.length,
    };

    console.log(`[CALENDAR SEASONAL API] ✅ Returning ${allSeasonalRules.length} seasonal rules`);

    return res.status(200).json(response);

  } catch (error) {
    console.error("[CALENDAR SEASONAL API] ❌ Error fetching calendar seasonal pricing:", error);
    return res.status(500).json({
      message: "Internal server error",
      error: error instanceof Error ? error.message : "Unknown error",
    });
  }
};
