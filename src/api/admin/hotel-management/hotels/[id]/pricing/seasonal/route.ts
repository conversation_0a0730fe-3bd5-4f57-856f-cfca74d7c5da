import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";
import { z } from "zod";
import { HOTEL_PRICING_MODULE } from "../../../../../../../modules/hotel-management/hotel-pricing";

// Validation schema for creating seasonal pricing
export const PostAdminCreateSeasonalPricing = z.object({
  name: z.string().min(1, "Season name is required"),
  start_date: z.string().regex(/^\d{4}-\d{2}-\d{2}$/, "Start date must be in YYYY-MM-DD format"),
  end_date: z.string().regex(/^\d{4}-\d{2}-\d{2}$/, "End date must be in YYYY-MM-DD format"),
  currency_code: z.string().optional(),
});

export type PostAdminCreateSeasonalPricingType = z.infer<typeof PostAdminCreateSeasonalPricing>;

/**
 * GET /admin/hotel-management/hotels/:id/pricing/seasonal
 * 
 * Get all seasonal pricing periods for a hotel
 */
export const GET = async (req: MedusaRequest, res: MedusaResponse) => {
  try {
    const hotelId = req.params.id;

    if (!hotelId) {
      return res.status(400).json({ message: "Hotel ID is required" });
    }

    console.log(`Fetching seasonal pricing periods for hotel: ${hotelId}`);

    // Get the hotel pricing service
    const hotelPricingService = req.scope.resolve(HOTEL_PRICING_MODULE);

    // Get currency_code from query parameters (if provided, filter by currency; otherwise get all)
    const currency_code = req.query.currency_code as string;

    // Get all base price rules for this hotel
    const basePriceRulesFilter: any = { hotel_id: hotelId };
    if (currency_code) {
      basePriceRulesFilter.currency_code = currency_code;
    }

    const basePriceRules = await hotelPricingService.listBasePriceRules(basePriceRulesFilter);

    // Get all seasonal overrides and group them by season
    const seasonalGroups = new Map();

    for (const basePriceRule of basePriceRules) {
      const seasonalOverrides = await hotelPricingService.listSeasonalPriceRules({
        base_price_rule_id: basePriceRule.id,
      });

      for (const override of seasonalOverrides) {
        const metadata = override.metadata as any;
        const seasonName = override.name || metadata?.name || "Unnamed Season";
        const startDate = override.start_date;
        const endDate = override.end_date;
        const overrideCurrency = override.currency_code || basePriceRule.currency_code;

        // Create a unique key for this season
        const key = `${seasonName}-${startDate}-${endDate}-${overrideCurrency}`;

        if (!seasonalGroups.has(key)) {
          seasonalGroups.set(key, {
            id: override.id, // Use the first override's ID as the season ID
            name: seasonName,
            start_date: startDate,
            end_date: endDate,
            currency_code: overrideCurrency,
          });
        }
      }
    }

    // Convert the map to an array
    const seasonalPeriods = Array.from(seasonalGroups.values());

    console.log("GET seasonal periods - returning data:", JSON.stringify(seasonalPeriods, null, 2));
    
    res.json({
      seasonal_periods: seasonalPeriods,
    });
  } catch (error) {
    console.error("Error fetching seasonal pricing periods:", error);
    res.status(500).json({
      message: "An error occurred while fetching seasonal pricing periods",
      error: error instanceof Error ? error.message : String(error),
    });
  }
};

/**
 * POST /admin/hotel-management/hotels/:id/pricing/seasonal
 * 
 * Create a new seasonal pricing period for a hotel
 */
export const POST = async (req: MedusaRequest<PostAdminCreateSeasonalPricingType>, res: MedusaResponse) => {
  try {
    const hotelId = req.params.id;

    if (!hotelId) {
      return res.status(400).json({ message: "Hotel ID is required" });
    }

    // Validate the request body
    const validationResult = PostAdminCreateSeasonalPricing.safeParse(req.body);
    if (!validationResult.success) {
      return res.status(400).json({
        message: "Invalid request data",
        errors: validationResult.error.errors,
      });
    }

    const { name, start_date, end_date, currency_code } = validationResult.data;

    console.log(`Creating seasonal pricing period for hotel: ${hotelId}`);
    console.log(`Season: ${name}, ${start_date} to ${end_date}`);

    // Get the hotel pricing service
    const hotelPricingService = req.scope.resolve(HOTEL_PRICING_MODULE);

    // Enhanced validation: Check for duplicate seasons and overlapping date ranges
    const basePriceRulesFilter: any = { hotel_id: hotelId };
    if (currency_code) {
      basePriceRulesFilter.currency_code = currency_code;
    }

    const basePriceRules = await hotelPricingService.listBasePriceRules(basePriceRulesFilter);

    // Check for existing seasons with the same name or overlapping dates
    const existingSeasons = new Map();
    
    for (const basePriceRule of basePriceRules) {
      const seasonalOverrides = await hotelPricingService.listSeasonalPriceRules({
        base_price_rule_id: basePriceRule.id,
      });

      for (const override of seasonalOverrides) {
        const metadata = override.metadata as any;
        const seasonName = override.name || metadata?.name || "Unnamed Season";
        const startDate = new Date(override.start_date);
        const endDate = new Date(override.end_date);
        const overrideCurrency = override.currency_code || basePriceRule.currency_code;

        // Only check seasons in the same currency (or if no currency specified)
        if (currency_code && overrideCurrency !== currency_code) {
          continue;
        }

        const key = `${seasonName}-${override.start_date}-${override.end_date}-${overrideCurrency}`;
        
        if (!existingSeasons.has(key)) {
          existingSeasons.set(key, {
            name: seasonName,
            start_date: override.start_date,
            end_date: override.end_date,
            currency_code: overrideCurrency,
          });
        }
      }
    }

    const existingSeasonsArray = Array.from(existingSeasons.values());

    // Check for duplicate name (case-insensitive)
    const duplicateName = existingSeasonsArray.find(
      season => season.name.toLowerCase() === name.toLowerCase()
    );

    if (duplicateName) {
      return res.status(400).json({
        message: "Validation failed",
        error: `A season with the name "${name}" already exists.`,
      });
    }

    // Check for overlapping date ranges
    const newStartDate = new Date(start_date);
    const newEndDate = new Date(end_date);

    for (const existingSeason of existingSeasonsArray) {
      const existingStartDate = new Date(existingSeason.start_date);
      const existingEndDate = new Date(existingSeason.end_date);

      // Check if date ranges overlap
      const hasOverlap = (
        (newStartDate <= existingEndDate && newStartDate >= existingStartDate) ||
        (newEndDate <= existingEndDate && newEndDate >= existingStartDate) ||
        (newStartDate <= existingStartDate && newEndDate >= existingEndDate)
      );

      if (hasOverlap) {
        return res.status(400).json({
          message: "Validation failed",
          error: `Date range overlaps with existing season "${existingSeason.name}" (${existingSeason.start_date} to ${existingSeason.end_date}).`,
        });
      }
    }

    // Validate date range
    if (newStartDate >= newEndDate) {
      return res.status(400).json({
        message: "Validation failed",
        error: "End date must be after start date.",
      });
    }

    // If validation passes, create a basic seasonal period entry
    // Note: The actual pricing rules will be created when prices are set via the bulk API
    const seasonalPeriod = {
      id: `season_${Date.now()}_${Math.random().toString(36).substring(2, 15)}`,
      name,
      start_date,
      end_date,
      currency_code: currency_code || "GBP", // Default currency
      created_at: new Date().toISOString(),
    };

    console.log(`✅ Successfully validated and created seasonal period: ${name}`);

    res.status(201).json({
      message: "Seasonal period created successfully",
      seasonal_period: seasonalPeriod,
    });
  } catch (error) {
    console.error("Error creating seasonal pricing period:", error);
    res.status(500).json({
      message: "An error occurred while creating seasonal pricing period",
      error: error instanceof Error ? error.message : String(error),
    });
  }
};
