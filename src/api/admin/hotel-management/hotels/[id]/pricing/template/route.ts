import {
  MedusaRequest,
  MedusaResponse,
} from "@camped-ai/framework/http";
import { z } from "zod";
import * as ExcelJS from 'exceljs';
import { HOTEL_PRICING_MODULE } from "src/modules/hotel-management/hotel-pricing";

// Query validation schema
const GetAdminHotelPricingTemplateQuery = z.object({
  currency: z.string().optional().default("CHF"),
  // Enhanced template generation parameters
  from_date: z.string().optional(), // Start date for date range
  to_date: z.string().optional(),   // End date for date range
  hotel_ids: z.string().optional(), // Comma-separated hotel IDs for multi-hotel templates
});

type GetAdminHotelPricingTemplateQueryType = z.infer<typeof GetAdminHotelPricingTemplateQuery>;

/**
 * GET /admin/hotel-management/hotels/[id]/pricing/template
 * Generate Excel template for hotel pricing import
 */
export const GET = async (
  req: MedusaRequest<{}, GetAdminHotelPricingTemplateQueryType>,
  res: MedusaResponse
) => {
  try {
    const hotelId = req.params.id;
    const query = req.scope.resolve("query");
    const hotelPricingService = req.scope.resolve(HOTEL_PRICING_MODULE);

    // Parse and validate query parameters
    const validatedQuery = GetAdminHotelPricingTemplateQuery.parse(req.query);
    const { currency, from_date, to_date, hotel_ids } = validatedQuery;

    console.log(`[Pricing Template API] Generating template for hotel: ${hotelId}, currency: ${currency}, date range: ${from_date} to ${to_date}, additional hotels: ${hotel_ids}`);

    // Determine which hotels to include in the template
    const targetHotelIds = hotel_ids
      ? [hotelId, ...hotel_ids.split(',').filter(id => id.trim() && id.trim() !== hotelId)]
      : [hotelId];

    // Get hotel details for all target hotels
    const { data: hotels } = await query.graph({
      entity: "hotel",
      filters: { id: targetHotelIds },
      fields: ["id", "name", "handle"],
    });

    if (!hotels || hotels.length === 0) {
      return res.status(404).json({
        message: "No hotels found",
        type: "not_found",
      });
    }

    console.log(`[Pricing Template API] Found ${hotels.length} hotels for template generation`);

    // Get room configurations for all target hotels
    const { data: allProducts } = await query.graph({
      entity: "product",
      filters: {},
      fields: ["id", "title", "handle", "metadata"],
    });

    const roomConfigs = allProducts?.filter(product =>
      product.metadata &&
      (targetHotelIds.includes(product.metadata.hotel_id as string) || product.metadata.type === "room_config")
    ) || [];

    // Get occupancy configurations for all target hotels
    const { data: occupancyConfigs } = await query.graph({
      entity: "occupancy_config",
      filters: { hotel_id: targetHotelIds },
      fields: ["id", "name", "hotel_id"],
    });

    // Get meal plans for all target hotels
    const allMealPlans = [];
    for (const hotel of hotels) {
      const mealPlans = await hotelPricingService.listMealPlans({
        hotel_id: hotel.id,
      });
      allMealPlans.push(...mealPlans.map(mp => ({ ...mp, hotel_id: hotel.id })));
    }

    console.log(`[Pricing Template API] Found ${roomConfigs.length} room configs, ${occupancyConfigs.length} occupancy configs, ${allMealPlans.length} meal plans across ${hotels.length} hotels`);

    // Generate date range if provided
    const dateRanges = [];
    if (from_date && to_date) {
      const startDate = new Date(from_date);
      const endDate = new Date(to_date);

      // Validate dates
      if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
        return res.status(400).json({
          message: "Invalid date format. Please use YYYY-MM-DD format.",
          type: "validation_error",
        });
      }

      if (startDate > endDate) {
        return res.status(400).json({
          message: "From date must be before or equal to To date.",
          type: "validation_error",
        });
      }

      dateRanges.push({
        from: from_date,
        to: to_date,
        label: `${from_date} to ${to_date}`
      });
    } else {
      // Default to no specific date range - template will have empty date fields
      dateRanges.push({
        from: '',
        to: '',
        label: 'Custom Date Range'
      });
    }

    // Create workbook
    const workbook = new ExcelJS.Workbook();
    workbook.creator = 'Hotel Management System';
    workbook.created = new Date();

    // Create main pricing sheet
    const worksheet = workbook.addWorksheet('Hotel Pricing Import');

    // Define columns - updated to match requirements
    const columns = [
      { header: 'Date From', key: 'date_from', width: 15 },
      { header: 'Date To', key: 'date_to', width: 15 },
      { header: 'Hotel Name', key: 'hotel_name', width: 25 },
      { header: 'Room Type', key: 'room_config_name', width: 25 },
      { header: 'Occupancy Type', key: 'occupancy_name', width: 20 },
      { header: 'Meal Plan', key: 'meal_plan_name', width: 25 },
      { header: 'Cost', key: 'cost', width: 15 },
      { header: 'Fixed Margin', key: 'fixed_margin', width: 15 },
      { header: 'Margin Percentage', key: 'margin_percentage', width: 18 },

    ];

    worksheet.columns = columns;

    // Style the header row
    const headerRow = worksheet.getRow(1);
    headerRow.font = { bold: true, color: { argb: 'FFFFFF' } };
    headerRow.fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: '4472C4' }
    };
    headerRow.alignment = { horizontal: 'center', vertical: 'middle' };
    headerRow.height = 25;

    // Generate all possible combinations: Date Range × Hotel × Room Type × Occupancy Type × Meal Plan
    const templateRows = [];

    // Create combinations for each date range
    for (const dateRange of dateRanges) {
      // For each hotel
      for (const hotel of hotels) {
        // Get room configs for this hotel
        const hotelRoomConfigs = roomConfigs.filter(rc =>
          rc.metadata && rc.metadata.hotel_id === hotel.id
        );

        // Get occupancy configs for this hotel
        const hotelOccupancyConfigs = occupancyConfigs.filter(oc =>
          oc.hotel_id === hotel.id
        );

        // Get meal plans for this hotel
        const hotelMealPlans = allMealPlans.filter(mp =>
          mp.hotel_id === hotel.id
        );

        // If no specific configurations found, use defaults
        const roomConfigsToUse = hotelRoomConfigs.length > 0 ? hotelRoomConfigs :
          [{ title: 'Standard Room', metadata: { hotel_id: hotel.id } }];
        const occupancyConfigsToUse = hotelOccupancyConfigs.length > 0 ? hotelOccupancyConfigs :
          [{ name: '2 Adults', hotel_id: hotel.id }];
        const mealPlansToUse = hotelMealPlans.length > 0 ? hotelMealPlans :
          [{ name: 'Room Only', hotel_id: hotel.id }];

        // Generate all combinations for this hotel
        for (const roomConfig of roomConfigsToUse) {
          for (const occupancyConfig of occupancyConfigsToUse) {
            for (const mealPlan of mealPlansToUse) {
              templateRows.push({
                date_from: dateRange.from,
                date_to: dateRange.to,
                hotel_name: hotel.name,
                room_config_name: roomConfig.title,
                occupancy_name: occupancyConfig.name,
                meal_plan_name: mealPlan.name,
                cost: '', // Empty - user input required (values in pence/cents)
                fixed_margin: '', // Empty - user input required
                margin_percentage: '', // Empty - user input required (2 decimal places)
              });
            }
          }
        }
      }
    }

    // Add template rows to worksheet
    templateRows.forEach(row => {
      worksheet.addRow(row);
    });

    // Style data rows
    for (let i = 2; i <= templateRows.length + 1; i++) {
      const row = worksheet.getRow(i);
      row.alignment = { horizontal: 'left', vertical: 'middle' };

      // Alternate row colors
      if (i % 2 === 0) {
        row.fill = {
          type: 'pattern',
          pattern: 'solid',
          fgColor: { argb: 'F8F9FA' }
        };
      }
    }

    // Add borders to all cells
    const range = `A1:${String.fromCharCode(65 + columns.length - 1)}${templateRows.length + 1}`;
    worksheet.getCell(range).border = {
      top: { style: 'thin' },
      left: { style: 'thin' },
      bottom: { style: 'thin' },
      right: { style: 'thin' }
    };

    // Create reference sheets
    
    // Room Configs sheet
    const roomConfigsSheet = workbook.addWorksheet('Room Configs Reference');
    roomConfigsSheet.columns = [
      { header: 'Room Config Name', key: 'name', width: 30 },
      { header: 'Room Config ID', key: 'id', width: 30 },
    ];
    
    roomConfigsSheet.getRow(1).font = { bold: true };
    roomConfigs.forEach(rc => {
      roomConfigsSheet.addRow({ name: rc.title, id: rc.id });
    });

    // Occupancy Configs sheet
    const occupancySheet = workbook.addWorksheet('Occupancy Reference');
    occupancySheet.columns = [
      { header: 'Occupancy Name', key: 'name', width: 25 },
      { header: 'Occupancy ID', key: 'id', width: 30 },
    ];
    
    occupancySheet.getRow(1).font = { bold: true };
    occupancyConfigs.forEach(oc => {
      occupancySheet.addRow({ name: oc.name, id: oc.id });
    });

    // Meal Plans sheet
    const mealPlansSheet = workbook.addWorksheet('Meal Plans Reference');
    mealPlansSheet.columns = [
      { header: 'Meal Plan Name', key: 'name', width: 30 },
      { header: 'Meal Plan ID', key: 'id', width: 30 },
    ];
    
    mealPlansSheet.getRow(1).font = { bold: true };
    allMealPlans.forEach(mp => {
      mealPlansSheet.addRow({ name: mp.name, id: mp.id });
    });

    // Instructions sheet
    const instructionsSheet = workbook.addWorksheet('Instructions');
    instructionsSheet.columns = [
      { header: 'Import Instructions', key: 'instruction', width: 80 },
    ];

    const instructions = [
      'Hotel Pricing Import Template Instructions',
      '',
      '1. Fill in the "Hotel Pricing Import" sheet with your pricing data',
      '2. Use exact names from the reference sheets for Room Configs, Occupancy, and Meal Plans',
      '3. Currency Code should match your hotel\'s supported currencies (e.g., CHF, USD, EUR)',
      '4. Prices should be entered as decimal numbers (e.g., 150.00, not 150)',
      '5. Leave price fields empty if not applicable for that day',
      '6. Do not modify the column headers',
      '7. You can add more rows as needed',
      '',
      'Required Columns:',
      '- Room Config Name: Must match exactly with names in "Room Configs Reference" sheet',
      '- Occupancy Name: Must match exactly with names in "Occupancy Reference" sheet', 
      '- Meal Plan Name: Must match exactly with names in "Meal Plans Reference" sheet',
      '- Currency Code: 3-letter currency code (e.g., CHF, USD, EUR)',
      '- Weekday Prices: Monday through Sunday pricing (optional, can be left empty)',
      '',
      'Tips:',
      '- Use the reference sheets to copy exact names to avoid errors',
      '- Prices will be validated to ensure they are positive numbers',
      '- Existing pricing rules will be updated if they already exist',
      '- New pricing rules will be created for new combinations',
    ];

    instructionsSheet.getRow(1).font = { bold: true, size: 14 };
    instructions.forEach((instruction, index) => {
      const row = instructionsSheet.addRow({ instruction });
      if (index === 0) {
        row.font = { bold: true, size: 14 };
      } else if (instruction.endsWith(':')) {
        row.font = { bold: true };
      }
    });

    // Generate filename
    const timestamp = new Date().toISOString().split('T')[0];
    const hotelNames = hotels.length === 1
      ? hotels[0].name.replace(/[^a-zA-Z0-9]/g, '_')
      : `${hotels.length}_hotels`;
    const dateRangeStr = from_date && to_date ? `_${from_date}_to_${to_date}` : '';
    const filename = `hotel_pricing_import_template_${hotelNames}_${currency}${dateRangeStr}_${timestamp}.xlsx`;

    // Set response headers
    res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);

    console.log(`[Pricing Template API] Generated template with ${templateRows.length} template rows for ${hotels.length} hotels`);

    // Write workbook to response
    await workbook.xlsx.write(res);
  } catch (error) {
    console.error('Error generating pricing template:', error);
    return res.status(500).json({
      message: "Failed to generate template",
      type: "internal_error",
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
};
