import {
  MedusaRequest,
  MedusaResponse,
} from "@camped-ai/framework/http";
import { z } from "zod";
import * as ExcelJS from 'exceljs';
import { HOTEL_PRICING_MODULE } from "src/modules/hotel-management/hotel-pricing";
import { calculateTotalFromCostMargin } from "src/modules/hotel-management/hotel-pricing/utils/cost-margin-calculator";

// Query validation schema for export options
const GetAdminExportHotelPricingQuery = z.object({
  format: z.enum(["csv", "xlsx", "excel"]).optional().default("xlsx"),
  currency: z.string().optional().default("CHF"),
  include_seasonal: z.enum(["true", "false"]).optional().default("true"),
  include_cost_margin: z.enum(["true", "false"]).optional().default("true"),
  room_config_ids: z.string().optional(), // Comma-separated room config IDs
  meal_plan_ids: z.string().optional(), // Comma-separated meal plan IDs
  occupancy_config_ids: z.string().optional(), // Comma-separated occupancy config IDs
  season_ids: z.string().optional(), // Comma-separated season IDs
  columns: z.string().optional(), // Comma-separated column names to include
  start_date: z.string().optional(), // For seasonal data filtering
  end_date: z.string().optional(), // For seasonal data filtering
});

type GetAdminExportHotelPricingQueryType = z.infer<typeof GetAdminExportHotelPricingQuery>;

/**
 * GET /admin/hotel-management/hotels/[id]/pricing/export
 * Export hotel pricing data in CSV or Excel format with configurable options
 */
export const GET = async (
  req: MedusaRequest<{}, GetAdminExportHotelPricingQueryType>,
  res: MedusaResponse
) => {
  try {
    const hotelId = req.params.id;
    const query = req.scope.resolve("query");
    const hotelPricingService = req.scope.resolve(HOTEL_PRICING_MODULE);

    // Parse and validate query parameters
    const validatedQuery = GetAdminExportHotelPricingQuery.parse(req.query);
    const {
      format,
      currency,
      include_seasonal,
      include_cost_margin,
      room_config_ids,
      meal_plan_ids,
      occupancy_config_ids,
      season_ids,
      columns,
      start_date,
      end_date
    } = validatedQuery;

    console.log(
      `[Pricing Export API] Exporting pricing for hotel: ${hotelId}, currency: ${currency}, format: ${format}`
    );
    console.log(`[Pricing Export API] Query parameters:`, validatedQuery);

    // Get hotel details
    const { data: hotel } = await query.graph({
      entity: "hotel",
      filters: {
        id: [hotelId],
      },
      fields: ["id", "name", "handle"],
    });

    if (!hotel || hotel.length === 0) {
      return res.status(404).json({
        message: "Hotel not found",
        type: "not_found",
      });
    }

    // Get room configurations with optional filtering
    let roomConfigFilters: any = { hotel_id: hotelId };
    let selectedRoomConfigIds: string[] = [];
    if (room_config_ids) {
      selectedRoomConfigIds = room_config_ids.split(',').map(id => id.trim()).filter(Boolean);
      if (selectedRoomConfigIds.length > 0) {
        roomConfigFilters.id = selectedRoomConfigIds;
      }
    }
    console.log(`[Pricing Export API] Room config filters:`, roomConfigFilters);
    console.log(`[Pricing Export API] Selected room config IDs:`, selectedRoomConfigIds);

    // Get room configurations (which are actually products with room_config metadata)
    let productFilters: any = {};
    if (selectedRoomConfigIds.length > 0) {
      productFilters.id = selectedRoomConfigIds;
    }

    const { data: allProducts } = await query.graph({
      entity: "product",
      filters: productFilters,
      fields: ["id", "title", "handle", "metadata"],
    });

    // Filter to only room config products
    const roomConfigs = allProducts?.filter(product =>
      product.metadata &&
      (product.metadata.hotel_id === hotelId || product.metadata.type === "room_config")
    ) || [];

    console.log(`[Pricing Export API] Found ${roomConfigs?.length || 0} room configs:`, roomConfigs?.map(rc => ({ id: rc.id, title: rc.title })));

    // Get occupancy configurations
    const { data: occupancyConfigs } = await query.graph({
      entity: "occupancy_config",
      filters: { hotel_id: hotelId },
      fields: ["id", "name"],
    });

    // Get meal plans with optional filtering
    let mealPlanFilters: any = { hotel_id: hotelId };
    let selectedMealPlanIds: string[] = [];
    if (meal_plan_ids) {
      selectedMealPlanIds = meal_plan_ids.split(',').map(id => id.trim()).filter(Boolean);
      if (selectedMealPlanIds.length > 0) {
        mealPlanFilters.id = selectedMealPlanIds;
      }
    }
    console.log(`[Pricing Export API] Meal plan filters:`, mealPlanFilters);
    console.log(`[Pricing Export API] Selected meal plan IDs:`, selectedMealPlanIds);

    const mealPlans = await hotelPricingService.listMealPlans(mealPlanFilters);
    console.log(`[Pricing Export API] Found ${mealPlans?.length || 0} meal plans:`, mealPlans?.map(mp => ({ id: mp.id, name: mp.name })));

    // Create lookup maps
    const occupancyMap = new Map(occupancyConfigs.map(oc => [oc.id, oc]));
    const mealPlanMap = new Map(mealPlans.map(mp => [mp.id, mp]));

    // Collect export data
    const exportRows: any[] = [];

    // Get all existing base price rules for this hotel and currency
    const allBasePriceRules = await hotelPricingService.listBasePriceRules({});
    const basePriceRulesForCurrency = allBasePriceRules.filter(rule =>
      rule.currency_code === currency &&
      roomConfigs.some(rc => rc.id === rule.room_config_id)
    );

    // Create a map of existing pricing rules for quick lookup
    const pricingRulesMap = new Map();
    basePriceRulesForCurrency.forEach(rule => {
      const key = `${rule.room_config_id}_${rule.occupancy_type_id}_${rule.meal_plan_id || 'null'}`;
      pricingRulesMap.set(key, rule);
    });

    console.log(`[Pricing Export API] Found ${basePriceRulesForCurrency.length} existing pricing rules for currency ${currency}`);

    // Helper function to get available meal plans for an occupancy type (same logic as UI)
    const getAvailableMealPlans = (occupancy: any) => {
      console.log(`[Pricing Export API] Checking occupancy: ${occupancy.name} (type: ${occupancy.type})`);

      // Logic for special accommodations (Extra Bed and Baby Cot)
      const isExtraBed = (occupancy as any).type === "EXTRA_BED" ||
                        occupancy.name?.toLowerCase().includes("extra bed");
      const isCot = (occupancy as any).type === "COT" ||
                   occupancy.name?.toLowerCase().includes("cot") ||
                   occupancy.name?.toLowerCase().includes("baby cot");

      console.log(`[Pricing Export API] isExtraBed: ${isExtraBed}, isCot: ${isCot}`);

      if (isExtraBed || isCot) {
        console.log(`[Pricing Export API] Finding "No Meals" meal plan for ${occupancy.name}`);

        // Find the "No Meals" meal plan instead of returning a fake option
        const noMealsPlan = mealPlans.find(
          (mp: any) =>
            mp.name?.toLowerCase().includes("no meals") ||
            mp.name?.toLowerCase().includes("none") ||
            mp.type === "none"
        );

        if (noMealsPlan) {
          console.log(`[Pricing Export API] Found "No Meals" plan: ${noMealsPlan.name}`);
          return [noMealsPlan];
        }

        // Fallback if no "No Meals" plan exists
        console.log(`[Pricing Export API] No "No Meals" plan found, using fallback`);
        return [{ id: null, name: 'No Meals', type: 'none' }];
      }

      // Filter meal plans based on their applicable_occupancy_types metadata
      const availableMealPlans = mealPlans.filter(mealPlan => {
        const applicableTypes = mealPlan.metadata?.applicable_occupancy_types as string[] | undefined;

        // If no applicable types specified, meal plan is available for all occupancy types
        if (!applicableTypes || applicableTypes.length === 0) {
          return true;
        }

        // Check if this occupancy type is in the applicable types
        return applicableTypes.includes(occupancy.id);
      });

      // If no meal plans are available, show only null meal plan
      if (availableMealPlans.length === 0) {
        console.log(`[Pricing Export API] No meal plans available for ${occupancy.name}, returning null meal plan`);
        return [{ id: null, name: '', type: 'none' }];
      }

      // Return only the available meal plans (no null option for regular occupancy types)
      console.log(`[Pricing Export API] Returning ${availableMealPlans.length} meal plans for ${occupancy.name}:`, availableMealPlans.map(mp => mp.name));
      return availableMealPlans;
    };

    // Generate all possible combinations of room configs, occupancy configs, and meal plans
    for (const roomConfig of roomConfigs) {
      try {
        console.log(`[Pricing Export API] Processing room config: ${roomConfig.id} (${roomConfig.title})`);

        // Process each occupancy configuration
        for (const occupancy of occupancyConfigs) {
          console.log(`[Pricing Export API] Processing occupancy: ${occupancy.name} (${occupancy.id})`);
          // Get available meal plans for this occupancy type (using same logic as UI)
          const mealPlansToProcess = getAvailableMealPlans(occupancy);
          console.log(`[Pricing Export API] Got ${mealPlansToProcess.length} meal plans for occupancy ${occupancy.name}`);

          for (const mealPlan of mealPlansToProcess) {
            // Skip if meal plan filtering is applied and this meal plan is not selected
            if (selectedMealPlanIds.length > 0 && mealPlan.id && !selectedMealPlanIds.includes(mealPlan.id)) {
              continue;
            }

            // Look up existing pricing rule for this combination
            const ruleKey = `${roomConfig.id}_${occupancy.id}_${mealPlan.id || 'null'}`;
            const existingRule = pricingRulesMap.get(ruleKey);

            // Create base row with all possible data
            const baseRow = {
              hotel_name: hotel[0].name,
              hotel_id: hotel[0].id,
              room_config_name: roomConfig.title.trim(),
              room_config_id: roomConfig.id,
              room_type: (roomConfig.metadata as any)?.room_type || '',
              max_occupancy: (roomConfig.metadata as any)?.max_occupancy || 0,
              max_cots: (roomConfig.metadata as any)?.max_cots || 0,
              occupancy_id: occupancy?.id || '',
              occupancy_name: occupancy?.name || 'Unknown',
              occupancy_adults: (occupancy as any)?.adults || 0,
              occupancy_children: (occupancy as any)?.children || 0,
              occupancy_infants: (occupancy as any)?.infants || 0,
              meal_plan_id: mealPlan?.id || '',
              meal_plan_name: mealPlan?.name || '-',
              meal_plan_type: mealPlan?.type || 'none',
              pricing_rule_id: existingRule?.id || '',
              pricing_type: 'Base Pricing',
              seasonal_period: 'Base Price',
              seasonal_start_date: '',
              seasonal_end_date: '',
              currency_code: currency,
              priority: 1, // Base pricing has priority 1

              // Weekday prices - use existing data or empty values
              monday_price: existingRule ? (existingRule.monday_price || existingRule.amount) / 100 : '',
              tuesday_price: existingRule ? (existingRule.tuesday_price || existingRule.amount) / 100 : '',
              wednesday_price: existingRule ? (existingRule.wednesday_price || existingRule.amount) / 100 : '',
              thursday_price: existingRule ? (existingRule.thursday_price || existingRule.amount) / 100 : '',
              friday_price: existingRule ? (existingRule.friday_price || existingRule.amount) / 100 : '',
              saturday_price: existingRule ? (existingRule.saturday_price || existingRule.amount) / 100 : '',
              sunday_price: existingRule ? (existingRule.sunday_price || existingRule.amount) / 100 : '',
            };

            // Add cost/margin data if requested
            if (include_cost_margin === "true") {
              if (existingRule) {
                // Default cost/margin values (convert from cents to currency units)
                const defaultGrossCost = existingRule.default_gross_cost ? existingRule.default_gross_cost / 100 : null;
                const defaultFixedMargin = existingRule.default_fixed_margin ? existingRule.default_fixed_margin / 100 : null;
                const defaultMarginPercentage = existingRule.default_margin_percentage || null;

                (baseRow as any).default_gross_cost = defaultGrossCost !== null ? defaultGrossCost : '';
                (baseRow as any).default_fixed_margin = defaultFixedMargin !== null ? defaultFixedMargin : '';
                (baseRow as any).default_margin_percentage = defaultMarginPercentage !== null ? defaultMarginPercentage : '';

                // Calculate default total if we have cost data
                if (defaultGrossCost !== null) {
                  const defaultTotal = calculateTotalFromCostMargin({
                    gross_cost: defaultGrossCost,
                    fixed_margin: defaultFixedMargin || 0,
                    margin_percentage: defaultMarginPercentage || 0
                  });
                  (baseRow as any).default_total = defaultTotal !== null ? defaultTotal : '';
                } else {
                  (baseRow as any).default_total = '';
                }

                // Weekday cost/margin values with calculated totals
                const weekdays = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'];
                weekdays.forEach(day => {
                  const grossCost = existingRule[`${day}_gross_cost`] ? existingRule[`${day}_gross_cost`] / 100 : null;
                  const fixedMargin = existingRule[`${day}_fixed_margin`] ? existingRule[`${day}_fixed_margin`] / 100 : null;
                  const marginPercentage = existingRule[`${day}_margin_percentage`] || null;

                  (baseRow as any)[`${day}_gross_cost`] = grossCost !== null ? grossCost : '';
                  (baseRow as any)[`${day}_fixed_margin`] = fixedMargin !== null ? fixedMargin : '';
                  (baseRow as any)[`${day}_margin_percentage`] = marginPercentage !== null ? marginPercentage : '';

                  // Calculate total for this day if we have cost data
                  if (grossCost !== null) {
                    const dayTotal = calculateTotalFromCostMargin({
                      gross_cost: grossCost,
                      fixed_margin: fixedMargin || 0,
                      margin_percentage: marginPercentage || 0
                    });

                    // If we calculated a total, use it; otherwise keep the existing price
                    if (dayTotal !== null) {
                      (baseRow as any)[`${day}_price`] = dayTotal;
                    }
                  }
                });
              } else {
                // No existing rule - add empty cost/margin fields
                (baseRow as any).default_gross_cost = '';
                (baseRow as any).default_fixed_margin = '';
                (baseRow as any).default_margin_percentage = '';
                (baseRow as any).default_total = '';

                // Empty weekday cost/margin fields
                const weekdays = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'];
                weekdays.forEach(day => {
                  (baseRow as any)[`${day}_gross_cost`] = '';
                  (baseRow as any)[`${day}_fixed_margin`] = '';
                  (baseRow as any)[`${day}_margin_percentage`] = '';
                });
              }
            }

            exportRows.push(baseRow);
            console.log(`[Pricing Export API] Added row for room ${roomConfig.id}, occupancy ${occupancy.id}, meal plan ${mealPlan?.id || 'null'} - ${existingRule ? 'with pricing data' : 'empty template'}`);
          }
        }
      } catch (error) {
        console.error(`Error processing room config ${roomConfig.id}:`, error);
        // Continue with other room configs
      }
    }

    console.log(`[Pricing Export API] Total export rows generated: ${exportRows.length}`);

    // Add seasonal pricing support if requested - Use EXACT same logic as comprehensive pricing API
    if (include_seasonal === "true") {
      console.log(`[Pricing Export API] Adding seasonal pricing data...`);

      try {
        // Use the EXACT same logic as the comprehensive pricing API to get seasonal periods
        // This ensures consistency between UI and export

        const hotelPricingService = req.scope.resolve(HOTEL_PRICING_MODULE);

        // Get room pricing data using the same logic as comprehensive API
        const roomPricingData = await Promise.all(
          roomConfigs.map(async (roomConfig: any) => {
            try {
              // Get base price rules for this room config
              const basePriceRules = await hotelPricingService.listBasePriceRules({
                room_config_id: roomConfig.id,
                currency_code: currency,
              });

              // Get seasonal prices for this room using EXACT same logic as comprehensive API
              const seasonalPrices: any[] = [];
              const seasonalGroups = new Map();

              // For each base price rule, get its seasonal overrides
              for (const basePriceRule of basePriceRules) {
                const seasonalOverrides = await hotelPricingService.listSeasonalPriceRules({
                  base_price_rule_id: basePriceRule.id,
                });

                // Group seasonal overrides by name, start_date, end_date, and currency
                // Filter seasonal overrides by requested currency (SAME AS COMPREHENSIVE API)
                const filteredSeasonalOverrides = seasonalOverrides.filter(override => {
                  const overrideCurrency = override.currency_code || basePriceRule.currency_code || "USD";
                  return overrideCurrency === currency;
                });

                for (const override of filteredSeasonalOverrides) {
                  const metadata = override.metadata as any;
                  // Use the same field as comprehensive pricing API for consistency
                  const baseName = override.description || metadata?.name || "Unnamed Season";
                  const startDate = override.start_date;
                  const endDate = override.end_date;

                  // Format the seasonal period name the same way as the UI
                  let seasonName = baseName;
                  if (startDate && endDate) {
                    try {
                      const startFormatted = new Date(startDate).toLocaleDateString('en-GB', {
                        month: 'short',
                        day: 'numeric'
                      });
                      const endFormatted = new Date(endDate).toLocaleDateString('en-GB', {
                        month: 'short',
                        day: 'numeric'
                      });
                      seasonName = `${baseName} ${startFormatted} - ${endFormatted}`;
                    } catch (error) {
                      console.error(`[Pricing Export API] Date formatting error for season ${baseName}:`, error);
                      // Fallback to just the base name if date formatting fails
                      seasonName = baseName;
                    }
                  }

                  const overrideCurrency = override.currency_code || basePriceRule.currency_code;

                  // Create a unique key for this season (SAME AS COMPREHENSIVE API)
                  const key = `${seasonName}-${startDate}-${endDate}-${overrideCurrency}`;

                  if (!seasonalGroups.has(key)) {
                    seasonalGroups.set(key, {
                      id: override.id,
                      name: seasonName,
                      start_date: startDate,
                      end_date: endDate,
                      currency_code: overrideCurrency,
                      weekday_rules: []
                    });
                  }

                  // Add the rule to this seasonal period
                  seasonalGroups.get(key).weekday_rules.push({
                    ...override,
                    base_price_rule: basePriceRule
                  });
                }
              }

              // Convert seasonal groups to array (SAME AS COMPREHENSIVE API)
              const roomSeasonalPrices = Array.from(seasonalGroups.values());

              return {
                room_config_id: roomConfig.id,
                room_config: roomConfig,
                weekday_rules: basePriceRules,
                seasonal_prices: roomSeasonalPrices,
              };
            } catch (error) {
              console.error(`[Pricing Export API] Error fetching pricing data for room ${roomConfig.id}:`, error);
              return {
                room_config_id: roomConfig.id,
                room_config: roomConfig,
                weekday_rules: [],
                seasonal_prices: [],
              };
            }
          })
        );

        // Extract all seasonal periods using EXACT same logic as comprehensive API hook
        const allSeasonalPeriods: any[] = [];
        roomPricingData.forEach((roomData) => {
          roomData.seasonal_prices.forEach((seasonalPrice: any) => {
            // Add to global seasonal periods list (avoid duplicates) - SAME AS COMPREHENSIVE API
            const existingPeriod = allSeasonalPeriods.find(
              (p) => p.id === seasonalPrice.id
            );
            if (!existingPeriod) {
              allSeasonalPeriods.push({
                id: seasonalPrice.id,
                name: seasonalPrice.name,
                start_date: seasonalPrice.start_date,
                end_date: seasonalPrice.end_date,
                currency_code: seasonalPrice.currency_code,
              });
            }
          });
        });

        const seasonalPeriods = allSeasonalPeriods;
        console.log(`[Pricing Export API] Found ${seasonalPeriods.length} seasonal periods using comprehensive API logic`);

        // If no seasonal periods found, skip seasonal export
        if (seasonalPeriods.length === 0) {
          console.log(`[Pricing Export API] No seasonal periods to export`);
        } else {
          // Generate ALL possible seasonal combinations using EXACT same logic as comprehensive table
          for (const seasonalPeriod of seasonalPeriods) {
            console.log(`[Pricing Export API] Processing seasonal period: ${seasonalPeriod.name}`);

            for (const roomConfig of roomConfigs) {
              for (const occupancy of occupancyConfigs) {
                // Get available meal plans for this occupancy type (using same logic as base pricing)
                const mealPlansToProcess = getAvailableMealPlans(occupancy);

                for (const mealPlan of mealPlansToProcess) {
                  // Skip if meal plan filtering is applied and this meal plan is not selected
                  if (selectedMealPlanIds.length > 0 && mealPlan.id && !selectedMealPlanIds.includes(mealPlan.id)) {
                    continue;
                  }

                  // Find existing seasonal pricing data for this combination using room pricing data
                  const roomPricingDataForRoom = roomPricingData.find(
                    (rpd: any) => rpd.room_config_id === roomConfig.id
                  );

                  let existingSeasonalRule = null;

                  if (roomPricingDataForRoom) {
                    // Find the seasonal price that matches this period
                    const seasonalPrice = roomPricingDataForRoom.seasonal_prices.find(
                      (sp: any) => sp.id === seasonalPeriod.id || sp.name === seasonalPeriod.name
                    );

                    if (seasonalPrice) {
                      // Find the specific rule for this occupancy and meal plan combination
                      existingSeasonalRule = seasonalPrice.weekday_rules.find(
                        (rule: any) => {
                          const basePriceRule = rule.base_price_rule;
                          return basePriceRule?.occupancy_type_id === occupancy.id &&
                            (mealPlan?.id === null
                              ? !basePriceRule?.meal_plan_id || basePriceRule?.meal_plan_id === null
                              : basePriceRule?.meal_plan_id === mealPlan?.id);
                        }
                      );
                    }
                  }
                  

                  // Create seasonal export row (even if no data exists - like comprehensive table)
                  const seasonalRow: any = {
                    hotel_name: hotel[0].name,
                    hotel_id: hotel[0].id,
                    room_config_name: roomConfig.title.trim(),
                    room_config_id: roomConfig.id,
                    room_type: (roomConfig.metadata as any)?.room_type || '',
                    max_occupancy: (roomConfig.metadata as any)?.max_occupancy || 0,
                    max_cots: (roomConfig.metadata as any)?.max_cots || 0,
                    occupancy_id: occupancy?.id || '',
                    occupancy_name: occupancy?.name || 'Unknown',
                    occupancy_adults: (occupancy as any)?.adults || 0,
                    occupancy_children: (occupancy as any)?.children || 0,
                    occupancy_infants: (occupancy as any)?.infants || 0,
                    meal_plan_id: mealPlan?.id || '',
                    meal_plan_name: mealPlan?.name || '-',
                    meal_plan_type: mealPlan?.type || 'none',
                    pricing_rule_id: existingSeasonalRule?.id || '',
                    pricing_type: 'Seasonal Pricing',
                    seasonal_period: seasonalPeriod.name, // Use actual season name
                    seasonal_start_date: seasonalPeriod.start_date,
                    seasonal_end_date: seasonalPeriod.end_date,
                    currency_code: seasonalPeriod.currency_code || currency,
                    priority: existingSeasonalRule?.priority || 2,

                    // Initialize weekday prices from stored values (will be overridden by calculations if cost/margin data exists)
                    monday_price: existingSeasonalRule?.metadata?.weekday_prices?.mon || '',
                    tuesday_price: existingSeasonalRule?.metadata?.weekday_prices?.tue || '',
                    wednesday_price: existingSeasonalRule?.metadata?.weekday_prices?.wed || '',
                    thursday_price: existingSeasonalRule?.metadata?.weekday_prices?.thu || '',
                    friday_price: existingSeasonalRule?.metadata?.weekday_prices?.fri || '',
                    saturday_price: existingSeasonalRule?.metadata?.weekday_prices?.sat || '',
                    sunday_price: existingSeasonalRule?.metadata?.weekday_prices?.sun || '',
                  };

                  // Calculate totals from cost/margin data if available (similar to base pricing logic)
                  if (existingSeasonalRule?.metadata) {
                    const metadata = existingSeasonalRule.metadata;
                    const weekdayValues = metadata.weekday_values;

                    // Calculate weekday totals from cost/margin data if available
                    const weekdays = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'];
                    const weekdayShortNames = ['mon', 'tue', 'wed', 'thu', 'fri', 'sat', 'sun'];

                    weekdays.forEach((day, index) => {
                      const shortDay = weekdayShortNames[index];
                      const dayValues = weekdayValues?.[shortDay];

                      // Calculate total for this day if we have cost data (convert from cents to currency units)
                      if (dayValues?.gross_cost !== undefined && dayValues?.gross_cost !== null) {
                        const grossCost = dayValues.gross_cost / 100;
                        const fixedMargin = dayValues.fixed_margin ? dayValues.fixed_margin / 100 : 0;
                        const marginPercentage = dayValues.margin_percentage || 0;

                        const dayTotal = calculateTotalFromCostMargin({
                          gross_cost: grossCost,
                          fixed_margin: fixedMargin,
                          margin_percentage: marginPercentage
                        });

                        // If we calculated a total, use it; otherwise keep the existing price
                        if (dayTotal !== null) {
                          seasonalRow[`${day}_price`] = dayTotal;
                        }
                      }
                    });
                  }

                  // Add cost/margin data if requested and available
                  if (include_cost_margin === "true") {
                    const metadata = existingSeasonalRule?.metadata;
                    const weekdayValues = metadata?.weekday_values;

                    // Default cost/margin values (convert from cents to currency units)
                    const defaultGrossCost = metadata?.default_values?.gross_cost ? metadata.default_values.gross_cost / 100 : null;
                    const defaultFixedMargin = metadata?.default_values?.fixed_margin ? metadata.default_values.fixed_margin / 100 : null;
                    const defaultMarginPercentage = metadata?.default_values?.margin_percentage || null;

                    seasonalRow.default_gross_cost = defaultGrossCost !== null ? defaultGrossCost : '';
                    seasonalRow.default_fixed_margin = defaultFixedMargin !== null ? defaultFixedMargin : '';
                    seasonalRow.default_margin_percentage = defaultMarginPercentage !== null ? defaultMarginPercentage : '';

                    // Calculate default total if we have cost data
                    if (defaultGrossCost !== null) {
                      const defaultTotal = calculateTotalFromCostMargin({
                        gross_cost: defaultGrossCost,
                        fixed_margin: defaultFixedMargin || 0,
                        margin_percentage: defaultMarginPercentage || 0
                      });
                      seasonalRow.default_total = defaultTotal !== null ? defaultTotal : '';
                    } else {
                      seasonalRow.default_total = '';
                    }

                    // Weekday cost/margin values (prices already calculated above)
                    const weekdays = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'];
                    const weekdayShortNames = ['mon', 'tue', 'wed', 'thu', 'fri', 'sat', 'sun'];

                    weekdays.forEach((day, index) => {
                      const shortDay = weekdayShortNames[index];
                      const dayValues = weekdayValues?.[shortDay];

                      // Convert from cents to currency units (same as base pricing logic)
                      const grossCost = dayValues?.gross_cost ? dayValues.gross_cost / 100 : null;
                      const fixedMargin = dayValues?.fixed_margin ? dayValues.fixed_margin / 100 : null;
                      const marginPercentage = dayValues?.margin_percentage || null;

                      seasonalRow[`${day}_gross_cost`] = grossCost !== null ? grossCost : '';
                      seasonalRow[`${day}_fixed_margin`] = fixedMargin !== null ? fixedMargin : '';
                      seasonalRow[`${day}_margin_percentage`] = marginPercentage !== null ? marginPercentage : '';
                      // Note: prices are already calculated above in the main calculation logic
                    });
                  }

                  exportRows.push(seasonalRow);
                  console.log(`[Pricing Export API] Added seasonal row for ${roomConfig.title} - ${occupancy.name} - ${mealPlan?.name || 'No Meal'} in ${seasonalPeriod.name}`);
                }
              }
            }
          }
        }

        console.log(`[Pricing Export API] Added ${exportRows.length - exportRows.filter(row => row.pricing_type === 'Base Pricing').length} seasonal pricing rows`);
      } catch (error) {
        console.error(`[Pricing Export API] Error adding seasonal pricing:`, error);
        // Continue with base pricing only if seasonal fails
      }
    }

    console.log(`[Pricing Export API] Final total export rows: ${exportRows.length}`);

    // Note: We now allow exporting even when no pricing data exists (empty template)
    if (exportRows.length === 0) {
      console.log(`[Pricing Export API] No room/occupancy/meal plan combinations found. Room configs: ${roomConfigs?.length}, Occupancy configs: ${occupancyConfigs?.length}, Meal plans: ${mealPlans?.length}`);
      console.log(`[Pricing Export API] This might indicate a configuration issue with the hotel setup.`);
    }

    // Filter columns if specific columns are requested
    let filteredExportRows = exportRows;
    if (columns) {
      const selectedColumns = columns.split(',').map(col => col.trim()).filter(Boolean);
      console.log(`[Pricing Export API] Filtering to selected columns:`, selectedColumns);

      filteredExportRows = exportRows.map(row => {
        const filteredRow: any = {};
        selectedColumns.forEach(column => {
          if (row.hasOwnProperty(column)) {
            filteredRow[column] = row[column];
          }
        });
        return filteredRow;
      });

      console.log(`[Pricing Export API] Filtered rows to ${selectedColumns.length} columns`);
    }

    // Generate filename
    const timestamp = new Date().toISOString().split('T')[0];
    const hotelName = hotel[0].name.replace(/[^a-zA-Z0-9]/g, '_');
    
    if (format === "csv") {
      // Generate CSV
      const csvContent = convertToCSV(filteredExportRows);
      const filename = `hotel_pricing_${hotelName}_${currency}_${timestamp}.csv`;

      res.setHeader('Content-Type', 'text/csv;charset=utf-8');
      res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);
      res.setHeader('Content-Length', Buffer.byteLength(csvContent, 'utf8'));

      console.log(`📤 Exported ${filteredExportRows.length} pricing records as CSV`);
      return res.send(csvContent);
    } else {
      // Generate Excel
      const workbook = await generateExcel(filteredExportRows, hotel[0].name, include_cost_margin === "true", hotelId, query, hotelPricingService);
      const filename = `hotel_pricing_${hotelName}_${currency}_${timestamp}.xlsx`;

      res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
      res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);

      console.log(`📤 Exported ${filteredExportRows.length} pricing records as Excel`);
      await workbook.xlsx.write(res);
    }
  } catch (error) {
    console.error('Error exporting hotel pricing data:', error);
    return res.status(500).json({
      message: "Failed to export pricing data",
      type: "internal_error",
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
};

/**
 * Convert data to CSV format
 */
function convertToCSV(data: any[]): string {
  if (data.length === 0) {
    return '';
  }

  const headers = Object.keys(data[0]);
  const csvRows = [
    headers.join(','),
    ...data.map(row =>
      headers.map(header => {
        const value = row[header]?.toString() || '';
        // Escape quotes and wrap in quotes if contains comma, quote, or newline
        return value.includes(',') || value.includes('"') || value.includes('\n')
          ? `"${value.replace(/"/g, '""')}"`
          : value;
      }).join(',')
    )
  ];

  // Add BOM for better Excel compatibility
  const BOM = '\uFEFF';
  return BOM + csvRows.join('\n');
}

/**
 * Add reference sheets to workbook (same as import template)
 */
async function addReferenceSheets(workbook: ExcelJS.Workbook, hotelId: string, query: any, hotelPricingService: any) {
  // Get room configurations
  const { data: allProducts } = await query.graph({
    entity: "product",
    filters: {},
    fields: ["id", "title", "handle", "metadata"],
  });

  const roomConfigs = allProducts?.filter((product: any) =>
    product.metadata &&
    (product.metadata.hotel_id === hotelId || product.metadata.type === "room_config")
  ) || [];

  // Get occupancy configurations
  const { data: occupancyConfigs } = await query.graph({
    entity: "occupancy_config",
    filters: { hotel_id: hotelId },
    fields: ["id", "name"],
  });

  // Get meal plans
  const mealPlans = await hotelPricingService.listMealPlans({
    hotel_id: hotelId,
  });

  // Room Configs sheet
  const roomConfigsSheet = workbook.addWorksheet('Room Configs Reference');
  roomConfigsSheet.columns = [
    { header: 'Room Config Name', key: 'name', width: 30 },
    { header: 'Room Config ID', key: 'id', width: 30 },
  ];

  roomConfigsSheet.getRow(1).font = { bold: true };
  roomConfigs.forEach((rc: any) => {
    roomConfigsSheet.addRow({ name: rc.title, id: rc.id });
  });

  // Occupancy Configs sheet
  const occupancySheet = workbook.addWorksheet('Occupancy Reference');
  occupancySheet.columns = [
    { header: 'Occupancy Name', key: 'name', width: 25 },
    { header: 'Occupancy ID', key: 'id', width: 30 },
  ];

  occupancySheet.getRow(1).font = { bold: true };
  occupancyConfigs.forEach((oc: any) => {
    occupancySheet.addRow({ name: oc.name, id: oc.id });
  });

  // Meal Plans sheet
  const mealPlansSheet = workbook.addWorksheet('Meal Plans Reference');
  mealPlansSheet.columns = [
    { header: 'Meal Plan Name', key: 'name', width: 30 },
    { header: 'Meal Plan ID', key: 'id', width: 30 },
  ];

  mealPlansSheet.getRow(1).font = { bold: true };
  mealPlans.forEach((mp: any) => {
    mealPlansSheet.addRow({ name: mp.name, id: mp.id });
  });

  // Add special entry for no meal plan
  mealPlansSheet.addRow({ name: '-', id: '' });

  // Seasonal Periods sheet
  const seasonalPeriodsSheet = workbook.addWorksheet('Seasonal Periods Reference');
  seasonalPeriodsSheet.columns = [
    { header: 'Season Name', key: 'name', width: 30 },
    { header: 'Season ID', key: 'id', width: 30 },
    { header: 'Start Date', key: 'start_date', width: 15 },
    { header: 'End Date', key: 'end_date', width: 15 },
  ];

  seasonalPeriodsSheet.getRow(1).font = { bold: true };

  // Add base pricing entry
  seasonalPeriodsSheet.addRow({
    name: 'Base Price',
    id: 'base',
    start_date: '',
    end_date: ''
  });

  // Get seasonal periods from the hotel pricing service
  try {
    const allBasePriceRules = await hotelPricingService.listBasePriceRules({});
    const basePriceRulesForHotel = allBasePriceRules.filter((rule: any) =>
      roomConfigs.some((rc: any) => rc.id === rule.room_config_id)
    );

    const seasonalPeriods = new Set();

    for (const basePriceRule of basePriceRulesForHotel) {
      const seasonalOverrides = await hotelPricingService.listSeasonalPriceRules({
        base_price_rule_id: basePriceRule.id,
      });

      seasonalOverrides.forEach((override: any) => {
        const seasonKey = `${override.description || 'Unnamed Season'}-${override.start_date}-${override.end_date}`;
        if (!seasonalPeriods.has(seasonKey)) {
          seasonalPeriods.add(seasonKey);
          seasonalPeriodsSheet.addRow({
            name: override.description || 'Unnamed Season',
            id: override.id,
            start_date: override.start_date ? new Date(override.start_date).toLocaleDateString() : '',
            end_date: override.end_date ? new Date(override.end_date).toLocaleDateString() : ''
          });
        }
      });
    }
  } catch (error) {
    console.error('Error fetching seasonal periods for reference sheet:', error);
  }

  // Currencies sheet
  const currenciesSheet = workbook.addWorksheet('Currencies Reference');
  currenciesSheet.columns = [
    { header: 'Currency Code', key: 'code', width: 15 },
    { header: 'Currency Name', key: 'name', width: 30 },
  ];

  currenciesSheet.getRow(1).font = { bold: true };

  // Add common currencies
  const commonCurrencies = [
    { code: 'CHF', name: 'Swiss Franc' },
    { code: 'EUR', name: 'Euro' },
    { code: 'USD', name: 'US Dollar' },
    { code: 'GBP', name: 'British Pound' },
    { code: 'JPY', name: 'Japanese Yen' },
    { code: 'CAD', name: 'Canadian Dollar' },
    { code: 'AUD', name: 'Australian Dollar' },
  ];

  commonCurrencies.forEach((currency) => {
    currenciesSheet.addRow(currency);
  });

  // Instructions sheet
  const instructionsSheet = workbook.addWorksheet('Instructions');
  instructionsSheet.columns = [
    { header: 'Export Information', key: 'instruction', width: 80 },
  ];

  const instructions = [
    'Hotel Pricing Export Information',
    '',
    'This export contains your current hotel pricing data with the following sheets:',
    '',
    '1. Hotel Pricing Data - Main pricing data with user-friendly names',
    '2. Room Configs Reference - Room configuration ID mappings',
    '3. Occupancy Reference - Occupancy type ID mappings',
    '4. Meal Plans Reference - Meal plan ID mappings',
    '5. Seasonal Periods Reference - Seasonal period ID mappings',
    '6. Currencies Reference - Supported currency codes',
    '7. Instructions - This information sheet',
    '',
    'Import/Export System:',
    '- The main sheet uses readable names for user-friendliness',
    '- Reference sheets contain ID mappings for system compatibility',
    '- During import, names are matched to IDs using reference sheets',
    '- This allows easy editing while maintaining data integrity',
    '',
    'To modify and re-import this data:',
    '1. Edit the pricing data in the "Hotel Pricing Data" sheet',
    '2. Use exact names from the reference sheets (case-sensitive)',
    '3. Do not modify the reference sheets',
    '4. Save as Excel format (.xlsx)',
    '5. Use the import function to upload your changes',
    '',
    'Column Information:',
    '- Season: Must match names in "Seasonal Periods Reference" or "Base Price"',
    '- Room Type: Must match exactly with names in "Room Configs Reference"',
    '- Occupancy: Must match exactly with names in "Occupancy Reference"',
    '- Meal Plan: Must match exactly with names in "Meal Plans Reference"',
    '- Currency Code: Must match codes in "Currencies Reference"',
    '- Default Total: Calculated automatically from cost/margin values',
    '- Cost/Margin Columns: Used for automatic price calculation',
    '- Weekday Prices: Final prices for each day of the week',
    '',
    'Cost/Margin Calculation:',
    '- Total = Gross Cost + Fixed Margin + (Gross Cost × Margin %)',
    '- If cost/margin data is provided, prices are calculated automatically',
    '- Manual price entries override calculated values',
    '',
    'Notes:',
    '- This export includes all current pricing data',
    '- Empty cells indicate no pricing is set for that combination',
    '- Seasonal pricing shows the actual season names from the database',
    '- Reference sheets ensure proper ID mapping during import',
  ];

  instructionsSheet.getRow(1).font = { bold: true, size: 14 };
  instructions.forEach((instruction, index) => {
    const row = instructionsSheet.addRow({ instruction });
    if (index === 0) {
      row.font = { bold: true, size: 14 };
    } else if (instruction.endsWith(':')) {
      row.font = { bold: true };
    }
  });
}

/**
 * Generate Excel workbook
 */
async function generateExcel(
  exportData: any[],
  hotelName: string,
  includeCostMargin: boolean,
  hotelId: string,
  query: any,
  hotelPricingService: any
): Promise<ExcelJS.Workbook> {
  const workbook = new ExcelJS.Workbook();

  // Set workbook properties
  workbook.creator = 'Hotel Pricing Export';
  workbook.lastModifiedBy = 'Hotel Pricing Export';
  workbook.created = new Date();
  workbook.modified = new Date();

  // Create main worksheet
  const worksheet = workbook.addWorksheet('Hotel Pricing Data');

  if (exportData.length === 0) {
    worksheet.addRow(['No pricing data available']);
    return workbook;
  }

  // Define column order to match comprehensive pricing table exactly
  // This ensures export matches the UI table structure: Season, Room Type, Occupancy, Meal Plan,
  // then default cost/margin, then weekday groups (Cost, Margin, %, Price for each day)
  const comprehensivePricingTableColumnOrder = [
    // Core identification columns (matching comprehensive table order)
    'seasonal_period',        // Season column
    'room_config_name',       // Room Type column
    'occupancy_name',         // Occupancy column
    'meal_plan_name',         // Meal Plan column
    'currency_code',          // Currency info

    // Default cost/margin columns (matching comprehensive table order)
    'default_gross_cost',     // Gross Cost
    'default_fixed_margin',   // Fixed Margin
    'default_margin_percentage', // Margin %
    'default_total',          // Default Total (calculated from cost/margin)

    // Monday group: Cost, Margin, %, Price
    'monday_gross_cost',
    'monday_fixed_margin',
    'monday_margin_percentage',
    'monday_price',

    // Tuesday group: Cost, Margin, %, Price
    'tuesday_gross_cost',
    'tuesday_fixed_margin',
    'tuesday_margin_percentage',
    'tuesday_price',

    // Wednesday group: Cost, Margin, %, Price
    'wednesday_gross_cost',
    'wednesday_fixed_margin',
    'wednesday_margin_percentage',
    'wednesday_price',

    // Thursday group: Cost, Margin, %, Price
    'thursday_gross_cost',
    'thursday_fixed_margin',
    'thursday_margin_percentage',
    'thursday_price',

    // Friday group: Cost, Margin, %, Price
    'friday_gross_cost',
    'friday_fixed_margin',
    'friday_margin_percentage',
    'friday_price',

    // Saturday group: Cost, Margin, %, Price
    'saturday_gross_cost',
    'saturday_fixed_margin',
    'saturday_margin_percentage',
    'saturday_price',

    // Sunday group: Cost, Margin, %, Price
    'sunday_gross_cost',
    'sunday_fixed_margin',
    'sunday_margin_percentage',
    'sunday_price',

    // Additional metadata columns (user-friendly, no IDs)
    'hotel_name',
    'max_occupancy',
    'max_cots',
    'occupancy_adults',
    'occupancy_children',
    'occupancy_infants',
    'meal_plan_type',
    'pricing_type',
    'seasonal_start_date',
    'seasonal_end_date'
  ];

  // Get headers from first row and order them according to import template
  const availableHeaders = Object.keys(exportData[0]);

  // Create ordered headers: first the comprehensive pricing table columns, then any additional ones
  const orderedHeaders = [
    ...comprehensivePricingTableColumnOrder.filter(col => availableHeaders.includes(col)),
    ...availableHeaders.filter(col => !comprehensivePricingTableColumnOrder.includes(col))
  ];

  // Define column headers - use raw column names for import compatibility
  const columns = orderedHeaders.map(header => {
    // Use raw column names that match what the import expects (for import compatibility)
    // The import function normalizes headers and expects these exact column names
    const headerMap: Record<string, string> = {
      'seasonal_period': 'seasonal_period',
      'room_config_name': 'room_config_name',  // Import expects this exact name
      'occupancy_name': 'occupancy_name',      // Import expects this exact name
      'meal_plan_name': 'meal_plan_name',      // Import expects this exact name
      'currency_code': 'currency_code',
      'default_gross_cost': 'default_gross_cost',
      'default_fixed_margin': 'default_fixed_margin',
      'default_margin_percentage': 'default_margin_percentage',
      'default_total': 'default_total',
      // Monday group - use raw column names for import compatibility
      'monday_gross_cost': 'monday_gross_cost',
      'monday_fixed_margin': 'monday_fixed_margin',
      'monday_margin_percentage': 'monday_margin_percentage',
      'monday_price': 'monday_price',
      // Tuesday group
      'tuesday_gross_cost': 'tuesday_gross_cost',
      'tuesday_fixed_margin': 'tuesday_fixed_margin',
      'tuesday_margin_percentage': 'tuesday_margin_percentage',
      'tuesday_price': 'tuesday_price',
      // Wednesday group
      'wednesday_gross_cost': 'wednesday_gross_cost',
      'wednesday_fixed_margin': 'wednesday_fixed_margin',
      'wednesday_margin_percentage': 'wednesday_margin_percentage',
      'wednesday_price': 'wednesday_price',
      // Thursday group
      'thursday_gross_cost': 'thursday_gross_cost',
      'thursday_fixed_margin': 'thursday_fixed_margin',
      'thursday_margin_percentage': 'thursday_margin_percentage',
      'thursday_price': 'thursday_price',
      // Friday group
      'friday_gross_cost': 'friday_gross_cost',
      'friday_fixed_margin': 'friday_fixed_margin',
      'friday_margin_percentage': 'friday_margin_percentage',
      'friday_price': 'friday_price',
      // Saturday group
      'saturday_gross_cost': 'saturday_gross_cost',
      'saturday_fixed_margin': 'saturday_fixed_margin',
      'saturday_margin_percentage': 'saturday_margin_percentage',
      'saturday_price': 'saturday_price',
      // Sunday group
      'sunday_gross_cost': 'sunday_gross_cost',
      'sunday_fixed_margin': 'sunday_fixed_margin',
      'sunday_margin_percentage': 'sunday_margin_percentage',
      'sunday_price': 'sunday_price',
    };

    return {
      header: headerMap[header] || header, // Use raw column name for import compatibility
      key: header,
      width: getColumnWidth(header)
    };
  });

  worksheet.columns = columns;

  // Style the header row
  const headerRow = worksheet.getRow(1);
  headerRow.font = { bold: true, color: { argb: 'FFFFFF' } };
  headerRow.fill = {
    type: 'pattern',
    pattern: 'solid',
    fgColor: { argb: '366092' }
  };
  headerRow.alignment = { horizontal: 'center', vertical: 'middle' };
  headerRow.height = 25;

  // Add data rows with proper column ordering
  exportData.forEach((row, index) => {
    // Reorder row data to match column order
    const orderedRowData = orderedHeaders.map(header => row[header] || '');
    const dataRow = worksheet.addRow(orderedRowData);

    // Alternate row colors for better readability
    if (index % 2 === 1) {
      dataRow.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: 'F8F9FA' }
      };
    }

    // Format pricing columns as currency
    orderedHeaders.forEach((header, colIndex) => {
      if (header.includes('_price') || header.includes('_cost') || header.includes('_margin')) {
        const cell = dataRow.getCell(colIndex + 1);
        if (typeof cell.value === 'number') {
          cell.numFmt = '#,##0.00';
        }
      }
    });
  });

  // Add borders to all cells (range calculated but not used for now)

  // Auto-filter
  worksheet.autoFilter = {
    from: 'A1',
    to: worksheet.getCell(1, orderedHeaders.length).address
  };

  // Freeze header row
  worksheet.views = [{ state: 'frozen', ySplit: 1 }];

  // Add reference sheets (same as import template)
  await addReferenceSheets(workbook, hotelId, query, hotelPricingService);

  return workbook;
}

/**
 * Get appropriate column width based on header name
 */
function getColumnWidth(header: string): number {
  const widthMap: Record<string, number> = {
    hotel_name: 25,
    room_config_name: 20,
    occupancy_name: 20,
    meal_plan_name: 20,
    pricing_type: 15,
    seasonal_period: 20,
    seasonal_start_date: 15,
    seasonal_end_date: 15,
    currency_code: 10,
    max_occupancy: 12,
    max_cots: 10,
    occupancy_adults: 10,
    occupancy_children: 12,
    occupancy_infants: 10,
    meal_plan_type: 15,
  };

  // Price and cost columns
  if (header.includes('_price') || header.includes('_cost') || header.includes('_margin')) {
    return 12;
  }

  // Percentage columns
  if (header.includes('_percentage')) {
    return 10;
  }

  return widthMap[header] || 15;
}
