import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";
import { z } from "zod";
import { HOTEL_PRICING_MODULE } from "src/modules/hotel-management/hotel-pricing";
import HotelPricingService from "src/modules/hotel-management/hotel-pricing/service";

// Validation schema for updating meal plan
export const PutAdminHotelMealPlan = z.object({
  name: z.string().optional(),
  type: z.string().optional(),
  is_default: z.boolean().optional(),
  applicable_occupancy_types: z.array(z.string()).optional(), // Array of occupancy type IDs
  metadata: z.record(z.any()).optional(),
});

export type PutAdminHotelMealPlanType = z.infer<typeof PutAdminHotelMealPlan>;

// GET endpoint to retrieve a specific meal plan
export const GET = async (req: MedusaRequest, res: MedusaResponse) => {
  try {
    const hotelId = req.params.id;
    const mealPlanId = req.params.meal_plan_id;

    // Get the hotel pricing service
    let hotelPricingService: HotelPricingService;
    try {
      hotelPricingService = req.scope.resolve(HOTEL_PRICING_MODULE);
    } catch (error) {
      console.error("Failed to resolve hotelPricingService:", error);
      return res.status(500).json({
        message: "Internal server error: Could not resolve hotelPricingService",
        error: error instanceof Error ? error.message : String(error),
      });
    }

    // Get the meal plan
    const mealPlan = await hotelPricingService.retrieveMealPlan(mealPlanId);

    if (!mealPlan || mealPlan.hotel_id !== hotelId) {
      return res.status(404).json({
        message: "Meal plan not found",
      });
    }

    res.json({ meal_plan: mealPlan });
  } catch (error) {
    res.status(400).json({
      message:
        error instanceof Error ? error.message : "Failed to retrieve meal plan",
    });
  }
};

// PUT endpoint to update a meal plan
export const PUT = async (
  req: MedusaRequest<PutAdminHotelMealPlanType>,
  res: MedusaResponse
) => {
  try {
    const hotelId = req.params.id;
    const mealPlanId = req.params.meal_plan_id;
    const { name, type, is_default, applicable_occupancy_types, metadata } =
      req.body;

    // Get the hotel pricing service
    let hotelPricingService: HotelPricingService;
    try {
      hotelPricingService = req.scope.resolve(HOTEL_PRICING_MODULE);
    } catch (error) {
      console.error("Failed to resolve hotelPricingService:", error);
      return res.status(500).json({
        message: "Internal server error: Could not resolve hotelPricingService",
        error: error instanceof Error ? error.message : String(error),
      });
    }

    // Verify the meal plan exists and belongs to this hotel
    const existingMealPlan = await hotelPricingService.retrieveMealPlan(
      mealPlanId
    );

    if (!existingMealPlan || existingMealPlan.hotel_id !== hotelId) {
      return res.status(404).json({
        message: "Meal plan not found",
      });
    }

    // Validate applicable occupancy types if provided
    if (applicable_occupancy_types && applicable_occupancy_types.length > 0) {
      // Verify all occupancy type IDs exist for this hotel
      const occupancyConfigs = await hotelPricingService.listOccupancyConfigs({
        hotel_id: hotelId,
        id: applicable_occupancy_types,
      });

      const foundIds = occupancyConfigs.map((config) => config.id);
      const invalidIds = applicable_occupancy_types.filter(
        (id) => !foundIds.includes(id)
      );

      if (invalidIds.length > 0) {
        return res.status(400).json({
          message: `Invalid occupancy type IDs: ${invalidIds.join(", ")}`,
        });
      }
    }

    // Prepare updated metadata
    let updatedMetadata = existingMealPlan.metadata || {};

    if (metadata !== undefined) {
      updatedMetadata = { ...updatedMetadata, ...metadata };
    }

    if (applicable_occupancy_types !== undefined) {
      updatedMetadata.applicable_occupancy_types =
        applicable_occupancy_types.length > 0
          ? applicable_occupancy_types
          : null; // null means all occupancy types
    }

    // If this meal plan is being set as default, unset any existing default
    if (is_default === true) {
      const existingMealPlans = await hotelPricingService.listMealPlans({
        hotel_id: hotelId,
        is_default: true,
      });

      // Update existing default meal plans (except this one) to not be default
      for (const existingMealPlan of existingMealPlans) {
        if (existingMealPlan.id !== mealPlanId) {
          await hotelPricingService.updateMealPlans({
            id: existingMealPlan.id,
            is_default: false,
          });
        }
      }
    }

    // Update the meal plan
    const updatedMealPlan = await hotelPricingService.updateMealPlans({
      id: mealPlanId,
      name,
      type,
      is_default,
      metadata: updatedMetadata,
    });

    res.json({ meal_plan: updatedMealPlan });
  } catch (error) {
    res.status(400).json({
      message:
        error instanceof Error ? error.message : "Failed to update meal plan",
    });
  }
};

// DELETE endpoint to remove a meal plan
export const DELETE = async (req: MedusaRequest, res: MedusaResponse) => {
  try {
    const hotelId = req.params.id;
    const mealPlanId = req.params.meal_plan_id;

    // Get the hotel pricing service
    let hotelPricingService: HotelPricingService;
    try {
      hotelPricingService = req.scope.resolve(HOTEL_PRICING_MODULE);
    } catch (error) {
      console.error("Failed to resolve hotelPricingService:", error);
      return res.status(500).json({
        message: "Internal server error: Could not resolve hotelPricingService",
        error: error instanceof Error ? error.message : String(error),
      });
    }

    // Verify the meal plan exists and belongs to this hotel
    const existingMealPlan = await hotelPricingService.retrieveMealPlan(
      mealPlanId
    );

    if (!existingMealPlan || existingMealPlan.hotel_id !== hotelId) {
      return res.status(404).json({
        message: "Meal plan not found",
      });
    }

    // Delete the meal plan
    await hotelPricingService.deleteMealPlans(mealPlanId);

    res.status(204).send();
  } catch (error) {
    res.status(400).json({
      message:
        error instanceof Error ? error.message : "Failed to delete meal plan",
    });
  }
};
