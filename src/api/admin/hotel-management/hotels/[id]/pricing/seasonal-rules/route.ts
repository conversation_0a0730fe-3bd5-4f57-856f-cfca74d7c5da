import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";
import { z } from "zod";
import { HOTEL_PRICING_MODULE } from "../../../../../../../modules/hotel-management/hotel-pricing";

// Validation schema for creating seasonal price rules
export const PostAdminCreateSeasonalPriceRule = z.object({
  base_price_rule_id: z.string().min(1, "Base price rule ID is required"),
  start_date: z.string().regex(/^\d{4}-\d{2}-\d{2}$/, "Start date must be in YYYY-MM-DD format"),
  end_date: z.string().regex(/^\d{4}-\d{2}-\d{2}$/, "End date must be in YYYY-MM-DD format"),
  amount: z.number().min(0, "Amount must be non-negative"),
  currency_code: z.string().min(3, "Currency code is required"),
  priority: z.number().optional().default(100),
  name: z.string().optional(),
  description: z.string().optional(),
});

export type PostAdminCreateSeasonalPriceRuleType = z.infer<typeof PostAdminCreateSeasonalPriceRule>;

/**
 * POST /admin/hotel-management/hotels/:id/pricing/seasonal-rules
 * Create a new seasonal price rule
 */
export const POST = async (
  req: MedusaRequest<PostAdminCreateSeasonalPriceRuleType>,
  res: MedusaResponse
) => {
  try {
    const hotelId = req.params.id;

    if (!hotelId) {
      return res.status(400).json({ message: "Hotel ID is required" });
    }

    // Validate the request body
    const validationResult = PostAdminCreateSeasonalPriceRule.safeParse(req.body);
    if (!validationResult.success) {
      return res.status(400).json({
        message: "Invalid request data",
        errors: validationResult.error.errors,
      });
    }

    const {
      base_price_rule_id,
      start_date,
      end_date,
      amount,
      currency_code,
      priority,
      name,
      description,
    } = validationResult.data;

    console.log(`Creating seasonal price rule for hotel: ${hotelId}`);
    console.log(`Base rule: ${base_price_rule_id}, ${start_date} to ${end_date}, amount: ${amount}`);

    // Get the hotel pricing service
    const hotelPricingService = req.scope.resolve(HOTEL_PRICING_MODULE);

    // Verify the base price rule exists and belongs to this hotel
    const basePriceRule = await hotelPricingService.retrieveBasePriceRule(base_price_rule_id);
    if (!basePriceRule) {
      return res.status(404).json({
        message: "Base price rule not found",
        type: "not_found",
      });
    }

    // Verify the base price rule belongs to this hotel
    if (basePriceRule.hotel_id !== hotelId) {
      return res.status(403).json({
        message: "Base price rule does not belong to this hotel",
        type: "forbidden",
      });
    }

    // Create the seasonal price rule
    const seasonalRules = await hotelPricingService.createSeasonalPriceRules([{
      base_price_rule_id,
      start_date: new Date(start_date),
      end_date: new Date(end_date),
      amount,
      currency_code,
      priority,
      name,
      description,
    }]);

    const seasonalRule = seasonalRules[0];

    console.log(`Created seasonal price rule: ${seasonalRule.id}`);

    res.status(201).json({
      seasonal_rule: seasonalRule,
      message: "Seasonal price rule created successfully",
    });
  } catch (error) {
    console.error("Error creating seasonal price rule:", error);
    res.status(500).json({
      message: "Internal server error",
      error: error instanceof Error ? error.message : String(error),
    });
  }
};

/**
 * GET /admin/hotel-management/hotels/:id/pricing/seasonal-rules
 * List all seasonal price rules for a hotel
 */
export const GET = async (req: MedusaRequest, res: MedusaResponse) => {
  try {
    const hotelId = req.params.id;

    if (!hotelId) {
      return res.status(400).json({ message: "Hotel ID is required" });
    }

    console.log(`Fetching seasonal price rules for hotel: ${hotelId}`);

    // Get the hotel pricing service
    const hotelPricingService = req.scope.resolve(HOTEL_PRICING_MODULE);

    // Get all base price rules for this hotel first
    const basePriceRules = await hotelPricingService.listBasePriceRules({
      hotel_id: hotelId,
    });

    // Get all seasonal rules for these base price rules
    const seasonalRules = [];
    for (const basePriceRule of basePriceRules) {
      const rules = await hotelPricingService.listSeasonalPriceRules({
        base_price_rule_id: basePriceRule.id,
      });
      seasonalRules.push(...rules);
    }

    console.log(`Found ${seasonalRules.length} seasonal price rules for hotel ${hotelId}`);

    res.json({
      seasonal_rules: seasonalRules,
      count: seasonalRules.length,
    });
  } catch (error) {
    console.error("Error fetching seasonal price rules:", error);
    res.status(500).json({
      message: "Internal server error",
      error: error instanceof Error ? error.message : String(error),
    });
  }
};
