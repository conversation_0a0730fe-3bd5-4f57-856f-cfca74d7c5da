import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";
import { z } from "zod";
import { HOTEL_PRICING_MODULE } from "../../../../../../../../modules/hotel-management/hotel-pricing";

// Validation schema for updating seasonal price rules
export const PutAdminUpdateSeasonalPriceRule = z.object({
  start_date: z.string().regex(/^\d{4}-\d{2}-\d{2}$/, "Start date must be in YYYY-MM-DD format").optional(),
  end_date: z.string().regex(/^\d{4}-\d{2}-\d{2}$/, "End date must be in YYYY-MM-DD format").optional(),
  amount: z.number().min(0, "Amount must be non-negative").optional(),
  currency_code: z.string().min(3, "Currency code is required").optional(),
  priority: z.number().optional(),
  name: z.string().optional(),
  description: z.string().optional(),
});

export type PutAdminUpdateSeasonalPriceRuleType = z.infer<typeof PutAdminUpdateSeasonalPriceRule>;

/**
 * GET /admin/hotel-management/hotels/:id/pricing/seasonal-rules/:rule_id
 * Get a specific seasonal price rule
 */
export const GET = async (req: MedusaRequest, res: MedusaResponse) => {
  try {
    const hotelId = req.params.id;
    const ruleId = req.params.rule_id;

    if (!hotelId || !ruleId) {
      return res.status(400).json({ message: "Hotel ID and Rule ID are required" });
    }

    console.log(`Fetching seasonal price rule: ${ruleId} for hotel: ${hotelId}`);

    // Get the hotel pricing service
    const hotelPricingService = req.scope.resolve(HOTEL_PRICING_MODULE);

    // Get the seasonal rule
    const seasonalRule = await hotelPricingService.retrieveSeasonalPriceRule(ruleId);
    if (!seasonalRule) {
      return res.status(404).json({
        message: "Seasonal price rule not found",
        type: "not_found",
      });
    }

    // Verify the rule belongs to this hotel by checking the base price rule
    const basePriceRule = await hotelPricingService.retrieveBasePriceRule(
      seasonalRule.base_price_rule_id
    );
    if (!basePriceRule || basePriceRule.hotel_id !== hotelId) {
      return res.status(403).json({
        message: "Seasonal price rule does not belong to this hotel",
        type: "forbidden",
      });
    }

    res.json({
      seasonal_rule: seasonalRule,
    });
  } catch (error) {
    console.error("Error fetching seasonal price rule:", error);
    res.status(500).json({
      message: "Internal server error",
      error: error instanceof Error ? error.message : String(error),
    });
  }
};

/**
 * PUT /admin/hotel-management/hotels/:id/pricing/seasonal-rules/:rule_id
 * Update a specific seasonal price rule
 */
export const PUT = async (
  req: MedusaRequest<PutAdminUpdateSeasonalPriceRuleType>,
  res: MedusaResponse
) => {
  try {
    const hotelId = req.params.id;
    const ruleId = req.params.rule_id;

    if (!hotelId || !ruleId) {
      return res.status(400).json({ message: "Hotel ID and Rule ID are required" });
    }

    // Validate the request body
    const validationResult = PutAdminUpdateSeasonalPriceRule.safeParse(req.body);
    if (!validationResult.success) {
      return res.status(400).json({
        message: "Invalid request data",
        errors: validationResult.error.errors,
      });
    }

    const updateData = validationResult.data;

    console.log(`Updating seasonal price rule: ${ruleId} for hotel: ${hotelId}`);

    // Get the hotel pricing service
    const hotelPricingService = req.scope.resolve(HOTEL_PRICING_MODULE);

    // Get the existing seasonal rule
    const existingRule = await hotelPricingService.retrieveSeasonalPriceRule(ruleId);
    if (!existingRule) {
      return res.status(404).json({
        message: "Seasonal price rule not found",
        type: "not_found",
      });
    }

    // Verify the rule belongs to this hotel by checking the base price rule
    const basePriceRule = await hotelPricingService.retrieveBasePriceRule(
      existingRule.base_price_rule_id
    );
    if (!basePriceRule || basePriceRule.hotel_id !== hotelId) {
      return res.status(403).json({
        message: "Seasonal price rule does not belong to this hotel",
        type: "forbidden",
      });
    }

    // Prepare update data with proper date conversion
    const updatePayload: any = { ...updateData };
    if (updateData.start_date) {
      updatePayload.start_date = new Date(updateData.start_date);
    }
    if (updateData.end_date) {
      updatePayload.end_date = new Date(updateData.end_date);
    }

    // Update the seasonal price rule
    const updatedRule = await hotelPricingService.updateSeasonalPriceRule(ruleId, updatePayload);

    console.log(`Updated seasonal price rule: ${ruleId}`);

    res.json({
      seasonal_rule: updatedRule,
      message: "Seasonal price rule updated successfully",
    });
  } catch (error) {
    console.error("Error updating seasonal price rule:", error);
    res.status(500).json({
      message: "Internal server error",
      error: error instanceof Error ? error.message : String(error),
    });
  }
};

/**
 * DELETE /admin/hotel-management/hotels/:id/pricing/seasonal-rules/:rule_id
 * Delete a specific seasonal price rule
 */
export const DELETE = async (req: MedusaRequest, res: MedusaResponse) => {
  try {
    const hotelId = req.params.id;
    const ruleId = req.params.rule_id;

    if (!hotelId || !ruleId) {
      return res.status(400).json({ message: "Hotel ID and Rule ID are required" });
    }

    console.log(`Deleting seasonal price rule: ${ruleId} for hotel: ${hotelId}`);

    // Get the hotel pricing service
    const hotelPricingService = req.scope.resolve(HOTEL_PRICING_MODULE);

    // Get the existing seasonal rule
    const existingRule = await hotelPricingService.retrieveSeasonalPriceRule(ruleId);
    if (!existingRule) {
      return res.status(404).json({
        message: "Seasonal price rule not found",
        type: "not_found",
      });
    }

    // Verify the rule belongs to this hotel by checking the base price rule
    const basePriceRule = await hotelPricingService.retrieveBasePriceRule(
      existingRule.base_price_rule_id
    );
    if (!basePriceRule || basePriceRule.hotel_id !== hotelId) {
      return res.status(403).json({
        message: "Seasonal price rule does not belong to this hotel",
        type: "forbidden",
      });
    }

    // Delete the seasonal price rule
    await hotelPricingService.deleteSeasonalPriceRule(ruleId);

    console.log(`Deleted seasonal price rule: ${ruleId}`);

    res.json({
      message: "Seasonal price rule deleted successfully",
      deleted_rule_id: ruleId,
    });
  } catch (error) {
    console.error("Error deleting seasonal price rule:", error);
    res.status(500).json({
      message: "Internal server error",
      error: error instanceof Error ? error.message : String(error),
    });
  }
};
