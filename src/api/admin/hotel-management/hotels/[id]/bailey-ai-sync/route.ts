import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";
import { HOTEL_MODULE } from "src/modules/hotel-management/hotel";
import { UpdateHotelWorkflow } from "src/workflows/hotel-management/hotel/update-hotel";

/**
 * POST /admin/hotel-management/hotels/[id]/bailey-ai-sync
 * Sync hotel with Bailey AI - updates database first, then triggers external API
 */
export async function POST(req: MedusaRequest, res: MedusaResponse) {
  try {
    const hotelId = req.params.id;

    if (!hotelId) {
      return res.status(400).json({
        success: false,
        message: "Hotel ID is required",
      });
    }

    // Step 1: Get current hotel data
    const hotelService = req.scope.resolve(HOTEL_MODULE);
    const hotel = await hotelService.retrieveHotel(hotelId);

    if (!hotel) {
      return res.status(404).json({
        success: false,
        message: "Hotel not found",
      });
    }

    console.log("🤖 Starting Bailey AI sync for hotel:", hotelId, "with slug:", hotel.handle);

    // Step 2: Update hotel in database first (this ensures data is persisted)
    console.log("📝 Updating hotel in database...");
    const { result: updatedHotel } = await UpdateHotelWorkflow(req.scope).run({
      input: {
        id: hotelId,
        ai_content: hotel.ai_content, // Preserve current ai_content
      },
    });

    console.log("✅ Database updated successfully");

    // Step 3: Trigger external Bailey AI API
    console.log("🚀 Triggering external Bailey AI API...");

    try {
      // Use hotel slug for the external API endpoint
      const response = await fetch(`https://ai.perfect-piste.flinkk.io/api/sync/hotel/${hotel.handle}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        // Note: credentials are not included here as this is a server-to-server call
      });

      if (response.ok) {
        const result = await response.json();
        console.log("✅ Bailey AI sync successful:", result);

        // Step 4: Update hotel again if Bailey AI returns updated content
        if (result.ai_content && result.ai_content !== hotel.ai_content) {
          console.log("📝 Updating hotel with Bailey AI response...");
          const { result: finalHotel } = await UpdateHotelWorkflow(req.scope).run({
            input: {
              id: hotelId,
              ai_content: result.ai_content,
            },
          });

          return res.status(200).json({
            success: true,
            message: "Successfully synced with Bailey AI and updated content",
            hotel: finalHotel,
            ai_response: result,
          });
        } else {
          return res.status(200).json({
            success: true,
            message: "Successfully synced with Bailey AI",
            hotel: updatedHotel,
            ai_response: result,
          });
        }
      } else {
        const errorData = await response.json().catch(() => ({}));
        console.error("❌ Bailey AI API returned error:", response.status, errorData);

        // Even if external API fails, we still updated the database
        return res.status(207).json({ // 207 Multi-Status
          success: false,
          message: "Database updated but Bailey AI sync failed",
          hotel: updatedHotel,
          error: {
            status: response.status,
            message: errorData.message || "External API error",
          },
        });
      }
    } catch (externalApiError) {
      console.error("❌ Error calling Bailey AI API:", externalApiError);

      // Even if external API fails, we still updated the database
      return res.status(207).json({ // 207 Multi-Status
        success: false,
        message: "Database updated but Bailey AI sync failed due to network error",
        hotel: updatedHotel,
        error: {
          message: externalApiError instanceof Error ? externalApiError.message : "Unknown error",
        },
      });
    }
  } catch (error) {
    console.error("❌ Error in Bailey AI sync:", error);
    
    return res.status(500).json({
      success: false,
      message: "Failed to sync with Bailey AI",
      error: error instanceof Error ? error.message : "Unknown error",
    });
  }
}
