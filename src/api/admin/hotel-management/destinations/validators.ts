import { z } from "zod";
import { isValidUrl } from "../../../../utils/url-validation";

const FaqSchema = z.object({
  question: z.string().min(1, "Question is required"),
  answer: z.string().min(1, "Answer is required"),
});

// URL validation schema
const urlSchema = z.string().optional().refine(
  (url) => !url || isValidUrl(url),
  {
    message: "Must be a valid URL (e.g., https://example.com or www.example.com)",
  }
);

export const PostAdminCreateDestination = z.object({
  name: z.string(),
  handle: z.string(),
  description: z.string().optional(),
  is_active: z.boolean().default(true),
  country: z.string(),
  location: z.string().optional(),
  tags: z.array(z.string()).optional(),
  is_featured: z.boolean().default(false),
  ai_content: z.string().optional(),
  faqs: z.array(FaqSchema).optional(),
  // New fields for currency/margin enhancement
  internal_web_link: urlSchema,
  external_web_link: urlSchema,
  currency_code: z.string().optional(),
  margin_percentage: z.number().min(0).max(100).optional(),
});

const UpdateFaqSchema = z.object({
  id: z.string().optional(),
  question: z.string().min(1, "Question is required"),
  answer: z.string().min(1, "Answer is required"),
});

export const PostAdminUpdateDestination = z.object({
  id: z.string(),
  name: z.string().optional(),
  handle: z.string().optional(),
  description: z.string().optional(),
  is_active: z.boolean().default(true),
  country: z.string().optional(),
  location: z.string().optional(),
  tags: z.array(z.string()).optional(),
  is_featured: z.boolean().default(false),
  ai_content: z.string().optional(),
  faqs: z.array(UpdateFaqSchema).optional(),
  // New fields for currency/margin enhancement
  internal_web_link: urlSchema,
  external_web_link: urlSchema,
  currency_code: z.string().optional(),
  margin_percentage: z.number().min(0).max(100).optional(),
});

export const PostAdminDeleteDestination = z.object({
  ids: z.string(),
});
