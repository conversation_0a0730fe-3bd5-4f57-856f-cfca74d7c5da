import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";
import { UpdateDestinationWorkflow } from "src/workflows/hotel-management/destination/update-destination";
import { DESTINATION_MODULE } from "src/modules/hotel-management/destination";

/**
 * POST /admin/hotel-management/destinations/[id]/bailey-ai-sync
 * Sync destination with Bailey AI - updates database first, then triggers external API
 */
export async function POST(req: MedusaRequest, res: MedusaResponse) {
  try {
    const destinationId = req.params.id;

    if (!destinationId) {
      return res.status(400).json({
        success: false,
        message: "Destination ID is required",
      });
    }

    // Step 1: Get current destination data
    const destinationService = req.scope.resolve(DESTINATION_MODULE);
    const destination = await destinationService.retrieveDestination(destinationId);

    if (!destination) {
      return res.status(404).json({
        success: false,
        message: "Destination not found",
      });
    }

    console.log("🤖 Starting Bailey AI sync for destination:", destinationId, "with slug:", destination.handle);

    // Step 2: Update destination in database first (this ensures data is persisted)
    console.log("📝 Updating destination in database...");
    const { result: updatedDestination } = await UpdateDestinationWorkflow(req.scope).run({
      input: {
        id: destinationId,
        ai_content: destination.ai_content, // Preserve current ai_content
      },
    });

    console.log("✅ Database updated successfully");

    // Step 3: Trigger external Bailey AI API
    console.log("🚀 Triggering external Bailey AI API...");

    try {
      // Use destination slug for the external API endpoint
      const response = await fetch(`https://ai.perfect-piste.flinkk.io/api/sync/destination/${destination.handle}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        // Note: credentials are not included here as this is a server-to-server call
      });

      if (response.ok) {
        const result = await response.json();
        console.log("✅ Bailey AI sync successful:", result);

        // Step 4: Update destination again if Bailey AI returns updated content
        if (result.ai_content && result.ai_content !== destination.ai_content) {
          console.log("📝 Updating destination with Bailey AI response...");
          const { result: finalDestination } = await UpdateDestinationWorkflow(req.scope).run({
            input: {
              id: destinationId,
              ai_content: result.ai_content,
            },
          });

          return res.status(200).json({
            success: true,
            message: "Successfully synced with Bailey AI and updated content",
            destination: finalDestination,
            ai_response: result,
          });
        } else {
          return res.status(200).json({
            success: true,
            message: "Successfully synced with Bailey AI",
            destination: updatedDestination,
            ai_response: result,
          });
        }
      } else {
        const errorData = await response.json().catch(() => ({}));
        console.error("❌ Bailey AI sync failed:", response.status, errorData);

        // Even if external API fails, we still updated the database
        return res.status(207).json({ // 207 Multi-Status
          success: false,
          message: "Database updated but Bailey AI sync failed",
          destination: updatedDestination,
          error: {
            status: response.status,
            data: errorData,
          },
        });
      }
    } catch (externalApiError) {
      console.error("❌ Error calling Bailey AI API:", externalApiError);

      // Even if external API fails, we still updated the database
      return res.status(207).json({ // 207 Multi-Status
        success: false,
        message: "Database updated but Bailey AI sync failed due to network error",
        destination: updatedDestination,
        error: {
          message: externalApiError instanceof Error ? externalApiError.message : "Unknown error",
        },
      });
    }
  } catch (error) {
    console.error("❌ Error in Bailey AI sync:", error);
    
    return res.status(500).json({
      success: false,
      message: "Failed to sync with Bailey AI",
      error: error instanceof Error ? error.message : "Unknown error",
    });
  }
}
