import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";
import { Modules } from "@camped-ai/framework/utils";
import * as ExcelJS from "exceljs";

/**
 * GET endpoint to export room inventory data in exact import template format
 */
export const GET = async (req: MedusaRequest, res: MedusaResponse) => {
  try {
    console.log("Room inventory export request received");

    // Parse query parameters
    const {
      fields,
      from_date,
      to_date,
      status,
      hotel_id,
      format = "xlsx",
      include_room_reference,
      room_reference_fields,
    } = req.query;

    // Validate required parameters
    if (!from_date || !to_date) {
      return res
        .status(400)
        .json({ message: "From date and To date are required" });
    }

    console.log(`Exporting room inventory with filters:`, {
      from_date,
      to_date,
      status,
      hotel_id,
      format,
      include_room_reference,
      room_reference_fields,
    });

    // Use direct database query approach
    return await exportWithDirectQuery(req, res);
  } catch (error) {
    console.error("Error exporting room inventory:", error);
    console.error("Error stack:", error.stack);

    res.status(500).json({
      message: "Error exporting room inventory",
      error: error.message,
    });
  }
};

/**
 * Export inventory using direct database query
 */
async function exportWithDirectQuery(req: MedusaRequest, res: MedusaResponse) {
  try {
    console.log("Using direct database query for inventory export");

    // Parse query parameters
    const {
      fields,
      from_date,
      to_date,
      status,
      hotel_id,
      format = "xlsx",
      include_room_reference,
      room_reference_fields,
    } = req.query;

    // Use the query service instead of direct SQL
    const queryService = req.scope.resolve("query");
    if (!queryService) {
      throw new Error("Query service not found");
    }

    // Build filters for the graph query
    const filters: Record<string, any> = {};

    // Add date range filter - include records that are completely within the selected date range
    // A record is within range if: record.from_date >= export.from_date AND record.to_date <= export.to_date
    filters.from_date = {
      $gte: from_date, // Record starts on or after export start date
    };

    filters.to_date = {
      $lte: to_date, // Record ends on or before export end date
    };

    // Add status filter if provided
    if (status && status !== "all") {
      filters.status = status;
    }

    console.log("Using filters (records within date range):", filters);

    // Execute query using the graph query API with hotel filtering if provided
    let inventoryItems;

    if (hotel_id && hotel_id !== "all") {
      console.log(`🔍 Filtering room inventory by hotel_id: ${hotel_id}`);

      // First, get all products (room configs) for the specific hotel
      const { data: hotelProducts } = await queryService.graph({
        entity: "product",
        filters: {
          metadata: {
            hotel_id: hotel_id as string,
          },
        },
        fields: ["id"],
      });

      if (!hotelProducts || hotelProducts.length === 0) {
        console.log(`⚠️ No room configs found for hotel ${hotel_id}`);

        // Try alternative approach - get variants directly
        const { data: hotelVariants } = await queryService.graph({
          entity: "product_variant",
          filters: {
            metadata: {
              hotel_id: hotel_id as string,
            },
          },
          fields: ["id"],
        });

        if (!hotelVariants || hotelVariants.length === 0) {
          console.log(`⚠️ No room variants found for hotel ${hotel_id}`);
          inventoryItems = [];
        } else {
          const roomVariantIds = hotelVariants.map((variant) => variant.id);
          console.log(
            `🏨 Found ${roomVariantIds.length} room variants for hotel ${hotel_id}`
          );

          const inventoryFilters = {
            ...filters,
            inventory_item_id: { $in: roomVariantIds },
          };

          console.log(
            "Using inventory filters with hotel filtering (variants):",
            inventoryFilters
          );

          const { data: filteredInventoryItems } = await queryService.graph({
            entity: "room_inventory",
            filters: inventoryFilters,
            fields: [
              "id",
              "status",
              "from_date",
              "to_date",
              "available_quantity",
              "notes",
              "check_in_time",
              "check_out_time",
              "is_noon_to_noon",
              "inventory_item_id",
              "created_at",
              "updated_at",
            ],
          });

          inventoryItems = filteredInventoryItems;
        }
      } else {
        // Get all variants for these products
        const productIds = hotelProducts.map((product) => product.id);
        console.log(
          `🏨 Found ${productIds.length} room configs for hotel ${hotel_id}`
        );

        const { data: roomVariants } = await queryService.graph({
          entity: "product_variant",
          filters: {
            product_id: { $in: productIds },
          },
          fields: ["id"],
        });

        if (!roomVariants || roomVariants.length === 0) {
          console.log(`⚠️ No room variants found for hotel products`);
          inventoryItems = [];
        } else {
          const roomVariantIds = roomVariants.map((variant) => variant.id);
          console.log(
            `🏨 Found ${roomVariantIds.length} room variants for hotel ${hotel_id}`
          );

          const inventoryFilters = {
            ...filters,
            inventory_item_id: { $in: roomVariantIds },
          };

          console.log(
            "Using inventory filters with hotel filtering:",
            inventoryFilters
          );

          const { data: filteredInventoryItems } = await queryService.graph({
            entity: "room_inventory",
            filters: inventoryFilters,
            fields: [
              "id",
              "status",
              "from_date",
              "to_date",
              "available_quantity",
              "notes",
              "check_in_time",
              "check_out_time",
              "is_noon_to_noon",
              "inventory_item_id",
              "created_at",
              "updated_at",
            ],
          });

          inventoryItems = filteredInventoryItems;
        }
      }
    } else {
      console.log("🌍 Fetching room inventory for all hotels");

      const { data: allInventoryItems } = await queryService.graph({
        entity: "room_inventory",
        filters: filters,
        fields: [
          "id",
          "status",
          "from_date",
          "to_date",
          "available_quantity",
          "notes",
          "check_in_time",
          "check_out_time",
          "is_noon_to_noon",
          "inventory_item_id",
          "created_at",
          "updated_at",
        ],
      });

      inventoryItems = allInventoryItems;
    }

    console.log(`Found ${inventoryItems?.length || 0} inventory items`);

    // Process and export the data
    if (!inventoryItems || inventoryItems.length === 0) {
      console.log("No inventory items found");
      // Create an empty export
      await processAndExport(
        [],
        fields as string,
        format as string,
        res,
        include_room_reference === "true",
        room_reference_fields as string,
        {}
      );
    } else {
      // First, let's get all the inventory item IDs
      const inventoryItemIds = inventoryItems
        .map((item) => item.inventory_item_id)
        .filter(Boolean);

      // Fetch room information for these inventory items
      let roomInfo: Record<string, any> = {};

      try {
        // Since inventory_item_id in room_inventory corresponds to product_variant
        // We need to fetch product variant information including room details
        const { data: roomsData } = await queryService.graph({
          entity: "product_variant",
          filters: { id: { $in: inventoryItemIds } },
          fields: [
            "id",
            "title",
            "product.title",
            "product.metadata",
            "metadata",
          ],
        });

        if (roomsData && roomsData.length > 0) {
          console.log(`🏠 Found ${roomsData.length} room data records`);

          // Create a mapping from variant ID to room info
          roomsData.forEach((variant: any) => {
            if (variant.id) {
              const hotelId = variant.product?.metadata?.hotel_id || variant.metadata?.hotel_id || "";
              const roomNumber =
                variant.metadata?.room_number || variant.title || "";
              const roomType = variant.product?.title || "";
              const floor = variant.metadata?.floor || "";

              roomInfo[variant.id] = {
                room_number: roomNumber,
                room_type: roomType,
                floor: floor,
                hotel_id: hotelId,
              };

              // Debug: Log room info for troubleshooting
              console.log(`🏠 Room info for ${variant.id}:`, {
                room_number: roomNumber,
                hotel_id: hotelId,
                variant_metadata: variant.metadata,
                product_metadata: variant.product?.metadata
              });
            }
          });

          console.log(
            `Found room information for ${Object.keys(roomInfo).length} rooms`
          );
        }
      } catch (error) {
        console.error("Error fetching room information:", error);
      }

      // Status transformation mapping (Internal -> Display)
      const statusDisplayMapping = {
        available: "Available",
        reserved: "Reserved",
        maintenance: "Maintenance",
        cleaning: "Cleaning",
        unavailable: "Unavailable",
        on_demand: "On Request",
        reserved_unassigned: "Reserved",
        cart_reserved: "Reserved",
        booked: "Reserved", // Show as Reserved but not editable
      };

      // Process the data to match our expected format
      console.log(`🔄 PROCESSING ${inventoryItems.length} INVENTORY ITEMS`);

      // Show detailed breakdown of each date period instead of consolidating
      console.log(`📊 SHOWING DETAILED BREAKDOWN OF ${inventoryItems.length} DATE PERIODS`);
      console.log(`🔍 UNIQUE TEST MESSAGE - NO CONSOLIDATION SHOULD HAPPEN`);

      // Debug: Check for duplicate records and deduplicate
      const duplicateCheck = inventoryItems.reduce((acc, item) => {
        const key = `${item.inventory_item_id}-${item.from_date}-${item.to_date}`;
        if (!acc[key]) {
          acc[key] = [];
        }
        acc[key].push(item);
        return acc;
      }, {});

      // Deduplicate: Keep only the most recent record for each room-date combination
      const deduplicatedItems = [];
      Object.entries(duplicateCheck).forEach(([key, items]) => {
        if (items.length > 1) {
          console.log(`🚨 DUPLICATE FOUND: ${key} has ${items.length} records, keeping most recent`);
          // Sort by updated_at descending and keep the first (most recent)
          const sortedItems = items.sort((a, b) =>
            new Date(b.updated_at).getTime() - new Date(a.updated_at).getTime()
          );
          deduplicatedItems.push(sortedItems[0]);
        } else {
          deduplicatedItems.push(items[0]);
        }
      });

      console.log(`🔧 DEDUPLICATION: ${inventoryItems.length} → ${deduplicatedItems.length} items`);
      inventoryItems = deduplicatedItems;

      let processedItems = inventoryItems.map((item, index) => {
        const roomData = roomInfo[item.inventory_item_id] || {};

        // Determine the actual status - prioritize room_inventory status, fallback to room metadata status
        let actualStatus = item.status;

        // Handle unavailable status logic:
        // - If room metadata status is null, it represents "unavailable"
        // - If room_inventory status is "available" but room metadata has a different status, use metadata status
        if (
          actualStatus === "available" &&
          roomData.metadata?.status &&
          roomData.metadata.status !== "available"
        ) {
          actualStatus = roomData.metadata.status;
          console.log(
            `🔄 Status override from metadata: ${item.status} -> ${actualStatus} for room ${roomData.room_number}`
          );
        } else if (
          actualStatus === "available" &&
          roomData.metadata?.status === null
        ) {
          // null in room metadata represents unavailable status
          actualStatus = "unavailable";
          console.log(
            `🔄 Status override from null metadata: ${item.status} -> ${actualStatus} for room ${roomData.room_number}`
          );
        }

        // Ensure room_number and hotel_id are properly populated for import compatibility
        const processedItem = {
          id: item.id,
          room_id: item.inventory_item_id || "", // Add room_id which is the same as inventory_item_id
          inventory_item_id: item.inventory_item_id || "", // Keep this for internal use
          room_number: roomData.room_number || `Room-${item.inventory_item_id?.slice(-8) || 'Unknown'}`, // Fallback room number
          room_config_name: roomData.room_type || "", // Room configuration/type name
          room_type: roomData.room_type || "", // Keep for backward compatibility
          floor: roomData.floor || "",
          hotel_id: roomData.hotel_id || "", // This must be populated for import to work
          from_date: item.from_date,
          to_date: item.to_date,
          status: statusDisplayMapping[actualStatus] || actualStatus, // Transform status to display format
          available_quantity: item.available_quantity,
          notes: item.notes || "",
          check_in_time: item.check_in_time || "",
          check_out_time: item.check_out_time || "",
          is_noon_to_noon: item.is_noon_to_noon || false,
          created_at: item.created_at,
          updated_at: item.updated_at,
        };

        // Debug: Log items with missing room_number or hotel_id
        if (!processedItem.room_number || !processedItem.hotel_id) {
          console.log(`⚠️ Missing required fields for item ${item.id}:`, {
            room_number: processedItem.room_number,
            hotel_id: processedItem.hotel_id,
            inventory_item_id: item.inventory_item_id,
            roomData: roomData
          });
        }

        return processedItem;
      });

      // Sort by date (from_date first, then to_date) and then by room_number for better organization
      processedItems.sort((a, b) => {
        // First sort by from_date
        const dateA = new Date(a.from_date);
        const dateB = new Date(b.from_date);

        if (dateA.getTime() !== dateB.getTime()) {
          return dateA.getTime() - dateB.getTime();
        }

        // If from_date is the same, sort by to_date
        const toDateA = new Date(a.to_date);
        const toDateB = new Date(b.to_date);

        if (toDateA.getTime() !== toDateB.getTime()) {
          return toDateA.getTime() - toDateB.getTime();
        }

        // If both dates are the same, sort by room_number for consistency
        const roomA = a.room_number || "";
        const roomB = b.room_number || "";
        return roomA.localeCompare(roomB, undefined, { numeric: true });
      });

      // If hotel_id filter is provided, we need to filter the results
      if (hotel_id && hotel_id !== "all") {
        console.log(`🔍 HOTEL FILTERING DEBUG - START`);
        console.log(`📋 Input hotel_id: "${hotel_id}"`);
        console.log(
          `📊 Total items before filtering: ${processedItems.length}`
        );

        // First try to resolve hotel_id if it's a slug
        let resolvedHotelId = hotel_id;

        // Check if any room has a hotel_id that matches the provided hotel_id
        const directMatch = processedItems.find(
          (item) => item.hotel_id === hotel_id
        );

        console.log(`🎯 Direct match found: ${!!directMatch}`);

        if (!directMatch) {
          console.log(`🔄 No direct match, trying to resolve slug...`);
          // If no direct match, try to find hotel by slug using the query service
          try {
            const { data: hotels } = await queryService.graph({
              entity: "hotel",
              filters: {
                handle: hotel_id as string, // Try to find by slug/handle
              },
              fields: ["id", "handle", "name"],
            });

            console.log(`🏨 Hotel query result:`, hotels);

            if (hotels && hotels.length > 0) {
              resolvedHotelId = hotels[0].id;
              console.log(
                `✅ Resolved hotel slug "${hotel_id}" to ID "${resolvedHotelId}"`
              );
            } else {
              console.log(`❌ No hotel found with slug "${hotel_id}"`);
            }
          } catch (error) {
            console.warn(
              `⚠️ Could not resolve hotel identifier "${hotel_id}":`,
              error
            );
          }
        } else {
          console.log(`✅ Using direct hotel_id match: "${hotel_id}"`);
        }

        console.log(`🎯 Final resolvedHotelId: "${resolvedHotelId}"`);

        // Filter items
        const beforeFilterCount = processedItems.length;
        processedItems = processedItems.filter(
          (item) => item.hotel_id === resolvedHotelId
        );
        const afterFilterCount = processedItems.length;

        console.log(
          `📊 Items after hotel filtering: ${afterFilterCount} (was ${beforeFilterCount})`
        );

        console.log(`🔍 HOTEL FILTERING DEBUG - END`);
      }

      await processAndExport(
        processedItems,
        fields as string,
        format as string,
        res,
        include_room_reference === "true",
        room_reference_fields as string,
        roomInfo
      );
    }
  } catch (error) {
    console.error("Error with direct database query:", error);
    res.status(500).json({
      message: "Error exporting room inventory",
      error: error.message,
    });
  }
}

/**
 * Process inventory data and export to the requested format
 */
async function processAndExport(
  inventoryItems: any[],
  fieldsParam: string,
  format: string,
  res: MedusaResponse,
  includeRoomReference: boolean = false,
  roomReferenceFieldsParam: string = "",
  roomInfo: Record<string, any> = {}
) {
  // Parse fields to include - inventory_item_id column 1, hotel_id column 2, include room config name
  const fieldsToInclude = fieldsParam
    ? fieldsParam.split(",")
    : [
        "inventory_item_id", // Column 1
        "hotel_id",          // Column 2
        "room_number",
        "room_config_name",  // Room configuration/type name
        "from_date",
        "to_date",
        "available_quantity",
        "status",
        "notes",
        "check_in_time",
        "check_out_time",
        "is_noon_to_noon",
      ];

  // Parse room reference fields to include - EXACT import template format
  const roomReferenceFields = roomReferenceFieldsParam
    ? roomReferenceFieldsParam.split(",")
    : ["inventory_item_id", "room_number", "floor", "hotel_id"];

  // Process the data - format dates and transform status to match import template
  const processedData = inventoryItems.map((item) => {
    const data: Record<string, any> = {};

    fieldsToInclude.forEach((field) => {
      if ((field === "from_date" || field === "to_date") && item[field]) {
        // Format dates as MM/DD/YYYY to match user preference
        const date = new Date(item[field]);
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        const year = date.getFullYear();
        data[field] = `${month}/${day}/${year}`;
      } else if (field === "status" && item[field]) {
        // Transform status back to import format (lowercase internal values)
        const statusMapping = {
          "Available": "available",
          "Reserved": "reserved",
          "Maintenance": "maintenance",
          "Cleaning": "cleaning",
          "Unavailable": "unavailable",
          "On Request": "on_demand",
        };
        data[field] = statusMapping[item[field]] || item[field].toLowerCase();
      } else if (field === "is_noon_to_noon") {
        // Convert boolean to string for Excel compatibility
        data[field] = item[field] ? "true" : "false";
      } else {
        // For all other fields, use the value as-is
        data[field] = item[field] !== undefined ? item[field] : "";
      }
    });

    return data;
  });

  // Create Excel workbook
  const workbook = new ExcelJS.Workbook();

  // Sheet 1: Room Inventory (main data) - EXACT import template format
  const worksheet = workbook.addWorksheet("Room Inventory");

  // Define columns - inventory_item_id column 1, hotel_id column 2, include room config name
  worksheet.columns = [
    { header: "inventory_item_id", key: "inventory_item_id", width: 40 }, // Column 1
    { header: "hotel_id", key: "hotel_id", width: 40 },                  // Column 2
    { header: "room_number", key: "room_number", width: 15 },
    { header: "room_config_name", key: "room_config_name", width: 25 },  // Room configuration name
    { header: "from_date", key: "from_date", width: 15 },
    { header: "to_date", key: "to_date", width: 15 },
    { header: "available_quantity", key: "available_quantity", width: 15 },
    { header: "status", key: "status", width: 15 },
    { header: "notes", key: "notes", width: 40 },
    { header: "check_in_time", key: "check_in_time", width: 15 },
    { header: "check_out_time", key: "check_out_time", width: 15 },
    { header: "is_noon_to_noon", key: "is_noon_to_noon", width: 15 },
  ];

  // Style header row
  worksheet.getRow(1).font = { bold: true };
  worksheet.getRow(1).fill = {
    type: "pattern",
    pattern: "solid",
    fgColor: { argb: "FFE0E0E0" },
  };

  // Add data rows
  processedData.forEach((data) => {
    worksheet.addRow(data);
  });

  // Add data validation for status column if it exists
  const statusColumnIndex = fieldsToInclude.findIndex(
    (field) => field === "status"
  );
  if (statusColumnIndex !== -1) {
    const statusColumnLetter = String.fromCharCode(65 + statusColumnIndex); // A=65, B=66, etc.

    console.log(
      `Status column found at index ${statusColumnIndex}, column letter: ${statusColumnLetter}`
    );

    // Define valid status values with user-friendly labels
    const validStatuses = [
      "available",
      "reserved",
      "maintenance",
      "cleaning",
      "unavailable",
      "on_demand",
    ];

    // Apply data validation to the entire status column (starting from row 2, excluding header)
    const dataRange = `${statusColumnLetter}2:${statusColumnLetter}1000`; // Allow up to 1000 rows

    console.log(`Applying status validation to range: ${dataRange}`);
    console.log(`Valid statuses: ${validStatuses.join(", ")}`);

    try {
      // Try different formulae formats for better compatibility
      const validationConfig = {
        type: "list",
        allowBlank: true,
        showErrorMessage: true,
        errorStyle: "error",
        errorTitle: "Invalid Status",
        error: `Please select a valid status: ${validStatuses.join(", ")}`,
        showInputMessage: true,
        promptTitle: "Room Status",
        prompt: "Select a valid room status from the dropdown list",
      };

      // Try multiple formulae formats for better Excel compatibility
      try {
        // Method 1: Direct string list
        worksheet.dataValidations.add(dataRange, {
          ...validationConfig,
          formulae: [`"${validStatuses.join(",")}"`],
        });
        console.log(
          `✅ Status validation applied using Method 1 (direct string)`
        );
      } catch (e1) {
        try {
          // Method 2: Array format
          worksheet.dataValidations.add(dataRange, {
            ...validationConfig,
            formulae: [validStatuses],
          });
          console.log(`✅ Status validation applied using Method 2 (array)`);
        } catch (e2) {
          // Method 3: Individual strings
          worksheet.dataValidations.add(dataRange, {
            ...validationConfig,
            formulae: validStatuses,
          });
          console.log(
            `✅ Status validation applied using Method 3 (individual strings)`
          );
        }
      }

      console.log(
        `✅ Status dropdown validation applied successfully to column ${statusColumnLetter}`
      );
    } catch (validationError) {
      console.error(`❌ Error applying status validation:`, validationError);
    }

    console.log(
      `Added status dropdown validation to column ${statusColumnLetter} with values: ${validStatuses.join(
        ", "
      )}`
    );
  }

  // Add data validation for is_noon_to_noon column if it exists
  const noonToNoonColumnIndex = fieldsToInclude.findIndex(
    (field) => field === "is_noon_to_noon"
  );
  if (noonToNoonColumnIndex !== -1) {
    const noonToNoonColumnLetter = String.fromCharCode(
      65 + noonToNoonColumnIndex
    );

    // Define valid boolean values
    const validBooleans = ["true", "false"];

    // Apply data validation to the entire is_noon_to_noon column
    const dataRange = `${noonToNoonColumnLetter}2:${noonToNoonColumnLetter}1000`;

    worksheet.dataValidations.add(dataRange, {
      type: "list",
      allowBlank: true,
      formulae: [`"${validBooleans.join(",")}"`],
      showErrorMessage: true,
      errorStyle: "error",
      errorTitle: "Invalid Boolean Value",
      error: "Please select either 'true' or 'false'",
      showInputMessage: true,
      promptTitle: "Noon to Noon",
      prompt:
        "Select 'true' if check-in/check-out is noon to noon, otherwise 'false'",
    });

    console.log(
      `Added boolean dropdown validation to column ${noonToNoonColumnLetter} with values: ${validBooleans.join(
        ", "
      )}`
    );
  }

  // Sheet 2: Room Reference - EXACT import template format (always include)
  const roomSheet = workbook.addWorksheet("Rooms Reference");

  // Define columns exactly as in import template
  roomSheet.columns = [
    { header: "inventory_item_id", key: "inventory_item_id", width: 40 },
    { header: "room_number", key: "room_number", width: 15 },
    { header: "floor", key: "floor", width: 10 },
    { header: "hotel_id", key: "hotel_id", width: 40 },
  ];

  // Style header row
  roomSheet.getRow(1).font = { bold: true };
  roomSheet.getRow(1).fill = {
    type: "pattern",
    pattern: "solid",
    fgColor: { argb: "FFE0E0E0" },
  };

  // Add room reference data - always include for perfect import compatibility
  const uniqueRooms = new Set();
  Object.keys(roomInfo).forEach((inventoryItemId) => {
    if (!uniqueRooms.has(inventoryItemId)) {
      uniqueRooms.add(inventoryItemId);
      const roomData = roomInfo[inventoryItemId];

      roomSheet.addRow({
        inventory_item_id: inventoryItemId,
        room_number: roomData.room_number || "",
        floor: roomData.floor || "",
        hotel_id: roomData.hotel_id || "",
      });
    }
  });

  // Sheet 3: Instructions
  const instructionsSheet = workbook.addWorksheet("Instructions");
  instructionsSheet.columns = [
    { header: "Field", key: "field", width: 20 },
    { header: "Description", key: "description", width: 60 },
    { header: "Required", key: "required", width: 15 },
    { header: "Format", key: "format", width: 30 },
  ];

  // Style header row
  instructionsSheet.getRow(1).font = { bold: true };
  instructionsSheet.getRow(1).fill = {
    type: "pattern",
    pattern: "solid",
    fgColor: { argb: "FFE0E0E0" },
  };

  // Add instructions - include inventory_item_id for import compatibility
  const instructions = [
    {
      field: "inventory_item_id",
      description: "The ID of the inventory item (room variant). Either this OR both room_number and hotel_id must be provided.",
      required: "Conditional",
      format: "Text (UUID)",
    },
    {
      field: "room_number",
      description: "The room number (e.g., 101, 202). Required if inventory_item_id is not provided.",
      required: "Conditional",
      format: "Text",
    },
    {
      field: "room_config_name",
      description: "The room configuration/type name (e.g., Standard Room, Deluxe Suite). For reference only.",
      required: "No",
      format: "Text",
    },
    {
      field: "hotel_id",
      description: "The hotel ID where the room is located. Required if inventory_item_id is not provided.",
      required: "Conditional",
      format: "Text (UUID)",
    },
    {
      field: "from_date",
      description:
        "The start date of the inventory period. Must be in ISO format.",
      required: "Yes",
      format: "YYYY-MM-DD (e.g., 2023-12-31)",
    },
    {
      field: "to_date",
      description:
        "The end date of the inventory period. Must be in ISO format.",
      required: "Yes",
      format: "YYYY-MM-DD (e.g., 2024-01-01)",
    },
    {
      field: "available_quantity",
      description:
        "The number of rooms available (usually 0 or 1 for individual rooms)",
      required: "Yes",
      format: "Number",
    },
    {
      field: "status",
      description: "The status of the room during this period",
      required: "No",
      format: "Text (available, maintenance, reserved, booked, cleaning.)",
    },
    {
      field: "notes",
      description: "Additional notes about the inventory period",
      required: "No",
      format: "Text",
    },
    {
      field: "check_in_time",
      description: "The check-in time for this period",
      required: "No",
      format: "HH:MM (24-hour format)",
    },
    {
      field: "check_out_time",
      description: "The check-out time for this period",
      required: "No",
      format: "HH:MM (24-hour format)",
    },
    {
      field: "is_noon_to_noon",
      description: "Whether the check-in/check-out is noon to noon",
      required: "No",
      format: "true or false",
    },
  ];

  // Add a note about date formats - EXACT import template format
  instructionsSheet.addRow({
    field: "IMPORTANT NOTE",
    description:
      'Date fields must be in YYYY-MM-DD format (e.g., 2023-12-31). Excel may automatically format dates differently when you edit them. To ensure proper formatting, you may need to format the date cells as "Text" before entering dates, or use custom date formatting in Excel.',
    required: "",
    format: "",
  });

  instructions.forEach((instruction) => {
    instructionsSheet.addRow(instruction);
  });

  // Set response headers based on format
  if (format === "csv") {
    res.setHeader("Content-Type", "text/csv");
    res.setHeader(
      "Content-Disposition",
      "attachment; filename=room-inventory-export.csv"
    );

    // For CSV, we would need to implement CSV export
    // But for now, we'll just use Excel for both formats
  } else {
    res.setHeader(
      "Content-Type",
      "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
    );
    res.setHeader(
      "Content-Disposition",
      "attachment; filename=room-inventory-export.xlsx"
    );
  }

  // Write workbook to response
  await workbook.xlsx.write(res);
  console.log("Export completed successfully");
}

/**
 * Consolidate inventory items by room to avoid duplicate room numbers in export
 * Groups items by inventory_item_id and merges overlapping or adjacent date ranges
 */
function consolidateInventoryByRoom(inventoryItems: any[]): any[] {
  if (!inventoryItems || inventoryItems.length === 0) {
    return [];
  }

  // Group items by inventory_item_id (room)
  const groupedByRoom = inventoryItems.reduce((groups, item) => {
    const roomId = item.inventory_item_id;
    if (!groups[roomId]) {
      groups[roomId] = [];
    }
    groups[roomId].push(item);
    return groups;
  }, {} as Record<string, any[]>);

  const consolidatedItems: any[] = [];

  // Process each room's inventory items
  Object.entries(groupedByRoom).forEach(([roomId, roomItems]) => {
    // Sort items by from_date
    const sortedItems = roomItems.sort((a, b) =>
      new Date(a.from_date).getTime() - new Date(b.from_date).getTime()
    );

    // Merge overlapping or adjacent date ranges
    const mergedItems: any[] = [];
    let currentItem = { ...sortedItems[0] };

    for (let i = 1; i < sortedItems.length; i++) {
      const nextItem = sortedItems[i];
      const currentEnd = new Date(currentItem.to_date);
      const nextStart = new Date(nextItem.from_date);
      const nextEnd = new Date(nextItem.to_date);

      // Check if dates are adjacent or overlapping (within 1 day)
      const timeDiff = nextStart.getTime() - currentEnd.getTime();
      const oneDayInMs = 24 * 60 * 60 * 1000;

      if (timeDiff <= oneDayInMs) {
        // Merge the items - extend the end date and keep other properties from the first item
        currentItem.to_date = nextEnd > currentEnd ? nextItem.to_date : currentItem.to_date;
        // Update other properties if they differ (prefer non-empty values)
        if (nextItem.notes && nextItem.notes.trim() && !currentItem.notes?.trim()) {
          currentItem.notes = nextItem.notes;
        }
        if (nextItem.status && nextItem.status !== 'available' && currentItem.status === 'available') {
          currentItem.status = nextItem.status;
        }
      } else {
        // No overlap, save current item and start a new one
        mergedItems.push(currentItem);
        currentItem = { ...nextItem };
      }
    }

    // Add the last item
    mergedItems.push(currentItem);
    consolidatedItems.push(...mergedItems);
  });

  return consolidatedItems;
}
