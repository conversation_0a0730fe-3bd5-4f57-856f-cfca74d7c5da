import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";
import { z } from "zod";
import { HOTEL_PRICING_MODULE } from "../../../../../../modules/hotel-management/hotel-pricing";

// Validation schema for updating seasonal pricing weekday prices
export const PutAdminSeasonalPricingWeekdayPrices = z.object({
  weekday_prices: z.object({
    mon: z.number().min(0, "Monday price must be non-negative"),
    tue: z.number().min(0, "Tuesday price must be non-negative"),
    wed: z.number().min(0, "Wednesday price must be non-negative"),
    thu: z.number().min(0, "Thursday price must be non-negative"),
    fri: z.number().min(0, "Friday price must be non-negative"),
    sat: z.number().min(0, "Saturday price must be non-negative"),
    sun: z.number().min(0, "Sunday price must be non-negative"),
  }),
});

export type PutAdminSeasonalPricingWeekdayPricesType = z.infer<typeof PutAdminSeasonalPricingWeekdayPrices>;

/**
 * PUT /admin/hotel-management/seasonal-pricing/:id/update-weekday-prices
 *
 * Update weekday prices for a specific seasonal pricing rule
 */
export const PUT = async (req: MedusaRequest<PutAdminSeasonalPricingWeekdayPricesType>, res: MedusaResponse) => {
  try {
    const seasonalPriceRuleId = req.params.id;

    if (!seasonalPriceRuleId) {
      return res.status(400).json({ message: "Seasonal price rule ID is required" });
    }

    // Validate the request body
    const validationResult = PutAdminSeasonalPricingWeekdayPrices.safeParse(req.body);
    if (!validationResult.success) {
      return res.status(400).json({
        message: "Invalid request data",
        errors: validationResult.error.errors,
      });
    }

    const { weekday_prices } = validationResult.data;

    console.log(`Updating weekday prices for seasonal rule: ${seasonalPriceRuleId}`);
    console.log(`New weekday prices:`, weekday_prices);

    // Get the hotel pricing service
    const hotelPricingService = req.scope.resolve(HOTEL_PRICING_MODULE);

    // First, retrieve the existing seasonal price rule
    const existingRule = await hotelPricingService.retrieveSeasonalPriceRule(seasonalPriceRuleId);

    if (!existingRule) {
      return res.status(404).json({ message: "Seasonal price rule not found" });
    }

    // Prepare the updated metadata
    const existingMetadata = existingRule.metadata || {};
    const updatedMetadata = {
      ...existingMetadata,
      weekday_prices: weekday_prices,
      updated_at: new Date().toISOString(),
    };

    // Update the seasonal price rule with new weekday prices
    const updatedRules = await hotelPricingService.updateSeasonalPriceRules([{
      id: seasonalPriceRuleId,
      amount: weekday_prices.mon, // Use Monday's price as the base amount
      metadata: updatedMetadata,
    }]);

    const updatedRule = updatedRules[0];

    console.log(`✅ Successfully updated seasonal rule ${seasonalPriceRuleId} with new weekday prices`);

    res.json({
      message: "Seasonal pricing weekday prices updated successfully",
      seasonal_rule: {
        id: updatedRule.id,
        weekday_prices: weekday_prices,
        updated_at: updatedMetadata.updated_at,
      },
    });

  } catch (error) {
    console.error("Error updating seasonal pricing weekday prices:", error);
    res.status(500).json({
      message: "An error occurred while updating seasonal pricing weekday prices",
      error: error instanceof Error ? error.message : String(error),
    });
  }
};
