import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";
import { BOOKING_ADD_ONS_MODULE } from "../../../../../../modules/booking-add-ons";

/**
 * GET /admin/hotel-management/bookings/[id]/add-ons
 * Fetch add-ons for a specific booking
 */
export async function GET(req: MedusaRequest, res: MedusaResponse) {
  try {
    const { id: orderId } = req.params;
    console.log(`🔍 Fetching add-ons for booking ${orderId}...`);

    // Resolve the booking add-on service
    const bookingAddOnService = req.scope.resolve(BOOKING_ADD_ONS_MODULE);

    // Get booking add-ons for this order
    const result = await bookingAddOnService.getBookingAddOns(orderId);

    console.log(`✅ Found ${result.count} add-ons for booking ${orderId}`);

    return res.json({
      success: true,
      add_ons: result.booking_add_ons,
      count: result.count,
    });
  } catch (error) {
    console.error("❌ Error fetching booking add-ons:", error);
    return res.status(500).json({
      success: false,
      message: "Failed to fetch booking add-ons",
      error: error.message,
    });
  }
}
