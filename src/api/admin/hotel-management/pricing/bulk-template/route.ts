import {
  MedusaRequest,
  MedusaResponse,
} from "@camped-ai/framework/http";
import { z } from "zod";
import * as ExcelJS from 'exceljs';
import { HOTEL_PRICING_MODULE } from "src/modules/hotel-management/hotel-pricing";

// Query validation schema for bulk template generation
const GetAdminBulkHotelPricingTemplateQuery = z.object({
  currency: z.string().optional().default("CHF"),
  from_date: z.string().min(1, "From date is required"),
  to_date: z.string().min(1, "To date is required"),
  hotel_ids: z.string().min(1, "At least one hotel ID is required"), // Comma-separated hotel IDs
});

type GetAdminBulkHotelPricingTemplateQueryType = z.infer<typeof GetAdminBulkHotelPricingTemplateQuery>;

/**
 * GET /admin/hotel-management/pricing/bulk-template
 * Generate Excel template for bulk hotel pricing import with date ranges and multiple hotels
 */
export const GET = async (
  req: MedusaRequest<{}, GetAdminBulkHotelPricingTemplateQueryType>,
  res: MedusaResponse
) => {
  try {
    const query = req.scope.resolve("query");
    const hotelPricingService = req.scope.resolve(HOTEL_PRICING_MODULE);

    // Parse and validate query parameters
    const validatedQuery = GetAdminBulkHotelPricingTemplateQuery.parse(req.query);
    const { currency, from_date, to_date, hotel_ids } = validatedQuery;

    console.log(`[Bulk Pricing Template API] Generating template for hotels: ${hotel_ids}, currency: ${currency}, date range: ${from_date} to ${to_date}`);

    // Parse hotel IDs
    const targetHotelIds = hotel_ids.split(',').map(id => id.trim()).filter(id => id);

    // Get hotel details for all target hotels
    const { data: hotels } = await query.graph({
      entity: "hotel",
      filters: { id: targetHotelIds },
      fields: ["id", "name", "handle"],
    });

    if (!hotels || hotels.length === 0) {
      return res.status(404).json({
        message: "No hotels found",
        type: "not_found",
      });
    }

    console.log(`[Bulk Pricing Template API] Found ${hotels.length} hotels for template generation`);

    // Validate dates
    const startDate = new Date(from_date);
    const endDate = new Date(to_date);
    
    if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
      return res.status(400).json({
        message: "Invalid date format. Please use YYYY-MM-DD format.",
        type: "validation_error",
      });
    }
    
    if (startDate > endDate) {
      return res.status(400).json({
        message: "From date must be before or equal to To date.",
        type: "validation_error",
      });
    }

    // Get room configurations for all target hotels
    const { data: allProducts } = await query.graph({
      entity: "product",
      filters: {},
      fields: ["id", "title", "handle", "metadata"],
    });

    const roomConfigs = allProducts?.filter(product =>
      product.metadata &&
      (targetHotelIds.includes(product.metadata.hotel_id as string) || product.metadata.type === "room_config")
    ) || [];

    // Get occupancy configurations for all target hotels
    const { data: occupancyConfigs } = await query.graph({
      entity: "occupancy_config",
      filters: { hotel_id: targetHotelIds },
      fields: ["id", "name", "hotel_id"],
    });

    // Get meal plans for all target hotels
    const allMealPlans = [];
    for (const hotel of hotels) {
      const mealPlans = await hotelPricingService.listMealPlans({
        hotel_id: hotel.id,
      });
      allMealPlans.push(...mealPlans.map(mp => ({ ...mp, hotel_id: hotel.id })));
    }

    console.log(`[Bulk Pricing Template API] Found ${roomConfigs.length} room configs, ${occupancyConfigs.length} occupancy configs, ${allMealPlans.length} meal plans across ${hotels.length} hotels`);

    // Create workbook
    const workbook = new ExcelJS.Workbook();
    workbook.creator = 'Hotel Management System';
    workbook.created = new Date();

    // Create main pricing sheet
    const worksheet = workbook.addWorksheet('Hotel Pricing Import');

    // Define columns - matching requirements exactly
    const columns = [
      { header: 'Date From', key: 'date_from', width: 15 },
      { header: 'Date To', key: 'date_to', width: 15 },
      { header: 'Hotel Name', key: 'hotel_name', width: 25 },
      { header: 'Room Type', key: 'room_config_name', width: 25 },
      { header: 'Occupancy Type', key: 'occupancy_name', width: 20 },
      { header: 'Meal Plan', key: 'meal_plan_name', width: 25 },
      { header: 'Cost', key: 'cost', width: 15 },
      { header: 'Fixed Margin', key: 'fixed_margin', width: 15 },
      { header: 'Margin Percentage', key: 'margin_percentage', width: 18 },
    ];

    worksheet.columns = columns;

    // Style the header row
    const headerRow = worksheet.getRow(1);
    headerRow.font = { bold: true, color: { argb: 'FFFFFF' } };
    headerRow.fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: '4472C4' }
    };
    headerRow.alignment = { horizontal: 'center', vertical: 'middle' };
    headerRow.height = 25;

    // Generate all possible combinations: Date Range × Hotel × Room Type × Occupancy Type × Meal Plan
    const templateRows = [];
    
    // For each hotel
    for (const hotel of hotels) {
      // Get room configs for this hotel
      const hotelRoomConfigs = roomConfigs.filter(rc =>
        rc.metadata && rc.metadata.hotel_id === hotel.id
      );
      
      // Get occupancy configs for this hotel
      const hotelOccupancyConfigs = occupancyConfigs.filter(oc => 
        oc.hotel_id === hotel.id
      );
      
      // Get meal plans for this hotel
      const hotelMealPlans = allMealPlans.filter(mp => 
        mp.hotel_id === hotel.id
      );
      
      // If no specific configurations found, use defaults
      const roomConfigsToUse = hotelRoomConfigs.length > 0 ? hotelRoomConfigs : 
        [{ title: 'Standard Room', metadata: { hotel_id: hotel.id } }];
      const occupancyConfigsToUse = hotelOccupancyConfigs.length > 0 ? hotelOccupancyConfigs : 
        [{ name: '2 Adults', hotel_id: hotel.id }];
      const mealPlansToUse = hotelMealPlans.length > 0 ? hotelMealPlans : 
        [{ name: 'Room Only', hotel_id: hotel.id }];
      
      // Generate all combinations for this hotel
      for (const roomConfig of roomConfigsToUse) {
        for (const occupancyConfig of occupancyConfigsToUse) {
          for (const mealPlan of mealPlansToUse) {
            templateRows.push({
              date_from: from_date,
              date_to: to_date,
              hotel_name: hotel.name,
              room_config_name: roomConfig.title,
              occupancy_name: occupancyConfig.name,
              meal_plan_name: mealPlan.name,
              cost: '', // Empty - user input required (values in pence/cents)
              fixed_margin: '', // Empty - user input required
              margin_percentage: '', // Empty - user input required (2 decimal places)
            });
          }
        }
      }
    }

    // Add template rows to worksheet
    templateRows.forEach(row => {
      worksheet.addRow(row);
    });

    // Style data rows
    for (let i = 2; i <= templateRows.length + 1; i++) {
      const row = worksheet.getRow(i);
      row.alignment = { horizontal: 'left', vertical: 'middle' };
      
      // Alternate row colors
      if (i % 2 === 0) {
        row.fill = {
          type: 'pattern',
          pattern: 'solid',
          fgColor: { argb: 'F8F9FA' }
        };
      }
    }

    // Add borders to all cells
    const range = `A1:${String.fromCharCode(65 + columns.length - 1)}${templateRows.length + 1}`;
    worksheet.getCell(range).border = {
      top: { style: 'thin' },
      left: { style: 'thin' },
      bottom: { style: 'thin' },
      right: { style: 'thin' }
    };

    // Create reference sheets
    
    // Hotels sheet
    const hotelsSheet = workbook.addWorksheet('Hotels Reference');
    hotelsSheet.columns = [
      { header: 'Hotel Name', key: 'name', width: 30 },
      { header: 'Hotel ID', key: 'id', width: 30 },
    ];
    
    hotelsSheet.getRow(1).font = { bold: true };
    hotels.forEach(hotel => {
      hotelsSheet.addRow({ name: hotel.name, id: hotel.id });
    });

    // Room Configs sheet
    const roomConfigsSheet = workbook.addWorksheet('Room Configs Reference');
    roomConfigsSheet.columns = [
      { header: 'Room Config Name', key: 'name', width: 30 },
      { header: 'Room Config ID', key: 'id', width: 30 },
      { header: 'Hotel ID', key: 'hotel_id', width: 30 },
    ];
    
    roomConfigsSheet.getRow(1).font = { bold: true };
    roomConfigs.forEach(rc => {
      roomConfigsSheet.addRow({
        name: rc.title,
        id: rc.id,
        hotel_id: (rc.metadata?.hotel_id as string) || 'N/A'
      });
    });

    // Occupancy Configs sheet
    const occupancySheet = workbook.addWorksheet('Occupancy Reference');
    occupancySheet.columns = [
      { header: 'Occupancy Name', key: 'name', width: 25 },
      { header: 'Occupancy ID', key: 'id', width: 30 },
      { header: 'Hotel ID', key: 'hotel_id', width: 30 },
    ];
    
    occupancySheet.getRow(1).font = { bold: true };
    occupancyConfigs.forEach(oc => {
      occupancySheet.addRow({ name: oc.name, id: oc.id, hotel_id: oc.hotel_id });
    });

    // Meal Plans sheet
    const mealPlansSheet = workbook.addWorksheet('Meal Plans Reference');
    mealPlansSheet.columns = [
      { header: 'Meal Plan Name', key: 'name', width: 30 },
      { header: 'Meal Plan ID', key: 'id', width: 30 },
      { header: 'Hotel ID', key: 'hotel_id', width: 30 },
    ];
    
    mealPlansSheet.getRow(1).font = { bold: true };
    allMealPlans.forEach(mp => {
      mealPlansSheet.addRow({ name: mp.name, id: mp.id, hotel_id: mp.hotel_id });
    });

    // Instructions sheet
    const instructionsSheet = workbook.addWorksheet('Instructions');
    instructionsSheet.columns = [
      { header: 'Field', key: 'field', width: 25 },
      { header: 'Description', key: 'description', width: 60 },
      { header: 'Required', key: 'required', width: 15 },
      { header: 'Format', key: 'format', width: 20 },
    ];

    instructionsSheet.getRow(1).font = { bold: true };
    
    const instructions = [
      { field: 'Date From', description: 'Start date for the pricing period (pre-filled)', required: 'Yes', format: 'YYYY-MM-DD' },
      { field: 'Date To', description: 'End date for the pricing period (pre-filled)', required: 'Yes', format: 'YYYY-MM-DD' },
      { field: 'Hotel Name', description: 'Name of the hotel (pre-filled)', required: 'Yes', format: 'Text' },
      { field: 'Room Type', description: 'Room configuration name (pre-filled)', required: 'Yes', format: 'Text' },
      { field: 'Occupancy Type', description: 'Occupancy configuration name (pre-filled)', required: 'Yes', format: 'Text' },
      { field: 'Meal Plan', description: 'Meal plan name (pre-filled)', required: 'Yes', format: 'Text' },
      { field: 'Cost', description: 'Cost in smallest currency units (pence/cents) - USER INPUT REQUIRED', required: 'No', format: 'Number (no decimals)' },
      { field: 'Fixed Margin', description: 'Fixed margin amount - USER INPUT REQUIRED', required: 'No', format: 'Number' },
      { field: 'Margin Percentage', description: 'Margin percentage (2 decimal places) - USER INPUT REQUIRED', required: 'No', format: 'Number (2 decimals)' },
    ];

    instructions.forEach(instruction => {
      instructionsSheet.addRow(instruction);
    });

    // Generate filename
    const timestamp = new Date().toISOString().split('T')[0];
    const hotelNames = hotels.length === 1 
      ? hotels[0].name.replace(/[^a-zA-Z0-9]/g, '_')
      : `${hotels.length}_hotels`;
    const filename = `hotel_pricing_bulk_template_${hotelNames}_${currency}_${from_date}_to_${to_date}_${timestamp}.xlsx`;

    // Set response headers
    res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);

    console.log(`[Bulk Pricing Template API] Generated template with ${templateRows.length} template rows for ${hotels.length} hotels`);

    // Write workbook to response
    await workbook.xlsx.write(res);
  } catch (error) {
    console.error('Error generating bulk pricing template:', error);
    return res.status(500).json({
      message: "Failed to generate template",
      type: "internal_error",
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
};
