import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/types";
import { BOOKING_ADD_ONS_SERVICE } from "../../../../../modules/booking-add-ons";
import BookingAddOnService from "../../../../../modules/booking-add-ons/service";

/**
 * Register the service if not already registered
 */
function ensureServiceRegistered(container: any): void {
  if (!container.hasRegistration(BOOKING_ADD_ONS_SERVICE)) {
    container.register({
      [BOOKING_ADD_ONS_SERVICE]: {
        resolve: () => new BookingAddOnService(container),
      },
    });
    console.log("✅ BookingAddOnService registered in API route");
  }
}

/**
 * GET /admin/bookings/add-ons/available
 * Returns all available add-ons for booking
 */
export const GET = async (req: MedusaRequest, res: MedusaResponse) => {
  try {
    console.log("🔍 GET /admin/bookings/add-ons/available");

    // Ensure service is registered
    ensureServiceRegistered(req.scope);

    // Get the BookingAddOnService from the module
    const bookingAddOnService = req.scope.resolve(
      BOOKING_ADD_ONS_SERVICE
    ) as BookingAddOnService;

    // Get all available add-ons (no filtering for now)
    const result = await bookingAddOnService.getAvailableAddOns();

    return res.json(result);
  } catch (error) {
    console.error("❌ Error fetching available add-ons:", error);
    return res.status(500).json({
      message: "Failed to fetch available add-ons",
      error: error.message,
    });
  }
};
