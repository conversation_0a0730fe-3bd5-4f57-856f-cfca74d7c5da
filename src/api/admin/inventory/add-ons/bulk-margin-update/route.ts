import { MedusaRequest, MedusaResponse, AuthenticatedMedusaRequest } from "@camped-ai/framework/http";
import { z } from "zod";
import { Modules } from "@camped-ai/framework/utils";
import { IProductModuleService } from "@camped-ai/framework/types";

// Validation schemas
const PutAdminBulkUpdateMarginsSchema = z.object({
  updates: z.array(z.object({
    addOnId: z.string(),
    marginPercentage: z.number().min(-100).max(1000),
  })).min(1).max(100), // Limit to 100 updates at once
});

type PutAdminBulkUpdateMarginsType = z.infer<typeof PutAdminBulkUpdateMarginsSchema>;

/**
 * PUT /admin/inventory/add-ons/bulk-margin-update
 * Bulk update margins for multiple add-ons
 */
export const PUT = async (
  req: AuthenticatedMedusaRequest<PutAdminBulkUpdateMarginsType>,
  res: MedusaResponse
) => {
  try {
    console.log("🔄 PUT /admin/inventory/add-ons/bulk-margin-update - Bulk updating margins");
    console.log("Request body:", req.body);

    // Validate request body
    const validatedData = PutAdminBulkUpdateMarginsSchema.parse(req.body);

    // Get product module service
    const productModuleService: IProductModuleService = req.scope.resolve(Modules.PRODUCT);

    const results = [];
    const errors = [];

    // Process each update
    for (const update of validatedData.updates) {
      try {
        console.log(`Processing margin update for add-on ${update.addOnId}: ${update.marginPercentage}%`);

        // Get the product (add-on) with variants
        const product = await productModuleService.retrieveProduct(update.addOnId, {
          relations: ["variants", "variants.prices"]
        });

        if (!product) {
          errors.push({
            addOnId: update.addOnId,
            error: "Add-on not found"
          });
          continue;
        }

        // Check if this is actually an add-on
        const isAddOn = product.metadata?.add_on_service === true;
        if (!isAddOn) {
          errors.push({
            addOnId: update.addOnId,
            error: "Product is not an add-on"
          });
          continue;
        }

        // Get base cost from metadata
        const baseCost = parseFloat(product.metadata?.base_cost as string) || 0;
        const baseCurrency = product.metadata?.base_currency as string || "CHF";

        if (baseCost === 0) {
          errors.push({
            addOnId: update.addOnId,
            error: "Cannot update margin: base cost is not set"
          });
          continue;
        }

        // Calculate new selling price using formula: price = Net cost / (1 - margin/100)
        if (update.marginPercentage >= 100) {
          throw new Error(`Invalid margin percentage: ${update.marginPercentage}%. Margin must be less than 100%.`);
        }
        const newSellingPrice = baseCost / (1 - update.marginPercentage / 100);

        // Update all variants with new pricing
        for (const variant of product.variants || []) {
          // Update variant prices
          const updatedPrices = variant.prices?.map(price => ({
            ...price,
            amount: Math.round(newSellingPrice * 100), // Convert to cents
          })) || [];

          await productModuleService.updateProductVariants([{
            id: variant.id,
            prices: updatedPrices,
            metadata: {
              ...variant.metadata,
              calculated_margin: update.marginPercentage,
              selling_price: newSellingPrice,
              last_margin_update: new Date().toISOString(),
            }
          }]);
        }

        // Update product metadata
        await productModuleService.updateProducts([{
          id: product.id,
          metadata: {
            ...product.metadata,
            calculated_margin: update.marginPercentage,
            selling_price: newSellingPrice,
            last_margin_update: new Date().toISOString(),
          }
        }]);

        results.push({
          addOnId: update.addOnId,
          title: product.title,
          oldMargin: parseFloat(product.metadata?.calculated_margin as string) || 0,
          newMargin: update.marginPercentage,
          newSellingPrice: newSellingPrice,
        });

        console.log(`✅ Margin updated successfully for add-on ${update.addOnId}`);

      } catch (error) {
        console.error(`❌ Error updating margin for add-on ${update.addOnId}:`, error);
        errors.push({
          addOnId: update.addOnId,
          error: error instanceof Error ? error.message : "Unknown error"
        });
      }
    }

    console.log(`✅ Bulk margin update completed: ${results.length} successful, ${errors.length} errors`);

    res.json({
      success: true,
      updated_count: results.length,
      total_requested: validatedData.updates.length,
      results,
      errors,
    });

  } catch (error) {
    console.error("❌ Error during bulk margin update:", error);
    
    if (error.name === 'ZodError') {
      return res.status(400).json({
        message: "Invalid request data",
        errors: error.errors,
      });
    }

    res.status(500).json({
      message: error instanceof Error ? error.message : "Failed to update margins",
      error: error instanceof Error ? error.stack : String(error),
    });
  }
};

/**
 * POST /admin/inventory/add-ons/bulk-margin-update
 * Apply a uniform margin to multiple add-ons
 */
export const POST = async (
  req: AuthenticatedMedusaRequest<{
    addOnIds: string[];
    marginPercentage: number;
    applyToAll?: boolean;
  }>,
  res: MedusaResponse
) => {
  try {
    console.log("🔄 POST /admin/inventory/add-ons/bulk-margin-update - Applying uniform margin");
    console.log("Request body:", req.body);

    const { addOnIds, marginPercentage, applyToAll } = req.body;

    // Validate inputs
    if (!Array.isArray(addOnIds) && !applyToAll) {
      return res.status(400).json({
        message: "addOnIds must be an array or applyToAll must be true",
      });
    }

    if (typeof marginPercentage !== 'number' || marginPercentage < -100 || marginPercentage > 1000) {
      return res.status(400).json({
        message: "marginPercentage must be a number between -100 and 1000",
      });
    }

    // Get product module service
    const productModuleService: IProductModuleService = req.scope.resolve(Modules.PRODUCT);

    let targetAddOnIds = addOnIds;

    // If applyToAll is true, get all add-on IDs
    if (applyToAll) {
      const allProducts = await productModuleService.listProducts({
        metadata: {
          add_on_service: true
        }
      });
      targetAddOnIds = allProducts.map(p => p.id);
    }

    // Create updates array
    const updates = targetAddOnIds.map(id => ({
      addOnId: id,
      marginPercentage: marginPercentage,
    }));

    // Use the bulk update logic
    const bulkUpdateRequest = { updates };
    const validatedData = PutAdminBulkUpdateMarginsSchema.parse(bulkUpdateRequest);

    const results = [];
    const errors = [];

    // Process each update (same logic as PUT)
    for (const update of validatedData.updates) {
      try {
        const product = await productModuleService.retrieveProduct(update.addOnId, {
          relations: ["variants", "variants.prices"]
        });

        if (!product) {
          errors.push({
            addOnId: update.addOnId,
            error: "Add-on not found"
          });
          continue;
        }

        const isAddOn = product.metadata?.add_on_service === true;
        if (!isAddOn) {
          errors.push({
            addOnId: update.addOnId,
            error: "Product is not an add-on"
          });
          continue;
        }

        const baseCost = parseFloat(product.metadata?.base_cost as string) || 0;
        if (baseCost === 0) {
          errors.push({
            addOnId: update.addOnId,
            error: "Cannot update margin: base cost is not set"
          });
          continue;
        }

        const newSellingPrice = baseCost * (1 + update.marginPercentage / 100);

        // Update variants and product (same logic as PUT)
        for (const variant of product.variants || []) {
          const updatedPrices = variant.prices?.map(price => ({
            ...price,
            amount: Math.round(newSellingPrice * 100),
          })) || [];

          await productModuleService.updateProductVariants([{
            id: variant.id,
            prices: updatedPrices,
            metadata: {
              ...variant.metadata,
              calculated_margin: update.marginPercentage,
              selling_price: newSellingPrice,
              last_margin_update: new Date().toISOString(),
            }
          }]);
        }

        await productModuleService.updateProducts([{
          id: product.id,
          metadata: {
            ...product.metadata,
            calculated_margin: update.marginPercentage,
            selling_price: newSellingPrice,
            last_margin_update: new Date().toISOString(),
          }
        }]);

        results.push({
          addOnId: update.addOnId,
          title: product.title,
          oldMargin: parseFloat(product.metadata?.calculated_margin as string) || 0,
          newMargin: update.marginPercentage,
          newSellingPrice: newSellingPrice,
        });

      } catch (error) {
        errors.push({
          addOnId: update.addOnId,
          error: error instanceof Error ? error.message : "Unknown error"
        });
      }
    }

    console.log(`✅ Uniform margin application completed: ${results.length} successful, ${errors.length} errors`);

    res.json({
      success: true,
      updated_count: results.length,
      total_requested: targetAddOnIds.length,
      applied_margin: marginPercentage,
      results,
      errors,
    });

  } catch (error) {
    console.error("❌ Error during uniform margin application:", error);
    res.status(500).json({
      message: error instanceof Error ? error.message : "Failed to apply uniform margin",
      error: error instanceof Error ? error.stack : String(error),
    });
  }
};
