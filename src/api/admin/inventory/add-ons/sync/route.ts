import type { MedusaRequest, MedusaResponse } from "@camped-ai/framework";
import { withClient } from "src/utils/db";

/**
 * POST /admin/inventory/add-ons/sync
 * 
 * Sync product services to add-ons with proper metadata
 */
export const POST = async (
  req: MedusaRequest,
  res: MedusaResponse
) => {
  try {
    console.log("🔄 SYNC: Converting product services to add-ons...");
    
    const results = await withClient(async (client) => {
      // 1. Get all active product services that haven't been synced yet
      const productServicesQuery = `
        SELECT 
          ps.*,
          c.name as category_name,
          ut.name as unit_type_name
        FROM product_service ps
        LEFT JOIN product_service_category c ON ps.category_id = c.id
        LEFT JOIN product_service_unit_type ut ON ps.unit_type_id = ut.id
        WHERE ps.status = 'active' 
          AND ps.deleted_at IS NULL
          AND NOT EXISTS (
            SELECT 1 FROM product_variant pv 
            WHERE pv.metadata->>'supplier_product_service_id' = ps.id
          )
        ORDER BY ps.created_at DESC
      `;
      
      const productServicesResult = await client.query(productServicesQuery);
      const productServices = productServicesResult.rows;
      
      console.log(`Found ${productServices.length} new product services to sync`);
      
      if (productServices.length === 0) {
        return { 
          success: true, 
          message: "No new product services to sync",
          synced_count: 0,
          total_services: 0
        };
      }
      
      const syncedAddOns = [];
      
      // 2. For each product service, create a product and product variant with proper metadata
      for (const ps of productServices) {
        console.log(`Syncing product service: ${ps.name} (${ps.id})`);
        
        // Generate unique IDs
        const productId = `prod_addon_${ps.id.replace('ps_', '')}`;
        const variantId = `variant_addon_${ps.id.replace('ps_', '')}`;
        
        // Calculate pricing using formula: price = Net cost / (1 - margin)
        const baseCost = ps.base_cost || 50; // Default to 50 CHF if no base cost
        const margin = 0.20; // 20% margin
        const sellingPrice = Math.round((baseCost / (1 - margin)) * 100) / 100; // Round to 2 decimal places
        
        // Create comprehensive metadata with add_on_service flag
        const productMetadata = {
          add_on_service: true, // ✅ Critical flag for filtering
          service_type: ps.type?.toLowerCase() || 'service',
          supplier_product_service_id: ps.id,
          category_name: ps.category_name || 'General',
          unit_type_name: ps.unit_type_name || 'Per unit',
          base_cost: baseCost,
          selling_price: sellingPrice,
          margin_percentage: margin * 100,
          sync_source: 'admin_sync',
          sync_date: new Date().toISOString(),
          last_updated: new Date().toISOString()
        };
        
        // Create variant metadata (same as product for consistency)
        const variantMetadata = {
          add_on_service: true, // ✅ Critical flag for filtering
          service_type: ps.type?.toLowerCase() || 'service',
          supplier_product_service_id: ps.id,
          category_name: ps.category_name || 'General',
          unit_type_name: ps.unit_type_name || 'Per unit',
          base_cost: baseCost,
          selling_price: sellingPrice,
          margin_percentage: margin * 100,
          sync_source: 'admin_sync',
          sync_date: new Date().toISOString()
        };
        
        try {
          // Insert product
          const insertProductQuery = `
            INSERT INTO product (
              id, title, subtitle, description, handle, status, 
              thumbnail, weight, length, height, width, 
              origin_country, hs_code, mid_code, material, 
              collection_id, type_id, discountable, external_id, 
              metadata, created_at, updated_at
            ) VALUES (
              $1, $2, $3, $4, $5, 'published',
              null, null, null, null, null,
              null, null, null, null,
              null, null, true, null,
              $6, NOW(), NOW()
            ) ON CONFLICT (id) DO UPDATE SET
              metadata = EXCLUDED.metadata,
              updated_at = NOW()
          `;
          
          await client.query(insertProductQuery, [
            productId,
            ps.name,
            `${ps.category_name || 'Service'} - ${ps.unit_type_name || 'Per unit'}`,
            ps.description || `${ps.type || 'Service'}: ${ps.name}`,
            `addon-${ps.id.replace('ps_', '')}`,
            JSON.stringify(productMetadata)
          ]);
          
          // Insert product variant with proper metadata
          const insertVariantQuery = `
            INSERT INTO product_variant (
              id, title, sku, barcode, ean, upc, 
              weight, length, height, width, origin_country,
              hs_code, mid_code, material, metadata,
              product_id, created_at, updated_at
            ) VALUES (
              $1, $2, $3, null, null, null,
              null, null, null, null, null,
              null, null, null, $4,
              $5, NOW(), NOW()
            ) ON CONFLICT (id) DO UPDATE SET
              metadata = EXCLUDED.metadata,
              updated_at = NOW()
          `;
          
          await client.query(insertVariantQuery, [
            variantId,
            ps.name,
            `ADDON-${ps.id.replace('ps_', '').toUpperCase()}`,
            JSON.stringify(variantMetadata),
            productId
          ]);
          
          syncedAddOns.push({
            product_service_id: ps.id,
            product_service_name: ps.name,
            product_id: productId,
            variant_id: variantId,
            base_cost: baseCost,
            selling_price: sellingPrice,
            margin_percentage: margin * 100,
            metadata_flags: {
              add_on_service: true,
              service_type: ps.type?.toLowerCase() || 'service'
            }
          });
          
          console.log(`✅ Synced add-on: ${ps.name} (${variantId})`);
          
        } catch (error) {
          console.error(`❌ Failed to sync ${ps.name}:`, error);
        }
      }
      
      return {
        success: true,
        message: `Successfully synced ${syncedAddOns.length} product services to add-ons`,
        synced_count: syncedAddOns.length,
        total_services: productServices.length,
        synced_add_ons: syncedAddOns
      };
    });
    
    console.log("✅ Add-ons sync completed");
    
    return res.json(results);
    
  } catch (error) {
    console.error("❌ Error during add-ons sync:", error);
    return res.status(500).json({
      error: "Add-ons sync failed",
      message: error.message,
      success: false
    });
  }
};
