import type { MedusaRequest, MedusaResponse } from "@camped-ai/framework";
import { ContainerRegistration<PERSON>eys, Modules } from "@camped-ai/framework/utils";

/**
 * GET /admin/inventory/add-ons/:id
 *
 * Retrieve a single add-on product variant by ID.
 * This endpoint fetches a specific product variant that:
 * 1. Has ID pattern starting with "variant_addon"
 * 2. Has metadata.add_on_service = true
 * 3. Returns detailed information about the add-on
 */
export const GET = async (req: MedusaRequest, res: MedusaResponse) => {
  try {
    console.log(
      `📊 GET /admin/inventory/add-ons/${req.params.id} - Fetching single add-on`
    );

    const { id } = req.params;

    // Validate that the ID follows the expected pattern
    if (!id || !id.startsWith("variant_addon")) {
      return res.status(400).json({
        message:
          "Invalid add-on ID format. Expected ID to start with 'variant_addon'",
      });
    }

    // Get query service
    const query = req.scope.resolve(ContainerRegistrationKeys.QUERY);

    try {
      console.log("🔍 Querying product_variant table for specific add-on...");

      // Query the specific product variant
      const { data: variants } = await query.graph({
        entity: "product_variant",
        filters: {
          id: [id],
        },
        fields: [
          "id",
          "title",
          "sku",
          "metadata",
          "created_at",
          "updated_at",
          "prices.*",
        ],
      });

      if (!variants || variants.length === 0) {
        return res.status(404).json({
          message: "Add-on not found",
        });
      }

      const variant = variants[0];

      // Check if this is actually an add-on service
      if (!variant.metadata?.add_on_service) {
        return res.status(404).json({
          message: "Product variant is not an add-on service",
        });
      }

      console.log("✅ Found add-on variant:", variant.id);

      // Get custom_fields from metadata (stored as JSON object)
      const customFields = variant.metadata?.custom_fields || {};

      // Fetch hotel/destination names if available
      let hotelName = null;
      let destinationName = null;

      if (
        variant.metadata?.service_level === "hotel" &&
        variant.metadata?.hotel_id
      ) {
        try {
          // Parse hotel_id if it's a JSON string
          const hotelIds =
            typeof variant.metadata.hotel_id === "string"
              ? JSON.parse(variant.metadata.hotel_id)
              : [variant.metadata.hotel_id];

          if (hotelIds.length > 0) {
            const { data: hotels } = await query.graph({
              entity: "hotel",
              filters: { id: hotelIds },
              fields: ["id", "name"],
            });
            hotelName = hotels.map((h) => h.name).join(", ");
          }
        } catch (error) {
          console.warn(
            `Failed to fetch hotel names for variant ${variant.id}:`,
            error
          );
        }
      }

      if (
        variant.metadata?.service_level === "destination" &&
        variant.metadata?.destination_id
      ) {
        try {
          // Parse destination_id if it's a JSON string
          const destinationIds =
            typeof variant.metadata.destination_id === "string"
              ? JSON.parse(variant.metadata.destination_id)
              : [variant.metadata.destination_id];

          if (destinationIds.length > 0) {
            const { data: destinations } = await query.graph({
              entity: "destination",
              filters: { id: destinationIds },
              fields: ["id", "name"],
            });
            destinationName = destinations.map((d) => d.name).join(", ");
          }
        } catch (error) {
          console.warn(
            `Failed to fetch destination names for variant ${variant.id}:`,
            error
          );
        }
      }

      // Transform the variant data to match the expected add-on format
      const transformedAddOn = {
        id: variant.id,
        title: variant.title || "Untitled Add-on",
        sku: variant.sku,
        created_at: variant.created_at,
        updated_at: variant.updated_at,

        // Core fields from metadata
        // category: variant.metadata?.category || "Uncategorized",
        status: variant.metadata?.status || "active",
        description: variant.metadata?.description || "",

        // Industry-standard pricing columns
        cost: variant.metadata?.cost || 0,
        cost_currency: variant.metadata?.cost_currency || "CHF",
        selling_margin: variant.metadata?.selling_margin || 0,
        selling_price_cost_currency:
          variant.metadata?.selling_price_cost_currency || 0,
        selling_price: variant.metadata?.selling_price || 0,
        selling_currency: variant.metadata?.selling_currency || "CHF",

        // Additional metadata
        service_level: variant.metadata?.service_level as string,
        max_capacity: variant.metadata?.max_capacity
          ? parseInt(variant.metadata.max_capacity as string)
          : undefined,
        is_active: variant.metadata?.is_active !== "false", // Default to true unless explicitly false

        // Location information
        hotel_id: variant.metadata?.hotel_id,
        hotel_name: hotelName,
        destination_id: variant.metadata?.destination_id,
        destination_name: destinationName,

        // Custom fields as parsed object
        custom_fields: customFields,

        // Include full metadata for customer fields and other metadata
        metadata: variant.metadata,
      };

      console.log("✅ Successfully transformed add-on data");

      return res.json({
        add_on: transformedAddOn,
      });
    } catch (queryError) {
      console.error("❌ Database query error:", queryError);
      return res.status(500).json({
        message: "Failed to query add-on data",
        error:
          queryError instanceof Error
            ? queryError.message
            : "Unknown database error",
      });
    }
  } catch (error) {
    console.error("❌ GET /admin/inventory/add-ons/:id error:", error);
    return res.status(500).json({
      message: "Internal server error",
      error: error instanceof Error ? error.message : "Unknown error occurred",
    });
  }
};

/**
 * PATCH /admin/inventory/add-ons/:id
 *
 * Update pricing information for a specific add-on product variant.
 */
export const PATCH = async (req: MedusaRequest, res: MedusaResponse) => {
  try {
    console.log(
      `📝 PATCH /admin/inventory/add-ons/${req.params.id} - Updating add-on pricing`
    );

    const { id } = req.params;
    const updateData = req.body;

    // Validate that the ID follows the expected pattern
    if (!id || !id.startsWith("variant_addon")) {
      return res.status(400).json({
        message:
          "Invalid add-on ID format. Expected ID to start with 'variant_addon'",
      });
    }

    // Validate update data
    const allowedFields = [
      "cost",
      "cost_currency",
      "selling_margin",
      "selling_price_cost_currency",
      "selling_price",
      "selling_currency",
    ];

    const updateFields: Record<string, any> = {};
    for (const field of allowedFields) {
      if (updateData[field] !== undefined) {
        updateFields[field] = updateData[field];
      }
    }

    // Check if metadata is being updated
    const isMetadataUpdate = (updateData as any).metadata !== undefined;
    const isPricingUpdate = Object.keys(updateFields).length > 0;

    if (!isPricingUpdate && !isMetadataUpdate) {
      return res.status(400).json({
        message: "No valid fields provided for update",
      });
    }

    // Get query service
    const query = req.scope.resolve(ContainerRegistrationKeys.QUERY);

    try {
      // First, verify the variant exists and is an add-on
      const { data: variants } = await query.graph({
        entity: "product_variant",
        filters: { id: [id] },
        fields: ["id", "metadata"],
      });

      if (!variants || variants.length === 0) {
        return res.status(404).json({
          message: "Add-on not found",
        });
      }

      const variant = variants[0];
      if (!variant.metadata?.add_on_service) {
        return res.status(404).json({
          message: "Product variant is not an add-on service",
        });
      }

      // Get current pricing values from metadata
      const currentMetadata = variant.metadata || {};
      const currentCost = parseFloat(currentMetadata.cost as string) || 0;
      const currentMargin =
        parseFloat(currentMetadata.selling_margin as string) || 0;
      const currentCostCurrency =
        (currentMetadata.cost_currency as string) || "CHF";
      const currentSellingCurrency =
        (currentMetadata.selling_currency as string) || "CHF";

      // Initialize calculated fields for pricing updates
      let calculatedFields = isPricingUpdate ? { ...updateFields } : {};

      // Only perform pricing calculations if pricing fields are being updated
      if (isPricingUpdate) {
        // Auto-calculate selling_price_cost_currency when margin or cost changes
        if (
          updateFields.selling_margin !== undefined ||
          updateFields.cost !== undefined
        ) {
          const newCost =
            updateFields.cost !== undefined
              ? parseFloat(updateFields.cost) || 0
              : currentCost;
          const newMargin =
            updateFields.selling_margin !== undefined
              ? parseFloat(updateFields.selling_margin) || 0
              : currentMargin;

        // Calculate selling price in cost currency using formula: price = Net cost / (1 - margin/100)
        if (newMargin >= 100) {
          return res.status(400).json({
            message: "Margin percentage must be less than 100%",
          });
        }
        const sellingPriceCostCurrency = newCost / (1 - newMargin / 100);
        calculatedFields.selling_price_cost_currency = sellingPriceCostCurrency;

          console.log(
            `💰 Auto-calculated selling_price_cost_currency: ${sellingPriceCostCurrency} (cost: ${newCost}, margin: ${newMargin}%)`
          );
        }

        // Auto-calculate selling_price when selling_price_cost_currency changes or currency changes
        if (
          calculatedFields.selling_price_cost_currency !== undefined ||
          updateFields.cost_currency !== undefined ||
          updateFields.selling_currency !== undefined
        ) {
          const newSellingPriceCostCurrency =
            calculatedFields.selling_price_cost_currency !== undefined
              ? calculatedFields.selling_price_cost_currency
              : parseFloat(
                  currentMetadata.selling_price_cost_currency as string
                ) || 0;

          const newCostCurrency =
            updateFields.cost_currency || currentCostCurrency;
          const newSellingCurrency =
            updateFields.selling_currency || currentSellingCurrency;

          // For now, if currencies are the same, selling_price = selling_price_cost_currency
          // In the future, this could include currency conversion logic
          if (newCostCurrency === newSellingCurrency) {
            calculatedFields.selling_price = newSellingPriceCostCurrency;
          } else {
            // TODO: Implement currency conversion logic here
            // For now, just use the same value and log a warning
            calculatedFields.selling_price = newSellingPriceCostCurrency;
            console.log(
              `⚠️ Currency conversion needed: ${newCostCurrency} -> ${newSellingCurrency}. Using same value for now.`
            );
          }

          console.log(
            `💱 Auto-calculated selling_price: ${calculatedFields.selling_price} ${newSellingCurrency}`
          );
        }
      } // End of isPricingUpdate block

      // Prepare metadata updates
      let updatedMetadata = { ...currentMetadata };

      // Handle pricing updates
      if (isPricingUpdate) {
        updatedMetadata = {
          ...updatedMetadata,
          ...calculatedFields,
          // Add timestamp for last pricing update
          last_pricing_update: new Date().toISOString(),
        };
      }

      // Handle direct metadata updates
      if (isMetadataUpdate) {
        updatedMetadata = {
          ...updatedMetadata,
          ...(updateData as any).metadata,
          // Add timestamp for last metadata update
          last_metadata_update: new Date().toISOString(),
        };
      }

      // Use the product module service to update the variant
      const productModuleService = req.scope.resolve(Modules.PRODUCT);

      await productModuleService.updateProductVariants(id, {
        metadata: updatedMetadata,
      });

      const updateType =
        isPricingUpdate && isMetadataUpdate
          ? "pricing and metadata"
          : isPricingUpdate
          ? "pricing"
          : "metadata";

      console.log(`✅ Successfully updated add-on ${updateType} for ${id}`);

      const response: any = {
        message: `Add-on ${updateType} updated successfully`,
      };

      // Include pricing summary if pricing was updated
      if (isPricingUpdate) {
        response.updated_fields = calculatedFields;
        response.pricing_summary = {
          cost:
            calculatedFields.cost !== undefined
              ? calculatedFields.cost
              : currentCost,
          cost_currency: calculatedFields.cost_currency || currentCostCurrency,
          selling_margin:
            calculatedFields.selling_margin !== undefined
              ? calculatedFields.selling_margin
              : currentMargin,
          selling_price_cost_currency:
            calculatedFields.selling_price_cost_currency,
          selling_price: calculatedFields.selling_price,
          selling_currency:
            calculatedFields.selling_currency || currentSellingCurrency,
        };
      }

      // Include metadata if metadata was updated
      if (isMetadataUpdate) {
        response.updated_metadata = (updateData as any).metadata;
      }

      return res.json(response);
    } catch (queryError) {
      console.error("❌ Database update error:", queryError);
      return res.status(500).json({
        message: "Failed to update add-on",
        error:
          queryError instanceof Error
            ? queryError.message
            : "Unknown database error",
      });
    }
  } catch (error) {
    console.error("❌ PATCH /admin/inventory/add-ons/:id error:", error);
    return res.status(500).json({
      message: "Internal server error",
      error: error instanceof Error ? error.message : "Unknown error occurred",
    });
  }
};
