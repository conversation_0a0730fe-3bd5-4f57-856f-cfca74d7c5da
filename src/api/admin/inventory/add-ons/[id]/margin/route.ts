import { MedusaRequest, MedusaResponse, AuthenticatedMedusaRequest } from "@camped-ai/framework/http";
import { z } from "zod";
import { Modules } from "@camped-ai/framework/utils";
import { IProductModuleService } from "@camped-ai/framework/types";

// Validation schemas
const PutAdminUpdateAddOnMarginSchema = z.object({
  margin_percentage: z.number().min(-100).max(1000),
});

type PutAdminUpdateAddOnMarginType = z.infer<typeof PutAdminUpdateAddOnMarginSchema>;

/**
 * PUT /admin/inventory/add-ons/[id]/margin
 * Update margin for a specific add-on
 */
export const PUT = async (
  req: AuthenticatedMedusaRequest<PutAdminUpdateAddOnMarginType>,
  res: MedusaResponse
) => {
  try {
    const { id } = req.params;

    if (!id) {
      return res.status(400).json({
        message: "Add-on ID is required",
      });
    }

    console.log(`🔄 PUT /admin/inventory/add-ons/${id}/margin - Updating margin`);
    console.log("Request body:", req.body);

    // Validate request body
    const validatedData = PutAdminUpdateAddOnMarginSchema.parse(req.body);

    // Get product module service
    const productModuleService: IProductModuleService = req.scope.resolve(Modules.PRODUCT);

    // Get the product (add-on) with variants
    const product = await productModuleService.retrieveProduct(id, {
      relations: ["variants", "variants.prices"]
    });

    if (!product) {
      return res.status(404).json({
        message: "Add-on not found",
      });
    }

    // Check if this is actually an add-on
    const isAddOn = product.metadata?.add_on_service === true;
    if (!isAddOn) {
      return res.status(400).json({
        message: "Product is not an add-on",
      });
    }

    // Get base cost from metadata
    const baseCost = parseFloat(product.metadata?.base_cost as string) || 0;
    const baseCurrency = product.metadata?.base_currency as string || "CHF";

    if (baseCost === 0) {
      return res.status(400).json({
        message: "Cannot update margin: base cost is not set",
      });
    }

    // Calculate new selling price using formula: price = Net cost / (1 - margin/100)
    if (validatedData.margin_percentage >= 100) {
      return res.status(400).json({
        message: "Margin percentage must be less than 100%",
      });
    }
    const newSellingPrice = baseCost / (1 - validatedData.margin_percentage / 100);

    // Update all variants with new pricing
    const updatedVariants = [];
    for (const variant of product.variants || []) {
      // Update variant prices
      const updatedPrices = variant.prices?.map(price => ({
        ...price,
        amount: Math.round(newSellingPrice * 100), // Convert to cents
      })) || [];

      const updatedVariant = await productModuleService.updateProductVariants([{
        id: variant.id,
        prices: updatedPrices,
        metadata: {
          ...variant.metadata,
          calculated_margin: validatedData.margin_percentage,
          selling_price: newSellingPrice,
          last_margin_update: new Date().toISOString(),
        }
      }]);

      updatedVariants.push(updatedVariant[0]);
    }

    // Update product metadata
    const updatedProduct = await productModuleService.updateProducts([{
      id: product.id,
      metadata: {
        ...product.metadata,
        calculated_margin: validatedData.margin_percentage,
        selling_price: newSellingPrice,
        last_margin_update: new Date().toISOString(),
      }
    }]);

    console.log(`✅ Margin updated successfully for add-on ${id}`);

    res.json({
      success: true,
      addOn: {
        id: product.id,
        title: product.title,
        pricing: {
          base_cost: baseCost,
          base_currency: baseCurrency,
          total_selling_price: newSellingPrice,
          calculated_margin: validatedData.margin_percentage,
        },
      },
    });

  } catch (error) {
    console.error(`❌ Error updating margin for add-on ${req.params.id}:`, error);
    
    if (error.name === 'ZodError') {
      return res.status(400).json({
        message: "Invalid request data",
        errors: error.errors,
      });
    }

    res.status(500).json({
      message: error instanceof Error ? error.message : "Failed to update margin",
      error: error instanceof Error ? error.stack : String(error),
    });
  }
};

/**
 * GET /admin/inventory/add-ons/[id]/margin
 * Get current margin information for an add-on
 */
export const GET = async (req: MedusaRequest, res: MedusaResponse) => {
  try {
    const { id } = req.params;

    if (!id) {
      return res.status(400).json({
        message: "Add-on ID is required",
      });
    }

    console.log(`📊 GET /admin/inventory/add-ons/${id}/margin - Getting margin info`);

    // Get product module service
    const productModuleService: IProductModuleService = req.scope.resolve(Modules.PRODUCT);

    // Get the product (add-on) with variants
    const product = await productModuleService.retrieveProduct(id, {
      relations: ["variants", "variants.prices"]
    });

    if (!product) {
      return res.status(404).json({
        message: "Add-on not found",
      });
    }

    // Check if this is actually an add-on
    const isAddOn = product.metadata?.add_on_service === true;
    if (!isAddOn) {
      return res.status(400).json({
        message: "Product is not an add-on",
      });
    }

    // Get pricing information from metadata
    const baseCost = parseFloat(product.metadata?.base_cost as string) || 0;
    const baseCurrency = product.metadata?.base_currency as string || "CHF";
    const sellingPrice = parseFloat(product.metadata?.selling_price as string) || 0;
    const calculatedMargin = parseFloat(product.metadata?.calculated_margin as string) || 0;

    res.json({
      addOn: {
        id: product.id,
        title: product.title,
        pricing: {
          base_cost: baseCost,
          base_currency: baseCurrency,
          total_selling_price: sellingPrice,
          calculated_margin: calculatedMargin,
        },
        last_margin_update: product.metadata?.last_margin_update,
      },
    });

  } catch (error) {
    console.error(`❌ Error getting margin info for add-on ${req.params.id}:`, error);
    res.status(500).json({
      message: error instanceof Error ? error.message : "Failed to get margin information",
      error: error instanceof Error ? error.stack : String(error),
    });
  }
};
