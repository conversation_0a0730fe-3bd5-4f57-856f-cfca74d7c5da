import type { MedusaRequest, MedusaResponse } from "@camped-ai/framework";
import { z } from "zod";

// Validation schema for add-ons query parameters
const AdminInventoryAddOnsSchema = z.object({
  limit: z.coerce.number().optional().default(20),
  offset: z.coerce.number().optional().default(0),
  category: z.string().optional(),
  status: z.string().optional(),
  is_active: z.enum(["true", "false"]).optional(),
  search: z.string().optional(),
  currency_code: z.string().optional().default("CHF"),
  sort_by: z
    .enum([
      "title",
      "name",
      "created_at",
      "category",
      "base_cost",
      "status",
      "updated_at",
    ])
    .optional()
    .default("title"),
  sort_order: z.enum(["asc", "desc"]).optional().default("asc"),
});

/**
 * GET /admin/inventory/add-ons
 *
 * Retrieve add-on product variants from the product_variant table.
 * This endpoint filters product variants to show only those that:
 * 1. Have metadata.add_on_service = true
 * 2. Display data in the same format as Supplier Management > Products & Services
 */
export const GET = async (req: MedusaRequest, res: MedusaResponse) => {
  try {
    console.log("📊 GET /admin/inventory/add-ons - Fetching inventory add-ons");
    console.log("Query params:", req.query);

    // Validate query parameters
    const validationResult = AdminInventoryAddOnsSchema.safeParse(req.query);
    if (!validationResult.success) {
      return res.status(400).json({
        message: "Invalid query parameters",
        errors: validationResult.error.errors,
      });
    }

    const {
      limit,
      offset,
      category,
      status,
      is_active,
      search,
      currency_code,
      sort_by,
      sort_order,
    } = validationResult.data;





    // Build filters for product variants that are add-ons
    const variantFilters: Record<string, any> = {};

    // Apply search filter
    if (search) {
      variantFilters.$or = [{ title: { $ilike: `%${search}%` } }];
    }

    // Fetch product variants with add-on metadata
    let variants = [];
    let metadata = { count: 0, take: limit, skip: offset };

    // Query product_variant table for add-ons
    const query = req.scope.resolve("query");

    try {
      console.log("🔍 Querying product_variant table for add-ons...");

      // Build the base filters for add-on variants (ID pattern: variant_addon*)
      const baseFilters: Record<string, any> = {
        id: { $like: "variant_addon%" },
      };

      // Apply search filter if provided
      if (search) {
        baseFilters.$or = [{ title: { $ilike: `%${search}%` } }];
      }

      // Query all product variants first, then filter by metadata
      // Note: Medusa's query service doesn't support deep metadata filtering directly
      const { data: allVariants } = await query.graph({
        entity: "product_variant",
        filters: baseFilters,
        fields: [
          "id",
          "title",
          "sku",
          "metadata",
          "created_at",
          "updated_at",
          "prices.*",
        ],
        pagination: {
          skip: 0,
          take: 1000, // Get a large batch to filter from
        },
      });

      console.log(`Found ${allVariants?.length || 0} total product variants`);

      if (allVariants && allVariants.length > 0) {
        // Filter for add-ons based on additional criteria
        let addOnVariants = allVariants.filter((variant: any) => {
          const variantMetadata = variant.metadata || {};

          // Apply additional filters
          if (category && variantMetadata.category !== category) return false;
          if (status && variantMetadata.status !== status) return false;
          if (is_active !== undefined) {
            // Use the default status if metadata.status is undefined
            const actualStatus = variantMetadata.status || "Active";
            const isActiveValue = actualStatus === "Active";
            if (is_active === "true" && !isActiveValue) return false;
            if (is_active === "false" && isActiveValue) return false;
          }

          return true;
        });

        console.log(
          `Found ${addOnVariants.length} add-on variants after filtering`
        );

        // Apply sorting
        addOnVariants.sort((a, b) => {
          let aValue: any, bValue: any;

          switch (sort_by) {
            case "title":
            case "name":
              aValue = a.title || "";
              bValue = b.title || "";
              break;
            case "created_at":
              aValue = new Date(a.created_at).getTime();
              bValue = new Date(b.created_at).getTime();
              break;
            case "updated_at":
              aValue = new Date(a.updated_at).getTime();
              bValue = new Date(b.updated_at).getTime();
              break;

            case "category":
              aValue = a.metadata?.category || "";
              bValue = b.metadata?.category || "";
              break;
            case "base_cost":
              aValue = a.metadata?.base_cost || 0;
              bValue = b.metadata?.base_cost || 0;
              break;
            case "status":
              aValue = a.metadata?.status || "";
              bValue = b.metadata?.status || "";
              break;
            default:
              aValue = a.title || "";
              bValue = b.title || "";
          }

          if (sort_order === "desc") {
            return aValue < bValue ? 1 : aValue > bValue ? -1 : 0;
          } else {
            return aValue > bValue ? 1 : aValue < bValue ? -1 : 0;
          }
        });

        // Apply pagination
        const totalCount = addOnVariants.length;
        const paginatedVariants = addOnVariants.slice(offset, offset + limit);

        if (paginatedVariants.length > 0) {
          variants = paginatedVariants;
          metadata = { count: totalCount, take: limit, skip: offset };
          console.log(
            `✅ Using ${paginatedVariants.length} real add-on variants from database (${totalCount} total)`
          );
        } else if (totalCount > 0) {
          // There are add-ons but not in the current page
          variants = [];
          metadata = { count: totalCount, take: limit, skip: offset };
          console.log(
            `✅ Found ${totalCount} add-ons but none in current page (offset: ${offset})`
          );
        } else {
          console.log(
            "⚠️ No add-on variants found, will use mock data as fallback"
          );
        }
      } else {
        console.log(
          "⚠️ No product variants found at all, will use mock data as fallback"
        );
      }
    } catch (error) {
      console.error("❌ Error querying product_variant table:", error);
      console.log("⚠️ Will use mock data as fallback due to query error");
    }

    // Filter variants to only include add-ons (already filtered by ID pattern in query)
    const filteredVariants = variants.filter((variant: any) => {
      const metadata = variant.metadata || {};

      // Apply additional filters
      if (category && metadata.category !== category) return false;
      if (status && metadata.status !== status) return false;
      if (is_active !== undefined) {
        // Use the default status if metadata.status is undefined
        const actualStatus = metadata.status || "Active";
        const isActiveValue = actualStatus === "Active";
        if (is_active === "true" && !isActiveValue) return false;
        if (is_active === "false" && isActiveValue) return false;
      }

      return true;
    });

    // Transform the data to match supplier management page structure
    const transformedAddOns = filteredVariants.map((variant: any) => {
      const metadata = variant.metadata || {};

      // Get custom_fields from metadata (stored as JSON object)
      const customFields = metadata.custom_fields || {};

      // Extract pricing information from metadata (no longer calculating from prices)
      // Use the pricing fields stored directly in metadata

      // Note: For performance in list view, we don't fetch hotel/destination names
      // They will be fetched in the detail view when needed

      return {
        id: variant.id,
        title: variant.title || "Untitled Add-on",
        sku: variant.sku,
        created_at: variant.created_at,
        updated_at: variant.updated_at,

        // Core fields
        category: metadata.category || "Add-on",
        status: metadata.status || "Active",
        description: metadata.description || "",

        // Industry-standard pricing columns
        cost: metadata.cost || 0,
        cost_currency: metadata.cost_currency || currency_code,
        selling_margin: metadata.selling_margin || 0,
        selling_price_cost_currency: metadata.selling_price_cost_currency || 0,
        selling_price: metadata.selling_price || 0,
        selling_currency: metadata.selling_currency || currency_code,

        // Status and activity
        is_active: metadata.status === "Active",

        // Additional metadata
        service_level: metadata.service_level,
        max_capacity: metadata.max_capacity,

        // Location information (IDs only for list view)
        hotel_id: metadata.hotel_id,
        destination_id: metadata.destination_id,

        // Custom fields as parsed object
        custom_fields: customFields,
      };
    });

    // Get unique categories for filtering options
    const uniqueCategories = Array.from(
      new Set(transformedAddOns.map((addon) => addon.category).filter(Boolean))
    );

    // Get unique statuses for filtering options
    const uniqueStatuses = Array.from(
      new Set(transformedAddOns.map((addon) => addon.status).filter(Boolean))
    );

    // Apply pagination to the filtered results
    const paginatedAddOns = transformedAddOns.slice(offset, offset + limit);

    return res.json({
      add_ons: paginatedAddOns,
      count: transformedAddOns.length,
      limit: limit,
      offset: offset,

      // Additional metadata for UI
      filters: {
        categories: uniqueCategories,
        statuses: uniqueStatuses,
      },

      // Summary statistics
      summary: {
        total_add_ons: transformedAddOns.length,
        active_add_ons: transformedAddOns.filter((addon) => addon.is_active)
          .length,
        unique_categories: uniqueCategories.length,
        unique_statuses: uniqueStatuses.length,
      },
    });
  } catch (error) {
    console.error("Error fetching inventory add-ons:", error);
    return res.status(500).json({
      message: "Failed to fetch inventory add-ons",
      error: error instanceof Error ? error.message : "Unknown error",
    });
  }
};
