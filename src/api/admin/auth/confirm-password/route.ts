import {
  AuthenticatedMedusaRequest,
  MedusaResponse,
} from "@camped-ai/framework/http";
import { z } from "zod";
import { Modules } from "@camped-ai/framework/utils";
import {
  IUserModuleService,
  IAuthModuleService,
} from "@camped-ai/framework/types";

// Validation schema for password confirmation
export const PostAdminConfirmPassword = z
  .object({
    current_password: z.string().min(1, "Current password is required"),
    new_password: z
      .string()
      .min(8, "New password must be at least 8 characters long"),
    confirm_password: z.string().min(1, "Password confirmation is required"),
  })
  .refine((data) => data.new_password === data.confirm_password, {
    message: "Passwords don't match",
    path: ["confirm_password"],
  });

export type PostAdminConfirmPasswordType = z.infer<
  typeof PostAdminConfirmPassword
>;

/**
 * POST /admin/auth/confirm-password
 * Allow users to update their password and remove the requires_password_reset flag
 * This is used when users login for the first time with a temporary password
 */
export const POST = async (
  req: AuthenticatedMedusaRequest<PostAdminConfirmPasswordType>,
  res: MedusaResponse
) => {
  try {
    const { current_password, new_password } = req.body;

    // Get the authenticated user ID
    const userId = req.auth_context?.actor_id;
    if (!userId) {
      return res.status(401).json({
        type: "unauthorized",
        message: "Authentication required",
      });
    }

    // Get services
    const userService: IUserModuleService = req.scope.resolve(Modules.USER);
    const authService: IAuthModuleService = req.scope.resolve(Modules.AUTH);

    // Get user details
    const user = await userService.retrieveUser(userId);
    if (!user) {
      return res.status(404).json({
        type: "not_found",
        message: "User not found",
      });
    }

    // Verify current password by attempting authentication
    try {
      const authResult = await authService.authenticate("emailpass", {
        body: {
          email: user.email,
          password: current_password,
        },
      });

      if (!authResult.success) {
        return res.status(400).json({
          type: "invalid_credentials",
          message: "Current password is incorrect",
        });
      }
    } catch (authError) {
      return res.status(400).json({
        type: "invalid_credentials",
        message: "Current password is incorrect",
      });
    }

    // Update password using auth service
    try {
      // Find and delete the existing auth identity
      const authIdentities = await authService.listAuthIdentities();
      const userAuthIdentity = authIdentities.find(
        (identity) => identity.app_metadata?.user_id === user.id
      );

      if (userAuthIdentity) {
        await authService.deleteAuthIdentities([userAuthIdentity.id]);
      }

      // Re-register with new password
      const registrationResult = await authService.register("emailpass", {
        body: {
          email: user.email,
          password: new_password,
        },
      });

      if (registrationResult.error) {
        throw new Error(`Password update failed: ${registrationResult.error}`);
      }

      // Link the new auth identity to the user
      const newAuthIdentity = registrationResult.authIdentity;
      if (newAuthIdentity) {
        await authService.updateAuthIdentities([
          {
            id: newAuthIdentity.id,
            app_metadata: {
              user_id: user.id,
            },
          },
        ]);
      }
    } catch (updateError) {
      return res.status(500).json({
        type: "password_update_failed",
        message: `Failed to update password: ${updateError.message}`,
      });
    }

    // Update user metadata to remove password reset requirement and temp password
    const currentMetadata = user.metadata || {};
    const currentRbac =
      currentMetadata.rbac && typeof currentMetadata.rbac === "object"
        ? currentMetadata.rbac
        : {};

    // Remove temp_password from rbac for security
    const cleanRbac = { ...currentRbac };
    if ("temp_password" in cleanRbac) {
      delete cleanRbac.temp_password;
    }

    const updatedMetadata = {
      ...currentMetadata,
      rbac: {
        ...cleanRbac,
        requires_password_reset: false,
        password_confirmed_at: new Date().toISOString(),
      },
    };

    await userService.updateUsers([
      {
        id: user.id,
        metadata: updatedMetadata,
      },
    ]);

    res.status(200).json({
      message:
        "Password updated successfully. You can now access the system normally.",
      user: {
        id: user.id,
        email: user.email,
        first_name: user.first_name,
        last_name: user.last_name,
        requires_password_reset: false,
      },
    });
  } catch (error) {
    res.status(500).json({
      type: "internal_error",
      message: "An error occurred while updating the password",
    });
  }
};

/**
 * GET /admin/auth/confirm-password
 * Check if the current user needs to confirm their password
 */
export const GET = async (
  req: AuthenticatedMedusaRequest,
  res: MedusaResponse
) => {
  try {
    const userId = req.auth_context?.actor_id;
    if (!userId) {
      return res.status(401).json({
        type: "unauthorized",
        message: "Authentication required",
      });
    }

    const userService: IUserModuleService = req.scope.resolve(Modules.USER);
    const user = await userService.retrieveUser(userId);

    if (!user) {
      return res.status(404).json({
        type: "not_found",
        message: "User not found",
      });
    }

    const rbacData = user.metadata?.rbac as any;
    const requiresPasswordReset = rbacData?.requires_password_reset === true;

    res.status(200).json({
      requires_password_reset: requiresPasswordReset,
      user: {
        id: user.id,
        email: user.email,
        first_name: user.first_name,
        last_name: user.last_name,
      },
    });
  } catch (error) {
    res.status(500).json({
      type: "internal_error",
      message: "An error occurred while checking password status",
    });
  }
};
