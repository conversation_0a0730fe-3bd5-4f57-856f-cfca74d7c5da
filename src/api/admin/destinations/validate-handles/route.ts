import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";
import { ContainerRegistrationKeys } from "@camped-ai/framework/utils";
import { z } from "zod";

// Validation schema for handle validation request
const ValidateHandlesSchema = z.object({
  handles: z.array(z.string()).min(1, "At least one handle is required").max(100, "Maximum 100 handles per request"),
});

export type ValidateHandlesType = z.infer<typeof ValidateHandlesSchema>;

/**
 * POST /admin/destinations/validate-handles
 * 
 * Validate destination handles against existing destinations in the database
 */
export const POST = async (
  req: MedusaRequest<ValidateHandlesType>,
  res: MedusaResponse
) => {
  try {
    // Validate request body
    const validationResult = ValidateHandlesSchema.safeParse(req.body);
    if (!validationResult.success) {
      return res.status(400).json({
        success: false,
        message: "Invalid validation request",
        errors: validationResult.error.errors,
      });
    }

    const { handles } = validationResult.data;
    const query = req.scope.resolve(ContainerRegistrationKeys.QUERY);

    // Query existing destinations with the provided handles
    const { data: existingDestinations } = await query.graph({
      entity: "destination",
      filters: {
        handle: { $in: handles },
      },
      fields: ["id", "name", "handle"],
    });

    // Build response with duplicate information
    const duplicates = existingDestinations.map((dest: any) => ({
      handle: dest.handle,
      existingName: dest.name,
      existingId: dest.id,
    }));

    res.json({
      success: true,
      duplicates,
      total_checked: handles.length,
      duplicates_found: duplicates.length,
    });
  } catch (error) {
    console.error("Error validating destination handles:", error);
    res.status(500).json({
      success: false,
      message: "Failed to validate destination handles",
      error: error instanceof Error ? error.message : "Unknown error",
    });
  }
};
