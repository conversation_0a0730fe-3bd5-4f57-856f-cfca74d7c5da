import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";
import * as ExcelJS from 'exceljs';

/**
 * GET endpoint to download a destination import template
 */
export const GET = async (_req: MedusaRequest, res: MedusaResponse) => {
  try {
    // Create a new workbook
    const workbook = new ExcelJS.Workbook();
    const destinationsSheet = workbook.addWorksheet('Destinations');

    // Define columns with validation
    destinationsSheet.columns = [
      { header: 'Name*', key: 'name', width: 30 },
      { header: 'Description', key: 'description', width: 50 },
      { header: 'Is Active', key: 'is_active', width: 15 },
      { header: 'Country*', key: 'country', width: 20 },
      { header: 'Location', key: 'location', width: 30 },
      { header: 'Is Featured', key: 'is_featured', width: 15 },
      { header: 'Tags (comma separated)', key: 'tags', width: 30 },
      { header: 'Website', key: 'website', width: 30 },
    ];

    // Add styles to header row
    destinationsSheet.getRow(1).font = { bold: true, color: { argb: 'FF000000' } };
    destinationsSheet.getRow(1).fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FFE0E0E0' } };
    destinationsSheet.getRow(1).alignment = { vertical: 'middle', horizontal: 'center' };

    // Add data validation for boolean fields
    destinationsSheet.getColumn('is_active').eachCell({ includeEmpty: true }, (cell, rowNumber) => {
      if (rowNumber > 1) {
        cell.dataValidation = {
          type: 'list',
          allowBlank: true,
          formulae: ['"TRUE,FALSE"']
        };
      }
    });

    destinationsSheet.getColumn('is_featured').eachCell({ includeEmpty: true }, (cell, rowNumber) => {
      if (rowNumber > 1) {
        cell.dataValidation = {
          type: 'list',
          allowBlank: true,
          formulae: ['"TRUE,FALSE"']
        };
      }
    });

    // Add sample data row
    destinationsSheet.addRow({
      name: 'Paris',
      description: 'The capital of France, known for its art, fashion, and culture',
      is_active: 'TRUE',
      country: 'France',
      location: 'Europe',
      is_featured: 'TRUE',
      tags: 'europe,city,romantic,culture'
    });

    // Create FAQs sheet
    const faqsSheet = workbook.addWorksheet('FAQs');

    // Define FAQ columns
    faqsSheet.columns = [
      { header: 'Destination Name*', key: 'destination_name', width: 25 },
      { header: 'Question*', key: 'question', width: 50 },
      { header: 'Answer*', key: 'answer', width: 70 }
    ];

    // Add styles to FAQ header row
    faqsSheet.getRow(1).font = { bold: true, color: { argb: 'FF000000' } };
    faqsSheet.getRow(1).fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FFE0E0E0' } };
    faqsSheet.getRow(1).alignment = { vertical: 'middle', horizontal: 'center' };

    // Add sample FAQ data
    faqsSheet.addRow({
      destination_name: 'Paris',
      question: 'What is the best time to visit?',
      answer: 'Spring and fall offer the most pleasant weather with comfortable temperatures and fewer crowds.'
    });

    faqsSheet.addRow({
      destination_name: 'Paris',
      question: 'How do I get there?',
      answer: 'Multiple international airports serve the area including Charles de Gaulle and Orly airports.'
    });

    faqsSheet.addRow({
      destination_name: 'Paris',
      question: 'What language is spoken?',
      answer: 'French is the primary language, though English is widely understood in tourist areas.'
    });

    // Add instructions worksheet
    const instructionsSheet = workbook.addWorksheet('Instructions');
    instructionsSheet.columns = [
      { header: 'Field', key: 'field', width: 25 },
      { header: 'Required', key: 'required', width: 15 },
      { header: 'Description', key: 'description', width: 60 }
    ];

    // Style the header row
    instructionsSheet.getRow(1).font = { bold: true, color: { argb: 'FF000000' } };
    instructionsSheet.getRow(1).fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FFE0E0E0' } };

    // Add instructions for each field
    const instructions = [
      { field: '=== DESTINATIONS SHEET ===', required: '', description: '' },
      { field: 'Name', required: 'Yes', description: 'The name of the destination. A URL-friendly handle will be automatically generated from this name.' },
      { field: 'Description', required: 'No', description: 'A detailed description of the destination' },
      { field: 'Is Active', required: 'No', description: 'Whether the destination is active (TRUE or FALSE, defaults to TRUE)' },
      { field: 'Country', required: 'Yes', description: 'The country where the destination is located' },
      { field: 'Location', required: 'No', description: 'Geographic location or region' },
      { field: 'Is Featured', required: 'No', description: 'Whether the destination is featured (TRUE or FALSE, defaults to FALSE)' },
      { field: 'Tags', required: 'No', description: 'Comma-separated list of tags (e.g., "beach,summer,family")' },
      { field: '', required: '', description: '' },
      { field: '=== HANDLE GENERATION ===', required: '', description: '' },
      { field: 'Auto-Generated Handle', required: '', description: 'Handles are automatically created from the destination name (e.g., "Paris" becomes "paris"). If a duplicate exists, the country name is appended (e.g., "paris-france").' },
      { field: '', required: '', description: '' },
      { field: '=== FAQS SHEET ===', required: '', description: '' },
      { field: 'Destination Name', required: 'Yes', description: 'Must match exactly with a destination name from the Destinations sheet (e.g., "Paris" for destination named "Paris")' },
      { field: 'Question', required: 'Yes', description: 'The FAQ question text' },
      { field: 'Answer', required: 'Yes', description: 'The FAQ answer text' }
    ];

    instructions.forEach(instruction => {
      instructionsSheet.addRow(instruction);
    });

    // Add general instructions at the top
    instructionsSheet.insertRow(1, { field: 'BULK IMPORT INSTRUCTIONS', required: '', description: 'Please follow these guidelines when filling out the template.' });
    instructionsSheet.getRow(1).font = { bold: true, size: 14, color: { argb: 'FF0000FF' } };
    instructionsSheet.getRow(1).height = 30;

    instructionsSheet.insertRow(2, { field: 'Required fields', required: '', description: 'Fields marked with * are required and must be filled in.' });
    instructionsSheet.insertRow(3, { field: 'File format', required: '', description: 'Save the file as .xlsx format before uploading.' });
    instructionsSheet.insertRow(4, { field: 'Sample data', required: '', description: 'Sample data is provided in both Destinations and FAQs sheets.' });
    instructionsSheet.insertRow(5, { field: 'FAQ Import', required: '', description: 'FAQs are imported from a separate sheet. Each FAQ is one row with destination handle, question, and answer.' });
    instructionsSheet.insertRow(6, { field: '', required: '', description: '' });

    // Set the response headers for Excel download
    res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    res.setHeader('Content-Disposition', 'attachment; filename=destination-import-template.xlsx');

    // Write the workbook to the response
    await workbook.xlsx.write(res);
  } catch (error) {
    console.error('Error generating template:', error);
    res.status(500).json({ message: 'Failed to generate template' });
  }
};
