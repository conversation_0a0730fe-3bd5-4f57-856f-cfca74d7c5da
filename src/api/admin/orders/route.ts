import {
  AuthenticatedMedusaRequest,
  MedusaRequest,
  MedusaResponse,
} from "@camped-ai/framework/http";
import { ContainerRegistrationKeys } from "@camped-ai/framework/utils";
import {
  PostAdminCreateOrderType,
  GetAdminOrdersQueryType,
} from "./validators";
import { CreateOrderManagementWorkflow } from "src/workflows/order-management";

/**
 * GET /admin/orders
 * List orders with filtering and pagination
 */
export const GET = async (
  req: AuthenticatedMedusaRequest,
  res: MedusaResponse
) => {
  try {
    const query = req.scope.resolve(ContainerRegistrationKeys.QUERY);
    const {
      limit = 20,
      offset = 0,
      status,
      payment_status,
      fulfillment_status,
      customer_id,
      email,
      region_id,
      currency_code,
      created_at,
      updated_at,
      search,
      fields,
      expand,
    } = req.query as GetAdminOrdersQueryType;

    // Build filters
    const filters: Record<string, any> = {};

    if (status) {
      filters.status = status;
    }
    if (payment_status) {
      filters.payment_status = payment_status;
    }
    if (fulfillment_status) {
      filters.fulfillment_status = fulfillment_status;
    }
    if (customer_id) {
      filters.customer_id = customer_id;
    }
    if (email) {
      filters.email = { $ilike: `%${email}%` };
    }
    if (region_id) {
      filters.region_id = region_id;
    }
    if (currency_code) {
      filters.currency_code = currency_code;
    }
    if (created_at) {
      filters.created_at = created_at;
    }
    if (updated_at) {
      filters.updated_at = updated_at;
    }
    if (search) {
      filters.$or = [
        { display_id: { $ilike: `%${search}%` } },
        { email: { $ilike: `%${search}%` } },
        { customer: { first_name: { $ilike: `%${search}%` } } },
        { customer: { last_name: { $ilike: `%${search}%` } } },
      ];
    }

    // Define fields to retrieve
    const queryFields = fields ? fields.split(",") : [
      "*",
      "customer.*",
      "items.*",
      "shipping_address.*",
      "billing_address.*",
    ];

    const {
      data: orders,
      metadata: { count, take, skip },
    } = await query.graph({
      entity: "order",
      fields: queryFields,
      filters,
      pagination: {
        skip: Number(offset),
        take: Number(limit),
      },
    });

    res.json({
      orders,
      count,
      limit: take,
      offset: skip,
    });
  } catch (error) {
    console.error("Error listing orders:", error);
    res.status(500).json({
      type: "server_error",
      message: "Failed to list orders",
    });
  }
};

/**
 * POST /admin/orders
 * Create a new order
 */
export const POST = async (
  req: MedusaRequest<PostAdminCreateOrderType>,
  res: MedusaResponse
) => {
  try {
    const orderData = req.body;

    // Execute the order creation workflow
    const { result: order } = await CreateOrderManagementWorkflow(req.scope).run({
      input: orderData as any, // Type assertion to handle validation differences
    });

    res.status(201).json({ order });
  } catch (error) {
    console.error("Error creating order:", error);
    res.status(400).json({
      type: "invalid_data",
      message: error.message || "Failed to create order",
    });
  }
};
