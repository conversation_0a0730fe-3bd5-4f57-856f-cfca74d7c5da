import { medusaIntegrationTestRunner } from "@camped-ai/framework/test-utils";
import { Modules } from "@camped-ai/framework/utils";

jest.setTimeout(30000);

medusaIntegrationTestRunner({
  testSuite: ({ getContainer, api }) => {
    describe("Order Management API", () => {
      let container;
      let orderModuleService;

      beforeEach(async () => {
        container = getContainer();
        orderModuleService = container.resolve(Modules.ORDER);
      });

      describe("POST /admin/orders", () => {
        it("should create a new order", async () => {
          const orderData = {
            email: "<EMAIL>",
            currency_code: "USD",
            is_draft_order: true,
            items: [
              {
                variant_id: "variant_test",
                title: "Test Product",
                quantity: 1,
                unit_price: 1000,
              },
            ],
            metadata: {
              test: true,
            },
          };

          const response = await api.post("/admin/orders", orderData, {
            headers: {
              authorization: "Bearer test_token",
            },
          });

          expect(response.status).toBe(201);
          expect(response.data.order).toBeDefined();
          expect(response.data.order.email).toBe(orderData.email);
          expect(response.data.order.currency_code).toBe(orderData.currency_code);
        });

        it("should return 400 for invalid order data", async () => {
          const invalidOrderData = {
            // Missing required fields
          };

          const response = await api.post("/admin/orders", invalidOrderData, {
            headers: {
              authorization: "Bearer test_token",
            },
          });

          expect(response.status).toBe(400);
          expect(response.data.type).toBe("invalid_data");
        });
      });

      describe("GET /admin/orders", () => {
        it("should list orders with pagination", async () => {
          const response = await api.get("/admin/orders?limit=10&offset=0", {
            headers: {
              authorization: "Bearer test_token",
            },
          });

          expect(response.status).toBe(200);
          expect(response.data.orders).toBeDefined();
          expect(Array.isArray(response.data.orders)).toBe(true);
          expect(response.data.count).toBeDefined();
          expect(response.data.limit).toBe(10);
          expect(response.data.offset).toBe(0);
        });

        it("should filter orders by status", async () => {
          const response = await api.get("/admin/orders?status=pending", {
            headers: {
              authorization: "Bearer test_token",
            },
          });

          expect(response.status).toBe(200);
          expect(response.data.orders).toBeDefined();
        });
      });

      describe("GET /admin/orders/:id", () => {
        it("should retrieve a specific order", async () => {
          // First create an order
          const orderData = {
            email: "<EMAIL>",
            currency_code: "USD",
            is_draft_order: true,
          };

          const createResponse = await api.post("/admin/orders", orderData, {
            headers: {
              authorization: "Bearer test_token",
            },
          });

          const orderId = createResponse.data.order.id;

          // Then retrieve it
          const response = await api.get(`/admin/orders/${orderId}`, {
            headers: {
              authorization: "Bearer test_token",
            },
          });

          expect(response.status).toBe(200);
          expect(response.data.order).toBeDefined();
          expect(response.data.order.id).toBe(orderId);
        });

        it("should return 404 for non-existent order", async () => {
          const response = await api.get("/admin/orders/non_existent_id", {
            headers: {
              authorization: "Bearer test_token",
            },
          });

          expect(response.status).toBe(404);
          expect(response.data.type).toBe("not_found");
        });
      });

      describe("PUT /admin/orders/:id", () => {
        it("should update an order", async () => {
          // First create an order
          const orderData = {
            email: "<EMAIL>",
            currency_code: "USD",
            is_draft_order: true,
          };

          const createResponse = await api.post("/admin/orders", orderData, {
            headers: {
              authorization: "Bearer test_token",
            },
          });

          const orderId = createResponse.data.order.id;

          // Then update it
          const updateData = {
            status: "completed",
            metadata: {
              updated: true,
            },
          };

          const response = await api.put(`/admin/orders/${orderId}`, updateData, {
            headers: {
              authorization: "Bearer test_token",
            },
          });

          expect(response.status).toBe(200);
          expect(response.data.order).toBeDefined();
          expect(response.data.order.status).toBe("completed");
        });
      });

      describe("POST /admin/orders/:id/items", () => {
        it("should add items to an existing order", async () => {
          // First create an order
          const orderData = {
            email: "<EMAIL>",
            currency_code: "USD",
            is_draft_order: true,
          };

          const createResponse = await api.post("/admin/orders", orderData, {
            headers: {
              authorization: "Bearer test_token",
            },
          });

          const orderId = createResponse.data.order.id;

          // Then add items
          const itemsData = {
            items: [
              {
                variant_id: "variant_test_2",
                title: "Additional Product",
                quantity: 2,
                unit_price: 500,
              },
            ],
            mode: "draft",
          };

          const response = await api.post(
            `/admin/orders/${orderId}/items`,
            itemsData,
            {
              headers: {
                authorization: "Bearer test_token",
              },
            }
          );

          expect(response.status).toBe(201);
          expect(response.data.items_added).toBeDefined();
          expect(response.data.items_added.length).toBe(1);
          expect(response.data.mode).toBe("draft");
        });

        it("should return 400 for invalid items data", async () => {
          const orderId = "test_order_id";
          const invalidItemsData = {
            items: [], // Empty items array
            mode: "draft",
          };

          const response = await api.post(
            `/admin/orders/${orderId}/items`,
            invalidItemsData,
            {
              headers: {
                authorization: "Bearer test_token",
              },
            }
          );

          expect(response.status).toBe(400);
          expect(response.data.type).toBe("invalid_data");
        });
      });

      describe("DELETE /admin/orders/:id", () => {
        it("should delete a draft order", async () => {
          // First create a draft order
          const orderData = {
            email: "<EMAIL>",
            currency_code: "USD",
            is_draft_order: true,
          };

          const createResponse = await api.post("/admin/orders", orderData, {
            headers: {
              authorization: "Bearer test_token",
            },
          });

          const orderId = createResponse.data.order.id;

          // Then delete it
          const response = await api.delete(`/admin/orders/${orderId}`, {
            headers: {
              authorization: "Bearer test_token",
            },
          });

          expect(response.status).toBe(200);
          expect(response.data.deleted).toBe(true);
          expect(response.data.id).toBe(orderId);
        });
      });
    });
  },
});
