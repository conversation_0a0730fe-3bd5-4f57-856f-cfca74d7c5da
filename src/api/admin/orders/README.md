# Order Management API

This directory contains the comprehensive Order Management API implementation for the Medusa store v2 application.

## Overview

The Order Management API provides full CRUD operations for orders using Medusa workflows and following Medusa's standard patterns. It includes:

- **Order Creation**: Create new orders with items, addresses, and metadata
- **Order Retrieval**: List orders with filtering and get individual orders with full details
- **Order Updates**: Update order status, addresses, and metadata
- **Order Deletion**: Delete draft orders (with business logic restrictions)
- **Order Items Management**: Add items to existing orders with support for both draft and confirmed orders

## File Structure

```
src/api/admin/orders/
├── route.ts                    # Main orders CRUD (GET /admin/orders, POST /admin/orders)
├── [id]/
│   ├── route.ts               # Individual order operations (GET, PUT, DELETE /admin/orders/:id)
│   └── items/
│       └── route.ts           # Order items management (GET, POST /admin/orders/:id/items)
├── validators.ts              # Zod validation schemas for all endpoints
├── __tests__/
│   └── order-management.test.ts # Comprehensive test suite
└── README.md                  # This file
```

## Key Features

### 1. Comprehensive CRUD Operations
- **CREATE**: Full order creation with items, addresses, and customer data
- **READ**: List orders with advanced filtering and retrieve individual orders
- **UPDATE**: Update order details while preserving data integrity
- **DELETE**: Safe deletion with business logic validation

### 2. Order Items Management
- Add items to existing orders
- Support for both draft and confirmed orders
- Uses appropriate Medusa workflows based on order status
- Proper error handling and validation

### 3. Medusa Integration
- Uses Medusa's core workflows (`createOrderWorkflow`)
- Follows Medusa's standard API patterns
- Proper TypeScript typing with `MedusaRequest` and `MedusaResponse`
- Event emission for order operations

### 4. Validation & Security
- Comprehensive Zod validation schemas
- RBAC integration with admin/hotel manager permissions
- Input sanitization and error handling
- Business logic validation (e.g., only draft orders can be deleted)

### 5. Scalability
- Uses Medusa's dependency injection pattern
- Workflow-based architecture for reliability
- Proper compensation mechanisms for rollback
- Event-driven architecture for extensibility

## API Endpoints

### Order CRUD
- `GET /admin/orders` - List orders with filtering and pagination
- `POST /admin/orders` - Create a new order
- `GET /admin/orders/:id` - Get a specific order with full details
- `PUT /admin/orders/:id` - Update order details
- `DELETE /admin/orders/:id` - Delete a draft order

### Order Items Management
- `GET /admin/orders/:id/items` - Get all items for an order
- `POST /admin/orders/:id/items` - Add items to an existing order

## Workflows

### CreateOrderManagementWorkflow
Located in `src/workflows/order-management/create-order.ts`

- Validates order input data
- Creates orders using Medusa's `createOrderWorkflow`
- Handles customer creation/association
- Emits order creation events
- Provides compensation for rollback

### AddOrderItemsWorkflow
Located in `src/workflows/order-management/add-order-items.ts`

- Validates order existence and item data
- Supports both draft and confirmed order modes
- Uses appropriate Medusa patterns for each mode
- Handles order edits for confirmed orders
- Provides compensation for rollback

## Usage Examples

### Create an Order
```javascript
const orderData = {
  email: "<EMAIL>",
  currency_code: "USD",
  is_draft_order: true,
  items: [
    {
      variant_id: "variant_123",
      title: "Product Name",
      quantity: 2,
      unit_price: 1000
    }
  ],
  shipping_address: {
    first_name: "John",
    last_name: "Doe",
    address_1: "123 Main St",
    city: "New York",
    country_code: "US"
  }
};

const response = await fetch('/admin/orders', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': 'Bearer your_token'
  },
  body: JSON.stringify(orderData)
});
```

### Add Items to Order
```javascript
const itemsData = {
  items: [
    {
      variant_id: "variant_456",
      title: "Additional Product",
      quantity: 1,
      unit_price: 500
    }
  ],
  mode: "draft"
};

const response = await fetch('/admin/orders/order_123/items', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': 'Bearer your_token'
  },
  body: JSON.stringify(itemsData)
});
```

## Testing

Run the test suite:
```bash
npm test src/api/admin/orders/__tests__/
```

The tests cover:
- Order creation with various scenarios
- Order listing with filters
- Order retrieval and updates
- Order deletion with business logic
- Order items management
- Error handling and validation
- Authentication and authorization

## Error Handling

The API provides comprehensive error handling:

- **400 Bad Request**: Validation errors, invalid data
- **401 Unauthorized**: Missing or invalid authentication
- **403 Forbidden**: Insufficient permissions
- **404 Not Found**: Order or resource not found
- **500 Internal Server Error**: Unexpected server errors

All errors include descriptive messages and error types for proper client handling.

## Security Considerations

- All endpoints require authentication
- RBAC permissions enforced (admin or hotel manager)
- Input validation using Zod schemas
- Business logic validation for sensitive operations
- Proper error messages without sensitive data exposure

## Performance Considerations

- Efficient database queries using Medusa's query engine
- Pagination for list endpoints
- Selective field loading to reduce payload size
- Proper indexing on commonly filtered fields
- Workflow-based architecture for reliability

## Future Enhancements

Potential areas for future development:
- Bulk order operations
- Advanced order search and filtering
- Order export functionality
- Order templates and duplication
- Enhanced order edit capabilities
- Order approval workflows
- Integration with external systems

## Dependencies

- `@camped-ai/framework/http` - HTTP utilities and types
- `@camped-ai/framework/utils` - Container and utility functions
- `@camped-ai/medusa/core-flows` - Medusa core workflows
- `zod` - Runtime type validation
- Medusa Order Module - Core order functionality

## Contributing

When contributing to this module:
1. Follow existing code patterns and conventions
2. Add comprehensive tests for new functionality
3. Update documentation for API changes
4. Ensure proper error handling and validation
5. Follow Medusa's workflow patterns for complex operations
