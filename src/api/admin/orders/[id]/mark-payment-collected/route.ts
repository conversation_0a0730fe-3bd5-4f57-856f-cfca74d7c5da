import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";
import { Modules } from "@camped-ai/framework/utils";
import { z } from "zod";

/**
 * POST /admin/orders/{id}/mark-payment-collected
 * 
 * Allows sales team to mark payments as collected for:
 * - Bank transfers
 * - Manual payments
 * - Partial payment completion
 */

const MarkPaymentCollectedSchema = z.object({
  amount: z.number().positive(), // Amount collected in cents
  payment_method: z.enum(["bank_transfer", "cash", "check", "manual", "stripe_offline"]).default("bank_transfer"),
  collected_by: z.string().min(1, "Collector name/ID is required"),
  collection_date: z.string().optional(), // ISO date string
  notes: z.string().optional(),
  reference_number: z.string().optional(), // Bank transfer reference, check number, etc.
  metadata: z.record(z.any()).optional()
});

export const POST = async (req: MedusaRequest, res: MedusaResponse) => {
  try {
    const orderId = req.params.id;
    const validatedBody = MarkPaymentCollectedSchema.parse(req.body);
    
    const {
      amount,
      payment_method,
      collected_by,
      collection_date,
      notes,
      reference_number,
      metadata = {}
    } = validatedBody;

    console.log(`💰 Marking payment collected for order: ${orderId}`);
    console.log(`💵 Amount: ${amount}, Method: ${payment_method}`);

    // Get order details
    const orderModuleService = req.scope.resolve(Modules.ORDER);
    const order = await orderModuleService.retrieveOrder(orderId, {
      relations: ["payment_collections", "payment_collections.payments"]
    });

    if (!order) {
      return res.status(404).json({
        message: "Order not found"
      });
    }

    // Get current payment tracking from metadata
    const currentPaymentTracking = order.metadata?.payment_tracking || {};
    const paidAmount = currentPaymentTracking.paid_amount || 0;
    const totalAmount = order.total;
    const newPaidAmount = paidAmount + amount;
    const remainingAmount = totalAmount - newPaidAmount;

    console.log(`📊 Payment tracking: Paid: ${paidAmount} + ${amount} = ${newPaidAmount}, Remaining: ${remainingAmount}`);

    // Create payment collection record for tracking
    const collectionRecord = {
      order_id: orderId,
      amount: amount,
      payment_method: payment_method,
      collected_by: collected_by,
      collection_date: collection_date || new Date().toISOString(),
      notes: notes,
      reference_number: reference_number,
      status: "collected",
      created_at: new Date().toISOString(),
      ...metadata
    };

    // Update order metadata with payment tracking
    const updatedPaymentTracking = {
      ...currentPaymentTracking,
      paid_amount: newPaidAmount,
      remaining_amount: remainingAmount,
      requires_additional_payment: remainingAmount > 0,
      last_collection: collectionRecord,
      collections: [
        ...(currentPaymentTracking.collections || []),
        collectionRecord
      ],
      payment_status: remainingAmount <= 0 ? "fully_paid" : "partially_paid",
      updated_at: new Date().toISOString()
    };

    // Update order
    await orderModuleService.updateOrders(orderId, {
      metadata: {
        ...order.metadata,
        payment_tracking: updatedPaymentTracking,
        // Update concierge notes
        concierge_notes: remainingAmount <= 0 
          ? "Payment fully collected"
          : `Remaining payment: ${remainingAmount} ${order.currency_code} to be collected`
      }
    });

    // If fully paid, update order status
    if (remainingAmount <= 0) {
      await orderModuleService.updateOrders(orderId, {
        status: "completed" // or whatever status indicates full payment
      });
      console.log(`✅ Order ${orderId} marked as fully paid and completed`);
    }

    res.json({
      success: true,
      order_id: orderId,
      collection_record: collectionRecord,
      payment_summary: {
        total_amount: totalAmount,
        previously_paid: paidAmount,
        amount_collected: amount,
        new_paid_amount: newPaidAmount,
        remaining_amount: remainingAmount,
        currency_code: order.currency_code,
        is_fully_paid: remainingAmount <= 0,
        payment_status: remainingAmount <= 0 ? "fully_paid" : "partially_paid"
      },
      message: remainingAmount <= 0 
        ? `Payment fully collected. Order completed.`
        : `Payment of ${amount} ${order.currency_code} collected. Remaining: ${remainingAmount} ${order.currency_code}`,
      next_steps: remainingAmount <= 0 
        ? ["Order is fully paid and completed"]
        : [
            `Collect remaining ${remainingAmount} ${order.currency_code}`,
            "Update customer on payment status",
            "Schedule follow-up if needed"
          ]
    });

  } catch (error) {
    console.error("Error marking payment as collected:", error);
    
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        message: "Validation error",
        errors: error.errors
      });
    }

    res.status(500).json({
      message: "Failed to mark payment as collected",
      error: error instanceof Error ? error.message : "Unknown error"
    });
  }
};

/**
 * GET /admin/orders/{id}/payment-status
 * 
 * Get current payment status and collection history
 */
export const GET = async (req: MedusaRequest, res: MedusaResponse) => {
  try {
    const orderId = req.params.id;

    const orderModuleService = req.scope.resolve(Modules.ORDER);
    const order = await orderModuleService.retrieveOrder(orderId);

    if (!order) {
      return res.status(404).json({
        message: "Order not found"
      });
    }

    const paymentTracking = order.metadata?.payment_tracking || {};
    const totalAmount = order.total;
    const paidAmount = paymentTracking.paid_amount || 0;
    const remainingAmount = totalAmount - paidAmount;

    res.json({
      order_id: orderId,
      payment_status: {
        total_amount: totalAmount,
        paid_amount: paidAmount,
        remaining_amount: remainingAmount,
        currency_code: order.currency_code,
        is_fully_paid: remainingAmount <= 0,
        payment_status: remainingAmount <= 0 ? "fully_paid" : "partially_paid",
        requires_additional_payment: remainingAmount > 0
      },
      collection_history: paymentTracking.collections || [],
      last_collection: paymentTracking.last_collection || null,
      order_status: order.status,
      created_at: order.created_at,
      updated_at: order.updated_at
    });

  } catch (error) {
    console.error("Error getting payment status:", error);
    res.status(500).json({
      message: "Failed to get payment status",
      error: error instanceof Error ? error.message : "Unknown error"
    });
  }
};
