import {
  MedusaRequest,
  MedusaResponse,
} from "@camped-ai/framework/http";
import { ContainerRegistrationKeys, Modules } from "@camped-ai/framework/utils";
import {
  PostAdminOrderItemsType,
} from "../../validators";
import { AddOrderItemsWorkflow } from "../../../../../workflows/order-management";

/**
 * POST /admin/orders/[id]/items
 * Add order items and order line items to existing orders
 * 
 * This endpoint supports adding items to both draft and confirmed orders:
 * - For draft orders: Uses addDraftOrderItemsWorkflow pattern
 * - For confirmed orders: Uses orderEditAddNewItemWorkflow pattern
 * 
 * Request body:
 * {
 *   "order_id": "order_123", // Optional, will use URL param if not provided
 *   "items": [
 *     {
 *       "variant_id": "variant_123",
 *       "quantity": 2,
 *       "title": "Product Title",
 *       "unit_price": 1000,
 *       "metadata": {}
 *     }
 *   ],
 *   "mode": "draft" | "confirmed" // Determines which workflow to use
 * }
 */
export const POST = async (
  req: MedusaRequest<PostAdminOrderItemsType>,
  res: MedusaResponse
) => {
  try {
    const { id: orderIdFromUrl } = req.params;
    const { order_id: orderIdFromBody, items, mode = "draft" } = req.body;

    // Use order ID from URL parameter, fallback to body
    const order_id = orderIdFromUrl || orderIdFromBody;

    if (!order_id) {
      return res.status(400).json({
        type: "invalid_data",
        message: "Order ID is required either in URL or request body",
      });
    }

    if (!items || items.length === 0) {
      return res.status(400).json({
        type: "invalid_data",
        message: "At least one item is required",
      });
    }

    // Validate mode
    if (!["draft", "confirmed"].includes(mode)) {
      return res.status(400).json({
        type: "invalid_data",
        message: "Mode must be either 'draft' or 'confirmed'",
      });
    }

    console.log(`🔄 Adding ${items.length} items to order ${order_id} in ${mode} mode`);

    // // Execute the add order items workflow
    // const { result } = await AddOrderItemsWorkflow(req.scope).run({
    //   input: {
    //     order_id,
    //     items: items as any, // Type assertion to handle validation differences
    //     mode,
    //   },
    // }); 

     const { result } = await AddOrderItemsWorkflow(req.scope).run({
      input: {
        order_id,
        items: items as any,
        mode,
      },
    });


    console.log(`✅ Successfully added ${result.addedItems.length} items to order ${order_id}`);

    res.status(201).json({
      order_id,
      items_added: result.addedItems,
      mode: result.mode,
      message: `Successfully added ${result.addedItems.length} items to order`,
    });
  } catch (error) {
    console.error("❌ Error adding items to order:", error);
    
    // Handle specific error types
    if (error.message.includes("not found")) {
      return res.status(404).json({
        type: "not_found",
        message: error.message,
      });
    }
    
    if (error.message.includes("Cannot add items") || error.message.includes("Invalid mode")) {
      return res.status(400).json({
        type: "invalid_operation",
        message: error.message,
      });
    }

    res.status(400).json({
      type: "invalid_data",
      message: error.message || "Failed to add items to order",
    });
  }
};

/**
 * GET /admin/orders/[id]/items
 * Retrieve all items for a specific order
 */
export const GET = async (req: MedusaRequest, res: MedusaResponse) => {
  try {
    const { id: order_id } = req.params;

    if (!order_id) {
      return res.status(400).json({
        type: "invalid_data",
        message: "Order ID is required",
      });
    }

    const query = req.scope.resolve(ContainerRegistrationKeys.QUERY);

    // Get the order with its items
    const { data: orders } = await query.graph({
      entity: "order",
      fields: [
        "id",
        "items.*",
      ],
      filters: { id: order_id },
    });

    if (!orders || orders.length === 0) {
      return res.status(404).json({
        type: "not_found",
        message: "Order not found",
      });
    }

    const items = orders[0].items || [];

    res.json({
      order_id,
      items,
      count: items.length,
    });
  } catch (error) {
    console.error("Error retrieving order items:", error);
    res.status(500).json({
      type: "server_error",
      message: "Failed to retrieve order items",
    });
  }
};
