import {
  AuthenticatedMedusaRequest,
  MedusaRequest,
  MedusaResponse,
} from "@camped-ai/framework/http";
import { ContainerRegistrationKeys, Modules } from "@camped-ai/framework/utils";
import {
  PutAdminUpdateOrder,
  PutAdminUpdateOrderType,
} from "../validators";

/**
 * GET /admin/orders/[id]
 * Retrieve a specific order with full details
 */
export const GET = async (req: MedusaRequest, res: MedusaResponse) => {
  try {
    const { id } = req.params;

    if (!id) {
      return res.status(400).json({
        type: "invalid_data",
        message: "Order ID is required",
      });
    }

    const query = req.scope.resolve(ContainerRegistrationKeys.QUERY);

    // Get the order with basic related data
    const { data: orders } = await query.graph({
      entity: "order",
      fields: [
        "*",
        "customer.*",
        "items.*",
        "shipping_address.*",
        "billing_address.*",
      ],
      filters: { id },
    });

    if (!orders || orders.length === 0) {
      return res.status(404).json({
        type: "not_found",
        message: "Order not found",
      });
    }

    res.json({ order: orders[0] });
  } catch (error) {
    console.error("Error retrieving order:", error);
    res.status(500).json({
      type: "server_error",
      message: "Failed to retrieve order",
    });
  }
};

/**
 * PUT /admin/orders/[id]
 * Update order details
 */
export const PUT = async (
  req: MedusaRequest<PutAdminUpdateOrderType>,
  res: MedusaResponse
) => {
  try {
    const { id } = req.params;
    const updateData = req.body;

    if (!id) {
      return res.status(400).json({
        type: "invalid_data",
        message: "Order ID is required",
      });
    }

    const orderModuleService = req.scope.resolve(Modules.ORDER);

    // Check if order exists
    const existingOrder = await orderModuleService.retrieveOrder(id);
    if (!existingOrder) {
      return res.status(404).json({
        type: "not_found",
        message: "Order not found",
      });
    }

    // Prepare update data
    const updatePayload: any = {};

    if (updateData.status !== undefined) {
      updatePayload.status = updateData.status;
    }
    if (updateData.email !== undefined) {
      updatePayload.email = updateData.email;
    }
    if (updateData.metadata !== undefined) {
      updatePayload.metadata = {
        ...existingOrder.metadata,
        ...updateData.metadata,
      };
    }

    // Update addresses if provided
    if (updateData.shipping_address) {
      updatePayload.shipping_address = {
        ...existingOrder.shipping_address,
        ...updateData.shipping_address,
      };
    }
    if (updateData.billing_address) {
      updatePayload.billing_address = {
        ...existingOrder.billing_address,
        ...updateData.billing_address,
      };
    }

    // Update the order
    const updatedOrder = await orderModuleService.updateOrders(id, updatePayload);

    // Retrieve the updated order with full details
    const query = req.scope.resolve(ContainerRegistrationKeys.QUERY);
    const { data: orders } = await query.graph({
      entity: "order",
      fields: [
        "*",
        "customer.*",
        "items.*",
        "shipping_address.*",
        "billing_address.*",
      ],
      filters: { id },
    });

    res.json({ order: orders[0] });
  } catch (error) {
    console.error("Error updating order:", error);
    res.status(400).json({
      type: "invalid_data",
      message: error.message || "Failed to update order",
    });
  }
};

/**
 * DELETE /admin/orders/[id]
 * Delete an order (only for draft orders or specific business logic)
 */
export const DELETE = async (req: MedusaRequest, res: MedusaResponse) => {
  try {
    const { id } = req.params;

    if (!id) {
      return res.status(400).json({
        type: "invalid_data",
        message: "Order ID is required",
      });
    }

    const orderModuleService = req.scope.resolve(Modules.ORDER);

    // Check if order exists
    const existingOrder = await orderModuleService.retrieveOrder(id);
    if (!existingOrder) {
      return res.status(404).json({
        type: "not_found",
        message: "Order not found",
      });
    }

    // Business logic: Only allow deletion of draft orders or specific statuses
    if (!existingOrder.is_draft_order && existingOrder.status !== "pending") {
      return res.status(400).json({
        type: "invalid_operation",
        message: "Only draft orders or pending orders can be deleted",
      });
    }

    // Delete the order
    await orderModuleService.deleteOrders([id]);

    res.status(200).json({
      id,
      deleted: true,
    });
  } catch (error) {
    console.error("Error deleting order:", error);
    res.status(400).json({
      type: "invalid_data",
      message: error.message || "Failed to delete order",
    });
  }
};
