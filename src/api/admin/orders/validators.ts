import { z } from "zod";

// Order Item Schema for creating order items
export const OrderItemSchema = z.object({
  title: z.string().min(1, "Title is required"),
  variant_id: z.string().min(1, "Variant ID is required"),
  quantity: z.number().int().min(1, "Quantity must be at least 1"),
  unit_price: z.number().min(0, "Unit price must be non-negative"),
  metadata: z.record(z.any()).optional(),
});

// Order Line Item Schema for more detailed line items
export const OrderLineItemSchema = z.object({
  variant_id: z.string().min(1, "Variant ID is required"),
  product_id: z.string().optional(),
  product_title: z.string().optional(),
  product_type: z.string().optional(),
  prodcut_handle: z.string().optional(),
  variant_sku: z.string().optional(),
  variant_title: z.string().optional(),
  title: z.string().min(1, "Title is required"),
  subtitle: z.string().optional(),
  thumbnail: z.string().optional(),
  quantity: z.number().int().min(1, "Quantity must be at least 1"),
  unit_price: z.number().min(0, "Unit price must be non-negative"),
  is_tax_inclusive: z.boolean().default(false),
  compare_at_unit_price: z.number().optional(),
  metadata: z.record(z.any()).optional(),
});

// Schema for adding items to existing orders
export const PostAdminOrderItems = z.object({
  order_id: z.string().min(1, "Order ID is required"),
  items: z.array(OrderItemSchema).min(1, "At least one item is required"),
  mode: z.enum(["draft", "confirmed"]).default("draft"),
});

// Schema for creating new orders
export const PostAdminCreateOrder = z.object({
  region_id: z.string().optional(),
  customer_id: z.string().optional(),
  sales_channel_id: z.string().optional(),
  email: z.string().email().optional(),
  currency_code: z.string().length(3, "Currency code must be 3 characters").default("USD"),
  is_draft_order: z.boolean().default(true),
  status: z.enum(["pending", "completed", "archived", "canceled", "requires_action"]).optional(),
  items: z.array(OrderLineItemSchema).optional(),
  shipping_address: z.object({
    first_name: z.string().optional(),
    last_name: z.string().optional(),
    address_1: z.string().optional(),
    address_2: z.string().optional(),
    city: z.string().optional(),
    country_code: z.string().optional(),
    province: z.string().optional(),
    postal_code: z.string().optional(),
    phone: z.string().optional(),
    company: z.string().optional(),
  }).optional(),
  billing_address: z.object({
    first_name: z.string().optional(),
    last_name: z.string().optional(),
    address_1: z.string().optional(),
    address_2: z.string().optional(),
    city: z.string().optional(),
    country_code: z.string().optional(),
    province: z.string().optional(),
    postal_code: z.string().optional(),
    phone: z.string().optional(),
    company: z.string().optional(),
  }).optional(),
  metadata: z.record(z.any()).optional(),
});

// Schema for updating orders
export const PutAdminUpdateOrder = z.object({
  status: z.enum(["pending", "completed", "archived", "canceled", "requires_action"]).optional(),
  email: z.string().email().optional(),
  shipping_address: z.object({
    first_name: z.string().optional(),
    last_name: z.string().optional(),
    address_1: z.string().optional(),
    address_2: z.string().optional(),
    city: z.string().optional(),
    country_code: z.string().optional(),
    province: z.string().optional(),
    postal_code: z.string().optional(),
    phone: z.string().optional(),
    company: z.string().optional(),
  }).optional(),
  billing_address: z.object({
    first_name: z.string().optional(),
    last_name: z.string().optional(),
    address_1: z.string().optional(),
    address_2: z.string().optional(),
    city: z.string().optional(),
    country_code: z.string().optional(),
    province: z.string().optional(),
    postal_code: z.string().optional(),
    phone: z.string().optional(),
    company: z.string().optional(),
  }).optional(),
  metadata: z.record(z.any()).optional(),
});

// Schema for querying orders
export const GetAdminOrdersQuery = z.object({
  limit: z.coerce.number().min(1).max(100).default(20),
  offset: z.coerce.number().min(0).default(0),
  status: z.enum(["pending", "completed", "archived", "canceled", "requires_action"]).optional(),
  payment_status: z.enum(["not_paid", "awaiting", "captured", "partially_refunded", "refunded", "canceled", "requires_action"]).optional(),
  fulfillment_status: z.enum(["not_fulfilled", "partially_fulfilled", "fulfilled", "partially_shipped", "shipped", "partially_returned", "returned", "canceled", "requires_action"]).optional(),
  customer_id: z.string().optional(),
  email: z.string().optional(),
  region_id: z.string().optional(),
  currency_code: z.string().optional(),
  created_at: z.object({
    lt: z.string().datetime().optional(),
    gt: z.string().datetime().optional(),
    gte: z.string().datetime().optional(),
    lte: z.string().datetime().optional(),
  }).optional(),
  updated_at: z.object({
    lt: z.string().datetime().optional(),
    gt: z.string().datetime().optional(),
    gte: z.string().datetime().optional(),
    lte: z.string().datetime().optional(),
  }).optional(),
  search: z.string().optional(),
  fields: z.string().optional(),
  expand: z.string().optional(),
});

// Schema for deleting orders
export const DeleteAdminOrder = z.object({
  id: z.string().min(1, "Order ID is required"),
});

// Type exports for use in API routes
export type PostAdminOrderItemsType = z.infer<typeof PostAdminOrderItems>;
export type PostAdminCreateOrderType = z.infer<typeof PostAdminCreateOrder>;
export type PutAdminUpdateOrderType = z.infer<typeof PutAdminUpdateOrder>;
export type GetAdminOrdersQueryType = z.infer<typeof GetAdminOrdersQuery>;
export type DeleteAdminOrderType = z.infer<typeof DeleteAdminOrder>;
export type OrderItemType = z.infer<typeof OrderItemSchema>;
export type OrderLineItemType = z.infer<typeof OrderLineItemSchema>;
