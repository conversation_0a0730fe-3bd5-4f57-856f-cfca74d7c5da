import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";
import { z } from "zod";
import ItineraryService from "../../../../modules/concierge-management/itinerary-service";
import { asClass } from "awilix";
import { ITINERARY_SERVICE } from "../../../../modules/concierge-management";

// Validation schemas
export const PostAdminCreateItinerary = z.object({
  booking_id: z.string(),
  title: z.string().optional(),
  status: z.enum(["DRAFT", "FINALIZED"]).default("DRAFT"),
  created_by: z.string().optional(),
});

export const GetAdminListItineraries = z.object({
  booking_id: z.string().optional(),
  status: z.enum(["DRAFT", "FINALIZED"]).optional(),
  created_by: z.string().optional(),
});

export type PostAdminCreateItineraryType = z.infer<typeof PostAdminCreateItinerary>;
export type GetAdminListItinerariesType = z.infer<typeof GetAdminListItineraries>;

/**
 * Ensure itinerary service is registered with both constant and string keys
 */
function ensureItineraryServiceRegistered(container: any): any {
  // Register with constant key
  if (!container.hasRegistration(ITINERARY_SERVICE)) {
    container.register({
      [ITINERARY_SERVICE]: asClass(ItineraryService).singleton(),
    });
    console.log("✅ ItineraryService registered with constant key in API route");
  }

  // Register with string key for backward compatibility
  if (!container.hasRegistration("itineraryService")) {
    container.register({
      itineraryService: asClass(ItineraryService).singleton(),
    });
    console.log("✅ ItineraryService registered with string key in API route");
  }

  return container.resolve("itineraryService");
}

/**
 * POST /admin/concierge-management/itineraries
 * Create a new itinerary
 */
export async function POST(req: MedusaRequest, res: MedusaResponse) {
  try {
    const validatedData = PostAdminCreateItinerary.parse(req.body);

    // Ensure the service is registered and get it
    const itineraryService = ensureItineraryServiceRegistered(req.scope);
    
    const itinerary = await itineraryService.createItineraries(validatedData);
    
    res.status(201).json({
      itinerary,
      message: "Itinerary created successfully",
    });
  } catch (error) {
    console.error("Error creating itinerary:", error);
    res.status(400).json({
      error: error instanceof Error ? error.message : "Failed to create itinerary",
    });
  }
}

/**
 * GET /admin/concierge-management/itineraries
 * List itineraries with filtering
 */
export async function GET(req: MedusaRequest, res: MedusaResponse) {
  try {
    console.log("🚀 [ITINERARIES-LIST] API endpoint called");
    console.log("🔍 [ITINERARIES-LIST] Query params:", req.query);

    const validatedQuery = GetAdminListItineraries.parse(req.query);
    console.log("✅ [ITINERARIES-LIST] Validated query:", validatedQuery);

    // Ensure the service is registered and get it
    console.log("🔧 [ITINERARIES-LIST] Ensuring itinerary service is registered...");
    const itineraryService = ensureItineraryServiceRegistered(req.scope);
    console.log("✅ [ITINERARIES-LIST] Itinerary service resolved successfully");

    console.log("📋 [ITINERARIES-LIST] Calling listAllItineraries...");
    const itineraries = await itineraryService.listAllItineraries(validatedQuery);
    console.log("✅ [ITINERARIES-LIST] Found", itineraries?.length || 0, "itineraries");

    const response = {
      itineraries: Array.isArray(itineraries) ? itineraries : [],
      count: Array.isArray(itineraries) ? itineraries.length : 0,
    };

    console.log("📤 [ITINERARIES-LIST] Sending response:", response);
    res.json(response);
  } catch (error) {
    console.error("❌ [ITINERARIES-LIST] Error listing itineraries:", error);
    res.status(400).json({
      error: error instanceof Error ? error.message : "Failed to list itineraries",
    });
  }
}
