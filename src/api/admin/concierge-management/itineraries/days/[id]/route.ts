import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";
import { z } from "zod";
import ItineraryService from "../../../../../../modules/concierge-management/itinerary-service";

// Validation schemas
export const PatchUpdateItineraryDay = z.object({
  date: z.string().transform((str) => new Date(str)).optional(),
  title: z.string().optional(),
});

export type PatchUpdateItineraryDayType = z.infer<typeof PatchUpdateItineraryDay>;

/**
 * PATCH /admin/concierge-management/itineraries/days/:id
 * Update an itinerary day
 */
export async function PATCH(req: MedusaRequest, res: MedusaResponse) {
  try {
    const { id: dayId } = req.params;
    const validatedData = PatchUpdateItineraryDay.parse(req.body);
    
    const itineraryService: ItineraryService = req.scope.resolve("itineraryService");
    
    const day = await itineraryService.updateItineraryDays(dayId, validatedData);
    
    res.json({
      day,
      message: "Day updated successfully",
    });
  } catch (error) {
    console.error("Error updating day:", error);
    res.status(400).json({
      error: error instanceof Error ? error.message : "Failed to update day",
    });
  }
}

/**
 * DELETE /admin/concierge-management/itineraries/days/:id
 * Delete an itinerary day and all its events
 */
export async function DELETE(req: MedusaRequest, res: MedusaResponse) {
  try {
    const { id: dayId } = req.params;
    
    const itineraryService: ItineraryService = req.scope.resolve("itineraryService");
    
    await itineraryService.deleteDay(dayId);
    
    res.json({
      message: "Day deleted successfully",
    });
  } catch (error) {
    console.error("Error deleting day:", error);
    res.status(400).json({
      error: error instanceof Error ? error.message : "Failed to delete day",
    });
  }
}
