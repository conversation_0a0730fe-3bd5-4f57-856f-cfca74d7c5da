import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";
import { z } from "zod";
import ItineraryService from "../../../../../../../modules/concierge-management/itinerary-service";

// Validation schemas
export const PostAddItineraryEvent = z.object({
  category: z.string(), // Dynamic category name from supplier offerings
  type: z.string().optional(),
  title: z.string(),
  notes: z.string().optional(),
  start_time: z.string().optional(), // HH:MM format
  end_time: z.string().optional(),
  duration: z.string().optional(),
  timezone: z.string().optional(),
  details: z.record(z.any()).optional(),
  price: z.number().optional(),
  currency: z.string().optional(),
  media: z.array(z.string()).optional(),
  // New fields for supplier offering integration
  supplier_offering_id: z.string().optional(),
  category_id: z.string().optional(),
  supplier_id: z.string().optional(),
  from_date: z.string().optional(), // ISO date string
  to_date: z.string().optional(), // ISO date string
});

export type PostAddItineraryEventType = z.infer<typeof PostAddItineraryEvent>;

/**
 * POST /admin/concierge-management/itineraries/days/:dayId/events
 * Add a new event to a day
 */
export async function POST(req: MedusaRequest, res: MedusaResponse) {
  try {
    const { dayId } = req.params;

    if (!dayId) {
      return res.status(400).json({ error: "Day ID is required" });
    }

    console.log('🚀 [EVENT-CREATE] Raw request body:', JSON.stringify(req.body, null, 2));

    const validatedData = PostAddItineraryEvent.parse(req.body);

    console.log('✅ [EVENT-CREATE] Validated data:', JSON.stringify(validatedData, null, 2));

    const itineraryService: ItineraryService = req.scope.resolve("itineraryService");
    console.log('🔧 [EVENT-CREATE] Service resolved, calling addEventToDay...');

    const event = await itineraryService.addEventToDay(dayId, validatedData as any);

    console.log('✅ [EVENT-CREATE] Event created successfully:', JSON.stringify(event, null, 2));

    res.status(201).json({
      event,
      message: "Event added successfully",
    });
  } catch (error) {
    console.error("❌ [EVENT-CREATE] Error adding event to day:", error);
    console.error("❌ [EVENT-CREATE] Error stack:", error instanceof Error ? error.stack : 'No stack trace');
    res.status(400).json({
      error: error instanceof Error ? error.message : "Failed to add event to day",
    });
  }
}

/**
 * GET /admin/concierge-management/itineraries/days/:dayId/events
 * Get all events for a specific day
 */
export async function GET(req: MedusaRequest, res: MedusaResponse) {
  try {
    const { dayId } = req.params;
    
    const itineraryService: ItineraryService = req.scope.resolve("itineraryService");
    
    const events = await itineraryService.getEventsForDay(dayId);
    
    res.json({
      events,
      count: events.length,
    });
  } catch (error) {
    console.error("Error getting events for day:", error);
    res.status(400).json({
      error: error instanceof Error ? error.message : "Failed to get events for day",
    });
  }
}
