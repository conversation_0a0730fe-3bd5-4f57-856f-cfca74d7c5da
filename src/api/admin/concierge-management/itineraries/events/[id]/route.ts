import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";
import { z } from "zod";
import ItineraryService from "../../../../../../modules/concierge-management/itinerary-service";

// Validation schemas
export const PatchUpdateItineraryEvent = z.object({
  category: z.enum(["Flight", "Lodging", "Activity", "Cruise", "Transport", "Info"]).optional(),
  type: z.string().optional(),
  title: z.string().optional(),
  notes: z.string().optional(),
  start_time: z.string().optional(),
  end_time: z.string().optional(),
  duration: z.string().optional(),
  timezone: z.string().optional(),
  details: z.record(z.any()).optional(),
  price: z.number().optional(),
  currency: z.string().optional(),
  media: z.array(z.string()).optional(),
  attachments: z.array(z.string()).optional(),
  people: z.array(z.string()).optional(),
});

export type PatchUpdateItineraryEventType = z.infer<typeof PatchUpdateItineraryEvent>;

/**
 * PATCH /admin/concierge-management/itineraries/events/:id
 * Update an itinerary event
 */
export async function PATCH(req: MedusaRequest, res: MedusaResponse) {
  try {
    const { id: eventId } = req.params;
    const validatedData = PatchUpdateItineraryEvent.parse(req.body);
    
    const itineraryService: ItineraryService = req.scope.resolve("itineraryService");
    
    const event = await itineraryService.updateEvent(eventId, validatedData);
    
    res.json({
      event,
      message: "Event updated successfully",
    });
  } catch (error) {
    console.error("Error updating event:", error);
    res.status(400).json({
      error: error instanceof Error ? error.message : "Failed to update event",
    });
  }
}

/**
 * DELETE /admin/concierge-management/itineraries/events/:id
 * Delete an itinerary event
 */
export async function DELETE(req: MedusaRequest, res: MedusaResponse) {
  try {
    const { id: eventId } = req.params;
    
    const itineraryService: ItineraryService = req.scope.resolve("itineraryService");
    
    await itineraryService.deleteEvent(eventId);
    
    res.json({
      message: "Event deleted successfully",
    });
  } catch (error) {
    console.error("Error deleting event:", error);
    res.status(400).json({
      error: error instanceof Error ? error.message : "Failed to delete event",
    });
  }
}
