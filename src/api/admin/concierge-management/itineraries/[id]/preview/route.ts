import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";
import ItineraryService from "../../../../../../modules/concierge-management/itinerary-service";
import { asClass } from "awilix";
import { ITINERARY_SERVICE } from "../../../../../../modules/concierge-management";

/**
 * Ensure itinerary service is registered in the container
 */
function ensureItineraryServiceRegistered(scope: any): ItineraryService {
  if (!scope.hasRegistration("itineraryService")) {
    console.log("🔧 [ITINERARY-PREVIEW] Registering itinerary service...");
    scope.register({
      [ITINERARY_SERVICE]: asClass(ItineraryService).singleton(),
    });
    console.log("✅ [ITINERARY-PREVIEW] Itinerary service registered successfully");
  }
  return scope.resolve("itineraryService");
}

/**
 * POST /admin/concierge-management/itineraries/[id]/preview
 * Generate preview URL for itinerary by calling CRM API
 * This endpoint will be called when user clicks the "Preview" button
 */
export async function POST(req: MedusaRequest, res: MedusaResponse) {
  try {
    console.log("🚀 [ITINERARY-PREVIEW] Preview endpoint called");
    console.log("🔍 [ITINERARY-PREVIEW] Params:", req.params);

    const { id } = req.params;

    if (!id) {
      console.error("❌ [ITINERARY-PREVIEW] Missing itinerary ID");
      return res.status(400).json({
        success: false,
        error: "Itinerary ID is required",
        code: "MISSING_ITINERARY_ID"
      });
    }

    console.log("🔧 [ITINERARY-PREVIEW] Ensuring itinerary service is registered...");
    const itineraryService: ItineraryService = ensureItineraryServiceRegistered(req.scope);
    console.log("✅ [ITINERARY-PREVIEW] Itinerary service resolved successfully");

    // First, verify the itinerary exists
    console.log(`🔍 [ITINERARY-PREVIEW] Verifying itinerary exists for ID: ${id}`);
    const itinerary = await itineraryService.retrieveItinerary(id);

    if (!itinerary) {
      console.error(`❌ [ITINERARY-PREVIEW] Itinerary not found for ID: ${id}`);
      return res.status(404).json({
        success: false,
        error: "Itinerary not found",
        code: "ITINERARY_NOT_FOUND",
        itinerary_id: id
      });
    }

    console.log("✅ [ITINERARY-PREVIEW] Itinerary found, calling CRM API...");

    // TODO: Replace with actual CRM API endpoint when provided
    const CRM_API_ENDPOINT = process.env.CRM_PREVIEW_API_ENDPOINT || "https://your-crm-api.com/api/itinerary/preview";
    
    try {
      // Call CRM API with itinerary ID
      console.log(`🌐 [ITINERARY-PREVIEW] Calling CRM API: ${CRM_API_ENDPOINT}`);
      
      const crmResponse = await fetch(CRM_API_ENDPOINT, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          // Add any required authentication headers here
          ...(process.env.CRM_API_KEY && { 'Authorization': `Bearer ${process.env.CRM_API_KEY}` })
        },
        body: JSON.stringify({
          itinerary_id: id,
          booking_id: itinerary.booking_id,
          // Include any additional data the CRM might need
          metadata: {
            source: "medusa-store",
            timestamp: new Date().toISOString()
          }
        })
      });

      if (!crmResponse.ok) {
        console.error(`❌ [ITINERARY-PREVIEW] CRM API error: ${crmResponse.status} ${crmResponse.statusText}`);
        const errorText = await crmResponse.text();
        console.error(`❌ [ITINERARY-PREVIEW] CRM API error details:`, errorText);
        
        return res.status(502).json({
          success: false,
          error: "Failed to generate preview from CRM",
          code: "CRM_API_ERROR",
          details: `CRM API returned ${crmResponse.status}: ${crmResponse.statusText}`
        });
      }

      const crmData = await crmResponse.json();
      console.log("✅ [ITINERARY-PREVIEW] CRM API response received:", crmData);

      // Extract preview URL from CRM response
      const previewUrl = crmData.preview_url || crmData.url || crmData.redirect_url;

      if (!previewUrl) {
        console.error("❌ [ITINERARY-PREVIEW] No preview URL in CRM response");
        return res.status(502).json({
          success: false,
          error: "CRM did not provide preview URL",
          code: "MISSING_PREVIEW_URL"
        });
      }

      console.log(`✅ [ITINERARY-PREVIEW] Preview URL generated: ${previewUrl}`);

      return res.status(200).json({
        success: true,
        data: {
          preview_url: previewUrl,
          itinerary_id: id,
          booking_id: itinerary.booking_id
        },
        meta: {
          generated_at: new Date().toISOString(),
          crm_response: crmData
        }
      });

    } catch (fetchError) {
      console.error("❌ [ITINERARY-PREVIEW] Network error calling CRM API:", fetchError);
      
      // For development/testing, return a mock preview URL
      if (process.env.NODE_ENV === 'development') {
        console.log("🧪 [ITINERARY-PREVIEW] Development mode - returning mock preview URL");
        return res.status(200).json({
          success: true,
          data: {
            preview_url: `https://your-crm-preview.com/itinerary/${id}?mock=true`,
            itinerary_id: id,
            booking_id: itinerary.booking_id
          },
          meta: {
            generated_at: new Date().toISOString(),
            mode: "development_mock"
          }
        });
      }

      return res.status(503).json({
        success: false,
        error: "Unable to connect to CRM service",
        code: "CRM_CONNECTION_ERROR",
        details: fetchError instanceof Error ? fetchError.message : "Network error"
      });
    }

  } catch (error) {
    console.error("❌ [ITINERARY-PREVIEW] Unexpected error:", error);
    
    return res.status(500).json({
      success: false,
      error: "Failed to generate itinerary preview",
      code: "INTERNAL_SERVER_ERROR",
      details: error instanceof Error ? error.message : "Unknown error",
      timestamp: new Date().toISOString()
    });
  }
}
