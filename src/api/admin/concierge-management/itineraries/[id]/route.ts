import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";
import { Modules } from "@camped-ai/framework/utils";
import { z } from "zod";
import ItineraryService from "../../../../../modules/concierge-management/itinerary-service";
import { asClass } from "awilix";
import { ITINERARY_SERVICE } from "../../../../../modules/concierge-management";

// Validation schemas
export const PutAdminUpdateItinerary = z.object({
  title: z.string().optional(),
  status: z.enum(["DRAFT", "FINALIZED"]).optional(),
});

export type PutAdminUpdateItineraryType = z.infer<typeof PutAdminUpdateItinerary>;

/**
 * Ensure itinerary service is registered
 */
function ensureItineraryServiceRegistered(container: any): ItineraryService {
  // Register with constant key
  if (!container.hasRegistration(ITINERARY_SERVICE)) {
    container.register({
      [ITINERARY_SERVICE]: asClass(ItineraryService).singleton(),
    });
  }

  // Register with string key for backward compatibility
  if (!container.hasRegistration("itineraryService")) {
    container.register({
      itineraryService: asClass(ItineraryService).singleton(),
    });
  }

  return container.resolve("itineraryService");
}

/**
 * GET /admin/concierge-management/itineraries/[id]
 * Retrieve a specific itinerary with days and events
 */
export async function GET(req: MedusaRequest, res: MedusaResponse) {
  try {
    const { id } = req.params;

    if (!id) {
      return res.status(400).json({
        error: "Itinerary ID is required",
      });
    }

    const itineraryService: ItineraryService = ensureItineraryServiceRegistered(req.scope);

    const itinerary = await itineraryService.getItineraryWithDaysAndEvents(id);

    if (!itinerary) {
      return res.status(404).json({
        error: "Itinerary not found",
      });
    }

    // Ensure days is always an array
    const normalizedItinerary = {
      ...itinerary,
      days: Array.isArray(itinerary.days) ? itinerary.days : [],
    };

    res.json({ itinerary: normalizedItinerary });
  } catch (error) {
    console.error("Error retrieving itinerary:", error);
    res.status(404).json({
      error: error instanceof Error ? error.message : "Itinerary not found",
    });
  }
}

/**
 * PUT /admin/concierge-management/itineraries/[id]
 * Update an itinerary
 */
export async function PUT(req: MedusaRequest, res: MedusaResponse) {
  try {
    const { id } = req.params;
    const validatedData = PutAdminUpdateItinerary.parse(req.body);
    
    const itineraryService: ItineraryService = ensureItineraryServiceRegistered(req.scope);
    
    const itinerary = await itineraryService.updateItineraries({ id, ...validatedData });
    
    res.json({
      itinerary,
      message: "Itinerary updated successfully",
    });
  } catch (error) {
    console.error("Error updating itinerary:", error);
    res.status(400).json({
      error: error instanceof Error ? error.message : "Failed to update itinerary",
    });
  }
}

/**
 * DELETE /admin/concierge-management/itineraries/[id]
 * Delete an itinerary and all associated data
 */
export async function DELETE(req: MedusaRequest, res: MedusaResponse) {
  try {
    const { id } = req.params;

    if (!id) {
      return res.status(400).json({
        error: "Itinerary ID is required",
      });
    }

    const itineraryService: ItineraryService = ensureItineraryServiceRegistered(req.scope);
    const orderService = req.scope.resolve(Modules.ORDER);

    // First, get the itinerary to check if it exists and get the booking_id
    const itinerary = await itineraryService.retrieveItinerary(id);

    if (!itinerary) {
      return res.status(404).json({
        error: "Itinerary not found",
      });
    }

    // Additional validation: Check if itinerary is finalized
    if (itinerary.status === "FINALIZED") {
      console.warn(`Attempting to delete finalized itinerary: ${id}`);
      // Allow deletion but log it for audit purposes
    }

    // Get associated data count for logging
    const days = await itineraryService.listItineraryDays({ itinerary_id: id });
    const totalEvents = await Promise.all(
      days.map(day => itineraryService.listItineraryEvents({ day_id: day.id }))
    );
    const eventCount = totalEvents.reduce((sum, events) => sum + events.length, 0);

    console.log(`Deleting itinerary ${id}: ${days.length} days, ${eventCount} events`);

    // Delete the itinerary (this will cascade delete all associated data)
    await itineraryService.deleteItinerary(id);

    // Update the booking metadata to remove the itinerary_id reference
    if (itinerary.booking_id) {
      try {
        const booking = await orderService.retrieveOrder(itinerary.booking_id, {
          relations: ["metadata"],
        });

        if (booking && booking.metadata?.itinerary_id === id) {
          const updatedMetadata = { ...booking.metadata };
          delete updatedMetadata.itinerary_id;

          await orderService.updateOrders(itinerary.booking_id, {
            metadata: updatedMetadata,
          });
        }
      } catch (bookingError) {
        console.warn("Warning: Could not update booking metadata:", bookingError);
        // Don't fail the delete operation if booking update fails
      }
    }

    res.json({
      id,
      message: "Itinerary deleted successfully",
      deleted: true,
      details: {
        booking_id: itinerary.booking_id,
        days_deleted: days.length,
        events_deleted: eventCount,
        booking_metadata_updated: !!itinerary.booking_id,
      },
    });
  } catch (error) {
    console.error("Error deleting itinerary:", error);

    // Provide more specific error messages
    let errorMessage = "Failed to delete itinerary";
    let statusCode = 400;

    if (error instanceof Error) {
      if (error.message.includes("not found")) {
        statusCode = 404;
        errorMessage = "Itinerary not found";
      } else if (error.message.includes("permission")) {
        statusCode = 403;
        errorMessage = "Insufficient permissions to delete itinerary";
      } else {
        errorMessage = error.message;
      }
    }

    res.status(statusCode).json({
      error: errorMessage,
      details: {
        itinerary_id: req.params.id,
        timestamp: new Date().toISOString(),
      },
    });
  }
}
