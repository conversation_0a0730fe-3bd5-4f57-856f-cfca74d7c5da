import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";
import { z } from "zod";
import ItineraryService from "../../../../../../modules/concierge-management/itinerary-service";

// Validation schemas
export const PostAddItineraryDay = z.object({
  date: z.string().transform((str) => new Date(str)),
  title: z.string().optional(),
});

export const PatchReorderItineraryDays = z.object({
  day_updates: z.array(z.object({
    id: z.string(),
    sort_order: z.number(),
  })),
});

export type PostAddItineraryDayType = z.infer<typeof PostAddItineraryDay>;
export type PatchReorderItineraryDaysType = z.infer<typeof PatchReorderItineraryDays>;

/**
 * POST /admin/concierge-management/itineraries/:id/days
 * Add a new day to an itinerary
 */
export async function POST(req: MedusaRequest, res: MedusaResponse) {
  try {
    const { id: itineraryId } = req.params;
    const validatedData = PostAddItineraryDay.parse(req.body);
    
    const itineraryService: ItineraryService = req.scope.resolve("itineraryService");
    
    const day = await itineraryService.addDayToItinerary(
      itineraryId,
      validatedData.date,
      validatedData.title
    );
    
    res.status(201).json({
      day,
      message: "Day added successfully",
    });
  } catch (error) {
    console.error("Error adding day to itinerary:", error);
    res.status(400).json({
      error: error instanceof Error ? error.message : "Failed to add day to itinerary",
    });
  }
}

/**
 * PATCH /admin/concierge-management/itineraries/:id/days/reorder
 * Reorder days in an itinerary
 */
export async function PATCH(req: MedusaRequest, res: MedusaResponse) {
  try {
    const validatedData = PatchReorderItineraryDays.parse(req.body);
    
    const itineraryService: ItineraryService = req.scope.resolve("itineraryService");
    
    const updatedDays = await itineraryService.reorderDays(validatedData.day_updates);
    
    res.json({
      days: updatedDays,
      message: "Days reordered successfully",
    });
  } catch (error) {
    console.error("Error reordering days:", error);
    res.status(400).json({
      error: error instanceof Error ? error.message : "Failed to reorder days",
    });
  }
}
