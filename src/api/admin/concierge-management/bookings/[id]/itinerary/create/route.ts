import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";
import { Modules } from "@camped-ai/framework/utils";
import { z } from "zod";
import { ITINERARY_SERVICE, CONCIERGE_MANAGEMENT_MODULE } from "../../../../../../../modules/concierge-management";

// Validation schema
export const PostCreateItineraryFromBooking = z.object({
  created_by: z.string().optional(),
});

export type PostCreateItineraryFromBookingType = z.infer<typeof PostCreateItineraryFromBooking>;



/**
 * POST /admin/concierge-management/bookings/:id/itinerary/create
 * Create an itinerary from a booking
 */
export async function POST(req: MedusaRequest, res: MedusaResponse) {
  try {
    const { id: conciergeOrderId } = req.params;
    const validatedData = PostCreateItineraryFromBooking.parse(req.body);

    // Resolve services using the proper framework pattern
    const itineraryService = req.scope.resolve(ITINERARY_SERVICE);
    const orderService = req.scope.resolve(Modules.ORDER);

    // First, get the concierge order to find the actual order_id
    const conciergeService = req.scope.resolve(CONCIERGE_MANAGEMENT_MODULE);
    const conciergeOrder = await conciergeService.retrieveConciergeOrder(conciergeOrderId);

    if (!conciergeOrder) {
      return res.status(404).json({
        error: "Concierge order not found",
      });
    }

    // Now get the actual booking using the order_id from concierge order
    const booking = await orderService.retrieveOrder(conciergeOrder.order_id, {
      relations: ["metadata"],
    });

    if (!booking) {
      return res.status(404).json({
        error: `Order with id: ${conciergeOrder.order_id} was not found`,
      });
    }

    // Extract check-in date from booking metadata
    let bookingStartDate = new Date(); // Default to today
    if (booking.metadata?.check_in_date) {
      bookingStartDate = new Date(booking.metadata.check_in_date as string);
    }

    const result = await itineraryService.createItineraryFromBooking(
      conciergeOrder.order_id,
      bookingStartDate,
      validatedData.created_by
    );

    // Update booking metadata to include itinerary_id
    await orderService.updateOrders(conciergeOrder.order_id, {
      metadata: {
        ...booking.metadata,
        itinerary_id: result.itinerary.id,
      },
    });

    res.status(201).json({
      itinerary: result.itinerary,
      first_day: result.firstDay,
      message: "Itinerary created successfully",
      redirect_url: `/concierge-management/itineraries/${result.itinerary.id}/builder`,
    });
  } catch (error) {
    console.error("❌ [CONCIERGE-ITINERARY-CREATE] Error creating itinerary from booking:", error);
    res.status(400).json({
      error: error instanceof Error ? error.message : "Failed to create itinerary from booking",
    });
  }
}
