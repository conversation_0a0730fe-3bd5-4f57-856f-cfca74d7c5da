import { MedusaRequest, MedusaResponse, AuthenticatedMedusaRequest } from "@camped-ai/framework/http";
import { z } from "zod";
import { CONCIERGE_MANAGEMENT_MODULE } from "../../../../../../modules/concierge-management";
import { TaskStatus, TaskPriority } from "../../../../../../modules/concierge-management/types";

// Validation schemas
export const GetAdminBookingTasksQuery = z.object({
  limit: z.coerce.number().min(1).max(100).optional().default(50),
  offset: z.coerce.number().min(0).optional().default(0),
  status: z.nativeEnum(TaskStatus).optional(),
  priority: z.nativeEnum(TaskPriority).optional(),
  assigned_to: z.string().optional(),
  order: z.string().optional().default("created_at"),
  sort_order: z.enum(["asc", "desc"]).optional().default("desc"),
});

export const PostAdminCreateBookingTask = z.object({
  title: z.string().min(1).max(255),
  description: z.string().optional(),
  status: z.nativeEnum(TaskStatus).optional().default(TaskStatus.PENDING),
  priority: z.nativeEnum(TaskPriority).optional().default(TaskPriority.MEDIUM),
  assigned_to: z.string().optional(),
  due_date: z.string().datetime().optional(),
  metadata: z.record(z.any()).optional(),
});

export type GetAdminBookingTasksQueryType = z.infer<typeof GetAdminBookingTasksQuery>;
export type PostAdminCreateBookingTaskType = z.infer<typeof PostAdminCreateBookingTask>;

// GET endpoint to get tasks for a specific booking
export const GET = async (
  req: AuthenticatedMedusaRequest,
  res: MedusaResponse
) => {
  try {
    const conciergeService = req.scope.resolve(CONCIERGE_MANAGEMENT_MODULE);
    
    const { id: bookingId } = req.params;
    const query = GetAdminBookingTasksQuery.parse(req.query);
    
    // Build filters for this booking
    const filters: any = {
      entity_type: "booking",
      entity_id: bookingId,
    };
    
    if (query.status) filters.status = query.status;
    if (query.priority) filters.priority = query.priority;
    if (query.assigned_to) filters.assigned_to = query.assigned_to;
    
    // Build order
    const order: Record<string, "ASC" | "DESC"> = {};
    order[query.order] = query.sort_order.toUpperCase() as "ASC" | "DESC";
    
    const result = await conciergeService.listTasks({
      filters,
      options: {
        limit: query.limit,
        offset: query.offset,
        order,
      },
    });
    
    return res.json(result);
  } catch (error) {
    console.error("Error retrieving tasks for booking:", error);
    return res.status(500).json({ 
      message: "Error retrieving tasks for booking", 
      error: error.message 
    });
  }
};

// POST endpoint to create a task for a specific booking
export const POST = async (
  req: MedusaRequest<PostAdminCreateBookingTaskType>,
  res: MedusaResponse
) => {
  try {
    const conciergeService = req.scope.resolve(CONCIERGE_MANAGEMENT_MODULE);
    
    const { id: bookingId } = req.params;
    const validatedData = PostAdminCreateBookingTask.parse(req.body);
    
    // Add booking entity information and created_by from authenticated user
    const taskData = {
      ...validatedData,
      entity_type: "booking",
      entity_id: bookingId,
      ...(validatedData.due_date && { due_date: new Date(validatedData.due_date) }),
      created_by: req.user?.id,
    };
    
    const task = await conciergeService.createTask({ task: taskData });
    
    return res.status(201).json({ task });
  } catch (error) {
    console.error("Error creating task for booking:", error);
    
    if (error instanceof z.ZodError) {
      return res.status(400).json({ 
        message: "Validation error", 
        errors: error.errors 
      });
    }
    
    return res.status(500).json({ 
      message: "Error creating task for booking", 
      error: error.message 
    });
  }
};
