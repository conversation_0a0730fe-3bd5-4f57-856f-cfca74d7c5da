import { MedusaRequest, MedusaResponse, AuthenticatedMedusaRequest } from "@camped-ai/framework/http";
import { z } from "zod";
import { CONCIERGE_MANAGEMENT_MODULE } from "../../../../../modules/concierge-management";

// Validation schemas
export const PutAdminUpdateNote = z.object({
  title: z.string().min(1).max(255).optional(),
  content: z.string().optional(),
});

export type PutAdminUpdateNoteType = z.infer<typeof PutAdminUpdateNote>;

// GET endpoint to retrieve a single note
export const GET = async (
  req: AuthenticatedMedusaRequest,
  res: MedusaResponse
) => {
  try {
    const conciergeService = req.scope.resolve(CONCIERGE_MANAGEMENT_MODULE);
    
    const { id } = req.params;
    
    const note = await conciergeService.getNote(id);
    
    return res.json({ note });
  } catch (error) {
    console.error("Error retrieving note:", error);
    
    if (error.message.includes("not found")) {
      return res.status(404).json({ 
        message: "Note not found" 
      });
    }
    
    return res.status(500).json({ 
      message: "Error retrieving note", 
      error: error.message 
    });
  }
};

// PUT endpoint to update a note
export const PUT = async (
  req: AuthenticatedMedusaRequest,
  res: MedusaResponse
) => {
  try {
    const conciergeService = req.scope.resolve(CONCIERGE_MANAGEMENT_MODULE);
    
    const { id } = req.params;
    const data = PutAdminUpdateNote.parse(req.body);
    
    const note = await conciergeService.updateNote(id, {
      note: {
        ...data,
        updated_by_id: req.user?.id,
      },
    });
    
    return res.json({ 
      message: "Note updated successfully",
      note 
    });
  } catch (error) {
    console.error("Error updating note:", error);
    
    if (error.name === "ZodError") {
      return res.status(400).json({ 
        message: "Invalid input data", 
        errors: error.errors 
      });
    }
    
    if (error.message.includes("not found")) {
      return res.status(404).json({ 
        message: "Note not found" 
      });
    }
    
    return res.status(500).json({ 
      message: "Error updating note", 
      error: error.message 
    });
  }
};

// DELETE endpoint to soft delete a note
export const DELETE = async (
  req: AuthenticatedMedusaRequest,
  res: MedusaResponse
) => {
  try {
    const conciergeService = req.scope.resolve(CONCIERGE_MANAGEMENT_MODULE);
    
    const { id } = req.params;
    
    await conciergeService.softDeleteNote(id, req.user?.id);
    
    return res.json({ 
      message: "Note deleted successfully" 
    });
  } catch (error) {
    console.error("Error deleting note:", error);
    
    if (error.message.includes("not found")) {
      return res.status(404).json({ 
        message: "Note not found" 
      });
    }
    
    return res.status(500).json({ 
      message: "Error deleting note", 
      error: error.message 
    });
  }
};
