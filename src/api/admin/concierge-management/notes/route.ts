import { MedusaRequest, MedusaResponse, AuthenticatedMedusaRequest } from "@camped-ai/framework/http";
import { z } from "zod";
import { CONCIERGE_MANAGEMENT_MODULE } from "../../../../modules/concierge-management";

// Validation schemas
export const GetAdminNotesQuery = z.object({
  limit: z.coerce.number().min(1).max(100).optional().default(50),
  offset: z.coerce.number().min(0).optional().default(0),
  entity: z.string().optional(),
  entity_id: z.string().optional(),
  created_by_id: z.string().optional(),
  q: z.string().optional(), // Search query
  order: z.string().optional().default("created_at"),
  sort_order: z.enum(["asc", "desc"]).optional().default("desc"),
  created_at_gte: z.string().datetime().optional(),
  created_at_lte: z.string().datetime().optional(),
});

export const PostAdminCreateNote = z.object({
  title: z.string().min(1).max(255),
  content: z.string().optional(),
  entity: z.string().min(1),
  entity_id: z.string().min(1),
});

export type GetAdminNotesQueryType = z.infer<typeof GetAdminNotesQuery>;
export type PostAdminCreateNoteType = z.infer<typeof PostAdminCreateNote>;

// GET endpoint to list notes
export const GET = async (
  req: AuthenticatedMedusaRequest,
  res: MedusaResponse
) => {
  try {
    const conciergeService = req.scope.resolve(CONCIERGE_MANAGEMENT_MODULE);
    
    const query = GetAdminNotesQuery.parse(req.query);
    
    // Build filters
    const filters: any = {};
    
    if (query.q) {
      // Search in title and content
      filters.$or = [
        { title: { $ilike: `%${query.q}%` } },
        { content: { $ilike: `%${query.q}%` } },
      ];
    }
    
    if (query.entity) {
      filters.entity = query.entity;
    }
    
    if (query.entity_id) {
      filters.entity_id = query.entity_id;
    }
    
    if (query.created_by_id) {
      filters.created_by_id = query.created_by_id;
    }
    
    if (query.created_at_gte || query.created_at_lte) {
      filters.created_at = {};
      if (query.created_at_gte) filters.created_at.gte = new Date(query.created_at_gte);
      if (query.created_at_lte) filters.created_at.lte = new Date(query.created_at_lte);
    }
    
    // Build order
    const order: Record<string, "ASC" | "DESC"> = {};
    order[query.order] = query.sort_order.toUpperCase() as "ASC" | "DESC";
    
    const result = await conciergeService.getNotes({
      filters,
      options: {
        limit: query.limit,
        offset: query.offset,
        order,
      },
    });
    
    return res.json(result);
  } catch (error) {
    console.error("Error listing notes:", error);
    return res.status(500).json({ 
      message: "Error listing notes", 
      error: error.message 
    });
  }
};

// POST endpoint to create a new note
export const POST = async (
  req: AuthenticatedMedusaRequest,
  res: MedusaResponse
) => {
  try {
    const conciergeService = req.scope.resolve(CONCIERGE_MANAGEMENT_MODULE);
    
    const data = PostAdminCreateNote.parse(req.body);
    
    const note = await conciergeService.createNote({
      note: {
        ...data,
        created_by_id: req.user?.id,
      },
    });
    
    return res.status(201).json({ 
      message: "Note created successfully",
      note 
    });
  } catch (error) {
    console.error("Error creating note:", error);
    
    if (error.name === "ZodError") {
      return res.status(400).json({ 
        message: "Invalid input data", 
        errors: error.errors 
      });
    }
    
    return res.status(500).json({ 
      message: "Error creating note", 
      error: error.message 
    });
  }
};
