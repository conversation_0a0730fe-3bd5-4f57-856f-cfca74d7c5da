import { MedusaRequest, MedusaResponse, AuthenticatedMedusaRequest } from "@camped-ai/framework/http";
import { z } from "zod";
import { CONCIERGE_MANAGEMENT_MODULE } from "../../../../../../../modules/concierge-management";
import { ConciergeOrderItemStatus } from "../../../../../../../modules/concierge-management/types";

// Validation schemas
export const PatchAdminUpdateConciergeOrderItem = z.object({
  quantity: z.number().min(1).optional(),
  unit_price: z.number().min(0).optional(),
  title: z.string().min(1).optional(),
  status: z.nativeEnum(ConciergeOrderItemStatus).optional(),
  notes: z.string().optional(),
  is_active: z.boolean().optional(),
  finalized_at: z.string().datetime().optional(),
  metadata: z.record(z.any()).optional(),
});

export type PatchAdminUpdateConciergeOrderItemType = z.infer<typeof PatchAdminUpdateConciergeOrderItem>;

// GET endpoint to retrieve a specific concierge order item
export const GET = async (
  req: MedusaRequest,
  res: MedusaResponse
) => {
  try {
    const { id: conciergeOrderId, item_id } = req.params;
    const conciergeManagementService = req.scope.resolve(CONCIERGE_MANAGEMENT_MODULE);

    if (!conciergeOrderId) {
      return res.status(400).json({ message: "Concierge order ID is required" });
    }

    if (!item_id) {
      return res.status(400).json({ message: "Concierge order item ID is required" });
    }

    const conciergeOrderItem = await conciergeManagementService.retrieveConciergeOrderItem(item_id);

    // Verify the item belongs to the specified concierge order
    if (conciergeOrderItem.concierge_order_id !== conciergeOrderId) {
      return res.status(404).json({ 
        message: "Concierge order item not found in the specified order" 
      });
    }

    return res.json({ 
      concierge_order_item: conciergeOrderItem 
    });
  } catch (error) {
    console.error("Error retrieving concierge order item:", error);
    
    if (error.type === "not_found") {
      return res.status(404).json({ 
        message: "Concierge order item not found" 
      });
    }
    
    return res.status(500).json({ 
      message: "Internal server error",
      error: error.message 
    });
  }
};

// PATCH endpoint to update a concierge order item
export const PATCH = async (
  req: MedusaRequest<PatchAdminUpdateConciergeOrderItemType>,
  res: MedusaResponse
) => {
  try {
    const { id: conciergeOrderId, item_id } = req.params;
    const conciergeManagementService = req.scope.resolve(CONCIERGE_MANAGEMENT_MODULE);
    
    if (!conciergeOrderId) {
      return res.status(400).json({ message: "Concierge order ID is required" });
    }

    if (!item_id) {
      return res.status(400).json({ message: "Concierge order item ID is required" });
    }

    const validatedData = PatchAdminUpdateConciergeOrderItem.parse(req.body);
    
    // First verify the item exists and belongs to the specified order
    const existingItem = await conciergeManagementService.retrieveConciergeOrderItem(item_id);
    if (existingItem.concierge_order_id !== conciergeOrderId) {
      return res.status(404).json({
        message: "Concierge order item not found in the specified order"
      });
    }

    // If status is being updated, use the specialized method
    if (validatedData.status) {
      // First, get the current item to check its status
      const currentItem = await conciergeManagementService.retrieveConciergeOrderItem(item_id);

      // Prevent status changes from "order_placed" or "completed"
      if (currentItem.status === "order_placed" || currentItem.status === "completed") {
        return res.status(400).json({
          message: `Cannot change status from "${currentItem.status}". This status is locked and cannot be modified.`,
          type: "status_locked",
        });
      }

      const updatedItem = await conciergeManagementService.updateConciergeOrderItemStatus(
        item_id,
        validatedData.status,
        req.user?.id
      );

      // Update other fields if provided
      const { status, ...otherFields } = validatedData;
      if (Object.keys(otherFields).length > 0) {
        const finalItem = await conciergeManagementService.updateConciergeOrderItem(item_id, otherFields);
        return res.json({
          message: "Concierge order item updated successfully",
          concierge_order_item: finalItem,
        });
      }

      return res.json({
        message: "Concierge order item status updated successfully",
        concierge_order_item: updatedItem,
      });
    } else {
      // Regular update without status change
      const updatedItem = await conciergeManagementService.updateConciergeOrderItem(item_id, validatedData);

      return res.json({
        message: "Concierge order item updated successfully",
        concierge_order_item: updatedItem,
      });
    }
  } catch (error) {
    console.error("Error updating concierge order item:", error);
    
    if (error instanceof z.ZodError) {
      return res.status(400).json({ 
        message: "Validation error", 
        errors: error.errors 
      });
    }
    
    if (error.type === "not_found") {
      return res.status(404).json({ 
        message: "Concierge order item not found" 
      });
    }
    
    return res.status(500).json({ 
      message: "Internal server error",
      error: error.message 
    });
  }
};

// DELETE endpoint to delete (deactivate) a concierge order item
export const DELETE = async (
  req: MedusaRequest,
  res: MedusaResponse
) => {
  try {
    const { id: conciergeOrderId, item_id } = req.params;
    const conciergeManagementService = req.scope.resolve(CONCIERGE_MANAGEMENT_MODULE);
    
    if (!conciergeOrderId) {
      return res.status(400).json({ message: "Concierge order ID is required" });
    }

    if (!item_id) {
      return res.status(400).json({ message: "Concierge order item ID is required" });
    }

    // First verify the item exists and belongs to the specified order
    const existingItem = await conciergeManagementService.retrieveConciergeOrderItem(item_id);
    if (existingItem.concierge_order_id !== conciergeOrderId) {
      return res.status(404).json({
        message: "Concierge order item not found in the specified order"
      });
    }

    // Deactivate the item instead of hard delete
    const deactivatedItem = await conciergeManagementService.deactivateConciergeOrderItem(
      item_id,
      req.user?.id
    );

    return res.json({
      message: "Concierge order item deactivated successfully",
      concierge_order_item: deactivatedItem,
    });
  } catch (error) {
    console.error("Error deactivating concierge order item:", error);
    
    if (error.type === "not_found") {
      return res.status(404).json({ 
        message: "Concierge order item not found" 
      });
    }
    
    return res.status(500).json({ 
      message: "Internal server error",
      error: error.message 
    });
  }
};
