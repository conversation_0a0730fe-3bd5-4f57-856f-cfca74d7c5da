import { MedusaRequest, MedusaResponse, AuthenticatedMedusaRequest } from "@camped-ai/framework/http";
import { z } from "zod";
import { CONCIERGE_MANAGEMENT_MODULE } from "../../../../../../modules/concierge-management";
import { ConciergeOrderItemStatus } from "../../../../../../modules/concierge-management/types";
import { CreateConciergeOrderItemWorkflow } from "../../../../../../workflows/concierge-management";

// Validation schemas
export const GetAdminConciergeOrderItemsQuery = z.object({
  limit: z.coerce.number().min(1).max(100).optional().default(20),
  offset: z.coerce.number().min(0).optional().default(0),
  status: z.nativeEnum(ConciergeOrderItemStatus).optional(),
  is_active: z.coerce.boolean().optional(),
  line_item_id: z.string().optional(),
  item_id: z.string().optional(),
  variant_id: z.string().optional(),
  order: z.string().optional().default("created_at"),
  sort_order: z.enum(["asc", "desc"]).optional().default("desc"),
});

export const PostAdminCreateConciergeOrderItem = z.object({
  line_item_id: z.string().optional(),
  item_id: z.string().optional(),
  variant_id: z.string().optional(),
  quantity: z.number().min(1),
  unit_price: z.number().min(0),
  title: z.string().min(1),
  status: z.nativeEnum(ConciergeOrderItemStatus).optional().default(ConciergeOrderItemStatus.UNDER_REVIEW),
  metadata: z.record(z.any()).optional(),
  // New fields - allow null or string
  category_id: z.string().nullable().optional(),
  start_date: z.string().nullable().optional(),
  end_date: z.string().nullable().optional(),
}).refine(
  (data) => {
    if (data.start_date && data.end_date) {
      // Only validate dates if both are provided and not null
      try {
        return new Date(data.start_date) <= new Date(data.end_date);
      } catch {
        return false;
      }
    }
    return true;
  },
  {
    message: "Start date must be before or equal to end date",
    path: ["end_date"],
  }
);

export type GetAdminConciergeOrderItemsQueryType = z.infer<typeof GetAdminConciergeOrderItemsQuery>;
export type PostAdminCreateConciergeOrderItemType = z.infer<typeof PostAdminCreateConciergeOrderItem>;

// GET endpoint to list concierge order items for a specific order
export const GET = async (
  req: MedusaRequest<GetAdminConciergeOrderItemsQueryType>,
  res: MedusaResponse
) => {
  try {
    const { id: conciergeOrderId } = req.params;
    const conciergeManagementService = req.scope.resolve(CONCIERGE_MANAGEMENT_MODULE);

    if (!conciergeOrderId) {
      return res.status(400).json({ message: "Concierge order ID is required" });
    }

    const {
      limit,
      offset,
      status,
      is_active,
      line_item_id,
      item_id,
      variant_id,
      order: orderField,
      sort_order,
    } = GetAdminConciergeOrderItemsQuery.parse(req.query);

    // Build filters
    const filters: any = {
      concierge_order_id: conciergeOrderId,
    };
    if (status) filters.status = status;
    if (is_active !== undefined) filters.is_active = is_active;
    if (line_item_id) filters.line_item_id = line_item_id;
    if (item_id) filters.item_id = item_id;
    if (variant_id) filters.variant_id = variant_id;

    // Build options
    const options = {
      limit,
      offset,
      order: { [orderField]: sort_order.toUpperCase() },
    };

    const result = await conciergeManagementService.listConciergeOrderItemsWithPagination(filters, options);

    // Filter concierge order items to only include those with product_id: 'product_add_ons_main'
    const filteredItems = result.concierge_order_items.filter(item =>
      item.metadata && item.metadata.product_id === 'product_add_ons_main'
    );

    return res.json({
      concierge_order_items: filteredItems,
      count: filteredItems.length, // Update count to reflect filtered results
      limit: result.limit,
      offset: result.offset,
    });
  } catch (error) {
    console.error("Error listing concierge order items:", error);
    
    if (error instanceof z.ZodError) {
      return res.status(400).json({ 
        message: "Validation error", 
        errors: error.errors 
      });
    }
    
    return res.status(500).json({ 
      message: "Internal server error",
      error: error.message 
    });
  }
};

// POST endpoint to create a concierge order item
export const POST = async (
  req: MedusaRequest<PostAdminCreateConciergeOrderItemType>,
  res: MedusaResponse
) => {
  try {
    const { id: conciergeOrderId } = req.params;
    
    if (!conciergeOrderId) {
      return res.status(400).json({ message: "Concierge order ID is required" });
    }

    const validatedData = PostAdminCreateConciergeOrderItem.parse(req.body);
    
    // Execute the workflow
    const { result } = await CreateConciergeOrderItemWorkflow(req.scope).run({
      input: {
        concierge_order_id: conciergeOrderId,
        added_by: req.user?.id,
        ...validatedData,
      },
    });

    if (!result.success) {
      return res.status(400).json({
        message: "Failed to create concierge order item",
        error: result.error,
      });
    }

    return res.status(201).json({
      message: "Concierge order item created successfully",
      concierge_order_item: result.concierge_order_item,
    });
  } catch (error) {
    console.error("Error creating concierge order item:", error);
    
    if (error instanceof z.ZodError) {
      return res.status(400).json({ 
        message: "Validation error", 
        errors: error.errors 
      });
    }
    
    return res.status(500).json({ 
      message: "Internal server error",
      error: error.message 
    });
  }
};
