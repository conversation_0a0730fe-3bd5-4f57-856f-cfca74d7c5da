import { MedusaRequest, MedusaResponse, AuthenticatedMedusaRequest } from "@camped-ai/framework/http";
import { ContainerRegistrationKeys } from "@camped-ai/framework/utils";
import { z } from "zod";
import { ConciergeOrderStatus } from "../../../../modules/concierge-management/types";
import { CreateConciergeOrderWorkflow } from "../../../../workflows/concierge-management";

// Validation schemas
export const GetAdminConciergeOrdersQuery = z.object({
  limit: z.coerce.number().min(1).max(100).optional().default(20),
  offset: z.coerce.number().min(0).optional().default(0),
  q: z.string().optional(),
  order_id: z.string().optional(),
  status: z.nativeEnum(ConciergeOrderStatus).optional(),
  assigned_to: z.string().optional(),
  hotel_id: z.string().optional(),
  customer_name: z.string().optional(),
  created_at_gte: z.string().datetime().optional(),
  created_at_lte: z.string().optional(),
  check_in_date_gte: z.string().optional(),
  check_in_date_lte: z.string().optional(),
  check_out_date_gte: z.string().optional(),
  check_out_date_lte: z.string().datetime().optional(),
  order: z.string().optional().default("created_at"),
  sort_order: z.enum(["asc", "desc"]).optional().default("desc"),
});

export const PostAdminCreateConciergeOrder = z.object({
  order_id: z.string().min(1),
  hotel_id: z.string().optional(),
  check_in_date: z.string().datetime().optional(),
  check_out_date: z.string().datetime().optional(),
  assigned_to: z.string().optional(),
  notes: z.string().optional(),
  status: z.nativeEnum(ConciergeOrderStatus).optional().default(ConciergeOrderStatus.NOT_STARTED),
  metadata: z.record(z.any()).optional(),
}).transform((data) => ({
  ...data,
  order_id: data.order_id, // Ensure order_id is always present
}));

export type GetAdminConciergeOrdersQueryType = z.infer<typeof GetAdminConciergeOrdersQuery>;
export type PostAdminCreateConciergeOrderType = z.infer<typeof PostAdminCreateConciergeOrder>;

/**
 * GET endpoint to list concierge orders with combined order data
 *
 * Returns concierge orders with embedded order information including:
 * - All concierge order fields (from concierge_orders table)
 * - Related order data (from orders table) including customer info, items, addresses, etc.
 *
 * Supports filtering by:
 * - order_id: Filter by specific order ID
 * - status: Filter by concierge order status
 * - assigned_to: Filter by assigned user
 * - created_at_gte/lte: Filter by creation date range
 * - q: Search across concierge order notes, assigned user, order email, customer name, etc.
 *
 * Supports pagination with limit/offset and sorting.
 */
export const GET = async (
  req: MedusaRequest<GetAdminConciergeOrdersQueryType>,
  res: MedusaResponse
) => {
  try {
    const query = req.scope.resolve(ContainerRegistrationKeys.QUERY);

    const {
      limit,
      offset,
      q,
      order_id,
      status,
      assigned_to,
      hotel_id,
      customer_name,
      created_at_gte,
      created_at_lte,
      check_in_date_gte,
      check_in_date_lte,
      check_out_date_gte,
      check_out_date_lte,
      order: orderField,
      sort_order,
    } = GetAdminConciergeOrdersQuery.parse(req.query);

    // First, get concierge orders with basic filtering
    const conciergeOrderFilters: any = {};
    if (order_id) conciergeOrderFilters.order_id = order_id;
    if (status) conciergeOrderFilters.status = status;
    if (assigned_to) conciergeOrderFilters.assigned_to = assigned_to;
    if (hotel_id) conciergeOrderFilters.hotel_id = hotel_id;

    if (created_at_gte || created_at_lte) {
      conciergeOrderFilters.created_at = {};
      if (created_at_gte) conciergeOrderFilters.created_at.$gte = new Date(created_at_gte);
      if (created_at_lte) conciergeOrderFilters.created_at.$lte = new Date(created_at_lte);
    }

    // Add date range filters for check-in and check-out dates
    if (check_in_date_gte || check_in_date_lte) {
      conciergeOrderFilters.check_in_date = {};
      if (check_in_date_gte) conciergeOrderFilters.check_in_date.$gte = new Date(check_in_date_gte);
      if (check_in_date_lte) conciergeOrderFilters.check_in_date.$lte = new Date(check_in_date_lte);
    }

    if (check_out_date_gte || check_out_date_lte) {
      conciergeOrderFilters.check_out_date = {};
      if (check_out_date_gte) conciergeOrderFilters.check_out_date.$gte = new Date(check_out_date_gte);
      if (check_out_date_lte) conciergeOrderFilters.check_out_date.$lte = new Date(check_out_date_lte);
    }

    // Get concierge orders
    const { data: conciergeOrders } = await query.graph({
      entity: "concierge_order",
      fields: [
        "id",
        "order_id",
        "hotel_id",
        "check_in_date",
        "check_out_date",
        "assigned_to",
        "notes",
        "status",
        "last_contacted_at",
        "metadata",
        "created_at",
        "updated_at",
      ],
      filters: conciergeOrderFilters,
      pagination: {
        skip: offset,
        take: limit,
        order: { [orderField]: sort_order.toUpperCase() },
      },
    });

    // Now enhance each order with related data
    let enhancedOrders = await Promise.all(
      conciergeOrders.map(async (conciergeOrder: any) => {
        const result: any = { ...conciergeOrder };

        try {
          // Get order data with customer information
          const { data: orders } = await query.graph({
            entity: "order",
            fields: [
              "id",
              "total",
              "currency_code",
              "status",
              "created_at",
              "customer.id",
              "customer.first_name",
              "customer.last_name",
              "customer.email",
            ],
            filters: { id: conciergeOrder.order_id },
          });

          if (orders && orders.length > 0) {
            const order = orders[0];
            result.customer_id = order.customer?.id;
            result.customer_first_name = order.customer?.first_name;
            result.customer_last_name = order.customer?.last_name;
            result.customer_email = order.customer?.email;
            result.order_total = order.total;
            result.order_status = order.status;
            result.order_currency_code = order.currency_code;
          }

          // Get hotel information if hotel_id exists
          if (conciergeOrder.hotel_id) {
            try {
              const { data: hotels } = await query.graph({
                entity: "hotel",
                fields: ["id", "name"],
                filters: { id: conciergeOrder.hotel_id },
              });

              if (hotels && hotels.length > 0) {
                result.hotel_name = hotels[0].name;
              }
            } catch (hotelError) {
              console.warn(`[API] Could not fetch hotel data for ${conciergeOrder.hotel_id}:`, hotelError.message);
            }
          }

          // Get order line items for total selling price calculation (frontend will calculate)
          try {
            // Use the order relationship to get line items
            const { data: orderWithLineItems } = await query.graph({
              entity: "order",
              fields: [
                "id",
                "items.id",
                "items.unit_price",
                "items.quantity",
                "items.title",
                "items.variant_id",
                "items.product_id",
                "items.metadata"
              ],
              filters: { id: conciergeOrder.order_id },
            });

            result.order_line_items = orderWithLineItems[0]?.items || [];
            console.log(`[API] Fetched ${result.order_line_items.length} line items for order ${conciergeOrder.order_id}`);
          } catch (lineItemError) {
            console.warn(`[API] Could not fetch line items for order ${conciergeOrder.order_id}:`, lineItemError.message);
            result.order_line_items = [];
          }

          // Get payment collections using Medusa query API
          try {
            // Get order with payment collections
            const { data: orderWithPayments } = await query.graph({
              entity: "order",
              fields: [
                "id",
                "payment_collections.id",
                "payment_collections.amount",
                "payment_collections.captured_amount",
                "payment_collections.authorized_amount",
                "payment_collections.status",
                "payment_collections.currency_code",
                "payment_collections.created_at",
                "payment_collections.updated_at",
                "payment_collections.completed_at",
                "payment_collections.captured_at",
                "payment_collections.metadata"
              ],
              filters: { id: conciergeOrder.order_id },
            });

            // Extract payment collections from the order
            result.payment_collections = orderWithPayments[0]?.payment_collections || [];
            console.log(`[API] Fetched ${result.payment_collections.length} payment collections for order ${conciergeOrder.order_id}`);
          } catch (paymentError) {
            console.warn(`[API] Could not fetch payment collections for order ${conciergeOrder.order_id}:`, paymentError.message);
            result.payment_collections = [];
          }

          // Calculate remaining amount
          result.remaining = (result.total_selling_price || 0) - (result.paid || 0);

        } catch (error) {
          console.warn(`[API] Error enhancing order ${conciergeOrder.order_id}:`, error.message);
          // Set default values for failed calculations
          result.total_selling_price = 0;
          result.paid = 0;
          result.remaining = 0;
        }

        return result;
      })
    );

    // Apply client-side filtering for customer name and search query
    if (customer_name || q) {
      enhancedOrders = enhancedOrders.filter((order: any) => {
        let matches = true;

        // Filter by customer name
        if (customer_name) {
          const fullName = `${order.customer_first_name || ""} ${order.customer_last_name || ""}`.trim().toLowerCase();
          const email = (order.customer_email || "").toLowerCase();
          const searchTerm = customer_name.toLowerCase();

          matches = matches && (
            fullName.includes(searchTerm) ||
            email.includes(searchTerm)
          );
        }

        // Filter by general search query
        if (q) {
          const searchTerm = q.toLowerCase();
          const fullName = `${order.customer_first_name || ""} ${order.customer_last_name || ""}`.trim().toLowerCase();
          const email = (order.customer_email || "").toLowerCase();
          const orderId = (order.order_id || "").toLowerCase();
          const hotelName = (order.hotel_name || "").toLowerCase();

          matches = matches && (
            fullName.includes(searchTerm) ||
            email.includes(searchTerm) ||
            orderId.includes(searchTerm) ||
            hotelName.includes(searchTerm)
          );
        }

        return matches;
      });
    }

    // Get total count for pagination (before client-side filtering)
    const { data: countResult } = await query.graph({
      entity: "concierge_order",
      fields: ["id"],
      filters: conciergeOrderFilters,
    });

    return res.json({
      items: enhancedOrders,
      count: enhancedOrders.length, // Use filtered count
      total_count: countResult.length, // Original count before filtering
      limit: limit,
      offset: offset,
    });
  } catch (error) {
    console.error("Error listing concierge orders with enhanced data:", error);

    if (error instanceof z.ZodError) {
      return res.status(400).json({
        message: "Validation error",
        errors: error.errors
      });
    }

    return res.status(500).json({
      message: "Internal server error",
      error: error.message
    });
  }
};

// POST endpoint to create a concierge order
export const POST = async (
  req: MedusaRequest<PostAdminCreateConciergeOrderType>,
  res: MedusaResponse
) => {
  try {
    const validatedData = PostAdminCreateConciergeOrder.parse(req.body);

    // Execute the workflow
    const { result } = await CreateConciergeOrderWorkflow(req.scope).run({
      input: validatedData,
    });

    if (!result.success) {
      return res.status(400).json({
        message: "Failed to create concierge order",
        error: "Workflow execution failed",
      });
    }

    return res.status(201).json({
      message: "Concierge order created successfully",
      concierge_order: result.concierge_order,
    });
  } catch (error) {
    console.error("Error creating concierge order:", error);

    if (error instanceof z.ZodError) {
      return res.status(400).json({
        message: "Validation error",
        errors: error.errors
      });
    }

    return res.status(500).json({
      message: "Internal server error",
      error: error.message
    });
  }
};
