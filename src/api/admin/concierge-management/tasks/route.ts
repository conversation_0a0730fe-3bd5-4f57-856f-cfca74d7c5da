import { MedusaRequest, MedusaResponse, AuthenticatedMedusaRequest } from "@camped-ai/framework/http";
import { z } from "zod";
import { CONCIERGE_MANAGEMENT_MODULE } from "../../../../modules/concierge-management";
import { TaskStatus, TaskPriority } from "../../../../modules/concierge-management/types";

// Validation schemas
export const GetAdminConciergeTasksQuery = z.object({
  limit: z.coerce.number().min(1).max(100).optional().default(20),
  offset: z.coerce.number().min(0).optional().default(0),
  q: z.string().optional(),
  status: z.nativeEnum(TaskStatus).optional(),
  priority: z.nativeEnum(TaskPriority).optional(),
  entity_type: z.string().optional(),
  entity_id: z.string().optional(),
  assigned_to: z.string().optional(),
  created_by: z.string().optional(),
  due_date_gte: z.string().datetime().optional(),
  due_date_lte: z.string().datetime().optional(),
  created_at_gte: z.string().datetime().optional(),
  created_at_lte: z.string().datetime().optional(),
  order: z.string().optional().default("created_at"),
  sort_order: z.enum(["asc", "desc"]).optional().default("desc"),
});

export const PostAdminCreateConciergeTask = z.object({
  title: z.string().min(1).max(255),
  description: z.string().optional(),
  status: z.nativeEnum(TaskStatus).optional().default(TaskStatus.PENDING),
  priority: z.nativeEnum(TaskPriority).optional().default(TaskPriority.MEDIUM),
  entity_type: z.string().optional(),
  entity_id: z.string().optional(),
  assigned_to: z.string().optional(),
  due_date: z.string().datetime().optional(),
  metadata: z.record(z.any()).optional(),
});

export type GetAdminConciergeTasksQueryType = z.infer<typeof GetAdminConciergeTasksQuery>;
export type PostAdminCreateConciergeTaskType = z.infer<typeof PostAdminCreateConciergeTask>;

// GET endpoint to list concierge tasks
export const GET = async (
  req: AuthenticatedMedusaRequest,
  res: MedusaResponse
) => {
  try {
    const conciergeService = req.scope.resolve(CONCIERGE_MANAGEMENT_MODULE);
    
    const query = GetAdminConciergeTasksQuery.parse(req.query);
    
    // Build filters
    const filters: any = {};
    
    if (query.q) {
      // Search in title and description
      filters.$or = [
        { title: { $ilike: `%${query.q}%` } },
        { description: { $ilike: `%${query.q}%` } },
      ];
    }
    
    if (query.status) filters.status = query.status;
    if (query.priority) filters.priority = query.priority;
    if (query.entity_type) filters.entity_type = query.entity_type;
    if (query.entity_id) filters.entity_id = query.entity_id;
    if (query.assigned_to) filters.assigned_to = query.assigned_to;
    if (query.created_by) filters.created_by = query.created_by;
    
    // Date filters
    if (query.due_date_gte || query.due_date_lte) {
      filters.due_date = {};
      if (query.due_date_gte) filters.due_date.gte = new Date(query.due_date_gte);
      if (query.due_date_lte) filters.due_date.lte = new Date(query.due_date_lte);
    }
    
    if (query.created_at_gte || query.created_at_lte) {
      filters.created_at = {};
      if (query.created_at_gte) filters.created_at.gte = new Date(query.created_at_gte);
      if (query.created_at_lte) filters.created_at.lte = new Date(query.created_at_lte);
    }
    
    // Build order
    const order: Record<string, "ASC" | "DESC"> = {};
    order[query.order] = query.sort_order.toUpperCase() as "ASC" | "DESC";
    
    const result = await conciergeService.listTasks({
      filters,
      options: {
        limit: query.limit,
        offset: query.offset,
        order,
      },
    });
    
    return res.json(result);
  } catch (error) {
    console.error("Error listing concierge tasks:", error);
    return res.status(500).json({ 
      message: "Error listing concierge tasks", 
      error: error.message 
    });
  }
};

// POST endpoint to create a concierge task
export const POST = async (
  req: MedusaRequest<PostAdminCreateConciergeTaskType>,
  res: MedusaResponse
) => {
  try {
    const conciergeService = req.scope.resolve(CONCIERGE_MANAGEMENT_MODULE);
    
    const validatedData = PostAdminCreateConciergeTask.parse(req.body);
    
    // Add created_by from authenticated user if available
    const taskData = {
      ...validatedData,
      ...(validatedData.due_date && { due_date: new Date(validatedData.due_date) }),
      created_by: req.user?.id,
    };
    
    const task = await conciergeService.createTask({ task: taskData });
    
    return res.status(201).json({ task });
  } catch (error) {
    console.error("Error creating concierge task:", error);
    
    if (error instanceof z.ZodError) {
      return res.status(400).json({ 
        message: "Validation error", 
        errors: error.errors 
      });
    }
    
    return res.status(500).json({ 
      message: "Error creating concierge task", 
      error: error.message 
    });
  }
};
