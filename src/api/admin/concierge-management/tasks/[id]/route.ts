import { MedusaRequest, MedusaResponse, AuthenticatedMedusaRequest } from "@camped-ai/framework/http";
import { z } from "zod";
import { CONCIERGE_MANAGEMENT_MODULE } from "../../../../../modules/concierge-management";
import { TaskStatus, TaskPriority } from "../../../../../modules/concierge-management/types";

// Validation schemas
export const PutAdminUpdateConciergeTask = z.object({
  title: z.string().min(1).max(255).optional(),
  description: z.string().optional(),
  status: z.nativeEnum(TaskStatus).optional(),
  priority: z.nativeEnum(TaskPriority).optional(),
  entity_type: z.string().optional(),
  entity_id: z.string().optional(),
  assigned_to: z.string().optional(),
  due_date: z.string().datetime().optional(),
  metadata: z.record(z.any()).optional(),
});

export type PutAdminUpdateConciergeTaskType = z.infer<typeof PutAdminUpdateConciergeTask>;

// GET endpoint to retrieve a single concierge task
export const GET = async (
  req: AuthenticatedMedusaRequest,
  res: MedusaResponse
) => {
  try {
    const conciergeService = req.scope.resolve(CONCIERGE_MANAGEMENT_MODULE);
    
    const { id } = req.params;
    
    const task = await conciergeService.retrieveTask(id);
    
    return res.json({ task });
  } catch (error) {
    console.error("Error retrieving concierge task:", error);
    
    if (error.message.includes("not found")) {
      return res.status(404).json({ 
        message: "Task not found" 
      });
    }
    
    return res.status(500).json({ 
      message: "Error retrieving concierge task", 
      error: error.message 
    });
  }
};

// PUT endpoint to update a concierge task
export const PUT = async (
  req: MedusaRequest<PutAdminUpdateConciergeTaskType>,
  res: MedusaResponse
) => {
  try {
    const conciergeService = req.scope.resolve(CONCIERGE_MANAGEMENT_MODULE);
    
    const { id } = req.params;
    const validatedData = PutAdminUpdateConciergeTask.parse(req.body);
    
    // Add updated_by from authenticated user if available
    const updateData = {
      ...validatedData,
      ...(validatedData.due_date && { due_date: new Date(validatedData.due_date) }),
      updated_by: req.user?.id,
    };
    
    const task = await conciergeService.updateTask(id, { task: updateData });
    
    return res.json({ task });
  } catch (error) {
    console.error("Error updating concierge task:", error);
    
    if (error instanceof z.ZodError) {
      return res.status(400).json({ 
        message: "Validation error", 
        errors: error.errors 
      });
    }
    
    if (error.message.includes("not found")) {
      return res.status(404).json({ 
        message: "Task not found" 
      });
    }
    
    return res.status(500).json({ 
      message: "Error updating concierge task", 
      error: error.message 
    });
  }
};

// DELETE endpoint to soft delete a concierge task
export const DELETE = async (
  req: AuthenticatedMedusaRequest,
  res: MedusaResponse
) => {
  try {
    const conciergeService = req.scope.resolve(CONCIERGE_MANAGEMENT_MODULE);
    
    const { id } = req.params;
    
    // Use soft delete by default
    const task = await conciergeService.softDeleteTask(id, req.user?.id);
    
    return res.json({ 
      message: "Task deleted successfully",
      task 
    });
  } catch (error) {
    console.error("Error deleting concierge task:", error);
    
    if (error.message.includes("not found")) {
      return res.status(404).json({ 
        message: "Task not found" 
      });
    }
    
    return res.status(500).json({ 
      message: "Error deleting concierge task", 
      error: error.message 
    });
  }
};
