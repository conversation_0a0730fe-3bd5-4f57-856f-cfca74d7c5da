import { MedusaRequest, MedusaResponse, AuthenticatedMedusaRequest } from "@camped-ai/framework/http";
import { CONCIERGE_MANAGEMENT_MODULE } from "../../../../../../modules/concierge-management";

// POST endpoint to cancel a concierge task
export const POST = async (
  req: AuthenticatedMedusaRequest,
  res: MedusaResponse
) => {
  try {
    const conciergeService = req.scope.resolve(CONCIERGE_MANAGEMENT_MODULE);
    
    const { id } = req.params;
    
    const task = await conciergeService.cancelTask(id, req.user?.id);
    
    return res.json({ 
      message: "Task cancelled successfully",
      task 
    });
  } catch (error) {
    console.error("Error cancelling concierge task:", error);
    
    if (error.message.includes("not found")) {
      return res.status(404).json({ 
        message: "Task not found" 
      });
    }
    
    return res.status(500).json({ 
      message: "Error cancelling concierge task", 
      error: error.message 
    });
  }
};
