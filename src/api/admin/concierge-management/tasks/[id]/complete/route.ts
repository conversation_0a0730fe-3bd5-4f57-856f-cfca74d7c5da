import { MedusaRequest, MedusaResponse, AuthenticatedMedusaRequest } from "@camped-ai/framework/http";
import { CONCIERGE_MANAGEMENT_MODULE } from "../../../../../../modules/concierge-management";

// POST endpoint to complete a concierge task
export const POST = async (
  req: AuthenticatedMedusaRequest,
  res: MedusaResponse
) => {
  try {
    const conciergeService = req.scope.resolve(CONCIERGE_MANAGEMENT_MODULE);
    
    const { id } = req.params;
    
    const task = await conciergeService.completeTask(id, req.user?.id);
    
    return res.json({ 
      message: "Task completed successfully",
      task 
    });
  } catch (error) {
    console.error("Error completing concierge task:", error);
    
    if (error.message.includes("not found")) {
      return res.status(404).json({ 
        message: "Task not found" 
      });
    }
    
    return res.status(500).json({ 
      message: "<PERSON>rror completing concierge task", 
      error: error.message 
    });
  }
};
