import { MedusaRequest, MedusaResponse, AuthenticatedMedusaRequest } from "@camped-ai/framework/http";
import { z } from "zod";
import { CONCIERGE_MANAGEMENT_MODULE } from "../../../../../../modules/concierge-management";

// Validation schema for task assignment
export const PostAdminAssignConciergeTask = z.object({
  assigned_to: z.string().min(1),
});

export type PostAdminAssignConciergeTaskType = z.infer<typeof PostAdminAssignConciergeTask>;

// POST endpoint to assign a concierge task
export const POST = async (
  req: MedusaRequest<PostAdminAssignConciergeTaskType>,
  res: MedusaResponse
) => {
  try {
    const conciergeService = req.scope.resolve(CONCIERGE_MANAGEMENT_MODULE);
    
    const { id } = req.params;
    const { assigned_to } = PostAdminAssignConciergeTask.parse(req.body);
    
    const task = await conciergeService.assignTask(id, assigned_to, req.user?.id);
    
    return res.json({ 
      message: "Task assigned successfully",
      task 
    });
  } catch (error) {
    console.error("Error assigning concierge task:", error);
    
    if (error instanceof z.ZodError) {
      return res.status(400).json({ 
        message: "Validation error", 
        errors: error.errors 
      });
    }
    
    if (error.message.includes("not found")) {
      return res.status(404).json({ 
        message: "Task not found" 
      });
    }
    
    return res.status(500).json({ 
      message: "Error assigning concierge task", 
      error: error.message 
    });
  }
};

// DELETE endpoint to unassign a concierge task
export const DELETE = async (
  req: AuthenticatedMedusaRequest,
  res: MedusaResponse
) => {
  try {
    const conciergeService = req.scope.resolve(CONCIERGE_MANAGEMENT_MODULE);
    
    const { id } = req.params;
    
    const task = await conciergeService.unassignTask(id, req.user?.id);
    
    return res.json({ 
      message: "Task unassigned successfully",
      task 
    });
  } catch (error) {
    console.error("Error unassigning concierge task:", error);
    
    if (error.message.includes("not found")) {
      return res.status(404).json({ 
        message: "Task not found" 
      });
    }
    
    return res.status(500).json({ 
      message: "Error unassigning concierge task", 
      error: error.message 
    });
  }
};
