import {
  AuthenticatedMedusaRequest,
  MedusaResponse,
} from "@camped-ai/framework/http";
import { z } from "zod";
import { Modules } from "@camped-ai/framework/utils";
import { IUserModuleService } from "@camped-ai/framework/types";
import { CreateUserWorkflow } from "../../../workflows/user-management/create-user";
import { UserRole } from "../../../modules/rbac/types";

// Validation schema for creating a user
export const PostAdminCreateUser = z
  .object({
    email: z.string().email("Invalid email format"),
    first_name: z.string().min(1, "First name is required"),
    last_name: z.string().min(1, "Last name is required"),
    role: z.enum([UserRole.ADMIN]).optional(),
    role_id: z.string().optional(),
    assigned_hotels: z.array(z.string()).optional().default([]),
    custom_permissions: z.array(z.string()).optional().default([]),
    metadata: z.record(z.any()).optional(),
  })
  .refine((data) => data.role || data.role_id, {
    message: "Either role or role_id must be provided",
    path: ["role"],
  });

export type PostAdminCreateUserType = z.infer<typeof PostAdminCreateUser>;

/**
 * POST /admin/users
 * Create a new user directly with dummy password (bypassing invite system)
 * Admin only endpoint
 */
export const POST = async (
  req: AuthenticatedMedusaRequest<PostAdminCreateUserType>,
  res: MedusaResponse
) => {
  try {
    const {
      email,
      first_name,
      last_name,
      role,
      role_id,
      assigned_hotels,
      custom_permissions,
      metadata,
    } = req.body;

    // Validate email format (additional validation beyond Zod)
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return res.status(400).json({
        type: "validation_error",
        message: "Invalid email format",
      });
    }

    // Check if user already exists
    const userService: IUserModuleService = req.scope.resolve(Modules.USER);
    const existingUsers = await userService.listUsers({ email });

    if (existingUsers.length > 0) {
      return res.status(400).json({
        type: "validation_error",
        message: "User with this email already exists",
      });
    }

    // Validate name fields
    if (first_name.trim().length < 1 || last_name.trim().length < 1) {
      return res.status(400).json({
        type: "validation_error",
        message: "First name and last name are required",
      });
    }

    // Get current user for audit trail
    const currentUser = req.auth_context?.actor_id;

    // Build RBAC metadata
    const rbacMetadata: any = {
      is_active: true,
      custom_permissions: custom_permissions || [],
      assigned_hotels: assigned_hotels || [],
      updated_by: currentUser || "system",
      updated_at: new Date().toISOString(),
      requires_password_reset: true, // Force password reset on first login
    };

    // Add role field based on type
    if (role) {
      rbacMetadata.role = role;
    } else if (role_id) {
      rbacMetadata.role_id = role_id;
    }

    // Create user using workflow
    const { result: user } = await CreateUserWorkflow(req.scope).run({
      input: {
        email,
        first_name,
        last_name,
        rbac_metadata: rbacMetadata,
        additional_metadata: metadata,
      },
    });

    res.status(201).json({
      user: {
        id: user.id,
        email: user.email,
        first_name: user.first_name,
        last_name: user.last_name,
        created_at: user.created_at,
        rbac: rbacMetadata,
      },
      message:
        "User created successfully. Welcome email sent with password reset instructions.",
    });
  } catch (error) {
    if (error.message?.includes("already exists")) {
      return res.status(400).json({
        type: "validation_error",
        message: "User with this email already exists",
      });
    }

    res.status(500).json({
      type: "server_error",
      message: error instanceof Error ? error.message : "Failed to create user",
    });
  }
};
