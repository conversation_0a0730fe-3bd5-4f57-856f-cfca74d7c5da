import { MedusaRequest, MedusaResponse } from "@camped-ai/framework";
import { <PERSON><PERSON><PERSON>, MedusaError } from "@camped-ai/framework/utils";
import { z } from "zod";

/**
 * Admin API for region management
 * Use this to create regions before cart functionality is needed
 */

const CreateRegionSchema = z.object({
  name: z.string().min(1, "Region name is required"),
  currency_code: z.string().length(3, "Currency code must be 3 characters"),
  countries: z.array(z.string()).min(1, "At least one country is required"),
  tax_rate: z.number().min(0).max(100).optional(),
  is_default: z.boolean().optional().default(false),
  metadata: z.record(z.any()).optional()
});

type CreateRegionInput = z.infer<typeof CreateRegionSchema>;

/**
 * GET /admin/regions
 * List all regions for admin management
 */
export const GET = async (req: MedusaRequest, res: MedusaResponse) => {
  try {
    const regionModuleService = req.scope.resolve(Modules.REGION);

    const regions = await regionModuleService.listRegions(
      {},
      {
        relations: ["countries"],
        order: { created_at: "DESC" }
      }
    );

    // Transform for admin display
    const transformedRegions = regions.map(region => ({
      id: region.id,
      name: region.name,
      currency_code: region.currency_code,
      countries: region.countries?.map(country => ({
        id: country.id,
        name: country.name,
        iso_2: country.iso_2,
        iso_3: country.iso_3
      })) || [],
      tax_rate: region.metadata?.tax_rate || null,
      is_default: region.metadata?.is_default === true,
      created_at: region.created_at,
      updated_at: region.updated_at,
      metadata: region.metadata
    }));

    res.json({
      regions: transformedRegions,
      count: transformedRegions.length
    });

  } catch (error) {
    console.error("Error fetching regions:", error);
    res.status(500).json({
      message: "Failed to fetch regions",
      error: error instanceof Error ? error.message : "Unknown error"
    });
  }
};

/**
 * POST /admin/regions
 * Create a new region for cart functionality
 */
export const POST = async (req: MedusaRequest, res: MedusaResponse) => {
  try {
    const validatedData: CreateRegionInput = CreateRegionSchema.parse(req.body);
    const regionModuleService = req.scope.resolve(Modules.REGION);

    // If this is set as default, unset other defaults first
    if (validatedData.is_default) {
      const existingDefaults = await regionModuleService.listRegions({
        metadata: { is_default: true }
      });

      for (const existingDefault of existingDefaults) {
        await regionModuleService.updateRegions(existingDefault.id, {
          metadata: {
            ...existingDefault.metadata,
            is_default: false
          }
        });
      }
    }

    // Create the region
    const region = await regionModuleService.createRegions({
      name: validatedData.name,
      currency_code: validatedData.currency_code,
      countries: validatedData.countries,
      metadata: {
        tax_rate: validatedData.tax_rate,
        is_default: validatedData.is_default,
        created_by: "admin_api",
        ...validatedData.metadata
      }
    });

    console.log(`✅ Created region: ${region.name} (${region.currency_code})`);

    res.status(201).json({
      region: {
        id: region.id,
        name: region.name,
        currency_code: region.currency_code,
        countries: validatedData.countries,
        tax_rate: validatedData.tax_rate,
        is_default: validatedData.is_default,
        created_at: region.created_at,
        updated_at: region.updated_at
      },
      message: "Region created successfully"
    });

  } catch (error) {
    console.error("Error creating region:", error);
    
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        message: "Validation error",
        errors: error.errors
      });
    }

    res.status(500).json({
      message: "Failed to create region",
      error: error instanceof Error ? error.message : "Unknown error"
    });
  }
};
