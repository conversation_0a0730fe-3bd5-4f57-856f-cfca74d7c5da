import {
  MedusaRequest,
  MedusaResponse,
} from "@camped-ai/framework/http";
import { z } from "zod";
import { SUPPLIER_PRODUCTS_SERVICES_MODULE } from "src/modules/supplier-products-services";
import SupplierProductsServicesModuleService from "src/modules/supplier-products-services/service";

// Request body validation
const PostAdminBulkStatusSupplierOfferingsBody = z.object({
  ids: z.array(z.string()).min(1, "At least one ID is required"),
  status: z.enum(["active", "inactive"], {
    required_error: "Status is required",
    invalid_type_error: "Status must be either 'active' or 'inactive'",
  }),
});

type PostAdminBulkStatusSupplierOfferingsBodyType = z.infer<typeof PostAdminBulkStatusSupplierOfferingsBody>;

/**
 * POST /admin/supplier-management/supplier-offerings/bulk-status
 * Update status for multiple supplier offerings
 */
export const POST = async (
  req: MedusaRequest<PostAdminBulkStatusSupplierOfferingsBodyType>,
  res: MedusaResponse
) => {
  try {
    console.log("🌐 POST /admin/supplier-management/supplier-offerings/bulk-status called");
    console.log("📥 Request body:", req.body);

    const supplierProductsServicesService: SupplierProductsServicesModuleService = 
      req.scope.resolve(SUPPLIER_PRODUCTS_SERVICES_MODULE);

    // Validate request body
    const validation = PostAdminBulkStatusSupplierOfferingsBody.safeParse(req.body);
    if (!validation.success) {
      console.error("❌ Validation failed:", validation.error);
      return res.status(400).json({
        type: "validation_error",
        message: "Invalid request data",
        errors: validation.error.errors,
      });
    }

    const { ids, status } = validation.data;

    console.log(`🔄 Updating ${ids.length} supplier offerings to status: ${status}`);

    // Update each supplier offering individually
    const updatePromises = ids.map(async (id) => {
      try {
        console.log(`🔄 Updating supplier offering ${id} to ${status}`);
        
        const updatedOffering = await supplierProductsServicesService.updateSupplierOffering(id, {
          status,
        });
        
        console.log(`✅ Successfully updated supplier offering ${id}`);
        return {
          id,
          success: true,
          offering: updatedOffering,
        };
      } catch (error) {
        console.error(`❌ Failed to update supplier offering ${id}:`, error);
        return {
          id,
          success: false,
          error: error instanceof Error ? error.message : "Unknown error",
        };
      }
    });

    const results = await Promise.all(updatePromises);

    // Separate successful and failed updates
    const successful = results.filter(result => result.success);
    const failed = results.filter(result => !result.success);

    console.log(`✅ Successfully updated ${successful.length}/${ids.length} supplier offerings`);
    
    if (failed.length > 0) {
      console.error(`❌ Failed to update ${failed.length} supplier offerings:`, failed);
    }

    // Return results
    return res.status(200).json({
      message: `Successfully updated ${successful.length} of ${ids.length} supplier offerings`,
      successful_count: successful.length,
      failed_count: failed.length,
      successful_ids: successful.map(r => r.id),
      failed_updates: failed.map(r => ({
        id: r.id,
        error: r.error,
      })),
      updated_offerings: successful.map(r => r.offering),
    });
  } catch (error) {
    console.error("💥 Bulk status update error:", error);
    
    return res.status(500).json({
      type: "server_error",
      message: error instanceof Error ? error.message : "Failed to update supplier offering status",
    });
  }
};
