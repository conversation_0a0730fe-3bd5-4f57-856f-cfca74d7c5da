import {
  MedusaRequest,
  MedusaResponse,
  AuthenticatedMedusaRequest,
} from "@camped-ai/framework/http";
import { z } from "zod";
import { Modules } from "@camped-ai/framework/utils";
import { SUPPLIER_PRODUCTS_SERVICES_MODULE } from "src/modules/supplier-products-services";
import SupplierProductsServicesModuleService from "src/modules/supplier-products-services/service";
import { PostAdminUpdateSupplierOffering } from "../validators";
import {
  parseDateAsUTCStartOfDay,
  parseDateAsUTCEndOfDay,
} from "src/utils/date-utils";

type PostAdminUpdateSupplierOfferingType = z.infer<
  typeof PostAdminUpdateSupplierOffering
>;

/**
 * Resolves hotel and destination IDs to names in custom_fields for supplier offerings
 */
async function resolveCustomFieldNames(
  offering: any,
  scope: any
): Promise<any> {
  if (
    !offering.custom_fields ||
    !offering.product_service?.category?.dynamic_field_schema
  ) {
    return offering;
  }

  const query = scope.resolve("query");
  const resolvedCustomFields = { ...offering.custom_fields };

  // Find hotel, destination, and addon fields in the category schema
  const hotelFields = offering.product_service.category.dynamic_field_schema.filter(
    (field: any) => field.type === "hotels"
  );
  const destinationFields = offering.product_service.category.dynamic_field_schema.filter(
    (field: any) => field.type === "destinations"
  );
  const addonFields = offering.product_service.category.dynamic_field_schema.filter(
    (field: any) => field.type === "addons"
  );

  // Resolve hotel field names
  for (const field of hotelFields) {
    if (resolvedCustomFields[field.key]) {
      try {
        let hotelIds: string[] = [];

        // Handle different data formats
        if (Array.isArray(resolvedCustomFields[field.key])) {
          hotelIds = resolvedCustomFields[field.key];
        } else if (typeof resolvedCustomFields[field.key] === "string") {
          try {
            const parsed = JSON.parse(resolvedCustomFields[field.key]);
            hotelIds = Array.isArray(parsed) ? parsed : [parsed];
          } catch {
            hotelIds = [resolvedCustomFields[field.key]];
          }
        }

        const hotelNames: string[] = [];
        for (const hotelId of hotelIds) {
          try {
            const result = await query.graph({
              entity: "hotel",
              filters: { id: hotelId },
              fields: ["id", "name"],
            });

            if (result.data && result.data.length > 0) {
              hotelNames.push(result.data[0].name);
            } else {
              hotelNames.push(hotelId); // Fallback to ID
            }
          } catch (error) {
            console.warn(
              `Failed to resolve hotel name for ID ${hotelId}:`,
              error
            );
            hotelNames.push(hotelId); // Fallback to ID
          }
        }

        // Store both resolved names and original IDs
        resolvedCustomFields[field.key] = hotelIds;
        resolvedCustomFields[`${field.key}_names`] = hotelNames;
      } catch (error) {
        console.warn(
          `Error resolving hotel names for field ${field.key}:`,
          error
        );
      }
    }
  }

  // Resolve destination field names
  for (const field of destinationFields) {
    if (resolvedCustomFields[field.key]) {
      try {
        let destinationIds: string[] = [];

        // Handle different data formats
        if (Array.isArray(resolvedCustomFields[field.key])) {
          destinationIds = resolvedCustomFields[field.key];
        } else if (typeof resolvedCustomFields[field.key] === "string") {
          try {
            const parsed = JSON.parse(resolvedCustomFields[field.key]);
            destinationIds = Array.isArray(parsed) ? parsed : [parsed];
          } catch {
            destinationIds = [resolvedCustomFields[field.key]];
          }
        }

        const destinationNames: string[] = [];
        for (const destinationId of destinationIds) {
          try {
            const result = await query.graph({
              entity: "destination",
              filters: { id: destinationId },
              fields: ["id", "name"],
            });

            if (result.data && result.data.length > 0) {
              destinationNames.push(result.data[0].name);
            } else {
              destinationNames.push(destinationId); // Fallback to ID
            }
          } catch (error) {
            console.warn(
              `Failed to resolve destination name for ID ${destinationId}:`,
              error
            );
            destinationNames.push(destinationId); // Fallback to ID
          }
        }

        // Store both resolved names and original IDs
        resolvedCustomFields[field.key] = destinationIds;
        resolvedCustomFields[`${field.key}_names`] = destinationNames;
      } catch (error) {
        console.warn(
          `Error resolving destination names for field ${field.key}:`,
          error
        );
      }
    }
  }

  // Resolve addon field names
  for (const field of addonFields) {
    if (resolvedCustomFields[field.key]) {
      try {
        let addonIds: string[] = [];

        // Handle different data formats
        if (Array.isArray(resolvedCustomFields[field.key])) {
          addonIds = resolvedCustomFields[field.key];
        } else if (typeof resolvedCustomFields[field.key] === 'string') {
          try {
            const parsed = JSON.parse(resolvedCustomFields[field.key]);
            addonIds = Array.isArray(parsed) ? parsed : [parsed];
          } catch {
            addonIds = [resolvedCustomFields[field.key]];
          }
        }

        const addonNames: string[] = [];
        for (const addonId of addonIds) {
          try {
            const result = await query.graph({
              entity: "product_service",
              filters: { id: addonId },
              fields: ["id", "name"],
            });

            if (result.data && result.data.length > 0) {
              addonNames.push(result.data[0].name);
            } else {
              addonNames.push(addonId); // Fallback to ID
            }
          } catch (error) {
            console.warn(
              `Failed to resolve addon name for ID ${addonId}:`,
              error
            );
            addonNames.push(addonId); // Fallback to ID
          }
        }

        // Store both resolved names and original IDs
        resolvedCustomFields[field.key] = addonIds;
        resolvedCustomFields[`${field.key}_names`] = addonNames;
      } catch (error) {
        console.warn(
          `Error resolving addon names for field ${field.key}:`,
          error
        );
      }
    }
  }

  return {
    ...offering,
    custom_fields: resolvedCustomFields,
  };
}

export const GET = async (req: MedusaRequest, res: MedusaResponse) => {
  try {
    const supplierProductsServicesService: SupplierProductsServicesModuleService =
      req.scope.resolve(SUPPLIER_PRODUCTS_SERVICES_MODULE);

    const { id } = req.params;

    if (!id) {
      return res.status(400).json({
        type: "invalid_data",
        message: "Supplier offering ID is required",
      });
    }

    const supplierOffering =
      await supplierProductsServicesService.getSupplierOffering(id);

    // Resolve custom field names for the offering
    const resolvedOffering = await resolveCustomFieldNames(
      supplierOffering,
      req.scope
    );

    // Also resolve product service name if it contains IDs
    if (resolvedOffering.product_service) {
      const resolvedProductService = await resolveCustomFieldNames(
        resolvedOffering.product_service,
        req.scope
      );
      resolvedOffering.product_service = resolvedProductService;
    }

    console.log("🔍 API Debug - Supplier offering with resolved names:", {
      id: resolvedOffering.id,
      original_product_service_name: supplierOffering.product_service?.name,
      resolved_product_service_name: resolvedOffering.product_service?.name,
      product_service_custom_fields:
        resolvedOffering.product_service?.custom_fields,
      offering_custom_fields: resolvedOffering.custom_fields,
      has_category_schema:
        !!resolvedOffering.product_service?.category?.dynamic_field_schema,
    });

    res.status(200).json({
      supplier_offering: resolvedOffering,
    });
  } catch (error) {
    console.error("Error getting supplier offering:", error);

    if (error.type === "not_found") {
      return res.status(404).json({
        type: "not_found",
        message: error.message,
      });
    }

    res.status(500).json({
      type: "server_error",
      message: "Internal server error",
    });
  }
};

export const PUT = async (
  req: AuthenticatedMedusaRequest<PostAdminUpdateSupplierOfferingType>,
  res: MedusaResponse
) => {
  try {
    const supplierProductsServicesService: SupplierProductsServicesModuleService =
      req.scope.resolve(SUPPLIER_PRODUCTS_SERVICES_MODULE);

    const { id } = req.params;

    if (!id) {
      return res.status(400).json({
        type: "invalid_data",
        message: "Supplier offering ID is required",
      });
    }

    // Validate request body
    const validatedData = PostAdminUpdateSupplierOffering.parse(req.body);

    // Convert date strings to UTC Date objects if provided, preserve null for open-ended periods
    const processedData = {
      ...validatedData,
      active_from:
        validatedData.active_from !== undefined
          ? validatedData.active_from
            ? parseDateAsUTCStartOfDay(validatedData.active_from)
            : null
          : undefined,
      active_to:
        validatedData.active_to !== undefined
          ? validatedData.active_to
            ? parseDateAsUTCEndOfDay(validatedData.active_to)
            : null
          : undefined,
      updated_by: req.auth_context?.actor_id || validatedData.updated_by,
    };

    const supplierOffering =
      await supplierProductsServicesService.updateSupplierOffering(
        id,
        processedData
      );

    // Emit supplier-offering.updated event for price sync
    try {
      const eventBusService = req.scope.resolve(Modules.EVENT_BUS);
      await eventBusService.emit({
        name: "supplier-offering.updated",
        data: {
          id: supplierOffering.id,
          ...supplierOffering,
        },
      });
    } catch (eventError) {
      console.error(
        "❌ Failed to emit supplier-offering.updated event:",
        eventError
      );
      // Don't fail the request if event emission fails
    }

    res.status(200).json({
      supplier_offering: supplierOffering,
    });
  } catch (error) {
    console.error("Error updating supplier offering:", error);

    if (error.name === "ZodError") {
      return res.status(400).json({
        type: "invalid_data",
        message: "Invalid request data",
        details: error.errors,
      });
    }

    // Handle MedusaError types
    if (
      error.type === "duplicate_error" ||
      (error.__isMedusaError && error.type === "duplicate_error")
    ) {
      return res.status(409).json({
        type: "duplicate_error",
        message: error.message,
      });
    }

    if (error.type === "not_found") {
      return res.status(404).json({
        type: "not_found",
        message: error.message,
      });
    }

    if (error.type === "invalid_data") {
      return res.status(400).json({
        type: "invalid_data",
        message: error.message,
      });
    }

    res.status(500).json({
      type: "server_error",
      message: "Internal server error",
    });
  }
};

export const DELETE = async (
  req: AuthenticatedMedusaRequest,
  res: MedusaResponse
) => {
  try {
    const supplierProductsServicesService: SupplierProductsServicesModuleService =
      req.scope.resolve(SUPPLIER_PRODUCTS_SERVICES_MODULE);

    const { id } = req.params;

    if (!id) {
      return res.status(400).json({
        type: "invalid_data",
        message: "Supplier offering ID is required",
      });
    }

    // Get the supplier offering before deletion for event emission

    await supplierProductsServicesService.deleteSupplierOffering(id);
    // const supplierOfferingToDelete = await supplierProductsServicesService.retrieveSupplierOffering(id);
    // // Emit supplier-offering.deleted event for cleanup
    // try {
    //   const eventBusService = req.scope.resolve(Modules.EVENT_BUS);
    //   await eventBusService.emit({
    //     name: "supplier-offering.deleted",
    //     data: {
    //       id: supplierOfferingToDelete.id,
    //       ...supplierOfferingToDelete,
    //     },
    //   });
    //   console.log(`✅ Emitted supplier-offering.deleted event for ${supplierOfferingToDelete.id}`);
    // } catch (eventError) {
    //   console.error("❌ Failed to emit supplier-offering.deleted event:", eventError);
    //   // Don't fail the request if event emission fails
    // }

    res.status(200).json({
      id,
      deleted: true,
    });
  } catch (error) {
    console.error("Error deleting supplier offering:", error);

    if (error.type === "not_found") {
      return res.status(404).json({
        type: "not_found",
        message: error.message,
      });
    }

    res.status(500).json({
      type: "server_error",
      message: "Internal server error",
    });
  }
};
