import { z } from "zod";

// Get Supplier Offering Cost History Query Validation
export const GetAdminSupplierOfferingCostHistoryQuery = z.object({
  limit: z.string().optional(),
  offset: z.string().optional(),
  changed_by_user_id: z.string().optional(),
  date_from: z.string().optional(),
  date_to: z.string().optional(),
  has_cost_change: z.enum(["true", "false"]).optional(),
  has_currency_change: z.enum(["true", "false"]).optional(),
});

// Create Supplier Offering Cost History Validation
export const PostAdminCreateSupplierOfferingCostHistory = z.object({
  supplier_offering_id: z.string().min(1, "Supplier offering ID is required"),
  previous_cost: z.number().min(0, "Previous cost must be non-negative").optional(),
  new_cost: z.number().min(0, "New cost must be non-negative").optional(),
  previous_currency: z
    .string()
    .regex(/^[A-Z]{3}$/, "Previous currency must be a valid 3-letter code")
    .optional(),
  new_currency: z
    .string()
    .regex(/^[A-Z]{3}$/, "New currency must be a valid 3-letter code")
    .optional(),
  change_reason: z.string().optional(),
  changed_by_user_id: z.string().optional(),
});

// Update Supplier Offering Cost History Validation
export const PostAdminUpdateSupplierOfferingCostHistory = z.object({
  change_reason: z.string().optional(),
  changed_by_user_id: z.string().optional(),
});
