import {
  MedusaRequest,
  MedusaResponse,
  AuthenticatedMedusaRequest,
} from "@camped-ai/framework/http";
import { z } from "zod";
import { SUPPLIER_PRODUCTS_SERVICES_MODULE } from "src/modules/supplier-products-services";
import SupplierProductsServicesModuleService from "src/modules/supplier-products-services/service";
import {
  GetAdminSupplierOfferingCostHistoryQuery,
  PostAdminCreateSupplierOfferingCostHistory,
} from "./validators";

type GetAdminSupplierOfferingCostHistoryQueryType = z.infer<
  typeof GetAdminSupplierOfferingCostHistoryQuery
>;
type PostAdminCreateSupplierOfferingCostHistoryType = z.infer<
  typeof PostAdminCreateSupplierOfferingCostHistory
>;

export const GET = async (
  req: MedusaRequest<{}, GetAdminSupplierOfferingCostHistoryQueryType>,
  res: MedusaResponse
) => {
  try {
    const supplierProductsServicesService: SupplierProductsServicesModuleService =
      req.scope.resolve(SUPPLIER_PRODUCTS_SERVICES_MODULE);

    const { id } = req.params as { id: string };

    // Validate that the supplier offering exists
    try {
      const offering =
        await supplierProductsServicesService.getSupplierOffering(id);
      if (!offering) {
        return res.status(404).json({
          type: "not_found",
          message: `Supplier offering with id ${id} not found`,
        });
      }
    } catch (error) {
      if (error.type === "not_found") {
        return res.status(404).json({
          type: "not_found",
          message: `Supplier offering with id ${id} not found`,
        });
      }
      throw error;
    }

    // Parse query parameters
    const limit = parseInt(req.query.limit as string) || 25;
    const offset = parseInt(req.query.offset as string) || 0;

    // Build filters
    const filters: any = {
      supplier_offering_id: id,
      limit,
      offset,
    };

    if (req.query.changed_by_user_id) {
      filters.changed_by_user_id = req.query.changed_by_user_id;
    }

    if (req.query.date_from) {
      filters.date_from = new Date(req.query.date_from as string);
    }

    if (req.query.date_to) {
      filters.date_to = new Date(req.query.date_to as string);
    }

    if (req.query.has_cost_change === "true") {
      filters.has_cost_change = true;
    }

    if (req.query.has_currency_change === "true") {
      filters.has_currency_change = true;
    }

    const result =
      await supplierProductsServicesService.listSupplierOfferingCostHistoryWithFilters(
        filters
      );

    // Get stats for this specific offering
    const stats =
      await supplierProductsServicesService.getSupplierOfferingCostHistoryStats(
        id
      );

    res.status(200).json({
      cost_history: result.data,
      count: result.count,
      limit: result.limit,
      offset: result.offset,
      stats,
    });
  } catch (error) {
    console.error("Error listing supplier offering cost history:", error);

    if (error.name === "ZodError") {
      return res.status(400).json({
        type: "invalid_data",
        message: "Invalid query parameters",
        details: error.errors,
      });
    }

    res.status(500).json({
      type: "server_error",
      message: "Internal server error",
    });
  }
};

export const POST = async (
  req: AuthenticatedMedusaRequest<PostAdminCreateSupplierOfferingCostHistoryType>,
  res: MedusaResponse
) => {
  try {
    const supplierProductsServicesService: SupplierProductsServicesModuleService =
      req.scope.resolve(SUPPLIER_PRODUCTS_SERVICES_MODULE);

    const { id } = req.params as { id: string };

    // Validate request body
    const validatedData = PostAdminCreateSupplierOfferingCostHistory.parse(
      req.body
    );

    // Ensure the supplier_offering_id matches the URL parameter
    const processedData = {
      ...validatedData,
      supplier_offering_id: id,
      changed_by_user_id:
        req.auth_context?.actor_id || validatedData.changed_by_user_id,
    };

    const costHistory =
      await supplierProductsServicesService.createSupplierOfferingCostHistory(
        processedData
      );

    res.status(201).json({
      cost_history: costHistory,
    });
  } catch (error) {
    console.error("Error creating cost history:", error);

    if (error.name === "ZodError") {
      return res.status(400).json({
        type: "invalid_data",
        message: "Invalid request data",
        details: error.errors,
      });
    }

    if (error.type === "not_found") {
      return res.status(404).json({
        type: "not_found",
        message: error.message,
      });
    }

    res.status(500).json({
      type: "server_error",
      message: "Internal server error",
    });
  }
};
