import {
  MedusaRequest,
  MedusaResponse,
} from "@camped-ai/framework/http";
import { ContainerRegistrationKeys } from "@camped-ai/framework/utils";
import { SUPPLIER_PRODUCTS_SERVICES_MODULE } from "src/modules/supplier-products-services";
import { SUPPLIER_MANAGEMENT_MODULE } from "src/modules/vendor_management";
import SupplierProductsServicesModuleService from "src/modules/supplier-products-services/service";
import SupplierManagementModuleService from "src/modules/vendor_management/supplier-service";
import * as ExcelJS from 'exceljs';

// Currency options matching the pricing calculator (same as useAdminCurrencies)
const CURRENCY_OPTIONS = [
  { value: "CHF", label: "CHF - Swiss Franc", symbol: "CHF" },
  { value: "EUR", label: "EUR - Euro", symbol: "€" },
  { value: "USD", label: "USD - US Dollar", symbol: "$" },
  { value: "GBP", label: "GBP - British Pound", symbol: "£" },
  { value: "JPY", label: "JPY - Japanese Yen", symbol: "¥" },
  { value: "CAD", label: "CAD - Canadian Dollar", symbol: "C$" },
  { value: "AUD", label: "AUD - Australian Dollar", symbol: "A$" },
  { value: "CNY", label: "CNY - Chinese Yuan", symbol: "¥" },
  { value: "INR", label: "INR - Indian Rupee", symbol: "₹" },
  { value: "SEK", label: "SEK - Swedish Krona", symbol: "kr" },
  { value: "NOK", label: "NOK - Norwegian Krone", symbol: "kr" },
  { value: "DKK", label: "DKK - Danish Krone", symbol: "kr" },
];

/**
 * GET /admin/supplier-management/supplier-offerings/template
 * Generate and download Excel template for supplier offerings import
 * Supports category_id parameter for category-specific templates
 */
export const GET = async (
  req: MedusaRequest,
  res: MedusaResponse
) => {
  try {
    console.log("🌐 GET /admin/supplier-management/supplier-offerings/template called");
    console.log("Query params:", req.query);

    // Get category_id from query params if provided (for category-specific template)
    const { category_id } = req.query;

    const supplierProductsServicesService: SupplierProductsServicesModuleService =
      req.scope.resolve(SUPPLIER_PRODUCTS_SERVICES_MODULE);
    const supplierManagementService: SupplierManagementModuleService =
      req.scope.resolve(SUPPLIER_MANAGEMENT_MODULE);

    // Fetch required data for template generation
    const [suppliersResult, productsServicesResult, categoriesResult, unitTypesResult, hotelsResult, destinationsResult] = await Promise.all([
      supplierManagementService.listSuppliers({}, { skip: 0, take: 100 }),
      // If category_id is provided, filter products/services by that category
      category_id
        ? supplierProductsServicesService.listProductServicesWithFiltersAndCount({ category_id: category_id as string }, { skip: 0, take: 100 })
        : supplierProductsServicesService.listProductServicesWithFiltersAndCount({}, { skip: 0, take: 100 }),
      // If category_id is provided, fetch only that category, otherwise fetch all
      category_id
        ? supplierProductsServicesService.retrieveCategory(category_id as string).then(cat => [cat])
        : supplierProductsServicesService.listCategories({}, { skip: 0, take: 100 }),
      supplierProductsServicesService.listUnitTypes({}, { skip: 0, take: 100 }),
      // Fetch hotels data directly from database using query service
      (async () => {
        try {
          const query = req.scope.resolve(ContainerRegistrationKeys.QUERY);
          const result = await query.graph({
            entity: "hotel",
            fields: ["id", "name", "destination_id", "is_active"],
            filters: { is_active: true },
            pagination: { skip: 0, take: 100 }
          });
          return { hotels: result.data || [] };
        } catch (err) {
          console.warn('Error fetching hotels from database for template generation:', err);
          return { hotels: [] };
        }
      })(),
      // Fetch destinations data directly from database using query service
      (async () => {
        try {
          const query = req.scope.resolve(ContainerRegistrationKeys.QUERY);
          const result = await query.graph({
            entity: "destination",
            fields: ["id", "name", "country", "is_active"],
            filters: { is_active: true },
            pagination: { skip: 0, take: 100 }
          });
          return { destinations: result.data || [] };
        } catch (err) {
          console.warn('Error fetching destinations from database for template generation:', err);
          return { destinations: [] };
        }
      })()
    ]);

    // Handle different response structures from different services
    const suppliers = (suppliersResult as any).suppliers || (suppliersResult as any).data || suppliersResult || [];

    // Handle products/services response which could be filtered by category
    const productsServices = category_id
      ? (productsServicesResult as any).data || [] // When filtered by category, it returns {data: [...]}
      : Array.isArray(productsServicesResult)
        ? productsServicesResult
        : (productsServicesResult as any).data || productsServicesResult || [];

    // Handle categories response which could be a single category or list
    const categories = category_id
      ? Array.isArray(categoriesResult) ? categoriesResult : [categoriesResult] // Single category case
      : Array.isArray(categoriesResult)
        ? categoriesResult
        : (categoriesResult as any).data || categoriesResult || [];

    const unitTypes = Array.isArray(unitTypesResult)
      ? unitTypesResult
      : (unitTypesResult as any).data || unitTypesResult || [];

    const hotels = hotelsResult.hotels || [];
    const destinations = destinationsResult.destinations || [];

    console.log(`Template data: ${suppliers.length} suppliers, ${productsServices.length} products/services, ${categories.length} categories`);

    // If category_id was provided but no matching category was found
    if (category_id && categories.length === 0) {
      return res.status(400).json({
        type: "not_found",
        message: `Category with ID ${category_id} not found`
      });
    }

    console.log("Data extracted:", {
      suppliers: suppliers.length,
      productsServices: productsServices.length,
      categories: categories.length,
      unitTypes: unitTypes.length,
      hotels: hotels.length,
      destinations: destinations.length
    });

    if (suppliers.length === 0 || productsServices.length === 0 || categories.length === 0) {
      return res.status(400).json({
        type: "insufficient_data",
        message: "Insufficient data: Please ensure you have suppliers, products/services, and categories created before generating template",
      });
    }

    // Create workbook using ExcelJS
    const workbook = new ExcelJS.Workbook();

    // Define core columns - only input fields, calculated fields will be auto-computed during import
    // Column names match the pricing calculator table exactly
    const coreColumns = [
      'supplier_name',
      'product_service_name',
      // Removed category_name as per requirements
      'active_from',
      'active_to',

      // Input pricing fields only (matching pricing calculator table structure)
      'cost_currency',                   // Cost Currency (input) - auto-populated from supplier
      'gross_cost',                      // Gross Cost (input) - was gross_price
      'commission',                      // Commission (%) (input)
      // 'net_cost' - REMOVED: Will be auto-calculated from gross_cost - (gross_cost * commission)
      'margin_rate',                     // Margin Rate (%) (input)
      // 'selling_price_cost_currency' - REMOVED: Will be auto-calculated from net_cost / (1 - margin_rate)
      // 'selling_price_default_currency' - REMOVED: Will be auto-calculated using exchange rates
      // NOTE: exchange_rate and selling_currency removed - not in pricing calculator

      'availability_notes',
      'status'
    ];

    // Debug: Log the core columns being included
    console.log("🔍 Template Debug - Core columns:", coreColumns);
    console.log("🔍 Template Debug - Input pricing fields only (matching pricing calculator):", [
      'cost_currency', 'gross_cost', 'commission', 'margin_rate'
    ]);
    console.log("🔍 Template Debug - Auto-calculated fields (removed from template):", [
      'net_cost', 'selling_price_cost_currency', 'selling_price_default_currency'
    ]);
    console.log("🔍 Template Debug - Cost currency dropdown with options:", CURRENCY_OPTIONS.map(c => c.value));

    // Collect all unique custom fields from all categories
    const allCustomFields = new Set<string>();
    const editableCustomFields = new Set<string>();
    const lockedCustomFields = new Set<string>();
    const customFieldSchemas = new Map<string, any>();

    categories.forEach((category: any) => {
      if (category.dynamic_field_schema && Array.isArray(category.dynamic_field_schema)) {
        const dynamicFields = category.dynamic_field_schema.filter((field: any) =>
          field.used_in_supplier_offering === true // Only show fields explicitly marked for supplier offerings
        );

        dynamicFields.forEach((field: any) => {
          const fieldKey = field.key; // Remove custom_field_ prefix for consistency

          // For range fields (number, date, time), create separate from and to columns
          if (field.type === 'number-range') {
            const fromFieldKey = `${fieldKey}_from`;
            const toFieldKey = `${fieldKey}_to`;

            allCustomFields.add(fromFieldKey);
            allCustomFields.add(toFieldKey);
            customFieldSchemas.set(fromFieldKey, { ...field, type: 'number' });
            customFieldSchemas.set(toFieldKey, { ...field, type: 'number' });

            // Separate locked and editable fields
            if (field.locked_in_offerings) {
              lockedCustomFields.add(fromFieldKey);
              lockedCustomFields.add(toFieldKey);
            } else {
              editableCustomFields.add(fromFieldKey);
              editableCustomFields.add(toFieldKey);
            }
          } else if (field.type === 'date_availability' || field.type === 'date-range') {
            // For date availability/range fields, create availability_from and availability_to columns
            const fromFieldKey = `${fieldKey}_from`;
            const toFieldKey = `${fieldKey}_to`;

            allCustomFields.add(fromFieldKey);
            allCustomFields.add(toFieldKey);
            customFieldSchemas.set(fromFieldKey, { ...field, type: 'date' });
            customFieldSchemas.set(toFieldKey, { ...field, type: 'date' });

            // Separate locked and editable fields
            if (field.locked_in_offerings) {
              lockedCustomFields.add(fromFieldKey);
              lockedCustomFields.add(toFieldKey);
            } else {
              editableCustomFields.add(fromFieldKey);
              editableCustomFields.add(toFieldKey);
            }
          } else if (field.type === 'time-range') {
            // For time range fields, create time_from and time_to columns
            const fromFieldKey = `${fieldKey}_from`;
            const toFieldKey = `${fieldKey}_to`;

            allCustomFields.add(fromFieldKey);
            allCustomFields.add(toFieldKey);
            customFieldSchemas.set(fromFieldKey, { ...field, type: 'time' });
            customFieldSchemas.set(toFieldKey, { ...field, type: 'time' });

            // Separate locked and editable fields
            if (field.locked_in_offerings) {
              lockedCustomFields.add(fromFieldKey);
              lockedCustomFields.add(toFieldKey);
            } else {
              editableCustomFields.add(fromFieldKey);
              editableCustomFields.add(toFieldKey);
            }
          } else {
            allCustomFields.add(fieldKey);
            customFieldSchemas.set(fieldKey, field);

            // Separate locked and editable fields
            if (field.locked_in_offerings) {
              lockedCustomFields.add(fieldKey);
            } else {
              editableCustomFields.add(fieldKey);
            }
          }
        });
      }
    });

    // Combine core columns with only editable custom fields for the main template
    const allColumns = [...coreColumns, ...Array.from(editableCustomFields).sort()];

    // Create main data worksheet with category-specific name if filtered
    const worksheetName = category_id && categories.length > 0
      ? `${categories[0].name.substring(0, 20)}_Offerings`.replace(/[^a-zA-Z0-9_]/g, '_')
      : 'Supplier_Offerings_Template';

    const mainWorksheet = workbook.addWorksheet(worksheetName);

    // Set up headers with proper formatting
    const headerRow = mainWorksheet.addRow(allColumns);
    headerRow.font = { bold: true, color: { argb: "FFFFFF" } };
    headerRow.fill = {
      type: "pattern",
      pattern: "solid",
      fgColor: { argb: "4472C4" },
    };

    // Set column widths
    allColumns.forEach((col, index) => {
      mainWorksheet.getColumn(index + 1).width = Math.max(col.length, 20);
    });

    // Generate sample data for the template (only 1 example row)
    // If category_id is provided, we already have filtered products/services
    const maxExamples = 1; // Generate only 1 sample row

    // If no products/services found for the category, show a message
    if (productsServices.length === 0 && category_id) {
      return res.status(400).json({
        type: "insufficient_data",
        message: `No products/services found for category ID ${category_id}. Please create products/services in this category first.`
      });
    }

    for (let i = 0; i < maxExamples; i++) {
      // Select random supplier and product/service for realistic example
      const randomSupplierIndex = Math.floor(Math.random() * suppliers.length);
      const randomProductIndex = Math.floor(Math.random() * productsServices.length);

      const supplier = suppliers[randomSupplierIndex];
      const productService = productsServices[randomProductIndex];

      // When category_id is provided, we know the category
      const category = category_id
        ? categories[0]
        : categories.find((c: any) => c.id === productService.category_id) || categories[0];

      // Create realistic sample data for input fields only (calculated fields will be auto-computed)
      const sampleGrossCost = 150 + Math.floor(Math.random() * 300); // Random cost between 150-450
      const sampleCommission = 10 + Math.floor(Math.random() * 15); // Random commission 10-24%
      const sampleMarginRate = 15 + Math.floor(Math.random() * 20); // Random margin 15-34%

      // Generate realistic random dates
      const startYear = 2024;
      const startMonth = Math.floor(Math.random() * 12) + 1; // 1-12
      const endMonth = startMonth + Math.floor(Math.random() * 6) + 3; // 3-8 months later
      const endYear = endMonth > 12 ? startYear + 1 : startYear;
      const finalEndMonth = endMonth > 12 ? endMonth - 12 : endMonth;

      const activeFrom = `${startYear}-${startMonth.toString().padStart(2, '0')}-01`;
      const activeTo = `${endYear}-${finalEndMonth.toString().padStart(2, '0')}-${Math.floor(Math.random() * 28) + 1}`;

      // Generate realistic availability notes
      const availabilityOptions = [
        `Available year-round with ${supplier.name}`,
        `Seasonal availability - contact ${supplier.name} for details`,
        `Premium ${productService.name} - advance booking recommended`,
        `Standard offering through ${supplier.name} partnership`,
        `High-demand service - limited availability during peak season`
      ];
      const randomAvailabilityNote = availabilityOptions[Math.floor(Math.random() * availabilityOptions.length)];

      const sampleData: Record<string, any> = {
        supplier_name: supplier.name,
        product_service_name: productService.name,
        // Removed category_name as per requirements
        active_from: activeFrom,
        active_to: activeTo,

        // Input pricing fields only (matching pricing calculator table structure)
        cost_currency: supplier.default_currency || 'CHF',                   // Cost Currency (auto-populated from supplier)
        gross_cost: sampleGrossCost.toFixed(2),                             // Gross Cost (input)
        commission: sampleCommission.toString(),                             // Commission (%) (input)
        // net_cost: REMOVED - Will be auto-calculated during import
        margin_rate: sampleMarginRate.toString(),                            // Margin Rate (%) (input)
        // selling_price_cost_currency: REMOVED - Will be auto-calculated during import
        // selling_price_default_currency: REMOVED - Will be auto-calculated during import
        // NOTE: exchange rates handled automatically by system

        availability_notes: randomAvailabilityNote,
        status: Math.random() > 0.8 ? 'inactive' : 'active' // 80% active, 20% inactive for realism
      };

      // Add custom fields based on category schema - only include fields used in supplier offerings
      if (category.dynamic_field_schema && Array.isArray(category.dynamic_field_schema)) {
        const dynamicFields = category.dynamic_field_schema.filter((field: any) =>
          field.used_in_supplier_offering === true
        );

        dynamicFields.forEach((field: any) => {
          const fieldKey = `custom_field_${field.key}`;

          // For range fields, create separate from and to columns
          if (field.type === 'number-range') {
            sampleData[`${fieldKey}_from`] = '10';
            sampleData[`${fieldKey}_to`] = '20';
          } else if (field.type === 'date_availability' || field.type === 'date-range') {
            // For date availability/range fields, create date samples
            sampleData[`${fieldKey}_from`] = '2024-06-01';
            sampleData[`${fieldKey}_to`] = '2024-09-30';
          } else if (field.type === 'time-range') {
            // For time range fields, create time samples
            sampleData[`${fieldKey}_from`] = '09:00';
            sampleData[`${fieldKey}_to`] = '17:00';
          } else {
            let value = '';

            switch (field.type) {
              case 'dropdown':
                value = field.options?.[0] || 'Option 1';
                break;
              case 'multi-select':
                value = field.options?.slice(0, 2).join(',') || 'Option 1,Option 2';
                break;
              case 'number':
                value = '10';
                break;
              case 'text':
                value = 'Sample text value';
                break;
              case 'boolean':
                value = 'true';
                break;
              case 'date':
                value = '2024-01-01';
                break;
              case 'time':
                value = '14:30';
                break;
              case 'hotels':
                value = hotels.length > 0
                  ? hotels.slice(0, 2).map((h: any) => h.name).join(',')
                  : 'Hotel Name 1,Hotel Name 2';
                break;
              case 'destinations':
                value = destinations.length > 0
                  ? destinations[0].name
                  : 'Destination Name';
                break;
              default:
                value = 'Sample value';
            }

            sampleData[fieldKey] = value;
          }
        });
      }

      // Add the sample data row
      const values = allColumns.map(col => sampleData[col] || '');
      const dataRow = mainWorksheet.addRow(values);
      dataRow.font = { color: { argb: "000000" } };
    }

    // Note: We always generate exactly 1 example row above, so no fallback needed

    // Add dropdown validations using ExcelJS approach
    // When category is filtered, only show products/services from that category
    const dropdownOptions = {
      supplier_name: suppliers.map((s: any) => s.name),
      product_service_name: productsServices.map((ps: any) => ps.name), // Already filtered by category if category_id provided
      // Removed category_name dropdown as per requirements
      status: ['active', 'inactive'],
      // Add cost_currency dropdown matching pricing calculator options
      cost_currency: CURRENCY_OPTIONS.map(option => option.value), // ["CHF", "EUR", "USD", "GBP", "JPY", "CAD", "AUD", "CNY", "INR", "SEK", "NOK", "DKK"]
    };

    console.log('Dropdown options:', {
      suppliers: dropdownOptions.supplier_name.length,
      productsServices: dropdownOptions.product_service_name.length,
      costCurrencies: dropdownOptions.cost_currency.length
    });

    // Create a validation data sheet for dropdown values
    const validationSheet = workbook.addWorksheet('ValidationData');
    validationSheet.state = 'hidden'; // Hide this sheet

    // Add validation data for dropdowns
    let validationRow = 1;

    // Process core dropdowns
    Object.entries(dropdownOptions).forEach(([columnName, options]) => {
      if (options.length > 0) {
        console.log(`Adding dropdown validation for ${columnName} with options:`, options);

        // Add a header for this validation list
        validationSheet.getCell(validationRow, 1).value = columnName;
        validationSheet.getCell(validationRow, 1).font = { bold: true };
        validationRow++;

        // Add the options
        options.forEach((option: string, index: number) => {
          validationSheet.getCell(validationRow + index, 1).value = option;
        });

        // Create a named range reference
        const rangeRef = `ValidationData!$A$${validationRow}:$A$${validationRow + options.length - 1}`;

        // Apply validation to the main sheet
        const columnIndex = allColumns.indexOf(columnName) + 1;
        if (columnIndex > 0) {
          // Apply to first 10 rows only (to avoid performance issues)
          for (let rowNum = 2; rowNum <= 10; rowNum++) {
            const cell = mainWorksheet.getCell(rowNum, columnIndex);
            cell.dataValidation = {
              type: 'list',
              allowBlank: true,
              formulae: [rangeRef]
            };
          }
        }

        validationRow += options.length + 1; // Add a blank row between lists
      }
    });

    // Process custom field dropdowns
    customFieldSchemas.forEach((fieldSchema, fieldKey) => {
      // Only process editable fields for dropdown validation
      if (editableCustomFields.has(fieldKey) && fieldSchema.type === 'dropdown' && fieldSchema.options && fieldSchema.options.length > 0) {
        console.log(`Adding dropdown validation for custom field ${fieldKey} with options:`, fieldSchema.options);

        // Add a header for this validation list
        validationSheet.getCell(validationRow, 1).value = fieldKey;
        validationSheet.getCell(validationRow, 1).font = { bold: true };
        validationRow++;

        // Add the options
        fieldSchema.options.forEach((option: string, index: number) => {
          validationSheet.getCell(validationRow + index, 1).value = option;
        });

        // Create a named range reference
        const rangeRef = `ValidationData!$A$${validationRow}:$A$${validationRow + fieldSchema.options.length - 1}`;

        // Apply validation to the main sheet
        const columnIndex = allColumns.indexOf(fieldKey) + 1;
        if (columnIndex > 0) {
          // Apply to first 10 rows only (to avoid performance issues)
          for (let rowNum = 2; rowNum <= 10; rowNum++) {
            const cell = mainWorksheet.getCell(rowNum, columnIndex);
            cell.dataValidation = {
              type: 'list',
              allowBlank: true,
              formulae: [rangeRef]
            };
          }
        }

        validationRow += fieldSchema.options.length + 1; // Add a blank row between lists
      }
    });

    // We'll use a different approach for reference sheets, so these variables are no longer needed
    // The reference sheets will be created using the multiSelectFieldsMap below

    // Create reference sheets for multi-select fields (following export template format)
    const multiSelectFieldsMap = new Map<string, any[]>();

    // Collect all multi-select fields from categories
    categories.forEach((category: any) => {
      const allDynamicFields = (category.dynamic_field_schema as unknown as any[]) || [];
      const dynamicFieldSchema = allDynamicFields.filter((field: any) =>
        field.used_in_product !== false
      );

      dynamicFieldSchema.forEach((field: any) => {
        const fieldKey = field.key || field.field_name;

        // Only include editable fields in reference sheets
        if (editableCustomFields.has(fieldKey)) {
          if (field.type === 'multi-select' && field.options && field.options.length > 0) {
            multiSelectFieldsMap.set(`${fieldKey}_options`, field.options);
          } else if (field.type === 'hotels' && hotels.length > 0) {
            multiSelectFieldsMap.set(`${fieldKey}_hotels`, hotels.map((hotel: any) => hotel.name));
          } else if (field.type === 'destinations' && destinations.length > 0) {
            multiSelectFieldsMap.set(`${fieldKey}_destinations`, destinations.map((destination: any) => destination.name));
          }
        }
      });
    });

    // Create reference sheets for multi-select fields (column format like export template)
    multiSelectFieldsMap.forEach((options, fieldKey) => {
      if (options.length > 0) {
        // Create reference sheet names matching export template format
        // Format the sheet name to be more readable (spaces instead of underscores)
        // and ensure it doesn't exceed Excel's 31 character limit
        const sheetName = `${fieldKey.replace(/_/g, ' ').substring(0, 27)} ref`;

        const referenceSheet = workbook.addWorksheet(sheetName);

        // Add data in column format (names listed vertically)
        options.forEach((option: any, index: number) => {
          const optionValue = typeof option === 'string' ? option : option.value || option.label;
          // Add each option in a new row (column format)
          referenceSheet.getCell(index + 1, 1).value = optionValue;
        });

        // Set column width
        referenceSheet.getColumn(1).width = 25;
      }
    });



    // Create instructions sheet
    const instructionsSheet = workbook.addWorksheet('Instructions');

    // Add instructions header
    const instructionsHeader = instructionsSheet.addRow(['Field', 'Description', 'Required', 'Valid Values']);
    instructionsHeader.font = { bold: true, color: { argb: "FFFFFF" } };
    instructionsHeader.fill = {
      type: "pattern",
      pattern: "solid",
      fgColor: { argb: "4472C4" },
    };

    // Add instructions for each field
    const categoryInfo = category_id && categories.length > 0
      ? ` (filtered for ${categories[0].name} category)`
      : '';

    const instructionsData = [
      {
        Field: 'supplier_name',
        Description: 'Name of the supplier offering the product/service',
        Required: 'Yes',
        'Valid Values': 'Select from dropdown list of available suppliers'
      },
      {
        Field: 'product_service_name',
        Description: `Name of the product or service being offered${categoryInfo}`,
        Required: 'Yes',
        'Valid Values': category_id
          ? `Select from dropdown list of products/services in ${categories[0].name} category`
          : 'Select from dropdown list of available products/services'
      },
      // Removed category_name field as per requirements
      {
        Field: 'active_from',
        Description: 'Start date when this offering becomes active',
        Required: 'No',
        'Valid Values': 'Date in YYYY-MM-DD format'
      },
      {
        Field: 'active_to',
        Description: 'End date when this offering expires',
        Required: 'No',
        'Valid Values': 'Date in YYYY-MM-DD format'
      },
      {
        Field: 'cost_currency',
        Description: 'Currency for the cost (defaults to supplier currency, can be overridden)',
        Required: 'Yes',
        'Valid Values': 'Select from dropdown: CHF, EUR, USD, GBP, JPY, CAD, AUD, CNY, INR, SEK, NOK, DKK'
      },
      {
        Field: 'gross_cost',
        Description: 'Gross cost before commission deduction',
        Required: 'Yes',
        'Valid Values': 'Numeric value (e.g., 300.00)'
      },
      {
        Field: 'commission',
        Description: 'Commission percentage',
        Required: 'No',
        'Valid Values': 'Numeric value (e.g., 15 for 15%)'
      },
      {
        Field: 'margin_rate',
        Description: 'Margin rate percentage',
        Required: 'Yes',
        'Valid Values': 'Numeric value (e.g., 25 for 25%)'
      },
      {
        Field: 'availability_notes',
        Description: 'Additional notes about availability or restrictions',
        Required: 'No',
        'Valid Values': 'Free text'
      },
      {
        Field: 'status',
        Description: 'Status of the offering',
        Required: 'Yes',
        'Valid Values': 'Select from dropdown: active, inactive'
      }
    ];

    // Add instructions for only editable custom fields
    customFieldSchemas.forEach((fieldSchema, fieldKey) => {
      // Only include editable fields in the main instructions
      if (!editableCustomFields.has(fieldKey)) {
        return;
      }
      let validValues = 'Free text';
      let description = fieldSchema.label || fieldSchema.key;

      // Handle range fields with separate from/to columns
      if (fieldKey.endsWith('_from') && fieldSchema.type === 'number') {
        // This is a number-range 'from' field
        description = `${description} (minimum value)`;
        validValues = 'Numeric value (minimum)';
      } else if (fieldKey.endsWith('_to') && fieldSchema.type === 'number') {
        // This is a number-range 'to' field
        description = `${description} (maximum value)`;
        validValues = 'Numeric value (maximum)';
      } else if (fieldKey.endsWith('_from') && fieldSchema.type === 'date') {
        // This is a date availability/range 'from' field
        description = `${description} (start date)`;
        validValues = 'Date in YYYY-MM-DD format';
      } else if (fieldKey.endsWith('_to') && fieldSchema.type === 'date') {
        // This is a date availability/range 'to' field
        description = `${description} (end date)`;
        validValues = 'Date in YYYY-MM-DD format';
      } else if (fieldKey.endsWith('_from') && fieldSchema.type === 'time') {
        // This is a time-range 'from' field
        description = `${description} (start time)`;
        validValues = 'Time in HH:MM format (24-hour)';
      } else if (fieldKey.endsWith('_to') && fieldSchema.type === 'time') {
        // This is a time-range 'to' field
        description = `${description} (end time)`;
        validValues = 'Time in HH:MM format (24-hour)';
      } else if (fieldSchema.type === 'dropdown' && fieldSchema.options) {
        validValues = `Select from dropdown: ${fieldSchema.options.join(', ')}`;
      } else if (fieldSchema.type === 'multi-select' && fieldSchema.options) {
        validValues = `Comma-separated values from: ${fieldSchema.options.join(', ')}`;
      } else if (fieldSchema.type === 'boolean') {
        validValues = 'true, false';
      } else if (fieldSchema.type === 'date') {
        validValues = 'Date in YYYY-MM-DD format';
      } else if (fieldSchema.type === 'time') {
        validValues = 'Time in HH:MM format (24-hour)';
      } else if (fieldSchema.type === 'number') {
        validValues = 'Numeric value';
      } else if (fieldSchema.type === 'number-range') {
        // This should not happen anymore as we're using separate from/to columns
        validValues = 'Numeric range (use separate from/to columns)';
      } else if (fieldSchema.type === 'hotels') {
        validValues = hotels.length > 0
          ? `Available hotels: ${hotels.map((h: any) => h.name).join(', ')} (comma-separated for multiple)`
          : 'Comma-separated hotel names (no hotels available)';
      } else if (fieldSchema.type === 'destinations') {
        validValues = destinations.length > 0
          ? `Available destinations: ${destinations.map((d: any) => d.name).join(', ')}`
          : 'Destination names (no destinations available)';
      }

      instructionsData.push({
        Field: fieldKey,
        Description: description,
        Required: fieldSchema.required ? 'Yes' : 'No',
        'Valid Values': validValues
      });
    });

    // Add instruction rows
    instructionsData.forEach((instruction: any) => {
      instructionsSheet.addRow([
        instruction.Field,
        instruction.Description,
        instruction.Required,
        instruction['Valid Values']
      ]);
    });

    // Add important note about currency auto-population
    const currencyNoteRow = instructionsSheet.addRow([
      'IMPORTANT NOTE',
      'Currency is automatically populated from supplier default currency',
      'N/A',
      'Currency cannot be overridden and will be inherited from the selected supplier'
    ]);
    currencyNoteRow.font = { bold: true, color: { argb: "0066CC" } };
    currencyNoteRow.fill = {
      type: "pattern",
      pattern: "solid",
      fgColor: { argb: "E3F2FD" }, // Light blue background
    };

    // Set column widths for instructions sheet
    instructionsSheet.getColumn(1).width = 25;
    instructionsSheet.getColumn(2).width = 40;
    instructionsSheet.getColumn(3).width = 10;
    instructionsSheet.getColumn(4).width = 50;

    // Create locked fields reference sheet if there are locked fields
    if (lockedCustomFields.size > 0) {
      const lockedFieldsSheet = workbook.addWorksheet('Locked_Fields_Reference');

      // Add header with styling
      const lockedHeaderRow = lockedFieldsSheet.addRow([
        'Field Name',
        'Example Inherited Value',
        'Description',
        'Note'
      ]);
      lockedHeaderRow.font = { bold: true, color: { argb: "FFFFFF" } };
      lockedHeaderRow.fill = {
        type: "pattern",
        pattern: "solid",
        fgColor: { argb: "DC3545" }, // Red background to indicate locked status
      };

      // Add warning note
      const warningRow = lockedFieldsSheet.addRow([
        'WARNING',
        'LOCKED FIELDS',
        'These fields are locked and values are inherited from products/services',
        'DO NOT EDIT - For reference only'
      ]);
      warningRow.font = { bold: true, color: { argb: "DC3545" } };
      warningRow.fill = {
        type: "pattern",
        pattern: "solid",
        fgColor: { argb: "FFF3CD" }, // Light yellow background
      };

      // Add locked field information with example inherited values
      Array.from(lockedCustomFields).forEach((fieldKey: string) => {
        const fieldSchema = customFieldSchemas.get(fieldKey);
        if (fieldSchema) {
          // Generate example inherited value based on field type
          let exampleValue = 'Example inherited value';
          switch (fieldSchema.type) {
            case 'number-range':
              exampleValue = '10 - 20';
              break;
            case 'number':
              exampleValue = fieldKey.endsWith('_from') ? '10' : fieldKey.endsWith('_to') ? '20' : '15';
              break;
            case 'date':
              exampleValue = fieldKey.endsWith('_from') ? '2024-06-01' : fieldKey.endsWith('_to') ? '2024-09-30' : '2024-01-15';
              break;
            case 'time':
              exampleValue = fieldKey.endsWith('_from') ? '09:00' : fieldKey.endsWith('_to') ? '17:00' : '14:30';
              break;
            case 'dropdown':
              exampleValue = fieldSchema.options?.[0] || 'Option 1';
              break;
            case 'multi-select':
              exampleValue = fieldSchema.options?.slice(0, 2).join(', ') || 'Option 1, Option 2';
              break;
            case 'boolean':
              exampleValue = 'Yes';
              break;
            case 'hotels':
              exampleValue = 'Hotel Example';
              break;
            case 'destinations':
              exampleValue = 'Destination Example';
              break;
            default:
              exampleValue = 'Sample text value';
          }

          lockedFieldsSheet.addRow([
            fieldSchema.label || fieldKey,
            exampleValue,
            'This field value is automatically inherited from the associated product/service',
            'Cannot be modified in supplier offerings'
          ]);
        }
      });

      // Set column widths for locked fields sheet
      lockedFieldsSheet.getColumn(1).width = 25;
      lockedFieldsSheet.getColumn(2).width = 15;
      lockedFieldsSheet.getColumn(3).width = 50;
      lockedFieldsSheet.getColumn(4).width = 30;
    }

    // Create Currency Reference sheet
    const currencyReferenceSheet = workbook.addWorksheet('Currency_Reference');

    // Add header for available currencies
    const currencyHeaderRow = currencyReferenceSheet.addRow([
      'Currency Code',
      'Currency Name',
      'Symbol'
    ]);
    currencyHeaderRow.font = { bold: true, color: { argb: "FFFFFF" } };
    currencyHeaderRow.fill = {
      type: "pattern",
      pattern: "solid",
      fgColor: { argb: "28A745" }, // Green background for reference sheet
    };

    // Add all available currencies from dropdown options
    CURRENCY_OPTIONS.forEach(currency => {
      currencyReferenceSheet.addRow([
        currency.value,
        currency.label,
        currency.symbol
      ]);
    });

    // Add explanatory note
    currencyReferenceSheet.addRow([]);
    const noteRow = currencyReferenceSheet.addRow([
      'NOTE: Cost Currency is selectable from dropdown in main sheet',
      'Default value is supplier\'s default currency, but can be overridden'
    ]);
    noteRow.font = { italic: true, color: { argb: "6C757D" } };

    // Add supplier default currencies section
    currencyReferenceSheet.addRow([]);
    const supplierHeaderRow = currencyReferenceSheet.addRow([
      'Supplier Name',
      'Default Currency'
    ]);
    supplierHeaderRow.font = { bold: true, color: { argb: "FFFFFF" } };
    supplierHeaderRow.fill = {
      type: "pattern",
      pattern: "solid",
      fgColor: { argb: "17A2B8" }, // Blue background for supplier section
    };

    // Add all suppliers with their default currencies
    suppliers.forEach(supplier => {
      currencyReferenceSheet.addRow([
        supplier.name,
        supplier.default_currency || 'CHF' // Default to CHF if not set
      ]);
    });

    // Set column widths for currency reference sheet
    currencyReferenceSheet.getColumn(1).width = 15; // Currency Code / Supplier Name
    currencyReferenceSheet.getColumn(2).width = 25; // Currency Name / Default Currency
    currencyReferenceSheet.getColumn(3).width = 10; // Symbol

    // Create Pricing Fields Instructions sheet
    const pricingInstructionsSheet = workbook.addWorksheet('Pricing_Fields_Instructions');

    // Add header for pricing instructions
    const pricingHeaderRow = pricingInstructionsSheet.addRow([
      'Field Name',
      'Type',
      'Description',
      'Format',
      'Example'
    ]);
    pricingHeaderRow.font = { bold: true, color: { argb: "FFFFFF" } };
    pricingHeaderRow.fill = {
      type: "pattern",
      pattern: "solid",
      fgColor: { argb: "4472C4" },
    };

    // Add pricing fields instructions matching pricing calculator table structure
    const pricingInstructions = [
      {
        field: 'cost_currency',
        type: 'DROPDOWN',
        description: 'Cost Currency - Select from dropdown list (defaults to supplier currency, can be overridden)',
        format: 'Select from dropdown: CHF, EUR, USD, GBP, JPY, CAD, AUD, CNY, INR, SEK, NOK, DKK',
        example: 'CHF'
      },
      {
        field: 'gross_cost',
        type: 'MANUAL INPUT',
        description: 'Gross Cost - The total cost before commission deduction',
        format: 'Decimal number',
        example: '300.00'
      },
      {
        field: 'commission',
        type: 'MANUAL INPUT',
        description: 'Commission (%) - Commission percentage (enter as number, e.g., 15 for 15%)',
        format: 'Number (percentage)',
        example: '15'
      },
      {
        field: 'margin_rate',
        type: 'MANUAL INPUT',
        description: 'Margin Rate (%) - Margin rate percentage (enter as number, e.g., 25 for 25%)',
        format: 'Number (percentage)',
        example: '25'
      }
    ];

    // Add auto-calculation note at the top
    pricingInstructionsSheet.addRow([
      'AUTO-CALCULATION',
      'SYSTEM FEATURE',
      'The following fields are automatically calculated during import and should NOT be included in your file:',
      'Auto-calculated',
      'net_cost, selling_price_cost_currency, selling_price_default_currency'
    ]);

    // Add formulas explanation
    pricingInstructionsSheet.addRow([
      'CALCULATION FORMULAS',
      'SYSTEM LOGIC',
      'net_cost = gross_cost - (gross_cost × commission); selling_price_cost_currency = net_cost ÷ (1 - margin_rate); selling_price_default_currency = converted using exchange rates',
      'Automatic',
      'Based on pricing calculator logic'
    ]);

    // Add empty row for separation
    pricingInstructionsSheet.addRow(['', '', '', '', '']);

    pricingInstructions.forEach(instruction => {
      const row = pricingInstructionsSheet.addRow([
        instruction.field,
        instruction.type,
        instruction.description,
        instruction.format,
        instruction.example
      ]);

      // Color code input vs calculated fields
      if (instruction.type === 'INPUT') {
        row.getCell(2).fill = {
          type: "pattern",
          pattern: "solid",
          fgColor: { argb: "E8F5E8" }, // Light green for input fields
        };
      } else {
        row.getCell(2).fill = {
          type: "pattern",
          pattern: "solid",
          fgColor: { argb: "FFF2CC" }, // Light yellow for calculated fields
        };
      }
    });

    // Add important notes
    pricingInstructionsSheet.addRow([]);
    const notesHeaderRow = pricingInstructionsSheet.addRow(['IMPORTANT NOTES']);
    notesHeaderRow.font = { bold: true, color: { argb: "DC3545" } };
    notesHeaderRow.fill = {
      type: "pattern",
      pattern: "solid",
      fgColor: { argb: "FFF3CD" },
    };

    const notes = [
      'PRICING FIELDS: Some fields are auto-calculated during import (see Auto-Calculation section)',
      'COST CURRENCY: Select from dropdown (defaults to supplier currency, can be overridden)',
      'Enter commission and margin_rate as numbers (e.g., 15 for 15%)',
      'Enter all monetary values as decimal numbers (e.g., 300.00)',
      'See Currency_Reference sheet for all available currencies and supplier defaults',
      'Auto-calculated fields: net_cost, selling_price_cost_currency, selling_price_default_currency',
      'System will NOT perform any automatic calculations during import',
      'Do NOT include a currency column in your data - it will be ignored'
    ];

    notes.forEach(note => {
      pricingInstructionsSheet.addRow(['', '', note]);
    });

    // Set column widths for pricing instructions sheet
    pricingInstructionsSheet.getColumn(1).width = 25;
    pricingInstructionsSheet.getColumn(2).width = 15;
    pricingInstructionsSheet.getColumn(3).width = 60;
    pricingInstructionsSheet.getColumn(4).width = 40;
    pricingInstructionsSheet.getColumn(5).width = 30;

    // Set response headers for file download
    const timestamp = new Date().toISOString().split('T')[0];
    const filename = category_id && categories.length > 0
      ? `supplier_offerings_${categories[0].name.replace(/[^a-zA-Z0-9]/g, '_')}_${timestamp}.xlsx`
      : `supplier_offerings_template_${timestamp}.xlsx`;

    res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);

    // Write the workbook to the response using ExcelJS
    try {
      await workbook.xlsx.write(res);
      console.log(`📤 Successfully generated supplier offerings template with dynamic dropdown values`);
    } catch (writeError) {
      console.error('Error writing Excel template file:', writeError);
      throw new Error('Failed to write Excel template file');
    }
  } catch (error) {
    console.error("💥 Template generation error:", error);

    return res.status(500).json({
      type: "server_error",
      message: error instanceof Error ? error.message : "Failed to generate template",
    });
  }
};
