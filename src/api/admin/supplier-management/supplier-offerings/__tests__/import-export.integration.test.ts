import {
  describe,
  it,
  expect,
  beforeEach,
  afterEach,
  jest,
} from "@jest/globals";
import request from "supertest";
import { medusaIntegrationTestRunner } from "@camped-ai/test-utils";

// Mock XLSX module
jest.mock("xlsx", () => ({
  utils: {
    json_to_sheet: jest.fn(() => ({})),
    book_new: jest.fn(() => ({})),
    book_append_sheet: jest.fn(),
    sheet_to_json: jest.fn(() => [
      {
        supplier_name: "Test Supplier",
        product_service_name: "Test Product",
        category_name: "Test Category",
        active_from: "2024-01-01",
        active_to: "2024-12-31",
        status: "Active",
      },
    ]),
  },
  write: jest.fn(() => Buffer.from("mock excel data")),
}));

medusaIntegrationTestRunner({
  testSuite: ({ getContainer, api }) => {
    describe("Supplier Offerings Import/Export API", () => {
      let container: any;

      beforeEach(async () => {
        container = getContainer();
      });

      describe("GET /admin/supplier-management/supplier-offerings/template", () => {
        it("should generate and return Excel template", async () => {
          // Mock service responses
          const mockSupplierService = {
            listSuppliers: jest.fn().mockResolvedValue({
              data: [
                { id: "sup_1", name: "Test Supplier 1" },
                { id: "sup_2", name: "Test Supplier 2" },
              ],
            }),
          };

          const mockProductsServicesService = {
            listProductServices: jest.fn().mockResolvedValue({
              data: [
                { id: "ps_1", name: "Test Product 1", category_id: "cat_1" },
                { id: "ps_2", name: "Test Service 1", category_id: "cat_2" },
              ],
            }),
            listCategories: jest.fn().mockResolvedValue({
              data: [
                {
                  id: "cat_1",
                  name: "Test Category 1",
                  dynamic_field_schema: [
                    {
                      key: "test_field",
                      label: "Test Field",
                      type: "text",
                      required: true,
                    },
                  ],
                },
                {
                  id: "cat_2",
                  name: "Test Category 2",
                  dynamic_field_schema: [],
                },
              ],
            }),
          };

          // Mock container resolution
          container.resolve = jest.fn((moduleName) => {
            if (moduleName === "supplier_management") {
              return mockSupplierService;
            }
            if (moduleName === "supplier_products_services") {
              return mockProductsServicesService;
            }
            return {};
          });

          const response = await api.get(
            "/admin/supplier-management/supplier-offerings/template"
          );

          expect(response.status).toBe(200);
          expect(response.headers["content-type"]).toContain(
            "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
          );
          expect(response.headers["content-disposition"]).toContain(
            "attachment"
          );
        });

        it("should return error when insufficient data", async () => {
          const mockSupplierService = {
            listSuppliers: jest.fn().mockResolvedValue({ data: [] }),
          };

          const mockProductsServicesService = {
            listProductServices: jest.fn().mockResolvedValue({ data: [] }),
            listCategories: jest.fn().mockResolvedValue({ data: [] }),
          };

          container.resolve = jest.fn((moduleName) => {
            if (moduleName === "supplier_management") {
              return mockSupplierService;
            }
            if (moduleName === "supplier_products_services") {
              return mockProductsServicesService;
            }
            return {};
          });

          const response = await api.get(
            "/admin/supplier-management/supplier-offerings/template"
          );

          expect(response.status).toBe(400);
          expect(response.body.type).toBe("insufficient_data");
        });
      });

      describe("POST /admin/supplier-management/supplier-offerings/import", () => {
        it("should import supplier offerings successfully", async () => {
          const mockService = {
            createSupplierOffering: jest.fn().mockResolvedValue({
              id: "so_1",
              supplier_id: "sup_1",
              product_service_id: "ps_1",
              status: "Active",
            }),
          };

          container.resolve = jest.fn(() => mockService);

          const importData = {
            supplier_offerings: [
              {
                supplier_id: "sup_1",
                product_service_id: "ps_1",
                active_from: "2024-01-01",
                active_to: "2024-12-31",
                status: "Active",
              },
            ],
          };

          const response = await api
            .post("/admin/supplier-management/supplier-offerings/import")
            .send(importData);

          expect(response.status).toBe(201);
          expect(response.body.success).toBe(true);
          expect(response.body.created).toBe(1);
          expect(response.body.errors).toHaveLength(0);
        });

        it("should handle validation errors", async () => {
          const response = await api
            .post("/admin/supplier-management/supplier-offerings/import")
            .send({
              supplier_offerings: [
                {
                  // Missing required fields
                  status: "Active",
                },
              ],
            });

          expect(response.status).toBe(400);
        });

        it("should handle partial import with errors", async () => {
          const mockService = {
            createSupplierOffering: jest
              .fn()
              .mockResolvedValueOnce({
                id: "so_1",
                supplier_id: "sup_1",
                product_service_id: "ps_1",
                status: "Active",
              })
              .mockRejectedValueOnce(new Error("Duplicate offering")),
          };

          container.resolve = jest.fn(() => mockService);

          const importData = {
            supplier_offerings: [
              {
                supplier_id: "sup_1",
                product_service_id: "ps_1",
                status: "Active",
              },
              {
                supplier_id: "sup_1",
                product_service_id: "ps_1",
                status: "Active",
              },
            ],
          };

          const response = await api
            .post("/admin/supplier-management/supplier-offerings/import")
            .send(importData);

          expect(response.status).toBe(207); // Multi-Status
          expect(response.body.created).toBe(1);
          expect(response.body.errors).toHaveLength(1);
        });
      });

      describe("GET /admin/supplier-management/supplier-offerings/export", () => {
        it("should export supplier offerings as Excel", async () => {
          const mockSupplierService = {
            listSuppliers: jest.fn().mockResolvedValue({
              data: [{ id: "sup_1", name: "Test Supplier" }],
            }),
          };

          const mockProductsServicesService = {
            listSupplierOfferingsWithFilters: jest.fn().mockResolvedValue({
              data: [
                {
                  id: "so_1",
                  supplier_id: "sup_1",
                  product_service_id: "ps_1",
                  status: "Active",
                  created_at: new Date(),
                  updated_at: new Date(),
                },
              ],
            }),
            listProductServices: jest.fn().mockResolvedValue({
              data: [
                {
                  id: "ps_1",
                  name: "Test Product",
                  category_id: "cat_1",
                  category: { name: "Test Category" },
                },
              ],
            }),
          };

          container.resolve = jest.fn((moduleName) => {
            if (moduleName === "supplier_management") {
              return mockSupplierService;
            }
            if (moduleName === "supplier_products_services") {
              return mockProductsServicesService;
            }
            return {};
          });

          const response = await api.get(
            "/admin/supplier-management/supplier-offerings/export?format=excel"
          );

          expect(response.status).toBe(200);
          expect(response.headers["content-type"]).toContain(
            "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
          );
        });

        it("should return 404 when no data found", async () => {
          const mockService = {
            listSupplierOfferingsWithFilters: jest
              .fn()
              .mockResolvedValue({ data: [] }),
          };

          container.resolve = jest.fn(() => mockService);

          const response = await api.get(
            "/admin/supplier-management/supplier-offerings/export"
          );

          expect(response.status).toBe(404);
          expect(response.body.type).toBe("no_data");
        });
      });
    });
  },
});
