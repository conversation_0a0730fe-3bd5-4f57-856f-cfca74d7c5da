import { MiddlewareRoute } from "@camped-ai/framework/http";
import { validateAndTransformBody } from "@camped-ai/framework/utils";
import { PostAdminImportSupplierOfferings } from "../validators";

export const adminSupplierOfferingsImportMiddlewares: MiddlewareRoute[] = [
  {
    method: ["POST"],
    matcher: "/admin/supplier-management/supplier-offerings/import",
    middlewares: [
      validateAndTransformBody(PostAdminImportSupplierOfferings),
    ],
  },
];
