import {
  AuthenticatedMedusaRequest,
  MedusaResponse,
} from "@camped-ai/framework/http";
import { z } from "zod";
import { SUPPLIER_PRODUCTS_SERVICES_MODULE } from "src/modules/supplier-products-services";
import SupplierProductsServicesModuleService from "src/modules/supplier-products-services/service";
import { PostAdminImportSupplierOfferings } from "../validators";
import {
  parseDateAsUTCStartOfDay,
  parseDateAsUTCEndOfDay,
} from "src/utils/date-utils";

// Valid currency options (same as template generation)
const VALID_CURRENCIES = [
  "CHF", "EUR", "USD", "GBP", "JPY", "CAD", "AUD", "CNY", "INR", "SEK", "NOK", "DKK"
];

/**
 * Validate currency selection
 */
function validateCurrency(currency: string): boolean {
  return VALID_CURRENCIES.includes(currency?.toUpperCase());
}

/**
 * Auto-calculate pricing fields using the same logic as the pricing calculator
 * This ensures consistency between manual entry and import
 * Matches the pricing calculator table structure exactly
 */
function calculatePricingFields(data: any): {
  net_cost?: number;
  selling_price?: number;
  selling_price_selling_currency?: number;
  // Map to database field names for compatibility
  gross_price?: number;
  currency?: string;
} {
  const result: any = {};

  try {
    // Parse input values (using new column names)
    const grossCost = typeof data.gross_cost === "string"
      ? parseFloat(data.gross_cost)
      : data.gross_cost;
    const commission = typeof data.commission === "string"
      ? parseFloat(data.commission) / 100  // Convert percentage to decimal
      : (data.commission ? data.commission / 100 : undefined);
    const marginRate = typeof data.margin_rate === "string"
      ? parseFloat(data.margin_rate) / 100  // Convert percentage to decimal
      : (data.margin_rate ? data.margin_rate / 100 : undefined);

    console.log(`🧮 Auto-calculation input (pricing calculator format):`, {
      grossCost, commission, marginRate, costCurrency: data.cost_currency
    });

    // Validate currency selection
    if (data.cost_currency && !validateCurrency(data.cost_currency)) {
      console.error(`❌ Invalid currency: ${data.cost_currency}. Must be one of: ${VALID_CURRENCIES.join(', ')}`);
      throw new Error(`Invalid currency: ${data.cost_currency}. Must be one of: ${VALID_CURRENCIES.join(', ')}`);
    }

    // Map gross_cost to gross_price for database compatibility
    if (grossCost !== undefined) {
      result.gross_price = grossCost;
    }

    // Map cost_currency to currency for database compatibility (normalize to uppercase)
    if (data.cost_currency) {
      result.currency = data.cost_currency.toUpperCase();
    }

    // Calculate Net Cost: Gross Cost - (Gross Cost × Commission)
    if (grossCost !== undefined && commission !== undefined &&
        !isNaN(grossCost) && !isNaN(commission) &&
        commission >= 0 && commission <= 1 && grossCost >= 0) {

      const differenceValue = grossCost * commission;
      result.net_cost = grossCost - differenceValue;

      console.log(`🧮 Net Cost calculation: ${grossCost} - (${grossCost} × ${commission}) = ${result.net_cost}`);
    }

    // Calculate Selling Price (Cost Currency): Net Cost ÷ (1 - Margin Rate)
    if (result.net_cost !== undefined && marginRate !== undefined &&
        !isNaN(marginRate) && marginRate >= 0 && marginRate < 1) {

      result.selling_price = result.net_cost / (1 - marginRate);

      console.log(`🧮 Selling Price (Cost Currency) calculation: ${result.net_cost} ÷ (1 - ${marginRate}) = ${result.selling_price}`);
    }

    // Note: Selling Price (Default Currency) will be calculated by the system using exchange rates
    // This matches the pricing calculator behavior where exchange rates are handled automatically

    console.log(`🧮 Auto-calculation result:`, result);

  } catch (error) {
    console.error(`❌ Error in auto-calculation:`, error);
    // Return empty object if calculation fails - let validation handle the errors
  }

  return result;
}

// Utility function to parse various date formats from Excel
const parseExcelDate = (dateValue: any): Date | null => {
  if (!dateValue || dateValue === "") return null;

  try {
    // If it's already a Date object
    if (dateValue instanceof Date) {
      return dateValue;
    }

    // If it's a number (Excel serial date)
    if (typeof dateValue === "number") {
      // Excel serial date to JavaScript date
      const excelEpoch = new Date(1900, 0, 1);
      return new Date(
        excelEpoch.getTime() + (dateValue - 2) * 24 * 60 * 60 * 1000
      );
    }

    // If it's a string, try various formats
    if (typeof dateValue === "string") {
      const trimmedValue = dateValue.trim();

      // Handle Excel date objects that have been converted to strings
      if (trimmedValue.includes("GMT") || trimmedValue.includes("Time")) {
        const date = new Date(trimmedValue);
        if (!isNaN(date.getTime())) {
          return date;
        }
      }

      // Try common date formats using UTC-safe parsing
      // ISO format (YYYY-MM-DD)
      const isoMatch = trimmedValue.match(/^(\d{4})-(\d{1,2})-(\d{1,2})$/);
      if (isoMatch) {
        const [, year, month, day] = isoMatch;
        // Use UTC constructor to avoid timezone issues - months are 0-based
        const date = new Date(
          Date.UTC(parseInt(year), parseInt(month) - 1, parseInt(day))
        );
        if (!isNaN(date.getTime())) {
          return date;
        }
      }

      // European format (DD/MM/YYYY)
      const euroMatch = trimmedValue.match(/^(\d{1,2})\/(\d{1,2})\/(\d{4})$/);
      if (euroMatch) {
        const [, day, month, year] = euroMatch;
        // Use UTC constructor to avoid timezone issues - months are 0-based
        const date = new Date(
          Date.UTC(parseInt(year), parseInt(month) - 1, parseInt(day))
        );
        if (!isNaN(date.getTime())) {
          return date;
        }
      }

      // Dot format (DD.MM.YYYY)
      const dotMatch = trimmedValue.match(/^(\d{1,2})\.(\d{1,2})\.(\d{4})$/);
      if (dotMatch) {
        const [, day, month, year] = dotMatch;
        // Use UTC constructor to avoid timezone issues - months are 0-based
        const date = new Date(
          Date.UTC(parseInt(year), parseInt(month) - 1, parseInt(day))
        );
        if (!isNaN(date.getTime())) {
          return date;
        }
      }

      // Fallback: try native Date parsing
      const fallbackDate = new Date(trimmedValue);
      if (!isNaN(fallbackDate.getTime())) {
        return fallbackDate;
      }
    }

    return null;
  } catch (error) {
    console.error("Error parsing date:", dateValue, error);
    return null;
  }
};

type PostAdminImportSupplierOfferingsType = z.infer<
  typeof PostAdminImportSupplierOfferings
>;

/**
 * POST /admin/supplier-management/supplier-offerings/import
 * Bulk import supplier offerings from CSV/Excel data
 */
export const POST = async (
  req: AuthenticatedMedusaRequest<PostAdminImportSupplierOfferingsType>,
  res: MedusaResponse
) => {
  try {
    // Handle both validated and raw body for debugging
    const requestBody = req.validatedBody || req.body;
    const { supplier_offerings } = requestBody;

    // Debug: Log received data
    console.log(
      "🔍 Backend Import Debug - Received supplier_offerings count:",
      supplier_offerings?.length
    );
    console.log(
      "🔍 Backend Import Debug - First offering data:",
      supplier_offerings?.[0]
    );

    if (
      !supplier_offerings ||
      !Array.isArray(supplier_offerings) ||
      supplier_offerings.length === 0
    ) {
      return res.status(400).json({
        type: "validation_error",
        message: "Supplier offerings array is required and must not be empty",
      });
    }

    const supplierProductsServicesService: SupplierProductsServicesModuleService =
      req.scope.resolve(SUPPLIER_PRODUCTS_SERVICES_MODULE);

    // Validate each supplier offering data
    const validationErrors: Array<{
      index: number;
      field: string;
      message: string;
      value?: any;
    }> = [];

    // Process each supplier offering
    const results = {
      created: 0,
      errors: [] as Array<{ index: number; message: string; data: any }>,
    };

    for (let i = 0; i < supplier_offerings.length; i++) {
      const offeringData = supplier_offerings[i];

      try {
        // Parse dates using timezone-safe UTC parsing
        let activeFromDate = null;
        let activeToDate = null;

        if (offeringData.active_from) {
          activeFromDate = parseDateAsUTCStartOfDay(offeringData.active_from);
        }

        if (offeringData.active_to) {
          activeToDate = parseDateAsUTCEndOfDay(offeringData.active_to);
        }

        // Auto-calculate pricing fields using the same logic as the pricing calculator
        const calculatedPricing = calculatePricingFields(offeringData);

        const processedData: any = {
          ...offeringData,
          ...calculatedPricing, // Merge auto-calculated fields
          active_from: activeFromDate,
          active_to: activeToDate,
          created_by: req.auth_context?.actor_id,
        };

        // Debug: Log processed data before sending to service
        console.log(
          `🔍 Backend Import Debug - Processing item ${i + 1}:`,
          processedData
        );
        console.log(`🔍 Backend Import Debug - Pricing fields being sent (pricing calculator format):`, {
          cost_currency: processedData.cost_currency,
          gross_cost: processedData.gross_cost,
          gross_price: processedData.gross_price, // AUTO-MAPPED from gross_cost
          commission: processedData.commission,
          net_cost: processedData.net_cost, // AUTO-CALCULATED
          margin_rate: processedData.margin_rate,
          selling_price: processedData.selling_price, // AUTO-CALCULATED (Cost Currency)
          currency: processedData.currency, // AUTO-MAPPED from cost_currency
        });

        // Create the supplier offering
        try {
          const result =
            await supplierProductsServicesService.createSupplierOffering(
              processedData
            );
          console.log(
            `🔍 Backend Import Debug - Successfully created offering:`,
            result.id
          );
        } catch (serviceError) {
          console.error(
            `🔍 Backend Import Debug - Service error for item ${i + 1}:`,
            serviceError
          );
          throw serviceError;
        }
        results.created++;
      } catch (error) {
        console.error(`❌ Failed to create supplier offering ${i + 1}:`, error);

        // Extract meaningful error message
        let errorMessage = "Unknown error occurred";
        if (error instanceof Error) {
          errorMessage = error.message;
        } else if (typeof error === "string") {
          errorMessage = error;
        }

        // Log the specific error for debugging
        if (errorMessage.includes("Date range conflict")) {
          console.error(`Date range conflict for row ${i + 1}:`, errorMessage);
        }

        results.errors.push({
          index: i + 1,
          message: errorMessage,
          data: offeringData,
        });
      }
    }

    // Prepare response
    const response = {
      success: results.errors.length === 0,
      message:
        results.errors.length === 0
          ? `Successfully imported ${results.created} supplier offerings`
          : `Imported ${results.created} supplier offerings with ${results.errors.length} errors`,
      created: results.created,
      errors: results.errors,
    };

    // Log specific error types for debugging
    if (results.errors.length > 0) {
      const dateConflictErrors = results.errors.filter(
        (err) =>
          err.message.includes("Date range conflict") ||
          err.message.includes("Validity period overlaps")
      );
    }

    if (results.errors.length > 0) {
      return res.status(207).json(response); // 207 Multi-Status for partial success
    }

    return res.status(201).json(response);
  } catch (error) {
    console.error("💥 Import supplier offerings error:", error);

    return res.status(500).json({
      type: "server_error",
      message:
        error instanceof Error
          ? error.message
          : "Internal server error occurred during import",
    });
  }
};
