import { MedusaRequest, MedusaResponse } from "@medusajs/medusa";
import { z } from "zod";

// Validation schema for query parameters
const AddonPricingQuerySchema = z.object({
  addon_ids: z.array(z.string()).min(1, "At least one addon ID is required"),
  supplier_id: z.string().optional(),
  activity_start_date: z.string().optional(),
  activity_end_date: z.string().optional(),
});

interface AddonPricingResponse {
  addon_id: string;
  name: string;
  supplier_name?: string;
  supplier_id?: string;

  // Complete supplier offering pricing data
  gross_price?: number;
  commission?: number;
  net_cost?: number;
  margin_rate?: number;
  selling_price?: number;
  currency?: string;
  selling_currency?: string;
  exchange_rate?: number;
  selling_price_selling_currency?: number;

  found: boolean;
  error?: string;
}

export const GET = async (req: MedusaRequest, res: MedusaResponse) => {
  try {
    // Parse query parameters
    const queryParams = req.query;
    console.log("🔍 API received query:", queryParams);
    console.log("🔍 Query keys:", Object.keys(queryParams));
    console.log("🔍 addon_ids[]:", queryParams["addon_ids[]"]);
    console.log("🔍 addon_ids:", queryParams.addon_ids);

    // Handle addon_ids[] array parameter
    let addonIds: string[] = [];
    if (queryParams["addon_ids[]"]) {
      addonIds = Array.isArray(queryParams["addon_ids[]"])
        ? (queryParams["addon_ids[]"] as string[])
        : [queryParams["addon_ids[]"] as string];
    } else if (queryParams.addon_ids) {
      // Also check for addon_ids without brackets
      addonIds = Array.isArray(queryParams.addon_ids)
        ? (queryParams.addon_ids as string[])
        : [queryParams.addon_ids as string];
    }

    console.log("📋 Parsed addon IDs:", addonIds);

    const queryData = {
      addon_ids: addonIds,
      supplier_id: queryParams.supplier_id as string,
      activity_start_date: queryParams.activity_start_date as string,
      activity_end_date: queryParams.activity_end_date as string,
    };

    console.log("🔧 Query data for validation:", queryData);

    const validatedQuery = AddonPricingQuerySchema.parse(queryData);

    const { addon_ids, supplier_id, activity_start_date, activity_end_date } =
      validatedQuery;

    console.log("✅ Validation passed, processing addon IDs:", addon_ids);

    // Get database connection
    const query = req.scope.resolve("query");
    const addonPricingResults: AddonPricingResponse[] = [];

    for (const addonId of addon_ids) {
      try {
        console.log(`🔍 Processing addon ID: ${addonId}`);

        // First, get the product service details
        const productServiceResult = await query.graph({
          entity: "product_service",
          fields: ["id", "name", "type"],
          filters: { id: addonId, status: "active" },
        });

        if (
          !productServiceResult.data ||
          productServiceResult.data.length === 0
        ) {
          console.log(`❌ Product service not found for ID: ${addonId}`);
          addonPricingResults.push({
            addon_id: addonId,
            name: `Unknown Addon (${addonId})`,
            found: false,
            error: "Product service not found",
          });
          continue;
        }

        const productService = productServiceResult.data[0];
        console.log(`✅ Found product service:`, productService);

        // Build supplier offering query filters
        let offeringFilters: any = {
          product_service_id: addonId,
          status: "active",
        };

        // Add date filtering if provided
        if (activity_start_date && activity_end_date) {
          offeringFilters.active_from = { $lte: activity_end_date };
          offeringFilters.active_to = { $gte: activity_start_date };
        }

        // Query supplier offerings (without supplier relation since it's not defined in the model)
        const offeringResult = await query.graph({
          entity: "supplier_offering",
          fields: [
            "id",
            "supplier_id",
            "gross_price",
            "commission",
            "net_cost",
            "margin_rate",
            "selling_price",
            "currency",
            "selling_currency",
            "exchange_rate",
            "selling_price_selling_currency",
            "active_from",
            "active_to",
          ],
          filters: offeringFilters,
          pagination: { take: 10 }, // Limit results
        });

        console.log(
          `🔍 Found ${
            offeringResult.data?.length || 0
          } supplier offerings for addon ${addonId}`
        );

        if (!offeringResult.data || offeringResult.data.length === 0) {
          // No supplier offering found - return addon info without pricing
          addonPricingResults.push({
            addon_id: addonId,
            name: productService.name,
            found: false,
          });
          continue;
        }

        // Sort offerings to prioritize same supplier if provided
        let offerings = offeringResult.data;
        if (supplier_id) {
          offerings = offerings.sort((a: any, b: any) => {
            if (a.supplier_id === supplier_id && b.supplier_id !== supplier_id)
              return -1;
            if (a.supplier_id !== supplier_id && b.supplier_id === supplier_id)
              return 1;
            return 0;
          });
        }

        // Use the first (best match) offering
        const offering = offerings[0];
        console.log(`✅ Selected offering:`, offering);

        // Get supplier name using service approach
        let supplierName: string | undefined;
        try {
          console.log(
            `🔍 Fetching supplier name for ID: ${offering.supplier_id}`
          );

          // Debug: Check what services are available
          console.log(
            `🔍 Available services in scope:`,
            Object.keys(req.scope.cradle)
          );

          // Try multiple service resolution patterns
          const servicePatterns = [
            "supplier_management", // This one is in the available services list!
            "vendorService",
            "vendor_managementService",
            "supplierService",
            "vendor-managementService",
            "vendorManagementService",
          ];

          let serviceFound = false;
          for (const serviceName of servicePatterns) {
            try {
              const service = req.scope.resolve(serviceName);
              if (service) {
                console.log(
                  `🔍 Found service ${serviceName}, checking methods:`,
                  Object.getOwnPropertyNames(service).filter(
                    (name) => typeof service[name] === "function"
                  )
                );

                // Try different method patterns
                const methodPatterns = [
                  "retrieveSupplier",
                  "getSupplier",
                  "retrieve",
                  "findSupplier",
                  "getSupplierById",
                  "findById",
                ];

                for (const methodName of methodPatterns) {
                  if (
                    service[methodName] &&
                    typeof service[methodName] === "function"
                  ) {
                    console.log(
                      `🔄 Using ${serviceName}.${methodName} to fetch supplier...`
                    );
                    const supplier = await service[methodName](
                      offering.supplier_id
                    );
                    supplierName = supplier?.name;
                    console.log(
                      `✅ Found supplier via ${serviceName}.${methodName}: ${supplierName}`
                    );
                    serviceFound = true;
                    break;
                  }
                }

                if (serviceFound) break;
              }
            } catch (serviceError) {
              console.log(`❌ ${serviceName} failed:`, serviceError.message);
            }
          }

          if (!serviceFound) {
            console.log(`🔄 No service found, trying direct query...`);
            // Fallback to direct query approach
            const supplierResult = await query.graph({
              entity: "supplier",
              fields: ["id", "name"],
              filters: { id: offering.supplier_id },
            });
            console.log(`📋 Direct query result:`, supplierResult);
            supplierName = supplierResult.data?.[0]?.name;
            console.log(`✅ Found supplier via query: ${supplierName}`);
          }
        } catch (error) {
          console.error(
            `❌ Could not fetch supplier name for ID: ${offering.supplier_id}`,
            error
          );

          // Final fallback: try with module-specific query
          try {
            console.log(`🔄 Trying module-specific supplier fetch...`);
            const moduleQuery = req.scope.resolve("query");
            const supplierResult = await moduleQuery.graph({
              entity: "vendor_management.supplier", // Try module-prefixed entity
              fields: ["id", "name"],
              filters: { id: offering.supplier_id },
            });
            supplierName = supplierResult.data?.[0]?.name;
            console.log(`✅ Module-specific fetch found: ${supplierName}`);
          } catch (moduleError) {
            console.error(`❌ Module-specific fetch also failed:`, moduleError);
            // Set a placeholder for now
            supplierName = `Supplier ${offering.supplier_id.slice(-6)}`;
            console.log(`🔄 Using placeholder name: ${supplierName}`);
          }
        }

        addonPricingResults.push({
          addon_id: addonId,
          name: productService.name,
          supplier_name: supplierName,
          supplier_id: offering.supplier_id,

          // Complete supplier offering pricing data (convert decimals to percentages for frontend)
          gross_price: offering.gross_price
            ? parseFloat(offering.gross_price)
            : undefined,
          commission: offering.commission
            ? parseFloat(offering.commission) * 100 // Convert decimal to percentage
            : undefined,
          net_cost: offering.net_cost
            ? parseFloat(offering.net_cost)
            : undefined,
          margin_rate: offering.margin_rate
            ? parseFloat(offering.margin_rate) * 100 // Convert decimal to percentage
            : undefined,
          selling_price: offering.selling_price
            ? parseFloat(offering.selling_price)
            : undefined,
          currency: offering.currency,
          selling_currency: offering.selling_currency,
          exchange_rate: offering.exchange_rate
            ? parseFloat(offering.exchange_rate)
            : undefined,
          selling_price_selling_currency:
            offering.selling_price_selling_currency
              ? parseFloat(offering.selling_price_selling_currency)
              : undefined,

          found: true,
        });
      } catch (error) {
        console.error(`❌ Error processing addon ${addonId}:`, error);
        addonPricingResults.push({
          addon_id: addonId,
          name: `Addon ${addonId}`,
          found: false,
          error: error instanceof Error ? error.message : "Unknown error",
        });
      }
    }

    console.log("📦 Returning addon pricing results:", addonPricingResults);

    res.json({
      addon_pricing: addonPricingResults,
    });
  } catch (error) {
    console.error("Error in addon pricing API:", error);

    if (error instanceof z.ZodError) {
      return res.status(400).json({
        message: "Invalid query parameters",
        errors: error.errors,
      });
    }

    res.status(500).json({
      message: "Internal server error",
      error: error instanceof Error ? error.message : "Unknown error",
    });
  }
};
