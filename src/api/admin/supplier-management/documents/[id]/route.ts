import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";
import { MedusaError } from "@camped-ai/framework/utils";
import { SUPPLIER_MANAGEMENT_MODULE } from "src/modules/vendor_management";

/**
 * @swagger
 * /admin/supplier-management/documents/{id}:
 *   get:
 *     summary: Get supplier document
 *     description: Retrieve a specific supplier document by ID
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: The document ID
 *     responses:
 *       200:
 *         description: Supplier document details
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 document:
 *                   type: object
 *                   properties:
 *                     id:
 *                       type: string
 *                     url:
 *                       type: string
 *                     rank:
 *                       type: number
 *                     metadata:
 *                       type: object
 *                     supplier_id:
 *                       type: string
 *                     created_at:
 *                       type: string
 *                     updated_at:
 *                       type: string
 */
export async function GET(req: MedusaRequest, res: MedusaResponse) {
  const supplierModuleService = req.scope.resolve(SUPPLIER_MANAGEMENT_MODULE);
  const { id: documentId } = req.params;

  try {
    const document = await supplierModuleService.retrieveSupplierDocument(
      documentId
    );

    res.status(200).json({
      supplier_document: document,
    });
  } catch (error) {
    throw new MedusaError(MedusaError.Types.NOT_FOUND, error.message);
  }
}

/**
 * @swagger
 * /admin/supplier-management/documents/{id}:
 *   put:
 *     summary: Update supplier document
 *     description: Update document metadata, rank, or URL
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: The document ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               url:
 *                 type: string
 *                 description: Document URL
 *               rank:
 *                 type: number
 *                 description: Document rank for ordering
 *               metadata:
 *                 type: object
 *                 description: Additional metadata
 *     responses:
 *       200:
 *         description: Document updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 document:
 *                   type: object
 */
export async function PUT(req: MedusaRequest, res: MedusaResponse) {
  const supplierModuleService = req.scope.resolve(SUPPLIER_MANAGEMENT_MODULE);
  const { id: documentId } = req.params;

  try {
    const document = await supplierModuleService.updateSupplierDocument(
      documentId,
      req.body
    );

    res.status(200).json({
      supplier_document: document,
    });
  } catch (error) {
    throw new MedusaError(MedusaError.Types.INVALID_DATA, error.message);
  }
}

/**
 * @swagger
 * /admin/supplier-management/documents/{id}:
 *   delete:
 *     summary: Delete supplier document
 *     description: Delete a supplier document
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: The document ID
 *     responses:
 *       200:
 *         description: Document deleted successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 */
export async function DELETE(req: MedusaRequest, res: MedusaResponse) {
  const supplierModuleService = req.scope.resolve(SUPPLIER_MANAGEMENT_MODULE);
  const { id: documentId } = req.params;

  try {
    await supplierModuleService.deleteSupplierDocument(documentId);

    res.status(200).json({
      message: "Document deleted successfully",
    });
  } catch (error) {
    throw new MedusaError(MedusaError.Types.INVALID_DATA, error.message);
  }
}
