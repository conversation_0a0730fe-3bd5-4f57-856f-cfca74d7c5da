import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";
import { ContainerRegistrationKeys } from "@camped-ai/framework/utils";

/**
 * GET /admin/supplier-management/dashboard/metrics
 * Get dashboard KPI metrics for supplier management
 */
export const GET = async (req: MedusaRequest, res: MedusaResponse) => {
  try {
    console.log(
      "📊 GET /admin/supplier-management/dashboard/metrics - Getting dashboard metrics"
    );

    // Get query service
    const query = req.scope.resolve(ContainerRegistrationKeys.QUERY);

    // Get active suppliers count (for KPI)
    const activeSuppliersResult = await query.graph({
      entity: "supplier",
      fields: ["id"],
      filters: { status: { $eq: "Active" } },
      pagination: { skip: 0, take: 1000 },
    });
    const activeSuppliersCount = activeSuppliersResult.metadata?.count || 0;

    // Get total suppliers count (for Core Functions)
    const totalSuppliersResult = await query.graph({
      entity: "supplier",
      fields: ["id"],
      pagination: { skip: 0, take: 1000 },
    });
    const totalSuppliersCount = totalSuppliersResult.metadata?.count || 0;

    // Get products & services count (total)
    const productsServicesResult = await query.graph({
      entity: "product_service",
      fields: ["id"],
      pagination: { skip: 0, take: 1000 },
    });
    const productsServicesCount = productsServicesResult.metadata?.count || 0;

    // Get active offerings count (for KPI)
    const activeOfferingsResult = await query.graph({
      entity: "supplier_offering",
      fields: ["id"],
      filters: { status: { $eq: "active" } },
      pagination: { skip: 0, take: 1000 },
    });
    const activeOfferingsCount = activeOfferingsResult.metadata?.count || 0;

    // Get total offerings count (for Core Functions)
    const totalOfferingsResult = await query.graph({
      entity: "supplier_offering",
      fields: ["id"],
      pagination: { skip: 0, take: 1000 },
    });
    const totalOfferingsCount = totalOfferingsResult.metadata?.count || 0;

    // Placeholder values for orders and contracts (coming soon)
    const pendingOrdersCount = 0;
    const contractsExpiringSoonCount = 0;

    const metrics = {
      // KPI metrics (for KPI cards)
      active_suppliers_count: activeSuppliersCount,
      products_services_count: productsServicesCount,
      active_offerings_count: activeOfferingsCount,
      pending_orders_count: pendingOrdersCount,
      contracts_expiring_soon_count: contractsExpiringSoonCount,

      // Total counts (for Core Functions)
      total_suppliers_count: totalSuppliersCount,
      total_offerings_count: totalOfferingsCount,

      last_updated: new Date().toISOString(),
    };

    console.log("📊 Dashboard metrics:", metrics);

    res.json({
      metrics,
      message: "Dashboard metrics retrieved successfully",
    });
  } catch (error) {
    console.error("Error fetching dashboard metrics:", error);
    res.status(500).json({
      error:
        error instanceof Error
          ? error.message
          : "Failed to fetch dashboard metrics",
    });
  }
};
