import {
  AuthenticatedMedusaRequest,
  MedusaResponse,
} from "@camped-ai/framework/http";
import { z } from "zod";
import { SUPPLIER_PRODUCTS_SERVICES_MODULE } from "src/modules/supplier-products-services";
import SupplierProductsServicesModuleService from "src/modules/supplier-products-services/service";

// Validation schema for bulk status update
const BulkStatusUpdateSchema = z.object({
  ids: z.array(z.string()).min(1, "At least one ID is required"),
  status: z.enum(["active", "inactive"], {
    errorMap: () => ({ message: "Status must be either 'active' or 'inactive'" }),
  }),
});

type BulkStatusUpdateType = z.infer<typeof BulkStatusUpdateSchema>;

/**
 * POST /admin/supplier-management/products-services/bulk-status
 * Bulk update status for multiple products/services
 */
export const POST = async (
  req: AuthenticatedMedusaRequest<BulkStatusUpdateType>,
  res: MedusaResponse
) => {
  try {
    console.log("🔄 POST /admin/supplier-management/products-services/bulk-status - Bulk updating status");
    console.log("Request body:", req.body);

    // Validate request body
    const validatedData = BulkStatusUpdateSchema.parse(req.body);
    const { ids, status } = validatedData;

    // Get supplier products services module
    const supplierProductsServicesService: SupplierProductsServicesModuleService =
      req.scope.resolve(SUPPLIER_PRODUCTS_SERVICES_MODULE);

    // Track results
    const results = [];
    const errors = [];
    let updatedCount = 0;

    // Update each product/service
    for (const id of ids) {
      try {
        console.log(`Updating product/service ${id} to status: ${status}`);
        
        // First, check if the product/service exists
        const existingProductService = await supplierProductsServicesService.retrieveProductService(id);
        
        if (!existingProductService) {
          errors.push({
            id,
            error: "Product/service not found",
          });
          continue;
        }

        // Update the status
        const updatedProductService = await supplierProductsServicesService.updateProductService(id, {
          status,
        });

        results.push({
          id,
          status: "success",
          previous_status: existingProductService.status,
          new_status: updatedProductService.status,
        });

        updatedCount++;
        console.log(`✅ Successfully updated product/service ${id} to ${status}`);
      } catch (error) {
        console.error(`❌ Failed to update product/service ${id}:`, error);
        errors.push({
          id,
          error: error instanceof Error ? error.message : "Unknown error",
        });
      }
    }

    // Prepare response
    const response = {
      message: `Bulk status update completed. ${updatedCount}/${ids.length} items updated successfully.`,
      updated_count: updatedCount,
      total_count: ids.length,
      error_count: errors.length,
      results,
      errors,
    };

    if (errors.length > 0) {
      console.log(`⚠️ Bulk status update completed with ${errors.length} errors`);
      return res.status(207).json(response); // 207 Multi-Status for partial success
    }

    console.log(`✅ Bulk status update completed successfully for all ${updatedCount} items`);
    return res.status(200).json(response);

  } catch (error) {
    console.error("❌ Bulk status update failed:", error);
    
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        type: "validation_error",
        message: "Invalid request data",
        errors: error.errors,
      });
    }

    return res.status(500).json({
      type: "internal_error",
      message: error instanceof Error ? error.message : "Failed to update product/service status",
    });
  }
};
