import {
  AuthenticatedMedusaRequest,
  MedusaResponse,
} from "@camped-ai/framework/http";
import { z } from "zod";
import { BulkImportCategoriesWorkflow } from "src/workflows/supplier-products-services/bulk-import-categories";

// Dynamic Field Schema validator
const DynamicFieldSchemaValidator = z.object({
  label: z.string().min(1, "Field label is required"),
  key: z
    .string()
    .min(1, "Field key is required")
    .regex(/^[a-z_]+$/, "Field key must be snake_case"),
  type: z.enum(
    [
      "text",
      "number",
      "dropdown",
      "multi-select",
      "date",
      "time",
      "time-range",
      "date-range",
      "boolean",
      "number-range",
      "hotels",
      "destinations",
      "addons",
    ],
    {
      required_error: "Field type is required",
      invalid_type_error:
        "Field type must be one of: text, number, dropdown, multi-select, date, time, time-range, date-range, boolean, number-range, hotels, destinations, addons",
    }
  ),
  options: z.array(z.string()).optional(),
  required: z.boolean(),
  used_in_filtering: z.boolean().optional(),
  used_in_supplier_offering: z.boolean().optional(),
  used_in_product: z.boolean().optional(),
  used_in_product_services: z.boolean().optional(),
  locked_in_offerings: z.boolean().optional(),
});

// Import category validator
const ImportCategoryValidator = z.object({
  name: z.string().min(1, "Name is required"),
  description: z.string().optional(),
  category_type: z
    .enum(["Product", "Service", "Both"])
    .optional()
    .default("Both"),
  icon: z.string().optional(),
  dynamic_field_schema: z.array(DynamicFieldSchemaValidator).optional(),
  is_active: z.boolean().optional().default(true),
});

const PostAdminImportCategories = z.object({
  categories: z
    .array(ImportCategoryValidator)
    .min(1, "At least one category is required"),
});

type PostAdminImportCategoriesType = z.infer<typeof PostAdminImportCategories>;

/**
 * POST /admin/supplier-management/products-services/categories/import
 * Bulk import categories from CSV/Excel data
 */
export const POST = async (
  req: AuthenticatedMedusaRequest<PostAdminImportCategoriesType>,
  res: MedusaResponse
) => {
  try {
    console.log(
      "🌐 POST /admin/supplier-management/products-services/categories/import called"
    );
    console.log("📥 Request body:", JSON.stringify(req.body, null, 2));

    const { categories } = req.body;

    if (!categories || !Array.isArray(categories) || categories.length === 0) {
      return res.status(400).json({
        type: "validation_error",
        message: "Categories array is required and must not be empty",
      });
    }

    // Validate each category data
    const validationErrors: Array<{
      index: number;
      field: string;
      message: string;
      value?: any;
    }> = [];

    const transformedCategories = categories
      .map((category, index) => {
        try {
          // Validate the category data
          const validatedCategory = ImportCategoryValidator.parse(category);

          // Additional validation for dynamic field schema
          if (validatedCategory.dynamic_field_schema) {
            validatedCategory.dynamic_field_schema.forEach(
              (field, fieldIndex) => {
                // Validate dropdown/multi-select options
                if (
                  (field.type === "dropdown" ||
                    field.type === "multi-select") &&
                  (!field.options || field.options.length === 0)
                ) {
                  validationErrors.push({
                    index,
                    field: `dynamic_field_schema[${fieldIndex}].options`,
                    message: `Options are required for ${field.type} field type`,
                    value: field.options,
                  });
                }

                // Hotels, destinations, and addons field types don't need options (they fetch from API)
                if (
                  (field.type === "hotels" ||
                    field.type === "destinations" ||
                    field.type === "addons") &&
                  field.options
                ) {
                  // Remove options if provided for these field types
                  delete field.options;
                }
              }
            );
          }

          return {
            name: validatedCategory.name.trim(),
            description: validatedCategory.description?.trim() || null,
            category_type: validatedCategory.category_type || "Both",
            icon: validatedCategory.icon?.trim() || null,
            dynamic_field_schema:
              validatedCategory.dynamic_field_schema || null,
            is_active: validatedCategory.is_active !== false,
          };
        } catch (error) {
          if (error instanceof z.ZodError) {
            error.errors.forEach((err) => {
              validationErrors.push({
                index,
                field: err.path.join("."),
                message: err.message,
                value:
                  err.code === "invalid_type"
                    ? category[err.path[0] as keyof typeof category]
                    : undefined,
              });
            });
          }
          return null;
        }
      })
      .filter(Boolean);

    if (validationErrors.length > 0) {
      return res.status(400).json({
        type: "validation_error",
        message: "Validation failed for some categories",
        errors: validationErrors,
      });
    }

    // Run the bulk import workflow
    console.log(
      "🚀 Running bulk import workflow with categories:",
      transformedCategories.length
    );
    console.log(
      "📋 Transformed categories data:",
      JSON.stringify(transformedCategories, null, 2)
    );

    let result;
    try {
      const workflowResult = await BulkImportCategoriesWorkflow(req.scope).run({
        input: { categories: transformedCategories },
      });
      result = workflowResult.result;

      console.log("✅ Workflow result:", JSON.stringify(result, null, 2));
    } catch (workflowError) {
      console.error("❌ Workflow execution failed:", workflowError);
      throw workflowError;
    }

    res.json({
      success: true,
      imported: result.imported || 0,
      errors: result.errors || [],
      message: `Successfully imported ${result.imported || 0} categories`,
      categories: result.categories || [],
    });
  } catch (error) {
    console.error("❌ Import categories error:", error);

    if (error instanceof z.ZodError) {
      return res.status(400).json({
        type: "validation_error",
        message: "Invalid request data",
        errors: error.errors,
      });
    }

    res.status(500).json({
      type: "server_error",
      message:
        error instanceof Error ? error.message : "Failed to import categories",
    });
  }
};
