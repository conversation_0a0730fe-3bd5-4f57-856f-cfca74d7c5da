import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";
import { SUPPLIER_PRODUCTS_SERVICES_MODULE } from "src/modules/supplier-products-services";
import SupplierProductsServicesModuleService from "src/modules/supplier-products-services/service";

/**
 * GET /admin/supplier-management/products-services/categories/{id}/usage-check
 * Check if a category can be safely inactivated by checking its usage
 */
export const GET = async (
  req: MedusaRequest,
  res: MedusaResponse
) => {
  try {
    const { id } = req.params;

    if (!id) {
      return res.status(400).json({
        message: "Category ID is required",
      });
    }

    const supplierProductsServicesService: SupplierProductsServicesModuleService = 
      req.scope.resolve(SUPPLIER_PRODUCTS_SERVICES_MODULE);

    // Check if category exists
    try {
      await supplierProductsServicesService.retrieveCategory(id);
    } catch (error) {
      return res.status(404).json({
        message: "Category not found",
      });
    }

    // Get all product services using this category
    const productServices = await supplierProductsServicesService.listProductServices({
      category_id: id,
    });

    // Count by type and status
    const activeProducts = productServices.filter((ps: any) => ps.type === "Product" && ps.status === "active");
    const activeServices = productServices.filter((ps: any) => ps.type === "Service" && ps.status === "active");
    const inactiveProducts = productServices.filter((ps: any) => ps.type === "Product" && ps.status === "inactive");
    const inactiveServices = productServices.filter((ps: any) => ps.type === "Service" && ps.status === "inactive");

    const totalActiveCount = activeProducts.length + activeServices.length;
    const totalInactiveCount = inactiveProducts.length + inactiveServices.length;
    const totalCount = productServices.length;

    // Check if category can be inactivated
    // Can inactivate if: no products/services OR all products/services are inactive
    const canInactivate = totalCount === 0 || totalActiveCount === 0;

    let message: string;
    if (totalCount === 0) {
      message = "This category is not currently being used and can be safely inactivated.";
    } else if (totalActiveCount === 0) {
      message = `This category has ${totalCount} associated item(s) but they are all inactive, so it can be safely inactivated.`;
    } else {
      message = `Cannot inactivate this category because it has ${totalActiveCount} active item(s) (${activeProducts.length} product(s), ${activeServices.length} service(s)). Please inactivate these items first before inactivating the category.`;
    }

    res.json({
      canInactivate,
      usage: {
        productCount: activeProducts.length + inactiveProducts.length,
        serviceCount: activeServices.length + inactiveServices.length,
        totalCount,
        activeProductCount: activeProducts.length,
        activeServiceCount: activeServices.length,
        activeCount: totalActiveCount,
        inactiveProductCount: inactiveProducts.length,
        inactiveServiceCount: inactiveServices.length,
        inactiveCount: totalInactiveCount,
      },
      message,
    });

  } catch (error) {
    console.error("Error checking category usage:", error);
    res.status(500).json({
      message: error instanceof Error ? error.message : "Failed to check category usage",
    });
  }
};
