import {
  AuthenticatedMedusaRequest,
  MedusaResponse,
} from "@camped-ai/framework/http";
import { z } from "zod";
import { BulkImportProductServicesWorkflow } from "src/workflows/supplier-products-services/bulk-import-product-services";

// Dynamic Field Schema validator
const DynamicFieldSchemaValidator = z.object({
  label: z.string().min(1, "Field label is required"),
  key: z
    .string()
    .min(1, "Field key is required")
    .regex(/^[a-z_]+$/, "Field key must be snake_case"),
  type: z.enum(
    [
      "text",
      "number",
      "dropdown",
      "multi-select",
      "date",
      "time",
      "time-range",
      "date-range",
      "boolean",
      "number-range",
      "hotels",
      "destinations",
      "addons",
    ],
    {
      required_error: "Field type is required",
      invalid_type_error:
        "Field type must be one of: text, number, dropdown, multi-select, date, time, time-range, date-range, boolean, number-range, hotels, destinations, addons",
    }
  ),
  options: z.array(z.string()).optional(),
  required: z.boolean(),
  used_in_filtering: z.boolean().optional(),
  used_in_supplier_offering: z.boolean().optional(),
  used_in_product: z.boolean().optional(),
  used_in_product_services: z.boolean().optional(),
  locked_in_offerings: z.boolean().optional(),
});

// Import product/service validator
const ImportProductServiceValidator = z.object({
  // Note: name will be auto-generated from category + required fields, so not included in validation
  description: z.string().optional(),
  base_cost: z.number().min(0, "Base cost must be non-negative").optional(),
  custom_fields: z.record(z.any()).optional(), // JSON object for dynamic field values
  status: z.enum(["active", "inactive"]).optional().default("active"),
  category_id: z.string().min(1, "Category ID is required"),
  unit_type_id: z.string().min(1, "Unit Type ID is required"),
  tag_ids: z.array(z.string()).optional(),
});

const PostAdminImportProductServices = z.object({
  product_services: z
    .array(ImportProductServiceValidator)
    .min(1, "At least one product/service is required"),
});

type PostAdminImportProductServicesType = z.infer<
  typeof PostAdminImportProductServices
>;

/**
 * POST /admin/supplier-management/products-services/import
 * Bulk import products/services from CSV/Excel data
 */
export const POST = async (
  req: AuthenticatedMedusaRequest<PostAdminImportProductServicesType>,
  res: MedusaResponse
) => {
  try {
    console.log(
      "🌐 POST /admin/supplier-management/products-services/import called"
    );
    console.log("📥 Request body:", JSON.stringify(req.body, null, 2));

    const { product_services } = req.body;

    if (
      !product_services ||
      !Array.isArray(product_services) ||
      product_services.length === 0
    ) {
      return res.status(400).json({
        type: "validation_error",
        message: "Product services array is required and must not be empty",
      });
    }

    // Validate each product/service data
    const validationErrors: Array<{
      index: number;
      field: string;
      message: string;
      value?: any;
    }> = [];

    const transformedProductServices = product_services
      .map((productService, index) => {
        try {
          // Validate the product/service data
          const validatedProductService =
            ImportProductServiceValidator.parse(productService);

          // Transform tag_ids from string to array if needed
          let tagIds: string[] = [];
          if (productService.tag_ids) {
            if (typeof productService.tag_ids === "string") {
              tagIds = productService.tag_ids
                .split(",")
                .map((id) => id.trim())
                .filter((id) => id);
            } else if (Array.isArray(productService.tag_ids)) {
              tagIds = productService.tag_ids;
            }
          }

          return {
            // Note: name will be auto-generated from category + required fields in the workflow
            description: validatedProductService.description?.trim() || null,
            base_cost: validatedProductService.base_cost || null,
            custom_fields: validatedProductService.custom_fields || null,
            status: validatedProductService.status || "active",
            category_id: validatedProductService.category_id,
            unit_type_id: validatedProductService.unit_type_id,
            tag_ids: tagIds.length > 0 ? tagIds : undefined,
          };
        } catch (error) {
          if (error instanceof z.ZodError) {
            error.errors.forEach((err) => {
              validationErrors.push({
                index,
                field: err.path.join("."),
                message: err.message,
                value:
                  err.code === "invalid_type"
                    ? productService[err.path[0] as keyof typeof productService]
                    : undefined,
              });
            });
          }
          return null;
        }
      })
      .filter(Boolean);

    if (validationErrors.length > 0) {
      return res.status(400).json({
        type: "validation_error",
        message: "Validation failed for some products/services",
        errors: validationErrors,
      });
    }

    // Run the bulk import workflow
    console.log(
      "🚀 Running bulk import workflow with products/services:",
      transformedProductServices.length
    );
    console.log(
      "📋 Transformed products/services data:",
      JSON.stringify(transformedProductServices, null, 2)
    );

    let result;
    try {
      const workflowResult = await BulkImportProductServicesWorkflow(
        req.scope
      ).run({
        input: { product_services: transformedProductServices },
      });
      result = workflowResult.result;

      console.log("✅ Workflow result:", JSON.stringify(result, null, 2));
    } catch (workflowError) {
      console.error("❌ Workflow execution failed:", workflowError);
      throw workflowError;
    }

    const totalProcessed = (result.imported || 0) + (result.updated || 0);
    const message =
      result.updated > 0
        ? `Successfully processed ${totalProcessed} products/services (${
            result.imported || 0
          } created, ${result.updated} updated)`
        : `Successfully imported ${result.imported || 0} products/services`;

    res.json({
      success: true,
      imported: result.imported || 0,
      updated: result.updated || 0,
      errors: result.errors || [],
      message,
      product_services: result.product_services || [],
    });
  } catch (error) {
    console.error("❌ Import products/services error:", error);

    if (error instanceof z.ZodError) {
      return res.status(400).json({
        type: "validation_error",
        message: "Invalid request data",
        errors: error.errors,
      });
    }

    res.status(500).json({
      type: "server_error",
      message:
        error instanceof Error
          ? error.message
          : "Failed to import products/services",
    });
  }
};
