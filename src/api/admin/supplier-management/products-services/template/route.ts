import {
  MedusaRequest,
  MedusaResponse,
} from "@camped-ai/framework/http";
import { ContainerRegistrationKeys } from "@camped-ai/framework/utils";
import { SUPPLIER_PRODUCTS_SERVICES_MODULE } from "src/modules/supplier-products-services";
import SupplierProductsServicesModuleService from "src/modules/supplier-products-services/service";
import * as ExcelJS from 'exceljs';

/**
 * GET /admin/supplier-management/products-services/template
 * Generate and download Excel template for products/services import
 * Each category gets its own sheet with category-specific custom fields
 */
export const GET = async (
  req: MedusaRequest,
  res: MedusaResponse
) => {
  try {

    const supplierProductsServicesService: SupplierProductsServicesModuleService = 
      req.scope.resolve(SUPPLIER_PRODUCTS_SERVICES_MODULE);

    // Get category_id from query params if provided (for single category template)
    const { category_id } = req.query;

    // Fetch required data for template generation (matching create page data fetching)
    const [categoriesResult, unitTypesResult, hotelsResult, destinationsResult] = await Promise.all([
      // If specific category_id is provided, fetch that category with full details
      category_id
        ? supplierProductsServicesService.retrieveCategory(category_id as string).then(cat => [cat])
        : supplierProductsServicesService.listCategories({ is_active: true }, { skip: 0, take: 100 }),
      // Fetch unit types from the same API endpoint used by the config page
      supplierProductsServicesService.listUnitTypes({ is_active: true }, { skip: 0, take: 100 }),
      // Fetch hotels data directly from database using query service
      (async () => {
        try {
          const query = req.scope.resolve(ContainerRegistrationKeys.QUERY);
          const result = await query.graph({
            entity: "hotel",
            fields: ["id", "name", "destination_id", "is_active"],
            filters: { is_active: true },
            pagination: { skip: 0, take: 100 }
          });
          return { hotels: result.data || [] };
        } catch (err) {
          console.warn('Error fetching hotels from database for template generation:', err);
          return { hotels: [] };
        }
      })(),
      // Fetch destinations data directly from database using query service
      (async () => {
        try {
          const query = req.scope.resolve(ContainerRegistrationKeys.QUERY);
          const result = await query.graph({
            entity: "destination",
            fields: ["id", "name", "country", "is_active"],
            filters: { is_active: true },
            pagination: { skip: 0, take: 100 }
          });
          return { destinations: result.data || [] };
        } catch (err) {
          console.warn('Error fetching destinations from database for template generation:', err);
          return { destinations: [] };
        }
      })()
    ]);

    const categories = Array.isArray(categoriesResult) ? categoriesResult : [];
    const unitTypes = Array.isArray(unitTypesResult) ? unitTypesResult : [];
    const hotels = hotelsResult?.hotels || [];
    const destinations = destinationsResult?.destinations || [];

    if (categories.length === 0) {
      return res.status(400).json({
        type: "insufficient_data",
        message: category_id
          ? `Category with ID ${category_id} not found`
          : "No categories found. Please create categories before generating template",
      });
    }

    // When category_id is provided, we already fetched the specific category
    // When no category_id is provided, we use all categories
    const targetCategories = categories;

    if (targetCategories.length === 0) {
      return res.status(400).json({
        type: "category_not_found",
        message: "Specified category not found",
      });
    }

    // Create workbook using ExcelJS
    const workbook = new ExcelJS.Workbook();

    // Create a sheet for each category
    for (const category of targetCategories) {
      const sheetName = category.name.substring(0, 31); // Excel sheet name limit

      const allDynamicFields = (category.dynamic_field_schema as unknown as any[]) || [];

      const dynamicFieldSchema = allDynamicFields.filter((field: any) => {
        const isSupplierContext = field.field_context === "supplier";
        const isUsedInProductServices = field.used_in_product_services === true;
        return isSupplierContext && isUsedInProductServices;
      });

      // Base columns that match the create form exactly
      const baseColumns = [
        'description',            // Description textarea
        'unit_type_name',         // Unit Type name (user-friendly dropdown)
        'base_cost',              // Base Cost input
        'status'                  // Status (active/inactive)
      ];

      // Add dynamic field columns based on category schema (only fields marked for product/services usage)
      // Use custom_field_ prefix to match frontend template generation
      const dynamicColumns: string[] = [];
      dynamicFieldSchema.forEach((field: any) => {
        const fieldKey = field.key || field.field_name;
        if(field.used_in_product_services !== true) return;
        if (field.type === 'number-range') {
          // For number-range fields, create separate from and to columns
          dynamicColumns.push(`custom_field_${fieldKey}_from`);
          dynamicColumns.push(`custom_field_${fieldKey}_to`);
        } else {
          dynamicColumns.push(`custom_field_${fieldKey}`);
        }
      });
      const allColumns = [...baseColumns, ...dynamicColumns];

      // Create sample data for this category (matching create form structure)
      const sampleData = {
        description: `Sample ${category.name.toLowerCase()} description`,
        unit_type_name: unitTypes[0]?.name || unitTypes[0] || '',
        base_cost: 50.00,
        status: 'active'
      };

      // Add sample values for dynamic fields (matching DynamicFieldRenderer types)
      // Use custom_field_ prefix to match column headers
      dynamicFieldSchema.forEach((field: any) => {
        const fieldKey = field.key || field.field_name;
        if(field.used_in_product_services !== true) return;
        switch (field.type) {
          case 'text':
            sampleData[`custom_field_${fieldKey}`] = `Sample ${field.label || fieldKey}`;
            break;
          case 'number':
            sampleData[`custom_field_${fieldKey}`] = 100;
            break;
          case 'boolean':
            sampleData[`custom_field_${fieldKey}`] = true;
            break;
          case 'dropdown':
            const options = field.options || [];
            sampleData[`custom_field_${fieldKey}`] = options[0] || 'Option 1';
            break;
          case 'multi-select':
            const multiOptions = field.options || [];
            sampleData[`custom_field_${fieldKey}`] = multiOptions.slice(0, 2).join(',');
            break;
          case 'date':
            sampleData[`custom_field_${fieldKey}`] = '2025-07-14';
            break;
          case 'number-range':
            // Create separate from and to columns for number-range fields
            sampleData[`custom_field_${fieldKey}_from`] = 10;
            sampleData[`custom_field_${fieldKey}_to`] = 20;
            break;
          case 'hotels':
            sampleData[`custom_field_${fieldKey}`] = hotels.length > 0
              ? hotels.slice(0, 2).map((h: any) => h.name).join(',')
              : 'hotel_name_1,hotel_name_2';
            break;
          case 'destinations':
            sampleData[`custom_field_${fieldKey}`] = destinations.length > 0
              ? destinations[0].name
              : 'destination_name_1';
            break;
          default:
            sampleData[fieldKey] = `Sample ${field.label || fieldKey}`;
        }
      });

      // Create worksheet
      const worksheet = workbook.addWorksheet(sheetName);

      // Set up headers with proper formatting
      const headerRow = worksheet.addRow(allColumns);
      headerRow.font = { bold: true, color: { argb: "FFFFFF" } };
      headerRow.fill = {
        type: "pattern",
        pattern: "solid",
        fgColor: { argb: "4472C4" },
      };

      // Set column widths
      allColumns.forEach((col, index) => {
        worksheet.getColumn(index + 1).width = Math.max(col.length, 20);
      });

      // Add the sample data row
      const values = allColumns.map(col => sampleData[col] || '');
      const dataRow = worksheet.addRow(values);
      dataRow.font = { color: { argb: "000000" } };

      // Add dropdown validations using ExcelJS approach (matching suppliers template)
      // Note: tags are excluded from dropdown as they are multi-select and should reference the Tags_Reference sheet
      const dropdownOptions = {
        status: ['active', 'inactive'],
        unit_type_name: unitTypes.map((ut: any) => ut.name || ut)
      };

      // Apply dropdown validation for base fields
      Object.entries(dropdownOptions).forEach(([fieldName, options]) => {
        const columnIndex = allColumns.indexOf(fieldName) + 1;
        if (columnIndex > 0 && options.length > 0) {
          const cleanOptions = options
            .filter(option => option && typeof option === 'string')
            .map(option => option.toString().trim())
            .filter(option => option.length > 0)
            .slice(0, 50);

          if (cleanOptions.length === 0) return;

          const optionsString = cleanOptions.join(',');
          if (optionsString.length <= 255) {
            for (let rowIndex = 2; rowIndex <= 1000; rowIndex++) {
              const cell = worksheet.getCell(rowIndex, columnIndex);
              try {
                cell.dataValidation = {
                  type: 'list',
                  allowBlank: true,
                  formulae: [`"${cleanOptions.join(',')}"`],
                  showErrorMessage: true,
                  errorStyle: 'error',
                  errorTitle: 'Invalid Value',
                  error: `Please select a value from the dropdown list`,
                  showInputMessage: true,
                  promptTitle: `Select ${fieldName.replace(/_/g, ' ')}`,
                  prompt: `Choose from available options`
                };
              } catch (validationError) {
                console.warn(`Failed to set validation for ${fieldName} at row ${rowIndex}:`, validationError);
              }
            }
          }
        }
      });

      // Custom field dropdown validations (dynamic from category schema)
      dynamicFieldSchema.forEach((field: any) => {
        const fieldKey = field.key || field.field_name;
        // Look for the prefixed column name in allColumns
        const prefixedFieldKey = `custom_field_${fieldKey}`;
        const columnIndex = allColumns.indexOf(prefixedFieldKey) + 1;

        if (columnIndex > 0) {
          let options: string[] = [];
          let useDropdown = true;

          if (field.type === 'dropdown' && field.options && field.options.length > 0) {
            options = field.options;
            useDropdown = true; // Single select - use dropdown
          } else if (field.type === 'multi-select' && field.options && field.options.length > 0) {
            options = field.options;
            useDropdown = false; // Multi-select - no dropdown, use reference sheet
          } else if (field.type === 'hotels' && hotels.length > 0) {
            options = hotels.map((hotel: any) => hotel.name);
            useDropdown = false; // Hotels - multi-select, no dropdown, use reference sheet
          } else if (field.type === 'destinations' && destinations.length > 0) {
            options = destinations.map((destination: any) => destination.name);
            useDropdown = false; // Destinations - multi-select, no dropdown, use reference sheet
          } else if (field.type === 'boolean') {
            options = ['true', 'false'];
            useDropdown = true; // Single select - use dropdown
          }

          // Only add dropdown validation for single-select fields
          if (options.length > 0 && useDropdown) {
            const cleanOptions = options
              .filter(option => option && typeof option === 'string')
              .map(option => option.toString().trim())
              .filter(option => option.length > 0)
              .slice(0, 50);

            if (cleanOptions.length > 0) {
              const optionsString = cleanOptions.join(',');
              if (optionsString.length <= 255) {
                for (let rowIndex = 2; rowIndex <= 1000; rowIndex++) {
                  const cell = worksheet.getCell(rowIndex, columnIndex);
                  try {
                    cell.dataValidation = {
                      type: 'list',
                      allowBlank: true,
                      formulae: [`"${cleanOptions.join(',')}"`],
                      showErrorMessage: true,
                      errorStyle: 'error',
                      errorTitle: 'Invalid Value',
                      error: `Please select a value from the dropdown list`,
                      showInputMessage: true,
                      promptTitle: `Select ${fieldKey.replace(/_/g, ' ')}`,
                      prompt: `Choose from available options`
                    };
                  } catch (validationError) {
                    console.warn(`Failed to set validation for ${fieldKey} at row ${rowIndex}:`, validationError);
                  }
                }
              }
            }
          }
        }
      });
    }

    // Add reference sheets for multi-select custom fields
    const multiSelectFields = new Map<string, string[]>();

    // Collect all multi-select fields from all categories
    targetCategories.forEach(category => {
      const allDynamicFields = (category.dynamic_field_schema as unknown as any[]) || [];
      const dynamicFieldSchema = allDynamicFields.filter((field: any) =>
        field.used_in_product !== false
      );

      dynamicFieldSchema.forEach((field: any) => {
        const fieldKey = field.key || field.field_name;
        if(field.used_in_product_services !== true) return;

        // Create reference sheets for multi-select fields
        if (field.type === 'multi-select' && field.options && field.options.length > 0) {
          multiSelectFields.set(`${fieldKey}_options`, field.options);
        } else if (field.type === 'hotels' && hotels.length > 0) {
          multiSelectFields.set(`${fieldKey}_hotels`, hotels.map((hotel: any) => hotel.name));
        } else if (field.type === 'destinations' && destinations.length > 0) {
          multiSelectFields.set(`${fieldKey}_destinations`, destinations.map((destination: any) => destination.name));
        }
      });
    });

    // Create reference sheets for multi-select fields
    multiSelectFields.forEach((options, fieldKey) => {
      if (options.length > 0) {
        // Ensure sheet name doesn't exceed Excel's 31 character limit
        const sheetName = `${fieldKey.replace(/_/g, '_').substring(0, 27)}_Ref`;
        const referenceSheet = workbook.addWorksheet(sheetName);

        const headers = ['name'];
        const headerRow = referenceSheet.addRow(headers);
        headerRow.font = { bold: true, color: { argb: "FFFFFF" } };
        headerRow.fill = {
          type: "pattern",
          pattern: "solid",
          fgColor: { argb: "4472C4" },
        };

        // Add instruction row
        const instructionRow = referenceSheet.addRow([`Available ${fieldKey.replace(/_/g, ' ')} options (use comma-separated for multiple)`]);
        instructionRow.font = { italic: true, color: { argb: '666666' } };

        // Add empty row for spacing
        referenceSheet.addRow(['']);

        // Add options
        options.forEach((option: string) => {
          referenceSheet.addRow([option]);
        });

        referenceSheet.getColumn(1).width = 25;
      }
    });

    // Create comprehensive instructions sheet with dynamic data
    const instructionsData = [
      { Field: 'category_name', Description: 'Category name (auto-filled, do not modify)', Required: 'Yes', 'Valid Values': 'Pre-filled category name' },
      { Field: 'description', Description: 'Product/Service description', Required: 'No', 'Valid Values': 'Any text' },
      {
        Field: 'unit_type_name',
        Description: 'Unit type name',
        Required: 'Yes',
        'Valid Values': `Available options: ${unitTypes.map((ut: any) => ut.name || ut).join(', ')} (dropdown available)`
      },
      { Field: 'base_cost', Description: 'Base cost in CHF', Required: 'No', 'Valid Values': 'Positive number' },
      { Field: 'status', Description: 'Product/Service status', Required: 'No', 'Valid Values': 'active, inactive (dropdown available)' }
    ];

    // Add custom field instructions with dropdown information
    if (targetCategories.length === 1) {
      const categoryFields = (targetCategories[0].dynamic_field_schema as unknown as any[]) || [];
      const productFields = categoryFields.filter((field: any) => field.used_in_product !== false);

      if (productFields.length > 0) {
        // Add separator for custom fields
        instructionsData.push({
          Field: '--- CUSTOM FIELDS ---',
          Description: `Category-specific fields for ${targetCategories[0].name}`,
          Required: '',
          'Valid Values': ''
        });
      }

      productFields.forEach((field: any) => {
        const fieldKey = field.key || field.field_name;
        let validValues = 'Any text';

        if (field.type === 'dropdown' && field.options) {
          validValues = `${field.options.join(', ')} (dropdown available)`;
        } else if (field.type === 'multi-select' && field.options) {
          const fieldKey = field.key || field.field_name;
          validValues = `Comma-separated from: ${field.options.join(', ')} (no dropdown - refer to ${fieldKey}_options_Ref sheet)`;
        } else if (field.type === 'boolean') {
          validValues = 'true, false (dropdown available)';
        } else if (field.type === 'number') {
          validValues = 'Number';
        } else if (field.type === 'date') {
          validValues = 'YYYY-MM-DD format';
        } else if (field.type === 'number-range') {
          // For number-range fields, add instructions for both from and to columns
          instructionsData.push({
            Field: `custom_field_${fieldKey}_from`,
            Description: `${field.label || fieldKey} - From Value (Custom Field)`,
            Required: field.required ? 'Yes' : 'No',
            'Valid Values': 'Number (minimum value of range)'
          });
          instructionsData.push({
            Field: `custom_field_${fieldKey}_to`,
            Description: `${field.label || fieldKey} - To Value (Custom Field)`,
            Required: field.required ? 'Yes' : 'No',
            'Valid Values': 'Number (maximum value of range)'
          });
          return; // Skip the default instruction addition below
        } else if (field.type === 'hotels') {
          const fieldKey = field.key || field.field_name;
          validValues = hotels.length > 0
            ? `Available hotels: ${hotels.map((h: any) => h.name).join(', ')} (no dropdown - refer to ${fieldKey}_hotels_Ref sheet, comma-separated for multiple)`
            : 'Comma-separated hotel names (no hotels available)';
        } else if (field.type === 'destinations') {
          const fieldKey = field.key || field.field_name;
          validValues = destinations.length > 0
            ? `Available destinations: ${destinations.map((d: any) => d.name).join(', ')} (no dropdown - refer to ${fieldKey}_destinations_Ref sheet, comma-separated for multiple)`
            : 'Comma-separated destination names (no destinations available)';
        }

        instructionsData.push({
          Field: `custom_field_${fieldKey}`,
          Description: `${field.label || fieldKey} (Custom Field)`,
          Required: field.required ? 'Yes' : 'No',
          'Valid Values': validValues
        });
      });
    }

    // Create instructions sheet using ExcelJS
    const instructionsSheet = workbook.addWorksheet('Instructions');
    const instructionHeaders = ['Field', 'Description', 'Required', 'Valid Values'];
    const instructionHeaderRow = instructionsSheet.addRow(instructionHeaders);
    instructionHeaderRow.font = { bold: true, color: { argb: "FFFFFF" } };
    instructionHeaderRow.fill = {
      type: "pattern",
      pattern: "solid",
      fgColor: { argb: "4472C4" },
    };

    instructionsData.forEach((instruction: any) => {
      instructionsSheet.addRow([
        instruction.Field,
        instruction.Description,
        instruction.Required,
        instruction['Valid Values']
      ]);
    });

    instructionsSheet.getColumn(1).width = 20;
    instructionsSheet.getColumn(2).width = 40;
    instructionsSheet.getColumn(3).width = 10;
    instructionsSheet.getColumn(4).width = 50;

    // Set response headers for file download
    const filename = category_id
      ? `products_services_template_${targetCategories[0].name}_${new Date().toISOString().split('T')[0]}.xlsx`
      : `products_services_template_${new Date().toISOString().split('T')[0]}.xlsx`;

    res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);

    // Write the workbook to the response using ExcelJS
    try {
      await workbook.xlsx.write(res);
    } catch (writeError) {
      console.error('Error writing Excel template file:', writeError);
      throw new Error('Failed to write Excel template file');
    }
  } catch (error) {
    console.error("💥 Template generation error:", error);
    res.status(500).json({
      type: "server_error",
      message: error instanceof Error ? error.message : "Failed to generate template",
    });
  }
};
