import { MedusaRequest, MedusaResponse, AuthenticatedMedusaRequest } from "@camped-ai/framework/http";
import { z } from "zod";
import { SUPPLIER_PRODUCTS_SERVICES_MODULE } from "src/modules/supplier-products-services";
import SupplierProductsServicesModuleService from "src/modules/supplier-products-services/service";
import { CreateProductServiceWorkflow } from "src/workflows/supplier-products-services/create-product-service";
import {
  PostAdminCreateProductService,
  GetAdminProductServicesQuery,
} from "./validators";

/**
 * Resolves hotel and destination IDs to names in custom_fields
 */
async function resolveCustomFieldNames(
  productService: any,
  scope: any
): Promise<any> {
  if (
    !productService.custom_fields ||
    !productService.category?.dynamic_field_schema
  ) {
    return productService;
  }

  const query = scope.resolve("query");
  const resolvedCustomFields = { ...productService.custom_fields };

  // Find hotel, destination, and addon fields in the category schema
  const hotelFields = productService.category.dynamic_field_schema.filter(
    (field: any) => field.type === "hotels"
  );
  const destinationFields = productService.category.dynamic_field_schema.filter(
    (field: any) => field.type === "destinations"
  );
  const addonFields = productService.category.dynamic_field_schema.filter(
    (field: any) => field.type === "addons"
  );

  // Resolve hotel names
  for (const field of hotelFields) {
    if (resolvedCustomFields[field.key]) {
      try {
        const hotelIds = Array.isArray(resolvedCustomFields[field.key])
          ? resolvedCustomFields[field.key]
          : [resolvedCustomFields[field.key]];

        const hotelNames: string[] = [];
        for (const hotelId of hotelIds) {
          try {
            const result = await query.graph({
              entity: "hotel",
              filters: { id: hotelId },
              fields: ["id", "name"],
            });

            if (result.data && result.data.length > 0) {
              hotelNames.push(result.data[0].name);
            } else {
              hotelNames.push(hotelId); // Fallback to ID
            }
          } catch (error) {
            console.warn(
              `Failed to resolve hotel name for ID ${hotelId}:`,
              error
            );
            hotelNames.push(hotelId); // Fallback to ID
          }
        }

        // Store both resolved names and original IDs
        resolvedCustomFields[field.key] = hotelIds;
        resolvedCustomFields[`${field.key}_names`] = hotelNames;
      } catch (error) {
        console.warn(
          `Error resolving hotel names for field ${field.key}:`,
          error
        );
      }
    }
  }

  // Resolve destination names
  for (const field of destinationFields) {
    if (resolvedCustomFields[field.key]) {
      try {
        const destinationIds = Array.isArray(resolvedCustomFields[field.key])
          ? resolvedCustomFields[field.key]
          : [resolvedCustomFields[field.key]];

        const destinationNames: string[] = [];
        for (const destinationId of destinationIds) {
          try {
            const result = await query.graph({
              entity: "destination",
              filters: { id: destinationId },
              fields: ["id", "name"],
            });

            if (result.data && result.data.length > 0) {
              destinationNames.push(result.data[0].name);
            } else {
              destinationNames.push(destinationId); // Fallback to ID
            }
          } catch (error) {
            console.warn(
              `Failed to resolve destination name for ID ${destinationId}:`,
              error
            );
            destinationNames.push(destinationId); // Fallback to ID
          }
        }

        // Store both resolved names and original IDs
        resolvedCustomFields[field.key] = destinationIds;
        resolvedCustomFields[`${field.key}_names`] = destinationNames;
      } catch (error) {
        console.warn(
          `Error resolving destination names for field ${field.key}:`,
          error
        );
      }
    }
  }

  // Resolve addon names
  for (const field of addonFields) {
    if (resolvedCustomFields[field.key]) {
      try {
        const addonIds = Array.isArray(resolvedCustomFields[field.key])
          ? resolvedCustomFields[field.key]
          : [resolvedCustomFields[field.key]];

        const addonNames: string[] = [];
        for (const addonId of addonIds) {
          try {
            const result = await query.graph({
              entity: "product_service",
              filters: { id: addonId },
              fields: ["id", "name"],
            });

            if (result.data && result.data.length > 0) {
              addonNames.push(result.data[0].name);
            } else {
              addonNames.push(addonId); // Fallback to ID
            }
          } catch (error) {
            console.warn(
              `Failed to resolve addon name for ID ${addonId}:`,
              error
            );
            addonNames.push(addonId); // Fallback to ID
          }
        }

        // Store both resolved names and original IDs
        resolvedCustomFields[field.key] = addonIds;
        resolvedCustomFields[`${field.key}_names`] = addonNames;
      } catch (error) {
        console.warn(
          `Error resolving addon names for field ${field.key}:`,
          error
        );
      }
    }
  }

  return {
    ...productService,
    custom_fields: resolvedCustomFields,
  };
}

type PostAdminCreateProductServiceType = z.infer<typeof PostAdminCreateProductService>;
type GetAdminProductServicesQueryType = z.infer<typeof GetAdminProductServicesQuery>;

export const POST = async (
  req: AuthenticatedMedusaRequest<PostAdminCreateProductServiceType>,
  res: MedusaResponse
) => {
  try {
    console.log("🔍 POST /admin/supplier-management/products-services");
    console.log("Request body:", req.body);
    console.log("Validated body:", req.validatedBody);

    // Use validatedBody from middleware instead of req.body
    const inputData = req.validatedBody || req.body;

    const { result } = await CreateProductServiceWorkflow(req.scope).run({
      input: inputData,
    });

    console.log("Product/Service created successfully via workflow:", result);
    res.status(201).json({ product_service: result });
  } catch (error) {
    console.error("❌ Error creating product/service:", error);

    // Handle MedusaError types properly
    if (error && typeof error === 'object' && '__isMedusaError' in error) {
      const medusaError = error as any;
      return res.status(400).json({
        message: medusaError.message,
        type: medusaError.type,
        __isMedusaError: true,
      });
    }

    // Handle regular errors
    res.status(400).json({
      message: error instanceof Error ? error.message : "Failed to create product/service",
    });
  }
};

export const GET = async (
  req: MedusaRequest<{}, GetAdminProductServicesQueryType>,
  res: MedusaResponse
) => {
  try {
    const supplierProductsServicesService: SupplierProductsServicesModuleService = 
      req.scope.resolve(SUPPLIER_PRODUCTS_SERVICES_MODULE);

    // Parse query parameters
    const limit = parseInt(req.query.limit as string) || 25;
    const offset = parseInt(req.query.offset as string) || 0;

    // Build filters
    const filters: any = {};

    // Handle search query (case-insensitive) - prioritize over name filter
    if (req.query.q) {
      filters.search = req.query.q;
    } else if (req.query.name) {
      // Only use name filter if no search query is present
      filters.name = req.query.name;
    }

    if (req.query.type) {
      filters.type = req.query.type;
    }

    if (req.query.status) {
      filters.status = req.query.status;
    }

    if (req.query.category_id) {
      filters.category_id = req.query.category_id;
    }

    if (req.query.unit_type_id) {
      filters.unit_type_id = req.query.unit_type_id;
    }

    if (req.query.tag_ids) {
      // Handle comma-separated tag IDs
      const tagIds = Array.isArray(req.query.tag_ids)
        ? req.query.tag_ids
        : (req.query.tag_ids as string).split(',');
      filters.tag_ids = tagIds;
    }

    if (req.query.service_level) {
      filters.service_level = req.query.service_level;
    }

    if (req.query.hotel_id) {
      filters.hotel_id = req.query.hotel_id;
    }

    if (req.query.destination_id) {
      filters.destination_id = req.query.destination_id;
    }

    // Add sorting parameters
    const sort_by = (req.query.sort_by as string) || "updated_at";
    const sort_order = (req.query.sort_order as string) || "desc";

    const result = await supplierProductsServicesService.listProductServicesWithFiltersAndCount(
      filters,
      { skip: offset, take: limit, sort_by, sort_order }
    );

    // Resolve custom field names for each product service
    console.log(`🔍 Resolving custom field names for ${result.data.length} product services`);
    const resolvedProductServices = await Promise.all(
      result.data.map(async (productService: any) => {
        try {
          console.log(`🔍 Resolving names for product service: ${productService.id}`);
          const resolved = await resolveCustomFieldNames(productService, req.scope);
          console.log(`✅ Resolved names for ${productService.id}:`, resolved.custom_fields);
          return resolved;
        } catch (error) {
          console.warn(
            `Failed to resolve custom field names for product service ${productService.id}:`,
            error
          );
          return productService; // Return original if resolution fails
        }
      })
    );

    res.json({
      product_services: resolvedProductServices,
      count: result.count,
      limit: result.limit,
      offset: result.offset,
    });
  } catch (error) {
    res.status(500).json({
      message: error instanceof Error ? error.message : "Failed to list products/services",
    });
  }
};
