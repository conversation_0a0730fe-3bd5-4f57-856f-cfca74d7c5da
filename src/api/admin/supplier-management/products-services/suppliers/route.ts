import { MedusaRequest, MedusaResponse } from "@medusajs/medusa";
import { z } from "zod";
import { SUPPLIER_PRODUCTS_SERVICES_MODULE } from "../../../../../modules/supplier-products-services";

// Validation schemas
const createProductServiceSupplierSchema = z.object({
  product_service_id: z.string(),
  supplier_id: z.string(),
  cost: z.number().positive(),
  currency_code: z.string().optional().default("CHF"),
  availability: z.string().min(1),
  max_capacity: z.number().positive().optional(),
  season: z.string().optional(),
  valid_from: z.string().datetime().optional(),
  valid_until: z.string().datetime().optional(),
  notes: z.string().optional(),
  lead_time_days: z.number().positive().optional(),
  minimum_order: z.number().positive().optional(),
  is_active: z.boolean().optional().default(true),
  is_preferred: z.boolean().optional().default(false),
});

const listProductServiceSuppliersSchema = z.object({
  product_service_id: z.string().optional(),
  supplier_id: z.string().optional(),
  is_active: z.boolean().optional(),
  is_preferred: z.boolean().optional(),
  season: z.string().optional(),
  limit: z.number().positive().max(100).optional().default(25),
  offset: z.number().min(0).optional().default(0),
});

export const GET = async (req: MedusaRequest, res: MedusaResponse) => {
  try {
    const supplierProductsServicesModuleService = req.scope.resolve(
      SUPPLIER_PRODUCTS_SERVICES_MODULE
    );

    // Validate query parameters
    const validatedQuery = listProductServiceSuppliersSchema.parse(req.query);

    const result = await supplierProductsServicesModuleService.listProductServiceSuppliers(
      validatedQuery
    );

    res.status(200).json({
      product_service_suppliers: result.data,
      count: result.count,
      limit: result.limit,
      offset: result.offset,
    });
  } catch (error) {
    console.error("Error listing product service suppliers:", error);
    
    if (error.name === "ZodError") {
      return res.status(400).json({
        type: "invalid_data",
        message: "Invalid query parameters",
        details: error.errors,
      });
    }

    res.status(500).json({
      type: "server_error",
      message: error.message || "Failed to list product service suppliers",
    });
  }
};

export const POST = async (req: MedusaRequest, res: MedusaResponse) => {
  try {
    const supplierProductsServicesModuleService = req.scope.resolve(
      SUPPLIER_PRODUCTS_SERVICES_MODULE
    );

    // Validate request body
    const validatedData = createProductServiceSupplierSchema.parse(req.body);

    // Convert date strings to Date objects if provided
    const processedData = {
      ...validatedData,
      valid_from: validatedData.valid_from ? new Date(validatedData.valid_from) : undefined,
      valid_until: validatedData.valid_until ? new Date(validatedData.valid_until) : undefined,
    };

    const productServiceSupplier = await supplierProductsServicesModuleService.createProductServiceSupplier(
      processedData
    );

    res.status(201).json({
      product_service_supplier: productServiceSupplier,
    });
  } catch (error) {
    console.error("Error creating product service supplier:", error);
    
    if (error.name === "ZodError") {
      return res.status(400).json({
        type: "invalid_data",
        message: "Invalid request data",
        details: error.errors,
      });
    }

    if (error.type === "duplicate_error") {
      return res.status(409).json({
        type: "duplicate_error",
        message: error.message,
      });
    }

    if (error.type === "not_found") {
      return res.status(404).json({
        type: "not_found",
        message: error.message,
      });
    }

    res.status(500).json({
      type: "server_error",
      message: error.message || "Failed to create product service supplier",
    });
  }
};
