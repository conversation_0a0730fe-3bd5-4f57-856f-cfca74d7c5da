import { MedusaRequest, MedusaResponse } from "@medusajs/medusa";
import { z } from "zod";
import { SUPPLIER_PRODUCTS_SERVICES_MODULE } from "../../../../../../modules/supplier-products-services";

// Validation schema for updates
const updateProductServiceSupplierSchema = z.object({
  cost: z.number().positive().optional(),
  currency_code: z.string().optional(),
  availability: z.string().min(1).optional(),
  max_capacity: z.number().positive().optional(),
  season: z.string().optional(),
  valid_from: z.string().datetime().optional(),
  valid_until: z.string().datetime().optional(),
  notes: z.string().optional(),
  lead_time_days: z.number().positive().optional(),
  minimum_order: z.number().positive().optional(),
  is_active: z.boolean().optional(),
  is_preferred: z.boolean().optional(),
});

export const GET = async (req: MedusaRequest, res: MedusaResponse) => {
  try {
    const supplierProductsServicesModuleService = req.scope.resolve(
      SUPPLIER_PRODUCTS_SERVICES_MODULE
    );

    const { id } = req.params;

    if (!id) {
      return res.status(400).json({
        type: "invalid_data",
        message: "Product service supplier ID is required",
      });
    }

    const productServiceSupplier = await supplierProductsServicesModuleService.getProductServiceSupplier(id);

    res.status(200).json({
      product_service_supplier: productServiceSupplier,
    });
  } catch (error) {
    console.error("Error getting product service supplier:", error);
    
    if (error.type === "not_found") {
      return res.status(404).json({
        type: "not_found",
        message: error.message,
      });
    }

    res.status(500).json({
      type: "server_error",
      message: error.message || "Failed to get product service supplier",
    });
  }
};

export const PUT = async (req: MedusaRequest, res: MedusaResponse) => {
  try {
    const supplierProductsServicesModuleService = req.scope.resolve(
      SUPPLIER_PRODUCTS_SERVICES_MODULE
    );

    const { id } = req.params;

    if (!id) {
      return res.status(400).json({
        type: "invalid_data",
        message: "Product service supplier ID is required",
      });
    }

    // Validate request body
    const validatedData = updateProductServiceSupplierSchema.parse(req.body);

    // Convert date strings to Date objects if provided
    const processedData = {
      ...validatedData,
      valid_from: validatedData.valid_from ? new Date(validatedData.valid_from) : undefined,
      valid_until: validatedData.valid_until ? new Date(validatedData.valid_until) : undefined,
    };

    const productServiceSupplier = await supplierProductsServicesModuleService.updateProductServiceSupplier(
      id,
      processedData
    );

    res.status(200).json({
      product_service_supplier: productServiceSupplier,
    });
  } catch (error) {
    console.error("Error updating product service supplier:", error);
    
    if (error.name === "ZodError") {
      return res.status(400).json({
        type: "invalid_data",
        message: "Invalid request data",
        details: error.errors,
      });
    }

    if (error.type === "not_found") {
      return res.status(404).json({
        type: "not_found",
        message: error.message,
      });
    }

    res.status(500).json({
      type: "server_error",
      message: error.message || "Failed to update product service supplier",
    });
  }
};

export const DELETE = async (req: MedusaRequest, res: MedusaResponse) => {
  try {
    const supplierProductsServicesModuleService = req.scope.resolve(
      SUPPLIER_PRODUCTS_SERVICES_MODULE
    );

    const { id } = req.params;

    if (!id) {
      return res.status(400).json({
        type: "invalid_data",
        message: "Product service supplier ID is required",
      });
    }

    const result = await supplierProductsServicesModuleService.deleteProductServiceSupplier(id);

    res.status(200).json(result);
  } catch (error) {
    console.error("Error deleting product service supplier:", error);
    
    if (error.type === "not_found") {
      return res.status(404).json({
        type: "not_found",
        message: error.message,
      });
    }

    res.status(500).json({
      type: "server_error",
      message: error.message || "Failed to delete product service supplier",
    });
  }
};
