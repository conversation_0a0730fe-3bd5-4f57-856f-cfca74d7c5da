import { MedusaRequest, MedusaResponse } from "@medusajs/medusa";
import { z } from "zod";
import { SUPPLIER_PRODUCTS_SERVICES_MODULE } from "../../../../../../modules/supplier-products-services";
import { withClient } from "../../../../../../utils/db";

// Validation schema for query parameters
const listSuppliersForProductSchema = z.object({
  is_active: z.boolean().optional(),
  is_preferred: z.boolean().optional(),
  season: z.string().optional(),
  limit: z.number().positive().max(100).optional().default(25),
  offset: z.number().min(0).optional().default(0),
});

export const GET = async (req: MedusaRequest, res: MedusaResponse) => {
  try {
    const { id: productServiceId } = req.params;

    if (!productServiceId) {
      return res.status(400).json({
        type: "invalid_data",
        message: "Product service ID is required",
      });
    }

    // Validate query parameters
    const validatedQuery = listSuppliersForProductSchema.parse(req.query);

    console.log("🔍 API Route - Getting suppliers for product service:", productServiceId);
    console.log("🔍 API Route - Query params:", validatedQuery);

    // Use direct database query to get product service suppliers
    const result = await withClient(async (client) => {
      // Build SQL query conditions
      const conditions: string[] = ['pss.deleted_at IS NULL', 'pss.product_service_id = $1'];
      const params: any[] = [productServiceId];
      let paramIndex = 2;

      if (validatedQuery.is_active !== undefined) {
        conditions.push(`pss.is_active = $${paramIndex}`);
        params.push(validatedQuery.is_active);
        paramIndex++;
      }
      if (validatedQuery.is_preferred !== undefined) {
        conditions.push(`pss.is_preferred = $${paramIndex}`);
        params.push(validatedQuery.is_preferred);
        paramIndex++;
      }
      if (validatedQuery.season) {
        conditions.push(`pss.season = $${paramIndex}`);
        params.push(validatedQuery.season);
        paramIndex++;
      }

      const whereClause = `WHERE ${conditions.join(' AND ')}`;

      // Query to get the product service suppliers with supplier details
      const query = `
        SELECT
          pss.*,
          s.name as supplier_name,
          s.handle as supplier_handle,
          s.status as supplier_status,
          s.type as supplier_type,
          s.primary_contact_email,
          s.primary_contact_phone,
          s.website,
          s.address
        FROM "product_service_supplier" pss
        LEFT JOIN "supplier" s ON pss.supplier_id = s.id
        ${whereClause}
        ORDER BY pss.created_at DESC
        LIMIT $${paramIndex} OFFSET $${paramIndex + 1}
      `;

      params.push(validatedQuery.limit, validatedQuery.offset);

      console.log("🔍 Final SQL query:", query);
      console.log("🔍 Query params:", params);

      const queryResult = await client.query(query, params);

      console.log("🔍 Raw database result:", { rowCount: queryResult.rowCount, rows: queryResult.rows });

      return {
        data: queryResult.rows,
        count: queryResult.rowCount || 0,
        limit: validatedQuery.limit,
        offset: validatedQuery.offset,
      };
    });

    console.log("🔍 API Route - Result:", { count: result.count, dataLength: result.data.length });

    res.status(200).json({
      product_service_suppliers: result.data,
      count: result.count,
      limit: result.limit,
      offset: result.offset,
    });
  } catch (error) {
    console.error("Error getting suppliers for product service:", error);

    if (error.name === "ZodError") {
      return res.status(400).json({
        type: "invalid_data",
        message: "Invalid query parameters",
        details: error.errors,
      });
    }

    if (error.type === "not_found") {
      return res.status(404).json({
        type: "not_found",
        message: error.message,
      });
    }

    res.status(500).json({
      type: "server_error",
      message: error.message || "Failed to get suppliers for product service",
    });
  }
};
