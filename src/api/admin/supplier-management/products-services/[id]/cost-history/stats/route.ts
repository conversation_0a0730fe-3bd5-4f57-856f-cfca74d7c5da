import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";
import { SUPPLIER_PRODUCTS_SERVICES_MODULE } from "src/modules/supplier-products-services";
import SupplierProductsServicesModuleService from "src/modules/supplier-products-services/service";

export const GET = async (req: MedusaRequest, res: MedusaResponse) => {
  try {
    const supplierProductsServicesService: SupplierProductsServicesModuleService =
      req.scope.resolve(SUPPLIER_PRODUCTS_SERVICES_MODULE);

    const { id } = req.params as { id: string };

    const stats = await supplierProductsServicesService.getProductServiceCostHistoryStats(id);

    res.json({
      stats,
    });
  } catch (error) {
    console.error("Error fetching product service cost history stats:", error);
    res.status(500).json({
      message: error instanceof Error ? error.message : "Internal server error",
    });
  }
};
