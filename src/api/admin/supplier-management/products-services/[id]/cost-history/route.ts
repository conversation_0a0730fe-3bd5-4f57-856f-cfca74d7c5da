import {
  MedusaRequest,
  MedusaResponse,
  AuthenticatedMedusaRequest,
} from "@camped-ai/framework/http";
import { z } from "zod";
import { SUPPLIER_PRODUCTS_SERVICES_MODULE } from "src/modules/supplier-products-services";
import SupplierProductsServicesModuleService from "src/modules/supplier-products-services/service";
import {
  GetAdminProductServiceCostHistoryQuery,
  PostAdminCreateProductServiceCostHistory,
} from "./validators";

type GetAdminProductServiceCostHistoryQueryType = z.infer<
  typeof GetAdminProductServiceCostHistoryQuery
>;
type PostAdminCreateProductServiceCostHistoryType = z.infer<
  typeof PostAdminCreateProductServiceCostHistory
>;

export const GET = async (
  req: MedusaRequest<{}, GetAdminProductServiceCostHistoryQueryType>,
  res: MedusaResponse
) => {
  try {
    const supplierProductsServicesService: SupplierProductsServicesModuleService =
      req.scope.resolve(SUPPLIER_PRODUCTS_SERVICES_MODULE);

    const { id } = req.params as { id: string };

    // Parse query parameters
    const limit = parseInt(req.query.limit as string) || 25;
    const offset = parseInt(req.query.offset as string) || 0;

    // Build filters
    const filters: any = {
      product_service_id: id,
      limit,
      offset,
    };

    if (req.query.changed_by_user_id) {
      filters.changed_by_user_id = req.query.changed_by_user_id;
    }

    if (req.query.date_from) {
      filters.date_from = new Date(req.query.date_from);
    }

    if (req.query.date_to) {
      filters.date_to = new Date(req.query.date_to);
    }

    if (req.query.has_cost_change) {
      filters.has_cost_change = req.query.has_cost_change === "true";
    }

    const result = await supplierProductsServicesService.listProductServiceCostHistoryWithFilters(
      filters
    );

    res.json({
      cost_history: result.data,
      count: result.count,
      limit: result.limit,
      offset: result.offset,
    });
  } catch (error) {
    console.error("Error fetching product service cost history:", error);
    res.status(500).json({
      message: error instanceof Error ? error.message : "Internal server error",
    });
  }
};

export const POST = async (
  req: AuthenticatedMedusaRequest<PostAdminCreateProductServiceCostHistoryType>,
  res: MedusaResponse
) => {
  try {
    const supplierProductsServicesService: SupplierProductsServicesModuleService =
      req.scope.resolve(SUPPLIER_PRODUCTS_SERVICES_MODULE);

    const { id } = req.params as { id: string };

    // Validate request body
    const validatedData = PostAdminCreateProductServiceCostHistory.parse(
      req.body
    );

    // Ensure the product_service_id matches the URL parameter
    const processedData = {
      ...validatedData,
      product_service_id: id,
      changed_by_user_id:
        req.auth_context?.actor_id || validatedData.changed_by_user_id,
    };

    const costHistory =
      await supplierProductsServicesService.createProductServiceCostHistory(
        processedData
      );

    res.status(201).json({
      cost_history: costHistory,
    });
  } catch (error) {
    console.error("Error creating product service cost history:", error);

    if (error.message?.includes("not found")) {
      return res.status(404).json({
        message: error.message,
      });
    }

    if (error.message?.includes("validation")) {
      return res.status(400).json({
        message: error.message,
      });
    }

    res.status(500).json({
      message: error instanceof Error ? error.message : "Internal server error",
    });
  }
};
