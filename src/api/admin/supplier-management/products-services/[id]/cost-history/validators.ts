import { z } from "zod";

// Get Product Service Cost History Query Validation
export const GetAdminProductServiceCostHistoryQuery = z.object({
  limit: z.string().optional(),
  offset: z.string().optional(),
  changed_by_user_id: z.string().optional(),
  date_from: z.string().optional(),
  date_to: z.string().optional(),
  has_cost_change: z.enum(["true", "false"]).optional(),
});

// Create Product Service Cost History Validation
export const PostAdminCreateProductServiceCostHistory = z.object({
  product_service_id: z.string().min(1, "Product service ID is required"),
  previous_cost: z.number().min(0, "Previous cost must be non-negative").optional(),
  new_cost: z.number().min(0, "New cost must be non-negative").optional(),
  change_reason: z.string().optional(),
  changed_by_user_id: z.string().optional(),
});

// Update Product Service Cost History Validation
export const PostAdminUpdateProductServiceCostHistory = z.object({
  change_reason: z.string().optional(),
  changed_by_user_id: z.string().optional(),
});
