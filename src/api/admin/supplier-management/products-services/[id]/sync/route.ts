import { MedusaRequest, MedusaResponse, AuthenticatedMedusaRequest } from "@camped-ai/framework/http";
import { z } from "zod";
import { ProductServiceSyncService } from "../../../../../../services/product-service-sync-service";
import { ensureServicesRegistered } from "../../../../../../utils/service-registration";

// Validation schemas
const PostAdminSyncProductServiceSchema = z.object({
  force_resync: z.boolean().optional().default(false),
});

type PostAdminSyncProductServiceType = z.infer<typeof PostAdminSyncProductServiceSchema>;



/**
 * POST /admin/supplier-management/products-services/[id]/sync
 * Sync a specific product service to add-on
 */
export const POST = async (
  req: AuthenticatedMedusaRequest<PostAdminSyncProductServiceType>,
  res: MedusaResponse
) => {
  try {
    const { id } = req.params;

    if (!id) {
      return res.status(400).json({
        message: "Product service ID is required",
      });
    }

    console.log(`🔄 POST /admin/supplier-management/products-services/${id}/sync - Syncing product service`);
    console.log("Request body:", req.body);

    // Ensure required services are registered
    ensureServicesRegistered(req.scope);

    // Validate request body
    const validatedData = PostAdminSyncProductServiceSchema.parse(req.body || {});

    // Create sync service
    const syncService = new ProductServiceSyncService(req.scope);

    // Perform sync
    const result = await syncService.syncProductServiceToAddOn(id);

    // Get sync status
    const syncStatus = await syncService.getSyncStatus(id);

    console.log(`✅ Sync completed for product service ${id}`);

    res.json({
      message: "Product service sync completed",
      product: result.product,
      variant: result.variant,
      sync_status: syncStatus,
    });

  } catch (error) {
    console.error(`❌ Error syncing product service ${req.params.id}:`, error);
    
    if (error.message.includes("not found")) {
      return res.status(404).json({
        message: error.message,
      });
    }

    res.status(500).json({
      message: error instanceof Error ? error.message : "Failed to sync product service",
      error: error instanceof Error ? error.stack : String(error),
    });
  }
};

/**
 * GET /admin/supplier-management/products-services/[id]/sync
 * Get sync status for a specific product service
 */
export const GET = async (req: MedusaRequest, res: MedusaResponse) => {
  try {
    const { id } = req.params;

    if (!id) {
      return res.status(400).json({
        message: "Product service ID is required",
      });
    }

    console.log(`📊 GET /admin/supplier-management/products-services/${id}/sync - Getting sync status`);

    // Ensure required services are registered
    ensureServicesRegistered(req.scope);

    // Create sync service
    const syncService = new ProductServiceSyncService(req.scope);

    // Get sync status
    const syncStatus = await syncService.getSyncStatus(id);

    // Get sync logs for this product service
    const logs = await syncService.getSyncLogsForProductService(id);

    res.json({
      product_service_id: id,
      sync_status: syncStatus,
      sync_logs: logs,
    });

  } catch (error) {
    console.error(`❌ Error getting sync status for product service ${req.params.id}:`, error);
    res.status(500).json({
      message: error instanceof Error ? error.message : "Failed to get sync status",
      error: error instanceof Error ? error.stack : String(error),
    });
  }
};
