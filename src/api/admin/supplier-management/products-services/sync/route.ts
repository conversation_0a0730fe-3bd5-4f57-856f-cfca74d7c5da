import { MedusaRequest, MedusaResponse, AuthenticatedMedusaRequest } from "@camped-ai/framework/http";
import { z } from "zod";
import { ProductServiceSyncService } from "../../../../../services/product-service-sync-service";
import { ensureServicesRegistered } from "../../../../../utils/service-registration";

// Validation schemas
const PostAdminSyncAllProductServicesSchema = z.object({
  status: z.enum(['active', 'inactive']).optional(),
  type: z.enum(['Product', 'Service']).optional(),
  limit: z.number().min(1).max(1000).optional().default(100),
  offset: z.number().min(0).optional().default(0),
  force_resync: z.boolean().optional().default(false), // Force resync even if already synced
});

const PostAdminSyncSingleProductServiceSchema = z.object({
  product_service_id: z.string(),
  force_resync: z.boolean().optional().default(false),
});

type PostAdminSyncAllProductServicesType = z.infer<typeof PostAdminSyncAllProductServicesSchema>;
type PostAdminSyncSingleProductServiceType = z.infer<typeof PostAdminSyncSingleProductServiceSchema>;



/**
 * POST /admin/supplier-management/products-services/sync
 * Bulk sync all product services to add-ons
 */
export const POST = async (
  req: AuthenticatedMedusaRequest<PostAdminSyncAllProductServicesType>,
  res: MedusaResponse
) => {
  try {
    console.log("🔄 POST /admin/supplier-management/products-services/sync - Bulk sync started");
    console.log("Request body:", req.body);

    // Ensure required services are registered
    ensureServicesRegistered(req.scope);

    // Validate request body
    const validatedData = PostAdminSyncAllProductServicesSchema.parse(req.body || {});

    // Create sync service
    const syncService = new ProductServiceSyncService(req.scope);

    // Perform bulk sync
    const result = await syncService.bulkSyncProductServicesToAddOns({
      status: validatedData.status,
      type: validatedData.type,
      limit: validatedData.limit,
      offset: validatedData.offset,
    });

    // Get sync statistics
    const stats = await syncService.getSyncStatistics();

    console.log(`✅ Bulk sync completed: ${result.synced}/${result.total} synced, ${result.errors.length} errors`);

    res.json({
      message: "Bulk sync completed",
      sync_result: result,
      statistics: stats,
    });

  } catch (error) {
    console.error("❌ Error during bulk sync:", error);
    res.status(500).json({
      message: error instanceof Error ? error.message : "Failed to perform bulk sync",
      error: error instanceof Error ? error.stack : String(error),
    });
  }
};

/**
 * PUT /admin/supplier-management/products-services/sync
 * Sync a single product service to add-on
 */
export const PUT = async (
  req: AuthenticatedMedusaRequest<PostAdminSyncSingleProductServiceType>,
  res: MedusaResponse
) => {
  try {
    console.log("🔄 PUT /admin/supplier-management/products-services/sync - Single sync started");
    console.log("Request body:", req.body);

    // Ensure required services are registered
    ensureServicesRegistered(req.scope);

    // Validate request body
    const validatedData = PostAdminSyncSingleProductServiceSchema.parse(req.body);

    // Create sync service
    const syncService = new ProductServiceSyncService(req.scope);

    // Perform single sync
    const result = await syncService.syncProductServiceToAddOn(validatedData.product_service_id);

    // Get sync status for this product service
    const syncStatus = await syncService.getSyncStatus(validatedData.product_service_id);

    console.log(`✅ Single sync completed for product service ${validatedData.product_service_id}`);

    res.json({
      message: "Single sync completed",
      product: result.product,
      variant: result.variant,
      sync_status: syncStatus,
    });

  } catch (error) {
    console.error("❌ Error during single sync:", error);
    res.status(500).json({
      message: error instanceof Error ? error.message : "Failed to perform single sync",
      error: error instanceof Error ? error.stack : String(error),
    });
  }
};

/**
 * GET /admin/supplier-management/products-services/sync
 * Get sync statistics and status
 */
export const GET = async (req: MedusaRequest, res: MedusaResponse) => {
  try {
    console.log("📊 GET /admin/supplier-management/products-services/sync - Getting sync statistics");

    // Ensure required services are registered
    ensureServicesRegistered(req.scope);

    // Create sync service
    const syncService = new ProductServiceSyncService(req.scope);

    // Get sync statistics
    const stats = await syncService.getSyncStatistics();

    // Get recent sync logs (last 50)
    const recentLogs = await syncService.getRecentSyncLogs(50);

    res.json({
      statistics: stats,
      recent_logs: recentLogs,
    });

  } catch (error) {
    console.error("❌ Error getting sync statistics:", error);
    res.status(500).json({
      message: error instanceof Error ? error.message : "Failed to get sync statistics",
      error: error instanceof Error ? error.stack : String(error),
    });
  }
};
