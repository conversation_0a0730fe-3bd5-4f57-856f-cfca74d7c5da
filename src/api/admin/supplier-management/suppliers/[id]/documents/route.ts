import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";
import { MedusaError } from "@camped-ai/framework/utils";
import { SUPPLIER_MANAGEMENT_MODULE } from "src/modules/vendor_management";

/**
 * @swagger
 * /admin/supplier-management/suppliers/{id}/documents:
 *   get:
 *     summary: List supplier documents
 *     description: Retrieve all documents for a specific supplier
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: The supplier ID
 *     responses:
 *       200:
 *         description: List of supplier documents
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 documents:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       id:
 *                         type: string
 *                       url:
 *                         type: string
 *                       rank:
 *                         type: number
 *                       metadata:
 *                         type: object
 *                       created_at:
 *                         type: string
 *                       updated_at:
 *                         type: string
 */
export async function GET(req: MedusaRequest, res: MedusaResponse) {
  const supplierModuleService = req.scope.resolve(SUPPLIER_MANAGEMENT_MODULE);
  const { id: supplierId } = req.params;

  try {
    const documents = await supplierModuleService.getSupplierDocuments(
      supplierId,
      {
        order: { rank: "ASC" },
      }
    );

    res.status(200).json({
      supplier_documents: documents,
    });
  } catch (error) {
    throw new MedusaError(MedusaError.Types.INVALID_DATA, error.message);
  }
}

/**
 * @swagger
 * /admin/supplier-management/suppliers/{id}/documents:
 *   post:
 *     summary: Upload supplier documents
 *     description: Upload one or more documents for a supplier
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: The supplier ID
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             properties:
 *               files:
 *                 type: array
 *                 items:
 *                   type: string
 *                   format: binary
 *               rank:
 *                 type: number
 *                 description: Starting rank for uploaded documents
 *               metadata:
 *                 type: object
 *                 description: Additional metadata to store with documents
 *     responses:
 *       201:
 *         description: Documents uploaded successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 documents:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       id:
 *                         type: string
 *                       url:
 *                         type: string
 *                       rank:
 *                         type: number
 *                       metadata:
 *                         type: object
 */
export async function POST(req: MedusaRequest, res: MedusaResponse) {
  const supplierId = req.params.id;
  //@ts-ignore
  const files = req.files as Express.Multer.File[];

  if (!files?.length) {
    throw new MedusaError(
      MedusaError.Types.INVALID_DATA,
      "No files were uploaded"
    );
  }

  const supplierModuleService = req.scope.resolve(SUPPLIER_MANAGEMENT_MODULE);
  const supplierDocuments = await supplierModuleService.uploadSupplierDocuments(
    supplierId,
    files,
    req.scope
  );

  res.status(200).json({ supplier_documents: supplierDocuments });
}
