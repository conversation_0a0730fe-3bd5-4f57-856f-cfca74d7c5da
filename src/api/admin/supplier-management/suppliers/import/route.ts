import {
  AuthenticatedMedusaRequest,
  MedusaResponse,
} from "@camped-ai/framework/http";
import { ContainerRegistrationKeys } from "@camped-ai/framework/utils";
import { z } from "zod";
import { PostAdminImportSuppliers } from "../validators";
import { BulkImportSuppliersWorkflow } from "src/workflows/vendor_management";
import { SUPPLIER_MANAGEMENT_MODULE } from "src/modules/vendor_management";
import { SUPPLIER_PRODUCTS_SERVICES_MODULE } from "src/modules/supplier-products-services";
import SupplierProductsServicesModuleService from "src/modules/supplier-products-services/service";

type PostAdminImportSuppliersType = z.infer<typeof PostAdminImportSuppliers>;

/**
 * POST /admin/supplier-management/suppliers/import
 * Bulk import suppliers from CSV/Excel data
 */
export const POST = async (
  req: AuthenticatedMedusaRequest<PostAdminImportSuppliersType>,
  res: MedusaResponse
) => {
  try {
    const { suppliers } = req.validatedBody;

    if (!suppliers || !Array.isArray(suppliers) || suppliers.length === 0) {
      return res.status(400).json({
        type: "validation_error",
        message: "Suppliers array is required and must not be empty",
      });
    }

    // Validate each supplier data
    const validationErrors: Array<{
      index: number;
      field: string;
      message: string;
      value?: any;
    }> = [];

    const requiredFields = [
      'name'
      // Note: address is optional and should not cause validation errors
      // Note: business_type has been removed from the simplified data model
    ];

    // Business types have been removed from the simplified data model

    suppliers.forEach((supplier, index) => {
      // Check required fields
      requiredFields.forEach(field => {
        if (!supplier[field] || supplier[field].toString().trim() === '') {
          validationErrors.push({
            index: index + 1,
            field,
            message: `${field.replace('_', ' ')} is required`,
            value: supplier[field]
          });
        }
      });

      // Validate preference (case-insensitive) - optional field
      if (supplier.preference && !['Preferred', 'Backup'].some(validPref =>
        validPref.toLowerCase() === supplier.preference.toLowerCase())) {
        validationErrors.push({
          index: index + 1,
          field: 'preference',
          message: 'Preference must be either "Preferred" or "Backup"',
          value: supplier.preference
        });
      }

      // Note: Email validation is now handled through the Contacts sheet
      // Note: Business type validation removed - field no longer exists in simplified data model

      // Validate that supplier has at least one contact (backend safety check)
      if (!supplier.contacts || !Array.isArray(supplier.contacts) || supplier.contacts.length === 0) {
        validationErrors.push({
          index: index + 1,
          field: 'contacts',
          message: `Supplier '${supplier.name || 'Unknown'}' must have at least one contact. Please add contact information before importing.`,
          value: supplier.contacts
        });
      } else {
        // Validate that supplier has at least one primary contact
        const hasPrimaryContact = supplier.contacts.some((contact: any) => contact.is_primary === true);

        if (!hasPrimaryContact) {
          validationErrors.push({
            index: index + 1,
            field: 'primary_contact',
            message: `Supplier '${supplier.name || 'Unknown'}' must have at least one primary contact. Please set one contact as primary (is_primary: true).`,
            value: supplier.contacts
          });
        }
      }
    });

    // Return validation errors if any
    if (validationErrors.length > 0) {
      return res.status(400).json({
        type: "validation_error",
        message: `Found ${validationErrors.length} validation errors`,
        errors: validationErrors,
      });
    }

    // Check for duplicate supplier names in the database
    const supplierModuleService = req.scope.resolve(SUPPLIER_MANAGEMENT_MODULE);
    const supplierNames = suppliers.map(s => s.name?.toString().trim()).filter(name => name);

    // Check for duplicates within the import data itself
    const duplicatesInImport = [];
    const nameCount = {};
    supplierNames.forEach((name, index) => {
      const normalizedName = name.toLowerCase();
      if (nameCount[normalizedName]) {
        duplicatesInImport.push({
          index: index + 1,
          name: name,
          message: `Duplicate supplier name "${name}" found in import data (also at row ${nameCount[normalizedName]})`
        });
      } else {
        nameCount[normalizedName] = index + 1;
      }
    });

    if (duplicatesInImport.length > 0) {
      return res.status(400).json({
        type: "duplicate_error",
        message: `Found ${duplicatesInImport.length} duplicate supplier names in import data`,
        errors: duplicatesInImport,
      });
    }

    // Check for existing suppliers in database (case-insensitive)
    const existingSuppliers = [];
    for (const name of supplierNames) {
      try {
        // Use a more comprehensive search to catch case variations
        const existing = await supplierModuleService.listSuppliers({
          name: {
            $ilike: name // Case-insensitive search
          }
        });

        if (existing && existing.length > 0) {
          existingSuppliers.push({
            name: name,
            existingName: existing[0].name,
            id: existing[0].id,
            message: `Supplier "${name}" already exists in the database${existing[0].name !== name ? ` as "${existing[0].name}"` : ''}`
          });
        }
      } catch (error) {
        // If $ilike doesn't work, try exact match
        try {
          const existing = await supplierModuleService.listSuppliers({
            name: name
          });

          if (existing && existing.length > 0) {
            existingSuppliers.push({
              name: name,
              existingName: existing[0].name,
              id: existing[0].id,
              message: `Supplier "${name}" already exists in the database`
            });
          }
        } catch (fallbackError) {
          // Continue checking other names if both fail
        }
      }
    }

    if (existingSuppliers.length > 0) {
      return res.status(409).json({
        type: "duplicate_error",
        message: `Found ${existingSuppliers.length} suppliers that already exist in the database`,
        errors: existingSuppliers,
        details: "Supplier names must be unique. Please remove or rename the duplicate suppliers."
      });
    }

    // Fetch categories for name-to-ID mapping
    let categoryMap = new Map<string, string>(); // name -> id
    try {
      const supplierProductsServicesService: SupplierProductsServicesModuleService =
        req.scope.resolve(SUPPLIER_PRODUCTS_SERVICES_MODULE);

      const categoriesResult = await supplierProductsServicesService.listCategories({
        is_active: true
      });

      // Handle different return formats
      let categories: any[] = [];
      if (Array.isArray(categoriesResult)) {
        categories = categoriesResult;
      } else if (categoriesResult && (categoriesResult as any).data) {
        categories = (categoriesResult as any).data;
      }

      // Build category name to ID mapping
      categories.forEach((category: any) => {
        if (category.name && category.id) {
          categoryMap.set(category.name.toLowerCase().trim(), category.id);
        }
      });
    } catch (error) {
      console.warn("Failed to fetch categories for mapping:", error.message);
      // Continue without category mapping - categories will be stored as names
    }

    // Transform suppliers data for the workflow
    const transformedSuppliers = suppliers.map(supplier => {
      const { contacts, ...otherData } = supplier;

      // Transform contacts array to the format expected by the service
      const transformedContacts = contacts && contacts.length > 0
        ? contacts.map((contact: any) => {
            const transformed = {
              name: contact.name, // Already mapped in parse endpoint
              email: contact.email, // Already mapped in parse endpoint
              phone: contact.phone, // Already mapped in parse endpoint
              is_whatsapp: contact.is_whatsapp === true || contact.is_whatsapp === 'true',
              is_primary: contact.is_primary === true || contact.is_primary === 'true',
            };
            return transformed;
          })
        : [];

      // Transform categories from names to IDs if mapping is available
      let transformedCategories = supplier.categories;
      if (supplier.categories && Array.isArray(supplier.categories) && categoryMap.size > 0) {
        transformedCategories = supplier.categories
          .map((categoryName: string) => {
            const categoryId = categoryMap.get(categoryName.toLowerCase().trim());
            if (categoryId) {
              return categoryId;
            } else {
              console.warn(`Category "${categoryName}" not found in database, keeping as name`);
              return categoryName; // Keep original name if not found
            }
          })
          .filter(Boolean); // Remove any null/undefined values
      }

      return {
        ...otherData,
        status: supplier.status || "pending",
        verification_status: supplier.verification_status || "unverified",
        categories: transformedCategories,
        contacts: transformedContacts,
      };
    });

    // Run the bulk import workflow
    let result: any;
    try {
      const workflowResult = await BulkImportSuppliersWorkflow(req.scope).run({
        input: { suppliers: transformedSuppliers },
      });
      result = workflowResult.result;
    } catch (workflowError) {
      throw workflowError;
    }

    res.json({
      success: true,
      imported: result.imported || 0,
      errors: result.errors || [],
      message: `Successfully imported ${result.imported || 0} suppliers`,
      suppliers: result.suppliers || [],
    });

  } catch (error) {
    console.error("Error in supplier import:", error);

    // Handle Zod validation errors
    if (error && typeof error === 'object' && 'issues' in error) {
      const zodError = error as any;
      const validationErrors = zodError.issues.map((issue: any) => {
        const path = issue.path || [];
        let supplierIndex = 'unknown';
        let fieldName = 'unknown field';

        // Extract supplier index and field name from path
        if (path.length >= 2 && path[0] === 'suppliers') {
          supplierIndex = (parseInt(path[1]) + 1).toString();
          if (path.length >= 4 && path[2] === 'contacts') {
            const contactIndex = parseInt(path[3]) + 1;
            fieldName = `contact ${contactIndex} ${path[4] || 'field'}`;
          } else {
            fieldName = path[2] || 'field';
          }
        }

        return {
          index: supplierIndex,
          field: fieldName,
          message: issue.message || 'Validation error',
          value: issue.received || 'invalid value'
        };
      });

      return res.status(400).json({
        type: "validation_error",
        message: `Found ${validationErrors.length} validation errors`,
        errors: validationErrors,
      });
    }

    // Handle specific error types
    if (error.message?.includes('duplicate') || error.message?.includes('unique')) {
      return res.status(409).json({
        type: "duplicate_error",
        message: "One or more suppliers already exist with the same name or email",
        details: error.message,
      });
    }

    res.status(400).json({
      type: "import_error",
      message: error.message || "Failed to import suppliers",
      details: error.stack,
    });
  }
};
