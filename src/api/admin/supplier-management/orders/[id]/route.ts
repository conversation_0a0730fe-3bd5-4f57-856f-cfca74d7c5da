import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";
import { SUPPLIER_MANAGEMENT_MODULE } from "../../../../../modules/vendor_management";
import SupplierModuleService from "../../../../../modules/vendor_management/supplier-service";
import { BOOKING_ADD_ONS_MODULE } from "../../../../../modules/booking-add-ons";

/**
 * Helper function to synchronize booking add-on statuses when supplier order status changes
 */
async function syncBookingAddOnStatuses(
  scope: any,
  orderId: string,
  newStatus: string
) {
  try {
    console.log(
      `🔄 Synchronizing booking add-on statuses for order ${orderId} to status: ${newStatus}`
    );

    // Resolve the booking add-on service
    const bookingAddOnService = scope.resolve(BOOKING_ADD_ONS_MODULE);

    // Find all booking add-ons associated with this supplier order
    const bookingAddOns = await bookingAddOnService.listBookingAddOns({
      supplier_order_id: orderId,
    });

    if (bookingAddOns && bookingAddOns.length > 0) {
      console.log(
        `📋 Found ${bookingAddOns.length} booking add-on(s) to update`
      );

      // Update each booking add-on's order_status
      for (const addOn of bookingAddOns) {
        await bookingAddOnService.updateBookingAddOn(addOn.id, {
          order_status: newStatus,
        });
        console.log(
          `✅ Updated booking add-on ${addOn.id} status to: ${newStatus}`
        );
      }

      console.log(
        `🎉 Successfully synchronized ${bookingAddOns.length} booking add-on status(es)`
      );
    } else {
      console.log(`ℹ️ No booking add-ons found for supplier order ${orderId}`);
    }
  } catch (error) {
    console.error(`❌ Failed to synchronize booking add-on statuses:`, error);
    // Don't throw the error to avoid breaking the main order update
  }
}

/**
 * GET /admin/supplier-management/orders/[id]
 * Retrieve a specific supplier order with full details
 */
export const GET = async (req: MedusaRequest, res: MedusaResponse) => {
  try {
    const { id } = req.params;

    if (!id) {
      return res.status(400).json({
        type: "invalid_data",
        message: "Order ID is required",
      });
    }

    console.log(`🔍 Fetching supplier order: ${id}`);

    const supplierService: SupplierModuleService = req.scope.resolve(
      SUPPLIER_MANAGEMENT_MODULE
    );

    // Get the supplier order
    const order = await supplierService.retrieveSupplierOrder(id);

    if (!order) {
      return res.status(404).json({
        type: "not_found",
        message: "Supplier order not found",
      });
    }

    // Get supplier information
    let supplierData = null;
    if (order.supplier_id) {
      try {
        supplierData = await supplierService.retrieveSupplier(
          order.supplier_id
        );
      } catch (error) {
        console.log(
          `Could not fetch supplier data for ${order.supplier_id}:`,
          error
        );
      }
    }

    // Get order items
    let orderItems = [];
    try {
      orderItems = await supplierService.getOrderItems(order.id);
    } catch (error) {
      console.log(`Could not fetch order items for ${order.id}:`, error);
    }

    // Enhance order with additional data
    const enhancedOrder = {
      ...order,
      supplier_name: supplierData?.name || "Unknown Supplier",
      supplier_email: supplierData?.email || null,
      supplier_phone: supplierData?.phone || null,
      supplier_address: supplierData?.address || null,
      items: orderItems,
      items_count: orderItems.length,
    };

    console.log(`✅ Retrieved supplier order: ${order.order_number}`);

    res.json({
      order: enhancedOrder,
    });
  } catch (error) {
    console.error("❌ Error fetching supplier order:", error);
    res.status(500).json({
      type: "server_error",
      message:
        error instanceof Error
          ? error.message
          : "Failed to fetch supplier order",
    });
  }
};

/**
 * PUT /admin/supplier-management/orders/[id]
 * Update supplier order details (status, notes, etc.)
 */
export const PUT = async (req: MedusaRequest, res: MedusaResponse) => {
  try {
    const { id } = req.params;
    const updateData = req.body;

    if (!id) {
      return res.status(400).json({
        type: "invalid_data",
        message: "Order ID is required",
      });
    }

    console.log(`🔄 Updating supplier order: ${id}`, updateData);

    const supplierService: SupplierModuleService = req.scope.resolve(
      SUPPLIER_MANAGEMENT_MODULE
    );

    // Validate the order exists
    const existingOrder = await supplierService.retrieveSupplierOrder(id);
    if (!existingOrder) {
      return res.status(404).json({
        type: "not_found",
        message: "Supplier order not found",
      });
    }

    // Validate status if provided
    if (updateData.status) {
      const validStatuses = [
        "pending",
        "confirmed",
        "in_progress",
        "completed",
        "cancelled",
      ];
      if (!validStatuses.includes(updateData.status)) {
        return res.status(400).json({
          type: "invalid_data",
          message: `Invalid status. Must be one of: ${validStatuses.join(
            ", "
          )}`,
        });
      }
    }

    // Update the order using the enhanced service method
    const updatedOrder = await supplierService.updateOrderDetails(
      id,
      updateData
    );

    // Synchronize booking add-on statuses if status was updated
    if (updateData.status && updateData.status !== existingOrder.status) {
      await syncBookingAddOnStatuses(req.scope, id, updateData.status);
    }

    // Get enhanced order data for response
    let supplierData = null;
    if (updatedOrder.supplier_id) {
      try {
        supplierData = await supplierService.retrieveSupplier(
          updatedOrder.supplier_id
        );
      } catch (error) {
        console.log(
          `Could not fetch supplier data for ${updatedOrder.supplier_id}:`,
          error
        );
      }
    }

    const enhancedOrder = {
      ...updatedOrder,
      supplier_name: supplierData?.name || "Unknown Supplier",
    };

    console.log(`✅ Updated supplier order: ${updatedOrder.order_number}`);

    res.json({
      order: enhancedOrder,
    });
  } catch (error) {
    console.error("❌ Error updating supplier order:", error);
    res.status(500).json({
      type: "server_error",
      message:
        error instanceof Error
          ? error.message
          : "Failed to update supplier order",
    });
  }
};

/**
 * PATCH /admin/supplier-management/orders/[id]
 * Update specific fields of supplier order (for partial updates)
 */
export const PATCH = async (req: MedusaRequest, res: MedusaResponse) => {
  try {
    const { id } = req.params;
    const updateData = req.body;

    if (!id) {
      return res.status(400).json({
        type: "invalid_data",
        message: "Order ID is required",
      });
    }

    console.log(`🔄 Partially updating supplier order: ${id}`, updateData);

    const supplierService: SupplierModuleService = req.scope.resolve(
      SUPPLIER_MANAGEMENT_MODULE
    );

    // For status-only updates, use the specific method
    if (updateData.status && Object.keys(updateData).length === 1) {
      const existingOrder = await supplierService.retrieveSupplierOrder(id);
      const updatedOrder = await supplierService.updateOrderStatus(
        id,
        updateData.status
      );

      // Synchronize booking add-on statuses if status changed
      if (updateData.status !== existingOrder.status) {
        await syncBookingAddOnStatuses(req.scope, id, updateData.status);
      }

      res.json({
        order: updatedOrder,
        message: "Order status updated successfully",
      });
    } else {
      // For other updates, use the general method
      const existingOrder = await supplierService.retrieveSupplierOrder(id);
      const updatedOrder = await supplierService.updateOrderDetails(
        id,
        updateData
      );

      // Synchronize booking add-on statuses if status was updated
      if (updateData.status && updateData.status !== existingOrder.status) {
        await syncBookingAddOnStatuses(req.scope, id, updateData.status);
      }

      res.json({
        order: updatedOrder,
        message: "Order updated successfully",
      });
    }
  } catch (error) {
    console.error("❌ Error updating supplier order:", error);
    res.status(500).json({
      type: "server_error",
      message:
        error instanceof Error
          ? error.message
          : "Failed to update supplier order",
    });
  }
};
