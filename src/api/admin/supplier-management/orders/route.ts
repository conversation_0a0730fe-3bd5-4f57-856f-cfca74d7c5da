import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";
import { SUPPLIER_MANAGEMENT_MODULE } from "../../../../modules/vendor_management";

export const GET = async (req: MedusaRequest, res: MedusaResponse) => {
  try {
    console.log("🔍 Fetching supplier orders...");

    const {
      limit = 50,
      offset = 0,
      supplier_id,
      status,
      order_type,
      order_number,
      search,
    } = req.query;

    // Resolve the supplier service
    const supplierService = req.scope.resolve(SUPPLIER_MANAGEMENT_MODULE);
    console.log("✅ SupplierService resolved successfully");

    // Build filters
    const filters: any = {};
    if (supplier_id) filters.supplier_id = supplier_id;
    if (status) {
      // Validate status value
      const validStatuses = [
        "pending",
        "confirmed",
        "in_progress",
        "completed",
        "cancelled",
      ];
      if (validStatuses.includes(status as string)) {
        filters.status = status;
        console.log(`✅ Valid status filter applied: ${status}`);
      } else {
        console.warn(
          `⚠️ Invalid status filter ignored: ${status}. Valid values: ${validStatuses.join(
            ", "
          )}`
        );
      }
    }
    if (order_type) filters.order_type = order_type;
    if (order_number) filters.order_number = order_number;

    console.log("🔍 Applied filters:", filters);

    try {
      // Get supplier orders using the service with correct method
      // Use listSupplierOrders which exists in the service
      const orders = await supplierService.listSupplierOrders(filters);
      const totalCount = orders.length;

      console.log(
        `✅ Found ${orders.length} supplier orders (total: ${totalCount})`
      );

      // Enhance orders with supplier information
      const enhancedOrders = await Promise.all(
        orders.map(async (order: any) => {
          let supplierData = null;

          // Fetch supplier information
          if (order.supplier_id) {
            try {
              supplierData = await supplierService.retrieveSupplier(
                order.supplier_id
              );
            } catch (supplierError) {
              console.log(
                `Could not fetch supplier data for ${order.supplier_id}:`,
                supplierError
              );
            }
          }

          // Get order items count
          let itemsCount = 0;
          try {
            const orderItems = await supplierService.getOrderItems(order.id);
            itemsCount = orderItems.length;
          } catch (itemsError) {
            console.log(
              `Could not fetch order items for ${order.id}:`,
              itemsError
            );
          }

          return {
            id: order.id,
            order_number: order.order_number,
            supplier_id: order.supplier_id,
            supplier_name: supplierData?.name || "Unknown Supplier",
            supplier_type: supplierData?.supplier_type || "Company",
            order_type: order.order_type,
            status: order.status,
            total_amount: parseFloat(order.total_amount || 0),
            currency_code: order.currency_code || "CHF",
            subtotal: parseFloat(order.subtotal || 0),
            tax_amount: parseFloat(order.tax_amount || 0),
            requested_delivery_date: order.requested_delivery_date,
            actual_delivery_date: order.actual_delivery_date,
            delivery_address: order.delivery_address,
            notes: order.notes,
            internal_notes: order.internal_notes,
            customer_name: order.customer_name,
            customer_email: order.customer_email,
            customer_phone: order.customer_phone,
            hotel_id: order.hotel_id,
            booking_id: order.booking_id,
            items_count: itemsCount,
            created_at: order.created_at,
            updated_at: order.updated_at,
            metadata: order.metadata,
          };
        })
      );

      // Apply search filter if provided
      let filteredOrders = enhancedOrders;
      if (search) {
        const searchLower = search.toString().toLowerCase();
        filteredOrders = enhancedOrders.filter(
          (order) =>
            order.order_number.toLowerCase().includes(searchLower) ||
            order.supplier_name.toLowerCase().includes(searchLower) ||
            order.status.toLowerCase().includes(searchLower) ||
            order.customer_name?.toLowerCase().includes(searchLower) ||
            order.customer_email?.toLowerCase().includes(searchLower)
        );
      }

      res.json({
        orders: filteredOrders,
        count: filteredOrders.length,
        total_count: totalCount,
        offset: parseInt(offset as string),
        limit: parseInt(limit as string),
      });
    } catch (serviceError) {
      console.log(
        "Service query failed, returning empty result:",
        serviceError
      );

      // Return empty result if service method fails
      res.json({
        orders: [],
        count: 0,
        offset: parseInt(offset as string),
        limit: parseInt(limit as string),
      });
    }
  } catch (error) {
    console.error("❌ Error fetching supplier orders:", error);
    res.status(500).json({
      type: "server_error",
      message:
        error instanceof Error
          ? error.message
          : "Failed to fetch supplier orders",
    });
  }
};
