import {
  AuthenticatedMedusaRequest,
  MedusaResponse,
} from "@camped-ai/framework/http";
import {
  PostAdminUpdateExchangeRate,
  PostAdminDeleteExchangeRate,
} from "../validators";
import {
  UpdateExchangeRateWorkflow,
  DeleteExchangeRateWorkflow,
} from "src/workflows/vendor_management/exchange-rate";
import { SUPPLIER_MANAGEMENT_MODULE } from "../../../../../modules/vendor_management";

/**
 * PUT /admin/supplier-management/exchange-rates/[id]
 * Update a specific exchange rate
 */
export async function PUT(
  req: AuthenticatedMedusaRequest,
  res: MedusaResponse
) {
  try {
    const { id } = req.params;

    // Add the ID from the URL params to the body for validation
    const bodyWithId = { ...req.body, id };
    const validatedBody = PostAdminUpdateExchangeRate.parse(bodyWithId);

    const { date, base_currency, selling_currency, exchange_rate, metadata } = validatedBody;

    // Prepare update data (exclude id from the data object)
    const updateData: any = {};
    if (date !== undefined) updateData.date = date;
    if (base_currency !== undefined) updateData.base_currency = base_currency;
    if (selling_currency !== undefined) updateData.selling_currency = selling_currency;
    if (exchange_rate !== undefined) updateData.exchange_rate = exchange_rate;
    if (metadata !== undefined) {
      updateData.metadata = {
        ...metadata,
        updated_by: req.auth?.actor_id || "system",
        updated_at: new Date().toISOString(),
      };
    }

    const { result } = await UpdateExchangeRateWorkflow(req.scope).run({
      input: {
        id,
        ...updateData,
      },
    });

    res.json({
      exchange_rate: result,
      message: "Exchange rate updated successfully",
    });
  } catch (error) {
    console.error("Error updating exchange rate:", error);

    if (error.errors) {
      return res.status(400).json({
        type: "validation_error",
        message: "Invalid request body",
        errors: error.errors,
      });
    }

    // Handle specific error types
    if (error.message.includes("not found")) {
      return res.status(404).json({
        type: "not_found",
        message: error.message,
      });
    }

    if (error.message.includes("already exists")) {
      return res.status(409).json({
        type: "conflict_error",
        message: error.message,
      });
    }

    if (error.message.includes("validation") || error.message.includes("required")) {
      return res.status(400).json({
        type: "validation_error",
        message: error.message,
      });
    }

    return res.status(500).json({
      type: "internal_error",
      message: "Failed to update exchange rate",
      details: error.message,
    });
  }
}

/**
 * DELETE /admin/supplier-management/exchange-rates/[id]
 * Delete a specific exchange rate
 */
export async function DELETE(
  req: AuthenticatedMedusaRequest,
  res: MedusaResponse
) {
  try {
    const { id } = req.params;

    // Validate the ID
    PostAdminDeleteExchangeRate.parse({ id });

    const { result } = await DeleteExchangeRateWorkflow(req.scope).run({
      input: { id },
    });

    res.json({
      id,
      deleted: true,
      message: "Exchange rate deleted successfully",
    });
  } catch (error) {
    console.error("Error deleting exchange rate:", error);

    if (error.errors) {
      return res.status(400).json({
        type: "validation_error",
        message: "Invalid exchange rate ID",
        errors: error.errors,
      });
    }

    // Handle specific error types
    if (error.message.includes("not found")) {
      return res.status(404).json({
        type: "not_found",
        message: error.message,
      });
    }

    return res.status(500).json({
      type: "internal_error",
      message: "Failed to delete exchange rate",
      details: error.message,
    });
  }
}

/**
 * GET /admin/supplier-management/exchange-rates/[id]
 * Retrieve a specific exchange rate by ID
 */
export const GET = async (
  req: AuthenticatedMedusaRequest,
  res: MedusaResponse
) => {
  try {
    const { id } = req.params;
    const supplierModuleService = req.scope.resolve(SUPPLIER_MANAGEMENT_MODULE);

    if (!id) {
      return res.status(400).json({
        type: "validation_error",
        message: "Exchange rate ID is required",
      });
    }

    try {
      const exchangeRate = await supplierModuleService.retrieveExchangeRate(id);

      if (!exchangeRate) {
        return res.status(404).json({
          type: "not_found",
          message: `Exchange rate with id ${id} not found`,
        });
      }

      res.json({
        exchange_rate: exchangeRate,
      });
    } catch (serviceError) {
      console.error("Service error in retrieveExchangeRate:", serviceError);
      
      if (serviceError.message.includes("not found")) {
        return res.status(404).json({
          type: "not_found",
          message: `Exchange rate with id ${id} not found`,
        });
      }

      return res.status(500).json({
        type: "service_error",
        message: "Failed to retrieve exchange rate",
        details: serviceError.message,
      });
    }
  } catch (error) {
    console.error("Error in GET /admin/supplier-management/exchange-rates/[id]:", error);
    return res.status(500).json({
      type: "internal_error",
      message: "Internal server error",
      details: error.message,
    });
  }
};


