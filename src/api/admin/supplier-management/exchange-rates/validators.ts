import { z } from "zod";

// Exchange rate creation validator
export const PostAdminCreateExchangeRate = z.object({
  date: z.string().refine((val) => {
    // Validate date format (YYYY-MM-DD or ISO date string)
    const date = new Date(val);
    return !isNaN(date.getTime());
  }, "Invalid date format"),
  
  base_currency: z
    .string()
    .min(3, "Base currency must be at least 3 characters")
    .max(3, "Base currency must be exactly 3 characters")
    .regex(/^[A-Z]{3}$/, "Base currency must be a valid 3-letter uppercase code (e.g., USD, EUR, CHF)")
    .transform((val) => val.toUpperCase()),
  
  selling_currency: z
    .string()
    .min(3, "Selling currency must be at least 3 characters")
    .max(3, "Selling currency must be exactly 3 characters")
    .regex(/^[A-Z]{3}$/, "Selling currency must be a valid 3-letter uppercase code (e.g., USD, EUR, CHF)")
    .transform((val) => val.toUpperCase()),
  
  exchange_rate: z
    .number()
    .positive("Exchange rate must be a positive number")
    .min(0.0001, "Exchange rate must be at least 0.0001")
    .max(1000000, "Exchange rate cannot exceed 1,000,000"),
  
  metadata: z.record(z.any()).optional(),
}).refine((data) => {
  // Ensure base and selling currencies are different
  return data.base_currency !== data.selling_currency;
}, {
  message: "Base currency and selling currency must be different",
  path: ["selling_currency"],
});

// Exchange rate update validator
export const PostAdminUpdateExchangeRate = z.object({
  id: z.string().min(1, "Exchange rate ID is required"),
  
  date: z.string().refine((val) => {
    const date = new Date(val);
    return !isNaN(date.getTime());
  }, "Invalid date format").optional(),
  
  base_currency: z
    .string()
    .min(3, "Base currency must be at least 3 characters")
    .max(3, "Base currency must be exactly 3 characters")
    .regex(/^[A-Z]{3}$/, "Base currency must be a valid 3-letter uppercase code")
    .transform((val) => val.toUpperCase())
    .optional(),
  
  selling_currency: z
    .string()
    .min(3, "Selling currency must be at least 3 characters")
    .max(3, "Selling currency must be exactly 3 characters")
    .regex(/^[A-Z]{3}$/, "Selling currency must be a valid 3-letter uppercase code")
    .transform((val) => val.toUpperCase())
    .optional(),
  
  exchange_rate: z
    .number()
    .positive("Exchange rate must be a positive number")
    .min(0.0001, "Exchange rate must be at least 0.0001")
    .max(1000000, "Exchange rate cannot exceed 1,000,000")
    .optional(),
  
  metadata: z.record(z.any()).optional(),
}).refine((data) => {
  // Ensure base and selling currencies are different if both are provided
  if (data.base_currency && data.selling_currency) {
    return data.base_currency !== data.selling_currency;
  }
  return true;
}, {
  message: "Base currency and selling currency must be different",
  path: ["selling_currency"],
});

// Exchange rate deletion validator
export const PostAdminDeleteExchangeRate = z.object({
  id: z.string().min(1, "Exchange rate ID is required"),
});

// Bulk import exchange rates validator
export const PostAdminImportExchangeRates = z.object({
  exchange_rates: z
    .array(
      z.object({
        date: z.string().refine((val) => {
          const date = new Date(val);
          return !isNaN(date.getTime());
        }, "Invalid date format"),
        
        base_currency: z
          .string()
          .min(3, "Base currency must be at least 3 characters")
          .max(3, "Base currency must be exactly 3 characters")
          .regex(/^[A-Z]{3}$/, "Base currency must be a valid 3-letter uppercase code")
          .transform((val) => val.toUpperCase()),
        
        selling_currency: z
          .string()
          .min(3, "Selling currency must be at least 3 characters")
          .max(3, "Selling currency must be exactly 3 characters")
          .regex(/^[A-Z]{3}$/, "Selling currency must be a valid 3-letter uppercase code")
          .transform((val) => val.toUpperCase()),
        
        exchange_rate: z
          .union([
            z.number(),
            z.string().transform((val) => {
              const num = parseFloat(val);
              if (isNaN(num)) throw new Error("Invalid exchange rate number");
              return num;
            }),
          ])
          .refine((val) => val > 0, "Exchange rate must be positive")
          .refine((val) => val >= 0.0001, "Exchange rate must be at least 0.0001")
          .refine((val) => val <= 1000000, "Exchange rate cannot exceed 1,000,000"),
        
        metadata: z.record(z.any()).optional(),
      }).refine((data) => {
        return data.base_currency !== data.selling_currency;
      }, {
        message: "Base currency and selling currency must be different",
        path: ["selling_currency"],
      })
    )
    .min(1, "At least one exchange rate is required"),
});

// Query parameters for listing exchange rates
export const GetAdminExchangeRatesQuery = z.object({
  limit: z.coerce.number().min(1).max(100).default(50),
  offset: z.coerce.number().min(0).default(0),
  
  // Date filters
  date: z.string().refine((val) => {
    const date = new Date(val);
    return !isNaN(date.getTime());
  }, "Invalid date format").optional(),
  
  date_from: z.string().refine((val) => {
    const date = new Date(val);
    return !isNaN(date.getTime());
  }, "Invalid date format").optional(),
  
  date_to: z.string().refine((val) => {
    const date = new Date(val);
    return !isNaN(date.getTime());
  }, "Invalid date format").optional(),
  
  // Currency filters
  base_currency: z
    .string()
    .regex(/^[A-Z]{3}$/, "Base currency must be a valid 3-letter uppercase code")
    .transform((val) => val.toUpperCase())
    .optional(),
  
  selling_currency: z
    .string()
    .regex(/^[A-Z]{3}$/, "Selling currency must be a valid 3-letter uppercase code")
    .transform((val) => val.toUpperCase())
    .optional(),
  
  // Exchange rate range filters
  exchange_rate_min: z.coerce.number().positive().optional(),
  exchange_rate_max: z.coerce.number().positive().optional(),
  
  // Sorting
  sort_by: z
    .enum(["date", "base_currency", "selling_currency", "exchange_rate", "created_at", "updated_at"])
    .default("date"),
  sort_order: z.enum(["asc", "desc"]).default("desc"),
}).refine((data) => {
  // Ensure date_from is before date_to if both are provided
  if (data.date_from && data.date_to) {
    return new Date(data.date_from) <= new Date(data.date_to);
  }
  return true;
}, {
  message: "date_from must be before or equal to date_to",
  path: ["date_to"],
}).refine((data) => {
  // Ensure exchange_rate_min is less than exchange_rate_max if both are provided
  if (data.exchange_rate_min && data.exchange_rate_max) {
    return data.exchange_rate_min <= data.exchange_rate_max;
  }
  return true;
}, {
  message: "exchange_rate_min must be less than or equal to exchange_rate_max",
  path: ["exchange_rate_max"],
});
