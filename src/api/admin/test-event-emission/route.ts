import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";
import { Modules } from "@camped-ai/framework/utils";

/**
 * POST /admin/test-event-emission
 * 
 * Test endpoint to verify event emission and subscriber functionality
 */
export const POST = async (req: MedusaRequest, res: MedusaResponse) => {
  try {
    const { order_id } = req.body;

    if (!order_id) {
      return res.status(400).json({
        message: "order_id is required"
      });
    }

    console.log(`🧪 [TEST] Testing event emission for order: ${order_id}`);

    // Emit order.created event manually
    const eventModuleService = req.scope.resolve(Modules.EVENT_BUS);

    await eventModuleService.emit({
      name: "order.created",
      data: {
        id: order_id,
        order_id: order_id,
        test_emission: true,
        emitted_at: new Date().toISOString()
      },
    });

    console.log(`✅ [TEST] Emitted order.created event for order: ${order_id}`);

    res.json({
      success: true,
      message: `Test event emitted for order: ${order_id}`,
      event_name: "order.created",
      order_id: order_id,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error("Error testing event emission:", error);
    res.status(500).json({
      message: "Failed to test event emission",
      error: error instanceof Error ? error.message : "Unknown error"
    });
  }
};
