import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";
import ItineraryService from "../../../modules/concierge-management/itinerary-service";
import { asClass } from "awilix";

/**
 * Test endpoint to verify itinerary service registration
 * This endpoint doesn't require authentication and can be used to test service resolution
 */
export async function GET(req: MedusaRequest, res: MedusaResponse) {
  try {
    console.log("🧪 [HEALTH-TEST] Testing itinerary service registration...");
    
    // Check if the service is registered
    const hasItineraryService = req.scope.hasRegistration("itineraryService");
    console.log("🔍 [HEALTH-TEST] Has itinerary service:", hasItineraryService);
    
    if (!hasItineraryService) {
      console.log("⚠️ [HEALTH-TEST] Service not registered, attempting registration...");
      
      try {
        // Register the service using static import
        req.scope.register({
          itineraryService: asClass(ItineraryService).singleton(),
        });
        
        console.log("✅ [HEALTH-TEST] Service registered successfully");
      } catch (registrationError) {
        console.error("❌ [HEALTH-TEST] Failed to register service:", registrationError);
        return res.status(500).json({
          success: false,
          message: "Failed to register itinerary service",
          error: registrationError.message,
          stack: registrationError.stack,
        });
      }
    }
    
    // Try to resolve the service
    let serviceResolved = false;
    let serviceError = null;
    
    try {
      const itineraryService = req.scope.resolve("itineraryService");
      serviceResolved = true;
      console.log("✅ [HEALTH-TEST] Service resolved successfully");
      
      // Test a simple method if available
      const hasCreateMethod = typeof itineraryService.createItineraries === 'function';
      const hasListMethod = typeof itineraryService.listItineraries === 'function';
      const hasCreateFromBookingMethod = typeof itineraryService.createItineraryFromBooking === 'function';
      
      return res.status(200).json({
        success: true,
        message: "Itinerary service test completed successfully",
        results: {
          service_registered: true,
          service_resolved: serviceResolved,
          has_create_method: hasCreateMethod,
          has_list_method: hasListMethod,
          has_create_from_booking_method: hasCreateFromBookingMethod,
          service_type: typeof itineraryService,
          service_constructor: itineraryService.constructor.name,
        },
      });
    } catch (resolveError) {
      serviceError = resolveError;
      console.error("❌ [HEALTH-TEST] Failed to resolve service:", resolveError);
      
      return res.status(500).json({
        success: false,
        message: "Failed to resolve itinerary service",
        results: {
          service_registered: req.scope.hasRegistration("itineraryService"),
          service_resolved: false,
        },
        error: resolveError.message,
        stack: resolveError.stack,
      });
    }
  } catch (error) {
    console.error("❌ [HEALTH-TEST] Unexpected error in test endpoint:", error);
    return res.status(500).json({
      success: false,
      message: "Unexpected error in test endpoint",
      error: error.message,
      stack: error.stack,
    });
  }
}
