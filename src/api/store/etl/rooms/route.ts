import {
  MedusaRequest,
  MedusaResponse,
} from "@camped-ai/framework/http";
import { ContainerRegistrationKeys } from "@camped-ai/framework/utils";
import { z } from "zod";
import { CreateRoomWorkflow } from "../../../../workflows/hotel-management/room/create-room";

// Validation schema for creating a single room via ETL
const StoreCreateRoomSchema = z.object({
  name: z.string().min(1, "Name is required"),
  room_number: z.string().min(1, "Room number is required"),
  status: z.enum(["available", "occupied", "maintenance", "cleaning"]).default("available"),
  floor: z.string().min(1, "Floor is required"),
  notes: z.string().optional(),
  is_active: z.boolean().default(true),
  // Room relationships (using room numbers for ETL)
  left_room: z.string().optional(),
  opposite_room: z.string().optional(),
  connected_room: z.string().optional(),
  right_room: z.string().optional(),
  // References
  room_config_id: z.string().min(1, "Room config ID is required"),
  hotel_id: z.string().min(1, "Hotel ID is required"),
  // Optional pricing
  price: z.number().min(0).optional(),
  currency_code: z.string().optional(),
  // Availability type
  availability_type: z.enum(["standard", "on_demand"]).default("standard"),
  // ETL specific flat fields
  salesforce_id: z.string().optional(),
  import_batch: z.string().optional(),
  room_view: z.string().optional(),
  has_balcony: z.boolean().optional(),
});

export type StoreCreateRoomType = z.infer<typeof StoreCreateRoomSchema>;

/**
 * POST /store/etl/rooms
 * 
 * Create a single room via ETL
 */
export const POST = async (
  req: MedusaRequest<StoreCreateRoomType>,
  res: MedusaResponse
) => {
  try {
    // Validate request body
    const validationResult = StoreCreateRoomSchema.safeParse(req.body);
    if (!validationResult.success) {
      return res.status(400).json({
        success: false,
        message: "Invalid room data",
        errors: validationResult.error.errors,
      });
    }

    const roomData = validationResult.data;
    const query = req.scope.resolve(ContainerRegistrationKeys.QUERY);

    // Check if hotel exists
    const { data: hotels } = await query.graph({
      entity: "hotel",
      filters: {
        id: roomData.hotel_id,
      },
      fields: ["id", "name"],
    });

    if (!hotels || hotels.length === 0) {
      return res.status(400).json({
        success: false,
        message: `Hotel with ID '${roomData.hotel_id}' not found`,
      });
    }

    // Check if room config exists (as a product)
    const { data: roomConfigProducts } = await query.graph({
      entity: "product",
      filters: {
        id: roomData.room_config_id,
      },
      fields: ["id", "title", "metadata"],
    });

    if (!roomConfigProducts || roomConfigProducts.length === 0) {
      return res.status(400).json({
        success: false,
        message: `Room config with ID '${roomData.room_config_id}' not found`,
      });
    }

    const roomConfig = roomConfigProducts[0];

    // Verify room config belongs to the specified hotel
    if (roomConfig.metadata?.hotel_id !== roomData.hotel_id) {
      return res.status(400).json({
        success: false,
        message: `Room config '${roomData.room_config_id}' does not belong to hotel '${roomData.hotel_id}'`,
      });
    }

    // Check if room with same room number already exists for this hotel
    const { data: existingRooms } = await query.graph({
      entity: "product_variant",
      filters: {
        product_id: roomData.room_config_id,
      },
      fields: ["id", "title", "metadata"],
    });

    const existingRoom = existingRooms?.find(room => 
      room.metadata?.room_number === roomData.room_number &&
      room.metadata?.hotel_id === roomData.hotel_id
    );

    if (existingRoom) {
      return res.status(409).json({
        success: false,
        message: `Room with number '${roomData.room_number}' already exists for this hotel`,
        existing_room: {
          id: existingRoom.id,
          name: existingRoom.title,
          room_number: existingRoom.metadata?.room_number,
          hotel_id: existingRoom.metadata?.hotel_id,
        },
      });
    }

    // Prepare additional metadata for ETL tracking
    const additionalMetadata: Record<string, any> = {
      created_via: "etl",
      created_at: new Date().toISOString(),
    };

    // Add flat ETL fields to metadata
    if (roomData.salesforce_id) additionalMetadata.salesforce_id = roomData.salesforce_id;
    if (roomData.import_batch) additionalMetadata.import_batch = roomData.import_batch;
    if (roomData.room_view) additionalMetadata.room_view = roomData.room_view;
    if (roomData.has_balcony !== undefined) additionalMetadata.has_balcony = roomData.has_balcony;

    // Create room using workflow
    const { result: room } = await CreateRoomWorkflow(req.scope).run({
      input: {
        name: roomData.name,
        room_number: roomData.room_number,
        status: roomData.status,
        floor: roomData.floor,
        notes: roomData.notes,
        is_active: roomData.is_active,
        left_room: roomData.left_room,
        opposite_room: roomData.opposite_room,
        connected_room: roomData.connected_room,
        right_room: roomData.right_room,
        room_config_id: roomData.room_config_id,
        hotel_id: roomData.hotel_id,
        price: roomData.price,
        currency_code: roomData.currency_code,
        availability_type: roomData.availability_type,
        additional_metadata: additionalMetadata,
      },
    });

    return res.status(201).json({
      success: true,
      message: "Room created successfully",
      data: room,
    });

  } catch (error) {
    console.error("Error creating room via ETL:", error);
    return res.status(500).json({
      success: false,
      message: "Failed to create room",
      error: error instanceof Error ? error.message : "Unknown error",
    });
  }
};
