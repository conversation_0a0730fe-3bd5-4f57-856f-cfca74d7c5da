import {
  MedusaRequest,
  MedusaResponse,
} from "@camped-ai/framework/http";
import { ContainerRegistrationKeys } from "@camped-ai/framework/utils";
import { z } from "zod";
import { CreateHotelWorkflow } from "../../../../../workflows/hotel-management/hotel/create-hotel";

// Validation schema for creating a single hotel via ETL
const StoreCreateHotelSchema = z.object({
  name: z.string().min(1, "Name is required"),
  handle: z.string().optional(), // Will be auto-generated from name if not provided
  description: z.string().optional(),
  is_active: z.boolean().default(true),
  website: z.string().optional().or(z.literal("")).or(z.null()).transform(val => {
    if (val === null || val === undefined) return "";
    if (val === "") return "";
    // Basic URL validation - just check if it looks like a URL
    if (val.includes("://") || val.startsWith("www.") || val.includes(".")) {
      return val;
    }
    return ""; // Invalid URL format, convert to empty string
  }),
  email: z.string().optional().or(z.literal("")).or(z.null()).transform(val => {
    if (val === null || val === undefined) return "";
    if (val === "") return "";
    // Basic email validation - just check if it contains @
    if (val.includes("@")) {
      return val;
    }
    return ""; // Invalid email format, convert to empty string
  }),
  destination_id: z.string().min(1, "Destination ID is required"),
  rating: z.number().min(0).max(5).optional(),
  total_reviews: z.number().min(0).optional(),
  notes: z.string().optional(),
  location: z.string().optional(),
  address: z.string().optional(),
  phone_number: z.string().optional(),
  timezone: z.string().optional(),
  available_languages: z.array(z.string()).optional(),
  tax_type: z.string().optional(),
  tax_number: z.string().optional(),
  tags: z.array(z.string()).optional(),
  amenities: z.array(z.string()).optional(),
  rules: z.array(z.string()).optional(),
  safety_measures: z.array(z.string()).optional(),
  currency: z.string().optional(),
  check_in_time: z.string().optional(),
  check_out_time: z.string().optional(),
  is_featured: z.boolean().default(false),
  is_pets_allowed: z.boolean().default(false),
  // ETL specific flat fields
  salesforce_id: z.string().optional(),
  import_batch: z.string().optional(),
  star_rating: z.string().optional(),
  property_type: z.string().optional(),
});

// Validation schema for bulk hotel creation
const StoreBulkCreateHotelsSchema = z.object({
  hotels: z.array(StoreCreateHotelSchema).min(1, "At least one hotel is required").max(100, "Maximum 100 hotels per batch"),
});

export type StoreCreateHotelType = z.infer<typeof StoreCreateHotelSchema>;
export type StoreBulkCreateHotelsType = z.infer<typeof StoreBulkCreateHotelsSchema>;

// Helper function to generate handle from name and destination
function generateHandle(name: string, destinationName?: string): string {
  let handle = name
    .toLowerCase()
    .replace(/[^a-z0-9\s-]/g, '') // Remove special characters
    .replace(/\s+/g, '-') // Replace spaces with hyphens
    .replace(/-+/g, '-') // Replace multiple hyphens with single
    .trim();
  
  // If destination name is provided, append it to make handle unique
  if (destinationName) {
    const destHandle = destinationName
      .toLowerCase()
      .replace(/[^a-z0-9\s-]/g, '')
      .replace(/\s+/g, '-')
      .replace(/-+/g, '-')
      .trim();
    handle = `${handle}-${destHandle}`;
  }
  
  return handle;
}

/**
 * POST /store/etl/hotels/bulk
 * 
 * Create multiple hotels via ETL in a single transaction
 */
export const POST = async (
  req: MedusaRequest<StoreBulkCreateHotelsType>,
  res: MedusaResponse
) => {
  try {
    // Validate request body
    const validationResult = StoreBulkCreateHotelsSchema.safeParse(req.body);
    if (!validationResult.success) {
      return res.status(400).json({
        success: false,
        message: "Invalid bulk hotel data",
        errors: validationResult.error.errors,
      });
    }

    const { hotels } = validationResult.data;
    const query = req.scope.resolve(ContainerRegistrationKeys.QUERY);

    // Prepare results tracking
    const results: Array<{
      index: number;
      success: boolean;
      data?: any;
      message?: string;
      errors?: any[];
    }> = [];

    let created = 0;
    let skipped = 0;
    let failed = 0;

    // Get all destinations for validation and handle generation
    const { data: allDestinations } = await query.graph({
      entity: "destination",
      fields: ["id", "name"],
    });

    const destinationMap = new Map(
      allDestinations?.map(dest => [dest.id, dest]) || []
    );

    // Process each hotel
    for (let i = 0; i < hotels.length; i++) {
      const hotelData = hotels[i];
      
      try {
        // Check if destination exists
        const destination = destinationMap.get(hotelData.destination_id);
        if (!destination) {
          results.push({
            index: i,
            success: false,
            message: `Destination with ID '${hotelData.destination_id}' not found`,
            errors: [`Invalid destination_id: ${hotelData.destination_id}`],
          });
          failed++;
          continue;
        }

        // Generate handle if not provided
        if (!hotelData.handle) {
          hotelData.handle = generateHandle(hotelData.name, destination.name);
        }

        // Check if hotel with same handle already exists
        const { data: existingHotels } = await query.graph({
          entity: "hotel",
          filters: {
            handle: hotelData.handle,
          },
          fields: ["id", "name", "handle"],
        });

        if (existingHotels && existingHotels.length > 0) {
          // Skip if already exists
          results.push({
            index: i,
            success: true,
            data: existingHotels[0],
            message: `Hotel with handle '${hotelData.handle}' already exists - skipped`,
          });
          skipped++;
          continue;
        }

        // Prepare metadata for ETL tracking
        const metadata: Record<string, any> = {
          created_via: "etl_bulk",
          created_at: new Date().toISOString(),
          batch_index: i,
        };

        // Add flat ETL fields to metadata
        if (hotelData.salesforce_id) metadata.salesforce_id = hotelData.salesforce_id;
        if (hotelData.import_batch) metadata.import_batch = hotelData.import_batch;
        if (hotelData.star_rating) metadata.star_rating = hotelData.star_rating;
        if (hotelData.property_type) metadata.property_type = hotelData.property_type;

        // Create hotel using workflow
        const { result: hotel } = await CreateHotelWorkflow(req.scope).run({
          input: {
            name: hotelData.name,
            handle: hotelData.handle,
            description: hotelData.description,
            is_active: hotelData.is_active,
            website: hotelData.website,
            email: hotelData.email,
            destination_id: hotelData.destination_id,
            rating: hotelData.rating,
            total_reviews: hotelData.total_reviews,
            notes: hotelData.notes,
            location: hotelData.location,
            address: hotelData.address,
            phone_number: hotelData.phone_number,
            timezone: hotelData.timezone,
            available_languages: hotelData.available_languages,
            tax_type: hotelData.tax_type,
            tax_number: hotelData.tax_number,
            tags: hotelData.tags,
            amenities: hotelData.amenities,
            rules: hotelData.rules,
            safety_measures: hotelData.safety_measures,
            currency: hotelData.currency,
            check_in_time: hotelData.check_in_time,
            check_out_time: hotelData.check_out_time,
            is_featured: hotelData.is_featured,
            is_pets_allowed: hotelData.is_pets_allowed,
            metadata: metadata,
          },
        });

        results.push({
          index: i,
          success: true,
          data: hotel,
          message: "Hotel created successfully",
        });
        created++;

      } catch (error) {
        console.error(`Error creating hotel at index ${i}:`, error);
        results.push({
          index: i,
          success: false,
          message: `Failed to create hotel: ${error instanceof Error ? error.message : "Unknown error"}`,
          errors: [error instanceof Error ? error.message : "Unknown error"],
        });
        failed++;
      }
    }

    const summary = {
      total: hotels.length,
      created,
      skipped,
      failed,
    };

    return res.status(created > 0 || skipped > 0 ? 201 : 400).json({
      success: failed === 0,
      message: `Bulk hotel creation completed. Created: ${created}, Skipped: ${skipped}, Failed: ${failed}`,
      summary,
      results,
    });

  } catch (error) {
    console.error("Error in bulk hotel creation:", error);
    return res.status(500).json({
      success: false,
      message: "Failed to process bulk hotel creation",
      error: error instanceof Error ? error.message : "Unknown error",
    });
  }
};
