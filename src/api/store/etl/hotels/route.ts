import {
  MedusaRequest,
  MedusaResponse,
} from "@camped-ai/framework/http";
import { ContainerRegistrationKeys } from "@camped-ai/framework/utils";
import { z } from "zod";
import { CreateHotelWorkflow } from "../../../../workflows/hotel-management/hotel/create-hotel";

// Validation schema for creating a single hotel via ETL
const StoreCreateHotelSchema = z.object({
  name: z.string().min(1, "Name is required"),
  handle: z.string().optional(), // Will be auto-generated from name if not provided
  description: z.string().optional(),
  is_active: z.boolean().default(true),
  website: z.string().optional().or(z.literal("")).or(z.null()).transform(val => {
    if (val === null || val === undefined) return "";
    if (val === "") return "";
    // Basic URL validation - just check if it looks like a URL
    if (val.includes("://") || val.startsWith("www.") || val.includes(".")) {
      return val;
    }
    return ""; // Invalid URL format, convert to empty string
  }),
  email: z.string().optional().or(z.literal("")).or(z.null()).transform(val => {
    if (val === null || val === undefined) return "";
    if (val === "") return "";
    // Basic email validation - just check if it contains @
    if (val.includes("@")) {
      return val;
    }
    return ""; // Invalid email format, convert to empty string
  }),
  destination_id: z.string().min(1, "Destination ID is required"),
  rating: z.number().min(0).max(5).optional(),
  total_reviews: z.number().min(0).optional(),
  notes: z.string().optional(),
  location: z.string().optional(),
  address: z.string().optional(),
  phone_number: z.string().optional(),
  timezone: z.string().optional(),
  available_languages: z.array(z.string()).optional(),
  tax_type: z.string().optional(),
  tax_number: z.string().optional(),
  tags: z.array(z.string()).optional(),
  amenities: z.array(z.string()).optional(),
  rules: z.array(z.string()).optional(),
  safety_measures: z.array(z.string()).optional(),
  currency: z.string().optional(),
  check_in_time: z.string().optional(),
  check_out_time: z.string().optional(),
  is_featured: z.boolean().default(false),
  is_pets_allowed: z.boolean().default(false),
  // ETL specific flat fields
  salesforce_id: z.string().optional(),
  import_batch: z.string().optional(),
  star_rating: z.string().optional(),
  property_type: z.string().optional(),
});

export type StoreCreateHotelType = z.infer<typeof StoreCreateHotelSchema>;

// Helper function to generate handle from name
function generateHandle(name: string): string {
  return name
    .toLowerCase()
    .replace(/[^a-z0-9\s-]/g, '') // Remove special characters
    .replace(/\s+/g, '-') // Replace spaces with hyphens
    .replace(/-+/g, '-') // Replace multiple hyphens with single
    .trim();
}

/**
 * POST /store/etl/hotels
 * 
 * Create a single hotel via ETL
 */
export const POST = async (
  req: MedusaRequest<StoreCreateHotelType>,
  res: MedusaResponse
) => {
  try {
    // Validate request body
    const validationResult = StoreCreateHotelSchema.safeParse(req.body);
    if (!validationResult.success) {
      return res.status(400).json({
        success: false,
        message: "Invalid hotel data",
        errors: validationResult.error.errors,
      });
    }

    const hotelData = validationResult.data;

    // Generate handle if not provided
    if (!hotelData.handle) {
      hotelData.handle = generateHandle(hotelData.name);
    }

    const query = req.scope.resolve(ContainerRegistrationKeys.QUERY);

    // Check if destination exists
    const { data: destinations } = await query.graph({
      entity: "destination",
      filters: {
        id: hotelData.destination_id,
      },
      fields: ["id", "name"],
    });

    if (!destinations || destinations.length === 0) {
      return res.status(400).json({
        success: false,
        message: `Destination with ID '${hotelData.destination_id}' not found`,
      });
    }

    // Check if hotel with same handle already exists
    const { data: existingHotels } = await query.graph({
      entity: "hotel",
      filters: {
        handle: hotelData.handle,
      },
      fields: ["id", "name", "handle"],
    });

    if (existingHotels && existingHotels.length > 0) {
      return res.status(409).json({
        success: false,
        message: `Hotel with handle '${hotelData.handle}' already exists`,
        existing_hotel: existingHotels[0],
      });
    }

    // Prepare metadata for ETL tracking
    const metadata: Record<string, any> = {
      created_via: "etl",
      created_at: new Date().toISOString(),
    };

    // Add flat ETL fields to metadata
    if (hotelData.salesforce_id) metadata.salesforce_id = hotelData.salesforce_id;
    if (hotelData.import_batch) metadata.import_batch = hotelData.import_batch;
    if (hotelData.star_rating) metadata.star_rating = hotelData.star_rating;
    if (hotelData.property_type) metadata.property_type = hotelData.property_type;

    // Create hotel using workflow
    const { result: hotel } = await CreateHotelWorkflow(req.scope).run({
      input: {
        name: hotelData.name,
        handle: hotelData.handle,
        description: hotelData.description,
        is_active: hotelData.is_active,
        website: hotelData.website,
        email: hotelData.email,
        destination_id: hotelData.destination_id,
        rating: hotelData.rating,
        total_reviews: hotelData.total_reviews,
        notes: hotelData.notes,
        location: hotelData.location,
        address: hotelData.address,
        phone_number: hotelData.phone_number,
        timezone: hotelData.timezone,
        available_languages: hotelData.available_languages,
        tax_type: hotelData.tax_type,
        tax_number: hotelData.tax_number,
        tags: hotelData.tags,
        amenities: hotelData.amenities,
        rules: hotelData.rules,
        safety_measures: hotelData.safety_measures,
        currency: hotelData.currency,
        check_in_time: hotelData.check_in_time,
        check_out_time: hotelData.check_out_time,
        is_featured: hotelData.is_featured,
        is_pets_allowed: hotelData.is_pets_allowed,
        metadata: metadata,
      },
    });

    return res.status(201).json({
      success: true,
      message: "Hotel created successfully",
      data: hotel,
    });

  } catch (error) {
    console.error("Error creating hotel via ETL:", error);
    return res.status(500).json({
      success: false,
      message: "Failed to create hotel",
      error: error instanceof Error ? error.message : "Unknown error",
    });
  }
};
