import {
  MedusaRequest,
  MedusaResponse,
} from "@camped-ai/framework/http";
import { ContainerRegistrationKeys } from "@camped-ai/framework/utils";
import { z } from "zod";
import { CreateDestinationWorkflow } from "../../../../workflows/hotel-management/destination/create-destination";

// Validation schema for creating a single destination via ETL
const StoreCreateDestinationSchema = z.object({
  name: z.string().min(1, "Name is required"),
  handle: z.string().optional(), // Will be auto-generated from name if not provided
  description: z.string().optional().or(z.null()).transform(val => val === null ? undefined : val),
  is_active: z.boolean().default(true),
  country: z.string().min(1, "Country is required"),
  location: z.string().optional().or(z.null()).transform(val => val === null ? undefined : val),
  tags: z.array(z.string()).optional().or(z.null()).transform(val => val === null ? undefined : val),
  is_featured: z.boolean().default(false),
  ai_content: z.string().optional().or(z.null()).transform(val => val === null ? undefined : val),
  // ETL specific flat fields
  salesforce_id: z.string().optional().or(z.null()).transform(val => val === null ? undefined : val),
  import_batch: z.string().optional().or(z.null()).transform(val => val === null ? undefined : val),
  region: z.string().optional().or(z.null()).transform(val => val === null ? undefined : val),
  timezone: z.string().optional().or(z.null()).transform(val => val === null ? undefined : val),
});

export type StoreCreateDestinationType = z.infer<typeof StoreCreateDestinationSchema>;

// Helper function to generate handle from name
function generateHandle(name: string): string {
  return name
    .toLowerCase()
    .replace(/[^a-z0-9\s-]/g, '') // Remove special characters
    .replace(/\s+/g, '-') // Replace spaces with hyphens
    .replace(/-+/g, '-') // Replace multiple hyphens with single
    .trim();
}

/**
 * POST /store/etl/destinations
 * 
 * Create a single destination via ETL
 */
export const POST = async (
  req: MedusaRequest<StoreCreateDestinationType>,
  res: MedusaResponse
) => {
  try {
    // Validate request body
    const validationResult = StoreCreateDestinationSchema.safeParse(req.body);
    if (!validationResult.success) {
      return res.status(400).json({
        success: false,
        message: "Invalid destination data",
        errors: validationResult.error.errors,
      });
    }

    const destinationData = validationResult.data;

    // Generate handle if not provided
    if (!destinationData.handle) {
      destinationData.handle = generateHandle(destinationData.name);
    }

    // Check if destination with same handle already exists
    const query = req.scope.resolve(ContainerRegistrationKeys.QUERY);
    const { data: existingDestinations } = await query.graph({
      entity: "destination",
      filters: {
        handle: destinationData.handle,
      },
      fields: ["id", "name", "handle"],
    });

    if (existingDestinations && existingDestinations.length > 0) {
      return res.status(409).json({
        success: false,
        message: `Destination with handle '${destinationData.handle}' already exists`,
        existing_destination: existingDestinations[0],
      });
    }

    // Prepare metadata for ETL tracking
    const metadata: Record<string, any> = {
      created_via: "etl",
      created_at: new Date().toISOString(),
    };

    // Add flat ETL fields to metadata
    if (destinationData.salesforce_id) metadata.salesforce_id = destinationData.salesforce_id;
    if (destinationData.import_batch) metadata.import_batch = destinationData.import_batch;
    if (destinationData.region) metadata.region = destinationData.region;
    if (destinationData.timezone) metadata.timezone = destinationData.timezone;

    // Create destination using workflow
    const { result: destination } = await CreateDestinationWorkflow(req.scope).run({
      input: {
        name: destinationData.name,
        handle: destinationData.handle,
        description: destinationData.description,
        is_active: destinationData.is_active,
        country: destinationData.country,
        location: destinationData.location,
        tags: destinationData.tags,
        is_featured: destinationData.is_featured,
        ai_content: destinationData.ai_content,
        metadata: metadata,
      },
    });

    return res.status(201).json({
      success: true,
      message: "Destination created successfully",
      data: destination,
    });

  } catch (error) {
    console.error("Error creating destination via ETL:", error);
    return res.status(500).json({
      success: false,
      message: "Failed to create destination",
      error: error instanceof Error ? error.message : "Unknown error",
    });
  }
};
