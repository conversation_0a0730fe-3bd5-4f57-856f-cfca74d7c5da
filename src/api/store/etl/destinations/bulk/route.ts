import {
  MedusaRequest,
  MedusaResponse,
} from "@camped-ai/framework/http";
import { ContainerRegistrationKeys } from "@camped-ai/framework/utils";
import { z } from "zod";
import { CreateDestinationWorkflow } from "../../../../../workflows/hotel-management/destination/create-destination";

// Validation schema for creating a single destination via ETL
const StoreCreateDestinationSchema = z.object({
  name: z.string().min(1, "Name is required"),
  handle: z.string().optional(), // Will be auto-generated from name if not provided
  description: z.string().optional().or(z.null()).transform(val => val === null ? undefined : val),
  is_active: z.boolean().default(true),
  country: z.string().min(1, "Country is required"),
  location: z.string().optional().or(z.null()).transform(val => val === null ? undefined : val),
  tags: z.array(z.string()).optional().or(z.null()).transform(val => val === null ? undefined : val),
  is_featured: z.boolean().default(false),
  ai_content: z.string().optional().or(z.null()).transform(val => val === null ? undefined : val),
  // ETL specific flat fields
  salesforce_id: z.string().optional().or(z.null()).transform(val => val === null ? undefined : val),
  import_batch: z.string().optional().or(z.null()).transform(val => val === null ? undefined : val),
  region: z.string().optional().or(z.null()).transform(val => val === null ? undefined : val),
  timezone: z.string().optional().or(z.null()).transform(val => val === null ? undefined : val),
});

// Validation schema for bulk destination creation
const StoreBulkCreateDestinationsSchema = z.object({
  destinations: z.array(StoreCreateDestinationSchema).min(1, "At least one destination is required").max(100, "Maximum 100 destinations per batch"),
});

export type StoreCreateDestinationType = z.infer<typeof StoreCreateDestinationSchema>;
export type StoreBulkCreateDestinationsType = z.infer<typeof StoreBulkCreateDestinationsSchema>;

// Helper function to generate handle from name
function generateHandle(name: string): string {
  return name
    .toLowerCase()
    .replace(/[^a-z0-9\s-]/g, '') // Remove special characters
    .replace(/\s+/g, '-') // Replace spaces with hyphens
    .replace(/-+/g, '-') // Replace multiple hyphens with single
    .trim();
}

/**
 * POST /store/etl/destinations/bulk
 * 
 * Create multiple destinations via ETL in a single transaction
 */
export const POST = async (
  req: MedusaRequest<StoreBulkCreateDestinationsType>,
  res: MedusaResponse
) => {
  try {
    // Validate request body
    const validationResult = StoreBulkCreateDestinationsSchema.safeParse(req.body);
    if (!validationResult.success) {
      return res.status(400).json({
        success: false,
        message: "Invalid bulk destination data",
        errors: validationResult.error.errors,
      });
    }

    const { destinations } = validationResult.data;
    const query = req.scope.resolve(ContainerRegistrationKeys.QUERY);

    // Prepare results tracking
    const results: Array<{
      index: number;
      success: boolean;
      data?: any;
      message?: string;
      errors?: any[];
    }> = [];

    let created = 0;
    let skipped = 0;
    let failed = 0;

    // Process each destination
    for (let i = 0; i < destinations.length; i++) {
      const destinationData = destinations[i];
      
      try {
        // Generate handle if not provided
        if (!destinationData.handle) {
          destinationData.handle = generateHandle(destinationData.name);
        }

        // Check if destination with same handle already exists
        const { data: existingDestinations } = await query.graph({
          entity: "destination",
          filters: {
            handle: destinationData.handle,
          },
          fields: ["id", "name", "handle"],
        });

        if (existingDestinations && existingDestinations.length > 0) {
          // Skip if already exists
          results.push({
            index: i,
            success: true,
            data: existingDestinations[0],
            message: `Destination with handle '${destinationData.handle}' already exists - skipped`,
          });
          skipped++;
          continue;
        }

        // Prepare metadata for ETL tracking
        const metadata: Record<string, any> = {
          created_via: "etl_bulk",
          created_at: new Date().toISOString(),
          batch_index: i,
        };

        // Add flat ETL fields to metadata
        if (destinationData.salesforce_id) metadata.salesforce_id = destinationData.salesforce_id;
        if (destinationData.import_batch) metadata.import_batch = destinationData.import_batch;
        if (destinationData.region) metadata.region = destinationData.region;
        if (destinationData.timezone) metadata.timezone = destinationData.timezone;

        // Create destination using workflow
        const { result: destination } = await CreateDestinationWorkflow(req.scope).run({
          input: {
            name: destinationData.name,
            handle: destinationData.handle,
            description: destinationData.description,
            is_active: destinationData.is_active,
            country: destinationData.country,
            location: destinationData.location,
            tags: destinationData.tags,
            is_featured: destinationData.is_featured,
            ai_content: destinationData.ai_content,
            metadata: metadata,
          },
        });

        results.push({
          index: i,
          success: true,
          data: destination,
          message: "Destination created successfully",
        });
        created++;

      } catch (error) {
        console.error(`Error creating destination at index ${i}:`, error);
        results.push({
          index: i,
          success: false,
          message: `Failed to create destination: ${error instanceof Error ? error.message : "Unknown error"}`,
          errors: [error instanceof Error ? error.message : "Unknown error"],
        });
        failed++;
      }
    }

    const summary = {
      total: destinations.length,
      created,
      skipped,
      failed,
    };

    return res.status(created > 0 || skipped > 0 ? 201 : 400).json({
      success: failed === 0,
      message: `Bulk destination creation completed. Created: ${created}, Skipped: ${skipped}, Failed: ${failed}`,
      summary,
      results,
    });

  } catch (error) {
    console.error("Error in bulk destination creation:", error);
    return res.status(500).json({
      success: false,
      message: "Failed to process bulk destination creation",
      error: error instanceof Error ? error.message : "Unknown error",
    });
  }
};
