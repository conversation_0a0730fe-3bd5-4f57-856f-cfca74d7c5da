import {
  MedusaRequest,
  MedusaResponse,
} from "@camped-ai/framework/http";
import { ContainerRegistrationKeys, Modu<PERSON> } from "@camped-ai/framework/utils";
import { z } from "zod";

// Validation schema for creating a single room config via ETL
const StoreCreateRoomConfigSchema = z.object({
  name: z.string().min(1, "Name is required"),
  type: z.string().min(1, "Type is required"),
  hotel_id: z.string().min(1, "Hotel ID is required"),
  description: z.string().optional(),
  room_size: z.string().optional(),
  amenities: z.array(z.string()).optional(),
  bed_type: z.string().optional(),
  max_extra_beds: z.number().min(0).default(0),
  max_cots: z.number().min(0).default(0),
  max_adults: z.number().min(1).default(1),
  max_adults_beyond_capacity: z.number().min(0).default(0),
  max_children: z.number().min(0).default(0),
  max_infants: z.number().min(0).default(0),
  max_occupancy: z.number().min(1).default(1),
  // ETL specific flat fields
  salesforce_id: z.string().optional(),
  import_batch: z.string().optional(),
  view_type: z.string().optional(),
  floor_level: z.string().optional(),
});

export type StoreCreateRoomConfigType = z.infer<typeof StoreCreateRoomConfigSchema>;

/**
 * POST /store/etl/room-configs
 * 
 * Create a single room configuration via ETL
 */
export const POST = async (
  req: MedusaRequest<StoreCreateRoomConfigType>,
  res: MedusaResponse
) => {
  try {
    // Validate request body
    const validationResult = StoreCreateRoomConfigSchema.safeParse(req.body);
    if (!validationResult.success) {
      return res.status(400).json({
        success: false,
        message: "Invalid room config data",
        errors: validationResult.error.errors,
      });
    }

    const roomConfigData = validationResult.data;
    const query = req.scope.resolve(ContainerRegistrationKeys.QUERY);

    // Check if hotel exists
    const { data: hotels } = await query.graph({
      entity: "hotel",
      filters: {
        id: roomConfigData.hotel_id,
      },
      fields: ["id", "name"],
    });

    if (!hotels || hotels.length === 0) {
      return res.status(400).json({
        success: false,
        message: `Hotel with ID '${roomConfigData.hotel_id}' not found`,
      });
    }

    // Check if room config with same name already exists for this hotel
    const productModuleService = req.scope.resolve(Modules.PRODUCT);
    const existingProducts = await productModuleService.listProducts({
      title: roomConfigData.name,
    });

    // Filter by hotel_id in metadata
    const existingRoomConfig = existingProducts.find(product => 
      product.metadata?.hotel_id === roomConfigData.hotel_id
    );

    if (existingRoomConfig) {
      return res.status(409).json({
        success: false,
        message: `Room config with name '${roomConfigData.name}' already exists for this hotel`,
        existing_room_config: {
          id: existingRoomConfig.id,
          name: existingRoomConfig.title,
          hotel_id: existingRoomConfig.metadata?.hotel_id,
        },
      });
    }

    // Prepare metadata for ETL tracking
    const metadata: Record<string, any> = {
      type: roomConfigData.type,
      room_size: roomConfigData.room_size || "",
      bed_type: roomConfigData.bed_type || "",
      max_extra_beds: roomConfigData.max_extra_beds,
      max_cots: roomConfigData.max_cots,
      max_adults: roomConfigData.max_adults,
      max_adults_beyond_capacity: roomConfigData.max_adults_beyond_capacity,
      max_children: roomConfigData.max_children,
      max_infants: roomConfigData.max_infants,
      max_occupancy: roomConfigData.max_occupancy,
      amenities: roomConfigData.amenities || [],
      hotel_id: roomConfigData.hotel_id,
      created_via: "etl",
      created_at: new Date().toISOString(),
    };

    // Add flat ETL fields to metadata
    if (roomConfigData.salesforce_id) metadata.salesforce_id = roomConfigData.salesforce_id;
    if (roomConfigData.import_batch) metadata.import_batch = roomConfigData.import_batch;
    if (roomConfigData.view_type) metadata.view_type = roomConfigData.view_type;
    if (roomConfigData.floor_level) metadata.floor_level = roomConfigData.floor_level;

    // Create room config as a product
    const product = await productModuleService.createProducts({
      title: roomConfigData.name,
      description: roomConfigData.description || "",
      is_giftcard: false,
      status: "published",
      metadata,
      handle: `room-config-${roomConfigData.hotel_id}-${Date.now()}`,
    });

    return res.status(201).json({
      success: true,
      message: "Room config created successfully",
      data: {
        id: product.id,
        name: product.title,
        type: roomConfigData.type,
        description: product.description,
        hotel_id: roomConfigData.hotel_id,
        room_size: roomConfigData.room_size,
        bed_type: roomConfigData.bed_type,
        max_extra_beds: roomConfigData.max_extra_beds,
        max_cots: roomConfigData.max_cots,
        max_adults: roomConfigData.max_adults,
        max_adults_beyond_capacity: roomConfigData.max_adults_beyond_capacity,
        max_children: roomConfigData.max_children,
        max_infants: roomConfigData.max_infants,
        max_occupancy: roomConfigData.max_occupancy,
        amenities: roomConfigData.amenities,
        metadata: product.metadata,
      },
    });

  } catch (error) {
    console.error("Error creating room config via ETL:", error);
    return res.status(500).json({
      success: false,
      message: "Failed to create room config",
      error: error instanceof Error ? error.message : "Unknown error",
    });
  }
};
