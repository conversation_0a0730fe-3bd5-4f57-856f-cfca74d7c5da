import {
  MedusaRequest,
  MedusaResponse,
} from "@camped-ai/framework/http";
import { ContainerRegistrationKeys, Modu<PERSON> } from "@camped-ai/framework/utils";
import { z } from "zod";

// Validation schema for creating a single room config via ETL
const StoreCreateRoomConfigSchema = z.object({
  name: z.string().min(1, "Name is required"),
  type: z.string().min(1, "Type is required"),
  hotel_id: z.string().min(1, "Hotel ID is required"),
  description: z.string().optional(),
  room_size: z.string().optional(),
  amenities: z.array(z.string()).optional(),
  bed_type: z.string().optional(),
  max_extra_beds: z.number().min(0).default(0),
  max_cots: z.number().min(0).default(0),
  max_adults: z.number().min(1).default(1),
  max_adults_beyond_capacity: z.number().min(0).default(0),
  max_children: z.number().min(0).default(0),
  max_infants: z.number().min(0).default(0),
  max_occupancy: z.number().min(1).default(1),
  // ETL specific flat fields
  salesforce_id: z.string().optional(),
  import_batch: z.string().optional(),
  view_type: z.string().optional(),
  floor_level: z.string().optional(),
});

// Validation schema for bulk room config creation
const StoreBulkCreateRoomConfigsSchema = z.object({
  room_configs: z.array(StoreCreateRoomConfigSchema).min(1, "At least one room config is required").max(100, "Maximum 100 room configs per batch"),
});

export type StoreCreateRoomConfigType = z.infer<typeof StoreCreateRoomConfigSchema>;
export type StoreBulkCreateRoomConfigsType = z.infer<typeof StoreBulkCreateRoomConfigsSchema>;

/**
 * POST /store/etl/room-configs/bulk
 * 
 * Create multiple room configurations via ETL in a single transaction
 */
export const POST = async (
  req: MedusaRequest<StoreBulkCreateRoomConfigsType>,
  res: MedusaResponse
) => {
  try {
    // Validate request body
    const validationResult = StoreBulkCreateRoomConfigsSchema.safeParse(req.body);
    if (!validationResult.success) {
      return res.status(400).json({
        success: false,
        message: "Invalid bulk room config data",
        errors: validationResult.error.errors,
      });
    }

    const { room_configs } = validationResult.data;
    const query = req.scope.resolve(ContainerRegistrationKeys.QUERY);
    const productModuleService = req.scope.resolve(Modules.PRODUCT);

    // Prepare results tracking
    const results: Array<{
      index: number;
      success: boolean;
      data?: any;
      message?: string;
      errors?: any[];
    }> = [];

    let created = 0;
    let skipped = 0;
    let failed = 0;

    // Get all hotels for validation
    const { data: allHotels } = await query.graph({
      entity: "hotel",
      fields: ["id", "name"],
    });

    const hotelMap = new Map(
      allHotels?.map(hotel => [hotel.id, hotel]) || []
    );

    // Get existing room configs to check for duplicates
    const existingProducts = await productModuleService.listProducts({});
    const existingRoomConfigs = existingProducts.filter(product => 
      product.metadata?.hotel_id
    );

    // Process each room config
    for (let i = 0; i < room_configs.length; i++) {
      const roomConfigData = room_configs[i];
      
      try {
        // Check if hotel exists
        const hotel = hotelMap.get(roomConfigData.hotel_id);
        if (!hotel) {
          results.push({
            index: i,
            success: false,
            message: `Hotel with ID '${roomConfigData.hotel_id}' not found`,
            errors: [`Invalid hotel_id: ${roomConfigData.hotel_id}`],
          });
          failed++;
          continue;
        }

        // Check if room config with same name already exists for this hotel
        const existingRoomConfig = existingRoomConfigs.find(product => 
          product.title === roomConfigData.name && 
          product.metadata?.hotel_id === roomConfigData.hotel_id
        );

        if (existingRoomConfig) {
          // Skip if already exists
          results.push({
            index: i,
            success: true,
            data: {
              id: existingRoomConfig.id,
              name: existingRoomConfig.title,
              hotel_id: existingRoomConfig.metadata?.hotel_id,
            },
            message: `Room config '${roomConfigData.name}' already exists for hotel - skipped`,
          });
          skipped++;
          continue;
        }

        // Prepare metadata for ETL tracking
        const metadata: Record<string, any> = {
          type: roomConfigData.type,
          room_size: roomConfigData.room_size || "",
          bed_type: roomConfigData.bed_type || "",
          max_extra_beds: roomConfigData.max_extra_beds,
          max_cots: roomConfigData.max_cots,
          max_adults: roomConfigData.max_adults,
          max_adults_beyond_capacity: roomConfigData.max_adults_beyond_capacity,
          max_children: roomConfigData.max_children,
          max_infants: roomConfigData.max_infants,
          max_occupancy: roomConfigData.max_occupancy,
          amenities: roomConfigData.amenities || [],
          hotel_id: roomConfigData.hotel_id,
          created_via: "etl_bulk",
          created_at: new Date().toISOString(),
          batch_index: i,
        };

        // Add flat ETL fields to metadata
        if (roomConfigData.salesforce_id) metadata.salesforce_id = roomConfigData.salesforce_id;
        if (roomConfigData.import_batch) metadata.import_batch = roomConfigData.import_batch;
        if (roomConfigData.view_type) metadata.view_type = roomConfigData.view_type;
        if (roomConfigData.floor_level) metadata.floor_level = roomConfigData.floor_level;

        // Create room config as a product
        const product = await productModuleService.createProducts({
          title: roomConfigData.name,
          description: roomConfigData.description || "",
          is_giftcard: false,
          status: "published",
          metadata,
          handle: `room-config-${roomConfigData.hotel_id}-${Date.now()}-${i}`,
        });

        const responseData = {
          id: product.id,
          name: product.title,
          type: roomConfigData.type,
          description: product.description,
          hotel_id: roomConfigData.hotel_id,
          room_size: roomConfigData.room_size,
          bed_type: roomConfigData.bed_type,
          max_extra_beds: roomConfigData.max_extra_beds,
          max_cots: roomConfigData.max_cots,
          max_adults: roomConfigData.max_adults,
          max_adults_beyond_capacity: roomConfigData.max_adults_beyond_capacity,
          max_children: roomConfigData.max_children,
          max_infants: roomConfigData.max_infants,
          max_occupancy: roomConfigData.max_occupancy,
          amenities: roomConfigData.amenities,
          metadata: product.metadata,
        };

        results.push({
          index: i,
          success: true,
          data: responseData,
          message: "Room config created successfully",
        });
        created++;

        // Add to existing room configs to prevent duplicates within the same batch
        existingRoomConfigs.push({
          id: product.id,
          title: product.title,
          metadata: product.metadata,
        } as any);

      } catch (error) {
        console.error(`Error creating room config at index ${i}:`, error);
        results.push({
          index: i,
          success: false,
          message: `Failed to create room config: ${error instanceof Error ? error.message : "Unknown error"}`,
          errors: [error instanceof Error ? error.message : "Unknown error"],
        });
        failed++;
      }
    }

    const summary = {
      total: room_configs.length,
      created,
      skipped,
      failed,
    };

    return res.status(created > 0 || skipped > 0 ? 201 : 400).json({
      success: failed === 0,
      message: `Bulk room config creation completed. Created: ${created}, Skipped: ${skipped}, Failed: ${failed}`,
      summary,
      results,
    });

  } catch (error) {
    console.error("Error in bulk room config creation:", error);
    return res.status(500).json({
      success: false,
      message: "Failed to process bulk room config creation",
      error: error instanceof Error ? error.message : "Unknown error",
    });
  }
};
