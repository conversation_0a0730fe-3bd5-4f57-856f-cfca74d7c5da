import { MedusaRequest, MedusaResponse } from "@camped-ai/framework";
import { ICustomerModuleService } from "@camped-ai/framework/types";
import { Modules } from "@camped-ai/framework/utils";

interface CustomerCheckRequestQuery {
  email?: string;
}

export async function GET(
  req: MedusaRequest<any, CustomerCheckRequestQuery>,
  res: MedusaResponse
) {
  const { email } = req.query;

  if (!email) {
    return res.status(400).json({ message: "Email is required" });
  }

  try {
    const customerService = req.scope.resolve<ICustomerModuleService>(Modules.CUSTOMER);
    const [customer] = await customerService.listCustomers({
      email: email as string,
    });

    if (customer) {
      return res.status(200).json({ exists: true, customer_id: customer.id });
    } else {
      return res.status(200).json({ exists: false });
    }
  } catch (error) {
    return res.status(500).json({ message: "Error checking email", error: error.message });
  }
}