import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";
import { HOTEL_PRICING_MODULE } from "src/modules/hotel-management/hotel-pricing";
import HotelPricingService from "src/modules/hotel-management/hotel-pricing/service";

// Public GET endpoint to mirror admin meal plan response exactly
// Route: /store/hotel-management/hotels/:id/pricing/meal-plan
export const GET = async (req: MedusaRequest, res: MedusaResponse) => {
  try {
    const hotelId = req.params.id;

    // Resolve hotel pricing service
    let hotelPricingService: HotelPricingService;
    try {
      hotelPricingService = req.scope.resolve(HOTEL_PRICING_MODULE);
    } catch (error) {
      console.error("[Store][MealPlan] Failed to resolve hotelPricingService:", error);
      return res.status(500).json({
        message: "Internal server error: Could not resolve hotelPricingService",
        error: error instanceof Error ? error.message : String(error),
      });
    }

    // Get all meal plans for this hotel
    const mealPlans = await hotelPricingService.listMealPlans({
      hotel_id: hotelId,
    });

    // Mirror admin response shape
    return res.json({ meal_plans: mealPlans });
  } catch (error) {
    console.error("[Store][MealPlan] GET error:", error);
    return res.status(400).json({
      message:
        error instanceof Error
          ? error.message
          : "Failed to retrieve meal plans",
    });
  }
};
