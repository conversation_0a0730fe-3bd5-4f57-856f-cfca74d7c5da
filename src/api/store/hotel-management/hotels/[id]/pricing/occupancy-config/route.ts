import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";
import { HOTEL_PRICING_MODULE } from "src/modules/hotel-management/hotel-pricing";
import HotelPricingService from "src/modules/hotel-management/hotel-pricing/service";

// Public GET endpoint to mirror admin occupancy-config response exactly
// Route: /store/hotel-management/hotels/:id/pricing/occupancy-config
export const GET = async (req: MedusaRequest, res: MedusaResponse) => {
  try {
    const hotelId = req.params.id;

    // Resolve hotel pricing service
    let hotelPricingService: HotelPricingService;
    try {
      hotelPricingService = req.scope.resolve(HOTEL_PRICING_MODULE);
    } catch (error) {
      console.error("[Store][OccupancyConfig] Failed to resolve hotelPricingService:", error);
      return res.status(500).json({
        message: "Internal server error: Could not resolve hotelPricingService",
        error: error instanceof Error ? error.message : String(error),
      });
    }

    // Get all occupancy configs for this hotel
    const occupancyConfigs = await hotelPricingService.listOccupancyConfigs({
      hotel_id: hotelId,
    });

    // Mirror admin response shape
    return res.json({ occupancy_configs: occupancyConfigs });
  } catch (error) {
    console.error("[Store][OccupancyConfig] GET error:", error);
    return res.status(400).json({
      message:
        error instanceof Error
          ? error.message
          : "Failed to retrieve occupancy configs",
    });
  }
};
