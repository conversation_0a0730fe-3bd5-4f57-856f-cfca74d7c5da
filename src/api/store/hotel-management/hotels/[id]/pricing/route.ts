import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";
import { z } from "zod";
import { HOTEL_PRICING_MODULE } from "../../../../../../modules/hotel-management/hotel-pricing";

// Validation schema for store pricing query
export const StoreHotelPricingSchema = z.object({
  currency: z.string().default("USD"),
  room_config_id: z.string().optional(),
  meal_plan_id: z.string().optional(),
  occupancy_config_id: z.string().optional(),
  check_in: z.string().optional(),
  check_out: z.string().optional(),
});

export type StoreHotelPricingType = z.infer<typeof StoreHotelPricingSchema>;

/**
 * Store endpoint to get hotel pricing information
 *
 * @param req - The request object
 * @param res - The response object
 * @returns Hotel pricing data for store/public use
 */
export const GET = async (req: MedusaRequest, res: MedusaResponse) => {
  try {
    const hotelId = req.params.id;
    const query = req.scope.resolve("query");
    const hotelPricingService = req.scope.resolve(HOTEL_PRICING_MODULE);

    // Validate query parameters
    const validatedQuery = StoreHotelPricingSchema.parse(req.query);
    const {
      currency = "USD",
      room_config_id,
      meal_plan_id,
      occupancy_config_id,
      check_in,
      check_out,
    } = validatedQuery;

    console.log(`[Store Pricing API] Fetching pricing for hotel: ${hotelId}, currency: ${currency}`);

    // Get hotel details
    const { data: hotel } = await query.graph({
      entity: "hotel",
      filters: {
        id: [hotelId],
      },
      fields: ["id", "name", "handle"],
    });

    if (!hotel || hotel.length === 0) {
      return res.status(404).json({
        success: false,
        message: "Hotel not found",
      });
    }

    // Get pricing data from the hotel pricing service
    const pricingData = await hotelPricingService.getHotelPricing({
      hotel_id: hotelId,
      currency_code: currency,
      room_config_id,
      meal_plan_id,
      occupancy_config_id,
      check_in_date: check_in,
      check_out_date: check_out,
      include_seasonal: true,
      store_context: true, // Flag to indicate this is for store/public use
    });

    return res.json({
      success: true,
      hotel: hotel[0],
      pricing: pricingData,
      currency: currency,
    });

  } catch (error) {
    console.error("Error in store hotel pricing endpoint:", error);
    return res.status(500).json({
      success: false,
      message: "Error fetching hotel pricing",
      error: error instanceof Error ? error.message : "Unknown error",
    });
  }
};