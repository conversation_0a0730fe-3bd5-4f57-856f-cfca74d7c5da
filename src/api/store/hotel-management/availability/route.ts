import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";
import { Modules } from "@camped-ai/framework/utils";
import { format, parseISO } from "date-fns";
import { z } from "zod";
import {
  consolidateAvailability,
  adjustForNoonToNoon,
} from "../../../../utils/consolidate-availability";
import { RoomInventoryStatus } from "../../../../modules/hotel-management/room-inventory/models/room-inventory";

// Validation schema for store hotel availability query
export const StoreHotelAvailabilityQuerySchema = z.object({
  hotel_id: z.string(),
  start_date: z.string(),
  end_date: z.string(),
  consolidate: z.string().optional().default("false"),
});

export type StoreHotelAvailabilityQueryType = z.infer<
  typeof StoreHotelAvailabilityQuerySchema
>;

/**
 * Maps internal room inventory status values to UI-friendly status values for store API
 * @param status - The internal status value
 * @returns The UI-friendly status value
 */
function mapStatusForStore(status: string): string {
  switch (status) {
    case RoomInventoryStatus.AVAILABLE:
      return "available";
    case RoomInventoryStatus.BOOKED:
      return "booked";
    case RoomInventoryStatus.MAINTENANCE:
      return "maintenance";
    case RoomInventoryStatus.RESERVED:
      return "reserved";
    case RoomInventoryStatus.RESERVED_UNASSIGNED:
      return "reserved"; // Map reserved_unassigned to reserved for store
    case RoomInventoryStatus.CART_RESERVED:
      return "reserved"; // Map cart_reserved to reserved for store
    case RoomInventoryStatus.CLEANING:
      return "maintenance"; // Map cleaning to maintenance for store
    case RoomInventoryStatus.UNAVAILABLE:
      return "unavailable";
    case RoomInventoryStatus.ON_DEMAND:
      return "available"; // Map on_demand to available for store customers
    case RoomInventoryStatus.ON_HOLD:
      return "on_hold"; // Map on_hold to unavailable for store customers
    default:
      return status;
  }
}

/**
 * Store API endpoint to get availability for all rooms in a hotel
 * This endpoint is designed for customer-facing applications and requires a publishable API key
 *
 * Query Parameters:
 * @param hotel_id - The ID of the hotel to get availability for
 * @param start_date - The start date of the availability range (YYYY-MM-DD)
 * @param end_date - The end date of the availability range (YYYY-MM-DD)
 * @param consolidate - Whether to consolidate the availability data into date ranges (true/false)
 *
 * @param req - The request object
 * @param res - The response object
 * @returns Hotel availability data with room configurations, rooms, and availability
 */
export const GET = async (req: MedusaRequest, res: MedusaResponse) => {
  try {
    // Validate query parameters
    const validationResult = StoreHotelAvailabilityQuerySchema.safeParse(req.query);
    
    if (!validationResult.success) {
      return res.status(400).json({
        message: "Invalid query parameters",
        errors: validationResult.error.errors,
      });
    }

    const { hotel_id, start_date, end_date, consolidate } = validationResult.data;

    // Resolve the product module service if available
    let productModuleService = null;

    try {
      productModuleService = req.scope.resolve(Modules.PRODUCT);
    } catch (error) {
      // Product module service not available
    }

    if (!hotel_id) {
      return res.status(400).json({
        message: "hotel_id is required",
      });
    }

    // Get all room configurations for this hotel
    let roomConfigs = [];
    try {
      if (productModuleService) {
        // Get products (room configurations) for this hotel
        const result: any = await productModuleService.listProducts(
          {
            is_giftcard: false,
          },
          {
            relations: ["variants"],
          }
        );

        // Filter products by hotel_id in metadata after fetching
        let products: any[] = [];

        if (Array.isArray(result)) {
          products = result;
        } else if (result && typeof result === "object") {
          // Try to extract products from different possible response formats
          if (Array.isArray(result.products)) {
            products = result.products;
          } else if (result.products) {
            products = [result.products];
          } else {
            // If we can't find products in the expected places, use the result itself
            products = [result];
          }
        }

        // Filter products by hotel_id in metadata
        roomConfigs = products.filter(
          (product: any) =>
            product &&
            product.metadata &&
            typeof product.metadata === "object" &&
            product.metadata.hotel_id === hotel_id
        );
      } else {
        roomConfigs = [];
      }
    } catch (error) {
      console.error("Error fetching room configurations:", error);
      roomConfigs = [];
    }

    // If we couldn't get real room configurations, return empty data
    if (!roomConfigs.length) {
      return res.json({
        hotel_id,
        room_configs: [],
        rooms: [],
        availability: [],
        date_range: {
          start_date,
          end_date,
        },
      });
    }

    // Get all rooms (variants) for these room configurations
    let rooms = [];

    // Extract rooms from room configurations
    for (const config of roomConfigs) {
      if (config.variants && Array.isArray(config.variants)) {
        // Add room configuration info to each room
        const roomsWithConfig = config.variants.map((variant: any) => {
          return {
            ...variant,
            room_config_id: config.id,
            config_name: config.title || config.name || "Unknown Configuration",
            room_number: variant.title || variant.name || `Room ${variant.id}`,
            name: variant.title || variant.name || `Room ${variant.id}`,
          };
        });

        rooms.push(...roomsWithConfig);
      }
    }

    // If we couldn't get real rooms, return empty data
    if (rooms.length === 0) {
      return res.json({
        hotel_id,
        room_configs: roomConfigs,
        rooms: [],
        availability: [],
        date_range: {
          start_date,
          end_date,
        },
      });
    }

    // Get availability data for each room
    const availability = [];
    // Convert query parameters to Date objects
    const startDateObj = new Date(start_date);
    const endDateObj = new Date(end_date);

    // Get the query service for direct database access
    const query = req.scope.resolve("query");

    // Process each room to get its availability
    for (const room of rooms) {
      try {
        // Get room inventory records from the database
        let roomInventories = [];
        try {
          const result = await query.graph({
            entity: "room_inventory",
            filters: {
              inventory_item_id: [room.id],
              from_date: { $gte: startDateObj },
              to_date: { $lte: endDateObj },
            },
            fields: [
              "id",
              "inventory_item_id",
              "from_date",
              "to_date",
              "available_quantity",
              "check_in_time",
              "check_out_time",
              "notes",
              "status",
              "order_id",
            ],
          });
          roomInventories = result.data || [];
        } catch (error) {
          console.error(`Error fetching inventory for room ${room.id}:`, error);
          roomInventories = [];
        }

        // Process room inventory records
        for (const record of roomInventories) {
          const fromDate = new Date(record.from_date);
          const toDate = new Date(record.to_date);

          // Format dates as strings
          const fromDateStr = format(fromDate, "yyyy-MM-dd");
          const toDateStr = format(toDate, "yyyy-MM-dd");

          // Determine status and quantity
          const rawStatus =
            record.status ||
            (record.available_quantity > 0 ? "available" : "booked");
          const status = mapStatusForStore(rawStatus);
          const quantity = record.available_quantity || 0;

          // Add the record to the availability array
          availability.push({
            room_id: room.id,
            room_number: room.room_number,
            room_name: room.name,
            config_name: room.config_name,
            from_date: fromDateStr,
            to_date: toDateStr,
            status,
            quantity,
            notes: record.notes || "",
          });
        }

        // If no inventory records were found for this room, add a default "unavailable" entry
        if (roomInventories.length === 0) {
          availability.push({
            room_id: room.id,
            room_number: room.room_number,
            room_name: room.name,
            config_name: room.config_name,
            from_date: format(startDateObj, "yyyy-MM-dd"),
            to_date: format(endDateObj, "yyyy-MM-dd"),
            status: "unavailable",
            quantity: 0,
            notes: "",
          });
        }
      } catch (error) {
        console.error(`Error processing room ${room.id}:`, error);
        // Add default availability (unavailable) for this room
        availability.push({
          room_id: room.id,
          room_number: room.room_number,
          room_name: room.name,
          config_name: room.config_name,
          from_date: format(startDateObj, "yyyy-MM-dd"),
          to_date: format(endDateObj, "yyyy-MM-dd"),
          status: "unavailable",
          quantity: 0,
          notes: "",
        });
      }
    }

    // Check if the client wants consolidated availability data
    const shouldConsolidate = consolidate === "true" || consolidate === "1";

    if (shouldConsolidate) {
      // Convert the date ranges to individual dates for consolidation
      const expandedAvailability = [];

      for (const record of availability) {
        const fromDate = parseISO(record.from_date);
        const toDate = parseISO(record.to_date);

        // Generate dates from from_date to to_date (exclusive)
        let currentDate = fromDate;
        while (currentDate < toDate) {
          expandedAvailability.push({
            room_id: record.room_id,
            room_number: record.room_number,
            room_name: record.room_name,
            config_name: record.config_name,
            date: format(currentDate, "yyyy-MM-dd"),
            status: record.status,
            quantity: record.quantity,
            dynamic_price: null,
            notes: record.notes,
            order_id: "",
          });

          // Move to the next day
          currentDate = new Date(currentDate);
          currentDate.setDate(currentDate.getDate() + 1);
        }
      }

      // Consolidate the expanded availability
      const consolidatedAvailability = consolidateAvailability(expandedAvailability);

      // Apply noon-to-noon concept
      const adjustedAvailability = adjustForNoonToNoon(consolidatedAvailability);

      // Return the consolidated and adjusted availability data
      return res.json({
        hotel_id,
        room_configs: roomConfigs,
        rooms,
        availability: adjustedAvailability,
        date_range: {
          start_date,
          end_date,
        },
        consolidated: true,
      });
    } else {
      // Return the original availability data
      return res.json({
        hotel_id,
        room_configs: roomConfigs,
        rooms,
        availability,
        date_range: {
          start_date,
          end_date,
        },
        consolidated: false,
      });
    }
  } catch (error) {
    console.error("Error in store hotel availability endpoint:", error);
    return res.status(500).json({
      message: "Error getting hotel availability",
      error: error instanceof Error ? error.message : "Unknown error",
    });
  }
};
