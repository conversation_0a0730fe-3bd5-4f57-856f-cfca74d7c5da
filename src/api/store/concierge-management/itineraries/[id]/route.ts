import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";
import ItineraryService from "../../../../../modules/concierge-management/itinerary-service";
import { asClass } from "awilix";
import { ITINERARY_SERVICE } from "../../../../../modules/concierge-management";

/**
 * Ensure itinerary service is registered in the container
 */
function ensureItineraryServiceRegistered(scope: any): ItineraryService {
  if (!scope.hasRegistration("itineraryService")) {
    console.log("🔧 [ITINERARY-API] Registering itinerary service...");
    scope.register({
      [ITINERARY_SERVICE]: asClass(ItineraryService).singleton(),
    });
    console.log("✅ [ITINERARY-API] Itinerary service registered successfully");
  }
  return scope.resolve("itineraryService");
}

/**
 * GET /store/concierge-management/itineraries/[id]
 * Public endpoint to retrieve complete itinerary details for CRM integration
 * This endpoint provides comprehensive itinerary data including all related information
 * from the three linked tables: itinerary, itinerary_day, and itinerary_event
 */
export async function GET(req: MedusaRequest, res: MedusaResponse) {
  try {
    console.log("🚀 [ITINERARY-CRM-API] Public itinerary details endpoint called");
    console.log("🔍 [ITINERARY-CRM-API] Params:", req.params);

    const { id } = req.params;

    if (!id) {
      console.error("❌ [ITINERARY-CRM-API] Missing itinerary ID");
      return res.status(400).json({
        success: false,
        error: "Itinerary ID is required",
        code: "MISSING_ITINERARY_ID"
      });
    }

    console.log("🔧 [ITINERARY-CRM-API] Ensuring itinerary service is registered...");
    const itineraryService: ItineraryService = ensureItineraryServiceRegistered(req.scope);
    console.log("✅ [ITINERARY-CRM-API] Itinerary service resolved successfully");

    console.log(`🔍 [ITINERARY-CRM-API] Fetching itinerary details for ID: ${id}`);
    const itinerary = await itineraryService.getItineraryWithDaysAndEvents(id);

    if (!itinerary) {
      console.error(`❌ [ITINERARY-CRM-API] Itinerary not found for ID: ${id}`);
      return res.status(404).json({
        success: false,
        error: "Itinerary not found",
        code: "ITINERARY_NOT_FOUND",
        itinerary_id: id
      });
    }

    console.log("✅ [ITINERARY-CRM-API] Itinerary found, processing data...");

    // Ensure days is always an array and sort by sort_order
    const sortedDays = Array.isArray(itinerary.days) 
      ? itinerary.days.sort((a, b) => (a.sort_order || 0) - (b.sort_order || 0))
      : [];

    // Process each day to ensure events are sorted by start_time
    const processedDays = sortedDays.map(day => ({
      ...day,
      events: Array.isArray(day.events) 
        ? day.events.sort((a, b) => {
            // Sort by start_time, handling null values
            if (!a.start_time && !b.start_time) return 0;
            if (!a.start_time) return 1;
            if (!b.start_time) return -1;
            return a.start_time.localeCompare(b.start_time);
          })
        : []
    }));

    // Create comprehensive response with all itinerary data
    const response = {
      success: true,
      data: {
        itinerary: {
          ...itinerary,
          days: processedDays
        },
        // Summary statistics for quick reference
        summary: {
          total_days: processedDays.length,
          total_events: processedDays.reduce((sum, day) => sum + day.events.length, 0),
          event_categories: [...new Set(
            processedDays.flatMap(day => 
              day.events.map(event => event.category)
            )
          )],
          date_range: {
            start_date: processedDays.length > 0 ? processedDays[0].date : null,
            end_date: processedDays.length > 0 ? processedDays[processedDays.length - 1].date : null
          }
        },
        // Metadata for CRM integration
        meta: {
          retrieved_at: new Date().toISOString(),
          api_version: "1.0",
          endpoint: "store/concierge-management/itineraries"
        }
      }
    };

    console.log("✅ [ITINERARY-CRM-API] Successfully processed itinerary data");
    console.log(`📊 [ITINERARY-CRM-API] Summary - Days: ${response.data.summary.total_days}, Events: ${response.data.summary.total_events}`);

    return res.status(200).json(response);

  } catch (error) {
    console.error("❌ [ITINERARY-CRM-API] Error retrieving itinerary:", error);
    
    return res.status(500).json({
      success: false,
      error: "Failed to retrieve itinerary details",
      code: "INTERNAL_SERVER_ERROR",
      details: error instanceof Error ? error.message : "Unknown error",
      timestamp: new Date().toISOString()
    });
  }
}
