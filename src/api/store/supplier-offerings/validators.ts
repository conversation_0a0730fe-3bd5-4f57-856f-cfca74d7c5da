import { z } from "zod";

// Store Supplier Offerings Query Validation
// Based on admin API but filtered for customer/store access
export const GetStoreSupplierOfferingsQuery = z.object({
  limit: z.string().optional(),
  offset: z.string().optional(),
  
  // Filter parameters
  product_service_id: z.string().optional(),
  category_id: z.string().optional(),
  status: z.enum(["active"]).optional().default("active"), // Only allow active for store
  active_from: z.string().optional(),
  active_to: z.string().optional(),
  
  // Search parameters
  search: z.string().optional(),
  
  // Sorting parameters
  sort_by: z
    .enum([
      "product_service_name", 
      "validity", 
      "updated_at"
    ])
    .optional()
    .default("updated_at"),
  sort_order: z.enum(["asc", "desc"]).optional().default("desc"),
});

export type GetStoreSupplierOfferingsQueryType = z.infer<typeof GetStoreSupplierOfferingsQuery>;
