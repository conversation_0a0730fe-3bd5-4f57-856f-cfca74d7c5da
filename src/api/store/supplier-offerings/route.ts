import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";
import { z } from "zod";
import { SUPPLIER_PRODUCTS_SERVICES_MODULE } from "src/modules/supplier-products-services";
import SupplierProductsServicesModuleService from "src/modules/supplier-products-services/service";
import {
  GetStoreSupplierOfferingsQuery,
  GetStoreSupplierOfferingsQueryType,
} from "./validators";

/**
 * GET /store/supplier-offerings
 * 
 * Store API endpoint for retrieving supplier offerings
 * This endpoint provides customer-facing access to active supplier offerings
 * with appropriate data filtering for store/customer consumption
 * Excludes sensitive pricing/cost data that should not be exposed to customers
 */
export const GET = async (
  req: MedusaRequest<{}, GetStoreSupplierOfferingsQueryType>,
  res: MedusaResponse
) => {
  try {
    // Validate query parameters
    const validatedQuery = GetStoreSupplierOfferingsQuery.parse(req.query);
    
    const supplierProductsServicesService: SupplierProductsServicesModuleService =
      req.scope.resolve(SUPPLIER_PRODUCTS_SERVICES_MODULE);

    // Parse query parameters
    const limit = parseInt(validatedQuery.limit as string) || 25;
    const offset = parseInt(validatedQuery.offset as string) || 0;

    // Build filters - enforce store-specific constraints
    const filters: any = {
      status: "active", // Force active status for store API
    };

    // Add optional filters
    if (validatedQuery.product_service_id) {
      filters.product_service_id = validatedQuery.product_service_id;
    }

    if (validatedQuery.category_id) {
      filters.category_id = validatedQuery.category_id;
    }

    if (validatedQuery.active_from) {
      filters.active_from = new Date(validatedQuery.active_from as string);
    }

    if (validatedQuery.active_to) {
      filters.active_to = new Date(validatedQuery.active_to as string);
    }

    if (validatedQuery.search) {
      filters.search = validatedQuery.search;
    }

    // Add sorting parameters
    const sort_by = validatedQuery.sort_by || "updated_at";
    const sort_order = validatedQuery.sort_order || "desc";

    console.log("Store Supplier Offerings API - Filters:", filters, "Options:", { limit, offset, sort_by, sort_order });

    // Fetch data using the same service as admin API
    const result = await supplierProductsServicesService.listSupplierOfferingsWithFilters(
      filters,
      { limit, offset, sort_by, sort_order }
    );

    console.log(result.data)

    // Filter sensitive data for store API - exclude cost/pricing information
    const filteredData = result.data.map(item => ({
      id: item.id,
      product_service_id: item.product_service_id,
      supplier_id: item.supplier_id,
      active_from: item.active_from,
      active_to: item.active_to,
      status: item.status,
      
      // Include selling price in selling currency if available (customer-facing price)
      selling_price_selling_currency: item.calculated_selling_price_selling_currency,
      selling_currency: item.selling_currency,
      
      // Include product service details
      product_service: item.product_service ? {
        id: item.product_service.id,
        name: item.product_service.name,
        description: item.product_service.description,
        status: item.product_service.status,
        category_id: item.product_service.category_id,
        unit_type_id: item.product_service.unit_type_id,
        service_level: item.product_service.service_level,
        hotel_id: item.product_service.hotel_id,
        destination_id: item.product_service.destination_id,
      } : undefined,
      
      // Include supplier details (basic info only)
      supplier: item.supplier ? {
        id: item.supplier.id,
        name: item.supplier.name,
        // Exclude sensitive supplier data
      } : undefined,
      
      created_at: item.created_at,
      updated_at: item.updated_at,
      
      // Exclude sensitive fields:
      // - cost, commission, gross_price, net_cost, margin_rate
      // - availability_notes (internal use)
      // - custom_fields (may contain sensitive data)
      // - exchange_rate details
      // - created_by, updated_by
    }));

    res.status(200).json({
      supplier_offerings: filteredData,
      count: result.count,
      limit: result.limit,
      offset: result.offset,
    });
  } catch (error) {
    console.error("❌ Error in store supplier-offerings API:", error);
    
    if (error.name === "ZodError") {
      return res.status(400).json({
        type: "invalid_data",
        message: "Invalid query parameters",
        details: error.errors,
      });
    }

    res.status(500).json({
      type: "server_error",
      message: "Failed to retrieve supplier offerings",
      error: error instanceof Error ? error.message : "Unknown error",
    });
  }
};
