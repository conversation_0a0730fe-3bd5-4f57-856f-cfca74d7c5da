import { MedusaRequest, MedusaResponse } from "@camped-ai/framework";
import { Modules } from "@camped-ai/framework/utils";

/**
 * GET /store/regions
 * 
 * Returns available regions for cart creation.
 * This endpoint helps the CRM system select appropriate regions and currencies.
 */
export const GET = async (req: MedusaRequest, res: MedusaResponse) => {
  try {
    const regionModuleService = req.scope.resolve(Modules.REGION);

    // Get all active regions
    const regions = await regionModuleService.listRegions(
      {
        // Filter out disabled regions if metadata supports it
      },
      {
        relations: ["countries"],
        order: { name: "ASC" }
      }
    );

    // Transform regions for frontend consumption
    const transformedRegions = regions.map(region => ({
      id: region.id,
      name: region.name,
      currency_code: region.currency_code,
      countries: region.countries?.map(country => ({
        id: country.id,
        name: country.name,
        iso_2: country.iso_2,
        iso_3: country.iso_3
      })) || [],
      is_default: region.metadata?.is_default === true,
      tax_rate: region.metadata?.tax_rate || null,
      created_at: region.created_at,
      updated_at: region.updated_at
    }));

    // Sort to put default region first
    transformedRegions.sort((a, b) => {
      if (a.is_default && !b.is_default) return -1;
      if (!a.is_default && b.is_default) return 1;
      return a.name.localeCompare(b.name);
    });

    res.json({
      regions: transformedRegions,
      count: transformedRegions.length
    });

  } catch (error) {
    console.error("Error fetching regions:", error);
    res.status(500).json({
      message: "Failed to fetch regions",
      error: error instanceof Error ? error.message : "Unknown error"
    });
  }
};
