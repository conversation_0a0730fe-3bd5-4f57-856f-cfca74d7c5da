import { z } from "zod";

// Hotel booking metadata schema for line items
const HotelBookingMetadataSchema = z.object({
  // Room and hotel information
  room_id: z.string().optional(),
  hotel_id: z.string().optional(),
  hotel_name: z.string().optional(),

  // Booking dates
  check_in_date: z.string().optional(),
  check_out_date: z.string().optional(),
  check_in_time: z.string().optional(),
  check_out_time: z.string().optional(),

  // Room configuration
  room_config_id: z.string().optional(),
  room_config_name: z.string().optional(),
  room_name: z.string().optional(),
  room_type: z.string().optional(),

  // Booking details
  number_of_rooms: z.number().optional(),
  number_of_guests: z.number().optional(),

  // Occupancy information
  occupancy_type_id: z.string().optional(),
  occupancy_type_name: z.string().optional(),

  // Meal plan information
  meal_plan_id: z.string().optional(),
  meal_plan_name: z.string().optional(),

  // Guest information
  guest_name: z.string().optional(),
  guest_email: z.string().email().optional().or(z.literal("")),
  guest_phone: z.string().optional(),

  // Additional booking details
  special_requests: z.string().optional(),
  total_amount: z.number().optional(),
  currency_code: z.string().optional(),

  // Allow any additional metadata fields
}).catchall(z.any());

export const StorePostCartLineItemsReqSchema = z.object({
  items: z.array(z.object({
    // Required core fields
    variant_id: z.string().min(1, "Variant ID is required"),
    quantity: z.number().min(1, "Quantity must be at least 1"),

    // Optional core fields
    product_id: z.string().optional(),
    unit_price: z.number().optional(),
    title: z.string().optional(),

    // Product information fields
    product_collection: z.string().optional(),
    product_description: z.string().optional(),
    product_handle: z.string().optional(),
    product_subtitle: z.string().optional(),
    product_type: z.string().optional(),
    product_type_id: z.string().optional(),
    raw_compare_at_unit_price: z.number().optional(),

    // Shipping and other options
    requires_shipping: z.boolean().optional(),

    // Hotel booking metadata - can be either structured or free-form
    metadata: z.union([
      HotelBookingMetadataSchema,
      z.record(z.any())
    ]).optional(),
  })).min(1, "At least one item is required"),
});

export type StorePostCartLineItemsReq = z.infer<typeof StorePostCartLineItemsReqSchema>;

// Export the hotel booking metadata type for use in other parts of the application
export type HotelBookingMetadata = z.infer<typeof HotelBookingMetadataSchema>;
