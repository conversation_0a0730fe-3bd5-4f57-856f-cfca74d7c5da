import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";
import {
  ContainerRegistrationKeys,
  remoteQueryObjectFromString,
  Modules,
  MedusaError,
} from "@camped-ai/framework/utils";
import {
  completeCartWorkflow,
  createPaymentCollectionForCartWorkflow,
  createPaymentSessionsWorkflow,
} from "@camped-ai/medusa/core-flows";
import { z } from "zod";

/**
 * POST /store/carts/{id}/complete-manual
 * 
 * Simple manual cart completion that bypasses payment session complexities
 * Perfect for quote approvals and manual payment collection workflows
 */

const CompleteManualSchema = z.object({
  payment_type: z.enum(["deposit", "full", "manual"]).default("manual"),
  partial_amount: z.number().positive().optional(), // Partial amount in cents
  notes: z.string().optional(),
  collected_by: z.string().default("sales_team"),
  metadata: z.record(z.any()).optional()
});

export const POST = async (req: MedusaRequest, res: MedusaResponse) => {
  try {
    const cartId = req.params.id;
    const validatedBody = CompleteManualSchema.parse(req.body);
    
    const {
      payment_type,
      partial_amount,
      notes,
      collected_by,
      metadata = {}
    } = validatedBody;

    console.log(`🛒 Manual cart completion: ${cartId}`);
    console.log(`💰 Payment type: ${payment_type}`);

    // Get cart details
    const query = req.scope.resolve(ContainerRegistrationKeys.QUERY);
    const [cart] = await query(
      remoteQueryObjectFromString({
        entryPoint: "cart",
        variables: { id: cartId },
        fields: [
          "id",
          "total",
          "currency_code",
          "customer_id",
          "email",
          "metadata",
          "items.*"
        ]
      })
    );

    if (!cart) {
      return res.status(404).json({
        message: "Cart not found"
      });
    }

    console.log(`📋 Cart total: ${cart.total} ${cart.currency_code}`);

    // Calculate payment amounts
    let paymentAmount: number;

    switch (payment_type) {
      case "deposit":
        // Default: 20% deposit or custom amount
        paymentAmount = partial_amount || Math.round(cart.total * 0.2);
        break;
      case "full":
        paymentAmount = cart.total;
        break;
      case "manual":
        // For manual payments, use minimal amount or specified amount
        paymentAmount = partial_amount || 0; // Can be 0 for pure manual
        break;
      default:
        paymentAmount = partial_amount || 0;
    }

    const remainingAmount = cart.total - paymentAmount;

    console.log(`💳 Payment amount: ${paymentAmount}`);
    console.log(`💰 Remaining amount: ${remainingAmount}`);

    // Step 1: Create minimal payment collection if needed
    const [existingCollection] = await query(
      remoteQueryObjectFromString({
        entryPoint: "cart_payment_collection",
        variables: { filters: { cart_id: cartId } },
        fields: [
          "payment_collection.id",
          "payment_collection.status"
        ]
      })
    );

    let paymentCollectionId: string;

    if (existingCollection?.payment_collection) {
      paymentCollectionId = existingCollection.payment_collection.id;
      console.log(`♻️ Using existing payment collection: ${paymentCollectionId}`);
    } else {
      console.log("🏦 Creating minimal payment collection...");
      
      await createPaymentCollectionForCartWorkflow(req.scope).run({
        input: {
          cart_id: cartId,
          metadata: {
            cart_id: cartId,
            cart_total: cart.total,
            payment_amount: paymentAmount,
            payment_type: payment_type,
            is_partial_payment: payment_type !== "full",
            remaining_amount: remainingAmount,
            manual_completion: true,
            collected_by: collected_by,
            notes: notes,
            created_via: "manual_completion_api",
            ...metadata
          }
        }
      });

      const [newCollection] = await query(
        remoteQueryObjectFromString({
          entryPoint: "cart_payment_collection",
          variables: { filters: { cart_id: cartId } },
          fields: ["payment_collection.id"]
        })
      );

      paymentCollectionId = newCollection?.payment_collection?.id;
      
      if (!paymentCollectionId) {
        throw new MedusaError(
          MedusaError.Types.INVALID_DATA,
          "Failed to create payment collection"
        );
      }

      console.log(`✅ Payment collection created: ${paymentCollectionId}`);
    }

    // Step 2: Create and authorize payment session (required by Medusa)
    console.log("💳 Creating payment session for manual completion...");

    try {
      const sessionInput = {
        payment_collection_id: paymentCollectionId,
        provider_id: "pp_stripe_stripe", // Use Stripe even for manual
        context: {
          extra: {
            email: cart.email,
            customer_id: cart.customer_id,
            payment_type: payment_type,
            payment_amount: paymentAmount,
            remaining_amount: remainingAmount,
            manual_completion: true,
            collected_by: collected_by,
            notes: notes
          }
        }
      };

      console.log("Payment session input:", JSON.stringify(sessionInput, null, 2));

      const workflowResult = await createPaymentSessionsWorkflow(req.scope).run({
        input: sessionInput
      });

      console.log("Payment session workflow result:", JSON.stringify(workflowResult, null, 2));

      const paymentSessions = workflowResult?.result;

      if (!paymentSessions) {
        console.error("No payment sessions in workflow result");
        throw new MedusaError(
          MedusaError.Types.INVALID_DATA,
          "Payment session workflow returned no result"
        );
      }

      if (!Array.isArray(paymentSessions) || paymentSessions.length === 0) {
        console.error("Payment sessions is not an array or is empty:", paymentSessions);
        throw new MedusaError(
          MedusaError.Types.INVALID_DATA,
          "No payment sessions created"
        );
      }

      const paymentSession = paymentSessions[0];

      if (!paymentSession) {
        console.error("First payment session is undefined");
        throw new MedusaError(
          MedusaError.Types.INVALID_DATA,
          "Payment session is undefined"
        );
      }

      if (!paymentSession.id) {
        console.error("Payment session missing ID:", paymentSession);
        throw new MedusaError(
          MedusaError.Types.INVALID_DATA,
          "Payment session created but missing ID"
        );
      }

      console.log(`💳 Payment session created: ${paymentSession.id}`);

      // Authorize the payment session
      console.log("🔐 Authorizing payment session...");

      const paymentModuleService = req.scope.resolve(Modules.PAYMENT);
      await paymentModuleService.authorizePaymentSession(paymentSession.id, {
        context: {
          ...paymentSession.context,
          authorized_amount: paymentAmount,
          authorization_type: "manual",
          manual_completion: true,
          notes: `Manual completion - ${payment_type} payment for cart ${cartId}`,
          authorized_at: new Date().toISOString()
        }
      });

      console.log("✅ Payment session authorized for manual completion");

    } catch (sessionError) {
      console.error("Error creating payment session for manual completion:", sessionError);
      console.error("Session error details:", {
        message: sessionError.message,
        stack: sessionError.stack,
        name: sessionError.name
      });

      // Try a simpler approach - create a minimal payment session directly
      console.log("🔄 Attempting direct payment session creation...");

      try {
        const paymentModuleService = req.scope.resolve(Modules.PAYMENT);

        const directSession = await paymentModuleService.createPaymentSession({
          payment_collection_id: paymentCollectionId,
          provider_id: "pp_stripe_stripe",
          currency_code: cart.currency_code,
          amount: paymentAmount,
          context: {
            email: cart.email,
            customer_id: cart.customer_id,
            manual_completion: true,
            payment_type: payment_type
          }
        });

        console.log("Direct payment session created:", directSession.id);

        // Authorize it
        await paymentModuleService.authorizePaymentSession(directSession.id, {
          context: {
            manual_authorization: true,
            payment_type: payment_type,
            notes: `Manual completion - ${payment_type} payment`
          }
        });

        console.log("✅ Direct payment session authorized");

      } catch (directError) {
        console.error("Direct payment session creation also failed:", directError);
        throw new MedusaError(
          MedusaError.Types.INVALID_DATA,
          `Both workflow and direct payment session creation failed. Workflow: ${sessionError.message}, Direct: ${directError.message}`
        );
      }
    }

    // Step 3: Complete cart (now with required payment sessions)
    console.log("🎯 Completing cart with payment sessions...");
    
    try {
      const { result: order } = await completeCartWorkflow(req.scope).run({
        input: { id: cartId }
      });

      console.log(`🎉 Order created: ${order.id}`);

      // Step 4: Update order metadata with manual payment tracking
      const orderModuleService = req.scope.resolve(Modules.ORDER);
      await orderModuleService.updateOrders(order.id, {
        metadata: {
          ...order.metadata,
          payment_tracking: {
            payment_type: payment_type,
            paid_amount: paymentAmount,
            remaining_amount: remainingAmount,
            total_amount: cart.total,
            currency_code: cart.currency_code,
            requires_additional_payment: remainingAmount > 0,
            payment_collection_id: paymentCollectionId,
            payment_session_id: "manual_completion",
            completed_at: new Date().toISOString(),
            manual_completion: true,
            collected_by: collected_by,
            notes: notes
          },
          concierge_notes: remainingAmount > 0
            ? `Remaining payment of ${remainingAmount} ${cart.currency_code} to be collected by ${collected_by}`
            : "Full payment completed",
          manual_completion: {
            completed_at: new Date().toISOString(),
            completed_by: collected_by,
            notes: notes,
            requires_payment_collection: true
          }
        }
      });

      res.json({
        success: true,
        order: {
          id: order.id,
          total: order.total,
          currency_code: order.currency_code,
          status: order.status,
          customer_id: order.customer_id,
          email: order.email
        },
        payment_info: {
          payment_type: payment_type,
          paid_amount: paymentAmount,
          remaining_amount: remainingAmount,
          total_amount: cart.total,
          currency_code: cart.currency_code,
          requires_additional_payment: remainingAmount > 0,
          payment_collection_id: paymentCollectionId,
          manual_completion: true
        },
        cart_id: cartId,
        message: payment_type === "manual" && paymentAmount === 0
          ? `Order created successfully. Full payment of ${cart.total} ${cart.currency_code} to be collected by ${collected_by}.`
          : remainingAmount > 0
            ? `Order created with ${payment_type} payment. Remaining ${remainingAmount} ${cart.currency_code} to be collected by ${collected_by}.`
            : "Order created with full payment completed.",
        next_steps: remainingAmount > 0
          ? [
              "Order is confirmed and created",
              paymentAmount > 0 ? `${collected_by} has collected ${paymentAmount} ${cart.currency_code}` : "No payment collected yet",
              `${collected_by} should collect remaining payment`,
              `Amount to collect: ${remainingAmount} ${cart.currency_code}`,
              "Use admin API to mark payment as collected when received",
              `POST /admin/orders/${order.id}/mark-payment-collected`
            ]
          : [
              "Order is confirmed and created",
              "Full payment completed",
              "No additional payment required"
            ],
        admin_actions: {
          mark_payment_collected_url: `/admin/orders/${order.id}/mark-payment-collected`,
          payment_status_url: `/admin/orders/${order.id}/payment-status`,
          example_payload: {
            amount: remainingAmount,
            payment_method: "bank_transfer",
            collected_by: collected_by,
            reference_number: "TXN_123456",
            notes: remainingAmount > 0 ? "Remaining payment collected" : "Full payment collected"
          }
        }
      });

    } catch (completionError) {
      console.error("Error completing cart manually:", completionError);
      
      // If cart completion fails due to payment authorization, try a different approach
      if (completionError.message?.includes("payment") || completionError.message?.includes("authorization")) {
        console.log("⚠️ Payment authorization issue detected - attempting workaround");
        
        // Try to authorize any existing payment sessions manually
        const paymentModuleService = req.scope.resolve(Modules.PAYMENT);
        
        try {
          // Get payment sessions for this collection
          const sessions = await paymentModuleService.listPaymentSessions({
            payment_collection_id: paymentCollectionId
          });
          
          console.log(`Found ${sessions.length} payment sessions to authorize`);
          
          // Authorize all sessions
          for (const session of sessions) {
            if (session.status !== "authorized") {
              console.log(`Authorizing session: ${session.id}`);
              await paymentModuleService.authorizePaymentSession(session.id, {
                context: {
                  manual_authorization: true,
                  authorized_by: collected_by,
                  notes: "Manual authorization for cart completion"
                }
              });
            }
          }
          
          // Try cart completion again
          const { result: order } = await completeCartWorkflow(req.scope).run({
            input: { id: cartId }
          });
          
          console.log(`🎉 Order created after manual authorization: ${order.id}`);
          
          return res.json({
            success: true,
            order: { id: order.id, total: order.total },
            message: "Order created successfully after manual payment authorization",
            manual_completion: true
          });
          
        } catch (authError) {
          console.error("Manual authorization also failed:", authError);
          throw new MedusaError(
            MedusaError.Types.INVALID_DATA,
            `Cart completion failed: ${completionError.message}. Manual authorization also failed: ${authError.message}`
          );
        }
      } else {
        throw completionError;
      }
    }

  } catch (error) {
    console.error("Error in manual cart completion:", error);
    
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        message: "Validation error",
        errors: error.errors
      });
    }

    if (error instanceof MedusaError) {
      return res.status(400).json({
        type: error.type,
        message: error.message
      });
    }

    res.status(500).json({
      message: "Failed to complete cart manually",
      error: error instanceof Error ? error.message : "Unknown error"
    });
  }
};
