import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";
import { UpdateTravellerProfileSchema } from "../../schemas";

export const POST = async (req: MedusaRequest, res: MedusaResponse) => {
  try {
    const cart_id = req.params.id;
    const traveller_id = req.params.traveller_id;
    const customer_id = (req as any).user?.customer_id || (req as any).user?.id || (req as any).customer_id;

    if (!customer_id) {
      return res.status(401).json({ message: "Unauthorized" });
    }

    // Validate request body using schema
    const validationResult = UpdateTravellerProfileSchema.safeParse(req.body);
    if (!validationResult.success) {
      return res.status(400).json({
        message: "Validation failed",
        errors: validationResult.error.errors
      });
    }

    const body = validationResult.data;

    // TODO: Implement proper database access once customer_traveller entity is registered
    // For now, return a mock response to indicate the endpoint is working
    const updated = {
      id: traveller_id,
      customer_id,
      cart_id: body.cart_id || cart_id,
      first_name: body.first_name,
      last_name: body.last_name,
      date_of_birth: body.date_of_birth,
      gender: body.gender,
      relationship: body.relationship,
      is_primary: body.is_primary,
      created_at: new Date(Date.now() - 86400000).toISOString(), // Mock created yesterday
      updated_at: new Date().toISOString()
    };

    res.status(200).json({ cart_id, customer_id, profile: updated });
  } catch (error) {
    console.error("Error updating traveller profile:", error);
    return res.status(500).json({
      message: "Internal server error",
      error: error instanceof Error ? error.message : "Unknown error"
    });
  }
}
