import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";
import { CUSTOMER_TRAVELLER_MODULE } from "../../../../../../../modules/customer-travellers";

/**
 * DELETE /store/carts/:id/traveller-profiles/:traveller_id
 * Requirement: Not need customer_id
 * Behavior: Soft-delete traveller profile by traveller_id (within cart context), return 204.
 */
export const DELETE = async (req: MedusaRequest, res: MedusaResponse) => {
  try {
    const cart_id = req.params.id;
    const traveller_id = req.params.traveller_id;

    if (!cart_id) {
      return res.status(400).json({ message: "cart id is required" });
    }
    if (!traveller_id) {
      return res.status(400).json({ message: "traveller id is required" });
    }

    // Resolve service (loosely typed to avoid signature coupling)
    const travellerService = req.scope.resolve(CUSTOMER_TRAVELLER_MODULE) as any;

    if (!travellerService) {
      return res.status(500).json({ message: "Traveller service not available" });
    }

    // Delete without customer_id: directly invoke delete(id) as requested
    await travellerService.delete(traveller_id);

    return res.status(204).send();
  } catch (error) {
    const message =
      (error as any)?.message ||
      (typeof error === "string" ? error : "Internal server error");

    console.error("Error deleting traveller profile:", error);
    return res.status(500).json({
      message,
    });
  }
};
