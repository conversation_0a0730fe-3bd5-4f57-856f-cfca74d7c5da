import { z } from "zod";

/**
 * Enum values for validation
 */
const GenderEnum = z.enum(["male", "female", "other"]);
const RelationshipEnum = z.enum([
  "spouse", "child", "sibling", "grandparent", "friend",
  "parent", "colleague", "relative", "parent_in_law", "other"
]);

/**
 * Schema for creating a traveller profile according to PRD
 */
export const CreateTravellerProfileSchema = z.object({
  first_name: z.string().min(1, "First name is required").max(255, "First name too long"),
  last_name: z.string().min(1, "Last name is required").max(255, "Last name too long"),
  date_of_birth: z.string().min(1, "Date of birth is required")
    .regex(/^\d{4}-\d{2}-\d{2}$/, "Date must be in YYYY-MM-DD format"),
  customer_id: z.string().optional(),
  gender: GenderEnum.optional(),
  relationship: RelationshipEnum.optional(), 
  is_primary: z.boolean().default(false),
}).refine((data) => {
  // If not primary, relationship is required
  if (data.is_primary === false && !data.relationship) {
    return false;
  }
  return true;
}, {
  message: "Relationship is required for additional travellers",
  path: ["relationship"],
});

/**
 * Schema for updating a traveller profile
 */
export const UpdateTravellerProfileSchema = z.object({
  first_name: z.string().min(1, "First name is required").max(255, "First name too long").optional(),
  last_name: z.string().min(1, "Last name is required").max(255, "Last name too long").optional(),
  date_of_birth: z.string()
    .regex(/^\d{4}-\d{2}-\d{2}$/, "Date must be in YYYY-MM-DD format")
    .optional(),
  gender: GenderEnum.optional(),
  relationship: RelationshipEnum.optional(),
  is_primary: z.boolean().optional(),
  cart_id: z.string().optional(),
}).refine((data) => {
  // If explicitly setting non-primary, relationship should be present
  if (data.is_primary === false && !data.relationship) {
    return false;
  }
  return true;
}, {
  message: "Relationship is required for additional travellers",
  path: ["relationship"],
});

/**
 * Response schemas for API documentation
 */
export const TravellerProfileResponseSchema = z.object({
  id: z.string(),
  customer_id: z.string(),
  cart_id: z.string().nullable(),
  first_name: z.string(),
  last_name: z.string(),
  date_of_birth: z.string(),
  gender: GenderEnum.nullable(),
  relationship: RelationshipEnum.nullable(),
  is_primary: z.boolean(),
  created_at: z.string(),
  updated_at: z.string(),
  deleted_at: z.string().nullable(),
});

export const ListTravellerProfilesResponseSchema = z.object({
  cart_id: z.string(),
  profiles: z.array(TravellerProfileResponseSchema),
});

export const CreateTravellerProfileResponseSchema = z.object({
  cart_id: z.string(),
  profile: TravellerProfileResponseSchema,
});

/**
 * Type definitions for request bodies
 */
export type CreateTravellerProfileReq = z.infer<typeof CreateTravellerProfileSchema>;
export type UpdateTravellerProfileReq = z.infer<typeof UpdateTravellerProfileSchema>;
export type TravellerProfileResponse = z.infer<typeof TravellerProfileResponseSchema>;
export type ListTravellerProfilesResponse = z.infer<typeof ListTravellerProfilesResponseSchema>;
export type CreateTravellerProfileResponse = z.infer<typeof CreateTravellerProfileResponseSchema>;
