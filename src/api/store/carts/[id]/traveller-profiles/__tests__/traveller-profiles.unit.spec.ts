import { describe, it, expect, beforeEach, jest } from "@jest/globals";
import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";
import { MedusaError } from "@camped-ai/framework/utils";
import { GET, POST } from "../route";
import CustomerTravellerModuleService from "../../../../../../modules/customer-travellers/service";
import * as customerAuth from "../../../../../../utils/customer-auth";

// Mock the customer auth module
jest.mock("../../../../../../utils/customer-auth");
const mockExtractCustomerId = customerAuth.extractCustomerId as jest.MockedFunction<typeof customerAuth.extractCustomerId>;

// Mock the service
const mockTravellerService = {
  listByCart: jest.fn(),
  create: jest.fn(),
} as jest.Mocked<Partial<CustomerTravellerModuleService>>;

// Mock request and response
const createMockRequest = (params: any = {}, body: any = {}, headers: any = {}): MedusaRequest => ({
  params,
  body,
  headers,
  scope: {
    resolve: jest.fn().mockReturnValue(mockTravellerService),
  },
} as any);

const createMockResponse = (): MedusaResponse => {
  const res = {
    status: jest.fn().mockReturnThis(),
    json: jest.fn().mockReturnThis(),
  } as any;
  return res;
};

describe("Traveller Profiles API", () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe("GET /store/carts/{id}/traveller-profiles", () => {
    it("should return 400 if cart_id is missing", async () => {
      const req = createMockRequest({});
      const res = createMockResponse();

      await GET(req, res);

      expect(res.status).toHaveBeenCalledWith(400);
      expect(res.json).toHaveBeenCalledWith({
        type: "invalid_data",
        message: "Cart ID is required"
      });
    });

    it("should return 500 if service is not available", async () => {
      const req = createMockRequest({ id: "cart_123" });
      const res = createMockResponse();
      req.scope.resolve = jest.fn().mockReturnValue({});

      await GET(req, res);

      expect(res.status).toHaveBeenCalledWith(500);
      expect(res.json).toHaveBeenCalledWith({
        type: "service_error",
        message: "Traveller service not available"
      });
    });

    it("should return empty profiles list successfully", async () => {
      const req = createMockRequest({ id: "cart_123" });
      const res = createMockResponse();
      mockTravellerService.listByCart!.mockResolvedValue([]);

      await GET(req, res);

      expect(mockTravellerService.listByCart).toHaveBeenCalledWith("cart_123");
      expect(res.status).toHaveBeenCalledWith(200);
      expect(res.json).toHaveBeenCalledWith({
        cart_id: "cart_123",
        profiles: []
      });
    });

    it("should return profiles list with proper formatting", async () => {
      const mockProfile = {
        id: "cust_trav_123",
        customer_id: "cus_123",
        cart_id: "cart_123",
        first_name: "John",
        last_name: "Doe",
        date_of_birth: new Date("1990-01-01"),
        gender: "male",
        relationship: null,
        is_primary: true,
        created_at: new Date("2025-01-01T00:00:00Z"),
        updated_at: new Date("2025-01-01T00:00:00Z"),
        deleted_at: null,
      };

      const req = createMockRequest({ id: "cart_123" });
      const res = createMockResponse();
      mockTravellerService.listByCart!.mockResolvedValue([mockProfile]);

      await GET(req, res);

      expect(res.status).toHaveBeenCalledWith(200);
      expect(res.json).toHaveBeenCalledWith({
        cart_id: "cart_123",
        profiles: [{
          ...mockProfile,
          date_of_birth: "1990-01-01T00:00:00.000Z",
          created_at: "2025-01-01T00:00:00.000Z",
          updated_at: "2025-01-01T00:00:00.000Z",
          deleted_at: null,
        }]
      });
    });

    it("should handle MedusaError properly", async () => {
      const req = createMockRequest({ id: "cart_123" });
      const res = createMockResponse();
      const error = new MedusaError("not_found", "Cart not found");
      mockTravellerService.listByCart!.mockRejectedValue(error);

      await GET(req, res);

      expect(res.status).toHaveBeenCalledWith(404);
      expect(res.json).toHaveBeenCalledWith({
        type: "not_found",
        message: "Cart not found"
      });
    });
  });

  describe("POST /store/carts/{id}/traveller-profiles", () => {
    const validBody = {
      first_name: "Jane",
      last_name: "Doe",
      date_of_birth: "1995-05-15",
      gender: "female",
      relationship: "spouse",
      is_primary: false,
    };

    it("should return 400 if cart_id is missing", async () => {
      const req = createMockRequest({}, validBody);
      const res = createMockResponse();

      await POST(req, res);

      expect(res.status).toHaveBeenCalledWith(400);
      expect(res.json).toHaveBeenCalledWith({
        type: "invalid_data",
        message: "Cart ID is required"
      });
    });

    it("should return 400 for invalid request body", async () => {
      const req = createMockRequest({ id: "cart_123" }, { first_name: "" });
      const res = createMockResponse();

      await POST(req, res);

      expect(res.status).toHaveBeenCalledWith(400);
      expect(res.json).toHaveBeenCalledWith(
        expect.objectContaining({
          type: "validation_error",
          message: "Validation failed",
        })
      );
    });

    it("should return 401 if customer is not authenticated", async () => {
      const req = createMockRequest({ id: "cart_123" }, validBody);
      const res = createMockResponse();
      mockExtractCustomerId.mockReturnValue(null);

      await POST(req, res);

      expect(res.status).toHaveBeenCalledWith(401);
      expect(res.json).toHaveBeenCalledWith({
        type: "unauthorized",
        message: "Customer authentication required"
      });
    });

    it("should create traveller profile successfully", async () => {
      const mockCreated = {
        id: "cust_trav_456",
        customer_id: "cus_123",
        cart_id: "cart_123",
        first_name: "Jane",
        last_name: "Doe",
        date_of_birth: new Date("1995-05-15"),
        gender: "female",
        relationship: "spouse",
        is_primary: false,
        created_at: new Date("2025-01-01T00:00:00Z"),
        updated_at: new Date("2025-01-01T00:00:00Z"),
        deleted_at: null,
      };

      const req = createMockRequest({ id: "cart_123" }, validBody);
      const res = createMockResponse();
      mockExtractCustomerId.mockReturnValue("cus_123");
      mockTravellerService.create!.mockResolvedValue(mockCreated);

      await POST(req, res);

      expect(mockTravellerService.create).toHaveBeenCalledWith({
        customer_id: "cus_123",
        cart_id: "cart_123",
        first_name: "Jane",
        last_name: "Doe",
        date_of_birth: "1995-05-15",
        gender: "female",
        relationship: "spouse",
        is_primary: false,
      });

      expect(res.status).toHaveBeenCalledWith(201);
      expect(res.json).toHaveBeenCalledWith({
        cart_id: "cart_123",
        profile: {
          ...mockCreated,
          date_of_birth: "1995-05-15T00:00:00.000Z",
          created_at: "2025-01-01T00:00:00.000Z",
          updated_at: "2025-01-01T00:00:00.000Z",
          deleted_at: null,
        }
      });
    });

    it("should handle primary traveller constraint violation", async () => {
      const req = createMockRequest({ id: "cart_123" }, { ...validBody, is_primary: true });
      const res = createMockResponse();
      mockExtractCustomerId.mockReturnValue("cus_123");
      
      const error = new MedusaError("not_allowed", "Primary traveller already exists for this customer");
      mockTravellerService.create!.mockRejectedValue(error);

      await POST(req, res);

      expect(res.status).toHaveBeenCalledWith(405);
      expect(res.json).toHaveBeenCalledWith({
        type: "not_allowed",
        message: "Primary traveller already exists for this customer"
      });
    });

    it("should handle maximum profiles constraint violation", async () => {
      const req = createMockRequest({ id: "cart_123" }, validBody);
      const res = createMockResponse();
      mockExtractCustomerId.mockReturnValue("cus_123");
      
      const error = new MedusaError("not_allowed", "Maximum number of traveller profiles reached");
      mockTravellerService.create!.mockRejectedValue(error);

      await POST(req, res);

      expect(res.status).toHaveBeenCalledWith(405);
      expect(res.json).toHaveBeenCalledWith({
        type: "not_allowed",
        message: "Maximum number of traveller profiles reached"
      });
    });
  });

  describe("Validation Schema Tests", () => {
    it("should validate primary traveller without relationship", () => {
      const { CreateTravellerProfileSchema } = require("../schemas");

      const validPrimary = {
        first_name: "John",
        last_name: "Doe",
        date_of_birth: "1990-01-01",
        is_primary: true,
      };

      const result = CreateTravellerProfileSchema.safeParse(validPrimary);
      expect(result.success).toBe(true);
    });

    it("should reject non-primary traveller without relationship", () => {
      const { CreateTravellerProfileSchema } = require("../schemas");

      const invalidNonPrimary = {
        first_name: "Jane",
        last_name: "Doe",
        date_of_birth: "1995-05-15",
        is_primary: false,
      };

      const result = CreateTravellerProfileSchema.safeParse(invalidNonPrimary);
      expect(result.success).toBe(false);
      expect(result.error?.errors[0].message).toBe("Relationship is required for additional travellers");
    });

    it("should validate non-primary traveller with relationship", () => {
      const { CreateTravellerProfileSchema } = require("../schemas");

      const validNonPrimary = {
        first_name: "Jane",
        last_name: "Doe",
        date_of_birth: "1995-05-15",
        relationship: "spouse",
        is_primary: false,
      };

      const result = CreateTravellerProfileSchema.safeParse(validNonPrimary);
      expect(result.success).toBe(true);
    });

    it("should reject invalid date format", () => {
      const { CreateTravellerProfileSchema } = require("../schemas");

      const invalidDate = {
        first_name: "John",
        last_name: "Doe",
        date_of_birth: "01/01/1990",
        is_primary: true,
      };

      const result = CreateTravellerProfileSchema.safeParse(invalidDate);
      expect(result.success).toBe(false);
      expect(result.error?.errors[0].message).toBe("Date must be in YYYY-MM-DD format");
    });
  });
});
