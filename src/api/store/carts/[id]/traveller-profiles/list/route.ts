import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";
import { MedusaError } from "@camped-ai/framework/utils";
import { CUSTOMER_TRAVELLER_MODULE } from "../../../../../../modules/customer-travellers";
import CustomerTravellerModuleService from "../../../../../../modules/customer-travellers/service";
import { ListTravellerProfilesResponseSchema } from "../schemas";

/**
 * GET /store/carts/{id}/traveller-profiles/list
 *
 * List traveller profiles associated with a cart during checkout.
 * Returns only non-deleted profiles tied to the specified cart.
 */
export const GET = async (req: MedusaRequest, res: MedusaResponse) => {
  try {
    const cart_id = req.params.id;

    if (!cart_id) {
      return res.status(400).json({
        type: "invalid_data",
        message: "Cart ID is required"
      });
    }

    // Resolve the CustomerTravellerService
    const travellerService = req.scope.resolve(CUSTOMER_TRAVELLER_MODULE) as CustomerTravellerModuleService;

    if (!travellerService?.listByCart) {
      console.error("CustomerTravellerService not properly registered");
      return res.status(500).json({
        type: "service_error",
        message: "Traveller service not available"
      });
    }

    // Get profiles for this cart
    const profiles = await travellerService.listByCart(cart_id);

    // Format response according to schema
    const response = {
      cart_id,
      profiles: profiles.map(profile => ({
        ...profile,
        date_of_birth: profile.date_of_birth.toISOString(),
        created_at: profile.created_at.toISOString(),
        updated_at: profile.updated_at.toISOString(),
        deleted_at: profile.deleted_at?.toISOString() || null,
      }))
    };

    // Validate response against schema
    const validatedResponse = ListTravellerProfilesResponseSchema.parse(response);

    return res.status(200).json(validatedResponse);
  } catch (error) {
    console.error("Error in traveller profiles list:", error);

    // Handle MedusaError instances
    if (error instanceof MedusaError) {
      const statusCode = error.type === "not_found" ? 404 :
                        error.type === "invalid_data" ? 400 : 500;
      return res.status(statusCode).json({
        type: error.type,
        message: error.message
      });
    }

    // Handle validation errors
    if ((error as any)?.name === "ZodError") {
      return res.status(500).json({
        type: "validation_error",
        message: "Response validation failed",
        errors: (error as any).errors
      });
    }

    // Generic error handling
    return res.status(500).json({
      type: "internal_server_error",
      message: "An unexpected error occurred"
    });
  }
};
