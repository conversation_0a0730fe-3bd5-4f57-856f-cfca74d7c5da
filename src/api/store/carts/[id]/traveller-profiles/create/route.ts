import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";
import { MedusaError } from "@camped-ai/framework/utils";
import { CreateTravellerProfileSchema, CreateTravellerProfileResponseSchema } from "../schemas";
import { CUSTOMER_TRAVELLER_MODULE } from "../../../../../../modules/customer-travellers";
import CustomerTravellerModuleService from "../../../../../../modules/customer-travellers/service";
import { Relationship } from "../../../../../../modules/customer-travellers/models/customer-traveller";

/**
 * POST /store/carts/{id}/traveller-profiles/create
 *
 * Create a traveller profile associated with a cart during checkout.
 * Enforces constraints: one primary traveller per customer; relationship required for additional travellers.
 */
export const POST = async (req: MedusaRequest, res: MedusaResponse) => {
  try {
    const cart_id = req.params.id;

    if (!cart_id) {
      return res.status(400).json({
        type: "invalid_data",
        message: "Cart ID is required"
      });
    }

    // Validate request body using schema
    const validation = CreateTravellerProfileSchema.safeParse(req.body);
    if (!validation.success) {
      return res.status(400).json({
        type: "validation_error",
        message: "Validation failed",
        errors: validation.error.errors,
      });
    }
    const body = validation.data;

    // Resolve the CustomerTravellerService
    const travellerService = req.scope.resolve(CUSTOMER_TRAVELLER_MODULE) as CustomerTravellerModuleService;

    if (!travellerService?.create) {
      console.error("CustomerTravellerService not properly registered");
      return res.status(500).json({
        type: "service_error",
        message: "Traveller service not available"
      });
    }

    // Create the traveller profile
    const created = await travellerService.create({
      customer_id: body.customer_id,
      cart_id,
      first_name: body.first_name,
      last_name: body.last_name,
      date_of_birth: body.date_of_birth,
      gender: body.gender || null,
      relationship: (body.relationship as Relationship) || null,
      is_primary: body.is_primary,
    });

    // Format response according to schema
    const response = {
      cart_id,
      profile: {
        ...created,
        date_of_birth: created.date_of_birth.toISOString(),
        created_at: created.created_at.toISOString(),
        updated_at: created.updated_at.toISOString(),
        deleted_at: created.deleted_at?.toISOString() || null,
      }
    };

    // Validate response against schema
    const validatedResponse = CreateTravellerProfileResponseSchema.parse(response);

    return res.status(201).json(validatedResponse);
  } catch (error) {
    console.error("Error creating traveller profile:", error);

    // Handle MedusaError instances with specific status codes
    if (error instanceof MedusaError) {
      let statusCode = 500;

      switch (error.type) {
        case "not_found":
          statusCode = 404;
          break;
        case "invalid_data":
          statusCode = 400;
          break;
        case "not_allowed":
          statusCode = 405;
          break;
        case "unauthorized":
          statusCode = 401;
          break;
        default:
          statusCode = 500;
      }

      return res.status(statusCode).json({
        type: error.type,
        message: error.message
      });
    }

    // Handle validation errors
    if ((error as any)?.name === "ZodError") {
      return res.status(500).json({
        type: "validation_error",
        message: "Response validation failed",
        errors: (error as any).errors
      });
    }

    // Generic error handling
    return res.status(500).json({
      type: "internal_server_error",
      message: "An unexpected error occurred"
    });
  }
};
