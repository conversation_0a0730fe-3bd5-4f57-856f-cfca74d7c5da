import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";
import {
  ContainerRegistrationKeys,
  remoteQueryObjectFromString,
  Modules,
  MedusaError
} from "@camped-ai/framework/utils";
import {
  createPaymentCollectionForCartWorkflow,
  createPaymentSessionsWorkflow,
} from "@camped-ai/medusa/core-flows";
import { z } from "zod";

/**
 * POST /store/carts/{id}/payment-collection
 * 
 * Creates a payment collection for partial payment (deposit) to enable cart completion
 * This supports your use case where customers pay a deposit and full payment is collected later
 */

const CreatePartialPaymentSchema = z.object({
  payment_provider_id: z.string().default("pp_stripe_stripe"), // ✅ FIX: Use configured Stripe provider
  partial_amount: z.number().positive().optional(), // Deposit amount in cents
  payment_type: z.enum(["deposit", "full", "manual"]).default("deposit"),
  metadata: z.record(z.any()).optional()
});

export const POST = async (req: MedusaRequest, res: MedusaResponse) => {
  try {
    const cartId = req.params.id;
    const validatedBody = CreatePartialPaymentSchema.parse(req.body);
    
    const {
      payment_provider_id,
      partial_amount,
      payment_type,
      metadata
    } = validatedBody;

    console.log(`🏦 Creating payment collection for cart: ${cartId}`);
    console.log(`💰 Payment type: ${payment_type}`);
    console.log(`💵 Partial amount: ${partial_amount || 'auto-calculate'}`);

    // Get cart details
    const query = req.scope.resolve(ContainerRegistrationKeys.QUERY);
    const [cart] = await query(
      remoteQueryObjectFromString({
        entryPoint: "cart",
        variables: { id: cartId },
        fields: [
          "id",
          "total",
          "currency_code",
          "customer_id",
          "email",
          "metadata",
          "items.*"
        ]
      })
    );

    if (!cart) {
      return res.status(404).json({
        message: "Cart not found"
      });
    }

    console.log(`📋 Cart total: ${cart.total} ${cart.currency_code}`);

    // Calculate payment amount based on type
    let paymentAmount: number;
    
    switch (payment_type) {
      case "deposit":
        // Default deposit: 20% of total or custom partial_amount
        paymentAmount = partial_amount || Math.round(cart.total * 0.2);
        break;
      case "full":
        paymentAmount = cart.total;
        break;
      case "manual":
        // For manual processing (concierge team handles payment)
        paymentAmount = partial_amount || 100; // Minimal amount (1 cent/unit) to satisfy Medusa
        break;
      default:
        paymentAmount = partial_amount || Math.round(cart.total * 0.2);
    }

    console.log(`💳 Payment amount: ${paymentAmount} ${cart.currency_code}`);

    // Check if payment collection already exists
    const [existingCollection] = await query(
      remoteQueryObjectFromString({
        entryPoint: "cart_payment_collection",
        variables: { filters: { cart_id: cartId } },
        fields: [
          "payment_collection.id",
          "payment_collection.status",
          "payment_collection.amount"
        ]
      })
    );

    if (existingCollection?.payment_collection) {
      console.log("⚠️ Payment collection already exists");
      return res.json({
        payment_collection: existingCollection.payment_collection,
        message: "Payment collection already exists",
        cart_id: cartId
      });
    }

    // Create payment collection for the cart
    await createPaymentCollectionForCartWorkflow(req.scope).run({
      input: {
        cart_id: cartId,
        metadata: {
          cart_id: cartId,
          cart_total: cart.total,
          payment_amount: paymentAmount,
          payment_type: payment_type,
          is_partial_payment: payment_type === "deposit",
          remaining_amount: cart.total - paymentAmount,
          created_via: "partial_payment_api",
          ...metadata
        }
      }
    });

    // Get the created payment collection
    const [cartCollectionRelation] = await query(
      remoteQueryObjectFromString({
        entryPoint: "cart_payment_collection",
        variables: { filters: { cart_id: cartId } },
        fields: [
          "payment_collection.id",
          "payment_collection.status",
          "payment_collection.amount",
          "payment_collection.currency_code",
          "payment_collection.metadata"
        ]
      })
    );

    const paymentCollection = cartCollectionRelation?.payment_collection;

    if (!paymentCollection) {
      throw new MedusaError(
        MedusaError.Types.INVALID_DATA,
        "Failed to create payment collection"
      );
    }

    console.log(`✅ Payment collection created: ${paymentCollection.id}`);

    // For manual payments, create and authorize a manual payment session
    if (payment_type === "manual" || payment_provider_id === "pp_system_default") {
      console.log("🔧 Creating manual payment session...");
      
      try {
        // Create payment session
        const { result: paymentSessions } = await createPaymentSessionsWorkflow(req.scope).run({
          input: {
            payment_collection_id: paymentCollection.id,
            provider_id: payment_provider_id,
            context: {
              extra: {
                email: cart.email,
                customer_id: cart.customer_id,
                payment_type: payment_type,
                manual_authorization: true,
                authorized_by: "system", // Could be user ID from session
                authorized_at: new Date().toISOString()
              }
            }
          }
        });

        if (paymentSessions && paymentSessions.length > 0) {
          const paymentSession = paymentSessions[0];
          console.log(`💳 Payment session created: ${paymentSession.id}`);

          // Auto-authorize manual payment session
          console.log("🔐 Authorizing manual payment session...");

          const paymentModuleService = req.scope.resolve(Modules.PAYMENT);
          await paymentModuleService.authorizePaymentSession(paymentSession.id, {
            context: {
              ...paymentSession.context,
              authorized_amount: paymentAmount,
              authorization_type: "manual",
              notes: `${payment_type} payment for cart ${cartId}`,
              authorized_at: new Date().toISOString()
            }
          });

          console.log("✅ Manual payment session authorized");
        }
      } catch (sessionError) {
        console.error("Error creating payment session:", sessionError);
        // Don't fail the request - payment collection is created
      }
    }

    res.json({
      payment_collection: {
        id: paymentCollection.id,
        status: paymentCollection.status,
        amount: paymentCollection.amount,
        currency_code: paymentCollection.currency_code,
        metadata: paymentCollection.metadata
      },
      cart_id: cartId,
      payment_type: payment_type,
      payment_amount: paymentAmount,
      cart_total: cart.total,
      remaining_amount: cart.total - paymentAmount,
      message: `${payment_type} payment collection created successfully`,
      next_steps: payment_type === "manual" 
        ? "Cart can now be completed. Full payment will be collected by concierge team."
        : "Proceed with payment authorization to complete cart."
    });

  } catch (error) {
    console.error("Error creating payment collection:", error);
    
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        message: "Validation error",
        errors: error.errors
      });
    }

    res.status(500).json({
      message: "Failed to create payment collection",
      error: error instanceof Error ? error.message : "Unknown error"
    });
  }
};
