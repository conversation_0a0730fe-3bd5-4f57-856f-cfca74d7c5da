import {
  StorePostCartLineItemsReqSchema,
  StorePostCartLineItemsReq
} from "../line-items/schemas";
import {
  MedusaRequest,
  MedusaResponse
} from "@camped-ai/framework"
import {
  Modules,
  MedusaError
} from "@camped-ai/framework/utils"

export const POST = async (
  req: MedusaRequest,
  res: MedusaResponse
) => {
  // Handle both single item and array formats
  let validatedBody: StorePostCartLineItemsReq;

  try {
    // Check if the request body is a single item or an array
    let requestBody: any = req.body;

    // If it's a single item (has variant_id directly), wrap it in an items array
    if (requestBody.variant_id && !requestBody.items) {
      requestBody = {
        items: [requestBody]
      };
    }

    validatedBody = StorePostCartLineItemsReqSchema.parse(requestBody);
  } catch (validationError: any) {
    console.error("Custom validation error:", validationError);
    throw new MedusaError(
      MedusaError.Types.INVALID_DATA,
      `Validation failed: ${validationError.message}`
    );
  }

  // Clean the cart ID by removing any leading colons
  const cartId = req.params.id?.replace(/^:+/, '') || req.params.id;

  if (!cartId) {
    throw new MedusaError(
      MedusaError.Types.INVALID_DATA,
      "Cart ID is required"
    )
  }

  const { items } = validatedBody;

  try {
    // Get cart module service
    const cartModuleService = req.scope.resolve(Modules.CART);

    // First, verify the cart exists and get its details
    let cart: any;
    try {
      cart = await cartModuleService.retrieveCart(cartId, {
        relations: ["items"]
      });
    } catch (error: any) {
      throw new MedusaError(
        MedusaError.Types.NOT_FOUND,
        `Cart with id ${cartId} not found`
      )
    }

    console.log("Found cart:", cart.id);

    // Check if any items have custom unit_price
    const hasCustomPricing = items.some(item => item.unit_price !== undefined);
    console.log("Has custom pricing:", hasCustomPricing);

    if (hasCustomPricing) {
      // Add items with custom pricing
      console.log("Adding items with custom pricing");

      // Get product module service to fetch variant/product details for titles
      const productModuleService = req.scope.resolve(Modules.PRODUCT);

      const lineItems = [];

      for (const item of items) {
        let title = item.title || `Product Item`; // Default fallback

        // Try to get a better title from variant/product data
        try {
          const variant = await productModuleService.retrieveProductVariant(item.variant_id, {
            relations: ["product"]
          });
          title = item.title || variant.title || variant.product?.title || `Product Item`;
        } catch (variantError) {
          console.warn(`Could not fetch variant details for title: ${item.variant_id}`, variantError.message);
          // Keep the default title
        }

        // Create line item with ONLY the absolute minimum fields Medusa requires
        // Let's try the most basic approach first
        const lineItem: any = {
          title: title,
          variant_id: item.variant_id,
          product_id: item.product_id,
          product_title: item.title || title,
          product_collection: item.product_collection,
          product_description: item.product_description,
          product_handle: item.product_handle,
          product_subtitle: item.product_subtitle,
          product_type: item.product_type,
          product_type_id: item.product_type_id,
          quantity: Number(item.quantity), // Ensure it's a proper number
          metadata: item.metadata,
          requires_shipping: false,
        };

        // Add optional fields only if they have valid values
        if (title) {
          lineItem.title = title;
        }
        // Only add unit_price if it's provided (custom pricing)
        if (item.unit_price !== undefined && item.unit_price !== null && typeof item.unit_price === 'number') {
          lineItem.unit_price = item.unit_price;
          lineItem.metadata.custom_price = true;
          lineItem.metadata.original_unit_price = item.unit_price;
        } else if (item.unit_price !== undefined) {
          console.warn(`⚠️ Invalid unit_price value: ${item.unit_price} (type: ${typeof item.unit_price})`);
        }

        lineItems.push(lineItem);
      }

      try {
        await cartModuleService.addLineItems(cartId, lineItems);
      } catch (addItemsError) {
        console.error("❌ Error adding line items:", addItemsError);
        console.error("❌ Line items that caused error:", JSON.stringify(lineItems, null, 2));
        throw addItemsError;
      }

      // Update cart metadata to mark as quote if it has custom pricing
      if (cart.metadata?.custom_pricing !== true) {
        await cartModuleService.updateCarts(cartId, {
          metadata: {
            ...cart.metadata,
            custom_pricing: true,
            cart_type: "quote"
          }
        });
      }
    } else {
      // Add items with default Medusa pricing
      console.log("Adding items with default Medusa pricing");

      // For default pricing, we need to fetch variant prices
      const productModuleService = req.scope.resolve(Modules.PRODUCT);
      const pricingModuleService = req.scope.resolve(Modules.PRICING);

      const lineItems = [];

      for (const item of items) {
        try {
          // Get variant details
          const variant = await productModuleService.retrieveProductVariant(item.variant_id, {
            relations: ["product"]
          });

          // Get pricing for the variant
          const priceSet = await pricingModuleService.calculatePrices(
            { id: [item.variant_id] },
            {
              context: {
                currency_code: cart.currency_code,
                region_id: cart.region_id
              }
            }
          );

          const calculatedPrice = priceSet?.[item.variant_id]?.calculated_amount || 0;

          const title = item.title || variant.title || variant.product?.title || `Product Item`;

          // Create line item with ONLY the absolute minimum fields Medusa requires
          const lineItemData: any = {
            title: title,
            variant_id: item.variant_id,
            quantity: item.quantity,
            unit_price: calculatedPrice,
            requires_shipping: false, // Default to false for hotel services
            metadata: {
              ...(item.metadata || {}),
              product_id: item.product_id,
              product_title: item.title || title,
              product_collection: item.product_collection,
              product_description: item.product_description,
              product_handle: item.product_handle,
              product_subtitle: item.product_subtitle,
              product_type: item.product_type,
              product_type_id: item.product_type_id,
              raw_compare_at_unit_price: item.raw_compare_at_unit_price,
            },
          };

          // Don't add any additional fields to avoid BigNumber issues
          // All product info is stored in metadata where it's safe

          lineItems.push(lineItemData);
        } catch (variantError) {
          console.error(`Error fetching variant ${item.variant_id}:`, variantError);
          // Fallback with minimal data
          const title = item.title || `Product Item`;

          // Create line item with ONLY the absolute minimum fields Medusa requires
          const fallbackLineItem: any = {
            title: title,
            variant_id: item.variant_id,
            quantity: item.quantity,
            unit_price: 0, // Fallback price
            requires_shipping: false, // Default to false for hotel services
            metadata: {
              ...(item.metadata || {}),
              // Store ALL product info in metadata (this is safe)
              product_id: item.product_id,
              product_title: item.title || title,
              product_collection: item.product_collection,
              product_description: item.product_description,
              product_handle: item.product_handle,
              product_subtitle: item.product_subtitle,
              product_type: item.product_type,
              product_type_id: item.product_type_id,
              raw_compare_at_unit_price: item.raw_compare_at_unit_price,
            },
          };

          // Don't add any additional fields to avoid BigNumber issues
          // All product info is stored in metadata where it's safe

          lineItems.push(fallbackLineItem);
        }
      }
      await cartModuleService.addLineItems(cartId, lineItems);
    }

    // Retrieve the updated cart with items
    try {
      const updatedCart = await cartModuleService.retrieveCart(cartId, {
        relations: ["items", "shipping_address", "billing_address"]
      });

      console.log("Line items added successfully to cart:", updatedCart.id);

      res.json({
        cart: updatedCart
      });
    } catch (retrieveError: any) {
      console.error("Error retrieving updated cart:", retrieveError);

      // Fallback: try to retrieve without relations
      try {
        const basicCart = await cartModuleService.retrieveCart(cartId);
        console.log("Retrieved basic cart:", basicCart.id);

        res.json({
          cart: basicCart
        });
      } catch (basicRetrieveError: any) {
        console.error("Error retrieving basic cart:", basicRetrieveError);
        throw new MedusaError(
          MedusaError.Types.INVALID_DATA,
          `Line items added but could not retrieve cart: ${basicRetrieveError.message}`
        )
      }
    }

  } catch (error: any) {
    console.error("Error adding line items to cart:", error);

    if (error instanceof MedusaError) {
      throw error;
    }

    throw new MedusaError(
      MedusaError.Types.INVALID_DATA,
      `Failed to add line items to cart: ${error.message}`
    )
  }
}
