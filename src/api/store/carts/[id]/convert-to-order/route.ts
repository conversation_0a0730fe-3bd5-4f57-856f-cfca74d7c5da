import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";
import {
  ContainerRegistrationKeys,
  remoteQueryObjectFromString,
  Modules,
  MedusaError,
} from "@camped-ai/framework/utils";
import { z } from "zod";

/**
 * POST /store/carts/{id}/convert-to-order
 * 
 * Direct cart to order conversion bypassing payment session complexities
 * Creates order directly from cart data for manual payment workflows
 */

const ConvertToOrderSchema = z.object({
  payment_type: z.enum(["deposit", "full", "manual"]).default("manual"),
  partial_amount: z.number().positive().optional(),
  notes: z.string().optional(),
  collected_by: z.string().default("sales_team"),
  metadata: z.record(z.any()).optional()
});

export const POST = async (req: MedusaRequest, res: MedusaResponse) => {
  try {
    const cartId = req.params.id;
    const validatedBody = ConvertToOrderSchema.parse(req.body);
    
    const {
      payment_type,
      partial_amount,
      notes,
      collected_by,
      metadata = {}
    } = validatedBody;

    console.log(`🔄 Converting cart to order: ${cartId}`);
    console.log(`💰 Payment type: ${payment_type}`);

    // Get cart details
    const query = req.scope.resolve(ContainerRegistrationKeys.QUERY);
    const [cart] = await query(
      remoteQueryObjectFromString({
        entryPoint: "cart",
        variables: { id: cartId },
        fields: [
          "id",
          "total",
          "currency_code",
          "customer_id",
          "email",
          "metadata",
          "items.*",
          "items.variant.*",
          "items.product.*",
          "shipping_address.*",
          "billing_address.*"
        ]
      })
    );

    if (!cart) {
      return res.status(404).json({
        message: "Cart not found"
      });
    }

    console.log(`📋 Cart total: ${cart.total} ${cart.currency_code}`);

    // Calculate payment amounts
    let paymentAmount: number;
    
    switch (payment_type) {
      case "deposit":
        paymentAmount = partial_amount || Math.round(cart.total * 0.2);
        break;
      case "full":
        paymentAmount = cart.total;
        break;
      case "manual":
        paymentAmount = partial_amount || 0;
        break;
      default:
        paymentAmount = partial_amount || 0;
    }

    const remainingAmount = cart.total - paymentAmount;

    console.log(`💳 Payment amount: ${paymentAmount}`);
    console.log(`💰 Remaining amount: ${remainingAmount}`);

    // Create order directly using order module
    const orderModuleService = req.scope.resolve(Modules.ORDER);
    
    const orderData = {
      currency_code: cart.currency_code,
      email: cart.email,
      customer_id: cart.customer_id,
      total: cart.total,
      subtotal: cart.total, // Simplified - you might want to calculate this properly
      tax_total: 0, // Simplified
      shipping_total: 0, // Simplified
      discount_total: 0, // Simplified
      gift_card_total: 0, // Simplified
      gift_card_tax_total: 0, // Simplified
      status: "pending", // Order status
      display_id: Math.floor(Math.random() * 1000000), // Simple display ID
      cart_id: cartId,
      region_id: cart.region_id,
      sales_channel_id: cart.sales_channel_id,
      shipping_address: cart.shipping_address,
      billing_address: cart.billing_address,
      metadata: {
        ...cart.metadata,
        converted_from_cart: true,
        conversion_method: "manual",
        payment_tracking: {
          payment_type: payment_type,
          paid_amount: paymentAmount,
          remaining_amount: remainingAmount,
          total_amount: cart.total,
          currency_code: cart.currency_code,
          requires_additional_payment: remainingAmount > 0,
          completed_at: new Date().toISOString(),
          manual_conversion: true,
          collected_by: collected_by,
          notes: notes
        },
        concierge_notes: remainingAmount > 0 
          ? `Remaining payment of ${remainingAmount} ${cart.currency_code} to be collected by ${collected_by}`
          : "Full payment completed",
        manual_completion: {
          completed_at: new Date().toISOString(),
          completed_by: collected_by,
          notes: notes,
          requires_payment_collection: remainingAmount > 0
        },
        ...metadata
      }
    };

    console.log("Creating order with data:", JSON.stringify(orderData, null, 2));

    const order = await orderModuleService.createOrders(orderData);
    
    console.log(`🎉 Order created: ${order.id}`);

    // Create order items from cart items
    if (cart.items && cart.items.length > 0) {
      console.log(`📦 Creating ${cart.items.length} order items...`);
      
      const orderItems = cart.items.map(item => ({
        order_id: order.id,
        variant_id: item.variant_id,
        product_id: item.product_id,
        title: item.title,
        subtitle: item.subtitle,
        thumbnail: item.thumbnail,
        quantity: item.quantity,
        unit_price: item.unit_price,
        total: item.total || (item.unit_price * item.quantity),
        subtotal: item.subtotal || (item.unit_price * item.quantity),
        tax_total: item.tax_total || 0,
        discount_total: item.discount_total || 0,
        metadata: item.metadata || {}
      }));

      await orderModuleService.createOrderLineItems(orderItems);
      console.log(`✅ Created ${orderItems.length} order items`);
    }

    // Mark cart as completed (optional)
    const cartModuleService = req.scope.resolve(Modules.CART);
    await cartModuleService.updateCarts(cartId, {
      completed_at: new Date(),
      metadata: {
        ...cart.metadata,
        converted_to_order: order.id,
        conversion_date: new Date().toISOString()
      }
    });

    // Emit order.created event for concierge synchronization
    console.log("🔄 [CONVERT-TO-ORDER] Emitting order.created event for concierge sync");
    const eventModuleService = req.scope.resolve(Modules.EVENT_BUS);

    await eventModuleService.emit({
      name: "order.created",
      data: {
        id: order.id,
        order_id: order.id,
        customer_id: order.customer_id,
        email: order.email,
        cart_id: cartId,
        is_draft_order: false,
        total_items: cart.items?.length || 0,
        conversion_method: "direct",
        manual_conversion: true
      },
    });

    console.log("✅ [CONVERT-TO-ORDER] Emitted order.created event for order:", order.id);

    res.json({
      success: true,
      order: {
        id: order.id,
        display_id: order.display_id,
        total: order.total,
        currency_code: order.currency_code,
        status: order.status,
        customer_id: order.customer_id,
        email: order.email,
        created_at: order.created_at
      },
      payment_info: {
        payment_type: payment_type,
        paid_amount: paymentAmount,
        remaining_amount: remainingAmount,
        total_amount: cart.total,
        currency_code: cart.currency_code,
        requires_additional_payment: remainingAmount > 0,
        manual_conversion: true
      },
      cart_id: cartId,
      conversion_method: "direct",
      message: payment_type === "manual" && paymentAmount === 0
        ? `Order created successfully. Full payment of ${cart.total} ${cart.currency_code} to be collected by ${collected_by}.`
        : remainingAmount > 0
          ? `Order created with ${payment_type} payment. Remaining ${remainingAmount} ${cart.currency_code} to be collected by ${collected_by}.`
          : "Order created with full payment completed.",
      next_steps: remainingAmount > 0 
        ? [
            "Order is confirmed and created",
            paymentAmount > 0 ? `${collected_by} has collected ${paymentAmount} ${cart.currency_code}` : "No payment collected yet",
            `${collected_by} should collect remaining payment`,
            `Amount to collect: ${remainingAmount} ${cart.currency_code}`,
            "Use admin API to mark payment as collected when received",
            `POST /admin/orders/${order.id}/mark-payment-collected`
          ]
        : [
            "Order is confirmed and created",
            "Full payment completed",
            "No additional payment required"
          ],
      admin_actions: {
        mark_payment_collected_url: `/admin/orders/${order.id}/mark-payment-collected`,
        payment_status_url: `/admin/orders/${order.id}/payment-status`,
        example_payload: {
          amount: remainingAmount,
          payment_method: "bank_transfer",
          collected_by: collected_by,
          reference_number: "TXN_123456",
          notes: remainingAmount > 0 ? "Remaining payment collected" : "Full payment collected"
        }
      }
    });

  } catch (error) {
    console.error("Error converting cart to order:", error);
    
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        message: "Validation error",
        errors: error.errors
      });
    }

    if (error instanceof MedusaError) {
      return res.status(400).json({
        type: error.type,
        message: error.message
      });
    }

    res.status(500).json({
      message: "Failed to convert cart to order",
      error: error instanceof Error ? error.message : "Unknown error"
    });
  }
};
