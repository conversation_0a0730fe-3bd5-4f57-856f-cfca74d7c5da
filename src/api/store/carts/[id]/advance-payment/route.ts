import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";
import {
  ContainerRegistrationKeys,
  remoteQueryObjectFromString,
  MedusaError,
  Modules,
} from "@camped-ai/framework/utils";
import {
  createPaymentCollectionForCartWorkflow,
  createPaymentSessionsWorkflow,
} from "@camped-ai/medusa/core-flows";
import { z } from "zod";

/**
 * POST /store/carts/{id}/advance-payment
 * 
 * Initiate advance payment collection for cart without completing it.
 * Supports both Stripe (with client secret) and manual payment modes.
 * 
 * This is the first step in the advance payment workflow:
 * 1. Initiate advance payment (this endpoint)
 * 2. Record payment (record-payment endpoint)
 * 3. Complete cart with advance (complete-with-advance endpoint)
 */

const AdvancePaymentSchema = z.object({
  payment_mode: z.enum(["stripe", "manual"]).default("manual"),
  advance_amount: z.number().positive(),
  currency_code: z.string().optional(),
  metadata: z.record(z.any()).optional(),
  // Stripe-specific options
  stripe_options: z.object({
    return_url: z.string().url().optional(),
    automatic_payment_methods: z.boolean().default(true),
  }).optional(),
});

export const POST = async (req: MedusaRequest, res: MedusaResponse) => {
  try {
    const cartId = req.params.id;
    const validatedBody = AdvancePaymentSchema.parse(req.body);
    
    const {
      payment_mode,
      advance_amount,
      currency_code,
      metadata = {},
      stripe_options = {}
    } = validatedBody;

    console.log(`🚀 Initiating advance payment for cart: ${cartId}`);
    console.log(`💰 Payment mode: ${payment_mode}, Amount: ${advance_amount}`);

    // Get cart details using the same pattern as hotel-management API
    const queryService = req.scope.resolve(ContainerRegistrationKeys.REMOTE_QUERY);
    const [cart] = await queryService(
      remoteQueryObjectFromString({
        entryPoint: "cart",
        variables: { filters: { id: cartId } }, // Use filters object like hotel API
        fields: [
          "id",
          "total",
          "subtotal", // Add subtotal for consistency
          "tax_total", // Add tax_total for consistency
          "currency_code",
          "customer_id",
          "email",
          "metadata",
          "items.*"
        ]
      })
    );

    if (!cart) {
      return res.status(404).json({
        message: "Cart not found",
        cart_id: cartId
      });
    }

    // Validate advance amount
    if (advance_amount > cart.total) {
      return res.status(400).json({
        message: "Advance amount cannot exceed cart total",
        cart_total: cart.total,
        advance_amount: advance_amount
      });
    }

    const remainingAmount = cart.total - advance_amount;
    const effectiveCurrency = currency_code || cart.currency_code;

    console.log(`📋 Cart total: ${cart.total} ${effectiveCurrency}`);
    console.log(`💳 Advance amount: ${advance_amount} ${effectiveCurrency}`);
    console.log(`💰 Remaining amount: ${remainingAmount} ${effectiveCurrency}`);

    // Check if payment collection already exists
    const [existingCollection] = await queryService(
      remoteQueryObjectFromString({
        entryPoint: "cart_payment_collection",
        variables: { filters: { cart_id: cartId } },
        fields: [
          "payment_collection.id",
          "payment_collection.status",
          "payment_collection.amount",
          "payment_collection.metadata"
        ]
      })
    );

    if (existingCollection?.payment_collection) {
      console.log("⚠️ Payment collection already exists");
      return res.status(400).json({
        message: "Advance payment already initiated for this cart",
        payment_collection_id: existingCollection.payment_collection.id,
        existing_amount: existingCollection.payment_collection.amount,
        cart_id: cartId
      });
    }

    // Step 1: Create payment collection using workflow (creates cart relationship)
    console.log("🏦 Creating payment collection for advance payment...");
    console.log(`💰 Will create payment collection and then update amount to ${advance_amount} ${effectiveCurrency}`);

    // ✅ CRITICAL FIX: Create payment collection with fallback for link issues
    try {
      await createPaymentCollectionForCartWorkflow(req.scope).run({
        input: {
          cart_id: cartId,
          metadata: {
            cart_id: cartId,
            cart_total: cart.total,
            advance_amount: advance_amount,
            remaining_amount: remainingAmount,
            payment_mode: payment_mode,
            currency_code: effectiveCurrency,
            is_advance_payment: true,
            advance_payment_status: "initiated",
            created_via: "advance_payment_api",
            created_at: new Date().toISOString(),
            ...metadata
          }
        }
      });

      console.log("✅ Payment collection created via workflow");

    } catch (workflowError) {
      console.error("❌ Workflow failed, creating payment collection manually:", workflowError.message);

      // Manual payment collection creation as fallback
      const paymentModuleService = req.scope.resolve(Modules.PAYMENT);

      // Create payment collection directly
      const paymentCollections = await paymentModuleService.createPaymentCollections([{
        currency_code: effectiveCurrency,
        amount: cart.total, // Start with full amount, will update later
        metadata: {
          cart_id: cartId,
          cart_total: cart.total,
          advance_amount: advance_amount,
          remaining_amount: remainingAmount,
          payment_mode: payment_mode,
          currency_code: effectiveCurrency,
          is_advance_payment: true,
          advance_payment_status: "initiated",
          created_via: "advance_payment_api_manual",
          created_at: new Date().toISOString(),
          ...metadata
        }
      }]);

      const paymentCollection = paymentCollections[0];
      console.log(`✅ Payment collection created manually: ${paymentCollection.id}`);

      // Create cart-payment collection link manually
      try {
        const manager: any = req.scope.resolve("manager");

        await manager.query(`
          INSERT INTO cart_payment_collection (cart_id, payment_collection_id, created_at, updated_at)
          VALUES ($1, $2, NOW(), NOW())
          ON CONFLICT (cart_id, payment_collection_id) DO NOTHING
        `, [cartId, paymentCollection.id]);

        console.log(`✅ Cart-payment collection link created manually`);

      } catch (linkError) {
        console.error("❌ Manual cart-payment collection link creation failed:", linkError.message);
        // Continue - the payment collection still exists
      }
    }

    // Step 2: Get the created payment collection
    const [cartCollectionRelation] = await queryService(
      remoteQueryObjectFromString({
        entryPoint: "cart_payment_collection",
        variables: { filters: { cart_id: cartId } },
        fields: [
          "payment_collection.id",
          "payment_collection.status",
          "payment_collection.amount",
          "payment_collection.currency_code",
          "payment_collection.metadata"
        ]
      })
    );

    const paymentCollection = cartCollectionRelation?.payment_collection;

    if (!paymentCollection) {
      throw new MedusaError(
        MedusaError.Types.INVALID_DATA,
        "Failed to create payment collection"
      );
    }

    console.log(`📋 Payment collection created: ${paymentCollection.id}`);
    console.log(`⚠️  Initial amount: ${paymentCollection.amount} ${paymentCollection.currency_code} (cart total)`);

    // Step 3: Update payment collection amount to advance amount
    console.log(`🔧 Updating payment collection amount to advance amount: ${advance_amount} ${effectiveCurrency}`);

    const paymentModuleService = req.scope.resolve(Modules.PAYMENT);
    await paymentModuleService.updatePaymentCollections(paymentCollection.id, {
      amount: advance_amount, // ✅ CRITICAL: Update to advance amount, not cart total
      metadata: {
        ...paymentCollection.metadata,
        amount_updated: true,
        original_amount: paymentCollection.amount,
        updated_amount: advance_amount,
        updated_at: new Date().toISOString()
      }
    });

    console.log(`✅ Payment collection amount updated: ${advance_amount} ${effectiveCurrency}`);

    // Use the updated payment collection for response
    const finalPaymentCollection = {
      id: paymentCollection.id,
      amount: advance_amount, // Use the advance amount
      currency_code: paymentCollection.currency_code,
      status: paymentCollection.status,
      metadata: {
        ...paymentCollection.metadata,
        amount_updated: true,
        original_amount: paymentCollection.amount,
        updated_amount: advance_amount,
        updated_at: new Date().toISOString()
      }
    };

    console.log(`✅ Payment collection ready: ${finalPaymentCollection.id}`);

    let responseData: any = {
      success: true,
      payment_collection_id: finalPaymentCollection.id,
      cart_id: cartId,
      payment_mode: payment_mode,
      advance_amount: advance_amount,
      remaining_amount: remainingAmount,
      currency_code: effectiveCurrency,
      status: "initiated",
      payment_collection_amount: finalPaymentCollection.amount, // ✅ Should equal advance_amount
      message: `Advance payment of ${advance_amount} ${effectiveCurrency} initiated successfully`
    };

    // Step 2: Handle payment mode-specific setup
    if (payment_mode === "stripe") {
      console.log("💳 Creating Stripe payment session...");
      
      try {
        const { result: paymentSessions } = await createPaymentSessionsWorkflow(req.scope).run({
          input: {
            payment_collection_id: finalPaymentCollection.id,
            provider_id: "pp_stripe_stripe", // ✅ CONFIRMED: Using configured Stripe provider
            context: {
              extra: {
                email: cart.email,
                customer_id: cart.customer_id,
                payment_type: "advance",
                advance_amount: advance_amount,
                remaining_amount: remainingAmount,
                cart_total: cart.total,
                return_url: stripe_options.return_url,
                automatic_payment_methods: stripe_options.automatic_payment_methods,
                metadata: {
                  cart_id: cartId,
                  payment_mode: "stripe",
                  is_advance_payment: true,
                  ...metadata
                }
              }
            }
          }
        });

        if (!paymentSessions) {
          throw new MedusaError(
            MedusaError.Types.INVALID_DATA,
            "Failed to create Stripe payment session - no result returned"
          );
        }

        // Handle both array and single object responses
        const paymentSessionsArray = Array.isArray(paymentSessions) ? paymentSessions : [paymentSessions];

        if (paymentSessionsArray.length === 0) {
          throw new MedusaError(
            MedusaError.Types.INVALID_DATA,
            "Failed to create Stripe payment session - empty result"
          );
        }

        const paymentSession = paymentSessionsArray[0];
        console.log(`💳 Stripe payment session created: ${paymentSession.id}`);

        // Add Stripe-specific data to response
        responseData.stripe = {
          payment_session_id: paymentSession.id,
          client_secret: paymentSession.data?.client_secret || null,
          status: paymentSession.status,
          next_action: "complete_stripe_payment"
        };

        responseData.next_steps = [
          "Use the client_secret to complete Stripe payment on frontend",
          "After successful payment, call POST /store/carts/{id}/record-payment",
          "Finally call POST /store/carts/{id}/complete-with-advance to create order"
        ];

      } catch (stripeError) {
        console.error("Error creating Stripe payment session:", stripeError);
        throw new MedusaError(
          MedusaError.Types.INVALID_DATA,
          `Failed to create Stripe payment session: ${stripeError.message}`
        );
      }

    } else if (payment_mode === "manual") {
      console.log("📝 Setting up manual payment collection...");
      
      // For manual mode, no payment session needed
      responseData.manual = {
        collection_method: "manual",
        status: "awaiting_payment",
        next_action: "record_manual_payment"
      };

      responseData.next_steps = [
        "Collect payment manually (cash, bank transfer, etc.)",
        "Record payment details using POST /store/carts/{id}/record-payment",
        "Finally call POST /store/carts/{id}/complete-with-advance to create order"
      ];
    }

    res.json(responseData);

  } catch (error) {
    console.error("Error initiating advance payment:", error);
    
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        message: "Validation error",
        errors: error.errors
      });
    }

    if (error instanceof MedusaError) {
      return res.status(400).json({
        type: error.type,
        message: error.message
      });
    }

    res.status(500).json({
      message: "Failed to initiate advance payment",
      error: error instanceof Error ? error.message : "Unknown error"
    });
  }
};
