import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";
import {
  ContainerRegistrationKeys,
  remoteQueryObjectFromString,
  Modules,
  MedusaError,
} from "@camped-ai/framework/utils";
import {
  createPaymentSessionsWorkflow,
} from "@camped-ai/medusa/core-flows";
import { z } from "zod";

/**
 * POST /store/carts/{id}/record-payment
 * 
 * Record payment completion for advance payment.
 * Handles both manual payment recording and Stripe payment confirmation.
 * 
 * This is the second step in the advance payment workflow:
 * 1. Initiate advance payment (advance-payment endpoint)
 * 2. Record payment (this endpoint)
 * 3. Complete cart with advance (complete-with-advance endpoint)
 */

const RecordPaymentSchema = z.object({
  payment_mode: z.enum(["stripe", "manual"]),
  payment_collection_id: z.string(), // ✅ REQUIRED: Explicit payment collection reference
  force_convert_to_advance: z.boolean().optional(), // ✅ OPTIONAL: Convert existing collection to advance payment
  // For manual payments
  manual_payment: z.object({
    payment_method: z.string(), // e.g., "bank_transfer", "cash", "check"
    reference_number: z.string().optional(),
    collected_by: z.string(),
    collection_date: z.string().optional(), // ISO date string
    notes: z.string().optional(),
    amount_received: z.number().positive(),
  }).optional(),
  // For Stripe payments
  stripe_payment: z.object({
    payment_session_id: z.string(),
    payment_intent_id: z.string().optional(),
    stripe_payment_method_id: z.string().optional(),
  }).optional(),
  metadata: z.record(z.any()).optional(),
});

export const POST = async (req: MedusaRequest, res: MedusaResponse) => {
  try {
    const cartId = req.params.id;
    const validatedBody = RecordPaymentSchema.parse(req.body);
    
    const {
      payment_mode,
      payment_collection_id,
      force_convert_to_advance = false,
      manual_payment,
      stripe_payment,
      metadata = {}
    } = validatedBody;

    console.log(`💳 Recording payment for cart: ${cartId}`);
    console.log(`💰 Payment mode: ${payment_mode}`);

    // Get cart details using the same pattern as hotel-management API
    const queryService = req.scope.resolve(ContainerRegistrationKeys.REMOTE_QUERY);
    const [cart] = await queryService(
      remoteQueryObjectFromString({
        entryPoint: "cart",
        variables: { filters: { id: cartId } },
        fields: [
          "id",
          "total",
          "currency_code",
          "customer_id",
          "email",
          "metadata"
        ]
      })
    );

    if (!cart) {
      return res.status(404).json({
        message: "Cart not found"
      });
    }

    // ✅ ENHANCED: Get specific payment collection by ID and validate cart relationship
    const [cartCollectionRelation] = await queryService(
      remoteQueryObjectFromString({
        entryPoint: "cart_payment_collection",
        variables: {
          filters: {
            cart_id: cartId,
            payment_collection_id: payment_collection_id // ✅ Validate specific collection
          }
        },
        fields: [
          "payment_collection.id",
          "payment_collection.status",
          "payment_collection.amount",
          "payment_collection.currency_code",
          "payment_collection.metadata"
        ]
      })
    );

    const paymentCollection = cartCollectionRelation?.payment_collection;

    if (!paymentCollection) {
      return res.status(400).json({
        message: "Payment collection not found or does not belong to this cart.",
        cart_id: cartId,
        payment_collection_id: payment_collection_id
      });
    }

    // ✅ ENHANCED: Validate payment collection ID matches
    if (paymentCollection.id !== payment_collection_id) {
      return res.status(400).json({
        message: "Payment collection ID mismatch.",
        cart_id: cartId,
        expected_payment_collection_id: payment_collection_id,
        found_payment_collection_id: paymentCollection.id
      });
    }

    console.log(`🏦 Payment collection found: ${paymentCollection.id}`);
    console.log(`📋 Payment collection amount: ${paymentCollection.amount}`);
    console.log(`💰 Payment collection metadata:`, JSON.stringify(paymentCollection.metadata, null, 2));

    // Validate payment collection metadata
    const collectionMetadata = paymentCollection.metadata || {};

    // ✅ ENHANCED: Handle existing payment collections with conversion option
    if (!collectionMetadata.is_advance_payment) {
      console.log("⚠️ Payment collection is not marked as advance payment:");
      console.log(`   - Payment collection ID: ${paymentCollection.id}`);
      console.log(`   - Created via: ${collectionMetadata.created_via || 'unknown'}`);
      console.log(`   - Has is_advance_payment flag: ${!!collectionMetadata.is_advance_payment}`);
      console.log(`   - Available metadata keys: ${Object.keys(collectionMetadata).join(', ')}`);
      console.log(`   - Force convert flag: ${force_convert_to_advance}`);

      if (!force_convert_to_advance) {
        return res.status(400).json({
          message: "Payment collection is not for advance payment. Please use the advance-payment API to create the payment collection first, or set force_convert_to_advance: true to convert this collection.",
          payment_collection_id: paymentCollection.id,
          cart_id: cartId,
          debug_info: {
            created_via: collectionMetadata.created_via || 'unknown',
            has_advance_payment_flag: !!collectionMetadata.is_advance_payment,
            metadata_keys: Object.keys(collectionMetadata),
            collection_amount: paymentCollection.amount,
            collection_currency: paymentCollection.currency_code
          },
          solutions: [
            "Option 1: Use POST /store/carts/{id}/advance-payment to create a proper advance payment collection",
            "Option 2: Add 'force_convert_to_advance: true' to this request to convert the existing collection",
            "Option 3: Use GET /store/carts/{id}/payment-collections to debug existing collections"
          ]
        });
      }

      // ✅ CONVERT: Update existing payment collection to advance payment
      console.log("🔄 Converting existing payment collection to advance payment...");

      const paymentModuleService = req.scope.resolve(Modules.PAYMENT);
      await paymentModuleService.updatePaymentCollections(paymentCollection.id, {
        metadata: {
          ...collectionMetadata,
          is_advance_payment: true,
          advance_payment_status: "initiated",
          converted_to_advance: true,
          converted_at: new Date().toISOString(),
          original_created_via: collectionMetadata.created_via || 'unknown',
          created_via: "record_payment_conversion",
          advance_amount: paymentCollection.amount,
          cart_total: cart.total,
          remaining_amount: cart.total - paymentCollection.amount,
          payment_mode: payment_mode
        }
      });

      console.log("✅ Payment collection converted to advance payment");

      // ✅ ENHANCED: Update local metadata for further processing with proper calculations
      const advanceAmount = paymentCollection.amount;
      const remainingAmount = cart.total - advanceAmount;

      console.log(`🔄 Conversion calculations:`);
      console.log(`   - Cart total: ${cart.total}`);
      console.log(`   - Payment collection amount (advance): ${advanceAmount}`);
      console.log(`   - Calculated remaining: ${remainingAmount}`);

      collectionMetadata.is_advance_payment = true;
      collectionMetadata.advance_payment_status = "initiated";
      collectionMetadata.advance_amount = advanceAmount;
      collectionMetadata.remaining_amount = remainingAmount;
      collectionMetadata.cart_total = cart.total;
    }

    if (collectionMetadata.advance_payment_status === "recorded") {
      return res.status(400).json({
        message: "Payment already recorded for this cart",
        payment_collection_id: paymentCollection.id,
        cart_id: cartId
      });
    }

    let paymentSessionId: string | null = null;
    let transactionData: any = {};

    // Handle payment mode-specific recording
    if (payment_mode === "manual") {
      if (!manual_payment) {
        return res.status(400).json({
          message: "Manual payment details required for manual payment mode"
        });
      }

      console.log("📝 Recording manual payment...");

      // ✅ ENHANCED: Validate amount with better error handling
      const expectedAmount = collectionMetadata.advance_amount || paymentCollection.amount;

      console.log(`💰 Amount validation:`);
      console.log(`   - Expected (from metadata): ${collectionMetadata.advance_amount}`);
      console.log(`   - Expected (from collection): ${paymentCollection.amount}`);
      console.log(`   - Received: ${manual_payment.amount_received}`);
      console.log(`   - Cart total: ${cart.total}`);

      if (manual_payment.amount_received !== expectedAmount) {
        return res.status(400).json({
          message: "Amount received does not match expected advance amount",
          expected_amount: expectedAmount,
          received_amount: manual_payment.amount_received,
          debug_info: {
            metadata_advance_amount: collectionMetadata.advance_amount,
            collection_amount: paymentCollection.amount,
            cart_total: cart.total,
            currency_code: paymentCollection.currency_code
          }
        });
      }

      // ✅ WORKFLOW-BASED: Create and authorize manual payment session using standard workflows
      console.log("💳 Creating manual payment session using standard workflows...");

      try {
        // Create payment session using workflow - use manual provider for manual payments
        const { result: paymentSessions } = await createPaymentSessionsWorkflow(req.scope).run({
          input: {
            payment_collection_id: paymentCollection.id,
            provider_id: "pp_system_default", // Use manual provider for manual payments
            context: {
              extra: {
                email: cart.email,
                customer_id: cart.customer_id,
                payment_type: "advance",
                payment_method: manual_payment.payment_method,
                reference_number: manual_payment.reference_number,
                collected_by: manual_payment.collected_by,
                collection_date: manual_payment.collection_date || new Date().toISOString(),
                notes: manual_payment.notes,
                amount_received: manual_payment.amount_received,
                manual_recording: true,
                metadata: {
                  cart_id: cartId,
                  payment_mode: "manual",
                  is_advance_payment: true,
                  ...metadata
                }
              }
            }
          }
        });

        // Handle both array and single object responses
        const paymentSessionsArray = Array.isArray(paymentSessions) ? paymentSessions : [paymentSessions];

        if (!paymentSessionsArray || paymentSessionsArray.length === 0) {
          throw new MedusaError(
            MedusaError.Types.INVALID_DATA,
            "Failed to create manual payment session - required for cart completion"
          );
        }

        paymentSessionId = paymentSessionsArray[0].id;
        console.log(`💳 Manual payment session created: ${paymentSessionId}`);

      } catch (sessionError) {
        console.error("Failed to create payment session:", sessionError);
        throw new MedusaError(
          MedusaError.Types.PAYMENT_AUTHORIZATION_ERROR,
          `Failed to create payment session: ${sessionError.message}`
        );
      }

      // ✅ WORKFLOW-ALIGNED: Leave payment session in pending status for completeCartWorkflow
      console.log("� Authorizing payment session using standard workflow...");

      // For manual payments, we leave the session in pending status for completeCartWorkflow
      console.log("💡 Leaving payment session in pending status for completeCartWorkflow to handle");
      console.log("✅ Manual payment session created - completeCartWorkflow will handle authorization");

      transactionData = {
        type: "manual",
        payment_method: manual_payment.payment_method,
        reference_number: manual_payment.reference_number,
        collected_by: manual_payment.collected_by,
        collection_date: manual_payment.collection_date || new Date().toISOString(),
        notes: manual_payment.notes,
        amount_received: manual_payment.amount_received,
        currency_code: paymentCollection.currency_code
      };

    } else if (payment_mode === "stripe") {
      if (!stripe_payment) {
        return res.status(400).json({
          message: "Stripe payment details required for Stripe payment mode"
        });
      }

      console.log("💳 Confirming Stripe payment using workflow...");

      paymentSessionId = stripe_payment.payment_session_id;

      // ✅ WORKFLOW-ALIGNED: Leave Stripe payment session in pending status for completeCartWorkflow
      console.log("💡 Leaving Stripe payment session in pending status for completeCartWorkflow to handle");
      console.log("✅ Stripe payment session found - completeCartWorkflow will handle authorization");

      transactionData = {
        type: "stripe",
        payment_session_id: paymentSessionId,
        payment_intent_id: stripe_payment.payment_intent_id,
        payment_method_id: stripe_payment.stripe_payment_method_id,
        amount_received: collectionMetadata.advance_amount,
        currency_code: paymentCollection.currency_code,
        status: "pending_authorization",
        recorded_at: new Date().toISOString()
      };
    }

    // ✅ WORKFLOW-BASED: Update payment collection metadata only (status managed by workflows)
    console.log("🔄 Updating payment collection metadata...");
    console.log(`🔍 DEBUG: Payment Collection ID: ${paymentCollection.id}`);
    console.log(`🔍 DEBUG: Payment Mode: ${payment_mode}`);

    const paymentCollectionService = req.scope.resolve(Modules.PAYMENT);
    const authorizedAmount = payment_mode === "manual"
      ? manual_payment.amount_received
      : paymentCollection.amount;

    console.log(`🔍 DEBUG: Authorized Amount Calculation: ${authorizedAmount}`);
    console.log(`🔍 DEBUG: Manual Payment Amount: ${manual_payment?.amount_received}`);
    console.log(`🔍 DEBUG: Collection Amount: ${paymentCollection.amount}`);

    try {
      // Only update metadata - let workflows handle status and amounts
      const updateData = {
        metadata: {
          ...collectionMetadata,
          advance_payment_status: "authorized",
          payment_recorded_at: new Date().toISOString(),
          payment_session_id: paymentSessionId,
          transaction_data: transactionData,
          // ✅ WORKFLOW-ALIGNED: Track expected amounts but don't mark as authorized yet
          expected_amount: authorizedAmount,
          payment_completion_method: payment_mode === "manual" ? "manual_advance_payment" : "stripe_advance_payment",
          workflow_authorization_pending: true,
          ...metadata
        }
      };

      console.log(`🔍 DEBUG: Payment Collection Update Data:`, JSON.stringify(updateData, null, 2));

      await paymentCollectionService.updatePaymentCollections(paymentCollection.id, updateData);

      console.log("✅ Payment collection metadata updated");
      console.log(`   Expected Amount: ${authorizedAmount} ${paymentCollection.currency_code}`);
      console.log(`   Payment Mode: ${payment_mode}`);
      console.log(`   Status: Pending authorization by completeCartWorkflow`);

    } catch (updateError) {
      console.error("❌ Failed to update payment collection metadata:", updateError.message);
      // Don't throw - metadata update failure is not critical
      console.log("⚠️ Continuing with workflow - metadata update failure is not critical");
    }

    res.json({
      success: true,
      payment_collection_id: paymentCollection.id,
      cart_id: cartId,
      payment_mode: payment_mode,
      payment_session_id: paymentSessionId,
      transaction_data: transactionData,
      status: "authorized",
      // ✅ ENHANCED: Payment status summary
      payment_summary: {
        cart_total: cart.total,
        advance_amount: collectionMetadata.advance_amount,
        remaining_amount: collectionMetadata.remaining_amount,
        currency_code: paymentCollection.currency_code,
        payment_status: collectionMetadata.remaining_amount > 0 ? "partial" : "complete",
        amount_paid: collectionMetadata.advance_amount,
        amount_remaining: collectionMetadata.remaining_amount
      },
      advance_amount: collectionMetadata.advance_amount,
      remaining_amount: collectionMetadata.remaining_amount,
      currency_code: paymentCollection.currency_code,
      message: `${payment_mode === "manual" ? "Manual" : "Stripe"} advance payment of ${collectionMetadata.advance_amount} ${paymentCollection.currency_code} recorded successfully - pending authorization`,
      next_steps: [
        "✅ Payment has been recorded",
        "✅ Payment session created in pending status",
        "⏳ Authorization will be handled by completeCartWorkflow",
        "📋 Call POST /store/carts/{id}/complete to create order (workflow will handle authorization)",
        `💰 Remaining balance: ${collectionMetadata.remaining_amount} ${paymentCollection.currency_code}`
      ]
    });

  } catch (error) {
    console.error("Error recording payment:", error);
    
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        message: "Validation error",
        errors: error.errors
      });
    }

    if (error instanceof MedusaError) {
      return res.status(400).json({
        type: error.type,
        message: error.message
      });
    }

    res.status(500).json({
      message: "Failed to record payment",
      error: error instanceof Error ? error.message : "Unknown error"
    });
  }
};
