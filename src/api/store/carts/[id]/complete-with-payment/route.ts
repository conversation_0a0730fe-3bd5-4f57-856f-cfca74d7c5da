import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";
import {
  ContainerRegistrationKeys,
  remoteQueryObjectFromString,
  Modules,
  MedusaError,
} from "@camped-ai/framework/utils";
import {
  completeCartWorkflow,
  createPaymentCollectionForCartWorkflow,
  createPaymentSessionsWorkflow,
} from "@camped-ai/medusa/core-flows";
import { z } from "zod";

/**
 * POST /store/carts/{id}/complete-with-payment
 * 
 * Complete cart with partial payment support for your use case:
 * 1. Customer pays deposit/partial amount
 * 2. Cart completes → Order created
 * 3. Concierge team collects remaining payment later
 */

const CompleteCartWithPaymentSchema = z.object({
  payment_type: z.enum(["deposit", "full", "manual"]).default("deposit"),
  partial_amount: z.number().positive().optional(), // Deposit amount in cents
  payment_provider_id: z.string().default("pp_stripe_stripe"), // Use Stripe for all payments
  metadata: z.record(z.any()).optional()
});

export const POST = async (req: MedusaRequest, res: MedusaResponse) => {
  try {
    const cartId = req.params.id;
    const validatedBody = CompleteCartWithPaymentSchema.parse(req.body);
    
    const {
      payment_type,
      partial_amount,
      payment_provider_id,
      metadata = {}
    } = validatedBody;

    console.log(`🛒 Completing cart with partial payment: ${cartId}`);
    console.log(`💰 Payment type: ${payment_type}`);

    // Get cart details
    const query = req.scope.resolve(ContainerRegistrationKeys.QUERY);
    const [cart] = await query(
      remoteQueryObjectFromString({
        entryPoint: "cart",
        variables: { id: cartId },
        fields: [
          "id",
          "total",
          "currency_code",
          "customer_id",
          "email",
          "metadata",
          "items.*"
        ]
      })
    );

    if (!cart) {
      return res.status(404).json({
        message: "Cart not found"
      });
    }

    console.log(`📋 Cart total: ${cart.total} ${cart.currency_code}`);

    // Calculate payment amounts
    let paymentAmount: number;

    switch (payment_type) {
      case "deposit":
        // Default: 20% deposit or custom amount
        paymentAmount = partial_amount || Math.round(cart.total * 0.2);
        break;
      case "full":
        paymentAmount = cart.total;
        break;
      case "manual":
        // For manual payments, use minimal amount (sales team will mark as paid later)
        paymentAmount = partial_amount || 100; // £1 minimal to satisfy Medusa
        break;
      default:
        paymentAmount = partial_amount || Math.round(cart.total * 0.2);
    }

    const remainingAmount = cart.total - paymentAmount;

    console.log(`💳 Payment amount: ${paymentAmount}`);
    console.log(`💰 Remaining amount: ${remainingAmount}`);

    // Step 1: Check if payment collection exists
    const [existingCollection] = await query(
      remoteQueryObjectFromString({
        entryPoint: "cart_payment_collection",
        variables: { filters: { cart_id: cartId } },
        fields: [
          "payment_collection.id",
          "payment_collection.status",
          "payment_collection.amount"
        ]
      })
    );

    let paymentCollectionId: string;

    if (existingCollection?.payment_collection) {
      paymentCollectionId = existingCollection.payment_collection.id;
      console.log(`♻️ Using existing payment collection: ${paymentCollectionId}`);

      // Check if there are existing payment sessions that need to be handled
      const query = req.scope.resolve(ContainerRegistrationKeys.QUERY);
      const [existingSessions] = await query(
        remoteQueryObjectFromString({
          entryPoint: "payment_session",
          variables: { filters: { payment_collection_id: paymentCollectionId } },
          fields: ["id", "status", "provider_id"]
        })
      );

      if (existingSessions && existingSessions.length > 0) {
        console.log(`⚠️ Found ${existingSessions.length} existing payment sessions`);
        console.log("Existing sessions:", JSON.stringify(existingSessions, null, 2));

        // For manual payments, we'll skip creating new sessions and use existing ones
        if (payment_type === "manual") {
          console.log("🔄 Manual payment with existing sessions - proceeding to cart completion");

          // Skip payment session creation and go directly to cart completion
          const { result: order } = await completeCartWorkflow(req.scope).run({
            input: { id: cartId }
          });

          console.log(`🎉 Order created: ${order.id}`);

          // Update order metadata
          const orderModuleService = req.scope.resolve(Modules.ORDER);
          await orderModuleService.updateOrders(order.id, {
            metadata: {
              ...order.metadata,
              payment_tracking: {
                payment_type: payment_type,
                paid_amount: paymentAmount,
                remaining_amount: remainingAmount,
                total_amount: cart.total,
                currency_code: cart.currency_code,
                requires_additional_payment: remainingAmount > 0,
                payment_collection_id: paymentCollectionId,
                payment_session_id: existingSessions[0]?.id || "manual",
                completed_at: new Date().toISOString(),
                manual_completion: true
              },
              concierge_notes: remainingAmount > 0
                ? `Remaining payment of ${remainingAmount} ${cart.currency_code} to be collected by sales team`
                : "Full payment completed"
            }
          });

          return res.json({
            success: true,
            order: {
              id: order.id,
              total: order.total,
              currency_code: order.currency_code,
              status: order.status,
              customer_id: order.customer_id,
              email: order.email
            },
            payment_info: {
              payment_type: payment_type,
              paid_amount: paymentAmount,
              remaining_amount: remainingAmount,
              total_amount: cart.total,
              currency_code: cart.currency_code,
              requires_additional_payment: remainingAmount > 0,
              payment_collection_id: paymentCollectionId,
              payment_session_id: existingSessions[0]?.id || "manual"
            },
            cart_id: cartId,
            message: "Order created successfully. Full payment to be collected by sales team.",
            next_steps: [
              "Order is confirmed and created",
              "Sales team should collect full payment",
              `Remaining amount: ${remainingAmount} ${cart.currency_code}`,
              "Use admin API to mark payment as collected"
            ]
          });
        }
      }
    } else {
      // Step 2: Create payment collection
      console.log("🏦 Creating payment collection...");
      
      await createPaymentCollectionForCartWorkflow(req.scope).run({
        input: {
          cart_id: cartId,
          metadata: {
            cart_id: cartId,
            cart_total: cart.total,
            payment_amount: paymentAmount,
            payment_type: payment_type,
            is_partial_payment: payment_type !== "full",
            remaining_amount: remainingAmount,
            created_via: "complete_with_payment_api",
            ...metadata
          }
        }
      });

      // Get the created payment collection
      const [newCollection] = await query(
        remoteQueryObjectFromString({
          entryPoint: "cart_payment_collection",
          variables: { filters: { cart_id: cartId } },
          fields: ["payment_collection.id"]
        })
      );

      paymentCollectionId = newCollection?.payment_collection?.id;
      
      if (!paymentCollectionId) {
        throw new MedusaError(
          MedusaError.Types.INVALID_DATA,
          "Failed to create payment collection"
        );
      }

      console.log(`✅ Payment collection created: ${paymentCollectionId}`);
    }

    // Step 3: Handle payment session creation
    let paymentSession;

    if (payment_type === "manual") {
      // For manual payments, skip payment session creation and go directly to cart completion
      console.log("⚠️ Manual payment type - skipping payment session creation");
      console.log("🎯 Proceeding directly to cart completion...");

      // Create a mock payment session for tracking
      paymentSession = {
        id: `manual_${cartId}_${Date.now()}`,
        status: "authorized",
        amount: paymentAmount,
        currency: cart.currency_code,
        metadata: {
          manual_payment: true,
          payment_type: payment_type,
          cart_id: cartId
        }
      };

    } else {
      // For real payments (deposit/full), create actual payment session
      console.log("💳 Creating payment session...");

      try {
        const { result: paymentSessions } = await createPaymentSessionsWorkflow(req.scope).run({
          input: {
            payment_collection_id: paymentCollectionId,
            provider_id: payment_provider_id,
            context: {
              extra: {
                email: cart.email,
                customer_id: cart.customer_id,
                payment_type: payment_type,
                payment_amount: paymentAmount,
                remaining_amount: remainingAmount,
                authorized_by: "system",
                authorized_at: new Date().toISOString(),
                notes: `${payment_type} payment for cart ${cartId}`
              }
            }
          }
        });

        console.log("Payment sessions result:", JSON.stringify(paymentSessions, null, 2));

        if (!paymentSessions || paymentSessions.length === 0) {
          throw new MedusaError(
            MedusaError.Types.INVALID_DATA,
            "Failed to create payment session - no sessions returned"
          );
        }

        paymentSession = paymentSessions[0];

        if (!paymentSession || !paymentSession.id) {
          throw new MedusaError(
            MedusaError.Types.INVALID_DATA,
            "Payment session created but missing ID"
          );
        }

        console.log(`💳 Payment session created: ${paymentSession.id}`);

        // Step 4: Authorize payment session
        console.log("🔐 Authorizing payment session...");

        const paymentModuleService = req.scope.resolve(Modules.PAYMENT);
        await paymentModuleService.authorizePaymentSession(paymentSession.id, {
          context: {
            ...paymentSession.context,
            authorized_amount: paymentAmount,
            authorization_type: payment_type,
            notes: `${payment_type} payment for cart ${cartId} - Remaining: ${remainingAmount}`,
            authorized_at: new Date().toISOString()
          }
        });

      } catch (sessionError) {
        console.error("Error creating payment session:", sessionError);
        throw new MedusaError(
          MedusaError.Types.INVALID_DATA,
          `Failed to create payment session: ${sessionError.message}`
        );
      }
    }

    console.log("✅ Payment session authorized");

    // Step 5: Complete the cart
    console.log("🎯 Completing cart...");
    
    const { result: order } = await completeCartWorkflow(req.scope).run({
      input: { id: cartId }
    });

    console.log(`🎉 Order created: ${order.id}`);

    // Step 6: Update order metadata with payment tracking
    const orderModuleService = req.scope.resolve(Modules.ORDER);
    await orderModuleService.updateOrders(order.id, {
      metadata: {
        ...order.metadata,
        payment_tracking: {
          payment_type: payment_type,
          paid_amount: paymentAmount,
          remaining_amount: remainingAmount,
          total_amount: cart.total,
          currency_code: cart.currency_code,
          requires_additional_payment: remainingAmount > 0,
          payment_collection_id: paymentCollectionId,
          payment_session_id: paymentSession.id,
          completed_at: new Date().toISOString()
        },
        concierge_notes: remainingAmount > 0 
          ? `Remaining payment of ${remainingAmount} ${cart.currency_code} to be collected by concierge team`
          : "Full payment completed"
      }
    });

    res.json({
      success: true,
      order: {
        id: order.id,
        total: order.total,
        currency_code: order.currency_code,
        status: order.status,
        customer_id: order.customer_id,
        email: order.email
      },
      payment_info: {
        payment_type: payment_type,
        paid_amount: paymentAmount,
        remaining_amount: remainingAmount,
        total_amount: cart.total,
        currency_code: cart.currency_code,
        requires_additional_payment: remainingAmount > 0,
        payment_collection_id: paymentCollectionId,
        payment_session_id: paymentSession.id
      },
      cart_id: cartId,
      message: payment_type === "manual" 
        ? "Order created successfully. Full payment to be collected by concierge team."
        : remainingAmount > 0
          ? `Order created with ${payment_type} payment. Remaining ${remainingAmount} ${cart.currency_code} to be collected by concierge team.`
          : "Order created with full payment completed.",
      next_steps: remainingAmount > 0 
        ? [
            "Order is confirmed and created",
            "Customer has paid deposit/partial amount",
            "Concierge team should collect remaining payment",
            `Remaining amount: ${remainingAmount} ${cart.currency_code}`
          ]
        : [
            "Order is confirmed and created", 
            "Full payment completed",
            "No additional payment required"
          ]
    });

  } catch (error) {
    console.error("Error completing cart with payment:", error);
    
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        message: "Validation error",
        errors: error.errors
      });
    }

    if (error instanceof MedusaError) {
      return res.status(400).json({
        type: error.type,
        message: error.message
      });
    }

    res.status(500).json({
      message: "Failed to complete cart with payment",
      error: error instanceof Error ? error.message : "Unknown error"
    });
  }
};
