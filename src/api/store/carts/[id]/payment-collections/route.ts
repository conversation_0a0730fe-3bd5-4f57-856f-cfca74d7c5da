import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";
import {
  ContainerRegistrationKeys,
  remoteQueryObjectFromString,
} from "@camped-ai/framework/utils";

/**
 * GET /store/carts/{id}/payment-collections
 * 
 * Debug endpoint to list all payment collections for a cart.
 * Helps identify which payment collections exist and their metadata.
 */

export const GET = async (req: MedusaRequest, res: MedusaResponse) => {
  try {
    const cartId = req.params.id;

    console.log(`🔍 Listing payment collections for cart: ${cartId}`);

    // Get cart details
    const queryService = req.scope.resolve(ContainerRegistrationKeys.REMOTE_QUERY);
    const [cart] = await queryService(
      remoteQueryObjectFromString({
        entryPoint: "cart",
        variables: { filters: { id: cartId } },
        fields: [
          "id",
          "total",
          "currency_code",
          "customer_id",
          "email"
        ]
      })
    );

    if (!cart) {
      return res.status(404).json({
        message: "Cart not found",
        cart_id: cartId
      });
    }

    // Get all payment collections for this cart
    const cartCollectionRelations = await queryService(
      remoteQueryObjectFromString({
        entryPoint: "cart_payment_collection",
        variables: { filters: { cart_id: cartId } },
        fields: [
          "payment_collection.id",
          "payment_collection.status",
          "payment_collection.amount",
          "payment_collection.currency_code",
          "payment_collection.metadata",
          "payment_collection.created_at",
          "payment_collection.updated_at"
        ]
      })
    );

    const paymentCollections = cartCollectionRelations.map(relation => {
      const collection = relation.payment_collection;
      const metadata = collection.metadata || {};
      
      return {
        id: collection.id,
        amount: collection.amount,
        currency_code: collection.currency_code,
        status: collection.status,
        created_at: collection.created_at,
        updated_at: collection.updated_at,
        metadata: metadata,
        // Analysis flags
        is_advance_payment: !!metadata.is_advance_payment,
        created_via: metadata.created_via || 'unknown',
        advance_payment_status: metadata.advance_payment_status || 'none',
        payment_mode: metadata.payment_mode || 'unknown',
        advance_amount: metadata.advance_amount || null,
        remaining_amount: metadata.remaining_amount || null,
        cart_total: metadata.cart_total || null
      };
    });

    console.log(`📋 Found ${paymentCollections.length} payment collection(s) for cart ${cartId}`);

    // Calculate payment summary
    const totalPaid = paymentCollections.reduce((sum, pc) => sum + (pc.amount || 0), 0);
    const remainingBalance = cart.total - totalPaid;
    const advancePaymentCollections = paymentCollections.filter(pc => pc.is_advance_payment);
    const totalAdvancePaid = advancePaymentCollections.reduce((sum, pc) => sum + (pc.advance_amount || pc.amount || 0), 0);

    res.json({
      success: true,
      cart: {
        id: cart.id,
        total: cart.total,
        currency_code: cart.currency_code,
        customer_id: cart.customer_id,
        email: cart.email
      },
      // ✅ ENHANCED: Payment status overview
      payment_status: {
        cart_total: cart.total,
        total_paid: totalPaid,
        remaining_balance: remainingBalance,
        currency_code: cart.currency_code,
        payment_status: remainingBalance <= 0 ? "fully_paid" : "partial",
        advance_payments: {
          total_advance_paid: totalAdvancePaid,
          advance_collections_count: advancePaymentCollections.length
        }
      },
      payment_collections: paymentCollections,
      summary: {
        total_collections: paymentCollections.length,
        advance_payment_collections: advancePaymentCollections.length,
        collection_sources: [...new Set(paymentCollections.map(pc => pc.created_via))],
        total_amounts: paymentCollections.map(pc => ({
          id: pc.id,
          amount: pc.amount,
          currency: pc.currency_code,
          type: pc.is_advance_payment ? 'advance' : 'regular'
        }))
      },
      recommendations: paymentCollections.length === 0 
        ? [
            "No payment collections found for this cart",
            "Use POST /store/carts/{id}/advance-payment to create an advance payment collection"
          ]
        : paymentCollections.filter(pc => pc.is_advance_payment).length === 0
        ? [
            "Payment collections exist but none are for advance payments",
            "Use POST /store/carts/{id}/advance-payment to create a proper advance payment collection",
            "Or delete existing collections and recreate with advance payment API"
          ]
        : [
            "Advance payment collections found",
            "Use the payment_collection_id in record-payment API calls"
          ]
    });

  } catch (error) {
    console.error("Error listing payment collections:", error);
    
    res.status(500).json({
      message: "Failed to list payment collections",
      error: error instanceof Error ? error.message : "Unknown error",
      cart_id: req.params.id
    });
  }
};
