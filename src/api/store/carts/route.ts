import {
  StorePostCartsReqSchema,
  StorePostCartsReq
} from "./schemas";
import {
  MedusaRequest,
  MedusaResponse
} from "@camped-ai/framework"
import {
  Mo<PERSON>les,
  MedusaError
} from "@camped-ai/framework/utils"
import { createCartWorkflow } from "@camped-ai/medusa/core-flows"

export const POST = async (
  req: MedusaRequest,
  res: MedusaResponse
) => {
  console.log("📋 Cart creation request received");
  console.log("Request body:", JSON.stringify(req.body, null, 2));

  // Parse and validate using our corrected schema
  const validatedBody: StorePostCartsReq = StorePostCartsReqSchema.parse(req.body);

  const {
    region_id,
    currency_code,
    items,
    email,
    sales_channel_id,
    promo_codes,
    metadata,
    shipping_address,
    billing_address
  } = validatedBody;

  try {
    console.log("Creating cart with Medusa-compliant payload");
    console.log("Validated body:", JSON.stringify(validatedBody, null, 2));

    // Check if any items have custom unit_price
    const hasCustomPricing = items && Array.isArray(items) &&
      items.some(item => item.unit_price !== undefined);

    console.log("Has custom pricing:", hasCustomPricing);

    // Create cart input following Medusa standards (no customer_id!)
    const cartInput = {
      region_id,
      currency_code,
      email, // Medusa uses email to associate with customer, not customer_id
      sales_channel_id,
      promo_codes,
      shipping_address,
      billing_address,
      metadata: {
        ...metadata,
        ...(hasCustomPricing && { custom_pricing: true, cart_type: "quote" }),
        created_via: "store_api",
        medusa_standards_compliant: true
      }
    };

    console.log("Creating cart with input:", JSON.stringify(cartInput, null, 2));

    // Create the cart
    const { result: cartResult } = await createCartWorkflow(req.scope).run({
      input: cartInput,
    });

    if (!cartResult || !cartResult.id) {
      throw new Error("Failed to create cart");
    }

    console.log("Cart created:", cartResult.id);

    // Add line items if provided
    if (items && Array.isArray(items) && items.length > 0) {
      const cartModuleService = req.scope.resolve(Modules.CART);
      
      if (hasCustomPricing) {
        // Add items with custom pricing
        console.log("Adding items with custom pricing");
        
        const lineItems = items.map(item => {
          const lineItem: any = {
            title: item.title || `Product Item`, // Required field
            variant_id: item.variant_id,
            quantity: item.quantity,
            metadata: {
              ...item.metadata
            },
            requires_shipping: item.requires_shipping ?? false // Default to false for hotel services
          };

          // Only add unit_price if it's provided (custom pricing)
          if (item.unit_price !== undefined) {
            lineItem.unit_price = item.unit_price;
            lineItem.metadata.custom_price = true;
            lineItem.metadata.original_unit_price = item.unit_price;
          }

          return lineItem;
        });

        console.log("Adding line items with custom pricing:", JSON.stringify(lineItems, null, 2));
        await cartModuleService.addLineItems(cartResult.id, lineItems);
      } else {
        // Add items with default Medusa pricing
        console.log("Adding items with default Medusa pricing");

        // For default pricing, we need to fetch variant prices
        const productModuleService = req.scope.resolve(Modules.PRODUCT);
        const pricingModuleService = req.scope.resolve(Modules.PRICING);

        const lineItems = [];

        for (const item of items) {
          try {
            // Get variant details
            const variant = await productModuleService.retrieveProductVariant(item.variant_id, {
              relations: ["product"]
            });

            // Get pricing for the variant
            const priceSet = await pricingModuleService.calculatePrices(
              { id: [item.variant_id] },
              {
                context: {
                  currency_code: currency_code,
                  region_id: region_id
                }
              }
            );

            const calculatedPrice = priceSet?.[item.variant_id]?.calculated_amount || 0;

            lineItems.push({
              title: item.title || variant.title || variant.product?.title || `Product Item`,
              variant_id: item.variant_id,
              quantity: item.quantity,
              unit_price: calculatedPrice,
              metadata: item.metadata || {},
              requires_shipping: item.requires_shipping ?? false // Default to false for hotel services
            });
          } catch (variantError) {
            console.error(`Error fetching variant ${item.variant_id}:`, variantError);
            // Fallback with minimal data
            lineItems.push({
              title: item.title || `Product Item`,
              variant_id: item.variant_id,
              quantity: item.quantity,
              unit_price: 0, // Fallback price
              metadata: item.metadata || {},
              requires_shipping: item.requires_shipping ?? false // Default to false for hotel services
            });
          }
        }

        console.log("Adding line items with default pricing:", JSON.stringify(lineItems, null, 2));
        await cartModuleService.addLineItems(cartResult.id, lineItems);
      }
    }

    // Retrieve the complete cart with items
    const cartModuleService = req.scope.resolve(Modules.CART);

    try {
      const finalCart = await cartModuleService.retrieveCart(cartResult.id, {
        relations: ["items", "shipping_address", "billing_address"]
      });

      console.log("Final cart created:", finalCart.id);

      res.json({
        cart: finalCart
      });
    } catch (retrieveError: any) {
      console.error("Error retrieving final cart:", retrieveError);

      // Fallback: try to retrieve without relations
      try {
        const basicCart = await cartModuleService.retrieveCart(cartResult.id);
        console.log("Retrieved basic cart:", basicCart.id);

        res.json({
          cart: basicCart
        });
      } catch (basicRetrieveError: any) {
        console.error("Error retrieving basic cart:", basicRetrieveError);
        throw new MedusaError(
          MedusaError.Types.INVALID_DATA,
          `Cart created but could not retrieve: ${basicRetrieveError.message}`
        )
      }
    }

  } catch (error: any) {
    console.error("Error creating cart:", error);
    
    if (error instanceof MedusaError) {
      throw error;
    }
    
    throw new MedusaError(
      MedusaError.Types.INVALID_DATA,
      `Failed to create cart: ${error.message}`
    )
  }
}
