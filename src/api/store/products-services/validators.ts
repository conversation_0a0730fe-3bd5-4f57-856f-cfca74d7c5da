import { z } from "zod";

// Store Products/Services Query Validation
// Based on admin API but filtered for customer/store access
export const GetStoreProductServicesQuery = z.object({
  limit: z.string().optional(),
  offset: z.string().optional(),
  
  // Filter parameters
  status: z.enum(["active"]).optional().default("active"), // Only allow active for store
  category_id: z.string().optional(),
  unit_type_id: z.string().optional(),
  tag_ids: z.union([z.string(), z.array(z.string())]).optional(),
  service_level: z.enum(["hotel", "destination"]).optional(),
  hotel_id: z.string().optional(),
  destination_id: z.string().optional(),
  
  // Search parameters
  name: z.string().optional(),
  search: z.string().optional(),
  
  // Sorting parameters
  sort_by: z
    .enum([
      "name",
      "category",
      "updated_at",
      "created_at",
    ])
    .optional()
    .default("name"), // Default to name for better customer experience
  sort_order: z.enum(["asc", "desc"]).optional().default("asc"),
});

export type GetStoreProductServicesQueryType = z.infer<typeof GetStoreProductServicesQuery>;
