import { z } from "zod";
import { SUPPLIER_PRODUCTS_SERVICES_MODULE } from "src/modules/supplier-products-services";
import SupplierProductsServicesModuleService from "src/modules/supplier-products-services/service";
import { GetAdminCategoriesQuery } from "../../../admin/supplier-management/products-services/validators";
import { MedusaRequest, MedusaResponse } from "@camped-ai/framework";

type GetStoreCategoriesQueryType = z.infer<typeof GetAdminCategoriesQuery>;

export const GET = async (
  req: MedusaRequest<{}, GetStoreCategoriesQueryType>,
  res: MedusaResponse
) => {
  try {
    console.log("GET /store/supplier-management/products-services/categories - Query:", req.query);

    const supplierProductsServicesService: SupplierProductsServicesModuleService =
      req.scope.resolve(SUPPLIER_PRODUCTS_SERVICES_MODULE);

    const limit = 100
    const offset = 0

    // Build filters
    const filters: any = {
      is_active: true,
    };

    console.log("Listing categories with filters:", filters, "options:", { skip: offset, take: limit });

    const result = await supplierProductsServicesService.listCategories(
      filters,
      { skip: offset, take: limit }
    );

    console.log({ result })

    res.json({
      result
    });
  } catch (error) {
    console.error("❌ Error listing categories:", error);
    console.error("Error details:", {
      message: error.message,
      stack: error.stack,
      type: error.constructor.name
    });

    res.status(500).json({
      message: error instanceof Error ? error.message : "Failed to list categories",
      error: error instanceof Error ? error.message : "Unknown error",
      errorType: error.constructor.name,
      timestamp: new Date().toISOString()
    });
  }
};