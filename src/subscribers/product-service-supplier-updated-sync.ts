import type { SubscriberArgs, SubscriberConfig } from "@camped-ai/framework";
import { ProductServiceSyncService } from "../services/product-service-sync-service";

/**
 * Subscriber for product service supplier updated events
 * Triggers sync when supplier information is updated
 */
export default async function productServiceSupplierUpdatedHandler({
  event: { data },
  container,
}: SubscriberArgs<{ id: string; product_service_id: string; supplier_id: string }>) {
  try {
    console.log(`🔄 Product service supplier link updated for product service: ${data.product_service_id}, supplier: ${data.supplier_id}`);

    // Get the sync service
    const syncService = new ProductServiceSyncService(container);

    // Check if auto-sync is enabled for this supplier
    const config = await syncService.getSupplierConfig(data.supplier_id);
    
    if (config.auto_sync_enabled) {
      console.log(`🔄 Auto-sync enabled for supplier ${data.supplier_id}, updating add-on for product service ${data.product_service_id}`);
      await syncService.syncProductServiceToAddOn(data.product_service_id);
    } else {
      console.log(`⏸️ Auto-sync disabled for supplier ${data.supplier_id}, skipping sync`);
    }

    console.log(`✅ Product service supplier link update sync completed successfully`);

  } catch (error) {
    console.error(`❌ Error syncing updated product service supplier link:`, error);
    // Don't throw error to prevent breaking the event flow
  }
}

export const config: SubscriberConfig = {
  event: "product-service-supplier.updated",
};
