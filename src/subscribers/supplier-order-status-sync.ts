import { SubscriberArgs, SubscriberConfig } from "@camped-ai/framework/types";
import { BOOKING_ADD_ONS_MODULE } from "../modules/booking-add-ons";
import BookingAddOnService from "../modules/booking-add-ons/service";

/**
 * Subscriber to sync booking add-on status when supplier order status changes
 *
 * This subscriber listens for supplier order status updates and automatically
 * updates the corresponding booking add-ons to keep them in sync.
 */
export default async function supplierOrderStatusSyncHandler({
  data,
  container,
}: SubscriberArgs<{ order: any; previous_status?: string }>) {
  const startTime = Date.now();
  const bookingAddOnService: BookingAddOnService = container.resolve(
    BOOKING_ADD_ONS_MODULE
  );

  try {
    const { order, previous_status } = data;

    console.log("\n" + "=".repeat(60));
    console.log("🔄 SUPPLIER ORDER STATUS SYNC EVENT TRIGGERED");
    console.log("=".repeat(60));
    console.log(`📦 Order ID: ${order.id}`);
    console.log(`📊 Order Number: ${order.order_number || "N/A"}`);
    console.log(`🔄 Status Change: ${previous_status} → ${order.status}`);
    console.log(`⏰ Event Time: ${new Date().toISOString()}`);
    console.log(
      `📊 Full Order Metadata:`,
      JSON.stringify(order.metadata, null, 2)
    );

    // Check if the order has booking add-on metadata
    const bookingAddonIds = order.metadata?.booking_addon_ids;

    if (!bookingAddonIds || !Array.isArray(bookingAddonIds)) {
      console.log(`⚠️ NO BOOKING ADD-ONS LINKED TO ORDER`);
      console.log(
        `📊 Available metadata keys: [${Object.keys(order.metadata || {}).join(
          ", "
        )}]`
      );
      console.log(`📊 booking_addon_ids value:`, bookingAddonIds);
      console.log(`📊 booking_addon_ids type:`, typeof bookingAddonIds);
      console.log("=".repeat(60));
      return;
    }

    console.log(`🎯 FOUND ${bookingAddonIds.length} BOOKING ADD-ONS TO UPDATE`);
    console.log(`📋 Add-on IDs: [${bookingAddonIds.join(", ")}]`);
    console.log(`🔄 Target Status: ${order.status}`);

    // Update all linked booking add-ons with the new status
    console.log(`\n🔄 STARTING BOOKING ADD-ON UPDATES...`);
    const updateResults = [];

    for (let i = 0; i < bookingAddonIds.length; i++) {
      const addonId = bookingAddonIds[i];
      try {
        console.log(
          `\n📝 Updating add-on ${i + 1}/${bookingAddonIds.length}: ${addonId}`
        );

        // First, check if the add-on exists
        const existingAddOn = await bookingAddOnService.retrieveBookingAddOn(
          addonId
        );
        console.log(
          `   📋 Current status: ${existingAddOn?.order_status || "null"}`
        );
        console.log(`   🎯 Target status: ${order.status}`);

        if (existingAddOn?.order_status === order.status) {
          console.log(`   ⏭️ Status already correct, skipping update`);
          updateResults.push({
            addonId,
            status: "skipped",
            reason: "already_correct",
          });
          continue;
        }

        const updatedAddOn = await bookingAddOnService.updateBookingAddOn(
          addonId,
          {
            order_status: order.status,
          }
        );

        console.log(
          `   ✅ Successfully updated to: ${updatedAddOn.order_status}`
        );
        updateResults.push({
          addonId,
          status: "success",
          newStatus: updatedAddOn.order_status,
        });
      } catch (error) {
        console.error(
          `   ❌ Failed to update booking add-on ${addonId}:`,
          error
        );
        updateResults.push({ addonId, status: "error", error: error.message });
      }
    }

    const successCount = updateResults.filter(
      (r) => r.status === "success"
    ).length;
    const skipCount = updateResults.filter(
      (r) => r.status === "skipped"
    ).length;
    const errorCount = updateResults.filter((r) => r.status === "error").length;

    console.log(`\n📊 UPDATE SUMMARY:`);
    console.log(`   ✅ Successful: ${successCount}`);
    console.log(`   ⏭️ Skipped: ${skipCount}`);
    console.log(`   ❌ Errors: ${errorCount}`);

    const duration = Date.now() - startTime;
    console.log(`⏱️ Total Duration: ${duration}ms`);
    console.log("=".repeat(60));
  } catch (error) {
    console.error("❌ Failed to sync booking add-on status:", error);
    // Don't throw here to prevent breaking the supplier order update
    // Just log the error for monitoring
  }
}

export const config: SubscriberConfig = {
  event: "supplier.order.updated",
  context: {
    subscriberId: "supplier-order-status-sync",
  },
};
