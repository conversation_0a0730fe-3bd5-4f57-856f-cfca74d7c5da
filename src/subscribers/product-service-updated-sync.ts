import type { SubscriberArgs, SubscriberConfig } from "@camped-ai/framework";
import { createProductServiceSyncService } from "../utils/service-registration";

/**
 * Subscriber for product service updated events
 * Automatically updates corresponding add-ons
 */
export default async function productServiceUpdatedHandler({
  event: { data },
  container,
}: SubscriberArgs<{ id: string; product_service: any }>) {
  try {
    console.log(`🔄 Product service updated event received for ID: ${data.id}`);

    // Get the sync service with proper service registration
    const syncService = createProductServiceSyncService(container);

    // Get price-related services
    const priceComparisonService = container.priceComparisonService;
    const priceFlagService = container.priceFlagService;
    const addOnSyncService = container.addOnSyncService;

    // Check if auto-sync is enabled for this product service's suppliers
    const productService = data.product_service || { id: data.id };
    
    // If the product service has suppliers, check their auto-sync settings
    if (productService.suppliers && productService.suppliers.length > 0) {
      for (const supplier of productService.suppliers) {
        const config = await syncService.getSupplierConfig(supplier.supplier_id);
        
        if (config.auto_sync_enabled) {
          console.log(`🔄 Auto-sync enabled for supplier ${supplier.supplier_id}, updating add-on for product service ${data.id}`);
          await syncService.syncProductServiceToAddOn(data.id);
          break; // Only sync once even if multiple suppliers have auto-sync enabled
        }
      }
    } else {
      // If no suppliers, use default auto-sync behavior
      console.log(`🔄 No suppliers found, using default sync for product service ${data.id}`);
      await syncService.syncProductServiceToAddOn(data.id);
    }

    // After syncing, check if this update affects any price flags
    if (priceComparisonService && priceFlagService && addOnSyncService) {
      try {
        // If the base price was updated, sync it to add-ons
        if (productService.base_cost) {
          console.log(`🔄 Syncing updated base price to add-ons for product service ${data.id}`);
          await addOnSyncService.syncAddOnFromProductServiceBasePrice(data.id);
        }

        // Check if any existing price flags need to be re-evaluated
        if (productService.price_flag_active) {
          console.log(`🔄 Re-evaluating price flag for updated product service ${data.id}`);

          // Get the supplier offering that triggered the flag
          if (productService.price_flag_supplier_offering_id) {
            const comparison = await priceComparisonService.comparePrice(
              data.id,
              productService.price_flag_supplier_offering_id
            );

            // If the flag is no longer needed, dismiss it
            if (!comparison.shouldFlag) {
              await priceFlagService.dismissPriceFlag(
                data.id,
                'Price flag no longer needed after product service update'
              );
            }
          }
        }
      } catch (priceError) {
        console.error(`❌ Error handling price sync for updated product service ${data.id}:`, priceError);
        // Don't throw - this is secondary to the main sync
      }
    }

    console.log(`✅ Product service ${data.id} update sync completed successfully`);

  } catch (error) {
    console.error(`❌ Error syncing updated product service ${data.id}:`, error);
    // Don't throw error to prevent breaking the event flow
    // The error is already logged in the sync service
  }
}

export const config: SubscriberConfig = {
  event: "product-service.updated",
};
