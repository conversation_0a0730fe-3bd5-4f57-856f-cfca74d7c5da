import { 
  type SubscriberConfig, 
  type SubscriberArgs,
} from "@camped-ai/framework/types";

export default async function supplierOfferingCleanupHandler({
  data,
  eventName,
  container,
}: SubscriberArgs<Record<string, any>>) {
  const priceFlagService = container.priceFlagService;
  const priceComparisonService = container.priceComparisonService;
  const supplierProductsServicesModuleService = container.supplierProductsServicesModuleService;

  try {
    console.log(`Processing ${eventName} event for supplier offering cleanup:`, data.id);

    const supplierOffering = data;
    const productServiceId = supplierOffering.product_service_id;

    if (!productServiceId) {
      console.warn(`No product service ID found for supplier offering ${supplierOffering.id}`);
      return;
    }

    // Check if this supplier offering was the one that triggered the current price flag
    const productService = await supplierProductsServicesModuleService.retrieveProductService(
      productServiceId
    );

    if (!productService.price_flag_active) {
      console.log(`No active price flag for product service ${productServiceId}, no cleanup needed`);
      return;
    }

    // Check if the deleted/deactivated offering was the one that triggered the flag
    if (productService.price_flag_supplier_offering_id === supplierOffering.id) {
      console.log(`Supplier offering ${supplierOffering.id} was the source of the price flag, checking for new highest price`);

      // Get all remaining active supplier offerings for this product service
      const remainingOfferings = await supplierProductsServicesModuleService.listSupplierOfferings({
        product_service_id: productServiceId,
        status: 'active',
      });

      // Filter out the deleted/deactivated offering
      const activeOfferings = remainingOfferings.filter((offering: any) => 
        offering.id !== supplierOffering.id && offering.cost && offering.currency
      );

      if (activeOfferings.length === 0) {
        // No more active offerings, dismiss the price flag
        console.log(`No more active offerings for product service ${productServiceId}, dismissing price flag`);
        
        await priceFlagService.dismissPriceFlag(
          productServiceId,
          `Source supplier offering ${supplierOffering.id} was deleted/deactivated`
        );
      } else {
        // Find the new highest price among remaining offerings
        console.log(`Finding new highest price among ${activeOfferings.length} remaining offerings`);
        
        let newHighestOffering = null;
        let newHighestNormalizedPrice = 0;

        for (const offering of activeOfferings) {
          try {
            const comparison = await priceComparisonService.comparePrice(
              productServiceId,
              offering.id
            );

            // Convert to normalized price for comparison
            const normalizedPrice = comparison.supplierPrice; // This should be normalized by the comparison service
            
            if (!newHighestOffering || normalizedPrice > newHighestNormalizedPrice) {
              newHighestOffering = offering;
              newHighestNormalizedPrice = normalizedPrice;
            }
          } catch (error) {
            console.error(`Error comparing price for offering ${offering.id}:`, error);
          }
        }

        if (newHighestOffering) {
          // Check if the new highest price still warrants a flag
          const newComparison = await priceComparisonService.comparePrice(
            productServiceId,
            newHighestOffering.id
          );

          if (newComparison.shouldFlag) {
            // Update the flag with the new highest offering
            console.log(`Updating price flag with new highest offering ${newHighestOffering.id}`);
            
            await priceFlagService.updatePriceFlag(productServiceId, {
              productServiceId,
              supplierOfferingId: newHighestOffering.id,
              currentBasePrice: newComparison.currentBasePrice,
              currentBaseCurrency: newComparison.currentBaseCurrency,
              newHighestPrice: newComparison.supplierPrice,
              newHighestCurrency: newComparison.supplierCurrency,
              priceIncrease: newComparison.priceIncrease || 0,
              priceIncreasePercent: newComparison.priceIncreasePercent || 0,
              reason: `Updated after supplier offering ${supplierOffering.id} was removed`,
            });
          } else {
            // New highest price doesn't warrant a flag, dismiss it
            console.log(`New highest price doesn't warrant a flag, dismissing price flag`);
            
            await priceFlagService.dismissPriceFlag(
              productServiceId,
              `New highest price after offering removal doesn't meet flag criteria`
            );
          }
        } else {
          // Couldn't find a valid new highest offering, dismiss the flag
          console.log(`Couldn't determine new highest offering, dismissing price flag`);
          
          await priceFlagService.dismissPriceFlag(
            productServiceId,
            `Unable to determine new highest price after offering removal`
          );
        }
      }
    } else {
      console.log(`Supplier offering ${supplierOffering.id} was not the source of the price flag, no cleanup needed`);
    }

    console.log(`Completed cleanup processing for supplier offering ${supplierOffering.id}`);

  } catch (error) {
    console.error(`Error in supplier offering cleanup handler:`, error);
    
    // Don't throw the error to prevent the event from failing
    if (container.logger) {
      container.logger.error('supplier-offering-cleanup-error', {
        error: error.message,
        supplierOfferingId: data.id,
        eventName,
        stack: error.stack,
      });
    }
  }
}

export const config: SubscriberConfig = {
  event: [
    "supplier-offering.deleted",
    "supplier-offering.deactivated"
  ],
  context: {
    subscriberId: "supplier-offering-cleanup",
  },
};
