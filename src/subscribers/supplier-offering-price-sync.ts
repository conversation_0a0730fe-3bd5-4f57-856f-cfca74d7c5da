import type { SubscriberArgs, SubscriberConfig } from "@camped-ai/framework";

export default async function supplierOfferingPriceSyncHandler({
  event: { data },
  container,
}: SubscriberArgs<Record<string, any>>) {
  console.log(`🔄 [PRICE-SYNC] Starting supplier-offering event processing for supplier offering:`, data.id);
  console.log(`🔄 [PRICE-SYNC] Event data:`, JSON.stringify(data, null, 2));

  // Import and instantiate services directly if not available in container
  let priceComparisonService: any, priceFlagService: any, basePriceAutoPopulationService: any, addOnSyncService: any;

  try {
    priceComparisonService = container.resolve("priceComparisonService");
  } catch (error) {
    console.log(`🔄 [PRICE-SYNC] Creating PriceComparisonService directly`);
    const PriceComparisonService = require("../services/price-comparison-service").default;
    priceComparisonService = new PriceComparisonService(container);
  }

  try {
    priceFlagService = container.resolve("priceFlagService");
  } catch (error) {
    console.log(`🔄 [PRICE-SYNC] Creating PriceFlagService directly`);

    // Check if the supplier products services module is available
    let supplierProductsServicesModule = null;
    try {
      supplierProductsServicesModule = container.resolve("supplierProductsServicesModule");
      console.log(`✅ [PRICE-SYNC] Found supplierProductsServicesModule for PriceFlagService`);
    } catch (moduleError) {
      console.warn(`⚠️ [PRICE-SYNC] supplierProductsServicesModule not available for PriceFlagService:`, moduleError.message);
    }

    // Create a simple price flag service that can work with the available module
    priceFlagService = {
      checkAndTriggerPriceFlag: async (productServiceId: string, supplierOfferingId: string, comparisonResult: any) => {
        if (!supplierProductsServicesModule) {
          console.warn(`⚠️ [PRICE-SYNC] Cannot create price flag - module not available`);
          return null;
        }

        try {
          // Create a price flag record and save it to the database
          const priceFlagData = {
            product_service_id: productServiceId,
            supplier_offering_id: supplierOfferingId,
            flag_type: 'price_increase',
            severity: comparisonResult.priceIncreasePercent > 100 ? 'high' :
                     comparisonResult.priceIncreasePercent > 50 ? 'medium' : 'low',
            current_price: parseFloat(comparisonResult.currentBasePrice),
            new_price: parseFloat(comparisonResult.supplierPrice),
            price_increase_percent: comparisonResult.priceIncreasePercent,
            currency: comparisonResult.supplierCurrency,
            status: 'active',
            metadata: {
              previous_highest_price: comparisonResult.previousHighestPrice,
              is_new_highest: comparisonResult.isNewHighestPrice
            }
          };

          // Save the price flag to the ProductService using existing price tracking fields
          console.log(`🔄 [PRICE-SYNC] Updating product service with price flag...`);
          console.log(`🔄 [PRICE-SYNC] Update data:`, {
            highest_price: priceFlagData.new_price,
            highest_price_currency: priceFlagData.currency,
            price_flag_active: true,
            price_flag_created_at: new Date(),
            price_flag_supplier_offering_id: supplierOfferingId
          });

          try {
            const updatedProductService = await supplierProductsServicesModule.updateProductService(productServiceId, {
              highest_price: priceFlagData.new_price,
              highest_price_currency: priceFlagData.currency,
              price_flag_active: true,
              price_flag_created_at: new Date(),
              price_flag_supplier_offering_id: supplierOfferingId
            });

            console.log(`✅ [PRICE-SYNC] Product service updated successfully:`, updatedProductService?.id);
          } catch (updateError) {
            console.error(`❌ [PRICE-SYNC] Failed to update product service:`, updateError);
            throw updateError;
          }

          const savedPriceFlag = {
            id: productServiceId, // Use product service ID since flag is stored on the product
            ...priceFlagData,
            created_at: new Date(),
            updated_at: new Date(),
            stored_in_product_service: true
          };

          console.log(`🚩 [PRICE-SYNC] Price flag saved to database:`, {
            id: savedPriceFlag.id,
            type: savedPriceFlag.flag_type,
            severity: savedPriceFlag.severity,
            increase: `${savedPriceFlag.price_increase_percent}%`,
            from: savedPriceFlag.current_price,
            to: savedPriceFlag.new_price,
            currency: savedPriceFlag.currency
          });

          return {
            success: true,
            message: `Price flag saved to database for ${savedPriceFlag.price_increase_percent}% increase`,
            flag: savedPriceFlag
          };
        } catch (error) {
          console.error(`Error saving price flag to database:`, error);

          // Fallback: at least log the flag details even if we can't save it
          console.log(`🚩 [PRICE-SYNC] Price flag (not saved):`, {
            type: 'price_increase',
            severity: comparisonResult.priceIncreasePercent > 100 ? 'high' : 'medium',
            increase: `${comparisonResult.priceIncreasePercent}%`,
            from: comparisonResult.currentBasePrice,
            to: comparisonResult.supplierPrice,
            currency: comparisonResult.supplierCurrency,
            error: error.message
          });

          return {
            success: false,
            message: `Price flag could not be saved: ${error.message}`,
            flag: null
          };
        }
      }
    };
  }

  try {
    basePriceAutoPopulationService = container.resolve("basePriceAutoPopulationService");
  } catch (error) {
    console.log(`🔄 [PRICE-SYNC] Creating BasePriceAutoPopulationService directly`);

    // Check if the supplier products services module is available
    let supplierProductsServicesModule = null;
    try {
      supplierProductsServicesModule = container.resolve("supplierProductsServicesModule");
      console.log(`✅ [PRICE-SYNC] Found supplierProductsServicesModule in container`);
    } catch (moduleError) {
      console.warn(`⚠️ [PRICE-SYNC] supplierProductsServicesModule not available:`, moduleError.message);
    }

    // Create a simple service that can work without the module for now
    basePriceAutoPopulationService = {
      shouldAutoPopulate: async (productServiceId: string) => {
        if (!supplierProductsServicesModule) {
          console.warn(`⚠️ [PRICE-SYNC] Cannot check auto-population - module not available`);
          return false;
        }

        try {
          const productService = await supplierProductsServicesModule.retrieveProductService(productServiceId);
          const baseCost = parseFloat(productService.base_cost) || 0;
          const shouldPopulate = !productService.base_cost || baseCost === 0;
          console.log(`🔍 [PRICE-SYNC] Auto-population check: base_cost="${productService.base_cost}", parsed=${baseCost}, shouldPopulate=${shouldPopulate}`);
          return shouldPopulate;
        } catch (error) {
          console.error(`Error checking auto-population for ${productServiceId}:`, error);
          return false;
        }
      },
      autoPopulateFromOffering: async (productServiceId: string, supplierOfferingId: string) => {
        if (!supplierProductsServicesModule) {
          console.warn(`⚠️ [PRICE-SYNC] Cannot auto-populate - module not available`);
          return null;
        }

        try {
          const supplierOffering = await supplierProductsServicesModule.retrieveSupplierOffering(supplierOfferingId);
          const updatedProductService = await supplierProductsServicesModule.updateProductService(productServiceId, {
            base_cost: supplierOffering.cost,
            base_currency: supplierOffering.currency,
          });

          console.log(`✅ [PRICE-SYNC] Auto-populated base price: ${supplierOffering.cost} ${supplierOffering.currency}`);
          return updatedProductService;
        } catch (error) {
          console.error(`Error auto-populating base price for ${productServiceId}:`, error);
          return null;
        }
      }
    };
  }

  // Skip AddOnSyncService for now due to import issues
  try {
    addOnSyncService = container.resolve("addOnSyncService");
  } catch (error) {
    console.log(`⚠️ [PRICE-SYNC] AddOnSyncService skipped due to import issues`);
    addOnSyncService = null;
  }

  // Check service availability
  console.log(`🔄 [PRICE-SYNC] Service availability check:`);
  console.log(`  - priceComparisonService: ${!!priceComparisonService}`);
  console.log(`  - priceFlagService: ${!!priceFlagService}`);
  console.log(`  - basePriceAutoPopulationService: ${!!basePriceAutoPopulationService}`);
  console.log(`  - addOnSyncService: ${!!addOnSyncService} (optional)`);

  try {
    console.log(`🔄 [PRICE-SYNC] Processing supplier-offering event for supplier offering:`, data.id);

    const supplierOffering = data;
    const productServiceId = supplierOffering.product_service_id;

    console.log(`🔄 [PRICE-SYNC] Supplier offering details:`, {
      id: supplierOffering.id,
      product_service_id: productServiceId,
      cost: supplierOffering.cost,
      currency: supplierOffering.currency,
      status: supplierOffering.status,
    });

    if (!productServiceId) {
      console.warn(`❌ [PRICE-SYNC] No product service ID found for supplier offering ${supplierOffering.id}`);
      return;
    }

    // Check if this is a price-related update
    if (!supplierOffering.cost || !supplierOffering.currency) {
      console.log(`⚠️ [PRICE-SYNC] Supplier offering ${supplierOffering.id} has no cost/currency, skipping price sync`);
      console.log(`   - Cost: ${supplierOffering.cost}`);
      console.log(`   - Currency: ${supplierOffering.currency}`);
      return;
    }

    // Step 1: Check if base price auto-population is needed
    console.log(`🔄 [PRICE-SYNC] Step 1: Checking if auto-population is needed for product service ${productServiceId}`);

    if (!basePriceAutoPopulationService) {
      console.error(`❌ [PRICE-SYNC] BasePriceAutoPopulationService not available`);
      return;
    }

    const shouldAutoPopulate = await basePriceAutoPopulationService.shouldAutoPopulate(productServiceId);
    console.log(`🔄 [PRICE-SYNC] Should auto-populate: ${shouldAutoPopulate}`);

    if (shouldAutoPopulate) {
      console.log(`✅ [PRICE-SYNC] Auto-populating base price for product service ${productServiceId}`);

      try {
        const autoPopulationResult = await basePriceAutoPopulationService.autoPopulateFromOffering(
          productServiceId,
          supplierOffering.id,
          {
            preferredCurrency: 'CHF',
            convertToStoreCurrency: true
          }
        );

        console.log(`✅ [PRICE-SYNC] Base price auto-populated:`, autoPopulationResult);

        // Sync to add-ons after auto-population
        if (addOnSyncService) {
          console.log(`🔄 [PRICE-SYNC] Syncing to add-ons...`);
          await addOnSyncService.syncAddOnFromProductServiceBasePrice(
            productServiceId,
            supplierOffering.id
          );
          console.log(`✅ [PRICE-SYNC] Add-ons synced successfully`);
        } else {
          console.warn(`⚠️ [PRICE-SYNC] AddOnSyncService not available, skipping add-on sync`);
        }

        console.log(`✅ [PRICE-SYNC] Auto-population completed successfully`);
        return; // Exit early since we auto-populated
      } catch (error) {
        console.error(`❌ [PRICE-SYNC] Failed to auto-populate base price for product service ${productServiceId}:`, error);
        console.error(`❌ [PRICE-SYNC] Error stack:`, error.stack);
        // Continue with price comparison even if auto-population fails
      }
    } else {
      console.log(`ℹ️ [PRICE-SYNC] Auto-population not needed (product service likely already has base price)`);
    }

    // Step 2: Perform price comparison
    const priceComparisonResult = await priceComparisonService.comparePrice(
      productServiceId,
      supplierOffering.id
    );

    console.log(`Price comparison result:`, priceComparisonResult);

    // Step 3: Check if price flag should be triggered
    if (priceComparisonResult.shouldFlag) {
      console.log(`🚩 [PRICE-SYNC] Price increase detected, triggering price flag`);
      try {
        const flagResult = await priceFlagService.checkAndTriggerPriceFlag(
          productServiceId,
          supplierOffering.id,
          priceComparisonResult
        );

        if (flagResult && flagResult.success) {
          console.log(`✅ [PRICE-SYNC] Price flag triggered successfully:`, flagResult.message);
        } else {
          console.log(`⚠️ [PRICE-SYNC] Price flag could not be created`);
        }
      } catch (flagError) {
        console.error(`❌ [PRICE-SYNC] Failed to trigger price flag:`, flagError);
      }
    } else {
      console.log(`ℹ️ [PRICE-SYNC] No price flag needed`);
    }

    // Step 4: Log the event for audit purposes
    console.log(`Completed price sync processing for supplier offering ${supplierOffering.id}`);

  } catch (error) {
    console.error(`Error in supplier offering price sync handler:`, error);
    
    // Don't throw the error to prevent the event from failing
    // Instead, log it for monitoring
    try {
      const logger = container.resolve("logger");
      if (logger) {
        logger.error(`supplier-offering-price-sync-error: ${error.message} for ${data.id}`);
      }
    } catch (loggerError) {
      // Fallback to console if logger not available
      console.error('supplier-offering-price-sync-error', {
        error: error.message,
        supplierOfferingId: data.id,
        stack: error.stack,
      });
    }
  }
}

export const config: SubscriberConfig = {
  event: [
    "supplier-offering.created",
    "supplier-offering.updated"
  ],
  context: {
    subscriberId: "supplier-offering-price-sync",
  },
};
