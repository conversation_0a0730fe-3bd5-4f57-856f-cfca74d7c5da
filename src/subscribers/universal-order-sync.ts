import type { SubscriberArgs, SubscriberConfig } from "@camped-ai/framework";
import { Modules } from "@camped-ai/framework/utils";
import { CreateConciergeOrderWorkflow } from "../workflows/concierge-management";
import { CONCIERGE_MANAGEMENT_MODULE } from "../modules/concierge-management";
import { ConciergeOrderItemStatus } from "../modules/concierge-management/types";

/**
 * Universal Order Synchronization Subscriber
 *
 * This is the SINGLE point of responsibility for concierge order creation.
 * It listens to order.created events and creates concierge orders for ALL orders
 * universally without any conditional filtering to ensure complete synchronization.
 *
 * Event: order.created (emitted immediately when any order is created)
 * Scope: ALL orders (API orders, cart-to-order conversions, etc.)
 * Creates: concierge_order + concierge_order_item records
 */
export default async function conciergeOrderCreationHandler({
  event: { data },
  container,
}: SubscriberArgs<any>) {
  // Handle both data.id and data.order_id for compatibility
  const orderId = data.id || data.order_id;
  console.log(
    "🎯 [CONCIERGE-SYNC] Universal order synchronization triggered for order:",
    orderId
  );

  try {
    if (!orderId) {
      console.log("⚠️ [CONCIERGE-SYNC] No order ID found in event data:", data);
      return;
    }

    // Resolve required services
    const orderModuleService = container.resolve(Modules.ORDER);
    const conciergeManagementService = container.resolve(
      CONCIERGE_MANAGEMENT_MODULE
    );

    // Retrieve the order with full details
    const order = await orderModuleService.retrieveOrder(orderId, {
      relations: ["items"],
    });

    if (!order) {
      console.log("❌ [CONCIERGE-SYNC] Order not found:", orderId);
      return;
    }

    // Check if concierge order already exists (duplicate prevention)
    const existingConciergeOrder =
      await conciergeManagementService.retrieveConciergeOrderByOrderId(
        order.id
      );
    if (existingConciergeOrder) {
      console.log(
        "ℹ️ [CONCIERGE-SYNC] Concierge order already exists for order:",
        order.id
      );
      return;
    }

    console.log(
      "✅ [CONCIERGE-SYNC] Creating concierge order (universal sync - ALL orders):",
      order.id
    );

    console.log({ order });

    // Create concierge order using workflow - NO CONDITIONAL LOGIC
    const { result } = await CreateConciergeOrderWorkflow(container).run({
      input: {
        order_id: order.id,
        check_in_date: new Date(order.metadata.check_in_date as string),
        check_out_date: new Date(order.metadata.check_out_date as string),
        hotel_id: order.metadata.hotel_id,
        metadata: {
          auto_created: true,
          universal_sync: true,
          order_value: order.total || 0,
          customer_email: order.email,
          created_at: new Date().toISOString(),
        },
      },
    });

    if (result.success) {
      console.log("✅ [CONCIERGE-SYNC] Concierge order created successfully:", {
        orderId: order.id,
        conciergeOrderId: result.concierge_order?.id,
      });

      // Create concierge order items for each line item
      if (
        order.items &&
        Array.isArray(order.items) &&
        result.concierge_order?.id
      ) {
        console.log(
          `📦 [CONCIERGE-SYNC] Creating ${order.items.length} concierge order items`
        );

        for (const lineItem of order.items) {
          try {
            console.log(
              `📦 Creating concierge order item for line item: ${lineItem.id}`
            );

            console.log({ lineItem });

            const itemTitle =
              lineItem.title ||
              lineItem.product_title ||
              lineItem.variant_title ||
              "Unknown Item";

            let conciergeOrderItemData = {
              concierge_order_id: result.concierge_order.id,
              line_item_id: lineItem.id,
              item_id: lineItem.id, // Set item_id to the same value as line_item_id for proper relationship tracking
              variant_id: lineItem.variant_id,
              quantity: lineItem.quantity,
              unit_price: lineItem.unit_price || 0,
              title: itemTitle,
              status: ConciergeOrderItemStatus.UNDER_REVIEW,
              added_by: null,
              category_id:
                typeof lineItem?.metadata?.category_id === "string"
                  ? lineItem.metadata.category_id
                  : null,
              metadata: {
                auto_created: true,
                universal_sync: true,
                original_line_item_metadata: lineItem.metadata || {},
              },
            };

            if (lineItem?.metadata?.start_date) {
              conciergeOrderItemData.start_date =
                typeof lineItem?.metadata?.start_date === "string" ||
                typeof lineItem?.metadata?.start_date === "number" ||
                lineItem?.metadata?.start_date instanceof Date
                  ? new Date(lineItem.metadata.start_date)
                  : new Date();
            }

            if (lineItem?.metadata?.end_date) {
              conciergeOrderItemData.end_date =
                typeof lineItem?.metadata?.end_date === "string" ||
                typeof lineItem?.metadata?.end_date === "number" ||
                lineItem?.metadata?.end_date instanceof Date
                  ? lineItem.metadata.end_date
                  : new Date();
            }

            const conciergeOrderItem =
              await conciergeManagementService.createConciergeOrderItem(
                conciergeOrderItemData
              );
            console.log(
              `✅ Created concierge order item: ${conciergeOrderItem.id} for line item: ${lineItem.id}`
            );
          } catch (itemError) {
            console.error(
              `❌ Failed to create concierge order item for line item: ${lineItem.id}`,
              itemError
            );
            // Continue with other items even if one fails
          }
        }
      } else {
        console.log(
          `⚠️ [CONCIERGE-SYNC] No line items found in order: ${order.id}`
        );
      }
    } else {
      console.error("❌ [CONCIERGE-SYNC] Failed to create concierge order:", {
        orderId: order.id,
        error: (result as any).error,
      });
    }
  } catch (error) {
    console.error("❌ [CONCIERGE-SYNC] Error processing order:", {
      orderId: orderId,
      error: error.message,
    });
  }
}

export const config: SubscriberConfig = {
  event: "order.created",
  context: {
    subscriberId: "universal-concierge-sync",
  },
};
