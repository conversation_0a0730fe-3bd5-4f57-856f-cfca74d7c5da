import type { SubscriberArgs, SubscriberConfig } from "@camped-ai/framework";
import { Modules } from "@camped-ai/framework/utils";
import { NOTIFICATION_TEMPLATE_SERVICE } from "../modules/notification-template/service";
import NotificationTemplateService from "../modules/notification-template/service";
import Handlebars from "handlebars";

export default async function userCreatedDirectHandler({
  event: { data },
  container,
}: SubscriberArgs<{
  user: any;
  email: string;
  first_name: string;
  last_name: string;
  temp_password: string;
}>) {
  // Resolve required services
  const notificationModuleService = container.resolve(Modules.NOTIFICATION);
  const notificationTemplateService: NotificationTemplateService =
    container.resolve(NOTIFICATION_TEMPLATE_SERVICE);

  // Check if notifications should be sent for this event and channel
  const shouldSendEmail =
    await notificationTemplateService.shouldSendNotification(
      "user.created_direct",
      "email"
    );

  if (!shouldSendEmail) {
    return;
  }

  // Get the template from the database
  const emailTemplate = await notificationTemplateService.getTemplate(
    "user.created_direct",
    "email"
  );

  if (!emailTemplate) {
    return;
  }

  // Prepare data for template rendering
  const baseUrl =
    process.env.MEDUSA_BACKEND_URL || process.env.MEDUSA_STOREFRONT_URL;
  const resetLink = `${baseUrl}/app/login`; // Direct to login page

  // Get the temporary password from event data (not stored in metadata for security)
  const tempPassword = data.temp_password || "Contact support for password";

  const templateData = {
    user: {
      email: data.email,
      first_name: data.first_name,
      last_name: data.last_name,
    },
    resetLink: resetLink,
    frontendURL: process.env.MEDUSA_STOREFRONT_URL || "",
    backendURL: process.env.MEDUSA_BACKEND_URL || "",
    tempPassword, // Add the temporary password to template data
  };

  // Compile and render the template with Handlebars
  const compiledTemplate = Handlebars.compile(emailTemplate.content);
  const emailBody = compiledTemplate(templateData);

  // Send email
  try {
    await notificationModuleService.createNotifications({
      to: data.email,
      channel: "email",
      template: "user.created_direct",
      data: {
        subject:
          emailTemplate.subject || "Welcome! Set up your account password",
        html: emailBody,
      },
    });
  } catch (error) {
    // Silent error handling
  }
}

export const config: SubscriberConfig = {
  event: "user.created_direct",
};
