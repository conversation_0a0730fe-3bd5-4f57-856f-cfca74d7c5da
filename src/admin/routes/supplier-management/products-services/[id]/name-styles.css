/* Custom styles for product service name truncation with line clamping */
.product-name-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* Ensure smooth transitions for show more/less functionality */
.product-name-clamp-2 {
  transition: all 0.2s ease-in-out;
}

/* Support for browsers that don't support -webkit-line-clamp */
@supports not (-webkit-line-clamp: 2) {
  .product-name-clamp-2 {
    display: block;
    max-height: calc(1.5em * 2); /* Assuming line-height of 1.5 */
    overflow: hidden;
    position: relative;
  }
  
  .product-name-clamp-2::after {
    content: "...";
    position: absolute;
    bottom: 0;
    right: 0;
    background: inherit;
    padding-left: 0.5em;
  }
}
