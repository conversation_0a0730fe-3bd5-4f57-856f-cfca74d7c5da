import React from "react";
import { Container, <PERSON>ing, Badge, StatusBadge } from "@camped-ai/ui";
import { TwoColumnPage } from "../../../../../components/layout/pages/two-column-page/two-column-page";
import { SectionRow } from "../../../../../components/common/section/section-row";

interface ProductService {
  id: string;
  name: string;
  description?: string;
  base_cost?: number;
  category_id: string;
  unit_type_id: string;
  tags?: Array<{
    id: string;
    name: string;
    color?: string;
  }>;
  custom_fields?: Record<string, any>;
  status: "active" | "inactive";
  category?: {
    id: string;
    name: string;
    description?: string;
    is_active: boolean;
    dynamic_field_schema?: any[];
  };
  unit_type?: { id: string; name: string };
  created_at: string;
  updated_at: string;
}

interface ProductServiceDetailsLayoutProps {
  productService: ProductService;
}

const ProductServiceDetailsLayout: React.FC<ProductServiceDetailsLayoutProps> = ({
  productService,
}) => {
  // Format date
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  // Get status badge color
  const getStatusBadgeColor = (status: string) => {
    switch (status) {
      case "active":
        return "green";
      case "inactive":
        return "grey";
      default:
        return "grey";
    }
  };

  // Main information section
  const ProductInformationSection = () => (
    <Container className="divide-y p-0">
      <div className="flex items-center justify-between px-6 py-4">
        <Heading level="h2">Product Details</Heading>
      </div>

      {/* <SectionRow
        title="Product/Service ID"
        value={productService.id}
      /> */}
      <SectionRow
        title="Description"
        value={productService.description || "—"}
      />
      <SectionRow
        title="Category"
        value={productService.category?.name || "—"}
      />
      <SectionRow
        title="Unit Type"
        value={productService.unit_type?.name || "—"}
      />
    </Container>
  );

  // Custom fields section
  const CustomFieldsSection = () => {
    const dynamicFieldSchema = productService.category?.dynamic_field_schema || [];
    const customFields = productService.custom_fields || {};

    if (dynamicFieldSchema.length === 0) {
      return null;
    }
    return (
      <Container className="divide-y p-0">
        <div className="flex items-center justify-between px-6 py-4">
          <Heading level="h2">Additional Details</Heading>
        </div>

        {dynamicFieldSchema.map((field: any) => {

          if(field.field_context === "customer") return
          if(field.used_in_product_services !== true) return

          const value = customFields[field.key];

          // Simple formatting using API-resolved names
          const formatFieldValue = (value: any, field: any) => {
            if (value === null || value === undefined) return "—";

            // Check for API-resolved names with standard naming pattern (field_key_names)
            const namesField = `${field.key}_names`;
            const resolvedNames = customFields[namesField];

            // Use resolved names if available
            if (
              resolvedNames &&
              Array.isArray(resolvedNames) &&
              resolvedNames.length > 0
            ) {
              // Filter out any null/undefined/empty values
              const validNames = resolvedNames.filter(
                (name) => name && typeof name === "string" && name.trim()
              );
              if (validNames.length > 0) {
                return validNames.join(", ");
              }
            }

            // Handle basic field types without external dependencies
            if (field.type === "boolean") {
              return value ? "Yes" : "No";
            }

            if (field.type === "date") {
              try {
                return new Date(value).toLocaleDateString("de-CH");
              } catch {
                return value.toString();
              }
            }

            if (field.type === "time-range") {
              // Helper function to convert 24-hour time to 12-hour format with AM/PM
              const formatTime = (timeStr: string): string => {
                try {
                  const [hours, minutes] = timeStr.split(':').map(Number);
                  if (isNaN(hours) || isNaN(minutes)) return timeStr;

                  const period = hours >= 12 ? 'PM' : 'AM';
                  const displayHours = hours === 0 ? 12 : hours > 12 ? hours - 12 : hours;
                  return `${displayHours}:${minutes.toString().padStart(2, '0')} ${period}`;
                } catch {
                  return timeStr;
                }
              };

              // Handle time range values stored as JSON objects
              if (typeof value === "object" && value !== null) {
                const { from, to } = value;
                if (from && to) {
                  return `${formatTime(from)} - ${formatTime(to)}`;
                } else if (from) {
                  return `From ${formatTime(from)}`;
                } else if (to) {
                  return `Until ${formatTime(to)}`;
                }
              }
              // Handle string format (JSON string)
              if (typeof value === "string") {
                try {
                  const parsed = JSON.parse(value);
                  if (parsed && typeof parsed === "object") {
                    const { from, to } = parsed;
                    if (from && to) {
                      return `${formatTime(from)} - ${formatTime(to)}`;
                    } else if (from) {
                      return `From ${formatTime(from)}`;
                    } else if (to) {
                      return `Until ${formatTime(to)}`;
                    }
                  }
                } catch {
                  // If parsing fails, return as-is
                  return value.toString();
                }
              }
              return "—";
            }

            if (field.type === "date-range") {
              // Handle date range values stored as JSON objects
              if (typeof value === "object" && value !== null) {
                const { from, to } = value;
                if (from && to) {
                  try {
                    const fromDate = new Date(from).toLocaleDateString("de-CH");
                    const toDate = new Date(to).toLocaleDateString("de-CH");
                    return `${fromDate} - ${toDate}`;
                  } catch {
                    return `${from} - ${to}`;
                  }
                } else if (from) {
                  try {
                    const fromDate = new Date(from).toLocaleDateString("de-CH");
                    return `From ${fromDate}`;
                  } catch {
                    return `From ${from}`;
                  }
                } else if (to) {
                  try {
                    const toDate = new Date(to).toLocaleDateString("de-CH");
                    return `Until ${toDate}`;
                  } catch {
                    return `Until ${to}`;
                  }
                }
              }
              // Handle string format (JSON string)
              if (typeof value === "string") {
                try {
                  const parsed = JSON.parse(value);
                  if (parsed && typeof parsed === "object") {
                    const { from, to } = parsed;
                    if (from && to) {
                      try {
                        const fromDate = new Date(from).toLocaleDateString("de-CH");
                        const toDate = new Date(to).toLocaleDateString("de-CH");
                        return `${fromDate} - ${toDate}`;
                      } catch {
                        return `${from} - ${to}`;
                      }
                    } else if (from) {
                      try {
                        const fromDate = new Date(from).toLocaleDateString("de-CH");
                        return `From ${fromDate}`;
                      } catch {
                        return `From ${from}`;
                      }
                    } else if (to) {
                      try {
                        const toDate = new Date(to).toLocaleDateString("de-CH");
                        return `Until ${toDate}`;
                      } catch {
                        return `Until ${to}`;
                      }
                    }
                  }
                } catch {
                  // If parsing fails, return as-is
                  return value.toString();
                }
              }
              return "—";
            }

            if (field.type === "number-range") {
              if (typeof value === "object" && value !== null) {
                const { min, max } = value;
                if (min !== undefined && max !== undefined) {
                  return `${min} - ${max}`;
                } else if (min !== undefined) {
                  return `${min}+`;
                } else if (max !== undefined) {
                  return `Up to ${max}`;
                }
              }
              if (typeof value === "string" && value.includes("-")) {
                return value;
              }
              return "—";
            }

            if (field.type === "dropdown" || field.type === "select") {
              if (field.options && Array.isArray(field.options)) {
                const option = field.options.find(
                  (opt: any) => (typeof opt === "object" ? opt.value : opt) === value
                );
                if (option) {
                  return typeof option === "object" ? option.label : option;
                }
              }
              return value.toString();
            }

            // Handle arrays (multi-select fields)
            if (Array.isArray(value)) {
              if (value.length === 0) {
                return "—";
              }
              return value.join(", ");
            }

            // Default: return as string
            return value.toString();
          };

          const formattedValue = formatFieldValue(value, field);
          return (
            <SectionRow
              key={field.key}
              title={field.label}
              value={formattedValue || "—"}
            />
          );
        })}
      </Container>
    );
  };

  // Tags section for sidebar
  const TagsSection = () => {
    if (!productService.tags || productService.tags.length === 0) {
      return null;
    }

    return (
      <Container className="divide-y p-0">
        <div className="flex items-center justify-between px-6 py-4">
          <Heading level="h2">Tags</Heading>
        </div>
        
        <div className="px-6 py-4">
          <div className="flex flex-wrap gap-2">
            {productService.tags.map((tag) => (
              <Badge
                key={tag.id}
                style={{ backgroundColor: tag.color || "#6B7280" }}
                className="text-white"
              >
                {tag.name}
              </Badge>
            ))}
          </div>
        </div>
      </Container>
    );
  };

  // Metadata section for sidebar
  const MetadataSection = () => (
    <Container className="divide-y p-0">
      <div className="flex items-center justify-between px-6 py-4">
        <Heading level="h2">Metadata</Heading>
      </div>
      
      <SectionRow
        title="Created"
        value={formatDate(productService.created_at)}
      />
      <SectionRow
        title="Last Updated"
        value={formatDate(productService.updated_at)}
      />
      <SectionRow
        title="Status"
        value={
          <StatusBadge color={getStatusBadgeColor(productService.status)}>
            {productService.status.charAt(0).toUpperCase() + productService.status.slice(1)}
          </StatusBadge>
        }
      />
    </Container>
  );

  return (
    <TwoColumnPage
      widgets={{
        before: [],
        after: [],
        sideBefore: [],
        sideAfter: [],
      }}
      data={productService}
      showJSON={false}
      showMetadata={false}
      hasOutlet={false}
    >
      <TwoColumnPage.Main>
        <ProductInformationSection />
      </TwoColumnPage.Main>

      <TwoColumnPage.Sidebar>
        <CustomFieldsSection />
        <TagsSection />
        <MetadataSection />
      </TwoColumnPage.Sidebar>
    </TwoColumnPage>
  );
};

export default ProductServiceDetailsLayout;
