import {
    Heading,
    Text,
    Button,
    Badge,
    Select,
    FocusModal,
    toast,
    Label,
} from "@camped-ai/ui";

import {
    Download,
    Upload,
    CheckCircle,
    AlertCircle,
    ArrowLeft,
} from "lucide-react";

import {
    useProductServiceImportExport,
} from "../../../../hooks/supplier-products-services/use-products-services";
import { useUnitTypes } from "../../../../hooks/supplier-products-services/use-unit-types";
import { useCategories } from "../../../../hooks/supplier-products-services/use-categories";
import { useHotels } from "../../../../hooks/supplier-products-services/use-hotels";
import { useDestinations } from "../../../../hooks/supplier-products-services/use-destinations";
import { useNavigate } from "react-router-dom";
import { defineRouteConfig } from "@camped-ai/admin-sdk";

import { useState } from "react";


export const ProductServicesImport = () => {
    const navigate = useNavigate();

    // Import functionality
    const [importFile, setImportFile] = useState<File | null>(null);
    const [importData, setImportData] = useState<any[]>([]);
    const [importErrors, setImportErrors] = useState<any[]>([]);
    const [importSuccess, setImportSuccess] = useState(false);
    const [importedCount, setImportedCount] = useState(0);

    // State for selected category in import modal
    const [selectedCategoryId, setSelectedCategoryId] = useState<string>("");


    // Import/Export hook
    const { parseImportFile, importProductServices, isImporting } =
        useProductServiceImportExport();

    // Fetch categories for import template generation
    const { data: importCategoriesData, isLoading: importCategoriesLoading } =
        useCategories({ limit: 100 });

    // Fetch hotels and destinations for name resolution
    const { data: hotelsData } = useHotels({ is_active: true });
    const { data: destinationsData } = useDestinations({ is_active: true });

    // API integration for filter options
    const { data: categoriesData, isLoading: categoriesLoading } = useCategories({
        is_active: true,
    });

    const { data: unitTypesData, isLoading: unitTypesLoading } = useUnitTypes({
        is_active: true,
    });
    const categories = categoriesData?.categories || [];
    const unitTypes = unitTypesData?.unit_types || [];
    const hotels = hotelsData?.hotels || [];
    const destinations = destinationsData?.destinations || [];


    const handleFileUpload = async (file: File) => {
        // Validate category selection before processing file
        if (!selectedCategoryId) {
            toast.error("Please select a category before uploading a file");
            return;
        }

        setImportFile(file);
        setImportData([]);
        setImportErrors([]);

        try {
            console.log(`Auto-parsing file: ${file.name}`);
            const { data, errors } = await parseImportFile(file, selectedCategoryId);
            setImportData(data);
            setImportErrors(errors);

            // Validation errors will be shown in the modal UI
            // No toast needed - errors are displayed in the validation errors section
        } catch (error) {
            toast.error("Failed to parse file");
            console.error("File parsing error:", error);
        }
    };

    // Function to handle downloading template for selected category
    const handleDownloadTemplate = async () => {
        if (!selectedCategoryId) {
            toast.error("Please select a category first");
            return;
        }

        const selectedCategory = importCategoriesData?.categories.find(
            (cat) => cat.id === selectedCategoryId
        );
        if (!selectedCategory) {
            toast.error("Selected category not found");
            return;
        }

        try {
            const response = await fetch(
                `/admin/supplier-management/products-services/template?category_id=${selectedCategoryId}`,
                {
                    method: "GET",
                    headers: {
                        "Content-Type": "application/json",
                    },
                }
            );

            if (!response.ok) {
                throw new Error("Failed to download template");
            }

            // Get the blob from the response
            const blob = await response.blob();

            // Create a download link
            const url = window.URL.createObjectURL(blob);
            const link = document.createElement("a");
            link.href = url;
            link.download = `products_services_template_${selectedCategory.name}_${new Date().toISOString().split("T")[0]
                }.xlsx`;
            document.body.appendChild(link);
            link.click();

            // Clean up
            document.body.removeChild(link);
            window.URL.revokeObjectURL(url);

            toast.success(
                `Template for ${selectedCategory.name} downloaded successfully`
            );
        } catch (error) {
            console.error("Error downloading template:", error);
            toast.error(`Failed to download template for ${selectedCategory.name}`);
        }
    };

    // Handle navigation back to main page
    const handleBackToMain = () => {
        navigate("/supplier-management/products-services");
    };

    const resetImportModal = () => {
        setSelectedCategoryId("");
        setImportFile(null);
        setImportData([]);
        setImportErrors([]);
        setImportSuccess(false);
        setImportedCount(0);
    };

    // Import handlers
    const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
        const file = event.target.files?.[0];
        if (file) {
            // Automatically parse the file when selected
            handleFileUpload(file);
        }
    };


    const handleImport = async () => {
        if (importData.length === 0) return;

        try {
            const result = await importProductServices(importData);
            console.log("Import completed, result:", result);

            // Manually refetch the data to ensure it's updated
            console.log("Manually refetching data...");
            //   await refetch();

            // Set success state to show in modal instead of toast
            setImportSuccess(true);
            setImportedCount(importData.length);

            // Reset import state after a delay
            setTimeout(() => {
                resetImportModal();
            }, 3000); // Slightly longer delay to show success message
        } catch (error) {
            console.error("Import error:", error);
            toast.error("Failed to import data");
        }
    };


    return (
        <FocusModal open={true} onOpenChange={handleBackToMain}>
            <FocusModal.Content className="flex flex-col h-full max-h-[98vh] bg-gray-50 dark:bg-gray-900">
                <FocusModal.Header className="flex-shrink-0 bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
                    <div className="flex justify-between items-center w-full py-4 px-6">
                        <div className="flex items-center gap-3">
                            <Heading
                                level="h2"
                                className="text-xl font-semibold text-gray-900 dark:text-gray-100"
                            >
                                Import Products & Services
                            </Heading>
                        </div>
                    </div>

                    {/* Progress Indicator */}
                    <div className="px-6 py-2 border-b border-gray-200 dark:border-gray-700">
                        <div className="flex items-center justify-between">
                            <div className="flex items-center space-x-4">
                                {/* Step 1 */}
                                <div className="flex items-center">
                                    <div className="w-8 h-8 bg-blue-600 dark:bg-blue-500 text-white rounded-full flex items-center justify-center text-sm font-semibold">
                                        1
                                    </div>
                                    <span className="ml-2 text-sm font-medium text-blue-600 dark:text-blue-400">
                                        Download
                                    </span>
                                </div>
                                <div className="w-12 h-0.5 bg-gray-300 dark:bg-gray-600"></div>

                                {/* Step 2 */}
                                <div className="flex items-center">
                                    <div
                                        className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-semibold ${importFile
                                            ? "bg-green-600 dark:bg-green-500 text-white"
                                            : "bg-gray-300 dark:bg-gray-600 text-gray-600 dark:text-gray-400"
                                            }`}
                                    >
                                        2
                                    </div>
                                    <span
                                        className={`ml-2 text-sm font-medium ${importFile
                                            ? "text-green-600 dark:text-green-400"
                                            : "text-gray-500 dark:text-gray-400"
                                            }`}
                                    >
                                        Upload
                                    </span>
                                </div>
                                <div className="w-12 h-0.5 bg-gray-300 dark:bg-gray-600"></div>

                                <div className="flex items-center">
                                    <div
                                        className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-semibold ${importData.length > 0
                                            ? "bg-yellow-500 dark:bg-yellow-400 text-white"
                                            : "bg-gray-300 dark:bg-gray-600 text-gray-600 dark:text-gray-400"
                                            }`}
                                    >
                                        3
                                    </div>
                                    <span
                                        className={`ml-2 text-sm font-medium ${importData.length > 0
                                            ? "text-yellow-600 dark:text-yellow-400"
                                            : "text-gray-500 dark:text-gray-400"
                                            }`}
                                    >
                                        Review
                                    </span>
                                </div>
                                <div className="w-12 h-0.5 bg-gray-300 dark:bg-gray-600"></div>

                                {/* Step 4 */}
                                <div className="flex items-center">
                                    <div
                                        className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-semibold ${importSuccess
                                            ? "bg-green-600 dark:bg-green-500 text-white"
                                            : importData.length > 0 && importErrors.length === 0
                                                ? "bg-purple-600 dark:bg-purple-500 text-white"
                                                : "bg-gray-300 dark:bg-gray-600 text-gray-600 dark:text-gray-400"
                                            }`}
                                    >
                                        {importSuccess ? "✓" : "4"}
                                    </div>
                                    <span
                                        className={`ml-2 text-sm font-medium ${importSuccess
                                            ? "text-green-600 dark:text-green-400"
                                            : importData.length > 0 && importErrors.length === 0
                                                ? "text-purple-600 dark:text-purple-400"
                                                : "text-gray-500 dark:text-gray-400"
                                            }`}
                                    >
                                        {importSuccess ? "Complete" : "Import"}
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                </FocusModal.Header>

                <FocusModal.Body className="flex flex-col flex-grow overflow-hidden">
                    <div className="flex-grow overflow-y-auto p-6 space-y-6">
                        <>
                            {/* Step 1: Download Template */}
                            <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 shadow-sm">
                                <div className="flex items-start gap-3 p-6">
                                    <div className="flex-shrink-0 w-8 h-8 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center">
                                        <span className="text-blue-600 dark:text-blue-400 font-semibold text-sm">
                                            1
                                        </span>
                                    </div>
                                    <div className="flex-1">
                                        <Heading
                                            level="h3"
                                            className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2"
                                        >
                                            Download Import Template
                                        </Heading>
                                        <Text className="text-gray-600 dark:text-gray-400 mb-4">
                                            Download category-specific Excel templates with
                                            pre-configured fields for products & services. Each
                                            category has its own template with custom fields
                                            specific to that category.
                                        </Text>

                                        <div className="bg-blue-50 dark:bg-blue-900/20 p-3 rounded-md border border-blue-200 dark:border-blue-700 mb-4">
                                            <Text className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                                Each template includes:
                                            </Text>
                                            <ul className="text-sm text-gray-600 dark:text-gray-400 space-y-1">
                                                <li>
                                                    •{" "}
                                                    <strong className="text-gray-800 dark:text-gray-200">
                                                        Category-specific fields:
                                                    </strong>{" "}
                                                    Custom fields and validation for the selected
                                                    category
                                                </li>
                                                <li>
                                                    •{" "}
                                                    <strong className="text-gray-800 dark:text-gray-200">
                                                        User-friendly dropdowns:
                                                    </strong>{" "}
                                                    Product/Service types, unit type names, tag names
                                                </li>
                                                <li>
                                                    •{" "}
                                                    <strong className="text-gray-800 dark:text-gray-200">
                                                        Tags reference sheet:
                                                    </strong>{" "}
                                                    Easy lookup for tag names (supports comma-separated
                                                    values)
                                                </li>
                                                <li>
                                                    •{" "}
                                                    <strong className="text-gray-800 dark:text-gray-200">
                                                        Custom field dropdowns:
                                                    </strong>{" "}
                                                    Dynamic validation for category-specific fields
                                                </li>
                                                <li>
                                                    •{" "}
                                                    <strong className="text-gray-800 dark:text-gray-200">
                                                        Instructions sheet:
                                                    </strong>{" "}
                                                    Comprehensive field guide with all available options
                                                </li>
                                            </ul>
                                        </div>

                                        {/* Category Selection and Download */}
                                        {importCategoriesLoading ? (
                                            <div className="flex items-center justify-center py-4">
                                                <Text className="text-gray-500 dark:text-gray-400">
                                                    Loading categories...
                                                </Text>
                                            </div>
                                        ) : importCategoriesData?.categories &&
                                            importCategoriesData.categories.length > 0 ? (
                                            <div className="space-y-4">
                                                <div>
                                                    <Label className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 block">
                                                        Select Category:
                                                    </Label>
                                                    <Select
                                                        value={selectedCategoryId}
                                                        onValueChange={setSelectedCategoryId}
                                                    >
                                                        <Select.Trigger className="w-80 max-w-full">
                                                            <Select.Value placeholder="Choose a category..." />
                                                        </Select.Trigger>
                                                        <Select.Content>
                                                            {importCategoriesData.categories.map(
                                                                (category) => (
                                                                    <Select.Item
                                                                        key={category.id}
                                                                        value={category.id}
                                                                    >
                                                                        <div className="flex items-center gap-2">
                                                                            <span>{category.icon}</span>
                                                                            <span>{category.name}</span>
                                                                            <span className="text-xs text-gray-500">
                                                                                ({category.category_type})
                                                                            </span>
                                                                        </div>
                                                                    </Select.Item>
                                                                )
                                                            )}
                                                        </Select.Content>
                                                    </Select>
                                                </div>

                                                <Button
                                                    variant="secondary"
                                                    onClick={handleDownloadTemplate}
                                                    disabled={!selectedCategoryId}
                                                    className="flex items-center gap-2 bg-blue-600 dark:bg-blue-500 text-white hover:bg-blue-700 dark:hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed"
                                                >
                                                    <Download className="w-4 h-4" />
                                                    Download Excel Template
                                                </Button>
                                            </div>
                                        ) : (
                                            <div className="bg-yellow-50 dark:bg-yellow-900/20 p-3 rounded-md border border-yellow-200 dark:border-yellow-700">
                                                <Text className="text-sm text-yellow-700 dark:text-yellow-300">
                                                    No categories found. Please create categories first
                                                    before generating templates.
                                                </Text>
                                            </div>
                                        )}
                                    </div>
                                </div>
                            </div>

                            {/* Step 2: Upload File */}
                            <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 shadow-sm">
                                <div className="flex items-start gap-3 p-6">
                                    <div className="flex-shrink-0 w-8 h-8 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center">
                                        <span className="text-green-600 dark:text-green-400 font-semibold text-sm">
                                            2
                                        </span>
                                    </div>
                                    <div className="flex-1">
                                        <Heading
                                            level="h3"
                                            className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2"
                                        >
                                            Upload & Parse Your Data File
                                        </Heading>
                                        <Text className="text-gray-600 dark:text-gray-400 mb-4">
                                            Upload the completed Excel file with your products &
                                            services data. The file will be automatically parsed and
                                            validated.
                                        </Text>

                                        {/* Category Selection Validation Message */}
                                        {!selectedCategoryId && (
                                            <div className="bg-yellow-50 dark:bg-yellow-900/20 p-3 rounded-md border border-yellow-200 dark:border-yellow-700 mb-4">
                                                <div className="flex items-center gap-2">
                                                    <AlertCircle className="w-4 h-4 text-yellow-600 dark:text-yellow-400" />
                                                    <Text className="text-sm font-medium text-yellow-700 dark:text-yellow-300">
                                                        Please select a category before uploading a file
                                                    </Text>
                                                </div>
                                                <Text className="text-xs text-yellow-600 dark:text-yellow-400 mt-1">
                                                    Category selection is required to validate your data against the correct template structure.
                                                </Text>
                                            </div>
                                        )}

                                        {/* File Upload Area */}
                                        <div
                                            className={`border-2 border-dashed rounded-lg p-8 text-center transition-colors ${
                                                !selectedCategoryId
                                                    ? "border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800/50 opacity-60"
                                                    : importFile
                                                    ? "border-green-300 dark:border-green-600 bg-green-50 dark:bg-green-900/20"
                                                    : "border-gray-300 dark:border-gray-600 hover:border-gray-400 dark:hover:border-gray-500"
                                                }`}
                                        >
                                            {importFile ? (
                                                <>
                                                    <CheckCircle className="w-12 h-12 text-green-500 mx-auto mb-4" />
                                                    <Text className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
                                                        File Selected: {importFile.name}
                                                    </Text>
                                                    <Text className="text-sm text-gray-500 dark:text-gray-400 mb-4">
                                                        Size: {(importFile.size / 1024 / 1024).toFixed(2)}{" "}
                                                        MB
                                                    </Text>
                                                    <Button
                                                        variant="secondary"
                                                        onClick={() => setImportFile(null)}
                                                        className="text-sm"
                                                    >
                                                        Choose Different File
                                                    </Button>
                                                </>
                                            ) : (
                                                <>
                                                    <Upload className={`w-12 h-12 mx-auto mb-4 ${
                                                        !selectedCategoryId
                                                            ? "text-gray-300 dark:text-gray-600"
                                                            : "text-gray-400 dark:text-gray-500"
                                                    }`} />
                                                    <Text className={`text-lg font-medium mb-2 ${
                                                        !selectedCategoryId
                                                            ? "text-gray-500 dark:text-gray-600"
                                                            : "text-gray-900 dark:text-gray-100"
                                                    }`}>
                                                        {!selectedCategoryId
                                                            ? "Select a category first to enable file upload"
                                                            : "Click to upload or drag and drop"
                                                        }
                                                    </Text>
                                                    <Text className={`text-sm mb-4 ${
                                                        !selectedCategoryId
                                                            ? "text-gray-400 dark:text-gray-600"
                                                            : "text-gray-500 dark:text-gray-400"
                                                    }`}>
                                                        Excel (.xlsx, .xls) and CSV files • Maximum 5MB
                                                    </Text>
                                                    <input
                                                        type="file"
                                                        accept=".xlsx,.xls,.csv"
                                                        onChange={handleFileSelect}
                                                        className="hidden"
                                                        id="file-upload"
                                                        disabled={!selectedCategoryId}
                                                    />
                                                    <label
                                                        htmlFor="file-upload"
                                                        className={`inline-flex items-center px-4 py-2 rounded-md transition-colors ${
                                                            !selectedCategoryId
                                                                ? "bg-gray-300 dark:bg-gray-600 text-gray-500 dark:text-gray-400 cursor-not-allowed"
                                                                : "bg-blue-600 dark:bg-blue-500 text-white hover:bg-blue-700 dark:hover:bg-blue-600 cursor-pointer"
                                                        }`}
                                                    >
                                                        Select a file to import
                                                    </label>
                                                </>
                                            )}
                                        </div>
                                    </div>
                                </div>
                            </div>

                            {/* Validation Errors Section */}
                            {importErrors.length > 0 && (
                                <div className="bg-white dark:bg-gray-800 rounded-lg border border-red-200 dark:border-red-700 shadow-sm">
                                    <div className="flex items-start gap-3 p-6">
                                        <div className="flex-shrink-0 w-8 h-8 bg-red-100 dark:bg-red-900 rounded-full flex items-center justify-center">
                                            <AlertCircle className="w-5 h-5 text-red-600 dark:text-red-400" />
                                        </div>
                                        <div className="flex-1">
                                            <Heading
                                                level="h3"
                                                className="text-lg font-medium text-red-900 dark:text-red-100 mb-2"
                                            >
                                                Validation Errors Found
                                            </Heading>
                                            <Text className="text-red-700 dark:text-red-300 mb-4">
                                                Found {importErrors.length} validation error
                                                {importErrors.length > 1 ? "s" : ""} in your data.
                                                Please fix these issues and re-upload the file.
                                            </Text>

                                            {/* Error List */}
                                            <div className="bg-red-50 dark:bg-red-900/20 rounded-md border border-red-200 dark:border-red-700 max-h-64 overflow-y-auto">
                                                <div className="p-4 space-y-3">
                                                    {importErrors.slice(0, 20).map((error, index) => (
                                                        <div
                                                            key={index}
                                                            className="flex items-start gap-3 p-3 bg-white dark:bg-gray-800 rounded border border-red-100 dark:border-red-800"
                                                        >
                                                            <div className="flex-shrink-0 w-6 h-6 bg-red-100 dark:bg-red-900 rounded-full flex items-center justify-center">
                                                                <span className="text-red-600 dark:text-red-400 text-xs font-medium">
                                                                    {error.row || index + 1}
                                                                </span>
                                                            </div>
                                                            <div className="flex-1 min-w-0">
                                                                <div className="flex items-center gap-2 mb-1">
                                                                    <Text className="text-sm font-medium text-red-900 dark:text-red-100">
                                                                        Row {error.row || index + 1}
                                                                    </Text>
                                                                    {error.field && (
                                                                        <Badge className="text-xs bg-red-100 dark:bg-red-900 text-red-700 dark:text-red-300">
                                                                            {error.field}
                                                                        </Badge>
                                                                    )}
                                                                </div>
                                                                <Text className="text-sm text-red-700 dark:text-red-300">
                                                                    {error.message}
                                                                </Text>
                                                                {error.value && (
                                                                    <Text className="text-xs text-red-600 dark:text-red-400 mt-1 font-mono bg-red-50 dark:bg-red-900/30 px-2 py-1 rounded">
                                                                        Value:{" "}
                                                                        {typeof error.value === "object"
                                                                            ? JSON.stringify(error.value)
                                                                            : String(error.value)}
                                                                    </Text>
                                                                )}
                                                            </div>
                                                        </div>
                                                    ))}
                                                    {importErrors.length > 20 && (
                                                        <div className="text-center py-2">
                                                            <Text className="text-sm text-red-600 dark:text-red-400">
                                                                ... and {importErrors.length - 20} more errors
                                                            </Text>
                                                        </div>
                                                    )}
                                                </div>
                                            </div>

                                            {/* Error Summary */}
                                            <div className="mt-4 p-3 bg-red-50 dark:bg-red-900/20 rounded-md border border-red-200 dark:border-red-700">
                                                <Text className="text-sm text-red-700 dark:text-red-300">
                                                    <strong>Next Steps:</strong>
                                                    <br />
                                                    1. Review the errors listed above
                                                    <br />
                                                    2. Fix the data in your Excel file
                                                    <br />
                                                    3. Re-upload the corrected file
                                                    <br />
                                                    4. Import will be enabled once all errors are
                                                    resolved
                                                </Text>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            )}

                            {/* File Preview Section */}
                            {importFile && importData.length > 0 && !importSuccess && (() => {
                                // Filter out completely empty rows
                                const filteredData = importData.filter((row) => {
                                    return Object.values(row).some(value =>
                                        value !== null &&
                                        value !== undefined &&
                                        value !== '' &&
                                        String(value).trim() !== ''
                                    );
                                }).slice(0, 10); // Limit to first 10 non-empty rows

                                // Create lookup maps for ID resolution
                                const categoryMap = new Map();
                                const unitTypeMap = new Map();
                                const hotelMap = new Map();
                                const destinationMap = new Map();

                                if (categories) {
                                    categories.forEach((cat: any) => {
                                        categoryMap.set(cat.id, cat.name);
                                    });
                                }

                                if (unitTypes) {
                                    unitTypes.forEach((ut: any) => {
                                        unitTypeMap.set(ut.id, ut.name);
                                    });
                                }

                                if (hotels) {
                                    hotels.forEach((hotel: any) => {
                                        hotelMap.set(hotel.id, hotel.name);
                                    });
                                }

                                if (destinations) {
                                    destinations.forEach((dest: any) => {
                                        destinationMap.set(dest.id, dest.name);
                                    });
                                }

                                // Collect all unique custom field keys across all rows
                                const customFieldKeys = new Set<string>();
                                filteredData.forEach(row => {
                                    if (row.custom_fields && typeof row.custom_fields === 'object') {
                                        Object.keys(row.custom_fields).forEach(key => customFieldKeys.add(key));
                                    }
                                });

                                // Define standard columns (excluding custom_fields as it will be split)
                                const standardColumns = ['description', 'status', 'base_cost', 'category_id', 'unit_type_id'];
                                const customFieldColumns = Array.from(customFieldKeys).sort();
                                const allColumns = [...standardColumns, ...customFieldColumns];

                                // Transform data for display with ID resolution
                                const transformedData = filteredData.map(row => {
                                    const transformed: any = {};

                                    // Handle standard fields with ID resolution
                                    standardColumns.forEach(col => {
                                        if (col === 'category_id') {
                                            // Resolve category ID to name, fallback to original value
                                            const categoryName = categoryMap.get(row[col]);
                                            transformed['category_name'] = categoryName || row.category_name || row[col] || '—';
                                        } else if (col === 'unit_type_id') {
                                            // Resolve unit type ID to name, fallback to original value
                                            const unitTypeName = unitTypeMap.get(row[col]);
                                            transformed['unit_type_name'] = unitTypeName || row.unit_type_name || row[col] || '—';
                                        } else {
                                            transformed[col] = row[col];
                                        }
                                    });

                                    // Handle custom fields as separate columns
                                    customFieldColumns.forEach(fieldKey => {
                                        const fieldValue = row.custom_fields?.[fieldKey];
                                        if (fieldValue !== null && fieldValue !== undefined && fieldValue !== '') {
                                            if (Array.isArray(fieldValue)) {
                                                // Try to resolve array of IDs to names
                                                const resolvedNames: string[] = [];

                                                fieldValue.forEach((id: any) => {
                                                    if (typeof id === 'string') {
                                                        // Try to resolve as hotel ID
                                                        const hotelName = hotelMap.get(id);
                                                        if (hotelName) {
                                                            resolvedNames.push(hotelName);
                                                            return;
                                                        }

                                                        // Try to resolve as destination ID
                                                        const destinationName = destinationMap.get(id);
                                                        if (destinationName) {
                                                            resolvedNames.push(destinationName);
                                                            return;
                                                        }

                                                        // If not resolved, keep original value
                                                        resolvedNames.push(String(id));
                                                    } else {
                                                        resolvedNames.push(String(id));
                                                    }
                                                });

                                                // Display resolved names or fallback to count
                                                if (resolvedNames.length > 0 && resolvedNames.some(name => name !== String(fieldValue[resolvedNames.indexOf(name)]))) {
                                                    // Some IDs were resolved to names, show the names
                                                    transformed[fieldKey] = resolvedNames.join(', ');
                                                } else {
                                                    // No IDs were resolved, show count
                                                    transformed[fieldKey] = `[${fieldValue.length} items]`;
                                                }
                                            } else if (typeof fieldValue === 'object') {
                                                transformed[fieldKey] = JSON.stringify(fieldValue);
                                            } else {
                                                // Try to resolve single ID to name
                                                const singleId = String(fieldValue);
                                                const hotelName = hotelMap.get(singleId);
                                                const destinationName = destinationMap.get(singleId);

                                                if (hotelName) {
                                                    transformed[fieldKey] = hotelName;
                                                } else if (destinationName) {
                                                    transformed[fieldKey] = destinationName;
                                                } else {
                                                    transformed[fieldKey] = singleId;
                                                }
                                            }
                                        } else {
                                            transformed[fieldKey] = '—';
                                        }
                                    });

                                    return transformed;
                                });

                                // Update column headers for display
                                const displayColumns = [
                                    'description', 'status', 'base_cost', 'category_name', 'unit_type_name',
                                    ...customFieldColumns
                                ];

                                return (
                                    <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 shadow-sm">
                                        <div className="p-6">
                                            <div className="flex items-center gap-2 mb-4">
                                                <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                                                <Text className="font-semibold text-gray-800 dark:text-gray-200">Products/Services Data Preview</Text>
                                                <span className="bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 text-xs font-medium px-2 py-1 rounded-full">
                                                    {filteredData.length} rows
                                                </span>
                                            </div>

                                            <div className="border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden shadow-sm">
                                                <div className="overflow-x-auto">
                                                    <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                                                        <thead className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20">
                                                            <tr>
                                                                {displayColumns.map((header) => (
                                                                    <th
                                                                        key={header}
                                                                        className="px-4 py-3 text-left text-xs font-semibold text-blue-800 dark:text-blue-200 uppercase tracking-wider"
                                                                    >
                                                                        {header.replace(/_/g, ' ')}
                                                                    </th>
                                                                ))}
                                                            </tr>
                                                        </thead>
                                                        <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-100 dark:divide-gray-700">
                                                            {transformedData.map((row, rowIndex) => (
                                                                <tr
                                                                    key={rowIndex}
                                                                    className={rowIndex % 2 === 0 ? 'bg-white dark:bg-gray-800 hover:bg-blue-25 dark:hover:bg-blue-900/10' : 'bg-blue-25 dark:bg-blue-900/5 hover:bg-blue-50 dark:hover:bg-blue-900/20'}
                                                                >
                                                                    {displayColumns.map((colKey, cellIndex) => {
                                                                        const value = row[colKey];
                                                                        let displayValue = '—';

                                                                        if (value !== null && value !== undefined && value !== '') {
                                                                            displayValue = String(value);
                                                                        }

                                                                        // Determine if field should be truncated
                                                                        const shouldTruncate = ['description'].includes(colKey);
                                                                        const maxLength = shouldTruncate ? 50 : 100;
                                                                        const truncatedValue = displayValue.length > maxLength
                                                                            ? displayValue.substring(0, maxLength) + '...'
                                                                            : displayValue;
                                                                        const isTruncated = displayValue.length > maxLength;

                                                                        return (
                                                                            <td
                                                                                key={cellIndex}
                                                                                className="px-4 py-3 text-sm text-gray-700 dark:text-gray-300 font-medium"
                                                                                title={isTruncated ? `Full text: ${displayValue}` : undefined}
                                                                            >
                                                                                <div className="flex items-center gap-1">
                                                                                    <span>{truncatedValue}</span>
                                                                                </div>
                                                                            </td>
                                                                        );
                                                                    })}
                                                                </tr>
                                                            ))}
                                                        </tbody>
                                                    </table>
                                                </div>
                                            </div>

                                            <div className="mt-3 flex items-center gap-2">
                                                <div className="w-2 h-2 bg-blue-400 rounded-full"></div>
                                                <Text className="text-blue-600 dark:text-blue-400 text-sm font-medium">
                                                    Showing first {filteredData.length} rows • Ready for import
                                                </Text>
                                            </div>
                                        </div>
                                    </div>
                                );
                            })()}

                            {/* Success Message */}
                            {importSuccess && (
                                <div className="bg-white dark:bg-gray-800 rounded-lg border border-green-200 dark:border-green-700 shadow-sm">
                                    <div className="flex items-start gap-3 p-6">
                                        <div className="flex-shrink-0 w-8 h-8 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center">
                                            <CheckCircle className="w-5 h-5 text-green-600 dark:text-green-400" />
                                        </div>
                                        <div className="flex-1">
                                            <Heading
                                                level="h3"
                                                className="text-lg font-medium text-green-900 dark:text-green-100 mb-2"
                                            >
                                                Import Successful!
                                            </Heading>
                                            <Text className="text-green-700 dark:text-green-300 mb-4">
                                                Successfully imported {importedCount}{" "}
                                                products/services. The data has been added to your
                                                inventory and is now available in the products &
                                                services list.
                                            </Text>
                                            <div className="bg-green-50 dark:bg-green-900/20 p-3 rounded-md border border-green-200 dark:border-green-700">
                                                <Text className="text-sm text-green-700 dark:text-green-300">
                                                    ✓ Data imported and validated
                                                    <br />
                                                    ✓ Records added to database
                                                    <br />✓ Available in products & services list
                                                </Text>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            )}
                        </>
                    </div>

                    {/* Footer */}
                    <div className="flex-shrink-0 py-6 px-8 bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700">
                        <div className="flex items-center justify-between">
                            <div className="text-sm text-gray-500 dark:text-gray-400">
                                {importSuccess ? (
                                    <span className="text-green-600 dark:text-green-400 font-medium">
                                        ✅ Successfully imported {importedCount} records
                                    </span>
                                ) : importFile ? (
                                    <>
                                        <span className="text-green-600 dark:text-green-400">
                                            ✓
                                        </span>{" "}
                                        {importFile.name}
                                        {importData.length > 0 && importErrors.length === 0 && (
                                            <span className="text-green-600 dark:text-green-400">
                                                {" "}
                                                • {importData.length} records ready
                                            </span>
                                        )}
                                        {importErrors.length > 0 && (
                                            <span className="text-red-600 dark:text-red-400">
                                                {" "}
                                                • {importErrors.length} errors found
                                            </span>
                                        )}
                                    </>
                                ) : (
                                    "Select a file to automatically parse and import"
                                )}
                            </div>
                            <div className="flex gap-4">
                                <Button
                                    variant="secondary"
                                    onClick={resetImportModal}
                                    className="px-6 py-3 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600 border-gray-300 dark:border-gray-600 font-medium"
                                >
                                    Cancel
                                </Button>
                                <Button
                                    variant="primary"
                                    onClick={handleImport}
                                    disabled={
                                        isImporting ||
                                        importSuccess ||
                                        !importFile ||
                                        importData.length === 0 ||
                                        importErrors.length > 0
                                    }
                                    className={`flex items-center gap-3 px-6 py-3 shadow-lg font-medium disabled:opacity-50 disabled:cursor-not-allowed ${importSuccess
                                        ? "bg-green-600 dark:bg-green-500 text-white"
                                        : "bg-blue-600 dark:bg-blue-500 hover:bg-blue-700 dark:hover:bg-blue-600 text-white"
                                        }`}
                                >
                                    {importSuccess ? (
                                        <>
                                            <CheckCircle className="w-5 h-5" />
                                            Import Complete
                                        </>
                                    ) : (
                                        <>
                                            <Upload className="w-5 h-5" />
                                            {isImporting
                                                ? "Importing..."
                                                : `Import Data${importData.length > 0
                                                    ? ` (${importData.length} Records)`
                                                    : ""
                                                }`}
                                        </>
                                    )}
                                </Button>
                            </div>
                        </div>
                    </div>
                </FocusModal.Body>
            </FocusModal.Content>
        </FocusModal>
    )
}

export const config = defineRouteConfig({
    label: "Import Products & Services",
    icon: Upload,
});

export default ProductServicesImport;