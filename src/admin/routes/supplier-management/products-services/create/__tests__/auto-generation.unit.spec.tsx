import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import ProductServiceForm from '../page-client';

// Mock the hooks
jest.mock('../../../../../hooks/supplier-products-services/use-categories', () => ({
  useCategories: () => ({
    data: {
      categories: [
        {
          id: 'cat-1',
          name: 'Transportation',
          category_type: 'Service',
          dynamic_field_schema: [
            {
              label: 'Vehicle Type',
              key: 'vehicle_type',
              type: 'dropdown',
              options: ['Sedan', 'Minivan', 'Bus'],
              required: true,
              used_in_product: true,
              order: 1
            },
            {
              label: 'Passenger Count',
              key: 'passenger_count',
              type: 'number',
              required: true,
              used_in_product: true,
              order: 2
            }
          ]
        }
      ]
    },
    isLoading: false
  })
}));

jest.mock('../../../../../hooks/supplier-products-services/use-unit-types', () => ({
  useUnitTypes: () => ({
    data: {
      unit_types: [
        { id: 'unit-1', name: 'Per Trip' }
      ]
    }
  })
}));

jest.mock('../../../../../hooks/supplier-products-services/use-hotels', () => ({
  useHotels: () => ({
    data: { hotels: [] }
  })
}));

jest.mock('../../../../../hooks/supplier-products-services/use-destinations', () => ({
  useDestinations: () => ({
    data: { destinations: [] }
  })
}));

jest.mock('../../../../../hooks/supplier-products-services/use-products-services', () => ({
  useProductsServices: () => ({
    data: { product_services: [] }
  })
}));

// Mock the name generator
jest.mock('../../../../../../modules/supplier-products-services/utils/name-generator', () => ({
  generateProductServiceName: jest.fn().mockResolvedValue('Transportation – Minivan – 6')
}));

const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: { retry: false },
    mutations: { retry: false }
  }
});

describe('Product Service Auto-Generation', () => {
  let queryClient: QueryClient;

  beforeEach(() => {
    queryClient = createTestQueryClient();
    jest.clearAllMocks();
  });

  it('should disable name field from the start in create mode', () => {
    renderComponent();

    const nameInput = screen.getByLabelText(/name/i) as HTMLInputElement;
    expect(nameInput.disabled).toBe(true);
    expect(screen.getByText('(Auto-generated)')).toBeTruthy();
  });

  const renderComponent = (props = {}) => {
    const defaultProps = {
      mode: 'create' as const,
      onSubmit: jest.fn(),
      onCancel: jest.fn(),
      isSubmitting: false
    };

    return render(
      <QueryClientProvider client={queryClient}>
        <ProductServiceForm {...defaultProps} {...props} />
      </QueryClientProvider>
    );
  };

  it('should auto-generate name when category and custom fields are selected', async () => {
    renderComponent();

    // Select category
    const categorySelect = screen.getByRole('combobox', { name: /category/i });
    fireEvent.click(categorySelect);

    const categoryOption = screen.getByText('Transportation');
    fireEvent.click(categoryOption);

    // Select unit type
    const unitTypeSelect = screen.getByRole('combobox', { name: /pricing unit/i });
    fireEvent.click(unitTypeSelect);

    const unitTypeOption = screen.getByText('Per Trip');
    fireEvent.click(unitTypeOption);

    // Wait for name generation (should include unit type now)
    await waitFor(() => {
      const nameInput = screen.getByLabelText(/name/i) as HTMLInputElement;
      expect(nameInput.value).toBe('Transportation – Minivan – 6 – (Per Trip)');
    });

    // Check auto-generated indicator
    expect(screen.getByText('(Auto-generated)')).toBeTruthy();
    expect(screen.getByText(/Name is automatically generated/)).toBeTruthy();
  });

  it('should regenerate name when unit type changes', async () => {
    renderComponent();

    // Select category first
    const categorySelect = screen.getByRole('combobox', { name: /category/i });
    fireEvent.click(categorySelect);
    fireEvent.click(screen.getByText('Transportation'));

    // Select initial unit type
    const unitTypeSelect = screen.getByRole('combobox', { name: /pricing unit/i });
    fireEvent.click(unitTypeSelect);
    fireEvent.click(screen.getByText('Per Trip'));

    // Wait for initial name generation
    await waitFor(() => {
      const nameInput = screen.getByLabelText(/name/i) as HTMLInputElement;
      expect(nameInput.value).toBe('Transportation – Minivan – 6 – (Per Trip)');
    });

    // Change unit type (if there were multiple options, the name would update)
    // For now, just verify the name includes the unit type
    expect(screen.getByText('(Auto-generated)')).toBeTruthy();
  });

  it('should clear auto-generated flag when user manually edits name', async () => {
    renderComponent();

    // Select category first
    const categorySelect = screen.getByRole('combobox', { name: /category/i });
    fireEvent.click(categorySelect);
    fireEvent.click(screen.getByText('Transportation'));

    // Wait for auto-generation
    await waitFor(() => {
      expect(screen.getByText('(Auto-generated)')).toBeTruthy();
    });

    // Manually edit the name
    const nameInput = screen.getByLabelText(/name/i);
    fireEvent.change(nameInput, { target: { value: 'Custom Name' } });

    // Auto-generated indicator should disappear
    await waitFor(() => {
      expect(screen.queryByText('(Auto-generated)')).toBeNull();
    });
  });

  it('should detect auto-generated names in edit mode', () => {
    const mockInitialData = {
      id: 'test-id',
      name: 'Transportation – Sedan – 4 Passengers – (Per Trip)',
      type: 'Service' as const,
      description: 'Test description',
      category_id: 'cat-1',
      unit_type_id: 'unit-1',
      custom_fields: {
        vehicle_type: 'Sedan',
        passenger_count: 4
      },
      status: 'active' as const
    };

    renderComponent({
      mode: 'edit',
      initialData: mockInitialData
    });

    // Should detect auto-generated name and show indicator
    expect(screen.getByText('(Auto-generated)')).toBeTruthy();

    // Name field should be disabled
    const nameInput = screen.getByLabelText(/name/i) as HTMLInputElement;
    expect(nameInput.disabled).toBe(true);
  });

  it('should regenerate name in edit mode when fields change', async () => {
    const mockInitialData = {
      id: 'test-id',
      name: 'Transportation – Sedan – 4 Passengers – (Per Trip)',
      type: 'Service' as const,
      description: 'Test description',
      category_id: 'cat-1',
      unit_type_id: 'unit-1',
      custom_fields: {
        vehicle_type: 'Sedan',
        passenger_count: 4
      },
      status: 'active' as const
    };

    renderComponent({
      mode: 'edit',
      initialData: mockInitialData
    });

    // Should detect auto-generated name
    expect(screen.getByText('(Auto-generated)')).toBeTruthy();

    // Change a custom field value (this would trigger regeneration in real scenario)
    // The name should update to reflect the new values
    const nameInput = screen.getByLabelText(/name/i) as HTMLInputElement;
    expect(nameInput.value).toContain('Transportation');
    expect(nameInput.value).toContain('(Per Trip)');
  });
});
