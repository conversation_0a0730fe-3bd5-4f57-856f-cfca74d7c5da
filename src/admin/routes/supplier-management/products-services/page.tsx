import { defineRouteConfig } from "@camped-ai/admin-sdk";
import { useSearchParams } from "react-router-dom";
import { useMemo, lazy, Suspense } from "react";
import { Container, Heading, Text } from "@camped-ai/ui";
import PermissionBasedSidebarHider from "../../../widgets/permission-based-sidebar-hider";
import { RoleGuard } from "../../../components/rbac/RoleGuard";
import {
  useProductsServices,
} from "../../../hooks/supplier-products-services/use-products-services";
import { useCategories } from "../../../hooks/supplier-products-services/use-categories";
import { useUnitTypes } from "../../../hooks/supplier-products-services/use-unit-types";
import { PageHeader } from "../../../../components/layout/page-header";
import { Toaster } from "@camped-ai/ui";
import { useRbac } from "../../../hooks/use-rbac";


// Dynamically import page client for better performance
const ProductsServicesPageClient = lazy(() => import("./page-client"));

const ProductsServicesPage = () => {
  const [searchParams] = useSearchParams();
  const { hasPermission } = useRbac();

  // Get current page and page size from URL params
  const currentPage = parseInt(searchParams.get("page") || "1");
  const pageSize = parseInt(searchParams.get("limit") || "25");
  const searchTerm = searchParams.get("q") || "";

  // Build filters for the query
  const filters = useMemo(() => {
    const baseFilters: any = {
      limit: pageSize,
      offset: (currentPage - 1) * pageSize,
      sort_by: searchParams.get("order")?.replace("-", "") || "updated_at",
      sort_order: searchParams.get("order")?.startsWith("-") ? "desc" : "asc",
    };

    // Add search term (case-insensitive search)
    if (searchTerm) {
      baseFilters.search = searchTerm;
    }

    // Add filters from URL params
    const typeFilter = searchParams.get("type");
    if (typeFilter && typeFilter !== "all") {
      baseFilters.type = typeFilter;
    }

    const statusFilter = searchParams.get("status");
    if (statusFilter && statusFilter !== "all") {
      baseFilters.status = statusFilter;
    }

    const categoryFilter = searchParams.get("category_id");
    if (categoryFilter && categoryFilter !== "all") {
      baseFilters.category_id = categoryFilter;
    }

    const unitTypeFilter = searchParams.get("unit_type_id");
    if (unitTypeFilter && unitTypeFilter !== "all") {
      baseFilters.unit_type_id = unitTypeFilter;
    }

    return baseFilters;
  }, [searchParams, currentPage, pageSize, searchTerm]);

  // Use the products services hook to fetch data
  const {
    data: productsServicesData,
    isLoading: productsServicesLoading,
    error: productsServicesError,
  } = useProductsServices(filters);

  // Fetch categories data
  const {
    data: categoriesData,
    isLoading: categoriesLoading,
    error: categoriesError,
  } = useCategories({
    is_active: true,
  });

  // Fetch unit types data
  const {
    data: unitTypesData,
    isLoading: unitTypesLoading,
    error: unitTypesError,
  } = useUnitTypes({
    is_active: true,
  });

  // Extract data from queries
  const productsServices = productsServicesData?.product_services || [];
  const categories = categoriesData?.categories || [];
  const unitTypes = unitTypesData?.unit_types || [];
  const totalCount = productsServicesData?.count || 0;
  const isLoading = productsServicesLoading || categoriesLoading || unitTypesLoading;

  // Handle errors
  if (productsServicesError) {
    console.error("Error fetching products/services:", productsServicesError);
  }
  if (categoriesError) {
    console.error("Error fetching categories:", categoriesError);
  }
  if (unitTypesError) {
    console.error("Error fetching unit types:", unitTypesError);
  }

  return (
    <>
      <PermissionBasedSidebarHider />
      <RoleGuard
        requirePermission="supplier_management:view"
        fallback={
          <Container>
            <div className="p-8 text-center">
              <Heading level="h1">Access Denied</Heading>
              <Text className="mt-2">
                You don't have permission to view supplier management.
              </Text>
            </div>
          </Container>
        }
      >
        <Container className="divide-y p-0">
          <PageHeader
            title="Products & Services"
            path="/supplier-management/products-services"
            hasCreate={hasPermission("supplier_management:create")}
            hasExport={hasPermission("supplier_management:view")}
            hasImport={hasPermission("supplier_management:bulk_operations")}
          />
          <Suspense
            fallback={
              <Container className="p-6">
                <div className="flex items-center justify-center py-12">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
                  <span className="ml-3 text-sm text-gray-600">Loading products & services...</span>
                </div>
              </Container>
            }
          >

            <ProductsServicesPageClient
              productsServices={productsServices}
              categories={categories}
              unitTypes={unitTypes}
              isLoading={isLoading}
              totalCount={totalCount}
              pageSize={pageSize}
            />
          </Suspense>
        </Container>
      </RoleGuard>
      <Toaster />
    </>
  );
};

export const config = defineRouteConfig({
  label: "Products & Services",
});

export default ProductsServicesPage;
