import {
  <PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Trash,
  ToggleLeft,
  ToggleRight,
} from "lucide-react";
import {
  Text,
  Button,
  Badge,
  toast,
  DropdownMenu,
  Prompt,
} from "@camped-ai/ui";

import { useMemo, useState, useCallback } from "react";
import { Link, useNavigate, useLocation } from "react-router-dom";
import { useReactTable, getCoreRowModel, createColumnHelper, ColumnDef } from "@tanstack/react-table";
import { useRbac } from "../../../hooks/use-rbac";
import { DataTable } from "../../../../components/table/data-table";
import type { Filter } from "../../../../components/table/data-table";
import type { ProductService } from "../../../hooks/supplier-products-services/use-products-services";
import { CategoryIcon } from "../../../utils/category-icon-utils";
import {
  useUpdateProductService,
  useDeleteProductService
} from "../../../hooks/supplier-products-services/use-products-services";
import { BulkStatusChangeModal } from "../../../components/supplier-management/bulk-status-change-modal";

// Status badge colors
const statusColors = {
  active: "green",
  inactive: "grey",
} as const;



interface ProductsServicesPageClientProps {
  productsServices: ProductService[];
  categories: any[];
  unitTypes: any[];
  isLoading: boolean;
  totalCount: number;
  pageSize: number;
}

const ProductsServicesPageClient: React.FC<ProductsServicesPageClientProps> = ({
  productsServices,
  categories,
  unitTypes,
  isLoading,
  totalCount,
  pageSize,
}) => {
  const navigate = useNavigate();
  const location = useLocation();

  const { hasPermission } = useRbac();
  const updateProductService = useUpdateProductService();
  const deleteProductService = useDeleteProductService();

  // Row selection state for multi-select functionality
  const [rowSelection, setRowSelection] = useState({});

  // Modal state for bulk status change
  const [isBulkStatusModalOpen, setIsBulkStatusModalOpen] = useState(false);
  const [currentSelection, setCurrentSelection] = useState<Record<string, boolean>>({});

  // Delete modal state
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [itemToDelete, setItemToDelete] = useState<ProductService | null>(null);

  // Handle export of selected rows
  const handleExportSelected = useCallback(async (selection: Record<string, boolean>) => {
    const selectedIds = Object.keys(selection);
    if (selectedIds.length === 0) {
      toast.error("No rows selected for export");
      return;
    }

    try {
      // Build export URL with selected IDs
      const params = new URLSearchParams();
      params.append("format", "excel");
      selectedIds.forEach(id => params.append("ids", id));

      // Open export URL in new tab
      const exportUrl = `/admin/supplier-management/products-services/export?${params.toString()}`;
      window.open(exportUrl, '_blank');

      toast.success(`Exporting ${selectedIds.length} selected products/services`);
    } catch (error) {
      console.error("Export error:", error);
      toast.error("Failed to export selected products/services");
    }
  }, []);

  // Handle bulk status change - open modal
  const handleBulkStatusChange = useCallback(async (selection: Record<string, boolean>) => {
    const selectedIds = Object.keys(selection);

    if (selectedIds.length === 0) {
      toast.error("No rows selected for status change");
      return;
    }

    // Store the current selection for the modal
    setCurrentSelection(selection);
    // Open the modal instead of using prompt
    setIsBulkStatusModalOpen(true);
  }, []);

  // Handle the actual status change from modal
  const handleStatusChangeSubmit = async (ids: string[], newStatus: string) => {
    try {
      // Call bulk update API
      const response = await fetch("/admin/supplier-management/products-services/bulk-status", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        credentials: "include",
        body: JSON.stringify({
          ids,
          status: newStatus,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || "Failed to update status");
      }

      const result = await response.json();

      // Refresh the page data
      window.location.reload();
    } catch (error) {
      console.error("Bulk status change error:", error);
      throw error; // Re-throw to let modal handle the error display
    }
  };

  // Handle create supplier offering from selected product
  const handleCreateSupplierOffering = useCallback(async (selection: Record<string, boolean>) => {
    const selectedIds = Object.keys(selection);
    if (selectedIds.length === 0) {
      toast.error("No product selected");
      return;
    }
    if (selectedIds.length > 1) {
      toast.error("Please select only one product to create a supplier offering");
      return;
    }

    const selectedProductId = selectedIds[0];
    const selectedProduct = productsServices.find(p => p.id === selectedProductId);

    if (!selectedProduct) {
      toast.error("Selected product not found");
      return;
    }

    // Navigate to create supplier offering page with pre-populated data
    const params = new URLSearchParams();
    params.append("product_service_id", selectedProductId);
    if (selectedProduct.category_id) {
      params.append("category_id", selectedProduct.category_id);
    }

    navigate(`/supplier-management/supplier-offerings/create?${params.toString()}`);
  }, [productsServices, navigate]);

  // Define bulk commands for selected rows - conditional based on selection count
  const commands = useMemo(() => {
    const selectedCount = Object.keys(rowSelection || {}).length;
    const commandList = [];

    // Always show Export Selected when items are selected
    if (selectedCount > 0) {
      commandList.push({
        label: "Export Selected",
        shortcut: "e",
        action: handleExportSelected,
      });
    }

    // Only show Create Supplier Offering for single selection
    if (selectedCount === 1) {
      commandList.push({
        label: "Create Supplier Offering",
        shortcut: "c",
        action: handleCreateSupplierOffering,
      });
    }

    // Show Change Status for any selection (1+ items)
    if (selectedCount > 0) {
      commandList.push({
        label: "Change Status",
        shortcut: "s",
        action: handleBulkStatusChange,
      });
    }

    return commandList;
  }, [rowSelection, handleExportSelected, handleCreateSupplierOffering, handleBulkStatusChange]);

  // Column helper for type safety
  const columnHelper = createColumnHelper<ProductService>();

  console.log("productsServicesClient", productsServices);

  // Helper functions
  const getStatusBadgeVariant = (status: string) => {
    return statusColors[status as keyof typeof statusColors] || "grey";
  };





  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    });
  };

  const resolveProductName = (item: ProductService) => {
    if (item.name) return item.name;

    // Build name from related entities
    const parts = [];
    if (item.category?.name) parts.push(item.category.name);

    return parts.length > 0 ? parts.join(" - ") : "Unnamed Product/Service";
  };

  const handleCopyId = (id: string) => {
    navigator.clipboard.writeText(id);
    toast.success("ID copied to clipboard");
  };

  const handleDismissFlag = async (item: ProductService) => {
    try {
      await updateProductService.mutateAsync({
        id: item.id,
        data: { price_flag_active: false },
      });
      toast.success("Price flag dismissed successfully");
    } catch (error) {
      toast.error("Failed to dismiss price flag");
    }
  };

  const handleUpdatePrice = async (item: ProductService) => {
    try {
      await updateProductService.mutateAsync({
        id: item.id,
        data: {
          base_cost: item.highest_price || undefined,
          price_flag_active: false
        },
      });
      toast.success("Price updated successfully");
    } catch (error) {
      toast.error("Failed to update price");
    }
  };

  const handleDeleteClick = (item: ProductService) => {
    setItemToDelete(item);
    setIsDeleteModalOpen(true);
  };

  // Handle status toggle
  const handleStatusToggle = useCallback(async (item: ProductService) => {
    const newStatus = item.status === "active" ? "inactive" : "active";

    try {
      await updateProductService.mutateAsync({
        id: item.id,
        data: {
          status: newStatus,
        },
      });

      toast.success("Success", {
        description: `Product/Service ${newStatus === "active" ? "activated" : "deactivated"} successfully`,
      });
    } catch (error) {
      console.error("Status toggle error:", error);
      toast.error("Error", {
        description: `Failed to ${newStatus === "active" ? "activate" : "deactivate"} product/service`,
      });
    }
  }, [updateProductService]);

  const handleDelete = async () => {
    if (!itemToDelete) return;

    // Dismiss any existing toasts and show loading toast
    toast.dismiss();
    const loadingToast = toast.loading("Deleting product/service...");

    try {
      await deleteProductService.mutateAsync(itemToDelete.id);
      toast.dismiss(loadingToast);
      toast.success("Product/Service deleted successfully");
      setIsDeleteModalOpen(false);
      setItemToDelete(null);
    } catch (error) {
      toast.dismiss(loadingToast);
      toast.error("Failed to delete product/service");
      setIsDeleteModalOpen(false);
      setItemToDelete(null);
    }
  };

  const handleCancelDelete = () => {
    setIsDeleteModalOpen(false);
    setItemToDelete(null);
  };

  // Get selected category from URL params
  const urlSearchParams = new URLSearchParams(location.search);
  const selectedCategoryId = urlSearchParams.get("category_id");

  // Find the selected category to get its dynamic field schema
  const selectedCategory = useMemo(() => {
    if (!selectedCategoryId || selectedCategoryId === "all") return null;
    return categories.find(cat => cat.id === selectedCategoryId);
  }, [categories, selectedCategoryId]);
  console.log("Selected_category:", selectedCategory);

  // Get custom field columns based on selected category
  const customFieldColumns = useMemo(() => {
    if (!selectedCategory?.dynamic_field_schema) return [];

    // Filter dynamic fields to only include fields marked for product/service usage
    const filteredFields = selectedCategory.dynamic_field_schema.filter((field: any) => {
      const isSupplierContext = !field.field_context || field.field_context === "supplier";
      const isUsedInProductServices = field.used_in_product_services === true;
      return isSupplierContext && isUsedInProductServices;
    });

    return filteredFields.map((field: any) =>
      columnHelper.display({
        id: `custom_field_${field.key}`,
        header: () => (
          <div className="flex h-full items-center">
            <span className="truncate">{field.label}</span>
          </div>
        ),
        cell: ({ row }) => {
          const item = row.original;
          const value = item.custom_fields?.[field.key];

          if (!value) return (
            <div className="flex h-full w-full items-center overflow-hidden">
              <Text className="text-sm text-ui-fg-subtle">—</Text>
            </div>
          );

          // Check for API-resolved names with standard naming pattern (field_key_names)
          const namesField = `${field.key}_names`;
          const resolvedNames = item.custom_fields?.[namesField];
          console.log("resolvedNames", resolvedNames);

          // Helper function to format field value with resolved names
          const formatFieldValue = (value: any, field: any) => {
            // Use resolved names if available
            if (
              resolvedNames &&
              Array.isArray(resolvedNames) &&
              resolvedNames.length > 0
            ) {
              // Filter out any null/undefined/empty values
              const validNames = resolvedNames.filter(
                (name) => name && typeof name === "string" && name.trim()
              );
              if (validNames.length > 0) {
                return validNames;
              }
            }

            // Handle basic field types without external dependencies
            if (field.type === "boolean") {
              return value ? "Yes" : "No";
            }

            if (field.type === "date") {
              try {
                return new Date(value).toLocaleDateString();
              } catch {
                return value.toString();
              }
            }

            if (field.type === "dropdown" || field.type === "select") {
              if (field.options && Array.isArray(field.options)) {
                const option = field.options.find(
                  (opt: any) => (typeof opt === "object" ? opt.value : opt) === value
                );
                if (option) {
                  return typeof option === "object" ? option.label : option;
                }
              }
              return value.toString();
            }

            // Handle arrays (multi-select fields)
            if (Array.isArray(value)) {
              if (value.length === 0) {
                return "—";
              }
              return value;
            }

            // Default: return as string
            return value.toString();
          };

          const formattedValue = formatFieldValue(value, field);

          console.log("formattedValue", formattedValue);
          console.log("field",field)

          // Handle different field types for display
          switch (field.type) {
            case "dropdown":
            case "multi-select":
              if (Array.isArray(formattedValue)) {
                return (
                  <div className="flex h-full w-full items-center overflow-hidden">
                    <div className="flex flex-wrap gap-1">
                      {formattedValue.map((val: string, idx: number) => (
                        <Badge key={idx} color="blue" size="xsmall">
                          {val}
                        </Badge>
                      ))}
                    </div>
                  </div>
                );
              }
              return (
                <div className="flex h-full w-full items-center overflow-hidden">
                  <Badge color="blue" size="xsmall">
                    {formattedValue}
                  </Badge>
                </div>
              );
            case "boolean":
              return (
                <div className="flex h-full w-full items-center overflow-hidden">
                  <Badge color={formattedValue === "Yes" ? "green" : "red"} size="xsmall">
                    {formattedValue}
                  </Badge>
                </div>
              );
            case "date":
              return (
                <div className="flex h-full w-full items-center overflow-hidden">
                  <Text className="text-sm truncate">
                    {formattedValue}
                  </Text>
                </div>
              );
            case "number":
              return (
                <div className="flex h-full w-full items-center overflow-hidden">
                  <Text className="text-sm font-medium truncate">
                    {typeof value === 'number' ? value.toLocaleString() : formattedValue}
                  </Text>
                </div>
              );
            default:
              return (
                <div className="flex h-full w-full items-center overflow-hidden">
                  <Text className="text-sm truncate">
                    {String(formattedValue)}
                  </Text>
                </div>
              );
          }
        },
      })
    );
  }, [selectedCategory, columnHelper]);

  // Define columns
  const columns = useMemo<ColumnDef<ProductService, any>[]>(() => [
    // Select column for multi-select functionality
    columnHelper.display({
      id: "select",
      header: ({ table }) => (
        <input
          type="checkbox"
          checked={table.getIsAllPageRowsSelected()}
          onChange={(e) => {
            try {
              table.getToggleAllPageRowsSelectedHandler()(e);
            } catch (error) {
              console.error("Error in select all handler:", error);
            }
          }}
          className="rounded border-ui-border-base"
        />
      ),
      cell: ({ row }) => (
        <input
          type="checkbox"
          checked={row.getIsSelected()}
          onChange={(e) => {
            try {
              row.getToggleSelectedHandler()(e);
            } catch (error) {
              console.error("Error in row select handler:", error);
            }
          }}
          className="rounded border-ui-border-base"
        />
      ),
    }),
    columnHelper.display({
      id: "name",
      header: () => (
        <div className="flex h-full w-full items-center">
          <span>Product/Service</span>
        </div>
      ),
      cell: ({ row }) => {
        const item = row.original;
        return (
          <div className="flex h-full w-full items-center gap-x-3 min-w-[400px] w-full pr-4 overflow-visible" style={{ minWidth: '400px', width: '100%', overflow: 'visible' }}>
            <div className="flex h-8 w-8 items-center justify-center rounded bg-ui-bg-subtle flex-shrink-0">
              <CategoryIcon category={item.category} className="h-4 w-4 text-ui-fg-subtle" />
            </div>
            <div className="flex-1 min-w-0 overflow-visible">
              <Text
                className="txt-compact-medium-plus break-words leading-relaxed whitespace-normal text-wrap overflow-visible"
                weight="plus"
                title={resolveProductName(item)}
                style={{
                  wordBreak: 'break-word',
                  whiteSpace: 'normal',
                  overflow: 'visible',
                  display: 'block',
                  width: '100%'
                }}
              >
                {resolveProductName(item)}
              </Text>
            </div>
          </div>
        );
      },
      size: 450, // Further increased column width
      minSize: 400,
      maxSize: 700,
    }),

    columnHelper.display({
      id: "category",
      header: () => (
        <div className="txt-compact-small-plus h-12 py-0 pl-0 pr-6 text-left bg-ui-bg-subtle sticky after:absolute after:inset-y-0 after:right-0 after:h-full after:w-px after:bg-transparent after:content-[''] left-[68px] flex items-center">
          <span>Category</span>
        </div>
      ),
      cell: ({ row }) => {
        const categoryName = row.original.category?.name || "—";
        return (
          <div className="h-12 py-0 pl-0 pr-6 !pl-0 !pr-0 bg-ui-bg-base group-data-[selected=true]/row:bg-ui-bg-highlight group-data-[selected=true]/row:group-hover/row:bg-ui-bg-highlight-hover group-hover/row:bg-ui-bg-base-hover transition-fg group-has-[[data-row-link]:focus-visible]:bg-ui-bg-base-hover sticky after:absolute after:inset-y-0 after:right-0 after:h-full after:w-px after:bg-transparent after:content-[''] left-[68px] flex items-center">
            <Text
              className="txt-compact-medium-plus break-words leading-relaxed whitespace-normal text-wrap overflow-visible"
              weight="plus"
              title={categoryName !== "—" ? categoryName : undefined}
              style={{
                wordBreak: 'break-word',
                whiteSpace: 'normal',
                overflow: 'visible',
                display: 'block',
                width: '100%'
              }}
            >
              {categoryName}
            </Text>
          </div>
        );
      },
      size: 280, // Further increased column width
      minSize: 250,
      maxSize: 400,
    }),

    columnHelper.display({
      id: "unit_type",
      header: () => (
        <div className="flex h-full w-full items-center">
          <span className="truncate">Unit Type</span>
        </div>
      ),
      cell: ({ row }) => (
        <div className="flex h-full w-full items-center overflow-hidden">
          <Text className="text-sm truncate">
            {row.original.unit_type?.name || "—"}
          </Text>
        </div>
      ),
    }),
    // Add custom field columns dynamically based on selected category
    ...customFieldColumns,
     columnHelper.accessor("status", {
      header: () => (
        <div className="flex h-full w-full items-center">
          <span className="truncate">Status</span>
        </div>
      ),
      cell: ({ getValue }) => (
        <div className="flex h-full w-full items-center overflow-hidden">
          <Badge color={getStatusBadgeVariant(getValue())} size="xsmall">
            {getValue()?.charAt(0).toUpperCase() + getValue()?.slice(1)}
          </Badge>
        </div>
      ),
    }),
    columnHelper.accessor("updated_at", {
      header: () => (
        <div className="flex h-full w-full items-center">
          <span className="truncate">Last Updated</span>
        </div>
      ),
      cell: ({ getValue }) => (
        <div className="flex h-full w-full items-center overflow-hidden">
          <Text className="text-sm text-ui-fg-subtle truncate">
            {formatDate(getValue())}
          </Text>
        </div>
      ),
    }),
    columnHelper.display({
      id: "actions",
      header: "",
      cell: ({ row }) => {
        const item = row.original;
        return (
          <DropdownMenu>
            <DropdownMenu.Trigger asChild>
              <Button variant="transparent" size="small">
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenu.Trigger>
            <DropdownMenu.Content>
              <DropdownMenu.Item
                onClick={() => navigate(`/supplier-management/products-services/${item.id}`)}
              >
                <Eye className="h-4 w-4 mr-2" />
                View
              </DropdownMenu.Item>
              {hasPermission("supplier_management:edit") && (
                <DropdownMenu.Item asChild>
                  <Link to={`/supplier-management/products-services/${item.id}/edit`}>
                    <Edit className="h-4 w-4 mr-2" />
                    Edit
                  </Link>
                </DropdownMenu.Item>
              )}
              {hasPermission("supplier_management:edit") && (
                <DropdownMenu.Item
                  onClick={(e) => {
                    e.stopPropagation();
                    handleStatusToggle(item);
                  }}
                  disabled={updateProductService.isPending}
                >
                  {item.status === "active" ? (
                    <>
                      <ToggleLeft className="h-4 w-4 mr-2" />
                      Deactivate
                    </>
                  ) : (
                    <>
                      <ToggleRight className="h-4 w-4 mr-2" />
                      Activate
                    </>
                  )}
                </DropdownMenu.Item>
              )}
              {hasPermission("supplier_management:delete") && (
                <DropdownMenu.Item
                  onClick={(e) => {
                    e.stopPropagation();
                    handleDeleteClick(item);
                  }}
                  className="text-ui-fg-error"
                >
                  <Trash className="h-4 w-4 mr-2" />
                  Delete
                </DropdownMenu.Item>
              )}
            </DropdownMenu.Content>
          </DropdownMenu>
        );
      },
    }),
  ], [columnHelper, navigate, hasPermission, updateProductService.isPending, deleteProductService.isPending, customFieldColumns]);

  // Define filters
  const filters: Filter[] = useMemo(() => [

    {
      key: "status",
      label: "Status",
      type: "select",
      options: [
        { label: "All Statuses", value: "all" },
        { label: "Active", value: "active" },
        { label: "Inactive", value: "inactive" },
      ],
    },
    {
      key: "category_id",
      label: "Category",
      type: "select",
      searchable: true,
      options: [
        { label: "All Categories", value: "all" },
        ...categories.map((category) => ({
          label: category.name,
          value: category.id,
        })),
      ],
    },
    {
      key: "unit_type_id",
      label: "Unit Type",
      type: "select",
      searchable: true,
      options: [
        { label: "All Unit Types", value: "all" },
        ...unitTypes.map((unitType) => ({
          label: unitType.name,
          value: unitType.id,
        })),
      ],
    },
  ], [categories, unitTypes]);

  // Define order by options
  const orderBy = useMemo(() => {
    const baseOrderBy = [
      { key: "name" as keyof ProductService, label: "Name" },

      { key: "base_cost" as keyof ProductService, label: "Base Cost" },
      { key: "status" as keyof ProductService, label: "Status" },
      { key: "updated_at" as keyof ProductService, label: "Last Updated" },
    ];

    // Add custom field sorting options if a category is selected
    if (selectedCategory?.dynamic_field_schema) {
      const customFieldOrderBy = selectedCategory.dynamic_field_schema.map((field: any) => ({
        key: `custom_fields.${field.key}` as keyof ProductService,
        label: field.label,
      }));
      return [...baseOrderBy, ...customFieldOrderBy];
    }

    return baseOrderBy;
  }, [selectedCategory]);

  // Get current page from URL
  const currentPage = parseInt(urlSearchParams.get("page") || "1");

  // Create table instance
  const table = useReactTable({
    data: productsServices || [],
    columns,
    getCoreRowModel: getCoreRowModel(),
    manualPagination: true,
    pageCount: Math.ceil((totalCount || 0) / (pageSize || 25)),
    enableRowSelection: true,
    getRowId: (row) => row?.id || '',
    columnResizeMode: 'onChange',
    enableColumnResizing: true,
    defaultColumn: {
      minSize: 200,
      maxSize: 1000,
    },
    state: {
      pagination: {
        pageIndex: Math.max(0, currentPage - 1),
        pageSize: pageSize || 25,
      },
      rowSelection: rowSelection || {},
    },
    onRowSelectionChange: setRowSelection,
  });

  return (
    <>
      <div className="overflow-x-auto min-w-0">
        <div className="min-w-[1200px]">
          <DataTable
            table={table}
            columns={columns}
            pageSize={pageSize}
            count={totalCount}
            isLoading={isLoading}
            filters={filters}
            orderBy={orderBy}
            search="autofocus"
            pagination
            navigateTo={(row) => `/supplier-management/products-services/${row.original.id}`}
            commands={commands}
            queryObject={Object.fromEntries(urlSearchParams)}
            noRecords={{
              title: "No products or services found",
              message: "Get started by creating your first product or service",
            }}
          />
        </div>
      </div>

      {/* Bulk Status Change Modal */}
      {isBulkStatusModalOpen && (
        <BulkStatusChangeModal
          open={isBulkStatusModalOpen}
          onOpenChange={setIsBulkStatusModalOpen}
          selectedIds={Object.keys(currentSelection)}
          selectedItems={Object.keys(currentSelection).map(id => {
            try {
              const item = productsServices.find(ps => ps.id === id);
              return {
                id,
                name: item ? resolveProductName(item) : `Product/Service ${id}`,
                status: item?.status || 'unknown'
              };
            } catch (error) {
              console.error("Error preparing modal item:", error);
              return {
                id,
                name: `Product/Service ${id}`,
                status: 'unknown'
              };
            }
          })}
          onStatusChange={handleStatusChangeSubmit}
        />
      )}

      {/* Delete Confirmation Modal */}
      <Prompt open={isDeleteModalOpen} onOpenChange={setIsDeleteModalOpen}>
        <Prompt.Content>
          <Prompt.Header>
            <Prompt.Title>Delete Product/Service</Prompt.Title>
            <Prompt.Description>
              Are you sure you want to delete "{itemToDelete ? resolveProductName(itemToDelete) : ''}"? This
              action cannot be undone.
            </Prompt.Description>
          </Prompt.Header>
          <Prompt.Footer>
            <Prompt.Cancel onClick={handleCancelDelete}>
              Cancel
            </Prompt.Cancel>
            <Prompt.Action onClick={handleDelete}>Delete</Prompt.Action>
          </Prompt.Footer>
        </Prompt.Content>
      </Prompt>
    </>
  );
};

export default ProductsServicesPageClient;
