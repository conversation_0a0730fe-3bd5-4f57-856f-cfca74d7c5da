import { defineRouteConfig } from "@camped-ai/admin-sdk";
import { ArrowLeft } from "@camped-ai/icons";
import { Edit, Package, Trash, Copy } from "lucide-react";
import {
  Container,
  Heading,
  Text,
  Button,
  Toaster,
  Prompt,
  toast,
} from "@camped-ai/ui";
import { useNavigate, useParams } from "react-router-dom";
import { useState, lazy, Suspense } from "react";
import PermissionBasedSidebarHider from "../../../../widgets/permission-based-sidebar-hider";
import { useRbac } from "../../../../hooks/use-rbac";
import { useOnRequestItem } from "../../../../hooks/supplier-management/use-on-request-items";
import { useAdminCurrencies } from "../../../../hooks/use-admin-currencies";
import { formatCurrencyAmount, fromSmallestUnit } from "../../../../utils/currency-utils";

// Dynamically import page client for better performance
const BookingAddOnDetailsLayout = lazy(() => import("./page-client"));



const OnRequestItemDetailPage = () => {
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();
  const { hasPermission } = useRbac();
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);

  // Use real API hooks
  const { data: itemResponse, isLoading, error } = useOnRequestItem(id!);
  const item = itemResponse?.concierge_order_item;
  const { defaultCurrency } = useAdminCurrencies();

  // Helper functions
  const formatCurrency = (amount: number) => {
    const currency = defaultCurrency || { currency_code: "GBP", symbol: "£", decimal_digits: 2 };

    // Convert from smallest unit (cents) to display unit
    const displayAmount = fromSmallestUnit(amount, currency);

    return formatCurrencyAmount(displayAmount, currency, {
      showSymbol: true,
      showCode: false,
      symbolPosition: "before",
    });
  };

  const handleCopyItemId = () => {
    if (item?.id) {
      navigator.clipboard.writeText(item.id);
      toast.success("Item ID copied to clipboard");
    }
  };

  // Handle edit navigation
  const handleEdit = () => {
    if (item) {
      // For now, redirect to the requests page where edit modal is available
      // TODO: Implement inline editing or dedicated edit page
      navigate(`/supplier-management/requests`);
    }
  };

  // Handle delete (deactivate)
  const handleDelete = async () => {
    if (!item) return;

    try {
      // Call the DELETE API endpoint to deactivate the item
      const response = await fetch(
        `/admin/concierge-management/orders/${item.concierge_order_id}/items/${item.id}`,
        {
          method: "DELETE",
          credentials: "include",
        }
      );

      if (!response.ok) {
        throw new Error(`Failed to delete item: ${response.status} ${response.statusText}`);
      }

      toast.success("Item deactivated successfully");
      navigate("/supplier-management/requests");
    } catch (error) {
      console.error("Failed to delete on-request item:", error);
      toast.error("Failed to delete item");
    }
  };

  const handleCancelDelete = () => {
    setIsDeleteModalOpen(false);
  };

  // Handle back navigation
  const handleBack = () => {
    navigate("/supplier-management/requests");
  };

  if (isLoading) {
    return (
      <>
        <PermissionBasedSidebarHider />
        <Container>
          <div className="p-8 text-center">
            <Text>Loading on-request item details...</Text>
          </div>
        </Container>
      </>
    );
  }

  if (error || !item) {
    return (
      <>
        <PermissionBasedSidebarHider />
        <Container>
          <div className="p-8 text-center">
            <Heading level="h1">On-Request Item Not Found</Heading>
            <Text className="mt-2">
              {error?.message || "The requested item could not be found."}
            </Text>
            <Button className="mt-4" onClick={handleBack}>
              Back to On Requests
            </Button>
          </div>
        </Container>
      </>
    );
  }

  return (
    <>
      <PermissionBasedSidebarHider />
      
      {/* Header */}
      <Container className="divide-y p-0">
        <div className="flex items-center justify-between px-6 py-4">
          <div className="flex items-center gap-x-4">
            <Button
              variant="transparent"
              size="small"
              onClick={handleBack}
              className="text-ui-fg-muted hover:text-ui-fg-subtle"
            >
              <ArrowLeft className="h-4 w-4" />
            </Button>
            <div>
              <div className="flex items-center gap-x-2">
                <Package className="h-5 w-5 text-ui-fg-muted" />
                <Heading level="h1">{item.title}</Heading>
              </div>
              <div className="flex items-center gap-x-4 mt-1">
                <Text className="text-ui-fg-muted">
                  Concierge Order: {item.concierge_order_id}
                </Text>
                <Text className="text-ui-fg-muted">
                  {formatCurrency(item.unit_price * item.quantity)}
                </Text>
                <Text className="text-ui-fg-muted">
                  Qty: {item.quantity}
                </Text>
              </div>
            </div>
          </div>
          
          <div className="flex items-center gap-x-2">
            <Button
              variant="transparent"
              size="small"
              onClick={handleCopyItemId}
            >
              <Copy className="h-4 w-4" />
            </Button>
            
            {hasPermission("supplier-orders:edit") && (
              <Button
                variant="secondary"
                size="small"
                onClick={handleEdit}
              >
                <Edit className="h-4 w-4 mr-2" />
                Edit
              </Button>
            )}
            
            {hasPermission("supplier-orders:delete") && (
              <Button
                variant="danger"
                size="small"
                onClick={() => setIsDeleteModalOpen(true)}
              >
                <Trash className="h-4 w-4 mr-2" />
                Delete
              </Button>
            )}
          </div>
        </div>
      </Container>

      <Suspense
        fallback={
          <Container className="p-6">
            <div className="flex items-center justify-center py-12">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
              <span className="ml-3 text-sm text-gray-600">Loading request details...</span>
            </div>
          </Container>
        }
      >
        <BookingAddOnDetailsLayout item={item} />
      </Suspense>

      {/* Delete Confirmation Modal */}
      <Prompt open={isDeleteModalOpen} onOpenChange={setIsDeleteModalOpen}>
        <Prompt.Content>
          <Prompt.Header>
            <Prompt.Title>Deactivate On Request Item</Prompt.Title>
            <Prompt.Description>
              Are you sure you want to deactivate item "{item.title}"? This
              will mark the item as inactive.
            </Prompt.Description>
          </Prompt.Header>
          <Prompt.Footer>
            <Prompt.Cancel onClick={handleCancelDelete}>
              Cancel
            </Prompt.Cancel>
            <Prompt.Action onClick={handleDelete}>Deactivate</Prompt.Action>
          </Prompt.Footer>
        </Prompt.Content>
      </Prompt>

      <Toaster />
    </>
  );
};

export const config = defineRouteConfig({
  label: "On Request Details",
});

export default OnRequestItemDetailPage;
