import { useNavigate, useSearchParams } from "react-router-dom";
import { useState, useEffect, ComponentType } from "react";
import { Container, Heading, Text, Button, Toaster } from "@camped-ai/ui";
import { Building2, CircleDollarSignIcon, Tags } from "lucide-react";
import { defineRouteConfig } from "@camped-ai/admin-sdk";
import HotelPricingManager from "../../../components/hotel/pricing/hotel-pricing-manager-new";
import HotelSelector from "../../../components/hotel/hotel-selector";
import { useRbac } from "../../../hooks/use-rbac";
import PermissionBasedSidebarHider from "../../../widgets/permission-based-sidebar-hider";
import { useHotels } from "../../../hooks/supplier-products-services/use-hotels";
import { RoleGuard } from "../../../components/rbac/RoleGuard";

const CircleDollarSignIconComponent: ComponentType = (props: any) => (
  <CircleDollarSignIcon {...props} className="h-[15px] w-4" />
);

// Interfaces removed - HotelPricingManager will handle data fetching

const HotelPricingPage = () => {
  const navigate = useNavigate();
  const [searchParams, setSearchParams] = useSearchParams();
  const { hasPermission, loading: rbacLoading } = useRbac();
  const [selectedHotelId, setSelectedHotelId] = useState<string | null>(null);

  // Get hotels list for default selection
  const { data: hotelsData, isLoading: isLoadingHotels } = useHotels({
    limit: 100,
    is_active: true,
  });

  // Initialize hotel selection from URL query string
  useEffect(() => {
    const hotelIdFromUrl = searchParams.get('hotel');
    if (hotelIdFromUrl && hotelsData?.hotels) {
      // Verify the hotel ID exists in the available hotels
      const hotelExists = hotelsData.hotels.some((hotel: any) => hotel.id === hotelIdFromUrl);
      if (hotelExists) {
        setSelectedHotelId(hotelIdFromUrl);
      } else {
        // Remove invalid hotel ID from URL
        setSearchParams(prev => {
          const newParams = new URLSearchParams(prev);
          newParams.delete('hotel');
          return newParams;
        });
      }
    }
  }, [hotelsData, searchParams, setSearchParams]);

  // Set default hotel selection when hotels are loaded (only if no URL param)
  useEffect(() => {
    if (
      hotelsData?.hotels &&
      hotelsData.hotels.length > 0 &&
      !selectedHotelId &&
      !searchParams.get('hotel')
    ) {
      const firstHotel = hotelsData.hotels[0];
      // setSelectedHotelId(firstHotel.id);
    }
  }, [hotelsData, selectedHotelId, searchParams]);

  // No need to fetch pricing data here - HotelPricingManager will handle it

  // Handle hotel selection change
  const handleHotelChange = (hotelId: string) => {
    setSelectedHotelId(hotelId);

    // Update URL query string to persist selection
    setSearchParams(prev => {
      const newParams = new URLSearchParams(prev);
      if (hotelId) {
        newParams.set('hotel', hotelId);
      } else {
        newParams.delete('hotel');
      }
      return newParams;
    });
  };

  // Show loading state while hotels are being fetched
  if (isLoadingHotels) {
    return (
      <div className="h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
      </div>
    );
  }

  // Show error state if no hotels available
  if (!hotelsData?.hotels || hotelsData.hotels.length === 0) {
    return (
      <Container className="py-8">
        <div className="text-center py-12 bg-muted rounded-lg">
          <Building2 className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
          <Text className="text-muted-foreground mb-4">No hotels found</Text>
          <Button
            variant="primary"
            size="small"
            onClick={() => navigate("/hotel-management/hotels")}
          >
            Back to Hotels
          </Button>
        </div>
      </Container>
    );
  }

  const canEdit = !rbacLoading && hasPermission("supplier_cost:edit");
  const canCreate = !rbacLoading && hasPermission("supplier_cost:create");
  const canDelete = !rbacLoading && hasPermission("supplier_cost:delete");

  return (
    <>
      <PermissionBasedSidebarHider />
      <RoleGuard
        requirePermission="supplier_cost:view"
        fallback={
          <Container className="py-8">
            <div className="text-center py-12 bg-muted rounded-lg">
              <Tags className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <Heading level="h1">Access Denied</Heading>
              <Text className="text-muted-foreground mb-4">
                You don't have permission to view supplier cost management.
                <br />
                Required permission: supplier_cost:view
              </Text>
              <Button
                variant="primary"
                size="small"
                onClick={() => navigate("/hotel-management/hotels")}
              >
                Back to Hotels
              </Button>
            </div>
          </Container>
        }
      >
        <Toaster />
        <Container className="py-6">
          {/* Header with hotel selector */}
          <div className="mb-6">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center space-x-4">
                <Heading level="h1" className="text-2xl font-bold">
                  Hotel Pricing Management
                </Heading>
              </div>
            </div>

            {/* Hotel Selector */}
            <div className="max-w-md">
              <HotelSelector
                selectedHotelId={selectedHotelId}
                onHotelChange={handleHotelChange}
                label="Select Hotel"
                placeholder="Choose a hotel to manage pricing..."
                showActiveOnly={true}
              />
            </div>
          </div>

          {/* Show loading state while hotels are being fetched */}
          {isLoadingHotels && (
            <div className="h-64 flex items-center justify-center">
              <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
            </div>
          )}

          {/* Show error state if no hotels available */}
          {!isLoadingHotels && (!hotelsData?.hotels || hotelsData.hotels.length === 0) && (
            <div className="text-center py-12 bg-muted rounded-lg">
              <Building2 className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <Text className="text-muted-foreground mb-4">No hotels found</Text>
              <Button
                variant="primary"
                size="small"
                onClick={() => navigate("/hotel-management/hotels")}
              >
                Back to Hotels
              </Button>
            </div>
          )}

          {/* Pricing Manager */}
          {!isLoadingHotels && selectedHotelId && (
            <HotelPricingManager
              hotelId={selectedHotelId}
              onBack={() => navigate("/hotel-management/hotels")}
              canEdit={canEdit}
              canCreate={canCreate}
              canDelete={canDelete}
              hideBackButton={true}
            />
          )}

          {/* No Hotel Selected State */}
          {!isLoadingHotels && !selectedHotelId && hotelsData?.hotels && hotelsData.hotels.length > 0 && (
            <div className="text-center py-12 bg-muted rounded-lg">
              <Building2 className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <Text className="text-muted-foreground mb-4">
                Please select a hotel to view and manage pricing
              </Text>
            </div>
          )}
        </Container>
      </RoleGuard>
    </>
  );
};

export const config = defineRouteConfig({
  label: "Hotel Pricing",
  icon: CircleDollarSignIconComponent,
});

export default HotelPricingPage;
