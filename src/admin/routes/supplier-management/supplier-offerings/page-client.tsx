import {
  <PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Trash,
  Copy,
} from "lucide-react";
import {
  Container,
  <PERSON>ing,
  Text,
  Button,
  Badge,
  Toaster,
  toast,
  DropdownMenu,
  Prompt,
} from "@camped-ai/ui";
import { useMemo, useState, useCallback } from "react";
import { Link, useNavigate, useLocation } from "react-router-dom";
import { useReactTable, getCoreRowModel, createColumnHelper, ColumnDef } from "@tanstack/react-table";
import { useRbac } from "../../../hooks/use-rbac";
import { DataTable } from "../../../../components/table/data-table";
import type { Filter } from "../../../../components/table/data-table";
import { useDeleteSupplierOffering, useDuplicateSupplierOffering } from "../../../hooks/supplier-products-services/use-supplier-offerings";
import { CategoryIcon } from "../../../utils/category-icon-utils";
import { BulkStatusChangeModal } from "../../../components/supplier-management/bulk-status-change-modal";
import { useAdminCurrencies } from "../../../hooks/use-admin-currencies";
import { formatCurrencyAmount } from "../../../utils/currency-utils";

// Types
interface SupplierOffering {
  id: string;
  supplier_id: string;
  product_service_id: string;
  commission?: string | number;
  gross_price?: string | number;
  selling_price?: string | number;
  selling_price_selling_currency?: string | number;
  selling_currency?: string;
  net_price?: string | number;
  currency?: string;
  active_from?: string;
  active_to?: string;
  status: "active" | "inactive";
  created_at: string;
  updated_at: string;

  // Calculated fields from API
  calculated_selling_price_selling_currency?: number;
  supplier?: {
    id: string;
    name: string;
    type: string;
    status: string;
  };
  product_service?: {
    id: string;
    name: string;
    type: string;
    category?: {
      id: string;
      name: string;
    };
  };
  category?: {
    id: string;
    name: string;
  };
}

interface SupplierOfferingsPageClientProps {
  offerings: SupplierOffering[];
  suppliers: any[];
  categories: any[];
  productsServices: any[];
  hotels: any[];
  destinations: any[];
  isLoading: boolean;
  totalCount: number;
  pageSize: number;
}

const columnHelper = createColumnHelper<SupplierOffering>();

const SupplierOfferingsPageClient = ({
  offerings,
  suppliers,
  categories,
  productsServices,
  hotels,
  destinations,
  isLoading,
  totalCount,
  pageSize,
}: SupplierOfferingsPageClientProps) => {
  const navigate = useNavigate();
  const location = useLocation();
  const { hasPermission } = useRbac();


  const deleteSupplierOfferingMutation = useDeleteSupplierOffering();
  const duplicateSupplierOfferingMutation = useDuplicateSupplierOffering();

  // Get default currency from store settings
  const { defaultCurrency } = useAdminCurrencies();

  // Format currency using the default currency from store settings
  const formatCurrency = (amount: number) => {
    const currency = defaultCurrency || { currency_code: "GBP", symbol: "£", decimal_digits: 2 };

    return formatCurrencyAmount(amount, currency, {
      showSymbol: true,
      showCode: false,
      symbolPosition: "before",
    });
  };

  // Row selection state for multi-select functionality
  const [rowSelection, setRowSelection] = useState({});

  // Modal state for bulk status change
  const [isBulkStatusModalOpen, setIsBulkStatusModalOpen] = useState(false);
  const [currentSelection, setCurrentSelection] = useState<Record<string, boolean>>({});

  // Delete modal state
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [offeringToDelete, setOfferingToDelete] = useState<SupplierOffering | null>(null);

  // Handle export of selected rows
  const handleExportSelected = async (selection: Record<string, boolean>) => {
    const selectedIds = Object.keys(selection);
    if (selectedIds.length === 0) {
      toast.error("No rows selected for export");
      return;
    }

    try {
      // Build export URL with selected IDs
      const params = new URLSearchParams();
      params.append("format", "excel");
      selectedIds.forEach(id => params.append("ids", id));

      // Open export URL in new tab
      const exportUrl = `/admin/supplier-management/supplier-offerings/export?${params.toString()}`;
      window.open(exportUrl, '_blank');

      toast.success(`Exporting ${selectedIds.length} selected supplier offerings`);
    } catch (error) {
      console.error("Export error:", error);
      toast.error("Failed to export selected supplier offerings");
    }
  };

  // Handle bulk status change - open modal
  const handleBulkStatusChange = useCallback(async (selection: Record<string, boolean>) => {
    const selectedIds = Object.keys(selection);

    if (selectedIds.length === 0) {
      toast.error("No rows selected for status change");
      return;
    }

    // Store the current selection for the modal
    setCurrentSelection(selection);
    // Open the modal
    setIsBulkStatusModalOpen(true);
  }, []);

  // Handle the actual status change from modal
  const handleStatusChangeSubmit = async (ids: string[], newStatus: string) => {
    try {
      // Call bulk update API
      const response = await fetch("/admin/supplier-management/supplier-offerings/bulk-status", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        credentials: "include",
        body: JSON.stringify({
          ids,
          status: newStatus,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || "Failed to update status");
      }

      await response.json();

      // Refresh the page data
      window.location.reload();
    } catch (error) {
      console.error("Bulk status change error:", error);
      throw error; // Re-throw to let modal handle the error display
    }
  };

  // Define bulk commands for selected rows - conditional based on selection count
  const commands = useMemo(() => {
    const selectedCount = Object.keys(rowSelection || {}).length;
    const commandList = [];

    // Always show Export Selected when items are selected
    if (selectedCount > 0) {
      commandList.push({
        label: "Export Selected",
        shortcut: "e",
        action: handleExportSelected,
      });
    }

    // Show Change Status for any selection (1+ items)
    if (selectedCount > 0) {
      commandList.push({
        label: "Change Status",
        shortcut: "s",
        action: handleBulkStatusChange,
      });
    }

    return commandList;
  }, [rowSelection, handleExportSelected, handleBulkStatusChange]);


  // Helper function to resolve product/service name
  const resolveProductServiceName = (productService: any, hotels: any[], destinations: any[]) => {
    if (!productService) return "Unknown Product/Service";
    
    if (productService.type === "hotel" && productService.hotel_id) {
      const hotel = hotels.find(h => h.id === productService.hotel_id);
      return hotel ? hotel.name : productService.name || "Unknown Hotel";
    }
    
    if (productService.type === "destination" && productService.destination_id) {
      const destination = destinations.find(d => d.id === productService.destination_id);
      return destination ? destination.name : productService.name || "Unknown Destination";
    }
    
    return productService.name || "Unknown Product/Service";
  };

  // Handle copy offering ID
  const handleCopyOfferingId = (id: string) => {
    navigator.clipboard.writeText(id);
    toast.success("Offering ID copied to clipboard");
  };

  // Handle delete offering click - open modal
  const handleDeleteOfferingClick = (offering: SupplierOffering) => {
    setOfferingToDelete(offering);
    setIsDeleteModalOpen(true);
  };

  // Handle delete offering - actual deletion
  const handleDeleteOffering = async () => {
    if (!offeringToDelete) return;

    // Dismiss any existing toasts and show loading toast
    toast.dismiss();
    const loadingToast = toast.loading("Deleting supplier offering...");

    try {
      await deleteSupplierOfferingMutation.mutateAsync(offeringToDelete.id);
      toast.dismiss(loadingToast);
      toast.success("Supplier offering deleted successfully");
      setIsDeleteModalOpen(false);
      setOfferingToDelete(null);
    } catch (error) {
      toast.dismiss(loadingToast);
      toast.error("Failed to delete supplier offering");
      setIsDeleteModalOpen(false);
      setOfferingToDelete(null);
    }
  };

  // Handle cancel delete
  const handleCancelDelete = () => {
    setIsDeleteModalOpen(false);
    setOfferingToDelete(null);
  };

  // Handle duplicate offering
  const handleDuplicateOffering = async (id: string) => {
    try {
      await duplicateSupplierOfferingMutation.mutateAsync(id);
      toast.success("Supplier offering duplicated successfully");
    } catch (error) {
      toast.error("Failed to duplicate supplier offering");
    }
  };



  // Get selected category from URL to determine if we should show custom field columns
  const searchParams = new URLSearchParams(location.search);
  const selectedCategoryId = searchParams.get("category_id");
  const selectedCategory = categories.find(cat => cat.id === selectedCategoryId);

  // Helper function to render custom field value with resolved names
  const renderCustomFieldValue = (customFields: any, fieldKey: string) => {
    if (!customFields) return "N/A";

    // Check for resolved names first (with _names suffix)
    const resolvedNamesKey = `${fieldKey}_names`;
    if (customFields[resolvedNamesKey]) {
      const names = customFields[resolvedNamesKey];
      if (Array.isArray(names)) {
        return names.length > 0 ? names.join(", ") : "N/A";
      }
      return names || "N/A";
    }

    // Fallback to original field value
    const value = customFields[fieldKey];
    if (!value) return "N/A";

    if (Array.isArray(value)) {
      return value.length > 0 ? value.join(", ") : "N/A";
    }

    return value.toString();
  };

  // Generate dynamic custom field columns based on selected category
  const generateCustomFieldColumns = () => {
    if (!selectedCategory?.dynamic_field_schema) return [];

    // Filter dynamic fields to only include fields marked for supplier offering usage
    const filteredFields = selectedCategory.dynamic_field_schema.filter((field: any) => {
      const isSupplierContext = !field.field_context || field.field_context === "supplier";
      const isUsedInSupplierOffering = field.used_in_supplier_offering === true;
      return isSupplierContext && isUsedInSupplierOffering;
    });

    return filteredFields.map((field: any) =>
      columnHelper.display({
        id: `custom_field_${field.key}`,
        header: field.label,
        cell: ({ row }) => {
          const offering = row.original;
          // Check both offering custom fields and product service custom fields
          const offeringValue = renderCustomFieldValue((offering as any).custom_fields, field.key);
          const productServiceValue = renderCustomFieldValue((offering.product_service as any)?.custom_fields, field.key);

          // Prioritize offering custom fields, fallback to product service custom fields
          const displayValue = offeringValue !== "N/A" ? offeringValue : productServiceValue;

          return (
            <div className="max-w-[200px]">
              {field.type === "hotels" || field.type === "destinations" || field.type === "addons" ? (
                <div className="flex flex-wrap gap-1">
                  {displayValue.split(", ").map((name: string, index: number) => (
                    <Badge key={index} size="small" color="grey">
                      {name}
                    </Badge>
                  ))}
                </div>
              ) : (
                <Text className="txt-compact-small truncate" title={displayValue}>
                  {displayValue}
                </Text>
              )}
            </div>
          );
        },
      })
    );
  };

  // Define columns
  const columns = useMemo<ColumnDef<SupplierOffering, any>[]>(() => {
    const baseColumns = [
    // Select column for multi-select functionality
    columnHelper.display({
      id: "select",
      header: ({ table }) => (
        <input
          type="checkbox"
          checked={table.getIsAllPageRowsSelected()}
          onChange={table.getToggleAllPageRowsSelectedHandler()}
          className="rounded border-ui-border-base"
        />
      ),
      cell: ({ row }) => (
        <input
          type="checkbox"
          checked={row.getIsSelected()}
          onChange={row.getToggleSelectedHandler()}
          className="rounded border-ui-border-base"
        />
      ),
    }),
    columnHelper.display({
      id: "supplier",
      header: "Supplier Name",
      cell: ({ row }) => {
        const offering = row.original;
        return (
          <div className="flex items-center gap-x-3 min-w-[280px] w-full pr-4" style={{ minWidth: '280px', width: '100%' }}>
            <div className="flex h-8 w-8 items-center justify-center rounded bg-ui-bg-subtle flex-shrink-0">
              <CategoryIcon category={offering.product_service?.category} className="h-4 w-4 text-ui-fg-subtle" />
            </div>
            <div className="flex-1 min-w-0 overflow-visible">
              <Text
                className="txt-compact-medium-plus break-words leading-relaxed whitespace-normal text-wrap overflow-visible"
                weight="plus"
                title={offering.supplier?.name || "Unknown Supplier"}
                style={{ wordBreak: 'break-word', whiteSpace: 'normal', overflow: 'visible' }}
              >
                {offering.supplier?.name || "Unknown Supplier"}
              </Text>
              <div className="flex items-center gap-x-1">
                <Text
                  size="small"
                  className="text-ui-fg-subtle break-words whitespace-normal overflow-visible"
                  style={{ wordBreak: 'break-word', whiteSpace: 'normal', overflow: 'visible' }}
                >
                  {offering.supplier?.type}
                </Text>
              </div>
            </div>
          </div>
        );
      },
      size: 300, // Further increased column width
      minSize: 280,
      maxSize: 450,
    }),
    columnHelper.display({
      id: "product_service",
      header: () => (
        <div className="txt-compact-small-plus h-12 py-0 pl-0 pr-6 text-left bg-ui-bg-subtle sticky after:absolute after:inset-y-0 after:right-0 after:h-full after:w-px after:bg-transparent after:content-[''] left-[68px] flex items-center">
          <span>Product/Service Name</span>
        </div>
      ),
      cell: ({ row }) => {
        const offering = row.original;
        const productServiceName = resolveProductServiceName(offering.product_service, hotels, destinations);
        return (
          <div className="h-12 py-0 pl-0 pr-6 !pl-0 !pr-0 bg-ui-bg-base group-data-[selected=true]/row:bg-ui-bg-highlight group-data-[selected=true]/row:group-hover/row:bg-ui-bg-highlight-hover group-hover/row:bg-ui-bg-base-hover transition-fg group-has-[[data-row-link]:focus-visible]:bg-ui-bg-base-hover sticky after:absolute after:inset-y-0 after:right-0 after:h-full after:w-px after:bg-transparent after:content-[''] left-[68px] flex items-center">
            <Text
              className="txt-compact-medium-plus font-medium break-words leading-relaxed whitespace-normal text-wrap overflow-visible"
              title={productServiceName}
              style={{
                wordBreak: 'break-word',
                whiteSpace: 'normal',
                overflow: 'visible',
                display: 'block',
                width: '100%'
              }}
            >
              {productServiceName}
            </Text>
          </div>
        );
      },
      size: 450, // Further increased column width
      minSize: 400,
      maxSize: 700,
    }),
    columnHelper.display({
      id: "category",
      header: "Category",
      cell: ({ row }) => {
        const offering = row.original;
        const categoryName = offering.product_service?.category?.name || "Uncategorized";
        return (
          <div className="min-w-[250px] w-full pr-4 overflow-visible" style={{ minWidth: '250px', width: '100%', overflow: 'visible' }}>
            <Text
              className="text-sm break-words leading-relaxed whitespace-normal text-wrap overflow-visible"
              title={categoryName}
              style={{
                wordBreak: 'break-word',
                whiteSpace: 'normal',
                overflow: 'visible',
                display: 'block',
                width: '100%'
              }}
            >
              {categoryName}
            </Text>
          </div>
        );
      },
      size: 280, // Further increased column width
      minSize: 250,
      maxSize: 400,
    }),
    columnHelper.display({
      id: "selling_price",
      header: "Selling Price",
      meta: {
        align: "right",
      },
      cell: ({ row }) => {
        const offering = row.original;

        // Debug: Log the offering data to see what's actually available
        console.log('Offering debug:', {
          id: offering.id,
          calculated_selling_price_selling_currency: offering.calculated_selling_price_selling_currency,
          selling_price_selling_currency: offering.selling_price_selling_currency,
          selling_price: offering.selling_price,
          gross_price: offering.gross_price,
          net_price: offering.net_price,
          currency: offering.currency,
          selling_currency: offering.selling_currency,
          allKeys: Object.keys(offering)
        });

        // Try to get selling price from calculated field first, then fallback to direct field
        let sellingPrice: number | undefined = offering.calculated_selling_price_selling_currency;

        // If calculated field is not available, try the direct field
        if (sellingPrice === undefined || sellingPrice === null) {
          const directPrice = offering.selling_price_selling_currency;
          sellingPrice = typeof directPrice === 'string' ? parseFloat(directPrice) : directPrice;
        }

        // If still no price, try other price fields as fallback
        if (sellingPrice === undefined || sellingPrice === null) {
          const fallbackPrice = offering.selling_price || offering.gross_price || offering.net_price;
          sellingPrice = typeof fallbackPrice === 'string' ? parseFloat(fallbackPrice) : fallbackPrice;
        }

        // Ensure we have a valid number
        const numericPrice = typeof sellingPrice === 'number' ? sellingPrice : undefined;

        return (
          <div className="h-12 py-0 pl-0 pr-6 !pl-0 !pr-0 bg-ui-bg-base group-data-[selected=true]/row:bg-ui-bg-highlight group-data-[selected=true]/row:group-hover/row:bg-ui-bg-highlight-hover group-hover/row:bg-ui-bg-base-hover transition-fg group-has-[[data-row-link]:focus-visible]:bg-ui-bg-base-hover sticky after:absolute after:inset-y-0 after:right-0 after:h-full after:w-px after:bg-transparent after:content-[''] left-[68px] flex items-center justify-end">
            <Text className="txt-compact-medium">
              {numericPrice && !isNaN(numericPrice) && numericPrice > 0
                ? formatCurrency(numericPrice)
                : "N/A"
              }
            </Text>
          </div>
        );
      },
      size: 120,
      minSize: 100,
      maxSize: 150,
    }),
    columnHelper.accessor("status", {
      header: "Status",
      cell: ({ getValue }) => {
        const status = getValue();
        return (
          <Badge color={status === "active" ? "green" : "red"} size="small">
            {status === "active" ? "Active" : "Inactive"}
          </Badge>
        );
      },
    }),
    columnHelper.display({
      id: "validity",
      header: "Validity",
      cell: ({ row }) => {
        const offering = row.original;

        const formatValidityPeriod = (from: Date | string | null, to: Date | string | null): string => {
          if (!from) return "Not set";

          const fromDate = new Date(from);
          const fromStr = fromDate.toLocaleDateString();

          if (!to) return `${fromStr} - Open`;

          const toDate = new Date(to);
          const toStr = toDate.toLocaleDateString();

          return `${fromStr} - ${toStr}`;
        };

        return (
          <div className="flex items-center gap-1">
            <Text size="small">
              {formatValidityPeriod(offering.active_from || null, offering.active_to || null)}
            </Text>
          </div>
        );
      },
    }),
    columnHelper.display({
      id: "actions",
      header: "Actions",
      cell: ({ row }) => {
        const offering = row.original;
        return (
          <DropdownMenu>
            <DropdownMenu.Trigger asChild>
              <Button
                variant="transparent"
                size="small"
                onClick={(e) => e.stopPropagation()}
              >
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenu.Trigger>
            <DropdownMenu.Content>
              <DropdownMenu.Item
                onClick={(e) => {
                  e.stopPropagation();
                  navigate(`/supplier-management/supplier-offerings/${offering.id}`);
                }}
              >
                <Eye className="h-4 w-4 mr-2" />
                View
              </DropdownMenu.Item>
              {hasPermission("supplier_management:edit") && (
                <DropdownMenu.Item
                  onClick={(e) => {
                    e.stopPropagation();
                    navigate(`/supplier-management/supplier-offerings/${offering.id}/edit`);
                  }}
                >
                  <Edit className="h-4 w-4 mr-2" />
                  Edit
                </DropdownMenu.Item>
              )}
              {hasPermission("supplier_management:create") && (
                <DropdownMenu.Item
                  onClick={(e) => {
                    e.stopPropagation();
                    handleDuplicateOffering(offering.id);
                  }}
                >
                  <Copy className="h-4 w-4 mr-2" />
                  Duplicate
                </DropdownMenu.Item>
              )}
              {hasPermission("supplier_management:delete") && (
                <DropdownMenu.Item
                  onClick={(e) => {
                    e.stopPropagation();
                    handleDeleteOfferingClick(offering);
                  }}
                  className="text-ui-fg-error"
                >
                  <Trash className="h-4 w-4 mr-2" />
                  Delete
                </DropdownMenu.Item>
              )}
            </DropdownMenu.Content>
          </DropdownMenu>
        );
      },
    }),
    ];

    // Add custom field columns if a category is selected
    const customFieldColumns = generateCustomFieldColumns();

    return [...baseColumns, ...customFieldColumns];
  }, [offerings, suppliers, categories, productsServices, hotels, destinations, hasPermission, navigate, selectedCategory]);

  // Define filters
  const filters = useMemo<Filter[]>(() => [
    {
      key: "supplier_id",
      label: "Supplier",
      type: "select",
      multiple: false,
      searchable: true,
      options: suppliers.map(supplier => ({
        label: supplier.name,
        value: supplier.id,
      })),
    },
    {
      key: "category_id",
      label: "Category",
      type: "select",
      multiple: false,
      options: categories.map(category => ({
        label: category.name,
        value: category.id,
      })),
    },
    {
      key: "product_service_id",
      label: "Product/Service",
      type: "select",
      multiple: false,
      searchable: true,
      options: productsServices.map(ps => ({
        label: resolveProductServiceName(ps, hotels, destinations),
        value: ps.id,
      })),
    },
    {
      key: "status",
      label: "Status",
      type: "select",
      multiple: false,
      options: [
        { label: "Active", value: "active" },
        { label: "Inactive", value: "inactive" },
      ],
    },
    {
      key: "active_from",
      label: "Active From",
      type: "date",
    },
    {
      key: "active_to",
      label: "Active To",
      type: "date",
    },
  ], [suppliers, categories, productsServices, hotels, destinations]);

  // Define orderBy options
  const orderBy = useMemo(() => [
    { key: "product_service_name" as keyof SupplierOffering, label: "Product/Service Name" },
    { key: "commission_percentage" as keyof SupplierOffering, label: "Commission %" },
    { key: "gross_price" as keyof SupplierOffering, label: "Gross Price" },
    { key: "net_price" as keyof SupplierOffering, label: "Net Price" },
    { key: "status" as keyof SupplierOffering, label: "Status" },
    { key: "validity" as keyof SupplierOffering, label: "Validity" },
    { key: "created_at" as keyof SupplierOffering, label: "Created At" },
    { key: "updated_at" as keyof SupplierOffering, label: "Updated At" },
  ], []);

  // Get current page from URL
  const currentPage = parseInt(searchParams.get("page") || "1");

  // Create table instance
  const table = useReactTable({
    data: offerings,
    columns,
    getCoreRowModel: getCoreRowModel(),
    manualPagination: true,
    pageCount: Math.ceil(totalCount / pageSize),
    enableRowSelection: true,
    getRowId: (row) => row.id,
    columnResizeMode: 'onChange',
    enableColumnResizing: true,
    defaultColumn: {
      minSize: 200,
      maxSize: 1000,
    },
    state: {
      pagination: {
        pageIndex: currentPage - 1,
        pageSize,
      },
      rowSelection,
    },
    onRowSelectionChange: setRowSelection,
  });

  return (
    <Container className="divide-y p-0">
      {/* Header */}
      <div className="flex items-center justify-between px-6 py-4">
        <div>
          <Heading level="h2">Supplier Offerings</Heading>
        </div>
        <div className="flex items-center gap-x-2">
          <Button
            variant="secondary"
            asChild
          >
            <Link to="/supplier-management/supplier-offerings/export">
              Export
            </Link>
          </Button>
          {hasPermission("supplier_management:bulk_operations") && (
            <Button
              variant="secondary"
              asChild
            >
              <Link to="/supplier-management/supplier-offerings/import">
                Import
              </Link>
            </Button>
          )}
          {hasPermission("supplier_management:create") && (
            <Button asChild>
              <Link to="/supplier-management/supplier-offerings/create">
                Create Offering
              </Link>
            </Button>
          )}
        </div>
      </div>

      {/* DataTable */}
      <div className="overflow-x-auto min-w-0">
        <div className="min-w-[1400px]">
          <DataTable
            table={table}
            columns={columns}
            pageSize={pageSize}
            count={totalCount}
            isLoading={isLoading}
            filters={filters}
            orderBy={orderBy}
            search="autofocus"
            pagination
            navigateTo={(row) => `/supplier-management/supplier-offerings/${row.original.id}`}
            commands={commands}
            queryObject={Object.fromEntries(searchParams)}
            noRecords={{
              title: "No supplier offerings found",
              message: "Get started by creating your first supplier offering",
            }}
          />
        </div>
      </div>
      <Toaster />

      {/* Bulk Status Change Modal */}
      {isBulkStatusModalOpen && (
        <BulkStatusChangeModal
          open={isBulkStatusModalOpen}
          onOpenChange={setIsBulkStatusModalOpen}
          selectedIds={Object.keys(currentSelection)}
          selectedItems={Object.keys(currentSelection).map(id => {
            try {
              const item = offerings.find((so: any) => so.id === id);
              return {
                id,
                name: item ? resolveProductServiceName(item.product_service, hotels, destinations) : `Supplier Offering ${id}`,
                status: item?.status || 'unknown'
              };
            } catch (error) {
              console.error("Error preparing modal item:", error);
              return {
                id,
                name: `Supplier Offering ${id}`,
                status: 'unknown'
              };
            }
          })}
          onStatusChange={handleStatusChangeSubmit}
        />
      )}

      {/* Delete Confirmation Modal */}
      <Prompt open={isDeleteModalOpen} onOpenChange={setIsDeleteModalOpen}>
        <Prompt.Content>
          <Prompt.Header>
            <Prompt.Title>Delete Supplier Offering</Prompt.Title>
            <Prompt.Description>
              Are you sure you want to delete this supplier offering? This action cannot be undone.
              {offeringToDelete && (
                <div className="mt-2 text-sm font-medium">
                  {resolveProductServiceName(offeringToDelete.product_service, hotels, destinations)} by {offeringToDelete.supplier?.name}
                </div>
              )}
            </Prompt.Description>
          </Prompt.Header>
          <Prompt.Footer>
            <Prompt.Cancel onClick={handleCancelDelete}>
              Cancel
            </Prompt.Cancel>
            <Prompt.Action onClick={handleDeleteOffering}>Delete</Prompt.Action>
          </Prompt.Footer>
        </Prompt.Content>
      </Prompt>
    </Container>
  );
};

export default SupplierOfferingsPageClient;
