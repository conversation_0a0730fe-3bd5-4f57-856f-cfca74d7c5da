import React, { useState, useRef, useEffect } from "react";
import {
  Button,
  FocusModal,
  Text,
  Heading,
  Input,
  Table,
  Badge,
  toast,
  Select,
  Label,
} from "@camped-ai/ui";
import {
  Upload,
  Download,
  AlertCircle,
  CheckCircle,
} from "lucide-react";
import {
  useSupplierOfferingImportExport,
  type ImportValidationError,
} from "../../../../hooks/supplier-products-services/use-supplier-offerings";
import { useCategories } from "../../../../hooks/supplier-products-services/use-categories";
import { useSuppliers } from "../../../../hooks/vendor-management/use-suppliers";
import { useProductsServices } from "../../../../hooks/supplier-products-services/use-products-services";
import { useHotels } from "../../../../hooks/supplier-products-services/use-hotels";
import { useDestinations } from "../../../../hooks/supplier-products-services/use-destinations";

import { useNavigate } from "react-router-dom";

// Helper function to format Excel dates for display
const formatExcelDateForDisplay = (dateValue: any): string | null => {
  if (!dateValue || dateValue === "") return null;

  try {
    // If it's already a Date object
    if (dateValue instanceof Date) {
      return dateValue.toLocaleDateString('en-GB'); // DD/MM/YYYY format
    }

    // If it's a number (Excel serial date)
    if (typeof dateValue === "number") {
      // Excel serial date to JavaScript date
      const excelEpoch = new Date(1900, 0, 1);
      const jsDate = new Date(
        excelEpoch.getTime() + (dateValue - 2) * 24 * 60 * 60 * 1000
      );
      return jsDate.toLocaleDateString('en-GB'); // DD/MM/YYYY format
    }

    // If it's a string, try to parse it
    if (typeof dateValue === "string") {
      const date = new Date(dateValue);
      if (!isNaN(date.getTime())) {
        return date.toLocaleDateString('en-GB'); // DD/MM/YYYY format
      }
    }

    return null;
  } catch (error) {
    console.error("Error formatting date for display:", error);
    return null;
  }
};

// Function to resolve hotel and destination IDs in product service names
const resolveProductServiceName = (productService: any, hotels: any[], destinations: any[]): string => {
  if (!productService?.name || !productService?.category?.dynamic_field_schema || !productService?.custom_fields) {
    return productService?.name || 'Unknown Product/Service';
  }

  let resolvedName = productService.name;

  // Find hotel and destination fields in the category schema
  const hotelFields = productService.category.dynamic_field_schema.filter(
    (field: any) => field.type === "hotels" && field.used_in_product !== false
  );
  const destinationFields = productService.category.dynamic_field_schema.filter(
    (field: any) => field.type === "destinations" && field.used_in_product !== false
  );

  // Resolve hotel field names
  hotelFields.forEach((field: any) => {
    if (productService.custom_fields[field.key]) {
      try {
        let hotelIds: string[] = [];

        // Handle different data formats
        if (Array.isArray(productService.custom_fields[field.key])) {
          hotelIds = productService.custom_fields[field.key];
        } else if (typeof productService.custom_fields[field.key] === 'string') {
          try {
            const parsed = JSON.parse(productService.custom_fields[field.key]);
            hotelIds = Array.isArray(parsed) ? parsed : [parsed];
          } catch {
            hotelIds = [productService.custom_fields[field.key]];
          }
        }

        hotelIds.forEach(hotelId => {
          const hotel = hotels.find(h => h.id === hotelId);
          if (hotel) {
            // Replace ID with name in the product service name
            resolvedName = resolvedName.replace(hotelId, hotel.name);
          }
        });
      } catch (error) {
        console.warn("Error resolving hotel names in product service name:", error);
      }
    }
  });

  // Resolve destination field names
  destinationFields.forEach((field: any) => {
    if (productService.custom_fields[field.key]) {
      try {
        let destinationIds: string[] = [];

        // Handle different data formats
        if (Array.isArray(productService.custom_fields[field.key])) {
          destinationIds = productService.custom_fields[field.key];
        } else if (typeof productService.custom_fields[field.key] === 'string') {
          try {
            const parsed = JSON.parse(productService.custom_fields[field.key]);
            destinationIds = Array.isArray(parsed) ? parsed : [parsed];
          } catch {
            destinationIds = [productService.custom_fields[field.key]];
          }
        }

        destinationIds.forEach(destinationId => {
          const destination = destinations.find(d => d.id === destinationId);
          if (destination) {
            // Replace ID with name in the product service name
            resolvedName = resolvedName.replace(destinationId, destination.name);
          }
        });
      } catch (error) {
        console.warn("Error resolving destination names in product service name:", error);
      }
    }
  });

  return resolvedName;
};


// Remove import steps - we'll use a simpler flow like products & services

const SupplierOfferingImportModal = () => {
  const [importFile, setImportFile] = useState<File | null>(null);
  const [importData, setImportData] = useState<any[]>([]);
  const [importErrors, setImportErrors] = useState<ImportValidationError[]>([]);
  const [selectedCategoryId, setSelectedCategoryId] = useState<string>('');
  const [importSuccess, setImportSuccess] = useState(false);
  const [importedCount, setImportedCount] = useState(0);
  const [importBackendErrors, setImportBackendErrors] = useState<any[]>([]);
  const [importResult, setImportResult] = useState<any>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const navigate = useNavigate();

  // Fetch data for preview functionality and dropdown
  const { data: categoriesData } = useCategories({ is_active: true });
  const { data: suppliersData } = useSuppliers();
  const { data: productsServicesData } = useProductsServices();
  const { data: hotelsData } = useHotels();
  const { data: destinationsData } = useDestinations();

  const categories = categoriesData?.categories || [];
  const suppliers = suppliersData?.suppliers || [];
  const productsServices = productsServicesData?.product_services || [];
  const hotels = hotelsData?.hotels || [];
  const destinations = destinationsData?.destinations || [];

  // Define import steps
  const importSteps = [
    { id: 1, title: "Download", description: "Get template" },
    { id: 2, title: "Upload", description: "Upload file" },
    { id: 3, title: "Review", description: "Review data" },
    { id: 4, title: "Import", description: "Process data" },
  ];

  // Determine current step based on state
  const getCurrentStep = () => {
    if (importSuccess || importResult) return 4; // Import/Complete step
    if (importFile && importData.length > 0) return 3; // Ready for review
    if (importFile) return 2; // File uploaded
    return 1; // Download step
  };

  // Get step status
  const getStepStatus = (stepId: number) => {
    const current = getCurrentStep();
    if (stepId < current) return 'completed';
    if (stepId === current) return 'current';
    return 'pending';
  };

  const {
    parseImportFile,
    importSupplierOfferings,
    isImporting,
  } = useSupplierOfferingImportExport();

  // Reset form when category changes
  useEffect(() => {
    if (selectedCategoryId) {
      setImportFile(null);
      setImportData([]);
      setImportErrors([]);
      setImportSuccess(false);
      setImportedCount(0);
      setImportBackendErrors([]);
      setImportResult(null);
    }
  }, [selectedCategoryId]);

  const resetImportModal = () => {
    setImportFile(null);
    setImportData([]);
    setImportErrors([]);
    setSelectedCategoryId('');
    setImportSuccess(false);
    setImportedCount(0);
    setImportBackendErrors([]);
    setImportResult(null);
  };

  const handleClose = () => {
    // Reset all modal state when closing
    resetImportModal();
    navigate("/supplier-management/supplier-offerings");
  };

  // Auto-parse file when uploaded (like products & services)
  const handleFileSelect = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      // Validate category selection before processing file
      if (!selectedCategoryId) {
        toast.error("Please select a category before uploading a file");
        // Reset the file input
        if (event.target) {
          event.target.value = '';
        }
        return;
      }

      setImportFile(file);
      setImportData([]);
      setImportErrors([]);
      setImportBackendErrors([]);
      setImportResult(null);
      setImportSuccess(false);

      try {
        const { data, errors } = await parseImportFile(file);

        // Debug - check if mandatory pricing fields exist (updated for new template structure)
        if (data.length > 0) {
          const firstRow = data[0];
          const mandatoryPricingFields = [
            'cost_currency',
            'gross_cost',
            'commission',
            'margin_rate'
            // Note: net_cost, selling_price, selling_price_selling_currency are auto-calculated
            // Note: exchange_rate is handled automatically by the system
          ];
          const missingPricingFields = mandatoryPricingFields.filter(field => !(field in firstRow));
          if (missingPricingFields.length > 0) {
            console.warn("⚠️ Mandatory pricing fields missing from Excel file:", missingPricingFields);
            console.warn("These fields are required and will cause validation errors during import.");
          }
        }

        setImportData(data);
        setImportErrors(errors);
      } catch (error) {
        console.error("🔍 Modal Debug - Parse error:", error);
        toast.error("Failed to parse file");
      }
    }
  };

  const handleImport = async () => {
    if (importData.length === 0) return;

    try {
      const result = await importSupplierOfferings(importData);

      setImportResult(result);

      // Check if there were any errors in the result
      if (result.errors && result.errors.length > 0) {
        setImportBackendErrors(result.errors);
        setImportSuccess(false);


        // Show partial success message if some records were imported
        if (result.created > 0) {
          toast.success(`Imported ${result.created} supplier offerings with ${result.errors.length} errors`);
        } else {
          toast.error("Import failed with errors. Please check the error details below.");
        }
      } else {
        setImportSuccess(true);
        setImportedCount(result.created || importData.length);
        toast.success(result.message || "Import completed successfully");
      }
    } catch (error) {

      // Try to extract detailed error information
      let errorMessage = "Import failed";
      let backendErrors: any[] = [];

      if (error instanceof Error) {
        errorMessage = error.message;

        // Check if the error contains structured error data
        try {
          const errorData = JSON.parse(error.message);
          if (errorData.errors && Array.isArray(errorData.errors)) {
            backendErrors = errorData.errors;
          }
        } catch (parseError) {
          // Error message is not JSON, check if it's a fetch error with response data
          if (error.message.includes("Import failed:")) {
            // This might be from the mutation error handling
            errorMessage = error.message;
          }
        }
      }

      // If we have specific backend errors, display them
      if (backendErrors.length > 0) {
        setImportBackendErrors(backendErrors);
        setImportSuccess(false);
        toast.error(`Import failed with ${backendErrors.length} errors. Please check the details below.`);
      } else {
        // For general errors, create a single error entry for display
        const generalError = {
          index: 0,
          message: errorMessage,
          data: null
        };
        setImportBackendErrors([generalError]);
        setImportSuccess(false);
        toast.error("Import failed. Please check the error details below.");
      }
    }
  };

  const handleDownloadTemplate = async () => {
    if (!selectedCategoryId) {
      toast.error("Please select a category first");
      return;
    }

    const selectedCategory = categories.find((cat: any) => cat.id === selectedCategoryId);
    if (!selectedCategory) {
      toast.error("Selected category not found");
      return;
    }

    try {
      // Use the API endpoint to download the template with dynamic dropdowns
      const templateUrl = `/admin/supplier-management/supplier-offerings/template?category_id=${selectedCategoryId}`;

      const response = await fetch(templateUrl, {
        method: 'GET',
        credentials: 'include',
        headers: {
          'Accept': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        },
      });

      if (!response.ok) {
        throw new Error(`Failed to download template: ${response.statusText}`);
      }

      // Get the blob from the response
      const blob = await response.blob();

      // Create a download link
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement("a");
      link.href = url;
      link.download = `supplier_offerings_template_${selectedCategory.name}_${new Date().toISOString().split("T")[0]
        }.xlsx`;
      document.body.appendChild(link);
      link.click();

      // Clean up
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);

      toast.success(`Template for ${selectedCategory.name} downloaded successfully`);
    } catch (error) {
      toast.error(`Failed to download template for ${selectedCategory.name}`);
    }
  };

  const renderImportContent = () => (
    <>
      {/* Step 1: Download Template */}
      <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 shadow-sm">
        <div className="flex items-start gap-3 p-6">
          <div className="flex-shrink-0 w-8 h-8 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center">
            <span className="text-blue-600 dark:text-blue-400 font-semibold text-sm">1</span>
          </div>
          <div className="flex-1">
            <Heading level="h3" className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
              Download Import Template
            </Heading>
            <Text className="text-gray-600 dark:text-gray-400 mb-4">
              Download category-specific Excel templates with pre-configured fields for supplier offerings. Each
              category has its own template with custom fields specific to that category.
            </Text>

            <div className="bg-blue-50 dark:bg-blue-900/20 p-3 rounded-md border border-blue-200 dark:border-blue-700 mb-4">
              <Text className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Each template includes:
              </Text>
              <ul className="text-sm text-gray-600 dark:text-gray-400 space-y-1">
                <li>
                  • <strong className="text-gray-800 dark:text-gray-200">Category-specific fields:</strong>{" "}
                  Custom fields and validation for the selected category
                </li>
                <li>
                  • <strong className="text-gray-800 dark:text-gray-200">User-friendly dropdowns:</strong>{" "}
                  Supplier names, product/service names, status options
                </li>
                <li>
                  • <strong className="text-gray-800 dark:text-gray-200">Reference sheets:</strong>{" "}
                  Easy lookup for multi-select fields (supports comma-separated values)
                </li>
                <li>
                  • <strong className="text-gray-800 dark:text-gray-200">Custom field dropdowns:</strong>{" "}
                  Dynamic validation for category-specific fields
                </li>
                <li>
                  • <strong className="text-gray-800 dark:text-gray-200">Instructions sheet:</strong>{" "}
                  Comprehensive field guide with all available options
                </li>
              </ul>
            </div>

            {/* Category Selection and Download */}
            {categories && categories.length > 0 ? (
              <div className="space-y-4">
                <div>
                  <Label className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 block">
                    Select Category:
                  </Label>
                  <Select value={selectedCategoryId} onValueChange={setSelectedCategoryId}>
                    <Select.Trigger className="w-80 max-w-full">
                      <Select.Value placeholder="Choose a category..." />
                    </Select.Trigger>
                    <Select.Content>
                      {categories.map((category: any) => (
                        <Select.Item key={category.id} value={category.id}>
                          <div className="flex items-center gap-2">
                            <span>{category.icon || '📦'}</span>
                            <span>{category.name}</span>
                            <span className="text-xs text-gray-500">
                              ({category.category_type || 'Category'})
                            </span>
                          </div>
                        </Select.Item>
                      ))}
                    </Select.Content>
                  </Select>
                </div>

                <Button
                  variant="secondary"
                  onClick={handleDownloadTemplate}
                  disabled={!selectedCategoryId}
                  className="flex items-center gap-2 bg-blue-600 dark:bg-blue-500 text-white hover:bg-blue-700 dark:hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <Download className="w-4 h-4" />
                  Download Excel Template
                </Button>
              </div>
            ) : (
              <div className="bg-yellow-50 dark:bg-yellow-900/20 p-3 rounded-md border border-yellow-200 dark:border-yellow-700">
                <Text className="text-sm text-yellow-700 dark:text-yellow-300">
                  No categories found. Please create categories first before generating templates.
                </Text>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Step 2: Upload File */}
      <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 shadow-sm">
        <div className="flex items-start gap-3 p-6">
          <div className="flex-shrink-0 w-8 h-8 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center">
            <span className="text-green-600 dark:text-green-400 font-semibold text-sm">2</span>
          </div>
          <div className="flex-1">
            <Heading level="h3" className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
              Upload & Parse Your Data File
            </Heading>
            <Text className="text-gray-600 dark:text-gray-400 mb-4">
              Upload the completed Excel file with your supplier offerings data. The file will be automatically parsed and validated.
            </Text>

            {/* Category Selection Validation Message */}
            {!selectedCategoryId && (
              <div className="bg-yellow-50 dark:bg-yellow-900/20 p-3 rounded-md border border-yellow-200 dark:border-yellow-700 mb-4">
                <div className="flex items-center gap-2">
                  <AlertCircle className="w-4 h-4 text-yellow-600 dark:text-yellow-400" />
                  <Text className="text-sm font-medium text-yellow-700 dark:text-yellow-300">
                    Please select a category before uploading a file
                  </Text>
                </div>
                <Text className="text-xs text-yellow-600 dark:text-yellow-400 mt-1">
                  Category selection is required to validate your data against the correct template structure.
                </Text>
              </div>
            )}

            {/* File Upload Area */}
            <div
              className={`border-2 border-dashed rounded-lg p-8 text-center transition-colors ${
                !selectedCategoryId
                  ? "border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800/50 opacity-60"
                  : importFile
                  ? "border-green-300 dark:border-green-600 bg-green-50 dark:bg-green-900/20"
                  : "border-gray-300 dark:border-gray-600 hover:border-gray-400 dark:hover:border-gray-500"
                }`}
            >
              {importFile ? (
                <>
                  <CheckCircle className="w-12 h-12 text-green-500 mx-auto mb-4" />
                  <Text className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
                    File Selected: {importFile.name}
                  </Text>
                  <Text className="text-sm text-gray-600 dark:text-gray-400 mb-4">
                    Size: {(importFile.size / 1024).toFixed(1)} KB
                  </Text>
                  <Button
                    variant="secondary"
                    onClick={() => setImportFile(null)}
                    className="text-sm"
                  >
                    Choose Different File
                  </Button>
                </>
              ) : (
                <>
                  <Upload className={`w-12 h-12 mx-auto mb-4 ${
                    !selectedCategoryId
                      ? "text-gray-300 dark:text-gray-600"
                      : "text-gray-400 dark:text-gray-500"
                  }`} />
                  <Text className={`text-lg font-medium mb-2 ${
                    !selectedCategoryId
                      ? "text-gray-500 dark:text-gray-600"
                      : "text-gray-900 dark:text-gray-100"
                  }`}>
                    {!selectedCategoryId
                      ? "Select a category first to enable file upload"
                      : "Choose a file to upload"
                    }
                  </Text>
                  <Text className={`text-sm mb-4 ${
                    !selectedCategoryId
                      ? "text-gray-400 dark:text-gray-600"
                      : "text-gray-600 dark:text-gray-400"
                  }`}>
                    Excel (.xlsx, .xls) or CSV files supported
                  </Text>
                  <Input
                    ref={fileInputRef}
                    type="file"
                    accept=".xlsx,.xls,.csv"
                    onChange={handleFileSelect}
                    className="hidden"
                    disabled={!selectedCategoryId}
                  />
                  <Button
                    variant="secondary"
                    onClick={() => fileInputRef.current?.click()}
                    className={`mx-auto ${
                      !selectedCategoryId
                        ? "bg-gray-300 dark:bg-gray-600 text-gray-500 dark:text-gray-400 cursor-not-allowed"
                        : ""
                    }`}
                    disabled={!selectedCategoryId}
                  >
                    Select File
                  </Button>
                </>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Validation Errors Section */}
      {importErrors.length > 0 && (
        <div className="bg-white dark:bg-gray-800 rounded-lg border border-red-200 dark:border-red-700 shadow-sm">
          <div className="flex items-start gap-3 p-6">
            <div className="flex-shrink-0 w-8 h-8 bg-red-100 dark:bg-red-900 rounded-full flex items-center justify-center">
              <AlertCircle className="w-5 h-5 text-red-600 dark:text-red-400" />
            </div>
            <div className="flex-1">
              <Heading level="h3" className="text-lg font-medium text-red-900 dark:text-red-100 mb-2">
                Validation Errors Found
              </Heading>
              <Text className="text-red-600 dark:text-red-400 mb-4">
                Please fix the following errors before importing:
              </Text>
              <div className="max-h-64 overflow-y-auto">
                <Table>
                  <Table.Header>
                    <Table.Row>
                      <Table.HeaderCell>Row</Table.HeaderCell>
                      <Table.HeaderCell>Field</Table.HeaderCell>
                      <Table.HeaderCell>Error</Table.HeaderCell>
                      <Table.HeaderCell>Value</Table.HeaderCell>
                    </Table.Row>
                  </Table.Header>
                  <Table.Body>
                    {importErrors.map((error, index) => (
                      <Table.Row key={index}>
                        <Table.Cell>{error.row}</Table.Cell>
                        <Table.Cell>
                          <Badge>{error.field}</Badge>
                        </Table.Cell>
                        <Table.Cell className="text-red-600 dark:text-red-400">
                          {error.message}
                        </Table.Cell>
                        <Table.Cell className="font-mono text-sm">
                          {error.value ? String(error.value) : '-'}
                        </Table.Cell>
                      </Table.Row>
                    ))}
                  </Table.Body>
                </Table>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Backend Import Errors Section */}
      {importBackendErrors.length > 0 && (() => {
        const dateConflictErrors = importBackendErrors.filter(error =>
          error.message?.includes("Date range conflict") || error.message?.includes("Validity period overlaps")
        );
        const otherErrors = importBackendErrors.filter(error =>
          !error.message?.includes("Date range conflict") && !error.message?.includes("Validity period overlaps")
        );

        return (
          <div className="space-y-4">
            {/* Date Range Conflicts Section */}
            {dateConflictErrors.length > 0 && (
              <div className="bg-white dark:bg-gray-800 rounded-lg border border-orange-200 dark:border-orange-700 shadow-sm">
                <div className="flex items-start gap-3 p-6">
                  <div className="flex-shrink-0 w-8 h-8 bg-orange-100 dark:bg-orange-900 rounded-full flex items-center justify-center">
                    <AlertCircle className="w-5 h-5 text-orange-600 dark:text-orange-400" />
                  </div>
                  <div className="flex-1">
                    <Heading level="h3" className="text-lg font-medium text-orange-900 dark:text-orange-100 mb-2">
                      Date Range Conflicts ({dateConflictErrors.length})
                    </Heading>
                    <Text className="text-orange-600 dark:text-orange-400 mb-4">
                      The following supplier offerings have overlapping validity periods with existing offerings:
                    </Text>
                    <div className="max-h-64 overflow-y-auto">
                      <div className="space-y-3">
                        {dateConflictErrors.slice(0, 10).map((error, index) => (
                          <div key={index} className="p-3 bg-orange-50 dark:bg-orange-900/20 rounded-md border border-orange-200 dark:border-orange-800">
                            <div className="flex items-start justify-between mb-2">
                              <Text className="font-medium text-orange-900 dark:text-orange-100">
                                Row {error.index}
                              </Text>
                            </div>
                            <Text className="text-sm text-orange-700 dark:text-orange-300 mb-2">
                              {error.message}
                            </Text>
                            <div className="mt-2 p-2 bg-orange-100 dark:bg-orange-900/30 rounded text-xs text-orange-700 dark:text-orange-300">
                              <strong>💡 Solution:</strong> Adjust the validity dates or update the existing offering first.
                            </div>
                          </div>
                        ))}
                        {dateConflictErrors.length > 10 && (
                          <div className="text-center py-2">
                            <Text className="text-sm text-orange-600 dark:text-orange-400">
                              ... and {dateConflictErrors.length - 10} more date conflicts
                            </Text>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Other Errors Section */}
            {otherErrors.length > 0 && (
              <div className="bg-white dark:bg-gray-800 rounded-lg border border-red-200 dark:border-red-700 shadow-sm">
                <div className="flex items-start gap-3 p-6">
                  <div className="flex-shrink-0 w-8 h-8 bg-red-100 dark:bg-red-900 rounded-full flex items-center justify-center">
                    <AlertCircle className="w-5 h-5 text-red-600 dark:text-red-400" />
                  </div>
                  <div className="flex-1">
                    <Heading level="h3" className="text-lg font-medium text-red-900 dark:text-red-100 mb-2">
                      Other Import Errors ({otherErrors.length})
                    </Heading>
                    <Text className="text-red-600 dark:text-red-400 mb-4">
                      {importResult?.created > 0
                        ? `${importResult.created} supplier offerings were imported successfully, but ${otherErrors.length} failed:`
                        : `Failed to import ${otherErrors.length} supplier offerings:`
                      }
                    </Text>
                    <div className="max-h-64 overflow-y-auto">
                      <div className="space-y-3">
                        {otherErrors.slice(0, 20).map((error, index) => (
                          <div key={index} className="p-3 bg-red-50 dark:bg-red-900/20 rounded-md border border-red-200 dark:border-red-800">
                            <div className="flex items-start justify-between mb-2">
                              <Text className="font-medium text-red-900 dark:text-red-100">
                                Row {error.index}
                              </Text>
                            </div>
                            <Text className="text-sm text-red-700 dark:text-red-300 mb-2">
                              {error.message}
                            </Text>
                            {error.data && (
                              <details className="mt-2">
                                <summary className="text-xs text-red-600 dark:text-red-400 cursor-pointer hover:text-red-800 dark:hover:text-red-200">
                                  Show data
                                </summary>
                                <pre className="text-xs text-red-600 dark:text-red-400 mt-1 p-2 bg-red-100 dark:bg-red-900/30 rounded overflow-x-auto">
                                  {JSON.stringify(error.data, null, 2)}
                                </pre>
                              </details>
                            )}
                          </div>
                        ))}
                        {otherErrors.length > 20 && (
                          <div className="text-center py-2">
                            <Text className="text-sm text-red-600 dark:text-red-400">
                              ... and {otherErrors.length - 20} more errors
                            </Text>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        );
      })()}

      {/* File Preview Section */}
      {importFile && importData.length > 0 && !importSuccess && (() => {
        // Filter out completely empty rows
        const filteredData = importData.filter((row) => {
          return Object.values(row).some(value =>
            value !== null &&
            value !== undefined &&
            value !== '' &&
            String(value).trim() !== ''
          );
        }).slice(0, 10); // Limit to first 10 non-empty rows

        // Create lookup maps for ID resolution
        const supplierMap = new Map();
        const productServiceMap = new Map();
        const categoryMap = new Map();

        suppliers.forEach((supplier: any) => {
          supplierMap.set(supplier.id, supplier.name);
          supplierMap.set(supplier.name.toLowerCase(), supplier.name);
        });

        productsServices.forEach((ps: any) => {
          productServiceMap.set(ps.id, ps.name);
          productServiceMap.set(ps.name.toLowerCase(), ps.name);
        });

        categories.forEach((cat: any) => {
          categoryMap.set(cat.id, cat.name);
          categoryMap.set(cat.name.toLowerCase(), cat.name);
        });

        // Collect all unique custom field keys across all rows AND add dynamic fields from product services
        const customFieldKeys = new Set<string>();

        // First, add dynamic fields from product services (like create page)
        filteredData.forEach((row) => {
          if (row.product_service_name) {
            const productService = productsServices.find(
              (ps: any) => ps.name === row.product_service_name
            );

            if (productService?.category?.dynamic_field_schema) {
              // Filter fields that are used in supplier offerings (same as create page)
              const offeringFields = productService.category.dynamic_field_schema.filter(
                (field: any) => field.used_in_supplier_offering
              );

              // Add all dynamic field keys to the set
              offeringFields.forEach((field: any) => {
                customFieldKeys.add(field.key);
              });
            }
          }
        });

        // Then, collect existing custom field keys from the data
        filteredData.forEach((row) => {
          // Handle custom fields from the custom_fields object (like Products & Services)
          if (row.custom_fields && typeof row.custom_fields === 'object') {
            const customFieldsInRow = Object.keys(row.custom_fields);
            customFieldsInRow.forEach(key => customFieldKeys.add(key));
          }

          // Also handle prefixed custom field columns for backward compatibility
          Object.keys(row).forEach(key => {
            if (key.startsWith('custom_field_')) {
              customFieldKeys.add(key);
            }
          });
        });

        // Define standard columns matching the pricing calculator table structure (only input fields)
        const standardColumns = [
          'supplier_name',
          'product_service_name',
          'active_from',
          'active_to',
          'cost_currency',           // Cost Currency (auto-populated from supplier)
          'gross_cost',              // Gross Cost (input) - was gross_price
          'commission',              // Commission (%) (input)
          // 'net_cost' - REMOVED: Auto-calculated during import
          'margin_rate',             // Margin Rate (%) (input)
          // 'selling_price_cost_currency' - REMOVED: Auto-calculated during import
          // 'selling_price_default_currency' - REMOVED: Auto-calculated during import
          'availability_notes',
          'status'
        ];
        const customFieldColumns = Array.from(customFieldKeys).sort();
        const allColumns = [...standardColumns, ...customFieldColumns];

        // Transform data for display with ID resolution
        const transformedData = filteredData.map(row => {
          const transformed: any = {};

          // Handle standard fields with ID resolution
          standardColumns.forEach(col => {
            let value = row[col];

            if (col === 'supplier_name' && row.supplier_id) {
              // Resolve supplier ID to name
              const supplierName = supplierMap.get(row.supplier_id) || supplierMap.get(row.supplier_name?.toLowerCase());
              transformed[col] = supplierName || row.supplier_name || row.supplier_id || '—';
            } else if (col === 'product_service_name') {
              // Resolve product service name with custom field resolution
              let productService = null;

              // First try to find by ID
              if (row.product_service_id) {
                productService = productsServices.find(ps => ps.id === row.product_service_id);
              }

              // If not found by ID, try to find by name
              if (!productService && row.product_service_name) {
                productService = productsServices.find(ps => ps.name === row.product_service_name);
              }

              if (productService) {
                const resolvedName = resolveProductServiceName(productService, hotels, destinations);
                transformed[col] = resolvedName;
              } else {
                // Fallback to simple name lookup
                const psName = productServiceMap.get(row.product_service_id) || productServiceMap.get(row.product_service_name?.toLowerCase());
                transformed[col] = psName || row.product_service_name || row.product_service_id || '—';
              }
            } else if (col === 'category_name' && row.category_id) {
              // Resolve category ID to name
              const categoryName = categoryMap.get(row.category_id) || categoryMap.get(row.category_name?.toLowerCase());
              transformed[col] = categoryName || row.category_name || row.category_id || '—';
            } else {
              // Handle regular fields with special formatting for dates
              if (value !== null && value !== undefined && value !== '') {
                // Format date fields properly
                if (col === 'active_from' || col === 'active_to') {
                  const formattedDate = formatExcelDateForDisplay(value);
                  transformed[col] = formattedDate || String(value);
                } else {
                  transformed[col] = String(value);
                }
              } else {
                transformed[col] = '—';
              }
            }
          });

          // Add dynamic fields from product service (like create page)
          if (row.product_service_name) {
            const productService = productsServices.find(
              (ps: any) => ps.name === row.product_service_name
            );

            if (productService?.category?.dynamic_field_schema) {
              // Filter fields that are used in supplier offerings (same as create page)
              const offeringFields = productService.category.dynamic_field_schema.filter(
                (field: any) => field.used_in_supplier_offering
              );

              offeringFields.forEach((field: any) => {
                if (field.locked_in_offerings && productService.custom_fields?.[field.key] !== undefined) {
                  // Inherit value from product/service for locked fields
                  transformed[field.key] = productService.custom_fields[field.key];
                } else {
                  // For non-locked fields, check Excel data first, then fall back to default
                  let excelValue = null;

                  // Handle range fields that are split into _from and _to columns
                  if (field.type === 'time-range' || field.type === 'date-range' || field.type === 'date_availability') {
                    const fromFieldKey = `${field.key}_from`;
                    const toFieldKey = `${field.key}_to`;
                    const prefixedFromFieldKey = `custom_field_${fromFieldKey}`;
                    const prefixedToFieldKey = `custom_field_${toFieldKey}`;

                    const fromValue = row[prefixedFromFieldKey] || row[fromFieldKey];
                    const toValue = row[prefixedToFieldKey] || row[toFieldKey];

                    if ((fromValue && fromValue !== '' && fromValue !== '-') ||
                        (toValue && toValue !== '' && toValue !== '-')) {
                      // Format the range for display
                      if (fromValue && toValue && fromValue !== '-' && toValue !== '-') {
                        if (field.type === 'time-range') {
                          excelValue = `${fromValue} - ${toValue}`;
                        } else {
                          // For date ranges, format the dates nicely
                          const formattedFromDate = formatExcelDateForDisplay(fromValue);
                          const formattedToDate = formatExcelDateForDisplay(toValue);
                          excelValue = `${formattedFromDate || fromValue} - ${formattedToDate || toValue}`;
                        }
                      } else if (fromValue && fromValue !== '-') {
                        if (field.type === 'time-range') {
                          excelValue = `From ${fromValue}`;
                        } else {
                          const formattedFromDate = formatExcelDateForDisplay(fromValue);
                          excelValue = `From ${formattedFromDate || fromValue}`;
                        }
                      } else if (toValue && toValue !== '-') {
                        if (field.type === 'time-range') {
                          excelValue = `Until ${toValue}`;
                        } else {
                          const formattedToDate = formatExcelDateForDisplay(toValue);
                          excelValue = `Until ${formattedToDate || toValue}`;
                        }
                      }
                    }
                  } else {
                    // Handle non-range fields as before
                    // Check for direct field name in Excel columns (e.g., 'vehicle_type', 'pickup_location')
                    if (row[field.key] !== undefined && row[field.key] !== null && row[field.key] !== '') {
                      excelValue = row[field.key];
                    } else if (row.custom_fields?.[field.key] !== undefined && row.custom_fields?.[field.key] !== null && row.custom_fields?.[field.key] !== '') {
                      // Check in the custom_fields object as fallback
                      excelValue = row.custom_fields[field.key];
                    }
                  }

                  if (excelValue !== null) {
                    transformed[field.key] = excelValue;
                  } else {
                    // Fall back to default value if no Excel data
                    transformed[field.key] = field.default_value || '—';
                  }
                }
              });
            }
          }

          // Handle custom fields as separate columns (same as Products & Services)
          customFieldColumns.forEach(fieldKey => {
            let fieldValue = null;

            // First check if we already set this field from dynamic schema above
            if (transformed[fieldKey] !== undefined) {
              fieldValue = transformed[fieldKey];
            } else {
              // Check if it's a prefixed custom field column
              if (fieldKey.startsWith('custom_field_')) {
                fieldValue = row[fieldKey];
              } else {
                // Check in the custom_fields object
                fieldValue = row.custom_fields?.[fieldKey];
              }
            }

            if (fieldValue !== null && fieldValue !== undefined && fieldValue !== '') {
              if (Array.isArray(fieldValue)) {
                // Handle array values (multi-select, hotels, destinations, etc.)
                if (fieldValue.length > 0) {
                  // Try to resolve IDs to names if possible
                  const resolvedNames: string[] = [];

                  fieldValue.forEach((id: any) => {
                    if (typeof id === 'string') {
                      // Try to resolve as hotel ID
                      const hotel = hotels.find(h => h.id === id);
                      if (hotel) {
                        resolvedNames.push(hotel.name);
                        return;
                      }

                      // Try to resolve as destination ID
                      const destination = destinations.find(d => d.id === id);
                      if (destination) {
                        resolvedNames.push(destination.name);
                        return;
                      }

                      // If not resolved, keep original value
                      resolvedNames.push(String(id));
                    } else {
                      resolvedNames.push(String(id));
                    }
                  });

                  // Display resolved names or fallback to count
                  if (resolvedNames.length > 0) {
                    transformed[fieldKey] = resolvedNames.join(', ');
                  } else {
                    transformed[fieldKey] = `[${fieldValue.length} items]`;
                  }
                } else {
                  transformed[fieldKey] = '—';
                }
              } else if (typeof fieldValue === 'object') {
                transformed[fieldKey] = JSON.stringify(fieldValue);
              } else {
                // Try to resolve single ID to name
                const singleId = String(fieldValue);
                const hotel = hotels.find(h => h.id === singleId);
                const destination = destinations.find(d => d.id === singleId);

                if (hotel) {
                  transformed[fieldKey] = hotel.name;
                } else if (destination) {
                  transformed[fieldKey] = destination.name;
                } else {
                  transformed[fieldKey] = singleId;
                }
              }
            } else {
              // Only set '—' if we haven't already set a value from dynamic schema
              if (transformed[fieldKey] === undefined) {
                transformed[fieldKey] = '—';
              }
            }
          });

          return transformed;
        });

        return (
          <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 shadow-sm">
            <div className="p-6">
              <div className="flex items-center gap-2 mb-4">
                <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                <Text className="font-semibold text-gray-800 dark:text-gray-200">Supplier Offerings Data Preview</Text>
                <span className="bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 text-xs font-medium px-2 py-1 rounded-full">
                  {filteredData.length} rows
                </span>
              </div>

              <div className="border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden shadow-sm">
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                    <thead className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20">
                      <tr>
                        {allColumns.map((header) => {
                          // Create user-friendly header names
                          const headerDisplayNames: Record<string, string> = {
                            'supplier_name': 'Supplier Name',
                            'product_service_name': 'Product/Service Name',
                            'active_from': 'Active From',
                            'active_to': 'Active To',
                            'cost_currency': 'Cost Currency',
                            'gross_cost': 'Gross Cost',
                            'commission': 'Commission (%)',
                            'margin_rate': 'Margin Rate (%)',
                            'availability_notes': 'Availability Notes',
                            'status': 'Status'
                            // Note: net_cost, selling_price, selling_price_selling_currency are auto-calculated
                          };

                          const displayName = headerDisplayNames[header] || header.replace(/_/g, ' ').replace('custom field ', '');

                          return (
                            <th
                              key={header}
                              className="px-4 py-3 text-left text-xs font-semibold text-blue-800 dark:text-blue-200 uppercase tracking-wider"
                            >
                              {displayName}
                            </th>
                          );
                        })}
                      </tr>
                    </thead>
                    <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-100 dark:divide-gray-700">
                      {transformedData.map((row, rowIndex) => (
                        <tr
                          key={rowIndex}
                          className={rowIndex % 2 === 0 ? 'bg-white dark:bg-gray-800 hover:bg-blue-25 dark:hover:bg-blue-900/10' : 'bg-blue-25 dark:bg-blue-900/5 hover:bg-blue-50 dark:hover:bg-blue-900/20'}
                        >
                          {allColumns.map((colKey, cellIndex) => {
                            const value = row[colKey];
                            let displayValue = '—';

                            if (value !== null && value !== undefined && value !== '') {
                              // Format pricing fields
                              if (['commission', 'margin_rate'].includes(colKey)) {
                                // Format percentage fields
                                const numValue = parseFloat(String(value));
                                if (!isNaN(numValue)) {
                                  displayValue = `${(numValue).toFixed(1)}%`;
                                } else {
                                  displayValue = String(value);
                                }
                              } else if (['gross_cost'].includes(colKey)) {
                                // Format monetary fields
                                const numValue = parseFloat(String(value));
                                if (!isNaN(numValue)) {
                                  displayValue = numValue.toFixed(2);
                                } else {
                                  displayValue = String(value);
                                }
                              } else if (colKey === 'cost_currency') {
                                // Format currency code
                                displayValue = String(value).toUpperCase();
                              } else {
                                displayValue = String(value);
                              }
                            }

                            // Determine if field should be truncated
                            const shouldTruncate = ['availability_notes', 'product_service_name'].includes(colKey);
                            const maxLength = shouldTruncate ? (colKey === 'product_service_name' ? 80 : 50) : 100;
                            const truncatedValue = displayValue.length > maxLength
                              ? displayValue.substring(0, maxLength) + '...'
                              : displayValue;
                            const isTruncated = displayValue.length > maxLength;

                            return (
                              <td
                                key={cellIndex}
                                className="px-4 py-3 text-sm text-gray-700 dark:text-gray-300 font-medium"
                                title={isTruncated ? `Full text: ${displayValue}` : undefined}
                              >
                                <div className="flex items-center gap-1">
                                  <span>{truncatedValue}</span>
                                </div>
                              </td>
                            );
                          })}
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>

              <div className="mt-3 flex items-center gap-2">
                <div className="w-2 h-2 bg-blue-400 rounded-full"></div>
                <Text className="text-blue-600 dark:text-blue-400 text-sm font-medium">
                  Showing first {filteredData.length} rows • Ready for import
                </Text>
              </div>
            </div>
          </div>
        );
      })()}

      {/* Success Message */}
      {importSuccess && (
        <div className="bg-white dark:bg-gray-800 rounded-lg border border-green-200 dark:border-green-700 shadow-sm">
          <div className="flex items-start gap-3 p-6">
            <div className="flex-shrink-0 w-8 h-8 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center">
              <CheckCircle className="w-5 h-5 text-green-600 dark:text-green-400" />
            </div>
            <div className="flex-1">
              <Heading level="h3" className="text-lg font-medium text-green-900 dark:text-green-100 mb-2">
                Import Completed Successfully!
              </Heading>
              <Text className="text-green-600 dark:text-green-400">
                Your supplier offerings have been imported and are now available in the supplier offerings list.
              </Text>
              <div className="bg-green-50 dark:bg-green-900/20 p-3 rounded-md border border-green-200 dark:border-green-700 mt-4">
                <Text className="text-sm text-green-700 dark:text-green-300">
                  ✓ Data imported and validated
                  <br />
                  ✓ Records added to database
                  <br />✓ Available in supplier offerings list
                </Text>
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  );







  return (
    <FocusModal open={true} onOpenChange={handleClose}>
      <FocusModal.Content className="flex flex-col h-full max-h-[98vh] bg-gray-50 dark:bg-gray-900">
        <FocusModal.Header className="flex-shrink-0 bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
          <div className="flex justify-between items-center w-full py-4 px-6">
            <div className="flex items-center gap-3">
              <Heading
                level="h2"
                className="text-xl font-semibold text-gray-900 dark:text-gray-100"
              >
                Import Supplier Offerings
              </Heading>
            </div>
          </div>

          {/* Progress Indicator */}
          <div className="px-6 py-2 border-b border-gray-200 dark:border-gray-700">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                {importSteps.map((step, index) => {
                  const status = getStepStatus(step.id);
                  const isCompleted = status === 'completed';
                  const isCurrent = status === 'current';

                  return (
                    <React.Fragment key={step.id}>
                      <div className="flex items-center">
                        <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-semibold ${isCompleted
                            ? "bg-green-600 dark:bg-green-500 text-white"
                            : isCurrent
                              ? "bg-blue-600 dark:bg-blue-500 text-white"
                              : "bg-gray-300 dark:bg-gray-600 text-gray-600 dark:text-gray-400"
                          }`}>
                          {isCompleted ? "✓" : step.id}
                        </div>
                        <span className={`ml-2 text-sm font-medium ${isCompleted
                            ? "text-green-600 dark:text-green-400"
                            : isCurrent
                              ? "text-blue-600 dark:text-blue-400"
                              : "text-gray-500 dark:text-gray-400"
                          }`}>
                          {step.id === 4 && importSuccess ? "Complete" : step.title}
                        </span>
                      </div>
                      {index < importSteps.length - 1 && (
                        <div className={`w-12 h-0.5 ${isCompleted ? "bg-green-300 dark:bg-green-600" : "bg-gray-300 dark:bg-gray-600"
                          }`}></div>
                      )}
                    </React.Fragment>
                  );
                })}
              </div>
            </div>
          </div>
        </FocusModal.Header>

        <FocusModal.Body className="flex flex-col flex-grow overflow-hidden">
          <div className="flex-grow overflow-y-auto p-6 space-y-6">
            {renderImportContent()}
          </div>

          {/* Footer */}
          <div className="flex-shrink-0 py-6 px-8 bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700">
            <div className="flex items-center justify-between">
              <div className="text-sm text-gray-500 dark:text-gray-400">
                {importSuccess ? (
                  <span className="text-green-600 dark:text-green-400 font-medium">
                    ✅ Successfully imported {importedCount} records
                  </span>
                ) : importFile ? (
                  <>
                    <span className="text-green-600 dark:text-green-400">
                      ✓
                    </span>{" "}
                    {importFile.name}
                    {importData.length > 0 && importErrors.length === 0 && (
                      <span className="text-green-600 dark:text-green-400">
                        {" "}
                        • {importData.length} records ready
                      </span>
                    )}
                    {importErrors.length > 0 && (
                      <span className="text-red-600 dark:text-red-400">
                        {" "}
                        • {importErrors.length} errors found
                      </span>
                    )}
                  </>
                ) : (
                  "Select a file to automatically parse and import"
                )}
              </div>
              <div className="flex gap-4">
                <Button
                  variant="secondary"
                  onClick={resetImportModal}
                  className="px-6 py-3 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600 border-gray-300 dark:border-gray-600 font-medium"
                >
                  Cancel
                </Button>
                <Button
                  variant="primary"
                  onClick={handleImport}
                  disabled={
                    isImporting ||
                    importSuccess ||
                    !importFile ||
                    importData.length === 0 ||
                    importErrors.length > 0
                  }
                  className={`flex items-center gap-3 px-6 py-3 shadow-lg font-medium disabled:opacity-50 disabled:cursor-not-allowed ${importSuccess
                      ? "bg-green-600 dark:bg-green-500 text-white"
                      : "bg-blue-600 dark:bg-blue-500 hover:bg-blue-700 dark:hover:bg-blue-600 text-white"
                    }`}
                >
                  {importSuccess ? (
                    <>
                      <CheckCircle className="w-5 h-5" />
                      Import Complete
                    </>
                  ) : (
                    <>
                      <Upload className="w-5 h-5" />
                      {isImporting
                        ? "Importing..."
                        : `Import Data${importData.length > 0
                          ? ` (${importData.length} Records)`
                          : ""
                        }`}
                    </>
                  )}
                </Button>
              </div>
            </div>
          </div>
        </FocusModal.Body>


      </FocusModal.Content>
    </FocusModal>
  );
};

export default SupplierOfferingImportModal;
