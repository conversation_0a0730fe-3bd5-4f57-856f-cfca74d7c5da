import { defineRouteConfig } from "@camped-ai/admin-sdk";
import { ArrowLeft, Edit, Package, Settings, Tag, Building, User } from "lucide-react";
import {
  Container,
  Heading,
  Text,
  Button,
  Badge,
  Toaster,
  Tabs,
} from "@camped-ai/ui";
import { useNavigate, useParams } from "react-router-dom";
import { useState, useEffect } from "react";
import PermissionBasedSidebarHider from "../../../../../widgets/permission-based-sidebar-hider";
import {
  useCategory,
  useCategoryUsageCheck,
} from "../../../../../hooks/supplier-products-services/use-categories";
import { useRbac } from "../../../../../hooks/use-rbac";
import { format } from "date-fns";

const ViewCategoryPage = () => {
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();
  const { hasPermission } = useRbac();
  const [usageData, setUsageData] = useState<any>(null);
  const [usageLoading, setUsageLoading] = useState(false);
  const [activeTab, setActiveTab] = useState<"supplier" | "customer">("supplier");

  // Helper functions to filter fields by context
  const getSupplierFields = () => {
    if (!category?.dynamic_field_schema) return [];
    return category.dynamic_field_schema.filter(
      (field: any) => !field.field_context || field.field_context === "supplier"
    );
  };

  const getCustomerFields = () => {
    if (!category?.dynamic_field_schema) return [];
    return category.dynamic_field_schema.filter(
      (field: any) => field.field_context === "customer"
    );
  };

  const handleTabChange = (value: string) => {
    if (value === "supplier" || value === "customer") {
      setActiveTab(value);
    }
  };

  // Fetch category data
  const {
    data: categoryData,
    isLoading: categoryLoading,
    error: categoryError,
  } = useCategory(id!);
  const category = categoryData?.category;

  // Usage check mutation
  const categoryUsageCheck = useCategoryUsageCheck();

  // Fetch usage data when category is loaded
  useEffect(() => {
    if (category?.id) {
      setUsageLoading(true);
      categoryUsageCheck.mutateAsync(category.id)
        .then((data) => {
          setUsageData(data.usage);
        })
        .catch((error) => {
          console.error("Failed to fetch usage data:", error);
        })
        .finally(() => {
          setUsageLoading(false);
        });
    }
  }, [category?.id]);

  const handleEdit = () => {
    navigate(`/supplier-management/config/categories/${id}/edit`);
  };

  const handleGoBack = () => {
    navigate("/supplier-management/config/categories");
  };

  if (categoryLoading) {
    return (
      <>
        <PermissionBasedSidebarHider />
        <Container>
          <div className="flex items-center justify-center py-8">
            <div className="animate-pulse space-y-4 w-full max-w-2xl">
              <div className="h-8 bg-ui-bg-subtle rounded w-1/3"></div>
              <div className="h-4 bg-ui-bg-subtle rounded w-2/3"></div>
              <div className="space-y-3">
                <div className="h-32 bg-ui-bg-subtle rounded"></div>
                <div className="h-24 bg-ui-bg-subtle rounded"></div>
              </div>
            </div>
          </div>
        </Container>
      </>
    );
  }

  if (categoryError || !category) {
    return (
      <>
        <PermissionBasedSidebarHider />
        <Container>
          <div className="text-center py-8">
            <Heading>Category Not Found</Heading>
            <Text className="mt-2">
              The category you're looking for doesn't exist or has been removed.
            </Text>
            <Button className="mt-4" onClick={handleGoBack}>
              Back to Categories
            </Button>
          </div>
        </Container>
      </>
    );
  }

  return (
    <>
      <PermissionBasedSidebarHider />
      <Toaster />
      <Container>
        {/* Header */}
        <div className="flex items-center gap-4 mb-6">
          <Button
            variant="secondary"
            size="small"
            onClick={handleGoBack}
          >
            <ArrowLeft className="h-4 w-4" />
          </Button>

          <div className="flex-1">
            <div className="flex items-center gap-3">
              <Heading level="h1">{category.name}</Heading>
              <Badge
                className={`px-2 py-1 text-xs rounded-full ${
                  category.is_active
                    ? "bg-green-100 text-green-800 border border-green-200"
                    : "bg-gray-100 text-gray-800 border border-gray-200"
                }`}
              >
                {category.is_active ? "Active" : "Inactive"}
              </Badge>
            </div>
            <Text className="text-ui-fg-subtle mt-1">
              View category details and configuration
            </Text>
          </div>

          {hasPermission("supplier_management:update") && (
            <Button onClick={handleEdit}>
              <Edit className="h-4 w-4 mr-2" />
              Edit Category
            </Button>
          )}
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Main Information */}
          <div className="lg:col-span-2 space-y-6">
            {/* Basic Information */}
            <div className="p-6 bg-white border border-ui-border-base rounded-lg shadow-sm">
              <div className="flex items-center gap-2 mb-4">
                <Tag className="h-5 w-5 text-ui-fg-subtle" />
                <Heading level="h2">Basic Information</Heading>
              </div>

              <div className="space-y-4">
                <div>
                  <Text className="text-sm font-medium text-ui-fg-subtle mb-1">
                    Name
                  </Text>
                  <Text>{category.name}</Text>
                </div>

                {category.description && (
                  <div>
                    <Text className="text-sm font-medium text-ui-fg-subtle mb-1">
                      Description
                    </Text>
                    <Text>{category.description}</Text>
                  </div>
                )}

                <div>
                  <Text className="text-sm font-medium text-ui-fg-subtle mb-1">
                    Category Type
                  </Text>
                  <Badge className="px-2 py-1 text-xs rounded-full bg-blue-100 text-blue-800 border border-blue-200">
                    {category.category_type}
                  </Badge>
                </div>

                {category.icon && (
                  <div>
                    <Text className="text-sm font-medium text-ui-fg-subtle mb-1">
                      Icon
                    </Text>
                    <Text className="font-mono text-sm">{category.icon}</Text>
                  </div>
                )}
              </div>
            </div>

            {/* Dynamic Field Schema with Tabs */}
            <div className="p-6 bg-white border border-ui-border-base rounded-lg shadow-sm">
              <div className="flex items-center gap-2 mb-4">
                <Settings className="h-5 w-5 text-ui-fg-subtle" />
                <Heading level="h2">Dynamic Field Configuration</Heading>
              </div>

              <Tabs value={activeTab} onValueChange={handleTabChange} className="w-full">
                <Tabs.List className="grid w-full grid-cols-2 mb-6">
                  <Tabs.Trigger value="supplier" className="flex items-center gap-2">
                    <Building size={18} />
                    <span>Supplier Fields</span>
                    <span className="ml-1 px-2 py-0.5 text-xs bg-blue-100 text-blue-800 rounded-full">
                      {getSupplierFields().length}
                    </span>
                  </Tabs.Trigger>
                  <Tabs.Trigger value="customer" className="flex items-center gap-2">
                    <User size={18} />
                    <span>Customer Fields</span>
                    <span className="ml-1 px-2 py-0.5 text-xs bg-green-100 text-green-800 rounded-full">
                      {getCustomerFields().length}
                    </span>
                  </Tabs.Trigger>
                </Tabs.List>

                <Tabs.Content value="supplier" className="mt-0">
                  <div className="space-y-4">
                    <Text size="small" className="text-ui-fg-subtle">
                      Fields that appear in supplier management forms (products, services, offerings)
                    </Text>
                    {getSupplierFields().length > 0 ? (
                      <div className="space-y-3">
                        {getSupplierFields().map((field: any, index: number) => (
                          <div key={index} className="border border-ui-border-base rounded-lg p-4">
                            <div className="flex items-center justify-between mb-2">
                              <Text className="font-medium">{field.label}</Text>
                              <div className="flex items-center gap-2">
                                <Badge className="px-2 py-1 text-xs rounded-full bg-gray-100 text-gray-800 border border-gray-200">
                                  {field.type}
                                </Badge>
                                <Badge className="px-2 py-1 text-xs rounded-full bg-blue-100 text-blue-800 border border-blue-200">
                                  📦 supplier
                                </Badge>
                              </div>
                            </div>

                            <div className="grid grid-cols-2 gap-4 text-sm">
                              <div>
                                <Text className="text-ui-fg-subtle">Field Key:</Text>
                                <Text className="font-mono">{field.key}</Text>
                              </div>
                              <div>
                                <Text className="text-ui-fg-subtle">Required:</Text>
                                <Text>{field.required ? "Yes" : "No"}</Text>
                              </div>
                            </div>

                            {field.options && field.options.length > 0 && (
                              <div className="mt-2">
                                <Text className="text-ui-fg-subtle text-sm">Options:</Text>
                                <div className="flex flex-wrap gap-1 mt-1">
                                  {field.options.map((option: any, optIndex: number) => (
                                    <Badge
                                      key={optIndex}
                                      className="text-xs px-2 py-1 rounded-full bg-gray-100 text-gray-800 border border-gray-200"
                                    >
                                      {option}
                                    </Badge>
                                  ))}
                                </div>
                              </div>
                            )}

                            {(field.used_in_filtering || field.used_in_supplier_offering || field.used_in_product) && (
                              <div className="mt-2">
                                <Text className="text-ui-fg-subtle text-sm">Used in:</Text>
                                <div className="flex flex-wrap gap-1 mt-1">
                                  {field.used_in_filtering && (
                                    <Badge className="text-xs px-2 py-1 rounded-full bg-blue-100 text-blue-800 border border-blue-200">
                                      Filtering
                                    </Badge>
                                  )}
                                  {field.used_in_supplier_offering && (
                                    <Badge className="text-xs px-2 py-1 rounded-full bg-green-100 text-green-800 border border-green-200">
                                      Supplier Offering
                                    </Badge>
                                  )}
                                  {field.used_in_product && (
                                    <Badge className="text-xs px-2 py-1 rounded-full bg-purple-100 text-purple-800 border border-purple-200">
                                      Product
                                    </Badge>
                                  )}
                                </div>
                              </div>
                            )}
                          </div>
                        ))}
                      </div>
                    ) : (
                      <Text className="text-ui-fg-subtle">No supplier fields configured for this category.</Text>
                    )}
                  </div>
                </Tabs.Content>

                <Tabs.Content value="customer" className="mt-0">
                  <div className="space-y-4">
                    <Text size="small" className="text-ui-fg-subtle">
                      Fields that appear in customer booking forms for add-ons
                    </Text>
                    {getCustomerFields().length > 0 ? (
                      <div className="space-y-3">
                        {getCustomerFields().map((field: any, index: number) => (
                          <div key={index} className="border border-ui-border-base rounded-lg p-4">
                            <div className="flex items-center justify-between mb-2">
                              <Text className="font-medium">{field.label}</Text>
                              <div className="flex items-center gap-2">
                                <Badge className="px-2 py-1 text-xs rounded-full bg-gray-100 text-gray-800 border border-gray-200">
                                  {field.type}
                                </Badge>
                                <Badge className="px-2 py-1 text-xs rounded-full bg-green-100 text-green-800 border border-green-200">
                                  👤 customer
                                </Badge>
                              </div>
                            </div>

                            <div className="grid grid-cols-2 gap-4 text-sm">
                              <div>
                                <Text className="text-ui-fg-subtle">Field Key:</Text>
                                <Text className="font-mono">{field.key}</Text>
                              </div>
                              <div>
                                <Text className="text-ui-fg-subtle">Required:</Text>
                                <Text>{field.required ? "Yes" : "No"}</Text>
                              </div>
                            </div>

                            {field.options && field.options.length > 0 && (
                              <div className="mt-2">
                                <Text className="text-ui-fg-subtle text-sm">Options:</Text>
                                <div className="flex flex-wrap gap-1 mt-1">
                                  {field.options.map((option: any, optIndex: number) => (
                                    <Badge
                                      key={optIndex}
                                      className="text-xs px-2 py-1 rounded-full bg-gray-100 text-gray-800 border border-gray-200"
                                    >
                                      {option}
                                    </Badge>
                                  ))}
                                </div>
                              </div>
                            )}

                            {(field.used_in_filtering || field.used_in_supplier_offering || field.used_in_product) && (
                              <div className="mt-2">
                                <Text className="text-ui-fg-subtle text-sm">Used in:</Text>
                                <div className="flex flex-wrap gap-1 mt-1">
                                  {field.used_in_filtering && (
                                    <Badge className="text-xs px-2 py-1 rounded-full bg-blue-100 text-blue-800 border border-blue-200">
                                      Filtering
                                    </Badge>
                                  )}
                                  {field.used_in_supplier_offering && (
                                    <Badge className="text-xs px-2 py-1 rounded-full bg-green-100 text-green-800 border border-green-200">
                                      Supplier Offering
                                    </Badge>
                                  )}
                                  {field.used_in_product && (
                                    <Badge className="text-xs px-2 py-1 rounded-full bg-purple-100 text-purple-800 border border-purple-200">
                                      Product
                                    </Badge>
                                  )}
                                </div>
                              </div>
                            )}
                          </div>
                        ))}
                      </div>
                    ) : (
                      <Text className="text-ui-fg-subtle">No customer fields configured for this category.</Text>
                    )}
                  </div>
                </Tabs.Content>
              </Tabs>
            </div>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Usage Statistics */}
            <div className="p-6 bg-white border border-ui-border-base rounded-lg shadow-sm">
              <div className="flex items-center gap-2 mb-4">
                <Package className="h-5 w-5 text-ui-fg-subtle" />
                <Heading level="h2">Usage</Heading>
              </div>
              
              <div className="space-y-3">
                <div className="flex justify-between items-center">
                  <Text className="text-sm text-ui-fg-subtle">Products</Text>
                  <Text className="font-medium">
                    {usageLoading ? "..." : (usageData?.productCount || 0)}
                  </Text>
                </div>
                <div className="flex justify-between items-center">
                  <Text className="text-sm text-ui-fg-subtle">Services</Text>
                  <Text className="font-medium">
                    {usageLoading ? "..." : (usageData?.serviceCount || 0)}
                  </Text>
                </div>
                <div className="border-t border-ui-border-base pt-3">
                  <div className="flex justify-between items-center">
                    <Text className="text-sm font-medium">Total</Text>
                    <Text className="font-medium">
                      {usageLoading ? "..." : (usageData?.totalCount || 0)}
                    </Text>
                  </div>
                </div>
              </div>
            </div>

            {/* Metadata */}
            {/* <div className="p-6 bg-white border border-ui-border-base rounded-lg shadow-sm">
              <Heading level="h2" className="mb-4">Metadata</Heading>
              
              <div className="space-y-3 text-sm">
                <div>
                  <Text className="text-ui-fg-subtle">Created</Text>
                  <Text>{format(new Date(category.created_at), "PPp")}</Text>
                </div>
                <div>
                  <Text className="text-ui-fg-subtle">Last Updated</Text>
                  <Text>{format(new Date(category.updated_at), "PPp")}</Text>
                </div>
                <div>
                  <Text className="text-ui-fg-subtle">Category ID</Text>
                  <Text className="font-mono text-xs">{category.id}</Text>
                </div>
              </div>
            </div> */}
          </div>
        </div>
      </Container>
    </>
  );
};

export const config = defineRouteConfig({
  label: "View Category",
});

export default ViewCategoryPage;
