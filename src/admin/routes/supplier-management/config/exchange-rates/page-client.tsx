import { Plus<PERSON>ini } from "@camped-ai/icons";
import {
  Edit,
  MoreHorizontal,
  Trash,
  Calendar,
  TrendingUp,
  X,
  DollarSign,
} from "lucide-react";
import {
  Container,
  Heading,
  Text,
  Button,
  Table,
  Badge,
  Toaster,
  DropdownMenu,
  Select,
  Prompt,
  DatePicker,
} from "@camped-ai/ui";
import { useState } from "react";
import PermissionBasedSidebarHider from "../../../../widgets/permission-based-sidebar-hider";
import { useRbac } from "../../../../hooks/use-rbac";
import { RoleGuard } from "../../../../components/rbac/RoleGuard";
import {
  useExchangeRates,
  useDeleteExchangeRate,
  type ExchangeRate,
  type ExchangeRateFilters,
} from "../../../../hooks/supplier-management/use-exchange-rates";
import { CreateExchangeRateModal } from "./components/CreateExchangeRateModal";
import { EditExchangeRateModal } from "./components/EditExchangeRateModal";

// Common currency codes
const CURRENCY_OPTIONS = [
  { value: "USD", label: "USD - US Dollar" },
  { value: "EUR", label: "EUR - Euro" },
  { value: "CHF", label: "CHF - Swiss Franc" },
  { value: "GBP", label: "GBP - British Pound" },
  { value: "JPY", label: "JPY - Japanese Yen" },
  { value: "CAD", label: "CAD - Canadian Dollar" },
  { value: "AUD", label: "AUD - Australian Dollar" },
  { value: "CNY", label: "CNY - Chinese Yuan" },
  { value: "INR", label: "INR - Indian Rupee" },
];

const PageClient = () => {
  const { hasPermission, hasAnyPermission } = useRbac();

  // Check if user has both required permissions
  const hasSupplierManagementAccess = hasPermission("supplier_management:view");
  const hasExchangeRateAccess = hasPermission("exchange_rate_management:view");
  const hasFullAccess = hasSupplierManagementAccess && hasExchangeRateAccess;

  // If user doesn't have both permissions, show access denied
  if (!hasFullAccess) {
    return (
      <>
        <PermissionBasedSidebarHider />
        <Container>
          <div className="p-8 text-center">
            <Heading level="h1">Access Denied</Heading>
            <Text className="mt-2">
              You need both Supplier Management and Exchange Rate Management permissions to access this page.
              <br />
              Required permissions: supplier_management:view AND exchange_rate_management:view
            </Text>
          </div>
        </Container>
      </>
    );
  }

  // State for filters
  const [filters, setFilters] = useState<ExchangeRateFilters>({
    limit: 50,
    offset: 0,
    sort_by: "date",
    sort_order: "desc",
  });

  // State for modals
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [selectedExchangeRate, setSelectedExchangeRate] =
    useState<ExchangeRate | null>(null);

  // State for delete confirmation
  const [showDeletePrompt, setShowDeletePrompt] = useState(false);
  const [exchangeRateToDelete, setExchangeRateToDelete] =
    useState<ExchangeRate | null>(null);

  // Hooks
  const { data: exchangeRatesData, isLoading } = useExchangeRates(filters);
  const deleteExchangeRate = useDeleteExchangeRate();

  const exchangeRates = exchangeRatesData?.exchange_rates || [];
  const totalCount = exchangeRatesData?.count || 0;

  // Filter handlers
  const handleFilterChange = (key: keyof ExchangeRateFilters, value: any) => {
    setFilters((prev) => ({
      ...prev,
      [key]: value,
      offset: 0, // Reset to first page when filters change
    }));
  };

  const clearFilters = () => {
    setFilters({
      limit: 50,
      offset: 0,
      sort_by: "date",
      sort_order: "desc",
    });
  };

  // Action handlers
  const handleEdit = (exchangeRate: ExchangeRate) => {
    setSelectedExchangeRate(exchangeRate);
    setShowEditModal(true);
  };

  const handleDeleteClick = (exchangeRate: ExchangeRate) => {
    setExchangeRateToDelete(exchangeRate);
    setShowDeletePrompt(true);
  };

  const handleDeleteConfirm = async () => {
    if (exchangeRateToDelete) {
      try {
        await deleteExchangeRate.mutateAsync(exchangeRateToDelete.id);
        setShowDeletePrompt(false);
        setExchangeRateToDelete(null);
      } catch (error) {
        // Error is handled by the hook
      }
    }
  };

  // Format currency pair display
  const formatCurrencyPair = (
    baseCurrency: string,
    sellingCurrency: string
  ) => {
    return `${baseCurrency}/${sellingCurrency}`;
  };

  // Format exchange rate display
  const formatExchangeRate = (rate: number | string) => {
    const numericRate = typeof rate === "string" ? parseFloat(rate) : rate;
    return isNaN(numericRate) ? "0.0000" : numericRate.toFixed(4);
  };

  // Format date display
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  // Check if filters are active
  const hasActiveFilters = Object.keys(filters).some((key) => {
    if (
      key === "limit" ||
      key === "offset" ||
      key === "sort_by" ||
      key === "sort_order"
    )
      return false;
    return (
      filters[key as keyof ExchangeRateFilters] !== undefined &&
      filters[key as keyof ExchangeRateFilters] !== ""
    );
  });

  return (
    <>
      <PermissionBasedSidebarHider />
      <Container className="divide-y p-0">
        {/* Header */}
        <div className="flex items-center justify-between px-6 py-4">
          <div>
            <Heading level="h2">Exchange Rates</Heading>
            <Text className="text-ui-fg-subtle">
              Manage currency exchange rates for transactions
            </Text>
          </div>
          <div className="flex items-center gap-x-2">
            {hasPermission("exchange_rate_management:create") && (
              <Button size="small" onClick={() => setShowCreateModal(true)}>
                <PlusMini />
                Add Exchange Rate
              </Button>
            )}
          </div>
        </div>

        {/* Filters */}
        <div className="px-6 py-4 space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {/* Date Range Filters */}
            <div>
              <Text className="text-sm font-medium mb-2">Date From</Text>
              <DatePicker
                value={
                  filters.date_from ? new Date(filters.date_from) : undefined
                }
                onChange={(date) =>
                  handleFilterChange(
                    "date_from",
                    date?.toISOString().split("T")[0]
                  )
                }
              />
            </div>
            <div>
              <Text className="text-sm font-medium mb-2">Date To</Text>
              <DatePicker
                value={filters.date_to ? new Date(filters.date_to) : undefined}
                onChange={(date) =>
                  handleFilterChange(
                    "date_to",
                    date?.toISOString().split("T")[0]
                  )
                }
              />
            </div>

            {/* Currency Filters */}
            <div>
              <Text className="text-sm font-medium mb-2">Base Currency</Text>
              <Select
                value={filters.base_currency || undefined}
                onValueChange={(value) =>
                  handleFilterChange(
                    "base_currency",
                    value === "clear" ? undefined : value
                  )
                }
              >
                <Select.Trigger>
                  <Select.Value placeholder="All base currencies" />
                </Select.Trigger>
                <Select.Content>
                  <Select.Item value="clear">All base currencies</Select.Item>
                  {CURRENCY_OPTIONS.map((option) => (
                    <Select.Item key={option.value} value={option.value}>
                      {option.label}
                    </Select.Item>
                  ))}
                </Select.Content>
              </Select>
            </div>
            <div>
              <Text className="text-sm font-medium mb-2">Selling Currency</Text>
              <Select
                value={filters.selling_currency || undefined}
                onValueChange={(value) =>
                  handleFilterChange(
                    "selling_currency",
                    value === "clear" ? undefined : value
                  )
                }
              >
                <Select.Trigger>
                  <Select.Value placeholder="All selling currencies" />
                </Select.Trigger>
                <Select.Content>
                  <Select.Item value="clear">
                    All selling currencies
                  </Select.Item>
                  {CURRENCY_OPTIONS.map((option) => (
                    <Select.Item key={option.value} value={option.value}>
                      {option.label}
                    </Select.Item>
                  ))}
                </Select.Content>
              </Select>
            </div>
          </div>

          {/* Clear Filters */}
          {hasActiveFilters && (
            <div className="flex items-center gap-2">
              <Button variant="secondary" size="small" onClick={clearFilters}>
                <X className="w-4 h-4" />
                Clear Filters
              </Button>
              <Text className="text-sm text-ui-fg-subtle">
                {totalCount} exchange rate{totalCount !== 1 ? "s" : ""} found
              </Text>
            </div>
          )}
        </div>

        {/* Exchange Rates Table */}
        <div className="overflow-hidden">
          <Table>
            <Table.Header>
              <Table.Row>
                <Table.HeaderCell>Date</Table.HeaderCell>
                <Table.HeaderCell>Currency Pair</Table.HeaderCell>
                <Table.HeaderCell>Exchange Rate</Table.HeaderCell>
                <Table.HeaderCell>Created</Table.HeaderCell>
                <Table.HeaderCell>Updated</Table.HeaderCell>
                <Table.HeaderCell className="w-[50px]"></Table.HeaderCell>
              </Table.Row>
            </Table.Header>
            <Table.Body>
              {isLoading ? (
                <Table.Row>
                  <Table.Cell colSpan={6} className="text-center py-8">
                    <Text>Loading exchange rates...</Text>
                  </Table.Cell>
                </Table.Row>
              ) : exchangeRates.length === 0 ? (
                <Table.Row>
                  <Table.Cell colSpan={6} className="text-center py-12">
                    <div className="flex flex-col items-center gap-3">
                      <DollarSign className="w-12 h-12 text-ui-fg-muted" />
                      <Text className="font-medium text-lg">
                        No exchange rates found
                      </Text>
                      <Text className="text-ui-fg-subtle">
                        {hasActiveFilters
                          ? "Try adjusting your filters"
                          : "Get started by creating your first exchange rate"}
                      </Text>
                    </div>
                  </Table.Cell>
                </Table.Row>
              ) : (
                exchangeRates.map((exchangeRate) => (
                  <Table.Row key={exchangeRate.id}>
                    <Table.Cell>
                      <div className="flex items-center gap-2">
                        <Calendar className="w-4 h-4 text-ui-fg-subtle" />
                        <Text className="font-medium">
                          {formatDate(exchangeRate.date)}
                        </Text>
                      </div>
                    </Table.Cell>
                    <Table.Cell>
                      <Badge>
                        {formatCurrencyPair(
                          exchangeRate.base_currency,
                          exchangeRate.selling_currency
                        )}
                      </Badge>
                    </Table.Cell>
                    <Table.Cell>
                      <div className="flex items-center gap-2">
                        <TrendingUp className="w-4 h-4 text-ui-fg-subtle" />
                        <Text className="font-mono">
                          {formatExchangeRate(exchangeRate.exchange_rate)}
                        </Text>
                      </div>
                    </Table.Cell>
                    <Table.Cell>
                      <Text className="text-ui-fg-subtle">
                        {formatDate(exchangeRate.created_at)}
                      </Text>
                    </Table.Cell>
                    <Table.Cell>
                      <Text className="text-ui-fg-subtle">
                        {formatDate(exchangeRate.updated_at)}
                      </Text>
                    </Table.Cell>
                    <Table.Cell>
                      <DropdownMenu>
                        <DropdownMenu.Trigger asChild>
                          <Button variant="secondary" size="small">
                            <MoreHorizontal className="w-4 h-4" />
                          </Button>
                        </DropdownMenu.Trigger>
                        <DropdownMenu.Content align="end">
                          {hasPermission("exchange_rate_management:edit") && (
                            <DropdownMenu.Item
                              onClick={() => handleEdit(exchangeRate)}
                            >
                              <Edit className="w-4 h-4" />
                              Edit
                            </DropdownMenu.Item>
                          )}
                          {hasPermission("exchange_rate_management:delete") && (
                            <DropdownMenu.Item
                              onClick={() => handleDeleteClick(exchangeRate)}
                              className="text-ui-fg-error"
                            >
                              <Trash className="w-4 h-4" />
                              Delete
                            </DropdownMenu.Item>
                          )}
                        </DropdownMenu.Content>
                      </DropdownMenu>
                    </Table.Cell>
                  </Table.Row>
                ))
              )}
            </Table.Body>
          </Table>
        </div>
      </Container>

      {/* Create Modal */}
      <CreateExchangeRateModal
        open={showCreateModal}
        onOpenChange={setShowCreateModal}
      />

      {/* Edit Modal */}
      <EditExchangeRateModal
        open={showEditModal}
        onOpenChange={setShowEditModal}
        exchangeRate={selectedExchangeRate}
      />

      {/* Delete Confirmation */}
      <Prompt
        open={showDeletePrompt}
        onOpenChange={setShowDeletePrompt}
        variant="danger"
      >
        <Prompt.Content>
          <Prompt.Header>
            <Prompt.Title>Delete Exchange Rate</Prompt.Title>
            <Prompt.Description>
              {exchangeRateToDelete
                ? `Are you sure you want to delete the exchange rate for ${formatCurrencyPair(
                    exchangeRateToDelete.base_currency,
                    exchangeRateToDelete.selling_currency
                  )} on ${formatDate(
                    exchangeRateToDelete.date
                  )}? This action cannot be undone.`
                : ""}
            </Prompt.Description>
          </Prompt.Header>
          <Prompt.Footer>
            <Prompt.Cancel>Cancel</Prompt.Cancel>
            <Prompt.Action onClick={handleDeleteConfirm}>Delete</Prompt.Action>
          </Prompt.Footer>
        </Prompt.Content>
      </Prompt>

      <Toaster />
    </>
  );
};

export default PageClient;
