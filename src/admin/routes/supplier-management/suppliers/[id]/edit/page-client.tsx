import React from "react";
import { use<PERSON><PERSON>, Controller } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import {
  Button,
  Container,
  Heading,
  Input,
  Label,
  Select,
  Text,
  Textarea,
  toast,
} from "@camped-ai/ui";
import { MultiSelect } from "../../../../../components/common/MultiSelect";
import {
  SUPPLIER_TYPES,
  SUPPLIER_STATUSES,
  TIMEZONES,
  CURRENCIES,
  LANGUAGES,
  PAYMENT_METHODS,
  PAYOUT_TERMS,
} from "../../../../../constants/supplier-form-options";
import { useCategories } from "../../../../../hooks/supplier-products-services/use-categories";
import { Plus } from "@camped-ai/icons";
import { Trash2, } from "lucide-react"

// Contact interface
interface Contact {
  id: string;
  name: string;
  email: string;
  phone_number: string;
  is_primary: boolean;
}

// Form validation schema
const supplierSchema = z.object({
  // Basic Info
  name: z.string().min(1, "Name is required"),
  supplier_type: z.enum(["Company", "Individual"], {
    required_error: "Supplier type is required",
  }),


  status: z
    .enum(["Active", "Inactive"])
    .default("Active"),

  // Business & Region Information
  timezone: z.string().optional(),
  language_preference: z.array(z.string()).optional(),

  // Financial Information
  payment_method: z.string().optional(),
  payout_terms: z.string().optional(),
  tax_id: z
    .string()
    .optional()
    .refine(
      (val) => !val || /^[A-Z0-9\-]{5,20}$/.test(val.replace(/[\s]/g, "")),
      "Please enter a valid tax ID (5-20 alphanumeric characters)"
    ),
  default_currency: z.string().default("CHF"),
  bank_account_details: z.string().optional(),

  // Additional Information
  categories: z.array(z.string()).optional(),

  // Contacts
  contacts: z
    .array(
      z.object({
        id: z.string(),
        name: z.string().min(1, "Contact name is required"),
        email: z.string().email("Valid email is required"),
        phone_number: z
          .string()
          .min(1, "Phone number is required")
          .refine(
            (val) =>
              /^[\+]?[1-9][\d]{0,15}$/.test(val.replace(/[\s\-\(\)]/g, "")),
            "Please enter a valid phone number"
          ),
        is_primary: z.boolean(),
      })
    )
    .min(1, "At least one contact is required"),
});

type SupplierFormData = z.infer<typeof supplierSchema>;

interface SupplierEditFormProps {
  supplier: any;
  onSubmit: (data: SupplierFormData) => void;
  onCancel: () => void;
  isSubmitting: boolean;
}

const SupplierEditForm: React.FC<SupplierEditFormProps> = ({
  supplier,
  onSubmit,
  onCancel,
  isSubmitting,
}) => {
  const {
    control,
    handleSubmit,
    formState: { errors },
    setValue,
    watch,
    reset,
  } = useForm<SupplierFormData>({
    resolver: zodResolver(supplierSchema),
    defaultValues: {
      name: supplier?.name || "",
      supplier_type: supplier?.supplier_type || "Company",

      status: supplier?.status || "Active",
      timezone: supplier?.timezone || "",
      language_preference: supplier?.language_preference || [],
      payment_method: supplier?.payment_method || "",
      payout_terms: supplier?.payout_terms || "",
      tax_id: supplier?.tax_id || "",
      default_currency: supplier?.default_currency || "CHF",
      bank_account_details: supplier?.bank_account_details || "",
      categories: supplier?.categories || [],
      contacts: supplier?.contacts?.length > 0 ? supplier.contacts.map((contact: any) => ({
        id: contact.id || Date.now().toString(),
        name: contact.name || "",
        email: contact.email || "",
        phone_number: contact.phone_number || "",
        is_primary: Boolean(contact.is_primary),
      })) : [
        {
          id: "1",
          name: "",
          email: "",
          phone_number: "",
          is_primary: true,
        },
      ],
    },
  });

  React.useEffect(() => {
    if (supplier) {
      reset({
        name: supplier?.name || "",
        supplier_type: supplier?.supplier_type || "Company",

        status: supplier?.status || "Active",
        timezone: supplier?.timezone || "",
        language_preference: supplier?.language_preference || [],
        payment_method: supplier?.payment_method || "",
        payout_terms: supplier?.payout_terms || "",
        tax_id: supplier?.tax_id || "",
        default_currency: supplier?.default_currency || "CHF",
        bank_account_details: supplier?.bank_account_details || "",
        categories: supplier?.categories || [],
        contacts: supplier?.contacts?.length > 0 ? supplier.contacts.map((contact: any) => ({
          id: contact.id || Date.now().toString(),
          name: contact.name || "",
          email: contact.email || "",
          phone_number: contact.phone_number || "",
          is_primary: Boolean(contact.is_primary),
        })) : [
          {
            id: "1",
            name: "",
            email: "",
            phone_number: "",
            is_primary: true,
          },
        ],
      });
    }
  }, [supplier, reset]);

  const watchedContacts = watch("contacts");
  const watchedSupplierType = watch("supplier_type");

  // Contact management functions
  const addContact = () => {
    const newContact: Contact = {
      id: Date.now().toString(),
      name: "",
      email: "",
      phone_number: "",
      is_primary: false,
    };
    setValue("contacts", [...watchedContacts, newContact]);
  };

  const removeContact = (index: number) => {
    if (watchedContacts.length <= 1) {
      toast.error("At least one contact is required");
      return;
    }
    const updatedContacts = watchedContacts.filter((_, i) => i !== index);
    setValue("contacts", updatedContacts);
  };

  const setPrimaryContact = (index: number) => {
    const updatedContacts = watchedContacts.map((contact, i) => ({
      ...contact,
      is_primary: i === index,
    }));
    setValue("contacts", updatedContacts);
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-3">
      {/* Basic Information */}
      <Container className="divide-y p-0">
        <div className="flex items-center justify-between px-6 py-4">
          <Heading level="h2">Basic Information</Heading>
        </div>
        <div className="px-6 py-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="space-y-2">
              <Label>Supplier Type *</Label>
              <Controller
                name="supplier_type"
                control={control}
                render={({ field }) => (
                  <Select value={field.value} onValueChange={field.onChange}>
                    <Select.Trigger>
                      <Select.Value />
                    </Select.Trigger>
                    <Select.Content>
                      {SUPPLIER_TYPES.map((type) => (
                        <Select.Item key={type.value} value={type.value}>
                          {type.label}
                        </Select.Item>
                      ))}
                    </Select.Content>
                  </Select>
                )}
              />
              {errors.supplier_type && (
                <Text size="small" className="text-red-600">
                  {errors.supplier_type.message}
                </Text>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="name">
                {watchedSupplierType === "Individual" ? "Full Name" : "Company Name"} *
              </Label>
              <Controller
                name="name"
                control={control}
                render={({ field }) => (
                  <Input
                    {...field}
                    id="name"
                    placeholder={
                      watchedSupplierType === "Individual"
                        ? "Enter full name"
                        : "Enter company name"
                    }
                    className={errors.name ? "border-red-500" : ""}
                  />
                )}
              />
              {errors.name && (
                <Text size="small" className="text-red-600">
                  {errors.name.message}
                </Text>
              )}
            </div>

            <div className="space-y-2">
              <Label>Status *</Label>
              <Controller
                name="status"
                control={control}
                render={({ field }) => (
                  <Select value={field.value} onValueChange={field.onChange}>
                    <Select.Trigger>
                      <Select.Value />
                    </Select.Trigger>
                    <Select.Content>
                      {SUPPLIER_STATUSES.map((status) => (
                        <Select.Item key={status.value} value={status.value}>
                          {status.label}
                        </Select.Item>
                      ))}
                    </Select.Content>
                  </Select>
                )}
              />
            </div>


          </div>
        </div>
      </Container>

      {/* Business & Region */}
      <Container className="divide-y p-0 mt-1">
        <div className="flex items-center justify-between px-6 py-4">
          <Heading level="h2">Business & Region</Heading>
        </div>
        <div className="px-6 py-4">
          <div className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="space-y-2">
                <Label>Timezone</Label>
                <Controller
                  name="timezone"
                  control={control}
                  render={({ field }) => (
                    <Select
                      value={field.value || ""}
                      onValueChange={field.onChange}
                    >
                      <Select.Trigger>
                        <Select.Value placeholder="Select timezone" />
                      </Select.Trigger>
                      <Select.Content>
                        {TIMEZONES.map((timezone) => (
                          <Select.Item
                            key={timezone.value}
                            value={timezone.value}
                          >
                            {timezone.label}
                          </Select.Item>
                        ))}
                      </Select.Content>
                    </Select>
                  )}
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label>Language Preference</Label>
              <Controller
                name="language_preference"
                control={control}
                render={({ field }) => (
                  <MultiSelect
                    options={LANGUAGES}
                    selectedValues={field.value || []}
                    onChange={field.onChange}
                    placeholder="Select languages"
                  />
                )}
              />
            </div>
          </div>
        </div>
      </Container>

      {/* Additional Information */}
      <Container className="divide-y p-0 mt-1">
        <div className="flex items-center justify-between px-6 py-4">
          <Heading level="h2">Additional Information</Heading>
        </div>
        <div className="px-6 py-4">
          <div className="space-y-4">
            <div className="grid grid-cols-1 gap-4">
              <div className="space-y-2">
                <Label>Categories (Optional)</Label>
                <Controller
                  name="categories"
                  control={control}
                  render={({ field }) => {
                    const { data: categoriesData, isLoading } = useCategories({
                      is_active: true,
                      limit: 100
                    });

                    const categories = categoriesData?.categories || [];
                    const categoryOptions = categories.map((category) => ({
                      value: category.id,
                      label: category.name,
                    }));

                    return (
                      <MultiSelect
                        options={categoryOptions}
                        selectedValues={field.value || []}
                        onChange={field.onChange}
                        placeholder="Select categories"
                        disabled={isLoading}
                        showSelectAll={true}
                        showSelectedTags={true}
                      />
                    );
                  }}
                />
              </div>
            </div>
          </div>
        </div>
      </Container>

      {/* Contacts */}
      <Container className="divide-y p-0 mt-1">
        <div className="flex items-center justify-between px-6 py-4">
          <Heading level="h2">Contacts</Heading>
          <Button
            type="button"
            variant="secondary"
            size="small"
            onClick={addContact}
            className="flex items-center gap-2"
          >
            <Plus className="w-4 h-4" />
            Add Contact
          </Button>
        </div>
        <div className="px-6 py-4">
          <div className="space-y-6">
            {watchedContacts.map((contact, index) => (
              <div
                key={contact.id}
                className="border border-gray-200 rounded-lg p-4 space-y-4"
              >
                <div className="flex items-center justify-between">
                  <Text weight="plus">Contact {index + 1}</Text>
                  <div className="flex items-center gap-2">
                    <Button
                      type="button"
                      variant={contact.is_primary ? "primary" : "secondary"}
                      size="small"
                      onClick={() => setPrimaryContact(index)}
                    >
                      {contact.is_primary ? "Primary" : "Set as Primary"}
                    </Button>
                    {watchedContacts.length > 1 && (
                      <Button
                        type="button"
                        variant="danger"
                        size="small"
                        onClick={() => removeContact(index)}
                      >
                        <Trash2 className="w-4 h-4" />
                      </Button>
                    )}
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor={`contact_name_${contact.id}`}>
                      Name *
                    </Label>
                    <Controller
                      name={`contacts.${index}.name`}
                      control={control}
                      render={({ field }) => (
                        <Input
                          {...field}
                          id={`contact_name_${contact.id}`}
                          placeholder="Enter contact name"
                          className={
                            errors.contacts?.[index]?.name
                              ? "border-red-500"
                              : ""
                          }
                        />
                      )}
                    />
                    {errors.contacts?.[index]?.name && (
                      <Text size="small" className="text-red-600">
                        {errors.contacts[index]?.name?.message}
                      </Text>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor={`contact_email_${contact.id}`}>
                      Email *
                    </Label>
                    <Controller
                      name={`contacts.${index}.email`}
                      control={control}
                      render={({ field }) => (
                        <Input
                          {...field}
                          id={`contact_email_${contact.id}`}
                          type="email"
                          placeholder="<EMAIL>"
                          className={
                            errors.contacts?.[index]?.email
                              ? "border-red-500"
                              : ""
                          }
                        />
                      )}
                    />
                    {errors.contacts?.[index]?.email && (
                      <Text size="small" className="text-red-600">
                        {errors.contacts[index]?.email?.message}
                      </Text>
                    )}
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor={`contact_phone_${contact.id}`}>
                      Phone Number *
                    </Label>
                    <Controller
                      name={`contacts.${index}.phone_number`}
                      control={control}
                      render={({ field }) => (
                        <Input
                          {...field}
                          id={`contact_phone_${contact.id}`}
                          type="tel"
                          placeholder="+****************"
                          className={
                            errors.contacts?.[index]?.phone_number
                              ? "border-red-500"
                              : ""
                          }
                        />
                      )}
                    />
                    {errors.contacts?.[index]?.phone_number && (
                      <Text size="small" className="text-red-600">
                        {errors.contacts[index]?.phone_number?.message}
                      </Text>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </Container>

      {/* Financial Information */}
      <Container className="divide-y p-0 mt-1">
        <div className="flex items-center justify-between px-6 py-4">
          <Heading level="h2">Financial Information</Heading>
        </div>
        <div className="px-6 py-4">
          <div className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="space-y-2">
                <Label>Payment Method</Label>
                <Controller
                  name="payment_method"
                  control={control}
                  render={({ field }) => (
                    <Select
                      value={field.value || ""}
                      onValueChange={field.onChange}
                    >
                      <Select.Trigger>
                        <Select.Value placeholder="Select payment method" />
                      </Select.Trigger>
                      <Select.Content>
                        {PAYMENT_METHODS.map((method) => (
                          <Select.Item key={method.value} value={method.value}>
                            {method.label}
                          </Select.Item>
                        ))}
                      </Select.Content>
                    </Select>
                  )}
                />
              </div>

              <div className="space-y-2">
                <Label>Payout Terms</Label>
                <Controller
                  name="payout_terms"
                  control={control}
                  render={({ field }) => (
                    <Select
                      value={field.value || ""}
                      onValueChange={field.onChange}
                    >
                      <Select.Trigger>
                        <Select.Value placeholder="Select payout terms" />
                      </Select.Trigger>
                      <Select.Content>
                        {PAYOUT_TERMS.map((term) => (
                          <Select.Item key={term.value} value={term.value}>
                            {term.label}
                          </Select.Item>
                        ))}
                      </Select.Content>
                    </Select>
                  )}
                />
              </div>

              <div className="space-y-2">
                <Label>Default Currency *</Label>
                <Controller
                  name="default_currency"
                  control={control}
                  render={({ field }) => (
                    <Select value={field.value} onValueChange={field.onChange}>
                      <Select.Trigger>
                        <Select.Value />
                      </Select.Trigger>
                      <Select.Content>
                        {CURRENCIES.map((currency) => (
                          <Select.Item
                            key={currency.value}
                            value={currency.value}
                          >
                            {currency.label}
                          </Select.Item>
                        ))}
                      </Select.Content>
                    </Select>
                  )}
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="space-y-2">
                <Label htmlFor="tax_id">Tax ID / VAT No.</Label>
                <Controller
                  name="tax_id"
                  control={control}
                  render={({ field }) => (
                    <Input
                      {...field}
                      id="tax_id"
                      placeholder="Enter tax identification number"
                    />
                  )}
                />
                {errors.tax_id && (
                  <Text size="small" className="text-red-600">
                    {errors.tax_id.message}
                  </Text>
                )}
              </div>


            </div>

            <div className="space-y-2">
              <Label htmlFor="bank_account_details">Bank Account Details</Label>
              <Controller
                name="bank_account_details"
                control={control}
                render={({ field }) => (
                  <Textarea
                    {...field}
                    id="bank_account_details"
                    placeholder="Enter bank account information"
                    rows={3}
                  />
                )}
              />
            </div>
          </div>
        </div>
      </Container>

      {/* Form Actions */}
      <div className="flex items-center justify-end gap-2 mt-6">
        <Button
          type="button"
          variant="secondary"
          onClick={onCancel}
          disabled={isSubmitting}
        >
          Cancel
        </Button>
        <Button
          type="submit"
          disabled={isSubmitting}
        >
          {isSubmitting ? "Updating..." : "Update Supplier"}
        </Button>
      </div>
    </form>
  );
};

export default SupplierEditForm;
