import { useState } from "react";
import {
  FocusModal,
  Heading,
  Button,
  Label,
  toast,
} from "@camped-ai/ui";
import { Download, Upload, } from "lucide-react";
import { defineRouteConfig } from "@camped-ai/admin-sdk";
import { useNavigate } from "react-router-dom";


const SupplierExportModal = () => {
  // State for selected fields (matching import template structure) - all fields selected by default
  const [selectedFields, setSelectedFields] = useState<Record<string, boolean>>({
    // Basic Information
    name: true,
    supplier_type: true,

    status: true,

    // Business Information
    preference: true,
    timezone: true,
    language_preference: true,
    payment_method: true,
    payout_terms: true,
    tax_id: true,
    default_currency: true,
    bank_account_details: true,
    categories: true,

    // Address Information (single field)
    address: true,
  });

  // State for filters
  const [filters, setFilters] = useState({
    is_active: "all", // "all", "true", "false"
    supplier_type: "all", // "all" or specific supplier type
  });

    const navigate = useNavigate();

  // Handle navigation back to main page
  const handleBackToMain = () => {
    navigate("/supplier-management/suppliers");
  };

  // State for file format
  const [fileFormat, setFileFormat] = useState<"csv" | "xlsx">("xlsx");

  // Handle field selection
  const handleFieldChange = (field: string, checked: boolean) => {
    setSelectedFields((prev) => ({
      ...prev,
      [field]: checked,
    }));
  };

  // Handle select all fields
  const handleSelectAll = () => {
    const allFields = { ...selectedFields };
    const allSelected = Object.values(allFields).every((value) => value);

    Object.keys(allFields).forEach((key) => {
      allFields[key] = !allSelected;
    });

    setSelectedFields(allFields);
  };

  // Handle filter changes
  const handleFilterChange = (
    filterName: string,
    value: string | boolean
  ) => {
    setFilters((prev) => ({
      ...prev,
      [filterName]: value,
    }));
  };

  // Handle export
  const handleExport = async () => {
    try {
      // Build query parameters for export
      const queryParams = new URLSearchParams();

      // Add selected fields
      const fieldsToExport = Object.entries(selectedFields)
        .filter(([_, selected]) => selected)
        .map(([field]) => field);

      // Validate that at least one field is selected
      if (fieldsToExport.length === 0) {
        toast.error("Please select at least one field to export");
        return;
      }

      if (fieldsToExport.length > 0) {
        queryParams.append('fields', fieldsToExport.join(','));
      }

      // Add filters
      if (filters.is_active !== 'all') {
        queryParams.append('is_active', filters.is_active);
      }

      if (filters.supplier_type !== 'all') {
        queryParams.append('supplier_type', filters.supplier_type);
      }

      // Add file format
      queryParams.append('format', fileFormat);

      // Make export request
      const response = await fetch(`/admin/supplier-management/suppliers/export?${queryParams.toString()}`, {
        method: 'GET',
        credentials: 'include',
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || `Export failed with status ${response.status}`);
      }

      // Get filename from response headers
      const contentDisposition = response.headers.get('Content-Disposition');
      const filename = contentDisposition
        ? contentDisposition.split('filename=')[1]?.replace(/"/g, '')
        : `suppliers_export_${new Date().toISOString().split('T')[0]}.${fileFormat}`;

      // Download the file
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = filename;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);

      toast.success("Export completed successfully");
      handleBackToMain();
    } catch (error) {
      console.error("Export error:", error);
      toast.error(error instanceof Error ? error.message : "Failed to export data");
    }
  };



  return (
    <FocusModal open={true} onOpenChange={handleBackToMain}>
      <FocusModal.Content className="flex flex-col h-full max-h-[98vh] bg-gray-50 dark:bg-gray-900">
        <FocusModal.Header className="flex-shrink-0 bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
          <div className="flex justify-between items-center w-full py-4 px-6">
            <div className="flex items-center gap-3">
              <Heading level="h2" className="text-xl font-semibold text-gray-900 dark:text-gray-100">
                Export Suppliers
              </Heading>
            </div>

            {/* Progress Indicator */}
            <div className="px-6 py-2 border-b border-gray-200 dark:border-gray-700">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  {/* Step 1 */}
                  <div className="flex items-center">
                    <div className="w-8 h-8 bg-blue-600 dark:bg-blue-500 text-white rounded-full flex items-center justify-center text-sm font-semibold">
                      1
                    </div>
                    <span className="ml-2 text-sm font-medium text-blue-600 dark:text-blue-400">Configure</span>
                  </div>
                  <div className="w-8 h-0.5 bg-gray-300 dark:bg-gray-600"></div>

                  {/* Step 2 */}
                  <div className="flex items-center">
                    <div className="w-8 h-8 bg-gray-300 dark:bg-gray-600 text-gray-600 dark:text-gray-400 rounded-full flex items-center justify-center text-sm font-semibold">
                      2
                    </div>
                    <span className="ml-2 text-sm font-medium text-gray-500 dark:text-gray-400">Export</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

        </FocusModal.Header>

        <FocusModal.Body className="flex flex-col flex-grow overflow-hidden">
          <div className="flex-grow overflow-y-auto p-6 pb-0">
            <div className="flex flex-col gap-6">
              {/* Fields Selection Section - Full Width Top Row */}
              <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 shadow-sm">
                <div className="flex-shrink-0 p-4 border-b border-gray-200 dark:border-gray-700">
                  <div className="flex justify-between items-center">
                    <Heading level="h3" className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                      Select Fields to Export
                    </Heading>
                    <Button
                      variant="secondary"
                      size="small"
                      onClick={handleSelectAll}
                      className="bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600 border-gray-300 dark:border-gray-600"
                    >
                      {Object.values(selectedFields).every((v) => v)
                        ? "Deselect All"
                        : "Select All"}
                    </Button>
                  </div>
                </div>

                <div className="p-4">
                  <div className="grid grid-cols-8 gap-3">
                    {Object.keys(selectedFields).map((field) => (
                      <div key={field} className="flex items-center gap-2 p-3 rounded-md hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors border border-gray-100 dark:border-gray-600 h-12 min-w-0">
                        <input
                          type="checkbox"
                          id={`field-${field}`}
                          checked={selectedFields[field]}
                          onChange={(e) => handleFieldChange(field, e.target.checked)}
                          className="h-4 w-4 flex-shrink-0 text-blue-600 dark:text-blue-400 focus:ring-blue-500 dark:focus:ring-blue-400 rounded border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700"
                        />
                        <Label htmlFor={`field-${field}`} className="cursor-pointer text-gray-700 dark:text-gray-300 text-xs font-medium truncate min-w-0 flex-1">
                          {field.replace(/_/g, " ").replace(/\b\w/g, (l) => l.toUpperCase())}
                        </Label>
                      </div>
                    ))}
                  </div>
                </div>
              </div>

              {/* Bottom Row - Filters, Format, and Summary */}
              <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6">

                {/* Filters Section */}
                <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 shadow-sm">
                  <div className="p-4 border-b border-gray-200 dark:border-gray-700">
                    <Heading level="h3" className="text-base font-semibold text-gray-900 dark:text-gray-100">
                      Filters
                    </Heading>
                  </div>

                  <div className="p-4 space-y-4">
                    <div>
                      <Label htmlFor="is_active" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Active Status</Label>
                      <select
                        id="is_active"
                        value={filters.is_active}
                        onChange={(e) => handleFilterChange("is_active", e.target.value)}
                        className="w-full px-3 py-2 text-sm border border-gray-200 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                      >
                        <option value="all">All Suppliers</option>
                        <option value="true">Active Only</option>
                        <option value="false">Inactive Only</option>
                      </select>
                    </div>

                    <div>
                      <Label htmlFor="supplier_type" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Supplier Type</Label>
                      <select
                        id="supplier_type"
                        value={filters.supplier_type}
                        onChange={(e) => handleFilterChange("supplier_type", e.target.value)}
                        className="w-full px-3 py-2 text-sm border border-gray-200 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                      >
                        <option value="all">All Types</option>
                        <option value="company">Company</option>
                        <option value="individual">Individual</option>
                      </select>
                    </div>
                  </div>
                </div>

                {/* Format Section */}
                <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 shadow-sm">
                  <div className="p-4 border-b border-gray-200 dark:border-gray-700">
                    <Heading level="h3" className="text-base font-semibold text-gray-900 dark:text-gray-100">
                      Export Format
                    </Heading>
                  </div>

                  <div className="p-4 space-y-3">
                    <div className="flex items-center gap-3 p-3 rounded-md hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors border border-gray-100 dark:border-gray-600">
                      <input
                        type="radio"
                        id="format-xlsx"
                        name="fileFormat"
                        value="xlsx"
                        checked={fileFormat === "xlsx"}
                        onChange={() => setFileFormat("xlsx")}
                        className="h-4 w-4 text-blue-600 dark:text-blue-400 focus:ring-blue-500 dark:focus:ring-blue-400 border-gray-300 dark:border-gray-600"
                      />
                      <div className="flex-grow">
                        <Label htmlFor="format-xlsx" className="cursor-pointer text-gray-900 dark:text-gray-100 font-medium">
                          Excel (.xlsx)
                        </Label>
                        <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">Recommended for data analysis</p>
                      </div>
                    </div>

                    <div className="flex items-center gap-3 p-3 rounded-md hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors border border-gray-100 dark:border-gray-600">
                      <input
                        type="radio"
                        id="format-csv"
                        name="fileFormat"
                        value="csv"
                        checked={fileFormat === "csv"}
                        onChange={() => setFileFormat("csv")}
                        className="h-4 w-4 text-blue-600 dark:text-blue-400 focus:ring-blue-500 dark:focus:ring-blue-400 border-gray-300 dark:border-gray-600"
                      />
                      <div className="flex-grow">
                        <Label htmlFor="format-csv" className="cursor-pointer text-gray-900 dark:text-gray-100 font-medium">
                          CSV (.csv)
                        </Label>
                        <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">Compatible with most applications</p>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Export Summary Section */}
                <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 shadow-sm">
                  <div className="p-4 border-b border-gray-200 dark:border-gray-700">
                    <Heading level="h3" className="text-base font-semibold text-gray-900 dark:text-gray-100">
                      Export Summary
                    </Heading>
                  </div>

                  <div className="p-4 space-y-4">
                    {/* Selected Fields Summary */}
                    <div className={`rounded-lg p-3 border ${Object.values(selectedFields).filter(Boolean).length === 0
                        ? "bg-red-50 dark:bg-red-900/20 border-red-200 dark:border-red-800"
                        : "bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-800"
                      }`}>
                      <div className="flex items-center justify-between mb-2">
                        <h4 className={`text-sm font-semibold ${Object.values(selectedFields).filter(Boolean).length === 0
                            ? "text-red-900 dark:text-red-100"
                            : "text-blue-900 dark:text-blue-100"
                          }`}>Selected Fields</h4>
                        <span className={`text-xs font-medium px-2 py-1 rounded-full ${Object.values(selectedFields).filter(Boolean).length === 0
                            ? "bg-red-100 dark:bg-red-800 text-red-800 dark:text-red-200"
                            : "bg-blue-100 dark:bg-blue-800 text-blue-800 dark:text-blue-200"
                          }`}>
                          {Object.values(selectedFields).filter(Boolean).length} of {Object.keys(selectedFields).length}
                        </span>
                      </div>
                      <div className={`text-xs max-h-16 overflow-y-auto ${Object.values(selectedFields).filter(Boolean).length === 0
                          ? "text-red-700 dark:text-red-300"
                          : "text-blue-700 dark:text-blue-300"
                        }`}>
                        {Object.values(selectedFields).filter(Boolean).length === 0 ? (
                          <div className="text-center font-medium">
                            ⚠️ No fields selected for export
                          </div>
                        ) : (
                          <>
                            {Object.entries(selectedFields)
                              .filter(([_, selected]) => selected)
                              .slice(0, 4)
                              .map(([field]) => (
                                <div key={field} className="truncate">
                                  • {field.replace(/_/g, " ").replace(/\b\w/g, (l) => l.toUpperCase())}
                                </div>
                              ))}
                            {Object.values(selectedFields).filter(Boolean).length > 4 && (
                              <div className="text-blue-600 dark:text-blue-400 font-medium">
                                +{Object.values(selectedFields).filter(Boolean).length - 4} more...
                              </div>
                            )}
                          </>
                        )}
                      </div>
                    </div>

                    {/* Filter & Export Details Combined */}
                    <div className="grid grid-cols-2 gap-3">
                      <div className="bg-green-50 dark:bg-green-900/20 rounded-lg p-3 border border-green-200 dark:border-green-800">
                        <h4 className="text-sm font-semibold text-green-900 dark:text-green-100 mb-2">Filters</h4>
                        <div className="space-y-1 text-xs text-green-700 dark:text-green-300">
                          <div>
                            <span className="font-medium">Status: </span>
                            {filters.is_active === "all" ? "All" : filters.is_active === "true" ? "Active" : "Inactive"}
                          </div>
                          <div>
                            <span className="font-medium">Type: </span>
                            <span className="truncate">
                              {filters.supplier_type === "all" ? "All Types" : filters.supplier_type}
                            </span>
                          </div>
                        </div>
                      </div>

                      <div className="bg-purple-50 dark:bg-purple-900/20 rounded-lg p-3 border border-purple-200 dark:border-purple-800">
                        <h4 className="text-sm font-semibold text-purple-900 dark:text-purple-100 mb-2">Export Details</h4>
                        <div className="space-y-1 text-xs text-purple-700 dark:text-purple-300">
                          <div>
                            <span className="font-medium">Format: </span>
                            <span className="uppercase">{fileFormat}</span>
                          </div>
                          <div>
                            <span className="font-medium">Date: </span>
                            {new Date().toLocaleDateString()}
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Instructions */}
                    <div className="bg-gray-50 dark:bg-gray-800/50 rounded-lg p-3 border border-gray-200 dark:border-gray-700">
                      <div className="text-sm text-gray-600 dark:text-gray-400 text-center">
                        Click "Export Data" to download the file to your downloads folder
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Footer */}
          <div className="flex-shrink-0 py-6 px-8 bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700">
            <div className="flex items-center justify-between">
              <div className="text-sm text-gray-500 dark:text-gray-400">
                {Object.values(selectedFields).filter(Boolean).length > 0
                  ? `Ready to export ${Object.values(selectedFields).filter(Boolean).length} fields`
                  : "Please select at least one field to export"
                }
              </div>
              <div className="flex gap-4">
                <Button
                  variant="secondary"
                  onClick={handleBackToMain}
                  className="px-6 py-3 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600 border-gray-300 dark:border-gray-600 font-medium"
                >
                  Cancel
                </Button>
                <Button
                  variant="primary"
                  onClick={handleExport}
                  disabled={Object.values(selectedFields).filter(Boolean).length === 0}
                  className={`flex items-center gap-3 px-6 py-3 shadow-lg font-medium ${Object.values(selectedFields).filter(Boolean).length === 0
                      ? "bg-gray-400 dark:bg-gray-600 text-gray-200 dark:text-gray-400 cursor-not-allowed"
                      : "bg-blue-600 dark:bg-blue-500 hover:bg-blue-700 dark:hover:bg-blue-600 text-white"
                    }`}
                >
                  <Download className="w-5 h-5" />
                  Export Data
                </Button>
              </div>
            </div>
          </div>
        </FocusModal.Body>
      </FocusModal.Content>
    </FocusModal>
  );
};

export const config = defineRouteConfig({
  label: "Export Products & Services",
  icon: Upload,
});

export default SupplierExportModal;
