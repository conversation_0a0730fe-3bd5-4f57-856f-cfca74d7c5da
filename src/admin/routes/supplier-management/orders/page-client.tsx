import {
  Edit,
  Eye,
  MoreHorizontal,
  Calendar,
  Package,
  ShoppingCart,
} from "lucide-react";
import {
  Container,
  Heading,
  Text,
  Button,
  Badge,
  Toaster,

  DropdownMenu,
} from "@camped-ai/ui";
import { useMemo } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import { useReactTable, getCoreRowModel, createColumnHelper, ColumnDef } from "@tanstack/react-table";
import { useRbac } from "../../../hooks/use-rbac";
import { DataTable } from "../../../../components/table/data-table";
import type { Filter } from "../../../../components/table/data-table";
import type { SupplierOrder } from "../../../hooks/vendor-management/use-supplier-orders";


// Status badge colors
const statusColors = {
  pending: "orange",
  confirmed: "green",
  in_progress: "blue",
  completed: "purple",
  cancelled: "grey",
} as const;

// Order type badge colors
const orderTypeColors = {
  product: "blue",
  service: "green",
  mixed: "purple",
} as const;

interface SupplierOrdersPageClientProps {
  orders: SupplierOrder[];
  suppliers: Array<{ id: string; name: string }>;
  isLoading: boolean;
  totalCount: number;
  pageSize: number;
}

const SupplierOrdersPageClient: React.FC<SupplierOrdersPageClientProps> = ({
  orders,
  suppliers,
  isLoading,
  totalCount,
  pageSize,
}) => {
  const navigate = useNavigate();
  const location = useLocation();
  const { hasPermission } = useRbac();

  // Column helper for type safety
  const columnHelper = createColumnHelper<SupplierOrder>();

  // Helper functions
  const getStatusBadgeVariant = (status: string) => {
    return statusColors[status as keyof typeof statusColors] || "grey";
  };

  const getOrderTypeBadgeVariant = (orderType: string) => {
    return orderTypeColors[orderType as keyof typeof orderTypeColors] || "grey";
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    });
  };

  const formatCurrency = (amount: number, currency: string) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: currency,
    }).format(amount);
  };

  // Navigation handlers
  const handleViewOrder = (orderId: string) => {
    navigate(`/supplier-management/orders/${orderId}`);
  };

  const handleEditOrder = (orderId: string) => {
    navigate(`/supplier-management/orders/${orderId}/edit`);
  };

  // Define columns
  const columns = useMemo<ColumnDef<SupplierOrder, any>[]>(() => [
    columnHelper.display({
      id: "order",
      header: "Order",
      cell: ({ row }) => {
        const order = row.original;
        return (
          <div className="flex items-center gap-x-3 w-[160px] truncate">
            <div className="flex p-2 items-center justify-center rounded bg-ui-bg-subtle">
              <ShoppingCart className="h-4 w-4 text-ui-fg-subtle" />
            </div>
            <div>
              <Text className="txt-compact-medium-plus" weight="plus">
                {order.id}
              </Text>
            </div>
          </div>
        );
      },
    }),
    columnHelper.display({
      id: "order_name",
      header: "Order Name",
      cell: ({ row }) => {
        const order = row.original;
        return (
          <div>
            <Text className="txt-compact-medium-plus" weight="plus">
              {order.metadata?.order_name || order.order_number}
            </Text>
            {order.metadata?.order_name && (
              <Text className="txt-compact-small text-ui-fg-subtle">
                {order.order_number}
              </Text>
            )}
          </div>
        );
      },
    }),
    columnHelper.display({
      id: "supplier",
      header: "Supplier",
      cell: ({ row }) => {
        const order : any = row.original;
        return (
          <Text className="txt-compact-medium-plus" weight="plus">
            {order.supplier_name.length > 30
              ? order.supplier_name.slice(0, 30) + "..."
              : order.supplier_name}
          </Text>
        );
      },
    }),
    columnHelper.display({
      id: "supplier_type",
      header: "Supplier Type",
      cell: ({ row }) => {
        const order = row.original;
        return (
          <Badge color={order.supplier_type === "Company" ? "blue" : "green"} size="xsmall">
            {order.supplier_type || "Company"}
          </Badge>
        );
      },
    }),
    columnHelper.display({
      id: "type",
      header: "Type",
      cell: ({ row }) => {
        const order = row.original;
        return (
          <Badge color={getOrderTypeBadgeVariant(order.order_type)} size="xsmall">
            {order.order_type}
          </Badge>
        );
      },
    }),
    columnHelper.display({
      id: "status",
      header: "Status",
      cell: ({ row }) => {
        const order = row.original;
        return (
          <Badge color={getStatusBadgeVariant(order.status)} size="xsmall">
            {order.status}
          </Badge>
        );
      },
    }),
    columnHelper.display({
      id: "amount",
      header: "Amount",
      cell: ({ row }) => {
        const order = row.original;
        return (
          <Text className="txt-compact-medium-plus" weight="plus">
            {formatCurrency(order.total_amount, order.currency_code)}
          </Text>
        );
      },
    }),
    columnHelper.display({
      id: "order_date",
      header: "Order Date",
      cell: ({ row }) => {
        const order = row.original;
        return (
          <div className="flex items-center gap-x-2">
            <Calendar className="h-3 w-3 text-ui-fg-subtle" />
            <Text className="txt-compact-small">
              {formatDate(order.created_at)}
            </Text>
          </div>
        );
      },
    }),
    columnHelper.display({
      id: "delivery_date",
      header: "Delivery Date",
      cell: ({ row }) => {
        const order = row.original;
        return (
          <div className="flex items-center gap-x-2">
            <Package className="h-3 w-3 text-ui-fg-subtle" />
            <Text className="txt-compact-small">
              {order.requested_delivery_date
                ? formatDate(order.requested_delivery_date)
                : "-"}
            </Text>
          </div>
        );
      },
    }),
    columnHelper.display({
      id: "items",
      header: "Items",
      cell: ({ row }) => {
        const order = row.original;
        return (
          <Badge color="grey" size="xsmall">
            {order.items_count} items
          </Badge>
        );
      },
    }),
    columnHelper.display({
      id: "actions",
      header: "",
      cell: ({ row }) => {
        const order = row.original;
        return (
          <DropdownMenu>
            <DropdownMenu.Trigger asChild>
              <Button variant="transparent" size="small">
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenu.Trigger>
            <DropdownMenu.Content>
              <DropdownMenu.Item
                onClick={() => handleViewOrder(order.id)}
              >
                <Eye className="h-4 w-4 mr-2" />
                View Details
              </DropdownMenu.Item>
              {hasPermission("supplier_orders:edit") && (
                <DropdownMenu.Item
                  onClick={() => handleEditOrder(order.id)}
                >
                  <Edit className="h-4 w-4 mr-2" />
                  Edit Order
                </DropdownMenu.Item>
              )}
            </DropdownMenu.Content>
          </DropdownMenu>
        );
      },
    }),
  ], [hasPermission]);

  // Define filters
  const filters = useMemo<Filter[]>(() => [
    {
      key: "status",
      label: "Status",
      type: "select",
      options: [
        { label: "Pending", value: "pending" },
        { label: "Confirmed", value: "confirmed" },
        { label: "In Progress", value: "in_progress" },
        { label: "Completed", value: "completed" },
        { label: "Cancelled", value: "cancelled" },
      ],
    },
    {
      key: "order_type",
      label: "Order Type",
      type: "select",
      options: [
        { label: "Product", value: "product" },
        { label: "Service", value: "service" },
        { label: "Mixed", value: "mixed" },
      ],
    },
    {
      key: "supplier_id",
      label: "Supplier",
      type: "select",
      searchable: true,
      options: suppliers.map(supplier => ({
        label: supplier.name,
        value: supplier.id,
      })),
    },
  ], [suppliers]);

  // Define orderBy options
  const orderBy = useMemo(() => [
    { key: "order_number" as keyof SupplierOrder, label: "Order Number" },
    { key: "supplier_name" as keyof SupplierOrder, label: "Supplier" },
    { key: "supplier_type" as keyof SupplierOrder, label: "Supplier Type" },
    { key: "status" as keyof SupplierOrder, label: "Status" },
    { key: "order_type" as keyof SupplierOrder, label: "Order Type" },
    { key: "total_amount" as keyof SupplierOrder, label: "Amount" },
    { key: "created_at" as keyof SupplierOrder, label: "Order Date" },
    { key: "requested_delivery_date" as keyof SupplierOrder, label: "Delivery Date" },
  ], []);

  // Get current page from URL
  const searchParams = new URLSearchParams(location.search);
  const currentPage = parseInt(searchParams.get("page") || "1");

  // Create table instance
  const table = useReactTable({
    data: orders,
    columns,
    getCoreRowModel: getCoreRowModel(),
    manualPagination: true,
    pageCount: Math.ceil(totalCount / pageSize),
    state: {
      pagination: {
        pageIndex: currentPage - 1,
        pageSize,
      },
    },
  });

  return (
    <Container className="divide-y p-0">
      {/* Header */}
      <div className="flex items-center justify-between px-6 py-4">
        <div>
          <Heading level="h2">Supplier Orders</Heading>
        </div>
      </div>

      {/* DataTable */}
      <DataTable
        table={table}
        columns={columns}
        pageSize={pageSize}
        count={totalCount}
        isLoading={isLoading}
        filters={filters}
        orderBy={orderBy}
        search="autofocus"
        pagination
        queryObject={Object.fromEntries(searchParams)}
        noRecords={{
          title: "No supplier orders found",
          message: "Get started by creating your first supplier order",
        }}
      />



      <Toaster />
    </Container>
  );
};

export default SupplierOrdersPageClient;
