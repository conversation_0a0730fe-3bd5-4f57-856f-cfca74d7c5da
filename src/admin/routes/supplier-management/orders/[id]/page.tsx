import { defineRouteConfig } from "@camped-ai/admin-sdk";
import { Toaster } from "@camped-ai/ui";
import { useParams } from "react-router-dom";
import PermissionBasedSidebarHider from "../../../../widgets/permission-based-sidebar-hider";
import { useRbac } from "../../../../hooks/use-rbac";
import ViewSupplierOrderPageClient from "./page-client.tsx";

const ViewSupplierOrderPage = () => {
  const { id } = useParams<{ id: string }>();
  const { hasPermission } = useRbac();

  if (!id) {
    return (
      <div className="flex items-center justify-center h-64">
        <p className="text-gray-500">Order ID not found</p>
      </div>
    );
  }

  if (!hasPermission("supplier_orders:read")) {
    return (
      <div className="flex items-center justify-center h-64">
        <p className="text-gray-500">You don't have permission to view supplier orders</p>
      </div>
    );
  }

  return (
    <>
      <PermissionBasedSidebarHider />
      <Toaster />
      <ViewSupplierOrderPageClient orderId={id} />
    </>
  );
};

export const config = defineRouteConfig({
  label: "View Supplier Order",
});

export default ViewSupplierOrderPage;
