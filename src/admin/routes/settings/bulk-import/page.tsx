import { defineRouteConfig } from "@camped-ai/admin-sdk";
import { Upload as UploadIcon, Download, Database } from "lucide-react";
import { ComponentType } from "react";

// Create icon component for route config
const BulkImportIcon: ComponentType = (props: any) => (
  <Database {...props} className="h-4 w-4" />
);

import {
  Container,
  Heading,
  Text,
  Button,
  Toaster,
  toast,
} from "@camped-ai/ui";
import { useState, useEffect } from "react";
import BulkImportModal from "../../../components/bulk-import/bulk-import-modal";
import PermissionBasedSidebarHider from "../../../widgets/permission-based-sidebar-hider";
import OutlineButton from "../../../components/shared/OutlineButton";

interface ImportHistory {
  id: string;
  filename: string;
  status: "completed" | "failed" | "processing";
  created_at: string;
  summary?: {
    destinations: { successful: number; failed: number };
    hotels: { successful: number; failed: number };
    room_configs: { successful: number; failed: number };
    rooms: { successful: number; failed: number };
  };
  errors?: string[];
}

const BulkImportPage = () => {
  const [importModalOpen, setImportModalOpen] = useState(false);
  const [importHistory, setImportHistory] = useState<ImportHistory[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  // Fetch import history
  const fetchImportHistory = async () => {
    try {
      setIsLoading(true);
      const response = await fetch("/admin/bulk-import/history");
      if (response.ok) {
        const data = await response.json();
        setImportHistory(data.imports || []);
      }
    } catch (error) {
      console.error("Error fetching import history:", error);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchImportHistory();
  }, []);

  const handleImportComplete = () => {
    fetchImportHistory();
    toast.success("Success", {
      description: "Import completed successfully",
    });
  };

  return (
    <>
      <PermissionBasedSidebarHider />
      <Toaster />

      <Container className="py-8">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div>
            <Heading
              level="h1"
              className="text-3xl font-bold text-gray-900 mb-2"
            >
              Bulk Data Import
            </Heading>
            <Text className="text-gray-600">
              Import destinations, hotels, room configurations, and rooms from
              Excel files. Supports both standard format and direct Salesforce
              migration.
            </Text>
          </div>

          <div className="flex items-center gap-3">
            <Button
              onClick={() => setImportModalOpen(true)}
              className="flex items-center gap-2"
            >
              <UploadIcon className="w-4 h-4" />
              Start Import
            </Button>
          </div>
        </div>

        {/* Quick Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <div className="bg-white rounded-lg border p-6">
            <div className="flex items-center">
              <div className="p-2 bg-blue-100 rounded-lg">
                <Database className="w-6 h-6 text-blue-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">
                  Total Imports
                </p>
                <p className="text-2xl font-bold text-gray-900">
                  {importHistory.length}
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg border p-6">
            <div className="flex items-center">
              <div className="p-2 bg-green-100 rounded-lg">
                <Database className="w-6 h-6 text-green-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Successful</p>
                <p className="text-2xl font-bold text-gray-900">
                  {importHistory.filter((h) => h.status === "completed").length}
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg border p-6">
            <div className="flex items-center">
              <div className="p-2 bg-red-100 rounded-lg">
                <Database className="w-6 h-6 text-red-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Failed</p>
                <p className="text-2xl font-bold text-gray-900">
                  {importHistory.filter((h) => h.status === "failed").length}
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg border p-6">
            <div className="flex items-center">
              <div className="p-2 bg-yellow-100 rounded-lg">
                <Database className="w-6 h-6 text-yellow-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Processing</p>
                <p className="text-2xl font-bold text-gray-900">
                  {
                    importHistory.filter((h) => h.status === "processing")
                      .length
                  }
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Template Downloads Section */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
          {/* Standard Template */}
          <div className="bg-gradient-to-br from-blue-50 to-blue-100 rounded-lg border border-blue-200 p-6">
            <div className="flex items-center mb-4">
              <div className="w-10 h-10 bg-blue-600 rounded-lg flex items-center justify-center mr-3">
                <Download className="w-5 h-5 text-white" />
              </div>
              <div>
                <Heading
                  level="h3"
                  className="text-lg font-semibold text-gray-900"
                >
                  Standard Template
                </Heading>
                <Text className="text-sm text-gray-600">
                  For new data imports
                </Text>
              </div>
            </div>

            <Text className="text-gray-700 mb-4">
              Perfect for importing fresh data with our standard format.
              Includes all necessary fields for destinations, hotels, room
              configurations, and rooms.
            </Text>

            <div className="space-y-2 mb-4">
              <Text className="text-sm font-medium text-gray-700">
                Includes:
              </Text>
              <ul className="text-sm text-gray-600 space-y-1">
                <li>• Destinations sheet</li>
                <li>• Hotels sheet</li>
                <li>• Room configurations sheet</li>
                <li>• Rooms sheet</li>
              </ul>
            </div>

            <Button
              variant="secondary"
              onClick={() =>
                window.open("/admin/bulk-import/template", "_blank")
              }
              className="w-full flex items-center justify-center gap-2 bg-blue-600 text-white hover:bg-blue-700"
            >
              <Download className="w-4 h-4" />
              Download Template
            </Button>
          </div>

          {/* Salesforce Template */}
          <div className="bg-gradient-to-br from-orange-50 to-orange-100 rounded-lg border border-orange-200 p-6">
            <div className="flex items-center mb-4">
              <div className="w-10 h-10 bg-orange-600 rounded-lg flex items-center justify-center mr-3">
                <Download className="w-5 h-5 text-white" />
              </div>
              <div>
                <Heading
                  level="h3"
                  className="text-lg font-semibold text-gray-900"
                >
                  Salesforce Template
                </Heading>
                <Text className="text-sm text-gray-600">
                  For Salesforce migration
                </Text>
              </div>
            </div>

            <Text className="text-gray-700 mb-4">
              Designed specifically for Salesforce data migration. Uses
              Salesforce field names and structure for seamless data transfer.
            </Text>

            <div className="space-y-2 mb-4">
              <Text className="text-sm font-medium text-gray-700">
                Features:
              </Text>
              <ul className="text-sm text-gray-600 space-y-1">
                <li>• Salesforce field mapping</li>
                <li>• Auto-conversion support</li>
                <li>• Relationship handling</li>
                <li>• Migration instructions</li>
              </ul>
            </div>

            <Button
              variant="secondary"
              onClick={() =>
                window.open("/admin/bulk-import/salesforce-template", "_blank")
              }
              className="w-full flex items-center justify-center gap-2 border-orange-300 text-orange-600 hover:bg-orange-50"
            >
              <Download className="w-4 h-4" />
              Download Template
            </Button>
          </div>
        </div>

        {/* Import Process Overview */}
        <div className="rounded-lg border shadow-sm p-8 mb-8">
          <Heading
            level="h2"
            className="text-xl font-semibold text-gray-900 mb-6"
          >
            Import Process Overview
          </Heading>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {/* Left Column - Process Steps */}
            <div>
              <Heading
                level="h3"
                className="text-lg font-medium text-gray-900 mb-4"
              >
                How It Works
              </Heading>

              <div className="space-y-4">
                <div className="flex items-start gap-3">
                  <div className="flex-shrink-0 w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center">
                    <span className="text-blue-600 font-semibold text-xs">
                      1
                    </span>
                  </div>
                  <div>
                    <Text className="font-medium text-gray-900">
                      Download Template
                    </Text>
                    <Text className="text-sm text-gray-600">
                      Choose standard or Salesforce format
                    </Text>
                  </div>
                </div>

                <div className="flex items-start gap-3">
                  <div className="flex-shrink-0 w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center">
                    <span className="text-blue-600 font-semibold text-xs">
                      2
                    </span>
                  </div>
                  <div>
                    <Text className="font-medium text-gray-900">
                      Prepare Your Data
                    </Text>
                    <Text className="text-sm text-gray-600">
                      Fill in the template with your data
                    </Text>
                  </div>
                </div>

                <div className="flex items-start gap-3">
                  <div className="flex-shrink-0 w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center">
                    <span className="text-blue-600 font-semibold text-xs">
                      3
                    </span>
                  </div>
                  <div>
                    <Text className="font-medium text-gray-900">
                      Upload & Import
                    </Text>
                    <Text className="text-sm text-gray-600">
                      Upload your file and start the import
                    </Text>
                  </div>
                </div>

                <div className="flex items-start gap-3">
                  <div className="flex-shrink-0 w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center">
                    <span className="text-blue-600 font-semibold text-xs">
                      4
                    </span>
                  </div>
                  <div>
                    <Text className="font-medium text-gray-900">
                      Review Results
                    </Text>
                    <Text className="text-sm text-gray-600">
                      Check import status and any errors
                    </Text>
                  </div>
                </div>
              </div>
            </div>

            {/* Right Column - Important Notes */}
            <div>
              <Heading
                level="h3"
                className="text-lg font-medium text-gray-900 mb-4"
              >
                Important Notes
              </Heading>

              <div className="space-y-3">
                <div className="p-3 bg-yellow-50 rounded-lg border border-yellow-200">
                  <Text className="text-sm font-medium text-yellow-800 mb-1">
                    Import Order Matters
                  </Text>
                  <Text className="text-sm text-yellow-700">
                    Data is imported in sequence: Destinations → Hotels → Room
                    Configs → Rooms
                  </Text>
                </div>

                <div className="p-3 bg-blue-50 rounded-lg border border-blue-200">
                  <Text className="text-sm font-medium text-blue-800 mb-1">
                    File Format
                  </Text>
                  <Text className="text-sm text-blue-700">
                    Only Excel files (.xlsx, .xls) are supported. Maximum file
                    size: 10MB
                  </Text>
                </div>

                <div className="p-3 bg-green-50 rounded-lg border border-green-200">
                  <Text className="text-sm font-medium text-green-800 mb-1">
                    Data Validation
                  </Text>
                  <Text className="text-sm text-green-700">
                    All data is validated before import. Errors will be reported
                    for review
                  </Text>
                </div>
              </div>
            </div>
          </div>

          <div className="mt-8 pt-6 border-t">
            <div className="flex items-center gap-3">
              <div className="flex items-center gap-2">
                <Button
                  variant="secondary"
                  onClick={() =>
                    window.open("/admin/bulk-import/template", "_blank")
                  }
                  className="flex items-center gap-2"
                >
                  <Download className="w-4 h-4" />
                  Standard Template
                </Button>

                <Button
                  variant="secondary"
                  onClick={() =>
                    window.open(
                      "/admin/bulk-import/salesforce-template",
                      "_blank"
                    )
                  }
                  className="flex items-center gap-2 border-orange-300 text-orange-600 hover:bg-orange-50"
                >
                  <Download className="w-4 h-4" />
                  Salesforce Template
                </Button>
              </div>

              <Button
                onClick={() => setImportModalOpen(true)}
                className="flex items-center gap-2 bg-blue-600 text-white hover:bg-blue-700"
              >
                <UploadIcon className="w-4 h-4" />
                Start Import
              </Button>
            </div>
          </div>
        </div>

        {/* Import History */}
        <div className="bg-white rounded-lg border">
          <div className="p-6 border-b">
            <div className="flex items-center justify-between">
              <div>
                <Heading
                  level="h2"
                  className="text-xl font-semibold text-gray-900 mb-2"
                >
                  Import History
                </Heading>
                <Text className="text-gray-600">
                  Track your recent bulk import operations
                </Text>
              </div>
              <OutlineButton onClick={fetchImportHistory} disabled={isLoading}>
                Refresh
              </OutlineButton>
            </div>
          </div>

          <div className="p-6">
            {isLoading ? (
              <div className="text-center py-8">
                <Text className="text-gray-500">Loading import history...</Text>
              </div>
            ) : importHistory.length === 0 ? (
              <div className="text-center py-8">
                <Database className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                <Text className="text-gray-500 mb-2">No imports yet</Text>
                <Text className="text-sm text-gray-400">
                  Your import history will appear here after you perform your
                  first bulk import.
                </Text>
              </div>
            ) : (
              <div className="space-y-4">
                {importHistory.map((item) => (
                  <div key={item.id} className="border rounded-lg p-4">
                    <div className="flex items-center justify-between mb-3">
                      <div className="flex items-center gap-3">
                        <div
                          className={`w-3 h-3 rounded-full ${
                            item.status === "completed"
                              ? "bg-green-500"
                              : item.status === "failed"
                              ? "bg-red-500"
                              : "bg-yellow-500"
                          }`}
                        />
                        <Text className="font-medium">{item.filename}</Text>
                        <span
                          className={`px-2 py-1 rounded-full text-xs font-medium ${
                            item.status === "completed"
                              ? "bg-green-100 text-green-800"
                              : item.status === "failed"
                              ? "bg-red-100 text-red-800"
                              : "bg-yellow-100 text-yellow-800"
                          }`}
                        >
                          {item.status.charAt(0).toUpperCase() +
                            item.status.slice(1)}
                        </span>
                      </div>
                      <Text className="text-sm text-gray-500">
                        {new Date(item.created_at).toLocaleDateString()}{" "}
                        {new Date(item.created_at).toLocaleTimeString()}
                      </Text>
                    </div>

                    {item.summary && (
                      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                        <div>
                          <Text className="text-gray-600">Destinations</Text>
                          <Text className="font-medium">
                            {item.summary.destinations.successful} successful,{" "}
                            {item.summary.destinations.failed} failed
                          </Text>
                        </div>
                        <div>
                          <Text className="text-gray-600">Hotels</Text>
                          <Text className="font-medium">
                            {item.summary.hotels.successful} successful,{" "}
                            {item.summary.hotels.failed} failed
                          </Text>
                        </div>
                        <div>
                          <Text className="text-gray-600">Room Configs</Text>
                          <Text className="font-medium">
                            {item.summary.room_configs.successful} successful,{" "}
                            {item.summary.room_configs.failed} failed
                          </Text>
                        </div>
                        <div>
                          <Text className="text-gray-600">Rooms</Text>
                          <Text className="font-medium">
                            {item.summary.rooms.successful} successful,{" "}
                            {item.summary.rooms.failed} failed
                          </Text>
                        </div>
                      </div>
                    )}

                    {item.errors && item.errors.length > 0 && (
                      <div className="mt-3 p-3 bg-red-50 rounded-lg">
                        <Text className="text-sm font-medium text-red-800 mb-2">
                          Errors:
                        </Text>
                        <ul className="text-sm text-red-700 space-y-1">
                          {item.errors.slice(0, 3).map((error, index) => (
                            <li key={index}>• {error}</li>
                          ))}
                          {item.errors.length > 3 && (
                            <li className="text-red-600">
                              ... and {item.errors.length - 3} more errors
                            </li>
                          )}
                        </ul>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      </Container>

      {/* Bulk Import Modal */}
      <BulkImportModal
        open={importModalOpen}
        onClose={() => setImportModalOpen(false)}
        onImportComplete={handleImportComplete}
      />
    </>
  );
};

export default BulkImportPage;
