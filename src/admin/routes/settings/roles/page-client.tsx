import {
  Con<PERSON>er,
  Button,
  Input,
  Badge,
  Text,
  Heading,
  Table,
  Select,
  Alert,
  Toaster,
  toast,
  Drawer,
  DropdownMenu,
  Tooltip,
  FocusModal,
} from "@camped-ai/ui";
import {
  PERMISSION_GROUPS,
  applyPermissionHierarchy,
} from "../../../../modules/rbac/permissions";
import { PermissionTable } from "../../../components/rbac/PermissionTable";
import { ScreenPermission } from "../../../../modules/rbac/types";
import {
  Users,
  Shield,
  Plus,
  Edit,
  Trash2,
  Eye,
  RefreshCw,
  CheckCircle,
  XCircle,
  MoreHorizontal,
} from "lucide-react";
import { sdk } from "../../../lib/sdk";
import { useState, useEffect, useRef } from "react";
import {
  UserRoleManager,
  UserRoleManagerRef,
} from "../../../components/rbac/UserRoleManager";
import PermissionBasedSidebarHider from "../../../widgets/permission-based-sidebar-hider";
import { useRbac, rbacQueryKeys } from "../../../hooks/use-rbac";
import { useQueryClient } from "@tanstack/react-query";
import UserStatusBadge from "../../../components/shared/UserStatusBadge";
import UserActionsDropdown from "../../../components/shared/UserActionsDropdown";


interface User {
  id: string;
  email: string;
  first_name?: string;
  last_name?: string;
  created_at: string;
  updated_at: string;
  metadata?: any;
}

export default function PageClient() {
  const { hasPermission, currentUser } = useRbac();
  const queryClient = useQueryClient();

  // Check if user has any permission to access this page
  const canAccessPage =
    hasPermission("user_management:view") ||
    hasPermission("role_management:view") ||
    hasPermission("user_management:create") ||
    hasPermission("role_management:create");

  const [activeTab, setActiveTab] = useState<"users" | "roles">("users");
  const [searchQuery, setSearchQuery] = useState("");
  const [roleSearchQuery, setRoleSearchQuery] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");
  const [roleFilter, setRoleFilter] = useState("all");
  const [users, setUsers] = useState<User[]>([]);

  const [customRoles, setCustomRoles] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // User creation state (formerly invite management)
  const [showInviteForm, setShowInviteForm] = useState(false);
  const [inviteEmail, setInviteEmail] = useState("");
  const [inviteFirstName, setInviteFirstName] = useState("");
  const [inviteLastName, setInviteLastName] = useState("");
  const [inviteSelectedRole, setInviteSelectedRole] = useState("");
  const [inviteLoading, setInviteLoading] = useState(false);
  const [inviteError, setInviteError] = useState<string | null>(null);

  // Role form state
  const [showRoleForm, setShowRoleForm] = useState(false);
  const [roleName, setRoleName] = useState("");
  const [roleDescription, setRoleDescription] = useState("");
  const [rolePermissions, setRolePermissions] = useState<ScreenPermission[]>(
    []
  );
  const [roleLoading, setRoleLoading] = useState(false);

  // Role view/edit state
  const [selectedRole, setSelectedRole] = useState<any>(null);
  const [showRoleViewModal, setShowRoleViewModal] = useState(false);
  const [showRoleEditModal, setShowRoleEditModal] = useState(false);
  const [editingRole, setEditingRole] = useState<any>(null);

  // User modal state
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [showUserModal, setShowUserModal] = useState(false);
  const userRoleManagerRef = useRef<UserRoleManagerRef>(null);
  const [canUpdateRole, setCanUpdateRole] = useState(false);
  const [isUpdating, setIsUpdating] = useState(false);
  const [currentRoleData, setCurrentRoleData] = useState<any>(null);
  const [togglingUsers, setTogglingUsers] = useState<Set<string>>(new Set());

  // Deactivation confirmation modal state
  const [showDeactivateModal, setShowDeactivateModal] = useState(false);
  const [userToDeactivate, setUserToDeactivate] = useState<User | null>(null);
  const [confirmationText, setConfirmationText] = useState("");
  const [isDeactivating, setIsDeactivating] = useState(false);

  // Load users, invites, and roles from API
  const loadData = async () => {
    try {
      setLoading(true);
      setError(null);

      // Load users
      const usersResponse = await sdk.admin.user.list();
      const mappedUsers = (usersResponse.users || []).map((user) => ({
        ...user,
        first_name: user.first_name || undefined,
        last_name: user.last_name || undefined,
      }));
      setUsers(mappedUsers);

      // Load custom roles
      try {
        const rolesResponse = await fetch("/admin/roles");
        if (rolesResponse.ok) {
          const rolesData = await rolesResponse.json();
          setCustomRoles(rolesData.roles || []);
        }
      } catch (roleError) {
        console.error("Failed to load roles:", roleError);
        // Don't fail the entire load if roles fail
      }
    } catch (err) {
      const errorMessage =
        err instanceof Error ? err.message : "Failed to load data";
      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    // Only load data if current user is available (authenticated)
    if (currentUser) {
      loadData();
    } else {
      // Keep loading state true while waiting for authentication
      setLoading(true);
      setError(null);
    }
  }, [currentUser]);

  // User creation functions (formerly invite management)
  const handleSendInvite = async () => {
    if (
      !inviteEmail ||
      !inviteFirstName ||
      !inviteLastName ||
      !inviteSelectedRole
    ) {
      return;
    }

    const emailToInvite = inviteEmail; // Store email for toast message
    const selectedRoleName =
      roleStats.find((role) => role.id === inviteSelectedRole)?.name ||
      inviteSelectedRole;

    try {
      setInviteLoading(true);
      setInviteError(null); // Clear any previous errors

      // Create user directly (bypassing invite system)
      // Determine if selected role is system or custom role
      const selectedRoleData = roleStats.find(
        (role) => role.id === inviteSelectedRole
      );
      const isSystemRole = selectedRoleData?.isSystemRole || false;

      // Build user creation payload
      const userPayload: any = {
        email: inviteEmail,
        first_name: inviteFirstName,
        last_name: inviteLastName,
        custom_permissions: [],
      };

      // Add role field based on type
      if (isSystemRole) {
        userPayload.role = inviteSelectedRole;
      } else {
        userPayload.role_id = inviteSelectedRole;
      }

      // Create user directly using new endpoint
      const response = await fetch("/admin/users", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          // Use the same authentication as the SDK
        },
        credentials: "include", // Include cookies for authentication
        body: JSON.stringify(userPayload),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || "Failed to create user");
      }

      const result = await response.json();
      console.log("User created successfully:", result);

      setInviteEmail("");
      setInviteFirstName("");
      setInviteLastName("");
      setInviteSelectedRole("");
      setInviteError(null);
      setShowInviteForm(false);
      await loadData(); // Refresh data

      // Show success toast
      toast.success("User created successfully", {
        description: `User account created for ${emailToInvite} with role: ${selectedRoleName}. Welcome email sent with password setup instructions.`,
      });
    } catch (err) {
      console.error("User creation error:", err);

      // Handle API error responses
      let errorMessage = "Failed to create user";

      if (err && typeof err === "object") {
        // Check if it's a response error with data
        if (
          "response" in err &&
          err.response &&
          typeof err.response === "object"
        ) {
          const response = err.response as any;
          if (response.data && response.data.message) {
            errorMessage = response.data.message;
          } else if (response.statusText) {
            errorMessage = response.statusText;
          }
        }
        // Check if it's a direct error object with message
        else if ("message" in err && typeof err.message === "string") {
          errorMessage = err.message;
        }
        // Check if the error itself has the API error structure
        else if ("type" in err && "message" in err) {
          errorMessage = (err as any).message;
        }
      }

      // Set error state and show error toast
      setInviteError(errorMessage);
      toast.error("Failed to create user", {
        description: errorMessage,
      });
    } finally {
      setInviteLoading(false);
    }
  };

  // Handle deactivation confirmation
  const handleDeactivateUser = (user: User) => {
    setUserToDeactivate(user);
    setShowDeactivateModal(true);
    setConfirmationText("");
  };

  // Close deactivation modal
  const closeDeactivateModal = () => {
    setShowDeactivateModal(false);
    setUserToDeactivate(null);
    setConfirmationText("");
    setIsDeactivating(false);
  };

  // Confirm deactivation
  const confirmDeactivation = async () => {
    if (
      !userToDeactivate ||
      confirmationText.trim().toUpperCase() !== "CONFIRM"
    )
      return;

    try {
      setIsDeactivating(true);

      // Call the role API to deactivate the user
      const response = await fetch(`/admin/users/${userToDeactivate.id}/role`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          is_active: false,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || "Failed to deactivate user");
      }

      // Invalidate React Query cache to ensure fresh data
      await queryClient.invalidateQueries({ queryKey: ["users"] });
      await queryClient.invalidateQueries({ queryKey: ["rbac"] });
      await queryClient.invalidateQueries({ queryKey: rbacQueryKeys.all });

      // Refresh local data
      await loadData();
      closeDeactivateModal();

      // Show success toast
      toast.success("User deactivated successfully", {
        description: "User will no longer have access to the system",
      });
    } catch (err) {
      setError(
        err instanceof Error ? err.message : "Failed to deactivate user"
      );
    } finally {
      setIsDeactivating(false);
    }
  };

  // Toggle user active/inactive status (soft delete)
  const handleToggleUserStatus = async (
    userId: string,
    currentStatus: boolean
  ) => {
    // If deactivating, show confirmation modal
    if (currentStatus) {
      const user = users.find((u) => u.id === userId);
      if (user) {
        handleDeactivateUser(user);
        return;
      }
    }

    // For activation, proceed directly
    try {
      // Add to toggling set to show loading state
      setTogglingUsers((prev) => new Set(prev).add(userId));

      // Call the role API to update the user's active status
      const response = await fetch(`/admin/users/${userId}/role`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          is_active: !currentStatus,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || "Failed to update user status");
      }

      // Invalidate React Query cache to ensure fresh data
      await queryClient.invalidateQueries({ queryKey: ["users"] });
      await queryClient.invalidateQueries({ queryKey: ["rbac"] });
      await queryClient.invalidateQueries({ queryKey: rbacQueryKeys.all });

      // Refresh local data
      await loadData();

      // Show success toast
      toast.success("User activated successfully", {
        description: "User can now access the system",
      });
    } catch (err) {
      setError(
        err instanceof Error ? err.message : "Failed to update user status"
      );
    } finally {
      // Remove from toggling set
      setTogglingUsers((prev) => {
        const newSet = new Set(prev);
        newSet.delete(userId);
        return newSet;
      });
    }
  };

  // Check if role name is unique
  const isRoleNameUnique = (name: string) => {
    const trimmedName = name.trim().toLowerCase();
    return !roleStats.some((role) => role.name.toLowerCase() === trimmedName);
  };

  // Create role
  const handleCreateRole = async () => {
    if (!roleName.trim()) return;

    // Check if role has at least one permission
    if (rolePermissions.length === 0) {
      setError(
        "Cannot create role with 0 permissions. Please select at least one permission."
      );
      return;
    }

    // Check for unique role name
    if (!isRoleNameUnique(roleName)) {
      setError("Role name already exists. Please choose a different name.");
      return;
    }

    setRoleLoading(true);
    try {
      const response = await fetch("/admin/roles", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          name: roleName.trim(),
          description:
            roleDescription.trim() ||
            `${roleName} role with custom permissions`,
          permissions: rolePermissions,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || "Failed to create role");
      }

      toast.success("Role created successfully", {
        description: `Role "${roleName}" has been created with ${rolePermissions.length} permissions`,
      });

      setShowRoleForm(false);
      setRoleName("");
      setRoleDescription("");
      setRolePermissions([]);
      setError(null); // Clear any previous errors
      await loadData(); // Refresh the data
    } catch (error) {
      console.error("Error creating role:", error);
      setError(
        error instanceof Error ? error.message : "Failed to create role"
      );
    } finally {
      setRoleLoading(false);
    }
  };

  // Permission management helpers
  const handlePermissionToggle = (permission: ScreenPermission) => {
    setRolePermissions((prev) => {
      const isCurrentlySelected = prev.includes(permission);
      const isSelecting = !isCurrentlySelected;

      // Apply hierarchical permission logic
      const newPermissions = applyPermissionHierarchy(
        prev,
        permission,
        isSelecting
      );
      return newPermissions;
    });
  };





  // Role management functions
  const handleViewRole = (role: any) => {
    setSelectedRole(role);
    setShowRoleViewModal(true);
  };

  const handleEditRole = (role: any) => {
    setEditingRole(role);
    setRoleName(role.name);
    setRoleDescription(role.description);
    setRolePermissions(role.permissions || []);
    setShowRoleEditModal(true);
  };

  const handleUpdateRole = async () => {
    if (!editingRole || !roleName.trim()) return;

    // Check if role has at least one permission
    if (rolePermissions.length === 0) {
      setError(
        "Cannot update role with 0 permissions. Please select at least one permission."
      );
      return;
    }

    // Check for unique role name (excluding current role)
    const trimmedName = roleName.trim().toLowerCase();
    const nameExists = roleStats.some(
      (role) =>
        role.name.toLowerCase() === trimmedName && role.id !== editingRole.id
    );

    if (nameExists) {
      setError("Role name already exists. Please choose a different name.");
      return;
    }

    setRoleLoading(true);
    try {
      const response = await fetch(`/admin/roles/${editingRole.id}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          name: roleName.trim(),
          description: roleDescription.trim(),
          permissions: rolePermissions,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || "Failed to update role");
      }

      toast.success("Role updated successfully", {
        description: `Role "${roleName}" has been updated`,
      });

      setShowRoleEditModal(false);
      setEditingRole(null);
      setRoleName("");
      setRoleDescription("");
      setRolePermissions([]);
      setError(null);
      await loadData();
    } catch (error) {
      console.error("Error updating role:", error);
      setError(
        error instanceof Error ? error.message : "Failed to update role"
      );
    } finally {
      setRoleLoading(false);
    }
  };

  const handleDeleteRole = async (roleId: string) => {
    // Find the role to get user count
    const role = roleStats.find((r) => r.id === roleId);

    if (role && role.userCount > 0) {
      alert(
        `Cannot delete role "${role.name}". ${role.userCount} user${
          role.userCount > 1 ? "s are" : " is"
        } currently assigned to this role. Please reassign or remove these users before deleting the role.`
      );
      return;
    }

    if (
      !confirm(
        "Are you sure you want to delete this role? This action cannot be undone."
      )
    ) {
      return;
    }

    try {
      const response = await fetch(`/admin/roles/${roleId}`, {
        method: "DELETE",
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || "Failed to delete role");
      }

      toast.success("Role deleted successfully");
      await loadData();
    } catch (error) {
      console.error("Error deleting role:", error);
      setError(
        error instanceof Error ? error.message : "Failed to delete role"
      );
    }
  };

  // Helper functions
  const getUserDisplayName = (user: User) => {
    if (user.first_name && user.last_name) {
      return `${user.first_name} ${user.last_name}`;
    }
    return user.email;
  };

  const getUserInitials = (user: User) => {
    if (user.first_name && user.last_name) {
      return `${user.first_name.charAt(0)}${user.last_name.charAt(
        0
      )}`.toUpperCase();
    }
    return user.email.charAt(0).toUpperCase();
  };

  // Process all roles from API (includes both system and custom roles)
  const roleStats = customRoles.map((role) => {
    const usersWithRole = users.filter((user) => {
      const userRbac = user.metadata?.rbac;
      // For system roles, check the role field; for custom roles, check role_id
      if (role.is_system_role) {
        return userRbac?.role === role.id;
      } else {
        return userRbac?.role_id === role.id;
      }
    });
    return {
      id: role.id,
      name: role.name,
      userCount: usersWithRole.length,
      description: role.description,
      isSystemRole: role.is_system_role,
      permissions: role.permissions || [],
      created_at: role.created_at,
    };
  });

  const getUserRole = (user: User) => {
    const rbacData = user.metadata?.rbac;
    if (!rbacData) return null;

    // If user is inactive, they should have no role
    if (rbacData.is_active === false) {
      return null;
    }

    // For system roles, look up the display name from roleStats
    if (rbacData.role) {
      const systemRole = roleStats.find(
        (role) => role.id === rbacData.role && role.isSystemRole
      );
      return systemRole ? systemRole.name : rbacData.role;
    }

    // For custom roles, look up the role name from customRoles
    if (rbacData.role_id) {
      const customRole = customRoles.find(
        (role) => role.id === rbacData.role_id
      );
      return customRole ? customRole.name : rbacData.role_id; // Fallback to ID if name not found
    }

    return null;
  };

  const getUserStatus = (user: User) => {
    // Check new user_status metadata first (this is the primary source)
    const userStatus = user.metadata?.user_status;
    if (userStatus && userStatus.is_active !== undefined) {
      return userStatus.is_active ? "Active" : "Inactive";
    }

    // Fallback to RBAC metadata for backward compatibility
    const isActive = user.metadata?.rbac?.is_active;
    if (isActive === undefined) return "Pending";
    return isActive ? "Active" : "Inactive";
  };

  // Manual refresh function
  const handleRefreshData = async () => {
    try {
      setLoading(true);

      // Clear all caches first
      await queryClient.invalidateQueries({ queryKey: ["users"] });
      await queryClient.invalidateQueries({ queryKey: ["rbac"] });
      await queryClient.invalidateQueries({ queryKey: rbacQueryKeys.all });

      // Force reload data
      await loadData();

      toast.success("Data refreshed successfully");
    } catch (error) {
      toast.error("Failed to refresh data");
    }
  };

  // Check if user is the owner (first user in the list)
  const isOwner = (user: User) => {
    return users.length > 0 && users[0].id === user.id;
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    });
  };

  const getTimeAgo = (dateString: string) => {
    const now = new Date();
    const date = new Date(dateString);
    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);

    if (diffInSeconds < 60) return "Just now";
    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m ago`;
    if (diffInSeconds < 86400)
      return `${Math.floor(diffInSeconds / 3600)}h ago`;
    if (diffInSeconds < 2592000)
      return `${Math.floor(diffInSeconds / 86400)}d ago`;
    return formatDate(dateString);
  };

  const formatLastLogin = (user: User) => {
    // For now, we'll use updated_at as a proxy for last login
    // In a real implementation, you'd have a separate last_login field
    return new Date(user.updated_at).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  // User modal functions
  const openUserModal = async (user: User) => {
    // Force invalidate the specific user's role cache before opening modal
    await queryClient.invalidateQueries({
      queryKey: rbacQueryKeys.userRole(user.id),
    });
    await queryClient.invalidateQueries({
      queryKey: [...rbacQueryKeys.all, "userRole"],
    });

    setSelectedUser(user);
    setShowUserModal(true);
  };

  const closeUserModal = () => {
    setShowUserModal(false);
    setSelectedUser(null);
  };

  // Filter users based on search and filters
  const filteredUsers = users.filter((user) => {
    const matchesSearch =
      user.email.toLowerCase().includes(searchQuery.toLowerCase()) ||
      getUserDisplayName(user)
        .toLowerCase()
        .includes(searchQuery.toLowerCase());

    const matchesStatus =
      statusFilter === "all" ||
      getUserStatus(user).toLowerCase() === statusFilter.toLowerCase();

    const matchesRole =
      roleFilter === "all" ||
      getUserRole(user)?.toLowerCase() === roleFilter.toLowerCase();

    return matchesSearch && matchesStatus && matchesRole;
  });

  // Filter roles based on search
  const filteredRoles = roleStats.filter((role) => {
    const matchesSearch =
      role.name.toLowerCase().includes(roleSearchQuery.toLowerCase()) ||
      role.description.toLowerCase().includes(roleSearchQuery.toLowerCase());

    return matchesSearch;
  });

  const getRoleBadge = (role: string, user: User) => {
    // Check if user is inactive
    const isInactive = getUserStatus(user) === "Inactive";

    if (!role || isInactive) {
      return (
        <Badge
          className={`text-xs px-2 py-1 rounded-full border ${
            isInactive
              ? "bg-rose-100 text-rose-700 border-rose-200 dark:bg-rose-900/30 dark:text-rose-300 dark:border-rose-800"
              : "bg-slate-100 text-slate-600 border-slate-200 dark:bg-slate-800 dark:text-slate-400 dark:border-slate-700"
          }`}
        >
          {isInactive ? "Role Removed" : "No Role"}
        </Badge>
      );
    }

    switch (role) {
      case "admin":
        return (
          <Badge className="text-xs px-2 py-1 rounded-full bg-violet-100 text-violet-700 border border-violet-200 dark:bg-violet-900/30 dark:text-violet-300 dark:border-violet-800">
            System Administrator
          </Badge>
        );

      default:
        // For custom roles, show the role name with a different color
        return (
          <Badge className="text-xs px-2 py-1 rounded-full bg-indigo-100 text-indigo-700 border border-indigo-200 dark:bg-indigo-900/30 dark:text-indigo-300 dark:border-indigo-800">
            {role}
          </Badge>
        );
    }
  };

  // Access control check
  if (!canAccessPage) {
    return (
      <Container className="p-6 max-w-7xl mx-auto">
        <div className="text-center py-12">
          <Shield className="h-16 w-16 text-gray-400 dark:text-gray-500 mx-auto mb-4" />
          <Heading level="h2" className="text-gray-600 dark:text-gray-400 mb-2">
            Access Denied
          </Heading>
          <Text className="text-gray-500 dark:text-gray-400">
            You don't have permission to access user and role management.
          </Text>
        </div>
      </Container>
    );
  }

  return (
    <>
      <PermissionBasedSidebarHider />
      <Toaster />
      <Container className="p-6 max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-6">
          <Heading
            level="h1"
            className="text-2xl font-bold mb-2 text-gray-900 dark:text-gray-100"
          >
            User & Role Management
          </Heading>
          <Text className="text-gray-600 dark:text-gray-400">
            Manage users, roles, and permissions across your organization
          </Text>
        </div>

        {/* Tabs */}
        <div className="mb-6">
          <div className="flex space-x-1 border-b border-gray-200 dark:border-gray-700">
            {hasPermission("user_management:view") && (
              <button
                onClick={() => setActiveTab("users")}
                className={`flex items-center space-x-2 px-4 py-2 border-b-2 font-medium text-sm ${
                  activeTab === "users"
                    ? "border-blue-500 text-blue-600 dark:text-blue-400"
                    : "border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"
                }`}
              >
                <Users size={16} />
                <span>Users</span>
                <Badge className="rounded-full">{users.length}</Badge>
              </button>
            )}
            {hasPermission("role_management:view") && (
              <button
                onClick={() => setActiveTab("roles")}
                className={`flex items-center space-x-2 px-4 py-2 border-b-2 font-medium text-sm ${
                  activeTab === "roles"
                    ? "border-blue-500 text-blue-600 dark:text-blue-400"
                    : "border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"
                }`}
              >
                <Shield size={16} />
                <span>Roles</span>
                <Badge className="rounded-full">{roleStats.length}</Badge>
              </button>
            )}
          </div>
        </div>

        {/* Users Tab */}
        {activeTab === "users" && hasPermission("user_management:view") && (
          <div className="space-y-4">
            {/* Error Alert */}
            {error && <Alert variant="error">{error}</Alert>}

            {/* Search and Filters */}
            <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
              <div className="flex flex-col sm:flex-row gap-3 flex-1">
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none"></div>
                  <Input
                    placeholder="Search users..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="w-64"
                  />
                </div>
                <Select value={statusFilter} onValueChange={setStatusFilter}>
                  <Select.Trigger className="w-32">
                    <Select.Value placeholder="All Status" />
                  </Select.Trigger>
                  <Select.Content>
                    <Select.Item value="all">All Status</Select.Item>
                    <Select.Item value="active">Active</Select.Item>
                    <Select.Item value="inactive">Inactive</Select.Item>
                  </Select.Content>
                </Select>
                <Select value={roleFilter} onValueChange={setRoleFilter}>
                  <Select.Trigger className="w-32">
                    <Select.Value placeholder="All Roles" />
                  </Select.Trigger>
                  <Select.Content>
                    <Select.Item value="all">All Roles</Select.Item>
                    {roleStats.map((role) => (
                      <Select.Item
                        key={role.id}
                        value={role.name.toLowerCase()}
                      >
                        {role.name}
                      </Select.Item>
                    ))}
                  </Select.Content>
                </Select>
              </div>
              <div className="flex items-center space-x-2">
                <Button
                  variant="secondary"
                  onClick={handleRefreshData}
                  disabled={loading}
                >
                  <RefreshCw
                    size={16}
                    className={loading ? "animate-spin" : ""}
                  />
                </Button>
                {hasPermission("user_management:create") && (
                  <Button
                    className="flex items-center space-x-2"
                    onClick={() => setShowInviteForm(true)}
                  >
                    <Plus size={16} />
                    <span>Add New User</span>
                  </Button>
                )}
              </div>
            </div>

            {/* Users Table */}
            <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 overflow-hidden">
              {loading ? (
                <div className="flex flex-col items-center justify-center py-16 space-y-4">
                  <div className="animate-spin rounded-full h-10 w-10 border-b-2 border-blue-600"></div>
                  <Text className="text-gray-500 dark:text-gray-400 text-lg">
                    Loading users...
                  </Text>
                </div>
              ) : filteredUsers.length === 0 ? (
                <div className="flex flex-col items-center justify-center py-16 space-y-4">
                  <div className="w-16 h-16 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center">
                    <Users
                      size={32}
                      className="text-gray-400 dark:text-gray-500"
                    />
                  </div>
                  <div className="text-center space-y-2">
                    <Text className="text-gray-900 dark:text-gray-100 font-semibold text-lg">
                      No users found
                    </Text>
                    <Text className="text-gray-500 dark:text-gray-400 text-sm max-w-md mx-auto">
                      {searchQuery
                        ? "Try adjusting your search criteria or clear the search to see all users"
                        : "Get started by inviting your first user to the platform"}
                    </Text>
                  </div>
                </div>
              ) : (
                <div className="overflow-x-auto">
                  <Table>
                    <Table.Header className="bg-gray-50 dark:bg-gray-700">
                      <Table.Row>
                        <Table.HeaderCell className="px-6 py-4 text-left text-xs font-semibold text-gray-600 dark:text-gray-300 uppercase tracking-wider min-w-[280px]">
                          USER
                        </Table.HeaderCell>
                        <Table.HeaderCell className="px-6 py-4 text-left text-xs font-semibold text-gray-600 dark:text-gray-300 uppercase tracking-wider min-w-[120px]">
                          STATUS
                        </Table.HeaderCell>
                        <Table.HeaderCell className="px-6 py-4 text-left text-xs font-semibold text-gray-600 dark:text-gray-300 uppercase tracking-wider min-w-[160px]">
                          ROLE
                        </Table.HeaderCell>
                        <Table.HeaderCell className="px-6 py-4 text-left text-xs font-semibold text-gray-600 dark:text-gray-300 uppercase tracking-wider min-w-[180px]">
                          LAST ACTIVITY
                        </Table.HeaderCell>
                        <Table.HeaderCell className="px-6 py-4 text-left text-xs font-semibold text-gray-600 dark:text-gray-300 uppercase tracking-wider min-w-[140px]">
                          JOINED
                        </Table.HeaderCell>
                        <Table.HeaderCell className="px-6 py-4 text-left text-xs font-semibold text-gray-600 dark:text-gray-300 uppercase tracking-wider min-w-[100px]">
                          ACTIONS
                        </Table.HeaderCell>
                      </Table.Row>
                    </Table.Header>
                    <Table.Body className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                      {filteredUsers.map((user) => (
                        <Table.Row
                          key={user.id}
                          className="hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-150"
                        >
                          <Table.Cell className="px-6 py-4 min-w-[280px]">
                            <div className="flex items-center space-x-4">
                              <div className="flex-shrink-0">
                                <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-full flex items-center justify-center shadow-sm">
                                  <span className="text-white font-semibold text-sm">
                                    {getUserInitials(user)}
                                  </span>
                                </div>
                              </div>
                              <div className="flex-1 min-w-0">
                                <div className="flex items-start flex-col space-y-1">
                                  <div className="flex items-center space-x-2 w-full">
                                    <Tooltip content={getUserDisplayName(user)}>
                                      <p className="text-sm font-semibold text-gray-900 dark:text-gray-100 truncate max-w-[180px]">
                                        {getUserDisplayName(user)}
                                      </p>
                                    </Tooltip>
                                  </div>
                                  <Tooltip content={user.email}>
                                    <p className="text-sm text-gray-500 dark:text-gray-400 truncate max-w-[200px]">
                                      {user.email}
                                    </p>
                                  </Tooltip>
                                </div>
                              </div>
                            </div>
                          </Table.Cell>
                          <Table.Cell className="px-6 py-4 min-w-[120px]">
                            <div className="flex items-center">
                              <UserStatusBadge user={user} showTooltip={true} />
                            </div>
                          </Table.Cell>
                          <Table.Cell className="px-6 py-4 min-w-[160px]">
                            <div className="flex items-center">
                              {getRoleBadge(getUserRole(user), user)}
                            </div>
                          </Table.Cell>
                          <Table.Cell className="px-6 py-4 min-w-[180px]">
                            <div className="space-y-1">
                              <div className="text-sm text-gray-900 dark:text-gray-100 font-medium">
                                {formatLastLogin(user)}
                              </div>
                              <div className="text-xs text-gray-500 dark:text-gray-400">
                                {getTimeAgo(user.updated_at)}
                              </div>
                            </div>
                          </Table.Cell>
                          <Table.Cell className="px-6 py-4 min-w-[140px]">
                            <div className="space-y-1">
                              <div className="text-sm text-gray-900 dark:text-gray-100 font-medium">
                                {formatDate(user.created_at)}
                              </div>
                              <div className="text-xs text-gray-500 dark:text-gray-400">
                                {getTimeAgo(user.created_at)}
                              </div>
                            </div>
                          </Table.Cell>
                          <Table.Cell className="px-6 py-4 min-w-[100px]">
                            <div className="flex items-center justify-center">
                              {isOwner(user) ? (
                                <Badge className="text-xs px-2 py-1 rounded-full bg-orange-100 text-orange-700 border border-orange-200 dark:bg-orange-900/30 dark:text-orange-300 dark:border-orange-800">
                                  Owner
                                </Badge>
                              ) : !(hasPermission("user_management:edit") || hasPermission("user_management:activate") || hasPermission("user_management:deactivate")) ? (
                                <Badge className="text-xs px-2 py-1 rounded-full bg-muted text-muted-foreground border border-border">
                                  View Only
                                </Badge>
                              ) : (
                                <UserActionsDropdown
                                  user={user}
                                  onViewDetails={() => openUserModal(user)}
                                  showViewDetails={true}
                                />
                              )}
                            </div>
                          </Table.Cell>
                        </Table.Row>
                      ))}
                    </Table.Body>
                  </Table>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Roles Tab */}
        {activeTab === "roles" && hasPermission("role_management:view") && (
          <div className="space-y-4">
            {/* Search and Add Role */}
            <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
              <div className="relative">
                <Input
                  placeholder="Search roles..."
                  value={roleSearchQuery}
                  onChange={(e) => setRoleSearchQuery(e.target.value)}
                  className="w-64"
                />
              </div>
              {hasPermission("role_management:create") && (
                <Button
                  className="flex items-center space-x-2"
                  onClick={() => {
                    // Reset form data to ensure clean state
                    setRoleName("");
                    setRoleDescription("");
                    setRolePermissions([]);
                    setError(null);
                    setShowRoleForm(true);
                  }}
                >
                  <Plus size={16} />
                  <span>Add Role</span>
                </Button>
              )}
            </div>

            {/* Roles Table */}
            <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 overflow-hidden">
              {filteredRoles.length === 0 ? (
                <div className="flex flex-col items-center justify-center py-16 space-y-4">
                  <div className="w-16 h-16 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center">
                    <Shield
                      size={32}
                      className="text-gray-400 dark:text-gray-500"
                    />
                  </div>
                  <div className="text-center space-y-2">
                    <Text className="text-gray-900 dark:text-gray-100 font-semibold text-lg">
                      No roles found
                    </Text>
                    <Text className="text-gray-500 dark:text-gray-400 text-sm max-w-md mx-auto">
                      {roleSearchQuery
                        ? "Try adjusting your search criteria or clear the search to see all roles"
                        : "Create your first custom role to manage user permissions"}
                    </Text>
                  </div>
                </div>
              ) : (
                <div className="overflow-x-auto">
                  <Table>
                    <Table.Header className="bg-gray-50 dark:bg-gray-700">
                      <Table.Row>
                        <Table.HeaderCell className="px-6 py-4 text-left text-xs font-semibold text-gray-600 dark:text-gray-300 uppercase tracking-wider min-w-[200px]">
                          ROLE
                        </Table.HeaderCell>
                        <Table.HeaderCell className="px-6 py-4 text-left text-xs font-semibold text-gray-600 dark:text-gray-300 uppercase tracking-wider min-w-[250px]">
                          DESCRIPTION
                        </Table.HeaderCell>
                        <Table.HeaderCell className="px-6 py-4 text-left text-xs font-semibold text-gray-600 dark:text-gray-300 uppercase tracking-wider min-w-[120px]">
                          USERS
                        </Table.HeaderCell>
                        <Table.HeaderCell className="px-6 py-4 text-left text-xs font-semibold text-gray-600 dark:text-gray-300 uppercase tracking-wider min-w-[100px]">
                          ACTIONS
                        </Table.HeaderCell>
                      </Table.Row>
                    </Table.Header>
                    <Table.Body className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                      {filteredRoles.map((role) => (
                        <Table.Row
                          key={role.id || role.name}
                          className="hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-150"
                        >
                          <Table.Cell className="px-6 py-4 min-w-[200px]">
                            <div className="flex items-center space-x-3">
                              <div className="flex-shrink-0">
                                <div
                                  className={`w-10 h-10 ${
                                    role.isSystemRole
                                      ? "bg-gradient-to-br from-red-500 to-red-600"
                                      : "bg-gradient-to-br from-green-500 to-green-600"
                                  } rounded-full flex items-center justify-center text-white text-sm font-semibold shadow-sm`}
                                >
                                  {role.name.charAt(0).toUpperCase()}
                                </div>
                              </div>
                              <div className="flex-1 min-w-0">
                                <div className="font-semibold text-gray-900 dark:text-gray-100 text-sm">
                                  {role.name === "admin"
                                    ? "System Administrator"
                                    : role.name}
                                </div>
                              </div>
                            </div>
                          </Table.Cell>
                          <Table.Cell className="px-6 py-4 min-w-[250px]">
                            <Text className="text-sm text-gray-600 dark:text-gray-400">
                              {role.description}
                            </Text>
                          </Table.Cell>

                          <Table.Cell className="px-6 py-4 min-w-[120px]">
                            <div className="flex items-center space-x-2">
                              <Users
                                size={14}
                                className="text-gray-400 dark:text-gray-500 flex-shrink-0"
                              />
                              <Text className="text-sm text-gray-900 dark:text-gray-100 font-medium">
                                {role.userCount}
                              </Text>
                            </div>
                          </Table.Cell>

                          <Table.Cell className="px-6 py-4 min-w-[100px]">
                            <div className="flex items-center justify-center">
                              <DropdownMenu>
                                <DropdownMenu.Trigger asChild>
                                  <Button
                                    variant="secondary"
                                    size="small"
                                    className="h-8 w-8 p-0"
                                  >
                                    <MoreHorizontal size={16} />
                                  </Button>
                                </DropdownMenu.Trigger>
                                <DropdownMenu.Content align="end">
                                  {hasPermission("role_management:view") && (
                                    <DropdownMenu.Item
                                      onClick={() => handleViewRole(role)}
                                      className="flex items-center gap-2"
                                    >
                                      <Eye size={14} />
                                      View Details
                                    </DropdownMenu.Item>
                                  )}
                                  {!role.isSystemRole &&
                                    hasPermission("role_management:edit") && (
                                      <DropdownMenu.Item
                                        onClick={() => handleEditRole(role)}
                                        className="flex items-center gap-2"
                                      >
                                        <Edit size={14} />
                                        Edit Role
                                      </DropdownMenu.Item>
                                    )}
                                  {!role.isSystemRole &&
                                    hasPermission("role_management:delete") && (
                                      <DropdownMenu.Item
                                        onClick={() =>
                                          handleDeleteRole(role.id)
                                        }
                                        disabled={role.userCount > 0}
                                        className={`flex items-center gap-2 ${
                                          role.userCount > 0
                                            ? "text-gray-400 cursor-not-allowed"
                                            : "text-red-600"
                                        }`}
                                      >
                                        <Trash2 size={14} />
                                        Delete Role
                                      </DropdownMenu.Item>
                                    )}
                                </DropdownMenu.Content>
                              </DropdownMenu>
                            </div>
                          </Table.Cell>
                        </Table.Row>
                      ))}
                    </Table.Body>
                  </Table>
                </div>
              )}
            </div>
          </div>
        )}
      </Container>

      {/* Create User Drawer */}
      <Drawer open={showInviteForm} onOpenChange={setShowInviteForm}>
        <Drawer.Content>
          <Drawer.Header>
            <Drawer.Title>Create New User</Drawer.Title>
            <Drawer.Description>
              Create a new user account and assign them a role. They will
              receive a welcome email with password setup instructions.
            </Drawer.Description>
          </Drawer.Header>

          <Drawer.Body className="overflow-y-auto p-6">
            <div className="flex flex-col gap-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium mb-2 text-gray-700 dark:text-gray-300">
                    First Name
                  </label>
                  <Input
                    type="text"
                    placeholder="John"
                    value={inviteFirstName}
                    onChange={(e) => {
                      setInviteFirstName(e.target.value);
                      // Clear error when user starts typing
                      if (inviteError) setInviteError(null);
                    }}
                    disabled={inviteLoading}
                    autoFocus
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium mb-2 text-gray-700 dark:text-gray-300">
                    Last Name
                  </label>
                  <Input
                    type="text"
                    placeholder="Doe"
                    value={inviteLastName}
                    onChange={(e) => {
                      setInviteLastName(e.target.value);
                      // Clear error when user starts typing
                      if (inviteError) setInviteError(null);
                    }}
                    disabled={inviteLoading}
                  />
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium mb-2 text-gray-700 dark:text-gray-300">
                  Email Address
                </label>
                <Input
                  type="email"
                  placeholder="<EMAIL>"
                  value={inviteEmail}
                  onChange={(e) => {
                    setInviteEmail(e.target.value);
                    // Clear error when user starts typing
                    if (inviteError) setInviteError(null);
                  }}
                  onKeyDown={(e) => {
                    if (
                      e.key === "Enter" &&
                      inviteEmail &&
                      inviteFirstName &&
                      inviteLastName &&
                      inviteSelectedRole &&
                      !inviteLoading
                    ) {
                      handleSendInvite();
                    }
                  }}
                  disabled={inviteLoading}
                />
              </div>

              <div>
                <label className="block text-sm font-medium mb-2">
                  Assign Role <span className="text-red-500">*</span>
                </label>
                <Select
                  value={inviteSelectedRole}
                  onValueChange={(value) => {
                    setInviteSelectedRole(value);
                    // Clear error when user changes role selection
                    if (inviteError) setInviteError(null);
                  }}
                  disabled={inviteLoading}
                >
                  <Select.Trigger className="w-full">
                    <Select.Value placeholder="Select a role for this user" />
                  </Select.Trigger>
                  <Select.Content>
                    {roleStats.length > 0 ? (
                      roleStats.map((role) => (
                        <Select.Item key={role.id} value={role.id}>
                          {role.name}{" "}
                          {role.isSystemRole ? "(System)" : "(Custom)"}
                        </Select.Item>
                      ))
                    ) : (
                      <Select.Item value="" disabled>
                        No roles available
                      </Select.Item>
                    )}
                  </Select.Content>
                </Select>
              </div>

              {/* Error Display */}
              {inviteError && <Alert variant="error">{inviteError}</Alert>}
            </div>
          </Drawer.Body>

          <Drawer.Footer>
            <div className="flex justify-end gap-3 w-full">
              <Button
                variant="secondary"
                onClick={() => {
                  setShowInviteForm(false);
                  setInviteEmail("");
                  setInviteFirstName("");
                  setInviteLastName("");
                  setInviteSelectedRole("");
                  setInviteError(null);
                }}
                disabled={inviteLoading}
              >
                Cancel
              </Button>
              <Button
                variant="primary"
                onClick={handleSendInvite}
                disabled={
                  !inviteEmail ||
                  !inviteFirstName ||
                  !inviteLastName ||
                  !inviteSelectedRole ||
                  inviteLoading
                }
              >
                {inviteLoading ? "Creating..." : "Create User"}
              </Button>
            </div>
          </Drawer.Footer>
        </Drawer.Content>
      </Drawer>

      {/* User Edit Drawer */}
      {showUserModal && selectedUser && (
        <Drawer open={showUserModal} onOpenChange={closeUserModal}>
          <Drawer.Content>
            <Drawer.Header>
              <Drawer.Title>
                <Text size="large" weight="plus">
                  Edit User
                </Text>
              </Drawer.Title>
            </Drawer.Header>
            <Drawer.Body className="overflow-y-auto p-6">
              <div className="flex flex-col gap-6">
                {/* User Information */}
                <div className="border border-border rounded-lg p-4">
                  <h3 className="text-lg font-medium mb-4 text-foreground">
                    User Information
                  </h3>
                  <div className="space-y-3">
                    <div className="flex items-center gap-2">
                      <Text className="text-sm font-medium text-foreground min-w-0 flex-shrink-0">
                        Email:
                      </Text>
                      <Text className="text-sm text-muted-foreground">
                        {selectedUser?.email}
                      </Text>
                    </div>

                    <div className="flex items-center gap-2">
                      <Text className="text-sm font-medium text-foreground min-w-0 flex-shrink-0">
                        Name:
                      </Text>
                      <Text className="text-sm text-muted-foreground">
                        {selectedUser ? getUserDisplayName(selectedUser) : "—"}
                      </Text>
                    </div>

                    <div className="flex items-center gap-2">
                      <Text className="text-sm font-medium text-foreground min-w-0 flex-shrink-0">
                        Created:
                      </Text>
                      <Text className="text-sm text-muted-foreground">
                        {formatDate(selectedUser.created_at)}
                      </Text>
                    </div>

                    <div className="flex items-center gap-2">
                      <Text className="text-sm font-medium text-foreground min-w-0 flex-shrink-0">
                        Last Updated:
                      </Text>
                      <Text className="text-sm text-muted-foreground">
                        {formatDate(selectedUser.updated_at)}
                      </Text>
                    </div>
                  </div>
                </div>

                {/* Role Management */}
                <div>
                  <h3 className="text-lg font-medium mb-4 text-foreground">
                    Role & Permissions
                  </h3>
                  <UserRoleManager
                    key={`${selectedUser?.id}-${showUserModal}`} // Force re-render on user change and modal open
                    ref={userRoleManagerRef}
                    user={selectedUser}
                    availableRoles={roleStats}
                    isOwner={isOwner(selectedUser)}
                    onRoleUpdated={async () => {
                      // Trigger data refresh with delay to ensure backend processing is complete
                      await new Promise((resolve) => setTimeout(resolve, 300));
                      await loadData();
                    }}
                    onRoleUpdateComplete={async () => {
                      // Refresh data and close modal with multiple refresh attempts
                      await new Promise((resolve) => setTimeout(resolve, 300));
                      await loadData();

                      // Additional refresh after a longer delay to catch any delayed updates
                      setTimeout(async () => {
                        await loadData();
                      }, 1000);

                      closeUserModal();
                    }}
                    onStateChange={(state) => {
                      setCanUpdateRole(state.canUpdateRole);
                      setIsUpdating(state.updating);
                      setCurrentRoleData(state.roleData);
                    }}
                  />
                </div>
              </div>
            </Drawer.Body>
            <Drawer.Footer>
              <div className="flex justify-between gap-4 w-full">
                <Button variant="secondary" onClick={closeUserModal}>
                  Close
                </Button>
                <div className="flex gap-2">
                  {/* Deactivate User Button - Only show if user has deactivate permission and user is active */}
                  {hasPermission("user_management:deactivate") &&
                    selectedUser &&
                    !isOwner(selectedUser) &&
                    getUserStatus(selectedUser) === "Active" && (
                      <Button
                        variant="secondary"
                        onClick={() => {
                          // Close the user modal first, then show deactivation modal
                          closeUserModal();
                          handleDeactivateUser(selectedUser);
                        }}
                        disabled={
                          togglingUsers.has(selectedUser.id) || isUpdating
                        }
                        className="text-red-600 hover:bg-red-50"
                      >
                        {togglingUsers.has(selectedUser.id)
                          ? "Processing..."
                          : "Deactivate User"}
                      </Button>
                    )}

                  {/* Activate User Button - Only show if user has activate permission and user is inactive */}
                  {hasPermission("user_management:activate") &&
                    selectedUser &&
                    !isOwner(selectedUser) &&
                    getUserStatus(selectedUser) !== "Active" && (
                      <Button
                        variant="secondary"
                        onClick={() => {
                          // For activation, proceed directly
                          handleToggleUserStatus(
                            selectedUser.id,
                            false // false means activate (not deactivate)
                          );
                        }}
                        disabled={
                          togglingUsers.has(selectedUser.id) || isUpdating
                        }
                        className="text-green-600 hover:bg-green-50"
                      >
                        {togglingUsers.has(selectedUser.id)
                          ? "Processing..."
                          : "Activate User"}
                      </Button>
                    )}

                  {/* Update User Role Button - Only show if user has edit permission */}
                  {hasPermission("user_management:edit") &&
                    selectedUser &&
                    !isOwner(selectedUser) && (
                      <Button
                        onClick={() =>
                          userRoleManagerRef.current?.handleUpdateRole()
                        }
                        disabled={!canUpdateRole || isUpdating}
                      >
                        {isUpdating
                          ? "Updating..."
                          : currentRoleData?.rbac
                          ? "Update User"
                          : "Assign User Role"}
                      </Button>
                    )}
                </div>
              </div>
            </Drawer.Footer>
          </Drawer.Content>
        </Drawer>
      )}

      {/* Create Role Drawer */}
      {showRoleForm && (
        <FocusModal
          open={showRoleForm}
          onOpenChange={() => setShowRoleForm(false)}
        >
          <FocusModal.Content>
            <FocusModal.Header>
              <FocusModal.Title>
                <Text size="large" weight="plus">
                  Create New Role
                </Text>
              </FocusModal.Title>
            </FocusModal.Header>
            <FocusModal.Body className="overflow-auto p-6">
              <div className="space-y-6">
                {/* Basic Information */}
                <div className="space-y-4">
                  <div>
                    <Text className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Role Name
                    </Text>
                    <Input
                      type="text"
                      placeholder="e.g. Hotel Manager"
                      value={roleName}
                      onChange={(e) => {
                        setRoleName(e.target.value);
                        if (error && error.includes("already exists")) {
                          setError(null); // Clear error when user starts typing
                        }
                      }}
                      disabled={roleLoading}
                      className="w-full"
                      autoFocus
                    />
                    {error && error.includes("already exists") && (
                      <Text className="text-red-600 text-sm mt-1">{error}</Text>
                    )}
                  </div>

                  <div>
                    <Text className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Description (Optional)
                    </Text>
                    <Input
                      type="text"
                      placeholder="Brief description of the role"
                      value={roleDescription}
                      onChange={(e) => setRoleDescription(e.target.value)}
                      disabled={roleLoading}
                      className="w-full"
                    />
                  </div>
                </div>

                {/* Permissions Configuration */}
                <PermissionTable
                  selectedPermissions={rolePermissions}
                  onPermissionToggle={handlePermissionToggle}
                  disabled={roleLoading}
                  loading={roleLoading}
                />
              </div>
            </FocusModal.Body>
            <FocusModal.Footer>
              <div className="flex justify-end gap-3 w-full">
                <Button
                  variant="secondary"
                  onClick={() => {
                    setShowRoleForm(false);
                    setRoleName("");
                    setRoleDescription("");
                    setRolePermissions([]);
                  }}
                  disabled={roleLoading}
                >
                  Cancel
                </Button>
                <Button
                  variant="primary"
                  onClick={handleCreateRole}
                  disabled={
                    !roleName.trim() ||
                    roleLoading ||
                    rolePermissions.length === 0
                  }
                >
                  {roleLoading
                    ? "Creating..."
                    : `Create Role (${rolePermissions.length} permissions)`}
                </Button>
              </div>
            </FocusModal.Footer>
          </FocusModal.Content>
        </FocusModal>
      )}

      {/* View Role Drawer */}
      {showRoleViewModal && selectedRole && (
        <FocusModal
          open={showRoleViewModal}
          onOpenChange={() => setShowRoleViewModal(false)}
        >
          <FocusModal.Content className="max-w-6xl">
            <FocusModal.Header>
              <FocusModal.Title>
                <Text size="large" weight="plus">
                  View Role: {selectedRole.name}
                </Text>
              </FocusModal.Title>
            </FocusModal.Header>
            <FocusModal.Body className="overflow-auto p-6">
              <div className="space-y-6">
                {/* Role Information */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
                  <div>
                    <Text className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      Role Name
                    </Text>
                    <Text className="text-gray-900 dark:text-gray-100 font-medium">
                      {selectedRole.name}
                    </Text>
                  </div>
                  <div>
                    <Text className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      Description
                    </Text>
                    <Text className="text-gray-600 dark:text-gray-400">
                      {selectedRole.description}
                    </Text>
                  </div>
                  <div>
                    <Text className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      Type
                    </Text>
                    <Badge
                      className={
                        selectedRole.isSystemRole
                          ? "bg-blue-100 text-blue-800"
                          : "bg-green-100 text-green-800"
                      }
                    >
                      {selectedRole.isSystemRole
                        ? "System Role"
                        : "Custom Role"}
                    </Badge>
                  </div>
                </div>

                {/* Permissions Table */}
                {!selectedRole.isSystemRole && (
                  <div>
                    <div className="border-b border-gray-200 dark:border-gray-700 pb-2 mb-4">
                      <Text
                        size="large"
                        weight="plus"
                        className="text-gray-900 dark:text-gray-100"
                      >
                        Permissions ({selectedRole.permissions.length} total)
                      </Text>
                      <Text className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                        Table view showing granted permissions by module and action type
                      </Text>
                    </div>

                    {/* Permission Matrix Table */}
                    <div className="border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden">
                      <Table>
                        <Table.Header>
                          <Table.Row className="bg-gray-50 dark:bg-gray-800">
                            <Table.HeaderCell className="font-semibold text-left w-1/4">
                              Module
                            </Table.HeaderCell>
                            <Table.HeaderCell className="font-semibold text-center w-15">
                              VIEW
                            </Table.HeaderCell>
                            <Table.HeaderCell className="font-semibold text-center w-15">
                              CREATE
                            </Table.HeaderCell>
                            <Table.HeaderCell className="font-semibold text-center w-15">
                              EDIT
                            </Table.HeaderCell>
                            <Table.HeaderCell className="font-semibold text-center w-15">
                              DELETE
                            </Table.HeaderCell>
                            <Table.HeaderCell className="font-semibold text-center w-15">
                              BULK OPS
                            </Table.HeaderCell>
                          </Table.Row>
                        </Table.Header>
                        <Table.Body>
                          {PERMISSION_GROUPS.map((group) => {
                            // Check if this group has any permissions granted
                            const hasAnyPermission = group.permissions.some((p) =>
                              selectedRole.permissions.includes(p)
                            );

                            if (!hasAnyPermission) return null;

                            // Define permission types for this group
                            const permissionTypes = ['view', 'create', 'edit', 'delete', 'bulk_operations'];

                            return (
                              <Table.Row key={group.id} className="hover:bg-gray-50 dark:hover:bg-gray-800">
                                <Table.Cell className="font-medium">
                                  <div>
                                    <Text className="font-medium text-gray-900 dark:text-gray-100">
                                      {group.name}
                                    </Text>
                                    <Text className="text-sm text-gray-600 dark:text-gray-400">
                                      {group.description}
                                    </Text>
                                  </div>
                                </Table.Cell>
                                {permissionTypes.map((type) => {
                                  // Find matching permission for this group and type
                                  const permission = group.permissions.find((p) => {
                                    const permissionType = p.split(':')[1];
                                    return permissionType === type;
                                  });

                                  const isGranted = permission && selectedRole.permissions.includes(permission);

                                  return (
                                    <Table.Cell key={type} className="text-center">
                                      {isGranted ? (
                                        <Badge className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                                          <CheckCircle size={12} className="mr-1" />
                                          ✓
                                        </Badge>
                                      ) : (
                                        <Badge className="bg-gray-100 text-gray-600 dark:bg-gray-800 dark:text-gray-400">
                                          <XCircle size={12} className="mr-1" />
                                          ✗
                                        </Badge>
                                      )}
                                    </Table.Cell>
                                  );
                                })}
                              </Table.Row>
                            );
                          })}
                        </Table.Body>
                      </Table>
                    </div>

                    {/* Detailed Permissions List */}
                    <div className="mt-6">
                      <Text className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
                        Detailed Permission List
                      </Text>
                      <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-4 max-h-60 overflow-y-auto">
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                          {selectedRole.permissions.map((permission: ScreenPermission) => {
                            return (
                              <div key={permission} className="flex items-center gap-2 text-sm">
                                <CheckCircle size={14} className="text-green-600 flex-shrink-0" />
                                <span className="font-mono text-gray-700 dark:text-gray-300">
                                  {permission}
                                </span>
                              </div>
                            );
                          })}
                        </div>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </FocusModal.Body>
            <FocusModal.Footer>
              <div className="flex justify-end gap-3 w-full">
                <Button
                  variant="secondary"
                  onClick={() => setShowRoleViewModal(false)}
                >
                  Close
                </Button>
                {!selectedRole.isSystemRole &&
                  hasPermission("role_management:edit") && (
                    <Button
                      variant="primary"
                      onClick={() => {
                        setShowRoleViewModal(false);
                        handleEditRole(selectedRole);
                      }}
                    >
                      Edit Role
                    </Button>
                  )}
              </div>
            </FocusModal.Footer>
          </FocusModal.Content>
        </FocusModal>
      )}

      {/* Edit Role Drawer */}
      {showRoleEditModal && editingRole && (
        <FocusModal
          open={showRoleEditModal}
          onOpenChange={() => setShowRoleEditModal(false)}
        >
          <FocusModal.Content>
            <FocusModal.Header>
              <FocusModal.Title>
                <Text size="large" weight="plus">
                  Edit Role: {editingRole.name}
                </Text>
              </FocusModal.Title>
            </FocusModal.Header>
            <FocusModal.Body className="overflow-auto p-6">
              <div className="space-y-6">
                {/* Basic Information */}
                <div className="space-y-4">
                  <div>
                    <Text className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Role Name
                    </Text>
                    <Input
                      type="text"
                      placeholder="e.g. Hotel Manager"
                      value={roleName}
                      onChange={(e) => {
                        setRoleName(e.target.value);
                        if (error && error.includes("already exists")) {
                          setError(null);
                        }
                      }}
                      disabled={roleLoading}
                      className="w-full"
                    />
                    {error && error.includes("already exists") && (
                      <Text className="text-red-600 text-sm mt-1">{error}</Text>
                    )}
                  </div>

                  <div>
                    <Text className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Description (Optional)
                    </Text>
                    <Input
                      type="text"
                      placeholder="Brief description of the role"
                      value={roleDescription}
                      onChange={(e) => setRoleDescription(e.target.value)}
                      disabled={roleLoading}
                      className="w-full"
                    />
                  </div>
                </div>

                {/* Permissions Configuration */}
                <PermissionTable
                  selectedPermissions={rolePermissions}
                  onPermissionToggle={handlePermissionToggle}
                  disabled={roleLoading}
                  loading={roleLoading}
                />
              </div>
            </FocusModal.Body>
            <FocusModal.Footer>
              <div className="flex justify-end gap-3 w-full">
                <Button
                  variant="secondary"
                  onClick={() => {
                    setShowRoleEditModal(false);
                    setEditingRole(null);
                    setRoleName("");
                    setRoleDescription("");
                    setRolePermissions([]);
                    setError(null);
                  }}
                  disabled={roleLoading}
                >
                  Cancel
                </Button>
                <Button
                  variant="primary"
                  onClick={handleUpdateRole}
                  disabled={
                    !roleName.trim() ||
                    roleLoading ||
                    rolePermissions.length === 0
                  }
                >
                  {roleLoading
                    ? "Updating..."
                    : `Update Role (${rolePermissions.length} permissions)`}
                </Button>
              </div>
            </FocusModal.Footer>
          </FocusModal.Content>
        </FocusModal>
      )}

      {/* Deactivation Confirmation Modal */}
      {showDeactivateModal && userToDeactivate && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[1000]"
          onClick={closeDeactivateModal}
        >
          <Container
            className="p-6 max-w-md bg-white dark:bg-gray-800 rounded-lg"
            onClick={(e) => e.stopPropagation()}
          >
            <div className="flex flex-col gap-4">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-red-100 dark:bg-red-900/30 rounded-full flex items-center justify-center">
                  <XCircle
                    size={20}
                    className="text-red-600 dark:text-red-400"
                  />
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                    Deactivate User
                  </h3>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    {getUserDisplayName(userToDeactivate)}
                  </p>
                </div>
              </div>

              <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md p-4">
                <h4 className="font-medium text-red-800 dark:text-red-300 mb-2">
                  ⚠️ Important Warning
                </h4>
                <div className="text-sm text-red-700 dark:text-red-300 space-y-2">
                  <p>This user will:</p>
                  <ul className="list-disc list-inside space-y-1 ml-2">
                    <li>No longer be able to access this site</li>
                    <li>Have their role assignment completely removed</li>
                    <li>Have all permissions permanently revoked</li>
                    <li>Lose access to all system features</li>
                    <li>Keep their activity history (data preserved)</li>
                  </ul>
                  <p className="font-medium mt-3">
                    This action can be reversed by reactivating the user, but
                    they will need to be assigned a new role.
                  </p>
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium mb-2 text-gray-900 dark:text-gray-100">
                  Type{" "}
                  <span className="font-bold text-red-600 dark:text-red-400">
                    CONFIRM
                  </span>{" "}
                  to proceed:
                </label>
                <Input
                  type="text"
                  placeholder="Type CONFIRM"
                  value={confirmationText}
                  onChange={(e) => setConfirmationText(e.target.value)}
                  onKeyDown={(e) => {
                    if (
                      e.key === "Enter" &&
                      confirmationText.trim().toUpperCase() === "CONFIRM" &&
                      !isDeactivating
                    ) {
                      confirmDeactivation();
                    } else if (e.key === "Escape") {
                      closeDeactivateModal();
                    }
                  }}
                  disabled={isDeactivating}
                  autoFocus
                  className="font-mono"
                />
              </div>

              <div className="flex gap-3 pt-2">
                <Button
                  variant="secondary"
                  onClick={closeDeactivateModal}
                  disabled={isDeactivating}
                  className="flex-1"
                >
                  Cancel
                </Button>
                <Button
                  variant="danger"
                  onClick={confirmDeactivation}
                  disabled={
                    confirmationText.trim().toUpperCase() !== "CONFIRM" ||
                    isDeactivating
                  }
                  className="flex-1"
                >
                  {isDeactivating ? "Deactivating..." : "Deactivate User"}
                </Button>
              </div>
            </div>
          </Container>
        </div>
      )}
    </>
  );
}