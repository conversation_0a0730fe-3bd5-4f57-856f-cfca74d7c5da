import {
  Con<PERSON>er,
  Button,
  Input,
  Text,
  Heading,
  Alert,
  Toaster,
  toast,
  InlineTip,
} from "@camped-ai/ui";
import { <PERSON>, AlertTriangle, CheckCircle, Eye, EyeOff } from "lucide-react";
import { sdk } from "../../../lib/sdk";
import { useState } from "react";
import { defineRouteConfig } from "@camped-ai/admin-sdk";
import { useCurrentUser } from "../../../hooks/use-rbac";
import { useLocation } from "react-router-dom";
import PermissionBasedSidebarHider from "../../../widgets/permission-based-sidebar-hider";

interface ChangePasswordForm {
  current_password: string;
  new_password: string;
  confirm_password: string;
}

export default function ChangePasswordPage() {
  const { data: currentUser, isLoading } = useCurrentUser();
  const location = useLocation();
  const [form, setForm] = useState<ChangePasswordForm>({
    current_password: "",
    new_password: "",
    confirm_password: "",
  });
  const [showPasswords, setShowPasswords] = useState({
    current: false,
    new: false,
    confirm: false,
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [errors, setErrors] = useState<Partial<ChangePasswordForm>>({});

  // Check if user has temporary password
  const hasTempPassword = (currentUser as any)?.metadata?.rbac?.temp_password;
  const requiresPasswordReset = (currentUser as any)?.metadata?.rbac
    ?.requires_password_reset;

  // Check if user was redirected here
  const wasRedirected = location.state?.from && location.state?.reason;

  const validateForm = (): boolean => {
    const newErrors: Partial<ChangePasswordForm> = {};

    if (!form.current_password) {
      newErrors.current_password = "Current password is required";
    }

    if (!form.new_password) {
      newErrors.new_password = "New password is required";
    } else if (form.new_password.length < 8) {
      newErrors.new_password = "Password must be at least 8 characters long";
    }

    if (!form.confirm_password) {
      newErrors.confirm_password = "Please confirm your password";
    } else if (form.new_password !== form.confirm_password) {
      newErrors.confirm_password = "Passwords don't match";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);

    try {
      await sdk.client.fetch("/admin/auth/confirm-password", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: form,
      });

      // If we get here without an exception, the request was successful
      toast.success("Password updated successfully!", {
        description: "You can now use your new password to log in.",
      });

      // Reset form
      setForm({
        current_password: "",
        new_password: "",
        confirm_password: "",
      });

      // Refresh user data to update temp_password status
      window.location.reload();
    } catch (error) {
      // Try to extract error message from the error object
      let errorMessage = "An error occurred while updating your password";

      if (error instanceof Error) {
        errorMessage = error.message;
      } else if (
        typeof error === "object" &&
        error !== null &&
        "message" in error
      ) {
        errorMessage = (error as any).message;
      }

      toast.error("Failed to update password", {
        description: errorMessage,
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleInputChange = (
    field: keyof ChangePasswordForm,
    value: string
  ) => {
    setForm((prev) => ({ ...prev, [field]: value }));
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors((prev) => ({ ...prev, [field]: undefined }));
    }
  };

  if (isLoading) {
    return (
      <Container className="py-8">
        <div className="animate-pulse">
          <div className="h-8 bg-muted rounded w-1/3 mb-4"></div>
          <div className="space-y-4">
            <div className="h-4 bg-muted rounded w-full"></div>
            <div className="h-10 bg-muted rounded w-full"></div>
          </div>
        </div>
      </Container>
    );
  }

  return (
    <>
      <PermissionBasedSidebarHider />

      <Toaster />
      <Container className="py-4">
        <div className="space-y-6">
          {/* Header */}
          <div className="space-y-1">
            <Heading level="h1">Change Password</Heading>
            <Text className="text-muted-foreground">
              Update your account password to keep your account secure.
            </Text>
          </div>

          {/* Redirected User Alert */}
          {wasRedirected && (
            <InlineTip
              label="Access Restricted"
              className="border-destructive/20 bg-destructive/10"
            >
              You must change your password before accessing other parts of the
              system.
            </InlineTip>
          )}

          {/* Temporary Password Alert */}
          {(hasTempPassword || requiresPasswordReset) && !wasRedirected && (
            <InlineTip
              label="Password Change Required"
              className="border-yellow-500/20 bg-yellow-500/10"
            >
              You're currently using a temporary password. Please set a new
              password to secure your account.
            </InlineTip>
          )}

          {/* Change Password Form */}
          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="bg-card border border-border rounded-lg p-6 space-y-4">
              {/* Current Password */}
              <div className="space-y-2">
                <label
                  htmlFor="current_password"
                  className="block text-sm font-medium text-foreground"
                >
                  Current Password
                </label>
                <div className="relative">
                  <Input
                    id="current_password"
                    type="password"
                    value={form.current_password}
                    onChange={(e) =>
                      handleInputChange("current_password", e.target.value)
                    }
                    placeholder="Enter your current password"
                    className={
                      errors.current_password ? "border-destructive" : ""
                    }
                  />
                </div>
                {errors.current_password && (
                  <Text className="text-sm text-destructive">
                    {errors.current_password}
                  </Text>
                )}
              </div>

              {/* New Password */}
              <div className="space-y-2">
                <label
                  htmlFor="new_password"
                  className="block text-sm font-medium text-foreground"
                >
                  New Password
                </label>
                <div className="relative">
                  <Input
                    id="new_password"
                    type="password"
                    value={form.new_password}
                    onChange={(e) =>
                      handleInputChange("new_password", e.target.value)
                    }
                    placeholder="Enter your new password"
                    className={errors.new_password ? "border-destructive" : ""}
                  />
                </div>
                {errors.new_password && (
                  <Text className="text-sm text-destructive">
                    {errors.new_password}
                  </Text>
                )}
                <Text className="text-xs text-muted-foreground">
                  Password must be at least 8 characters long
                </Text>
              </div>

              {/* Confirm Password */}
              <div className="space-y-2">
                <label
                  htmlFor="confirm_password"
                  className="block text-sm font-medium text-foreground"
                >
                  Confirm New Password
                </label>
                <div className="relative">
                  <Input
                    id="confirm_password"
                    type="password"
                    value={form.confirm_password}
                    onChange={(e) =>
                      handleInputChange("confirm_password", e.target.value)
                    }
                    placeholder="Confirm your new password"
                    className={
                      errors.confirm_password ? "border-destructive" : ""
                    }
                  />
                </div>
                {errors.confirm_password && (
                  <Text className="text-sm text-destructive">
                    {errors.confirm_password}
                  </Text>
                )}
              </div>
            </div>

            {/* Submit Button */}
            <div className="flex justify-end">
              <Button
                type="submit"
                disabled={isSubmitting}
                className="min-w-[120px]"
              >
                {isSubmitting ? (
                  <div className="flex items-center gap-2">
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary-foreground"></div>
                    Updating...
                  </div>
                ) : (
                  <div className="flex items-center gap-2">
                    <CheckCircle className="h-4 w-4" />
                    Update Password
                  </div>
                )}
              </Button>
            </div>
          </form>
        </div>
      </Container>
    </>
  );
}
