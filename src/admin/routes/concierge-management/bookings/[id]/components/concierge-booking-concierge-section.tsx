import { useState, useCallback, useEffect } from "react";
import { Container, Heading, Badge, Select, toast } from "@camped-ai/ui";
import { Clock, CheckCircle, AlertCircle, XCircle } from "lucide-react";

import { SectionRow } from "../../../../../../components/common/section/section-row";
import { useRbac } from "../../../../../hooks/use-rbac";

interface ConciergeBookingConciergeSection {
  booking: any;
  onStatusUpdate?: () => void;
}

// Status configuration with colors and icons
const STATUS_CONFIG = {
  not_started: {
    label: "Not Started",
    color: "grey" as const,
    icon: <Clock className="h-4 w-4" />,
    description: "Booking has not been started yet"
  },
  in_progress: {
    label: "In Progress",
    color: "blue" as const,
    icon: <AlertCircle className="h-4 w-4" />,
    description: "Concierge is actively working on this booking"
  },
  waiting_customer: {
    label: "Waiting Customer",
    color: "orange" as const,
    icon: <Clock className="h-4 w-4" />,
    description: "Waiting for customer response or confirmation"
  },
  ready_to_finalize: {
    label: "Ready to Finalize",
    color: "purple" as const,
    icon: <CheckCircle className="h-4 w-4" />,
    description: "Ready for final review and completion"
  },
  completed: {
    label: "Completed",
    color: "green" as const,
    icon: <CheckCircle className="h-4 w-4" />,
    description: "Booking has been completed successfully"
  },
  cancelled: {
    label: "Cancelled",
    color: "red" as const,
    icon: <XCircle className="h-4 w-4" />,
    description: "Booking has been cancelled"
  }
};

// User interface for API response
interface User {
  id: string;
  email: string;
  first_name?: string;
  last_name?: string;
}

const ConciergeBookingConciergeSection = ({ booking, onStatusUpdate }: ConciergeBookingConciergeSection) => {
  const { hasPermission } = useRbac();
  const [isUpdating, setIsUpdating] = useState(false);
  const [localStatus, setLocalStatus] = useState(booking?.status || "not_started");
  const [users, setUsers] = useState<User[]>([]);
  const [isLoadingUsers, setIsLoadingUsers] = useState(false);
  const [localAssignedTo, setLocalAssignedTo] = useState(() => {
    // Handle initial assignment - if it's a real user ID, keep it; otherwise use "unassigned"
    const assignedTo = booking?.assigned_to;
    if (!assignedTo) return "unassigned";
    return assignedTo;
  });
  const [localPriority, setLocalPriority] = useState(booking?.metadata?.priority_level || "not_set");

  const currentStatus = localStatus;
  const statusConfig = STATUS_CONFIG[currentStatus as keyof typeof STATUS_CONFIG] || STATUS_CONFIG.not_started;

  // Fetch users from API
  const fetchUsers = useCallback(async () => {
    setIsLoadingUsers(true);
    try {
      const response = await fetch('/admin/users?limit=50&fields=id,email,first_name,last_name');
      if (!response.ok) {
        throw new Error('Failed to fetch users');
      }
      const data = await response.json();
      setUsers(data.users || []);
    } catch (error) {
      console.error('Error fetching users:', error);
      toast.error("Error", {
        description: "Failed to load users",
      });
    } finally {
      setIsLoadingUsers(false);
    }
  }, []);

  // Fetch users on component mount
  useEffect(() => {
    fetchUsers();
  }, [fetchUsers]);

  // Sync local states with booking prop when it changes (only on initial load or external changes)
  useEffect(() => {
    if (booking?.status) {
      setLocalStatus(booking.status);
    }
    setLocalAssignedTo(booking?.assigned_to || "unassigned");
    setLocalPriority(booking?.metadata?.priority_level || "not_set");
  }, [booking?.status, booking?.assigned_to, booking?.metadata?.priority_level]);

  // Debug: Log when localStatus changes
  useEffect(() => {
    console.log("Local status changed to:", localStatus);
  }, [localStatus]);

  // Handle status update
  const handleStatusUpdate = useCallback(async (newStatus: string) => {
    if (!booking?.id || newStatus === currentStatus) return;

    console.log("Updating status from", currentStatus, "to", newStatus);
    setIsUpdating(true);

    try {
      const response = await fetch(`/admin/concierge-management/orders/${booking.id}`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          status: newStatus,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || "Failed to update status");
      }

      console.log("API call successful, updating local status to:", newStatus);
      // Update local status immediately without refreshing the screen
      setLocalStatus(newStatus);
      console.log("Local status updated, new value should be:", newStatus);

      toast.success("Success", {
        description: `Status updated to ${STATUS_CONFIG[newStatus as keyof typeof STATUS_CONFIG]?.label || newStatus}`,
      });

    } catch (error: any) {
      console.error("Error updating status:", error);
      toast.error("Error", {
        description: error.message || "Failed to update status",
      });
      // Don't update local status on error - keep the previous value
    } finally {
      setIsUpdating(false);
    }
  }, [booking?.id, currentStatus]);

  // Handle priority update
  const handlePriorityUpdate = useCallback(async (newPriority: string) => {
    if (!booking?.id || newPriority === localPriority) return;

    console.log("Updating priority from", localPriority, "to", newPriority);
    setIsUpdating(true);

    try {
      const response = await fetch(`/admin/concierge-management/orders/${booking.id}`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          metadata: {
            ...booking.metadata,
            priority_level: newPriority === "not_set" ? null : newPriority,
          },
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || "Failed to update priority");
      }

      console.log("Priority API call successful, updating local priority to:", newPriority);
      setLocalPriority(newPriority);

      toast.success("Success", {
        description: `Priority updated to ${newPriority.toUpperCase()}`,
      });

    } catch (error: any) {
      console.error("Error updating priority:", error);
      toast.error("Error", {
        description: error.message || "Failed to update priority",
      });
    } finally {
      setIsUpdating(false);
    }
  }, [booking?.id, localPriority]);

  // Handle assignment update
  const handleAssignmentUpdate = useCallback(async (newAssignee: string) => {
    if (!booking?.id || newAssignee === localAssignedTo) return;

    console.log("Updating assignment from", localAssignedTo, "to", newAssignee);
    setIsUpdating(true);

    try {
      const response = await fetch(`/admin/concierge-management/orders/${booking.id}`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          assigned_to: newAssignee === "unassigned" ? null : newAssignee,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || "Failed to update assignment");
      }

      console.log("Assignment API call successful, updating local assignment to:", newAssignee);
      setLocalAssignedTo(newAssignee);

      toast.success("Success", {
        description: `Assignment updated to ${newAssignee}`,
      });

    } catch (error: any) {
      console.error("Error updating assignment:", error);
      toast.error("Error", {
        description: error.message || "Failed to update assignment",
      });
    } finally {
      setIsUpdating(false);
    }
  }, [booking?.id, localAssignedTo]);



  // Format last updated time
  const formatLastUpdated = useCallback(() => {
    if (!booking?.updated_at) return "Unknown";

    try {
      const date = new Date(booking.updated_at);
      return date.toLocaleDateString("en-US", {
        year: "numeric",
        month: "short",
        day: "numeric",
        hour: "2-digit",
        minute: "2-digit",
      });
    } catch {
      return "Unknown";
    }
  }, [booking?.updated_at]);

  // Get status options for dropdown
  const getStatusOptions = () => {
    return Object.entries(STATUS_CONFIG).map(([value, config]) => ({
      value,
      label: config.label,
    }));
  };

  // Get status trigger classes for colored background
  const getStatusTriggerClasses = (color: string) => {
    const colorClasses = {
      grey: "bg-ui-bg-subtle border-ui-border-base text-ui-fg-subtle",
      blue: "bg-blue-50 border-blue-200 text-blue-700",
      orange: "bg-orange-50 border-orange-200 text-orange-700",
      purple: "bg-purple-50 border-purple-200 text-purple-700",
      green: "bg-green-50 border-green-200 text-green-700",
      red: "bg-red-50 border-red-200 text-red-700"
    };
    return colorClasses[color as keyof typeof colorClasses] || colorClasses.grey;
  };

  // Get assignment options from real users
  const getAssignmentOptions = () => {
    const options = [
      { value: "unassigned", label: "Unassigned" }
    ];

    // Add real users
    users.forEach(user => {
      const displayName = user.first_name && user.last_name
        ? `${user.first_name} ${user.last_name}`
        : user.email;
      options.push({
        value: user.id,
        label: displayName
      });
    });

    return options;
  };

  // Get user name from user ID
  const getUserName = (userId: string) => {
    if (!userId || userId === "unassigned") return "Unassigned";

    // Find user in the fetched users list
    const user = users.find(u => u.id === userId);
    if (user) {
      return user.first_name && user.last_name
        ? `${user.first_name} ${user.last_name}`
        : user.email;
    }

    // Fallback to userId if not found
    return userId;
  };

  // Format assigned concierge with dropdown
  const formatAssignedConcierge = () => {
    const displayName = getUserName(localAssignedTo);

    if (hasPermission("concierge_management:edit")) {
      return (
        <Select
          key={localAssignedTo}
          value={localAssignedTo}
          onValueChange={handleAssignmentUpdate}
          disabled={isUpdating || isLoadingUsers}
        >
          <Select.Trigger className="w-48">
            <Select.Value placeholder={isLoadingUsers ? "Loading users..." : "Select assignee"}>
              {isLoadingUsers ? "Loading..." : displayName}
            </Select.Value>
          </Select.Trigger>
          <Select.Content>
            {getAssignmentOptions().map((option) => (
              <Select.Item key={option.value} value={option.value}>
                {option.label}
              </Select.Item>
            ))}
          </Select.Content>
        </Select>
      );
    }
    return displayName;
  };

  // Get priority options
  const getPriorityOptions = () => {
    return [
      { value: "not_set", label: "Not Set" },
      { value: "low", label: "Low" },
      { value: "medium", label: "Medium" },
      { value: "high", label: "High" },
      { value: "urgent", label: "Urgent" },
    ];
  };

  // Get priority trigger classes for colored background
  const getPriorityTriggerClasses = (priority: string) => {
    const colorClasses = {
      not_set: "bg-ui-bg-subtle border-ui-border-base text-ui-fg-subtle",
      low: "bg-gray-50 border-gray-200 text-gray-700",
      medium: "bg-blue-50 border-blue-200 text-blue-700",
      high: "bg-orange-50 border-orange-200 text-orange-700",
      urgent: "bg-red-50 border-red-200 text-red-700"
    };
    return colorClasses[priority as keyof typeof colorClasses] || colorClasses.not_set;
  };

  // Format priority level with dropdown
  const formatPriorityLevel = () => {
    const priorityColors = {
      not_set: "grey" as const,
      low: "grey" as const,
      medium: "blue" as const,
      high: "orange" as const,
      urgent: "red" as const
    };

    if (hasPermission("concierge_management:edit")) {
      return (
        <Select
          key={localPriority}
          value={localPriority}
          onValueChange={handlePriorityUpdate}
          disabled={isUpdating}
        >
          <Select.Trigger className={`w-48 border-2 ${getPriorityTriggerClasses(localPriority || "not_set")}`}>
            <Select.Value placeholder="Set priority">
              <span className="font-medium">
                {localPriority && localPriority !== "not_set" ? localPriority.toUpperCase() : "NOT SET"}
              </span>
            </Select.Value>
          </Select.Trigger>
          <Select.Content>
            {getPriorityOptions().map((option) => (
              <Select.Item
                key={option.value}
                value={option.value}
                className={`${getPriorityTriggerClasses(option.value)} hover:opacity-80`}
              >
                <span className="font-medium">
                  {option.label.toUpperCase()}
                </span>
              </Select.Item>
            ))}
          </Select.Content>
        </Select>
      );
    }

    if (!localPriority || localPriority === "not_set") return "Not set";

    return (
      <Badge color={priorityColors[localPriority as keyof typeof priorityColors] || "grey"}>
        {localPriority.toUpperCase()}
      </Badge>
    );
  };

  return (
    <Container className="divide-y p-0">
      <div className="flex items-center justify-between px-6 py-4">
        <Heading>Concierge Management</Heading>

      </div>

      <SectionRow
        title="Status"
        value={
          hasPermission("concierge_management:edit") ? (
            <Select
              key={currentStatus}
              value={currentStatus}
              onValueChange={handleStatusUpdate}
              disabled={isUpdating}
            >
              <Select.Trigger className={`w-48 border-2 ${getStatusTriggerClasses(statusConfig.color)}`}>
                <Select.Value>
                  <div className="flex items-center gap-2">
                    {statusConfig.icon}
                    <span className="font-medium">{statusConfig.label}</span>
                  </div>
                </Select.Value>
              </Select.Trigger>
              <Select.Content>
                {getStatusOptions().map((option) => {
                  const optionConfig = STATUS_CONFIG[option.value as keyof typeof STATUS_CONFIG];
                  return (
                    <Select.Item
                      key={option.value}
                      value={option.value}
                      className={`${getStatusTriggerClasses(optionConfig?.color || 'grey')} hover:opacity-80`}
                    >
                      <div className="flex items-center gap-2 w-full">
                        {optionConfig?.icon}
                        <span className="font-medium">{option.label}</span>
                      </div>
                    </Select.Item>
                  );
                })}
              </Select.Content>
            </Select>
          ) : (
            <div className={`flex items-center gap-2 px-3 py-2 rounded-md border-2 w-48 ${getStatusTriggerClasses(statusConfig.color)}`}>
              {statusConfig.icon}
              <span className="font-medium">{statusConfig.label}</span>
            </div>
          )
        }
      />

      <SectionRow
        title="Assigned To"
        value={formatAssignedConcierge()}
      />

      <SectionRow
        title="Priority Level"
        value={formatPriorityLevel()}
      />

      <SectionRow
        title="Last Updated"
        value={formatLastUpdated()}
      />

      {booking?.last_contacted_at && (
        <SectionRow
          title="Last Contacted"
          value={
            new Date(booking.last_contacted_at).toLocaleDateString("en-US", {
              year: "numeric",
              month: "short",
              day: "numeric",
              hour: "2-digit",
              minute: "2-digit",
            })
          }
        />
      )}
    </Container>
  );
};

export default ConciergeBookingConciergeSection;
