import React, { useState } from "react";
import {
  Container,
  <PERSON><PERSON>,
  Button,
  Input,
  Textarea,
  Text,
  toast,
  usePrompt,
} from "@camped-ai/ui";
import { Trash, User, Calendar, Plus, Edit } from "lucide-react";
import { useTranslation } from "react-i18next";
import { format } from "date-fns";
import { SectionRow } from "../../../../../../components/common/section/section-row";
import { ActionMenu } from "../../../../../components/ActionMenu";
import {
  useEntityNotes,
  useCreateNote,
  useUpdateNote,
  useDeleteNote,
  Note,
} from "../../../../../hooks/api/notes";
import { useRbac } from "../../../../../hooks/use-rbac";

interface NotesSectionProps {
  entity: string;
  entityId: string;
}

const NotesSection = ({ entity, entityId }: NotesSectionProps) => {
  const { t } = useTranslation();
  const prompt = usePrompt();
  const { hasPermission } = useRbac();

  // UI state
  const [showAddForm, setShowAddForm] = useState(false);
  const [editingNoteId, setEditingNoteId] = useState<string | null>(null);

  // Add form state
  const [title, setTitle] = useState("");
  const [content, setContent] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Edit form state
  const [editTitle, setEditTitle] = useState("");
  const [editContent, setEditContent] = useState("");
  const [isUpdating, setIsUpdating] = useState(false);

  // API hooks
  const { data: notesData, isLoading, error } = useEntityNotes(entity, entityId, {
    order: "created_at",
    sort_order: "desc",
  });
  const createNoteMutation = useCreateNote();
  const updateNoteMutation = useUpdateNote();
  const deleteNoteMutation = useDeleteNote();

  const notes = notesData?.notes || [];

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!title.trim()) {
      toast.error("Title is required");
      return;
    }

    setIsSubmitting(true);

    try {
      await createNoteMutation.mutateAsync({
        title: title.trim(),
        content: content.trim() || undefined,
        entity,
        entity_id: entityId,
      });

      // Clear form and hide it
      setTitle("");
      setContent("");
      setShowAddForm(false);
    } catch (error) {
      // Error is handled by the mutation hook
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleCancelAdd = () => {
    setTitle("");
    setContent("");
    setShowAddForm(false);
  };

  const handleEditNote = (note: Note) => {
    setEditingNoteId(note.id);
    setEditTitle(note.title);
    setEditContent(note.content || "");
  };

  const handleUpdateNote = async (noteId: string) => {
    if (!editTitle.trim()) {
      toast.error("Title is required");
      return;
    }

    setIsUpdating(true);

    try {
      await updateNoteMutation.mutateAsync({
        noteId,
        data: {
          title: editTitle.trim(),
          content: editContent.trim() || undefined,
        },
      });

      // Exit edit mode
      setEditingNoteId(null);
      setEditTitle("");
      setEditContent("");
    } catch (error) {
      // Error is handled by the mutation hook
    } finally {
      setIsUpdating(false);
    }
  };

  const handleCancelEdit = () => {
    setEditingNoteId(null);
    setEditTitle("");
    setEditContent("");
  };

  const handleDeleteNote = async (note: Note) => {
    const confirmed = await prompt({
      title: "Delete Note",
      description: `Are you sure you want to delete the note "${note.title}"? This action cannot be undone.`,
    });

    if (confirmed) {
      try {
        await deleteNoteMutation.mutateAsync(note.id);
      } catch (error) {
        // Error is handled by the mutation hook
      }
    }
  };

  const formatDate = (dateString: string) => {
    try {
      return format(new Date(dateString), "MMM dd, yyyy 'at' HH:mm");
    } catch {
      return dateString;
    }
  };

  if (error) {
    return (
      <Container className="p-6">
        <Text className="text-red-600">
          Error loading notes: {error.message}
        </Text>
      </Container>
    );
  }

  return (
    <Container className="divide-y p-0">
      {/* Header with Add Note Button */}
      <div className="flex items-center justify-between px-6 py-4">
        <div className="flex items-center gap-4">
          <Heading level="h2">Notes</Heading>
          <Text size="small" className="text-muted-foreground">
            {notes.length} {notes.length === 1 ? "note" : "notes"}
          </Text>
        </div>
        {hasPermission("concierge_management:create") && (
          <Button
            variant="secondary"
            size="small"
            onClick={() => setShowAddForm(!showAddForm)}
            className="flex items-center gap-2"
          >
            <Plus className="h-4 w-4" />
            Add Note
          </Button>
        )}
      </div>

      {/* Conditional Add Note Form */}
      {showAddForm && (
        <div className="px-6 py-4 bg-gray-50">
          <form onSubmit={handleSubmit} className="space-y-4">
            <div>
              <Input
                placeholder="Note title (required)"
                value={title}
                onChange={(e) => setTitle(e.target.value)}
                disabled={isSubmitting}
                required
                autoFocus
              />
            </div>
            <div>
              <Textarea
                placeholder="Note content (optional)"
                value={content}
                onChange={(e) => setContent(e.target.value)}
                disabled={isSubmitting}
                rows={3}
              />
            </div>
            <div className="flex gap-2">
              <Button
                type="submit"
                disabled={!title.trim() || isSubmitting}
                loading={isSubmitting}
              >
                Save Note
              </Button>
              <Button
                type="button"
                variant="secondary"
                onClick={handleCancelAdd}
                disabled={isSubmitting}
              >
                Cancel
              </Button>
            </div>
          </form>
        </div>
      )}

      {/* Notes List */}
      {isLoading ? (
        <div className="px-6 py-8 text-center">
          <Text className="text-muted-foreground">Loading notes...</Text>
        </div>
      ) : notes.length === 0 ? (
        <div className="px-6 py-8 text-center">
          <Text className="text-muted-foreground">
            No notes yet. Click "Add Note" to create your first note.
          </Text>
        </div>
      ) : (
        <div className="divide-y">
          {notes.map((note) => (
            <div key={note.id} className="px-6 py-4">
              {editingNoteId === note.id ? (
                // Edit Mode
                <div className="space-y-4">
                  <div>
                    <Input
                      value={editTitle}
                      onChange={(e) => setEditTitle(e.target.value)}
                      disabled={isUpdating}
                      placeholder="Note title (required)"
                      autoFocus
                    />
                  </div>
                  <div>
                    <Textarea
                      value={editContent}
                      onChange={(e) => setEditContent(e.target.value)}
                      disabled={isUpdating}
                      placeholder="Note content (optional)"
                      rows={3}
                    />
                  </div>
                  <div className="flex gap-2">
                    <Button
                      size="small"
                      onClick={() => handleUpdateNote(note.id)}
                      disabled={!editTitle.trim() || isUpdating}
                      loading={isUpdating}
                    >
                      Save
                    </Button>
                    <Button
                      size="small"
                      variant="secondary"
                      onClick={handleCancelEdit}
                      disabled={isUpdating}
                    >
                      Cancel
                    </Button>
                  </div>
                </div>
              ) : (
                // Read Mode
                <div className="flex items-start justify-between gap-4">
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center gap-2 mb-2">
                      <Text weight="plus" className="truncate">
                        {note.title}
                      </Text>
                    </div>

                    {note.content && (
                      <Text size="small" className="text-muted-foreground mb-3 whitespace-pre-wrap">
                        {note.content}
                      </Text>
                    )}

                    <div className="flex items-center gap-4 text-xs text-muted-foreground">
                      <div className="flex items-center gap-1">
                        <Calendar className="h-3 w-3" />
                        <span>{formatDate(note.created_at)}</span>
                      </div>
                      {note.created_by_id && (
                        <div className="flex items-center gap-1">
                          <User className="h-3 w-3" />
                          <span>by {note.created_by_id}</span>
                        </div>
                      )}
                    </div>
                  </div>

                  <ActionMenu
                    groups={[
                      {
                        actions: [
                          ...(hasPermission("concierge_management:edit") ? [{
                            icon: <Edit className="h-4 w-4" />,
                            label: "Edit",
                            onClick: () => handleEditNote(note),
                          }] : []),
                          ...(hasPermission("concierge_management:delete") ? [{
                            icon: <Trash className="h-4 w-4" />,
                            label: "Delete",
                            onClick: () => handleDeleteNote(note),
                          }] : []),
                        ],
                      },
                    ]}
                  />
                </div>
              )}
            </div>
          ))}
        </div>
      )}
    </Container>
  );
};

export default NotesSection;
