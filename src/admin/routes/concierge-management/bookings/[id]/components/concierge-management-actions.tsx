import { useState } from "react";
import {
  Container,
  Heading,
  Text,
  Button,
  Select,
  Textarea,
  Badge,
  toast,
  Label,
} from "@camped-ai/ui";
import { User, AlertTriangle, Clock, Save, UserCheck } from "lucide-react";
import { SectionRow } from "../../../../../../components/common/section/section-row";
import { EnhancedConciergeOrder } from "../types";
import UserSelector from "../../../../../components/concierge/user-selector";

interface ConciergeManagementActionsProps {
  booking: EnhancedConciergeOrder | null;
  onUpdate?: () => void;
  onAssignedToChange?: (assignedTo: string) => void;
}

const ConciergeManagementActions = ({
  booking,
  onUpdate,
  onAssignedToChange,
}: ConciergeManagementActionsProps) => {
  if (!booking) {
    return (
      <Container className="divide-y p-0">
        <div className="flex items-center justify-between px-6 py-4">
          <Heading level="h3">Concierge Management</Heading>
        </div>
        <div className="px-6 py-4">
          <Text className="text-ui-fg-muted">
            Loading management options...
          </Text>
        </div>
      </Container>
    );
  }
  const [isUpdating, setIsUpdating] = useState(false);
  const [status, setStatus] = useState(booking.status || "not_started");
  const [assignedTo, setAssignedTo] = useState(
    booking.assigned_to || "unassigned"
  );
  const [priorityLevel, setPriorityLevel] = useState("medium");
  const [internalNotes, setInternalNotes] = useState(booking.notes || "");

  // Status options
  const statusOptions = [
    { label: "Not Started", value: "not_started" },
    { label: "In Progress", value: "in_progress" },
    { label: "Waiting Customer", value: "waiting_customer" },
    { label: "Ready to Finalize", value: "ready_to_finalize" },
    { label: "Completed", value: "completed" },
  ];

  // Priority options
  const priorityOptions = [
    { label: "Low", value: "low" },
    { label: "Medium", value: "medium" },
    { label: "High", value: "high" },
    { label: "Urgent", value: "urgent" },
  ];

  // Mock staff options (in real implementation, fetch from API)
  const staffOptions = [
    { label: "Unassigned", value: "unassigned" },
    { label: "Sarah Johnson", value: "sarah.johnson" },
    { label: "Michael Chen", value: "michael.chen" },
    { label: "Emma Rodriguez", value: "emma.rodriguez" },
    { label: "David Kim", value: "david.kim" },
  ];

  // Get status color
  const getStatusColor = (statusValue: string) => {
    switch (statusValue) {
      case "not_started":
        return "grey";
      case "in_progress":
        return "blue";
      case "waiting_customer":
        return "orange";
      case "ready_to_finalize":
        return "purple";
      case "completed":
        return "green";
      default:
        return "grey";
    }
  };

  // Get priority color
  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case "low":
        return "green";
      case "medium":
        return "orange";
      case "high":
        return "red";
      case "urgent":
        return "red";
      default:
        return "grey";
    }
  };

  // Handle update
  const handleUpdate = async () => {
    setIsUpdating(true);

    try {
      const response = await fetch(
        `/admin/concierge-management/orders/${booking.id}`,
        {
          method: "PATCH",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            status,
            assigned_to: assignedTo === "unassigned" ? null : assignedTo,
            priority_level: priorityLevel,
            notes: internalNotes,
          }),
        }
      );

      if (!response.ok) {
        throw new Error(
          `Failed to update: ${response.status} ${response.statusText}`
        );
      }

      toast.success("Success", {
        description: "Concierge order updated successfully",
      });

      if (onUpdate) {
        onUpdate();
      }
    } catch (error: any) {
      console.error("Error updating concierge order:", error);
      toast.error("Error", {
        description: `Failed to update concierge order: ${error.message}`,
      });
    } finally {
      setIsUpdating(false);
    }
  };

  // Check if there are changes
  const hasChanges =
    status !== booking.status ||
    assignedTo !== (booking.assigned_to || "unassigned") ||
    internalNotes !== (booking.notes || "");

  return (
    <Container className="divide-y p-0">
      <div className="flex items-center justify-between px-6 py-4">
        <Heading level="h3">Concierge Management</Heading>
        <Badge variant={getStatusColor(status) as any}>
          {statusOptions.find((opt) => opt.value === status)?.label || status}
        </Badge>
      </div>

      <div className="px-6 py-4 space-y-4">
        {/* Status Management */}
        <div>
          <Text className="text-sm font-medium mb-2">Status</Text>
          <Select value={status} onValueChange={setStatus}>
            <Select.Trigger>
              <Select.Value />
            </Select.Trigger>
            <Select.Content>
              {statusOptions.map((option) => (
                <Select.Item key={option.value} value={option.value}>
                  {option.label}
                </Select.Item>
              ))}
            </Select.Content>
          </Select>
        </div>

        {/* Assignment Controls */}
        <div>
          <Text className="text-sm font-medium mb-2">Assigned To</Text>
          <UserSelector
            value={assignedTo === "unassigned" ? "" : assignedTo}
            onChange={(val) => {
              const newValue = val || "unassigned";
              setAssignedTo(newValue);
              // Notify parent component of the change
              if (onAssignedToChange) {
                onAssignedToChange(val || "");
              }
            }}
            placeholder="Select a staff member"
          />
        </div>

        {/* Priority Settings */}
        <div>
          <Text className="text-sm font-medium mb-2">Priority Level</Text>
          <Select value={priorityLevel} onValueChange={setPriorityLevel}>
            <Select.Trigger>
              <Select.Value />
            </Select.Trigger>
            <Select.Content>
              {priorityOptions.map((option) => (
                <Select.Item key={option.value} value={option.value}>
                  <div className="flex items-center gap-2">
                    <AlertTriangle
                      className={`h-3 w-3 ${
                        option.value === "urgent" || option.value === "high"
                          ? "text-red-500"
                          : option.value === "medium"
                          ? "text-orange-500"
                          : "text-green-500"
                      }`}
                    />
                    {option.label}
                  </div>
                </Select.Item>
              ))}
            </Select.Content>
          </Select>
        </div>

        {/* Internal Notes */}
        <div>
          <Text className="text-sm font-medium mb-2">Internal Notes</Text>
          <Textarea
            value={internalNotes}
            onChange={(e) => setInternalNotes(e.target.value)}
            placeholder="Add internal notes for the concierge team..."
            rows={4}
          />
        </div>

        {/* Last Contact Information */}
        {booking.last_contacted_at && (
          <SectionRow title="Last Contacted" value="">
            <div className="flex items-center gap-2">
              <Clock className="h-4 w-4 text-ui-fg-muted" />
              <Text className="text-sm">
                {new Date(booking.last_contacted_at).toLocaleDateString(
                  "en-US",
                  {
                    month: "short",
                    day: "numeric",
                    year: "numeric",
                    hour: "2-digit",
                    minute: "2-digit",
                  }
                )}
              </Text>
            </div>
          </SectionRow>
        )}

        {/* Update Button */}
        <Button
          onClick={handleUpdate}
          disabled={!hasChanges || isUpdating}
          className="w-full"
          variant={hasChanges ? "primary" : "secondary"}
        >
          <Save className="h-4 w-4 mr-2" />
          {isUpdating
            ? "Updating..."
            : hasChanges
            ? "Save Changes"
            : "No Changes"}
        </Button>
      </div>
    </Container>
  );
};

export default ConciergeManagementActions;
