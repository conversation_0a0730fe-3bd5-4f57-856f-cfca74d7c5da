import { <PERSON><PERSON><PERSON> } from "@camped-ai/icons";
import { Container, Heading, Text, Badge } from "@camped-ai/ui";
import { useTranslation } from "react-i18next";
import { format } from "date-fns";

import { ActionMenu } from "../../../../../components/ActionMenu";
import { SectionRow } from "../../../../../../components/common/section/section-row";
import { useRbac } from "../../../../../hooks/use-rbac";

interface ConciergeBookingTasksSectionProps {
  booking: any;
}

const ConciergeBookingTasksSection = ({ booking }: ConciergeBookingTasksSectionProps) => {
  const { t } = useTranslation();
  const { hasPermission } = useRbac();

  const handleAddTask = () => {
    // TODO: Implement task creation
    console.log("Add task:", booking.id);
  };

  // Format date
  const formatDate = (dateString: string) => {
    try {
      return format(new Date(dateString), "dd MMM yyyy");
    } catch (error) {
      return "Invalid date";
    }
  };

  // Format tasks for display
  const formatTasks = () => {
    if (!booking.task_assignments || booking.task_assignments.length === 0) {
      return "No tasks assigned";
    }

    return (
      <div className="space-y-3">
        {booking.task_assignments.map((task: any, index: number) => (
          <div key={index} className="flex items-center justify-between p-3 bg-muted rounded-md">
            <div className="flex-1">
              <Text className="font-medium">{task.title || `Task ${index + 1}`}</Text>
              {task.description && (
                <Text className="text-sm text-muted-foreground mt-1">
                  {task.description}
                </Text>
              )}
              <div className="flex items-center gap-4 mt-2 text-xs text-muted-foreground">
                {task.assigned_to && <span>Assigned to: {task.assigned_to}</span>}
                {task.due_date && <span>Due: {formatDate(task.due_date)}</span>}
              </div>
            </div>
            <div className="flex items-center gap-2">
              {task.priority && (
                <Badge className="text-xs">{task.priority}</Badge>
              )}
              <Badge>{task.status || "pending"}</Badge>
            </div>
          </div>
        ))}
      </div>
    );
  };

  return (
    <Container className="divide-y p-0">
      <div className="flex items-center justify-between px-6 py-4">
        <Heading>Task Assignments</Heading>
        {hasPermission("concierge_management:create") && (
          <ActionMenu
            groups={[
              {
                actions: [
                  {
                    label: "Add Task",
                    onClick: handleAddTask,
                    icon: <PlusMini />,
                  },
                ],
              },
            ]}
          />
        )}
      </div>

      <SectionRow
        title="Assigned Tasks"
        value={formatTasks()}
      />
    </Container>
  );
};

export default ConciergeBookingTasksSection;
