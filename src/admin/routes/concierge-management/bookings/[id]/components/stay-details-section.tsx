import React from "react";
import { Container, Heading, Text } from "@camped-ai/ui";
import StayDetailsCard from "./stay-details-card";

interface StayDetailsSectionProps {
  booking: {
    order?: {
      items?: Array<{
        id: string;
        variant_id: string;
        title: string;
        quantity: number;
        unit_price: number;
        metadata?: Record<string, any>;
      }>;
      currency_code?: string;
    };
    currency_code?: string;
  };
}

const StayDetailsSection: React.FC<StayDetailsSectionProps> = ({ booking }) => {
  // Extract order items that represent room bookings
  const orderItems = booking.order?.items || [];
  
  // Filter items that appear to be room bookings
  // Look for items with hotel/room related metadata or specific patterns
  const roomItems = orderItems.filter(item => {
    const metadata = item.metadata || {};
    
    // Check if item has hotel/room related metadata
    const hasHotelData = metadata.hotel_id || metadata.hotel_name;
    const hasRoomData = metadata.room_config_id || metadata.room_config_name || metadata.room_type;
    const hasDateData = metadata.check_in_date || metadata.check_out_date;
    
    // Check if variant_id suggests it's a room (common patterns)
    const isRoomVariant = item.variant_id && (
      item.variant_id.includes('room') || 
      item.variant_id.includes('suite') ||
      item.variant_id.includes('hotel')
    );
    
    // Check if title suggests it's a room
    const isRoomTitle = item.title && (
      item.title.toLowerCase().includes('room') ||
      item.title.toLowerCase().includes('suite') ||
      item.title.toLowerCase().includes('hotel')
    );
    
    return hasHotelData || hasRoomData || hasDateData || isRoomVariant || isRoomTitle;
  });

  // Get currency code from order or booking
  const currencyCode = booking.order?.currency_code || booking.currency_code || "USD";

  // If no room items found, show a message
  if (roomItems.length === 0) {
    return (
      <Container className="divide-y p-0">
        <div className="flex items-center justify-between px-6 py-4">
          <Heading>Stay Details</Heading>
        </div>
        <div className="px-6 py-4">
          <Text className="text-muted-foreground">
            No room bookings found in this order.
          </Text>
        </div>
      </Container>
    );
  }

  return (
    <Container className="divide-y p-0">
      <div className="flex items-center justify-between px-6 py-4">
        <Heading>Stay Details</Heading>
        {roomItems.length > 1 && (
          <Text className="text-sm text-muted-foreground">
            {roomItems.length} room{roomItems.length !== 1 ? 's' : ''}
          </Text>
        )}
      </div>
      
      <div className="p-6 space-y-4">
        {roomItems.map((item, index) => (
          <StayDetailsCard
            key={item.id || `room-${index}`}
            orderItem={item}
            currencyCode={currencyCode}
          />
        ))}
      </div>
    </Container>
  );
};

export default StayDetailsSection;
