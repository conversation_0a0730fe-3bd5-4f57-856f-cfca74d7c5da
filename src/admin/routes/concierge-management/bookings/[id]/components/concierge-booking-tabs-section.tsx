import { useState } from "react";
import { Container, Tabs, Text, Heading } from "@camped-ai/ui";
import { CheckSquare, FileText, Mail, MessageCircle } from "lucide-react";

// Import components
import { ConciergeBookingTasksTable } from "./concierge-booking-tasks-table.tsx";
import NotesSection from "./notes-section";

interface ConciergeBookingTabsSectionProps {
  booking: any;
  currentAssignedTo?: string;
}

type TabId = "tasks" | "notes" | "email" | "whatsapp";

const ConciergeBookingTabsSection = ({ booking, currentAssignedTo }: ConciergeBookingTabsSectionProps) => {
  const [activeTab, setActiveTab] = useState<TabId>("tasks");

  const tabs = [
    {
      id: "tasks" as TabId,
      label: "Tasks",
      icon: CheckSquare,
    },
    {
      id: "notes" as TabId,
      label: "Notes",
      icon: FileText,
    },
    {
      id: "email" as TabId,
      label: "Email",
      icon: Mail,
    },
    {
      id: "whatsapp" as TabId,
      label: "WhatsApp",
      icon: MessageCircle,
    },
  ];

  const renderTabContent = () => {
    switch (activeTab) {
      case "tasks":
        return <ConciergeBookingTasksTable booking={booking} currentAssignedTo={currentAssignedTo} />;
      case "notes":
        return <NotesSection entity="booking" entityId={booking.id} />;
      case "email":
        return (
          <div className="flex items-center justify-center py-12">
            <div className="text-center">
              <Mail className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <Heading level="h3" className="text-lg mb-2">
                Email Coming Soon
              </Heading>
              <Text className="text-muted-foreground">
                Email functionality will be available in a future update.
              </Text>
            </div>
          </div>
        );
      case "whatsapp":
        return (
          <div className="w-full h-[600px] border border-gray-200 rounded-lg overflow-hidden">
            <iframe
              src="https://qa.one.flinkk.io/public/whatsapp-chat?inboxId=6889b3cfc5d3ad3f85892823&to=916203572705"
              className="w-full h-full border-0"
              title="WhatsApp Chat"
              allow="microphone; camera"
              sandbox="allow-scripts allow-same-origin allow-forms allow-popups allow-popups-to-escape-sandbox"
            />
          </div>
        );
      default:
        return null;
    }
  };

  return (
    <>

      <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as TabId)}>
        <Container>
          <Tabs.List className="grid w-full grid-cols-4">
            {tabs.map((tab) => {
              const Icon = tab.icon;
              return (
                <Tabs.Trigger key={tab.id} value={tab.id} className="flex items-center gap-2">
                  <Icon className="h-4 w-4" />
                  {tab.label}
                </Tabs.Trigger>
              );
            })}
          </Tabs.List>
        </Container>

        {tabs.map((tab) => (
          <Tabs.Content key={tab.id} value={tab.id} className="mt-3">
            {renderTabContent()}
          </Tabs.Content>
        ))}
      </Tabs>
    </>
  );
};

export default ConciergeBookingTabsSection;
