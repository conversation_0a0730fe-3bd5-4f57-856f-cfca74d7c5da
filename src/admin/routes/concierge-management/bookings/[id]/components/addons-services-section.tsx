import { Container, Heading, Text, Badge, Table } from "@camped-ai/ui";
import { Package, Info } from "lucide-react";
import { EnhancedConciergeOrder, AddOnService } from "../types";
import { formatCurrencyDisplay } from "../../../../../utils/currency-helpers";

interface AddOnsServicesSectionProps {
  booking: EnhancedConciergeOrder | null;
}

const AddOnsServicesSection = ({ booking }: AddOnsServicesSectionProps) => {
  if (!booking) {
    return (
      <Container className="divide-y p-0">
        <div className="flex items-center justify-between px-6 py-4">
          <Heading level="h2">Add-ons & Services</Heading>
        </div>
        <div className="px-6 py-4">
          <Text className="text-ui-fg-muted">Loading services information...</Text>
        </div>
      </Container>
    );
  }

  // Format currency helper using standardized currency helpers
  const formatCurrency = (amount: number, currencyCode: string = 'GBP') => {
    return formatCurrencyDisplay(amount, currencyCode);
  };

  // Process order line items into add-on services
  const getAddOnServices = (): AddOnService[] => {
    if (!booking.order_line_items || booking.order_line_items.length === 0) {
      return [];
    }

    return booking.order_line_items.map(item => ({
      id: item.id,
      title: item.title || 'Service',
      quantity: item.quantity || 1,
      unit_price: item.unit_price || 0,
      total_price: (item.unit_price || 0) * (item.quantity || 1),
      metadata: item.metadata
    }));
  };

  const addOnServices = getAddOnServices();
  const currency = booking.order_currency_code || booking.order?.currency_code || 'GBP';

  // Calculate totals
  const totalAmount = addOnServices.reduce((sum, service) => sum + service.total_price, 0);

  // Table columns
  const columns = [
    {
      header: "Service/Product",
      accessorKey: "title",
      cell: ({ row }: any) => {
        const service = row.original as AddOnService;
        return (
          <div>
            <Text className="font-medium">{service.title}</Text>
            {service.metadata?.description && (
              <Text className="text-sm text-ui-fg-muted mt-1">
                {service.metadata.description}
              </Text>
            )}
          </div>
        );
      },
    },
    {
      header: "Quantity",
      accessorKey: "quantity",
      cell: ({ row }: any) => {
        const service = row.original as AddOnService;
        return <Text>{service.quantity}</Text>;
      },
    },
    {
      header: "Unit Price",
      accessorKey: "unit_price",
      cell: ({ row }: any) => {
        const service = row.original as AddOnService;
        return (
          <Text className="font-medium">
            {formatCurrency(service.unit_price, currency)}
          </Text>
        );
      },
    },
    {
      header: "Total Price",
      accessorKey: "total_price",
      cell: ({ row }: any) => {
        const service = row.original as AddOnService;
        return (
          <Text className="font-medium">
            {formatCurrency(service.total_price, currency)}
          </Text>
        );
      },
    },
    {
      header: "Details",
      accessorKey: "metadata",
      cell: ({ row }: any) => {
        const service = row.original as AddOnService;
        const hasMetadata = service.metadata && Object.keys(service.metadata).length > 0;
        
        if (!hasMetadata) return <Text className="text-ui-fg-muted">—</Text>;
        
        return (
          <div className="space-y-1">
            {service.metadata?.category && (
              <Badge variant="secondary" size="small">
                {service.metadata.category}
              </Badge>
            )}
            {service.metadata?.notes && (
              <Text className="text-sm text-ui-fg-muted">
                {service.metadata.notes}
              </Text>
            )}
            {service.metadata?.variant_id && (
              <Text className="text-xs text-ui-fg-muted">
                Variant: {service.metadata.variant_id}
              </Text>
            )}
          </div>
        );
      },
    },
  ];

  return (
    <Container className="divide-y p-0">
      <div className="flex items-center justify-between px-6 py-4">
        <Heading level="h2">Add-ons & Services</Heading>
        <Badge variant="secondary">
          <Package className="h-3 w-3 mr-1" />
          {addOnServices.length} Item{addOnServices.length !== 1 ? 's' : ''}
        </Badge>
      </div>

      <div className="px-6 py-4">
        {addOnServices.length > 0 ? (
          <div className="space-y-4">
            {/* Services Table */}
            <div className="border border-ui-border-base rounded-lg overflow-hidden">
              <Table>
                <Table.Header>
                  <Table.Row>
                    {columns.map((column) => (
                      <Table.HeaderCell key={column.accessorKey}>
                        {column.header}
                      </Table.HeaderCell>
                    ))}
                  </Table.Row>
                </Table.Header>
                <Table.Body>
                  {addOnServices.map((service, index) => (
                    <Table.Row key={service.id || index}>
                      {columns.map((column) => (
                        <Table.Cell key={column.accessorKey}>
                          {column.cell({ row: { original: service } })}
                        </Table.Cell>
                      ))}
                    </Table.Row>
                  ))}
                </Table.Body>
              </Table>
            </div>

            {/* Total Summary */}
            <div className="flex justify-end">
              <div className="bg-ui-bg-subtle p-4 rounded-lg">
                <div className="flex items-center justify-between gap-8">
                  <Text className="font-medium">Total Add-ons & Services:</Text>
                  <Text className="font-bold text-lg">
                    {formatCurrency(totalAmount, currency)}
                  </Text>
                </div>
              </div>
            </div>

            {/* Additional Information */}
            {addOnServices.some(service => service.metadata?.special_instructions) && (
              <div className="mt-4 p-4 bg-ui-bg-subtle rounded-lg">
                <div className="flex items-start gap-2">
                  <Info className="h-4 w-4 text-ui-fg-muted mt-0.5" />
                  <div>
                    <Text className="font-medium text-sm">Special Instructions:</Text>
                    <div className="mt-2 space-y-1">
                      {addOnServices
                        .filter(service => service.metadata?.special_instructions)
                        .map((service, index) => (
                          <Text key={index} className="text-sm text-ui-fg-muted">
                            • {service.title}: {service.metadata?.special_instructions}
                          </Text>
                        ))}
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        ) : (
          <div className="text-center py-8">
            <Package className="h-12 w-12 text-ui-fg-muted mx-auto mb-4" />
            <Heading level="h3" className="text-ui-fg-muted">No Add-ons or Services</Heading>
            <Text className="text-ui-fg-muted mt-2">
              This booking doesn't have any additional services or add-ons.
            </Text>
          </div>
        )}
      </div>
    </Container>
  );
};

export default AddOnsServicesSection;
