import { Container, <PERSON><PERSON>, Text, Badge, Button, toast, Tooltip } from "@camped-ai/ui";
import { useTranslation } from "react-i18next";
import { useNavigate } from "react-router-dom";
import { format } from "date-fns";
import { useState } from "react";

import { SectionRow } from "../../../../../../components/common/section/section-row";
import { useRbac } from "../../../../../hooks/use-rbac";
import ConciergeBookingCustomerSection from "./concierge-booking-customer-section";

interface ConciergeBookingItinerarySectionProps {
  booking: any;
  hotelDetails: any;
  onItineraryCreated?: () => void; // Callback to refresh booking data
  onCustomerUpdate?: () => void; // Callback to refresh customer data
}

const ConciergeBookingItinerarySection = ({
  booking,
  hotelDetails,
  onItineraryCreated,
  onCustomerUpdate
}: ConciergeBookingItinerarySectionProps) => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const { hasPermission } = useRbac();
  const [isCreatingItinerary, setIsCreatingItinerary] = useState(false);
    const [isLoading, setIsLoading] = useState(true);

  // Format date
  const formatDate = (dateString: string) => {
    try {
      return format(new Date(dateString), "MMM dd");
    } catch (error) {
      return "Invalid date";
    }
  };

  // Format date range
  const formatDateRange = () => {
    if (!booking.check_in_date || !booking.check_out_date) return "Dates not specified";

    const checkIn = formatDate(booking.check_in_date);
    const checkOut = formatDate(booking.check_out_date);
    const year = format(new Date(booking.check_in_date), "yyyy");

    return `${checkIn} – ${checkOut}, ${year}`;
  };

  const truncate = (str: string, start = 12, end = 12) => {
    if (!str || str.length <= start + end) {
      return str;
    }
    return `${str.substring(0, start)}...${str.substring(str.length - end)}`;
  };

  const handleCreateItinerary = async () => {
    if (!booking?.id) {
      toast.error("Error", {
        description: "Booking ID is required to create an itinerary",
      });
      return;
    }

    setIsCreatingItinerary(true);

    try {
      const response = await fetch(
        `/admin/concierge-management/bookings/${booking.id}/itinerary/create`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            created_by: "current_user", // TODO: Get actual user ID from auth context
          }),
        }
      );

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to create itinerary");
      }

      const data = await response.json();

      toast.success("Success", {
        description: data.message || "Itinerary created successfully",
      });

      // Call the callback to refresh booking data
      if (onItineraryCreated) {
        onItineraryCreated();
      }

      // Navigate to the itinerary builder
      if (data.redirect_url) {
        navigate(data.redirect_url);
      } else {
        navigate(`/concierge-management/itineraries/${data.itinerary.id}/builder`);
      }
    } catch (error: any) {
      console.error("Error creating itinerary:", error);
      toast.error("Error", {
        description: error.message || "Failed to create itinerary",
      });
    } finally {
      setIsCreatingItinerary(false);
    }
  };

  const handleViewItinerary = () => {
    const itineraryId = booking.itinerary_id || booking.order?.metadata?.itinerary_id;
    if (itineraryId) {
      navigate(`/concierge-management/itineraries/${itineraryId}/builder`);
    }
  };

  // Format itinerary status with tooltip
  const formatItineraryStatus = () => {
    const itineraryId = booking.itinerary_id || booking.order?.metadata?.itinerary_id;
    if (!itineraryId) {
      return (
        <div className="flex items-center gap-2">
          <Text className="text-muted-foreground">Not created</Text>
        </div>
      );
    }
    return (
      <div className="flex items-center gap-2">
        <Badge color="green">Active</Badge>
      </div>
    );
  };



  return (
    <>
      {/* Itinerary Section */}
      <Container className="divide-y p-0">
        <div className="flex items-center justify-between px-6 py-4">
          <Heading>Itinerary</Heading>
          {(booking.itinerary_id || booking.order?.metadata?.itinerary_id) ? (
            <Button variant="secondary" onClick={handleViewItinerary}>
              View Itinerary
            </Button>
          ) : (
            hasPermission("concierge_management:create") && (
              <Button
                variant="secondary"
                onClick={handleCreateItinerary}
                disabled={isCreatingItinerary}
              >
                {isCreatingItinerary ? "Creating..." : "Create Itinerary"}
              </Button>
            )
          )}
        </div>

        <SectionRow
          title="Itinerary ID"
          value={
            (() => {
              const itineraryId = booking.itinerary_id || booking.order?.metadata?.itinerary_id;
              return itineraryId ? (
                <Tooltip content={itineraryId}>
                  <Text as="span">{truncate(itineraryId)}</Text>
                </Tooltip>
              ) : (
                <Text className="text-muted-foreground italic">
                  Not created
                </Text>
              );
            })()
          }
        />
        <SectionRow
          title="Status"
          value={formatItineraryStatus()}
        />
      </Container>
    </>
  );
};

export default ConciergeBookingItinerarySection;
