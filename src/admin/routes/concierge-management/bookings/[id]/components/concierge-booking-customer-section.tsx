import { useState, useCallback, useEffect } from "react";
import { Container, Heading, Text, Input, Button, toast, Badge } from "@camped-ai/ui";
import { User, Mail, Phone, Edit, Save, X, Users } from "lucide-react";

import { SectionRow } from "../../../../../../components/common/section/section-row";
import { useRbac } from "../../../../../hooks/use-rbac";
import { GuestInfo } from "../types";

interface ConciergeBookingCustomerSectionProps {
  booking: any;
  onCustomerUpdate?: () => void;
}

const ConciergeBookingCustomerSection = ({
  booking,
  onCustomerUpdate,
}: ConciergeBookingCustomerSectionProps) => {
  const { hasPermission } = useRbac();
  const [isEditing, setIsEditing] = useState(false);
  const [isUpdating, setIsUpdating] = useState(false);

  // Extract customer information from booking object with proper null checks
  const originalCustomerName = booking?.guest_name ||
                              booking?.order?.metadata?.guest_name ||
                              (booking?.order?.customer?.first_name && booking?.order?.customer?.last_name
                                ? `${booking.order.customer.first_name} ${booking.order.customer.last_name}`
                                : "");

  const originalCustomerEmail = booking?.guest_email ||
                               booking?.order?.metadata?.guest_email ||
                               booking?.order?.email ||
                               booking?.order?.customer?.email ||
                               "";

  const originalCustomerPhone = booking?.guest_phone ||
                               booking?.order?.metadata?.guest_phone ||
                               booking?.order?.customer?.phone ||
                               "";

  // Local state for editing
  const [editedName, setEditedName] = useState(originalCustomerName);
  const [editedPhone, setEditedPhone] = useState(originalCustomerPhone);

  // Sync local state when booking data changes
  useEffect(() => {
    setEditedName(originalCustomerName);
    setEditedPhone(originalCustomerPhone);
  }, [originalCustomerName, originalCustomerPhone]);

  // Handle save customer updates
  const handleSaveCustomer = useCallback(async () => {
    if (!booking?.id) return;

    setIsUpdating(true);
    try {
      const response = await fetch(`/admin/concierge-management/orders/${booking.id}`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          guest_name: editedName || undefined,
          guest_phone: editedPhone || undefined,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || "Failed to update customer information");
      }

      toast.success("Success", {
        description: "Customer information updated successfully",
      });

      setIsEditing(false);
      onCustomerUpdate?.();
    } catch (error: any) {
      console.error("Error updating customer information:", error);
      toast.error("Error", {
        description: error.message || "Failed to update customer information",
      });
    } finally {
      setIsUpdating(false);
    }
  }, [booking?.id, editedName, editedPhone, onCustomerUpdate]);

  // Handle cancel editing
  const handleCancelEdit = useCallback(() => {
    setEditedName(originalCustomerName);
    setEditedPhone(originalCustomerPhone);
    setIsEditing(false);
  }, [originalCustomerName, originalCustomerPhone]);

  // Safety check - don't render if booking data is not available
  if (!booking) {
    return null;
  }

  // Extract guest breakdown information
  const getGuestInfo = (): GuestInfo => {
    // Check customer metadata first
    const customerMetadata = booking?.order?.customer?.metadata || {};

    // Check order metadata
    const orderMetadata = booking?.order?.metadata || {};

    // Check order line item metadata
    const lineItemMetadata = booking?.order_line_items?.[0]?.metadata || {};

    // Try to extract guest breakdown from various sources
    const guestInfo = customerMetadata.guest_info ||
                     orderMetadata.guest_info ||
                     lineItemMetadata.guest_info ||
                     orderMetadata.travelers ||
                     lineItemMetadata.travelers;

    if (guestInfo) {
      // If we have structured guest info
      if (typeof guestInfo === 'object' && guestInfo.adults) {
        return {
          adults: guestInfo.adults || [],
          children: guestInfo.children || [],
          infants: guestInfo.infants || [],
          total_guests: (guestInfo.adults?.length || 0) + (guestInfo.children?.length || 0) + (guestInfo.infants?.length || 0)
        };
      }
    }

    // Fallback to simple guest count
    const totalGuests = orderMetadata.number_of_guests ||
                       lineItemMetadata.number_of_guests ||
                       orderMetadata.guest_count ||
                       lineItemMetadata.guest_count || 1;

    return {
      adults: Array(Math.max(1, totalGuests)).fill({ age: undefined }),
      children: [],
      infants: [],
      total_guests: totalGuests
    };
  };

  const formatGuestBreakdown = (guestInfo: GuestInfo) => {
    const parts = [];

    if (guestInfo.adults.length > 0) {
      const adultsWithAges = guestInfo.adults.filter(a => a.age).map(a => `${a.age}y`);
      if (adultsWithAges.length > 0) {
        parts.push(`${guestInfo.adults.length} Adult${guestInfo.adults.length > 1 ? 's' : ''} (${adultsWithAges.join(', ')})`);
      } else {
        parts.push(`${guestInfo.adults.length} Adult${guestInfo.adults.length > 1 ? 's' : ''}`);
      }
    }

    if (guestInfo.children.length > 0) {
      const childrenWithAges = guestInfo.children.filter(c => c.age).map(c => `${c.age}y`);
      if (childrenWithAges.length > 0) {
        parts.push(`${guestInfo.children.length} Child${guestInfo.children.length > 1 ? 'ren' : ''} (${childrenWithAges.join(', ')})`);
      } else {
        parts.push(`${guestInfo.children.length} Child${guestInfo.children.length > 1 ? 'ren' : ''}`);
      }
    }

    if (guestInfo.infants.length > 0) {
      const infantsWithAges = guestInfo.infants.filter(i => i.age).map(i => `${i.age}m`);
      if (infantsWithAges.length > 0) {
        parts.push(`${guestInfo.infants.length} Infant${guestInfo.infants.length > 1 ? 's' : ''} (${infantsWithAges.join(', ')})`);
      } else {
        parts.push(`${guestInfo.infants.length} Infant${guestInfo.infants.length > 1 ? 's' : ''}`);
      }
    }

    return parts.join(', ') || `${guestInfo.total_guests} Guest${guestInfo.total_guests > 1 ? 's' : ''}`;
  };

  // Format phone number for display
  const formatPhoneNumber = (phone: string) => {
    if (!phone || phone === "Not specified") return phone;

    // If phone starts with + (country code), format as (+XX) XXXXXXXXXX
    if (phone.startsWith('+')) {
      const cleaned = phone.replace(/\D/g, '');
      if (cleaned.length >= 3) {
        // Extract country code (first 1-3 digits after +)
        let countryCode = '';
        let number = '';

        // Common country code patterns
        if (cleaned.startsWith('1') && cleaned.length === 11) {
          // US/Canada: +1
          countryCode = cleaned.slice(0, 1);
          number = cleaned.slice(1);
        } else if (cleaned.startsWith('91') && cleaned.length === 12) {
          // India: +91
          countryCode = cleaned.slice(0, 2);
          number = cleaned.slice(2);
        } else if (cleaned.length >= 10) {
          // Default: assume 2-digit country code
          countryCode = cleaned.slice(0, 2);
          number = cleaned.slice(2);
        }

        if (countryCode && number) {
          return `(+${countryCode}) ${number}`;
        }
      }
      return phone; // Return original if can't parse
    }

    // If no country code, just return the number as-is
    return phone;
  };

  const guestInfo = getGuestInfo();

  return (
    <Container className="divide-y p-0">
      <div className="flex items-center justify-between px-6 py-4">
        <div className="flex items-center gap-3">
          <Heading>Customer Details</Heading>
          <Badge color="grey" size="small">
            <Users className="h-3 w-3 mr-1" />
            {guestInfo.total_guests} Guest{guestInfo.total_guests > 1 ? 's' : ''}
          </Badge>
        </div>
        {hasPermission("concierge_management:edit") && !isEditing && (
          <Button
            variant="secondary"
            size="small"
            onClick={() => setIsEditing(true)}
            disabled={isUpdating}
          >
            <Edit className="h-4 w-4 mr-1" />
            Edit
          </Button>
        )}
        {isEditing && (
          <div className="flex items-center gap-2">
            <Button
              variant="primary"
              size="small"
              onClick={handleSaveCustomer}
              disabled={isUpdating}
            >
              <Save className="h-4 w-4 mr-1" />
              {isUpdating ? "Saving..." : "Save"}
            </Button>
            <Button
              variant="secondary"
              size="small"
              onClick={handleCancelEdit}
              disabled={isUpdating}
            >
              <X className="h-4 w-4 mr-1" />
              Cancel
            </Button>
          </div>
        )}
      </div>

      <SectionRow
        title="Customer Name"
        value={
          <div className="flex items-center gap-2">
            <User className="h-4 w-4 text-ui-fg-subtle" />
            {isEditing ? (
              <Input
                value={editedName}
                onChange={(e) => setEditedName(e.target.value)}
                placeholder="Enter customer name"
                className="w-48"
                disabled={isUpdating}
              />
            ) : (
              <Text>{originalCustomerName || "Not specified"}</Text>
            )}
          </div>
        }
      />

      <SectionRow
        title="Email Address"
        value={
          <div className="flex items-center gap-2">
            <Mail className="h-4 w-4 text-ui-fg-subtle" />
            <Text>{originalCustomerEmail || "Not specified"}</Text>
          </div>
        }
      />

      <SectionRow
        title="Phone Number"
        value={
          <div className="flex items-center gap-2">
            <Phone className="h-4 w-4 text-ui-fg-subtle" />
            {isEditing ? (
              <Input
                value={editedPhone}
                onChange={(e) => setEditedPhone(e.target.value)}
                placeholder="Enter phone number"
                className="w-48"
                disabled={isUpdating}
              />
            ) : (
              <Text>{formatPhoneNumber(originalCustomerPhone) || "Not specified"}</Text>
            )}
          </div>
        }
      />

      <SectionRow
        title="Guest Breakdown"
        value={
          <div className="space-y-2">
            <div className="flex items-center gap-2">
              <Users className="h-4 w-4 text-ui-fg-subtle" />
              <Text>{formatGuestBreakdown(guestInfo)}</Text>
            </div>

            {/* Detailed breakdown if ages are available */}
            {(guestInfo.adults.some(a => a.age) || guestInfo.children.some(c => c.age) || guestInfo.infants.some(i => i.age)) && (
              <div className="ml-6 space-y-1 text-sm text-ui-fg-muted">
                {guestInfo.adults.filter(a => a.age).length > 0 && (
                  <div>Adults: {guestInfo.adults.filter(a => a.age).map(a => `${a.age} years`).join(', ')}</div>
                )}
                {guestInfo.children.filter(c => c.age).length > 0 && (
                  <div>Children: {guestInfo.children.filter(c => c.age).map(c => `${c.age} years`).join(', ')}</div>
                )}
                {guestInfo.infants.filter(i => i.age).length > 0 && (
                  <div>Infants: {guestInfo.infants.filter(i => i.age).map(i => `${i.age} months`).join(', ')}</div>
                )}
              </div>
            )}
          </div>
        }
      />
    </Container>
  );
};

export default ConciergeBookingCustomerSection;
