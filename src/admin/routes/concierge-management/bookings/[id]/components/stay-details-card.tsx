import React, { useState, useEffect } from "react";
import { Container, Text } from "@camped-ai/ui";
import { Calendar, Hotel, User, Utensils } from "lucide-react";
import {
  fetchVariantData,
  fetchRoomConfigData,
  formatStayDates,
  formatCurrency,
  extractHotelInfo,
  getRoomName,
  getRoomConfigName,
  calculateTotalPrice,
  type VariantData,
  type RoomConfigData
} from "../utils/stay-details-utils";

interface StayDetailsCardProps {
  orderItem: {
    id: string;
    variant_id: string;
    title: string;
    quantity: number;
    unit_price: number;
    metadata?: {
      hotel_id?: string;
      hotel_name?: string;
      room_config_id?: string;
      room_config_name?: string;
      check_in_date?: string;
      check_out_date?: string;
      occupancy_type?: string;
      meal_plan?: string;
      number_of_guests?: number;
      [key: string]: any;
    };
  };
  currencyCode?: string;
}

const StayDetailsCard: React.FC<StayDetailsCardProps> = ({
  orderItem,
  currencyCode = "USD"
}) => {
  const [variantData, setVariantData] = useState<VariantData | null>(null);
  const [roomConfigData, setRoomConfigData] = useState<RoomConfigData | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  // Load data on component mount
  useEffect(() => {
    const loadData = async () => {
      setIsLoading(true);

      // Fetch variant data if variant_id exists
      if (orderItem.variant_id) {
        const variant = await fetchVariantData(orderItem.variant_id);
        setVariantData(variant);
      }

      // Fetch room config data if room_config_id exists in metadata
      if (orderItem.metadata?.room_config_id) {
        const roomConfig = await fetchRoomConfigData(orderItem.metadata.room_config_id);
        setRoomConfigData(roomConfig);
      }

      setIsLoading(false);
    };

    loadData();
  }, [orderItem.variant_id, orderItem.metadata?.room_config_id]);

  // Use utility functions to get data
  const { dateRange, nights } = formatStayDates(
    orderItem.metadata?.check_in_date,
    orderItem.metadata?.check_out_date
  );

  const hotelInfo = extractHotelInfo(orderItem.metadata);
  const roomName = getRoomName(variantData, orderItem.title, orderItem.metadata);
  const roomConfigName = getRoomConfigName(roomConfigData, orderItem.metadata);
  const totalPrice = calculateTotalPrice(orderItem.unit_price, orderItem.quantity, nights);

  if (isLoading) {
    return (
      <Container className="p-4 border border-border rounded-lg">
        <div className="animate-pulse">
          <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
          <div className="h-3 bg-gray-200 rounded w-1/2 mb-2"></div>
          <div className="h-3 bg-gray-200 rounded w-2/3"></div>
        </div>
      </Container>
    );
  }

  return (
    <Container className="p-4 border border-border rounded-lg bg-card shadow-sm">
      {/* Room Name (Primary Display) */}
      <div className="flex items-start justify-between mb-3">
        <div className="flex-1">
          <div className="flex items-center gap-2 mb-1">
            <Hotel className="w-4 h-4 text-muted-foreground" />
            <Text className="font-semibold text-lg">
              {roomName}
            </Text>
          </div>
          {roomConfigName && (
            <Text className="text-sm text-muted-foreground ml-6">
              {roomConfigName}
            </Text>
          )}
          {/* Variant ID - Less prominent, for reference only */}
          <Text className="text-xs text-muted-foreground font-mono ml-6 mt-1">
            {orderItem.variant_id}
          </Text>
        </div>
      </div>

      {/* Stay Dates */}
      <div className="flex items-center gap-2 mb-3">
        <Calendar className="w-4 h-4 text-muted-foreground" />
        <Text className="text-sm">
          {dateRange}
        </Text>
      </div>

      {/* Price Breakdown */}
      <div className="mb-4">
        <Text className="text-lg font-semibold text-green-600">
          {formatCurrency(orderItem.unit_price, currencyCode)}/night × {nights} = {formatCurrency(totalPrice, currencyCode)}
        </Text>
      </div>

      {/* Hotel Information */}
      <div className="space-y-1">
        <div className="flex items-center gap-2">
          <Hotel className="w-4 h-4 text-muted-foreground" />
          <Text className="font-medium">
            {hotelInfo.name}
          </Text>
        </div>
        <div className="flex items-center gap-2">
          <User className="w-4 h-4 text-muted-foreground" />
          <Text className="text-sm text-muted-foreground">
            {hotelInfo.occupancy}
          </Text>
        </div>
        <div className="flex items-center gap-2">
          <Utensils className="w-4 h-4 text-muted-foreground" />
          <Text className="text-sm text-muted-foreground">
            {hotelInfo.mealPlan}
          </Text>
        </div>
      </div>
    </Container>
  );
};

export default StayDetailsCard;
