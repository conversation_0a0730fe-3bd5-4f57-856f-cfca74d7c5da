import { Container, Heading, Text, Badge } from "@camped-ai/ui";
import { Users, Mail, Phone, User } from "lucide-react";
import { SectionRow } from "../../../../../../components/common/section/section-row";
import { EnhancedConciergeOrder, GuestInfo } from "../types";

interface CustomerDetailsSectionProps {
  booking: EnhancedConciergeOrder | null;
}

const CustomerDetailsSection = ({ booking }: CustomerDetailsSectionProps) => {
  if (!booking) {
    return (
      <Container className="divide-y p-0">
        <div className="flex items-center justify-between px-6 py-4">
          <Heading level="h2">Customer Details</Heading>
        </div>
        <div className="px-6 py-4">
          <Text className="text-ui-fg-muted">Loading customer information...</Text>
        </div>
      </Container>
    );
  }

  // Extract customer information from multiple sources
  const getCustomerInfo = () => {
    // Priority: customer table > order metadata > order line item metadata
    const firstName = booking.customer_first_name || 
                     booking.order?.customer?.first_name || 
                     booking.order?.metadata?.guest_name?.split(' ')[0] ||
                     booking.order?.metadata?.customer_first_name;
    
    const lastName = booking.customer_last_name || 
                    booking.order?.customer?.last_name || 
                    booking.order?.metadata?.guest_name?.split(' ').slice(1).join(' ') ||
                    booking.order?.metadata?.customer_last_name;
    
    const email = booking.customer_email || 
                 booking.order?.customer?.email || 
                 booking.order?.metadata?.guest_email ||
                 booking.order?.metadata?.customer_email;
    
    const phone = booking.customer_phone || 
                 booking.order?.customer?.phone || 
                 booking.order?.metadata?.guest_phone ||
                 booking.order?.metadata?.customer_phone;

    return {
      firstName,
      lastName,
      fullName: firstName && lastName ? `${firstName} ${lastName}` : firstName || lastName || 'Guest',
      email,
      phone
    };
  };

  // Extract guest breakdown information
  const getGuestInfo = (): GuestInfo => {
    // Check customer metadata first
    const customerMetadata = booking.order?.customer?.metadata || {};
    
    // Check order metadata
    const orderMetadata = booking.order?.metadata || {};
    
    // Check order line item metadata
    const lineItemMetadata = booking.order_line_items?.[0]?.metadata || {};
    
    // Try to extract guest breakdown from various sources
    const guestInfo = customerMetadata.guest_info || 
                     orderMetadata.guest_info || 
                     lineItemMetadata.guest_info ||
                     orderMetadata.travelers ||
                     lineItemMetadata.travelers;

    if (guestInfo) {
      // If we have structured guest info
      if (typeof guestInfo === 'object' && guestInfo.adults) {
        return {
          adults: guestInfo.adults || [],
          children: guestInfo.children || [],
          infants: guestInfo.infants || [],
          total_guests: (guestInfo.adults?.length || 0) + (guestInfo.children?.length || 0) + (guestInfo.infants?.length || 0)
        };
      }
    }

    // Fallback to simple guest count
    const totalGuests = orderMetadata.number_of_guests || 
                       lineItemMetadata.number_of_guests || 
                       orderMetadata.guest_count ||
                       lineItemMetadata.guest_count || 1;

    return {
      adults: Array(Math.max(1, totalGuests)).fill({ age: undefined }),
      children: [],
      infants: [],
      total_guests: totalGuests
    };
  };

  const customerInfo = getCustomerInfo();
  const guestInfo = getGuestInfo();

  const formatGuestBreakdown = (guestInfo: GuestInfo) => {
    const parts = [];
    
    if (guestInfo.adults.length > 0) {
      const adultsWithAges = guestInfo.adults.filter(a => a.age).map(a => `${a.age}y`);
      if (adultsWithAges.length > 0) {
        parts.push(`${guestInfo.adults.length} Adult${guestInfo.adults.length > 1 ? 's' : ''} (${adultsWithAges.join(', ')})`);
      } else {
        parts.push(`${guestInfo.adults.length} Adult${guestInfo.adults.length > 1 ? 's' : ''}`);
      }
    }
    
    if (guestInfo.children.length > 0) {
      const childrenWithAges = guestInfo.children.filter(c => c.age).map(c => `${c.age}y`);
      if (childrenWithAges.length > 0) {
        parts.push(`${guestInfo.children.length} Child${guestInfo.children.length > 1 ? 'ren' : ''} (${childrenWithAges.join(', ')})`);
      } else {
        parts.push(`${guestInfo.children.length} Child${guestInfo.children.length > 1 ? 'ren' : ''}`);
      }
    }
    
    if (guestInfo.infants.length > 0) {
      const infantsWithAges = guestInfo.infants.filter(i => i.age).map(i => `${i.age}m`);
      if (infantsWithAges.length > 0) {
        parts.push(`${guestInfo.infants.length} Infant${guestInfo.infants.length > 1 ? 's' : ''} (${infantsWithAges.join(', ')})`);
      } else {
        parts.push(`${guestInfo.infants.length} Infant${guestInfo.infants.length > 1 ? 's' : ''}`);
      }
    }
    
    return parts.join(', ') || `${guestInfo.total_guests} Guest${guestInfo.total_guests > 1 ? 's' : ''}`;
  };

  return (
    <Container className="divide-y p-0">
      <div className="flex items-center justify-between px-6 py-4">
        <Heading level="h2">Customer Details</Heading>
        <Badge variant="secondary">
          <Users className="h-3 w-3 mr-1" />
          {guestInfo.total_guests} Guest{guestInfo.total_guests > 1 ? 's' : ''}
        </Badge>
      </div>

      <div className="px-6 py-4 space-y-4">
        {/* Customer Name */}
        <SectionRow title="Customer Name" value={customerInfo.fullName}>
          <div className="flex items-center gap-2">
            <User className="h-4 w-4 text-ui-fg-muted" />
            <Text>{customerInfo.fullName}</Text>
          </div>
        </SectionRow>

        {/* Email */}
        {customerInfo.email && (
          <SectionRow title="Email Address" value={customerInfo.email}>
            <div className="flex items-center gap-2">
              <Mail className="h-4 w-4 text-ui-fg-muted" />
              <Text>{customerInfo.email}</Text>
            </div>
          </SectionRow>
        )}

        {/* Phone */}
        {customerInfo.phone && (
          <SectionRow title="Phone Number" value={customerInfo.phone}>
            <div className="flex items-center gap-2">
              <Phone className="h-4 w-4 text-ui-fg-muted" />
              <Text>{customerInfo.phone}</Text>
            </div>
          </SectionRow>
        )}

        {/* Guest Breakdown */}
        <SectionRow title="Guest Breakdown" value={formatGuestBreakdown(guestInfo)}>
          <div className="space-y-2">
            <div className="flex items-center gap-2">
              <Users className="h-4 w-4 text-ui-fg-muted" />
              <Text>{formatGuestBreakdown(guestInfo)}</Text>
            </div>
            
            {/* Detailed breakdown if ages are available */}
            {(guestInfo.adults.some(a => a.age) || guestInfo.children.some(c => c.age) || guestInfo.infants.some(i => i.age)) && (
              <div className="ml-6 space-y-1 text-sm text-ui-fg-muted">
                {guestInfo.adults.filter(a => a.age).length > 0 && (
                  <div>Adults: {guestInfo.adults.filter(a => a.age).map(a => `${a.age} years`).join(', ')}</div>
                )}
                {guestInfo.children.filter(c => c.age).length > 0 && (
                  <div>Children: {guestInfo.children.filter(c => c.age).map(c => `${c.age} years`).join(', ')}</div>
                )}
                {guestInfo.infants.filter(i => i.age).length > 0 && (
                  <div>Infants: {guestInfo.infants.filter(i => i.age).map(i => `${i.age} months`).join(', ')}</div>
                )}
              </div>
            )}
          </div>
        </SectionRow>
      </div>
    </Container>
  );
};

export default CustomerDetailsSection;
