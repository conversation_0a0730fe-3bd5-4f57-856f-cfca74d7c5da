// Enhanced types for booking detail screen

export interface PaymentCollection {
  id: string
  amount: number
  captured_amount?: number
  authorized_amount?: number
  status: string
  currency_code: string
  created_at: string
  updated_at: string
  completed_at?: string
  captured_at?: string
  metadata?: Record<string, any>
}

export interface OrderLineItem {
  id: string
  unit_price: number
  quantity: number
  title: string
  variant_id?: string
  product_id?: string
  metadata?: Record<string, any>
}

export interface Customer {
  id: string
  first_name?: string
  last_name?: string
  email?: string
  phone?: string
  metadata?: Record<string, any>
}

export interface Order {
  id: string
  total: number
  currency_code: string
  status: string
  created_at: string
  metadata?: Record<string, any>
  customer?: Customer
}

export interface ConciergeOrderItem {
  id: string
  concierge_order_id: string
  product_id?: string
  variant_id?: string
  quantity: number
  unit_price: number
  total_price: number
  title: string
  description?: string
  metadata?: Record<string, any>
  created_at: string
  updated_at: string
}

export interface Hotel {
  id: string
  name: string
  address?: string
  city?: string
  country?: string
  metadata?: Record<string, any>
}

export interface EnhancedConciergeOrder {
  id: string
  order_id: string
  hotel_id?: string
  check_in_date?: string
  check_out_date?: string
  assigned_to?: string
  notes?: string
  status: string
  last_contacted_at?: string
  metadata?: Record<string, any>
  created_at: string
  updated_at: string
  deleted_at?: string
  
  // Enhanced API response fields
  customer_id?: string
  customer_first_name?: string
  customer_last_name?: string
  customer_email?: string
  customer_phone?: string
  hotel_name?: string
  order_total?: number
  order_status?: string
  order_currency_code?: string
  
  // Nested data
  order?: Order
  payment_collections: PaymentCollection[]
  order_line_items: OrderLineItem[]
  concierge_order_items: ConciergeOrderItem[]
}

// Guest information extracted from various sources
export interface GuestInfo {
  adults: Array<{ age?: number }>
  children: Array<{ age?: number }>
  infants: Array<{ age?: number }>
  total_guests: number
  guest_breakdown?: string
}

// Booking details for display
export interface BookingDetails {
  hotel_name?: string
  check_in_date?: string
  check_out_date?: string
  total_amount: number
  currency_code: string
  guest_info: GuestInfo
}

// Payment summary for display
export interface PaymentSummary {
  total_paid: number
  remaining_balance: number
  payments: Array<{
    amount: number
    date: string
    status: string
    payment_method?: string
  }>
}

// Add-on service for display
export interface AddOnService {
  id: string
  title: string
  quantity: number
  unit_price: number
  total_price: number
  metadata?: Record<string, any>
}
