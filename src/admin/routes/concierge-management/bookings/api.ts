import { sdk } from "../../../lib/sdk";

// Types for API responses
export interface Hotel {
  id: string;
  name: string;
}

export interface HotelsResponse {
  hotels: Hotel[];
  count: number;
  limit: number;
  offset: number;
}

// API function to fetch hotels
export const fetchHotels = async (): Promise<HotelsResponse> => {
  try {
    const response = await sdk.client.fetch("/admin/hotel-management/hotels") as any;
    // Ensure the response has the expected structure
    if (response && typeof response === 'object') {
      return {
        hotels: response.hotels || [],
        count: response.count || 0,
        limit: response.limit || 20,
        offset: response.offset || 0,
      };
    }
    // Fallback if response is not in expected format
    return {
      hotels: [],
      count: 0,
      limit: 20,
      offset: 0,
    };
  } catch (error) {
    console.error("Error fetching hotels:", error);
    throw error;
  }
};

// API function to fetch bookings with filters
export const fetchBookings = async (params: URLSearchParams) => {
  try {
    const response = await sdk.client.fetch(`/admin/hotel-management/bookings?${params}`);
    return response;
  } catch (error) {
    console.error("Error fetching bookings:", error);
    throw error;
  }
};
