import { QueryClient } from "@tanstack/react-query"
import { sdk } from "../../../../admin/lib/sdk"
import { queryClient } from "../../../../admin/lib/query-client"

// Query keys for concierge orders
const conciergeOrdersQueryKeys = {
    all: ["concierge-orders"] as const,
    lists: () => [...conciergeOrdersQueryKeys.all, "list"] as const,
    list: (filters: BookingScreenFilters) => [...conciergeOrdersQueryKeys.lists(), filters] as const,
    details: () => [...conciergeOrdersQueryKeys.all, "detail"] as const,
    detail: (id: string) => [...conciergeOrdersQueryKeys.details(), id] as const,
}

// Types for concierge order screen data
export interface BookingScreenFilters {
    limit?: number
    offset?: number
    q?: string
    hotel_id?: string
    status?: string
    payment_status?: string
    booking_status?: string
    sort_by?: string
    sort_order?: 'asc' | 'desc'
    customer_id?: string
    page?: number
    assigned_to?: string
    check_in_date_gte?: string
    check_in_date_lte?: string
    check_out_date_gte?: string
    check_out_date_lte?: string
    customer_name?: string
    order_id?: string
    created_at_gte?: string
    created_at_lte?: string
}

// Concierge Order Item interface
export interface ConciergeOrderItem {
    id: string
    concierge_order_id: string
    line_item_id: string | null
    item_id: string | null
    variant_id: string
    quantity: number
    unit_price: number
    title: string
    status: string
    is_active: boolean
    added_by: string | null
    finalized_by: string | null
    added_at: string
    finalized_at: string | null
    metadata: Record<string, any>
    created_at: string
    updated_at: string
    deleted_at: string | null
}

// Customer interface
export interface Customer {
    id: string
    first_name?: string
    last_name?: string
    email: string
    phone?: string
    metadata?: Record<string, any>
}

// Order interface (nested in concierge order)
export interface Order {
    id: string
    display_id: number
    region_id: string
    customer_id: string
    version: number
    sales_channel_id: string
    status: string
    is_draft_order: boolean
    email: string
    currency_code: string
    no_notification: boolean
    metadata: Record<string, any>
    canceled_at: string | null
    shipping_address: any
    billing_address: any
    created_at: string
    updated_at: string
    deleted_at: string | null
    shipping_address_id: string
    billing_address_id: string
    items: any[]
    customer?: Customer
}

// Payment Collection interface
export interface PaymentCollection {
    id: string
    amount: number
    captured_amount?: number
    authorized_amount?: number
    status: string
    currency_code: string
    created_at: string
    updated_at: string
    completed_at?: string
    captured_at?: string
    metadata?: Record<string, any>
}

// Order Line Item interface
export interface OrderLineItem {
    id: string
    unit_price: number
    quantity: number
    title: string
    variant_id?: string
    product_id?: string
    metadata?: Record<string, any>
}

// Main concierge order interface
export interface BookingScreenData {
    id: string
    order_id: string
    hotel_id?: string
    check_in_date?: string
    check_out_date?: string
    assigned_to: string | null
    notes: string
    status: string
    last_contacted_at: string | null
    metadata: Record<string, any>
    created_at: string
    updated_at: string
    deleted_at: string | null
    concierge_order_items: ConciergeOrderItem[]
    order: Order

    // Enhanced API response fields
    customer_id?: string
    customer_first_name?: string
    customer_last_name?: string
    customer_email?: string
    hotel_name?: string
    order_total?: number
    order_status?: string
    order_currency_code?: string

    // Nested payment and line item data (for frontend calculation)
    payment_collections: PaymentCollection[]
    order_line_items: OrderLineItem[]

    // Legacy computed fields (can be calculated on frontend now)
    total_selling_price?: number
    paid?: number
    remaining?: number
    nights?: number
    guest_count?: number
    room_type?: string
    check_in_formatted?: string
    check_out_formatted?: string
    total?: number
    payment_status?: string
}

export interface BookingScreenResponse {
    items: BookingScreenData[]
    count: number
    limit: number
    offset: number
}

// Default filters for booking screen
const defaultBookingScreenFilters: BookingScreenFilters = {
    limit: 20,
    offset: 0,
    sort_by: 'created_at',
    sort_order: 'desc',
}

// Query function for booking screen data
const bookingScreenQuery = (filters: BookingScreenFilters = {}) => {
    const mergedFilters = { ...defaultBookingScreenFilters, ...filters }

    return {
        queryKey: conciergeOrdersQueryKeys.list(mergedFilters),
        queryFn: async (): Promise<any> => {
            const params = new URLSearchParams()

            // Add filters to query params
            Object.entries(mergedFilters).forEach(([key, value]) => {
                if (value !== undefined && value !== null && value !== '') {
                    params.append(key, value.toString())
                }
            })

            const url = `/admin/concierge-management/orders${params.toString() ? `?${params.toString()}` : ''}`

            try {
                const response = await sdk.client.fetch(url)
                return response
            } catch (error) {
                console.error('Failed to fetch booking screen data:', error)
                throw error
            }
        }
    }
}
// Main loader function for booking screen
export const bookingScreenLoader = (client: QueryClient) => {
    return async (filters: BookingScreenFilters = {}) => {
        const query = bookingScreenQuery(filters)

        return (
            queryClient.getQueryData<BookingScreenResponse>(query.queryKey) ??
            (await client.fetchQuery(query))
        )
    }
}

// Export default loader
export default bookingScreenLoader
