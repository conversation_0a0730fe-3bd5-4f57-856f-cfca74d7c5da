import { QueryClient } from "@tanstack/react-query";

export interface TaskScreenFilters {
  limit?: number;
  offset?: number;
  sort_by?: string;
  sort_order?: "asc" | "desc";
  status?: string;
  priority?: string;
  entity_type?: string;
  assigned_to?: string;
  created_by?: string;
  q?: string;
  due_date_gte?: string;
  due_date_lte?: string;
  created_at_gte?: string;
  created_at_lte?: string;
}

export interface TaskScreenData {
  id: string;
  title: string;
  description?: string;
  status: "pending" | "in_progress" | "review" | "completed" | "cancelled";
  priority: "low" | "medium" | "high" | "urgent";
  entity_type?: string;
  entity_id?: string;
  assigned_to?: string;
  created_by?: string;
  updated_by?: string;
  due_date?: string;
  is_deleted: boolean;
  deleted_at?: string;
  created_at: string;
  updated_at: string;
  metadata?: Record<string, any>;
}

export interface TaskScreenResponse {
  tasks: TaskScreenData[];
  count: number;
  limit: number;
  offset: number;
}

/**
 * Loader function for the tasks screen
 * Fetches tasks with filtering, pagination, and sorting
 */
export const taskScreenLoader = (queryClient: QueryClient) => {
  return async (filters: TaskScreenFilters = {}): Promise<TaskScreenResponse> => {
    try {
      // Build query parameters
      const params = new URLSearchParams();
      
      // Pagination
      if (filters.limit) params.append("limit", filters.limit.toString());
      if (filters.offset) params.append("offset", filters.offset.toString());
      
      // Sorting
      if (filters.sort_by) params.append("order", filters.sort_by);
      if (filters.sort_order) params.append("sort_order", filters.sort_order);
      
      // Filters
      if (filters.status) params.append("status", filters.status);
      if (filters.priority) params.append("priority", filters.priority);
      if (filters.entity_type) params.append("entity_type", filters.entity_type);
      if (filters.assigned_to) params.append("assigned_to", filters.assigned_to);
      if (filters.created_by) params.append("created_by", filters.created_by);
      if (filters.q) params.append("q", filters.q);
      
      // Date filters
      if (filters.due_date_gte) params.append("due_date_gte", filters.due_date_gte);
      if (filters.due_date_lte) params.append("due_date_lte", filters.due_date_lte);
      if (filters.created_at_gte) params.append("created_at_gte", filters.created_at_gte);
      if (filters.created_at_lte) params.append("created_at_lte", filters.created_at_lte);

      const response = await fetch(`/admin/concierge-management/tasks?${params.toString()}`, {
        credentials: "include",
        headers: {
          "Content-Type": "application/json",
        },
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.message || `HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      
      return {
        tasks: data.tasks || [],
        count: data.count || 0,
        limit: data.limit || filters.limit || 20,
        offset: data.offset || filters.offset || 0,
      };
    } catch (error) {
      console.error("Error in taskScreenLoader:", error);
      throw error;
    }
  };
};
