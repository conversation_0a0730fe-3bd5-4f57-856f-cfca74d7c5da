import React, { useState, useEffect } from "react";
import {
  <PERSON>,
  Eye,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>py,
  Trash2,
} from "lucide-react";
import {
  Container,
  Heading,
  Text,
  Button,
  Badge,
  Toaster,
  toast,
  DropdownMenu,
  Prompt,
} from "@camped-ai/ui";
import { useTranslation } from "react-i18next";
import { useMemo } from "react";
import { Link, useNavigate, useLocation } from "react-router-dom";
import { useReactTable, getCoreRowModel, createColumnHelper, ColumnDef } from "@tanstack/react-table";
import { useRbac } from "../../../hooks/use-rbac";
import { DataTable } from "../../../../components/table/data-table";
import type { Filter } from "../../../../components/table/data-table";
import type { ItineraryScreenData } from "./loader";
import { format } from "date-fns";

// Status badge colors
const statusColors = {
  DRAFT: "orange",
  FINALIZED: "green",
} as const;

interface ItinerariesPageClientProps {
  itineraries: ItineraryScreenData[];
  isLoading: boolean;
  totalCount: number;
  pageSize: number;
}

const ItinerariesPageClient: React.FC<ItinerariesPageClientProps> = ({
  itineraries: initialItineraries,
  isLoading,
  totalCount,
  pageSize,
}) => {
  const navigate = useNavigate();
  const location = useLocation();
  const { t } = useTranslation();
  const { hasPermission } = useRbac();

  // Local state for itineraries (so we can remove items without refresh)
  const [itineraries, setItineraries] = useState<ItineraryScreenData[]>(initialItineraries);

  // State for delete confirmation modal
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [itineraryToDelete, setItineraryToDelete] = useState<ItineraryScreenData | null>(null);
  const [isDeleting, setIsDeleting] = useState(false);

  // Sync local state when initial itineraries change
  useEffect(() => {
    setItineraries(initialItineraries);
  }, [initialItineraries]);

  // Get current page from URL
  const searchParams = new URLSearchParams(location.search);

  // Column helper for type safety
  const columnHelper = createColumnHelper<ItineraryScreenData>();

  // Helper functions
  const getStatusBadgeVariant = (status: string) => {
    return statusColors[status as keyof typeof statusColors] || "grey";
  };

  const handleCopyItineraryId = (itineraryId: string) => {
    navigator.clipboard.writeText(itineraryId);
    toast.success("Itinerary ID copied to clipboard");
  };

  const handleViewItinerary = (itineraryId: string) => {
    navigate(`/concierge-management/itineraries/${itineraryId}/builder`);
  };

  const handleEditItinerary = (itineraryId: string) => {
    navigate(`/concierge-management/itineraries/${itineraryId}/builder`);
  };

  // Handle delete button click - show confirmation modal
  const handleDeleteClick = (itinerary: ItineraryScreenData) => {
    setItineraryToDelete(itinerary);
    setShowDeleteModal(true);
  };

  // Handle actual delete after confirmation
  const handleConfirmDelete = async () => {
    if (!itineraryToDelete) return;

    setIsDeleting(true);
    setShowDeleteModal(false);

    try {
      const response = await fetch(`/admin/concierge-management/itineraries/${itineraryToDelete.id}`, {
        method: "DELETE",
        headers: {
          "Content-Type": "application/json",
        },
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to delete itinerary");
      }

      toast.success("Itinerary deleted successfully");

      // Remove the deleted itinerary from the local state
      setItineraries(prevItineraries =>
        prevItineraries.filter(item => item.id !== itineraryToDelete.id)
      );

      // Reset states
      setItineraryToDelete(null);
      setIsDeleting(false);
    } catch (error: any) {
      console.error("Error deleting itinerary:", error);
      setIsDeleting(false); // Reset deleting state on error
      toast.error("Error", {
        description: error.message || "Failed to delete itinerary",
      });
    }
  };

  const navigateTo = (path: string) => {
    navigate(path);
  };

  // Define table columns
  const columns = useMemo<ColumnDef<ItineraryScreenData, any>[]>(() => [
    columnHelper.accessor("id", {
      header: "Itinerary ID",
      cell: ({ getValue }) => {
        const id = getValue();
        return (
          <div className="flex items-center gap-2">
            <Text className="font-mono text-sm">{id}</Text>
            <Button
              variant="transparent"
              size="small"
              onClick={() => handleCopyItineraryId(id)}
            >
              <Copy className="h-3 w-3" />
            </Button>
          </div>
        );
      },
    }),
    columnHelper.accessor("booking_id", {
      header: "Booking ID",
      cell: ({ getValue }) => {
        const bookingId = getValue();
        return (
          <Text className="font-mono text-sm">{bookingId}</Text>
        );
      },
    }),
    columnHelper.accessor("title", {
      header: "Title",
      cell: ({ getValue, row }) => {
        const title = getValue();
        const dayCount = row.original.days.length;
        return (
          <div>
            <Link
              to={`/concierge-management/itineraries/${row.original.id}/builder`}
              className="font-medium text-blue-600 hover:text-blue-800 hover:underline cursor-pointer"
            >
              {title || "Untitled Itinerary"}
            </Link>
            <Text className="text-sm text-gray-500">
              {dayCount} {dayCount === 1 ? "day" : "days"}
            </Text>
          </div>
        );
      },
    }),
    columnHelper.accessor("status", {
      header: "Status",
      cell: ({ getValue }) => {
        const status = getValue();
        return (
          <Badge color={getStatusBadgeVariant(status)}>
            {status}
          </Badge>
        );
      },
    }),
    columnHelper.accessor("created_by", {
      header: "Created By",
      cell: ({ getValue }) => {
        const createdBy = getValue();
        return (
          <Text className="text-sm">
            {createdBy || "System"}
          </Text>
        );
      },
    }),
    columnHelper.accessor("created_at", {
      header: "Created",
      cell: ({ getValue }) => {
        const createdAt = getValue();
        return (
          <div>
            <Text className="text-sm">
              {format(new Date(createdAt), "MMM d, yyyy")}
            </Text>
            <Text className="text-xs text-gray-500">
              {format(new Date(createdAt), "h:mm a")}
            </Text>
          </div>
        );
      },
    }),
    columnHelper.display({
      id: "actions",
      header: "Actions",
      cell: ({ row }) => {
        const itinerary = row.original;
        return (
          <DropdownMenu>
            <DropdownMenu.Trigger asChild>
              <Button variant="transparent" size="small">
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenu.Trigger>
            <DropdownMenu.Content>
              <DropdownMenu.Item onClick={(e) => {
                e.stopPropagation();
                handleViewItinerary(itinerary.id);
              }}>
                <Eye className="h-4 w-4 mr-2" />
                View Builder
              </DropdownMenu.Item>
              {/* {hasPermission("concierge_management:edit") && (
                <DropdownMenu.Item onClick={(e) => {
                  e.stopPropagation();
                  handleEditItinerary(itinerary.id);
                }}>
                  <Edit className="h-4 w-4 mr-2" />
                  Edit
                </DropdownMenu.Item>
              )} */}
              {hasPermission("concierge_management:delete") && (
                <>
                  <DropdownMenu.Separator />
                  <DropdownMenu.Item
                    onClick={(e) => {
                      e.stopPropagation();
                      handleDeleteClick(itinerary);
                    }}
                    className="text-red-600 hover:text-red-700 focus:text-red-700"
                  >
                    <Trash2 className="h-4 w-4 mr-2" />
                    Delete
                  </DropdownMenu.Item>
                </>
              )}
            </DropdownMenu.Content>
          </DropdownMenu>
        );
      },
    }),
  ], [hasPermission, navigate, handleDeleteClick]);

  // Define sortable columns
  const orderBy = useMemo(() => [
    { key: "booking_id" as keyof ItineraryScreenData, label: "Booking ID" },
    { key: "status" as keyof ItineraryScreenData, label: "Status" },
    { key: "created_at" as keyof ItineraryScreenData, label: "Created At" },
  ], []);

  // Define filters
  const filters: Filter[] = [
    {
      key: "status",
      label: "Status",
      type: "select",
      options: [
        { label: "All Statuses", value: "" },
        { label: "Draft", value: "DRAFT" },
        { label: "Finalized", value: "FINALIZED" },
      ],
    },
  ];

  console.log({ itineraries })

  // Create table instance
  const table = useReactTable({
    data: itineraries,
    columns,
    getCoreRowModel: getCoreRowModel(),
    manualPagination: true,
    manualSorting: true,
    manualFiltering: true,
  });

  return (
    <>
      <Toaster />
      <Container className="divide-y p-0">
        {/* Header */}
        <div className="flex items-center justify-between px-6 py-4">
          <div>
            <Heading level="h2">Concierge Itineraries</Heading>
          </div>
        </div>
        {/* Data Table */}
        <DataTable
          table={table}
          columns={columns}
          isLoading={isLoading}
          filters={filters}
          orderBy={orderBy}
          search="autofocus"
          pagination
          count={totalCount}
          pageSize={pageSize}
          queryObject={Object.fromEntries(searchParams)}

          noRecords={{
            title: "No itineraries found",
            message: "Get started by creating your first itinerary.",
          }}
        />
      </Container>

      {/* Delete Confirmation Modal */}
      <Prompt open={showDeleteModal} onOpenChange={setShowDeleteModal}>
        <Prompt.Content>
          <Prompt.Header>
            <Prompt.Title>Delete Itinerary</Prompt.Title>
            <Prompt.Description>
              Are you sure you want to delete "{itineraryToDelete?.title}"? This action cannot be undone and will remove all associated days, events, and media.
            </Prompt.Description>
          </Prompt.Header>
          <Prompt.Footer>
            <Prompt.Cancel onClick={() => setShowDeleteModal(false)}>
              Cancel
            </Prompt.Cancel>
            <Prompt.Action
              onClick={handleConfirmDelete}
              disabled={isDeleting}
            >
              {isDeleting ? "Deleting..." : "Delete"}
            </Prompt.Action>
          </Prompt.Footer>
        </Prompt.Content>
      </Prompt>
    </>
  );
};

export default ItinerariesPageClient;
