import React, { useState, useEffect } from "react";
import {
  Container,
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON>,
  Badge,
  Toaster,
  toast,
  DropdownMenu,
  Prompt,
} from "@camped-ai/ui";
import { ArrowLeft, Settings, MoreHorizontal, Eye, Trash2 } from "lucide-react";
import { useNavigate } from "react-router-dom";
import DaySidebar from "../../../../../components/concierge/itinerary/day-sidebar";
import EventPanel from "../../../../../components/concierge/itinerary/event-panel";
import Spinner from "../../../../../components/shared/spinner";

interface ItineraryBuilderProps {
  itineraryId?: string;
}

interface ItineraryData {
  id: string;
  booking_id: string;
  title?: string;
  status: "DRAFT" | "FINALIZED";
  created_by?: string;
  created_at: string;
  updated_at: string;
  days: Array<{
    id: string;
    itinerary_id: string;
    date: string;
    title?: string;
    sort_order: number;
    events: Array<{
      id: string;
      day_id: string;
      category: string;
      type?: string;
      title: string;
      notes?: string;
      start_time?: string;
      end_time?: string;
      duration?: string;
      timezone?: string;
      details?: Record<string, any>;
      price?: number;
      currency?: string;
      media?: string[];
      attachments?: string[];
      people?: string[];
    }>;
  }>;
}

const ItineraryBuilder: React.FC<ItineraryBuilderProps> = ({ itineraryId }) => {
  const navigate = useNavigate();
  const [itinerary, setItinerary] = useState<ItineraryData | null>(null);
  const [selectedDayId, setSelectedDayId] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [isGeneratingPreview, setIsGeneratingPreview] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [isDeleted, setIsDeleted] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);

  // Fetch itinerary data
  const fetchItinerary = async () => {
    if (!itineraryId) return;

    setIsLoading(true);
    try {
      console.log("Fetching itinerary data for ID:", itineraryId);
      const response = await fetch(`/admin/concierge-management/itineraries/${itineraryId}`, {
        credentials: "include",
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || "Failed to fetch itinerary");
      }

      const data = await response.json();
      console.log("Fetched itinerary data:", data);

      // Ensure data structure is valid
      const itinerary = data.itinerary || {};
      const days = Array.isArray(itinerary.days) ? itinerary.days : [];

      const normalizedItinerary = {
        ...itinerary,
        days: days,
      };

      console.log("Normalized itinerary with", days.length, "days");
      setItinerary(normalizedItinerary);

      // Select first day by default if no day is currently selected
      if (days.length > 0 && !selectedDayId) {
        setSelectedDayId(days[0].id);
      }
    } catch (error) {
      console.error("Error fetching itinerary:", error);
      toast.error("Failed to load itinerary");
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    // Don't fetch if we're in the process of deleting or already deleted
    if (!isDeleting && !isDeleted) {
      fetchItinerary();
    }
  }, [itineraryId, isDeleting, isDeleted]);

  // Handle day selection
  const handleDaySelect = (dayId: string) => {
    setSelectedDayId(dayId);
  };

  // Handle day updates
  const handleDayUpdate = () => {
    fetchItinerary(); // Refresh data
  };

  // Handle event updates
  const handleEventUpdate = () => {
    fetchItinerary(); // Refresh data
  };

  // Get selected day data
  const selectedDay = itinerary?.days.find(day => day.id === selectedDayId);

  // Handle back navigation
  const handleBack = () => {
    navigate("/concierge-management/itineraries");
  };

  // Handle preview generation
  const handlePreview = async () => {
    if (!itineraryId) {
      toast.error("No itinerary ID available");
      return;
    }

    setIsGeneratingPreview(true);
    try {
      console.log("🚀 Generating preview for itinerary:", itineraryId);

      const response = await fetch(`/admin/concierge-management/itineraries/${itineraryId}/preview`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          // Add any additional data needed for preview generation
        })
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      console.log("✅ Preview generated successfully:", data);

      if (data.success && data.data.preview_url) {
        toast.success("Preview generated! Redirecting to CRM...");

        // Redirect to the CRM preview page
        window.open(data.data.preview_url, '_blank');
      } else {
        throw new Error("No preview URL received from CRM");
      }

    } catch (error) {
      console.error("❌ Error generating preview:", error);
      toast.error(
        error instanceof Error
          ? `Failed to generate preview: ${error.message}`
          : "Failed to generate preview. Please try again."
      );
    } finally {
      setIsGeneratingPreview(false);
    }
  };

  // Handle save
  const handleSave = async () => {
    if (!itinerary) return;

    setIsSaving(true);
    try {
      // Auto-save is handled by individual components
      toast.success("Itinerary saved successfully");
    } catch (error) {
      console.error("Error saving itinerary:", error);
      toast.error("Failed to save itinerary");
    } finally {
      setIsSaving(false);
    }
  };

  // Handle delete button click - show confirmation modal
  const handleDeleteClick = () => {
    setShowDeleteModal(true);
  };

  // Handle actual delete after confirmation
  const handleConfirmDelete = async () => {
    if (!itinerary || isDeleting) return;

    setIsDeleting(true);
    setShowDeleteModal(false);

    try {
      console.log(`Deleting itinerary: ${itinerary.id}`);

      const response = await fetch(`/admin/concierge-management/itineraries/${itinerary.id}`, {
        method: "DELETE",
        headers: {
          "Content-Type": "application/json",
        },
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to delete itinerary");
      }

      const result = await response.json();
      console.log("Delete successful:", result);

      // Mark as deleted to prevent any further operations
      setIsDeleted(true);

      toast.success("Itinerary deleted successfully");

      console.log("About to navigate to /concierge-management/itineraries");

      // Force a complete page reload to clear any React Router state
      window.location.replace("/concierge-management/itineraries");

      console.log("Navigation called");

    } catch (error: any) {
      console.error("Error deleting itinerary:", error);
      setIsDeleting(false); // Reset deleting state on error
      toast.error("Error", {
        description: error.message || "Failed to delete itinerary",
      });
    }
  };

  // If deleted, show a brief message before redirect
  if (isDeleted) {
    return (
      <div className="flex items-center justify-center py-8">
        <Spinner size="medium" />
        <div className="ml-4">Itinerary deleted. Redirecting...</div>
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-8">
        <Spinner size="medium" />
        <div className="ml-4">Loading itinerary...</div>
      </div>
    );
  }

  if (!itinerary) {
    return (
      <div className="p-8 text-center">
        <Heading level="h1">Itinerary Not Found</Heading>
        <Text className="mt-2">
          The requested itinerary could not be found.
        </Text>
        <Button className="mt-4" onClick={handleBack}>
          Back to Itineraries
        </Button>
      </div>
    );
  }

  return (
    <>
      <Toaster />
      {/* Header with navigation and actions */}
      <Container className="flex items-center justify-between bg-white px-6 py-4">
        <div className="flex items-center gap-4">
          <Button variant="transparent" size="small" onClick={handleBack}>
            <ArrowLeft className="w-4 h-4 mr-2" />
          </Button>
          <div className="min-w-0" style={{ maxWidth: 180 }}>
            <Heading level="h1" className="truncate">{itinerary.title || "Cruise Itinerary"}</Heading>
          </div>
        </div>
        <div className="flex items-center gap-2">
          <Badge color={itinerary.status === "DRAFT" ? "orange" : "green"}>
            {itinerary.status}
          </Badge>
          <DropdownMenu>
            <DropdownMenu.Trigger asChild>
              <Button variant="transparent" size="small">
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenu.Trigger>
            <DropdownMenu.Content>
              <DropdownMenu.Item
                onClick={handleDeleteClick}
                disabled={isDeleting}
                className="text-red-600 hover:text-red-700 focus:text-red-700"
              >
                {isDeleting ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-red-600 mr-2"></div>
                    Deleting...
                  </>
                ) : (
                  <>
                    <Trash2 className="h-4 w-4 mr-2" />
                    Delete Itinerary
                  </>
                )}
              </DropdownMenu.Item>
            </DropdownMenu.Content>
          </DropdownMenu>
        </div>
      </Container>

      {/* Main Content */}
      <Container className="flex gap-4 p-0 bg-transparent shadow-none border-none pt-4" style={{ height: 'calc(100vh - 120px)' }}>
        {/* Left Sidebar - Days */}
        <Container className="w-80 bg-gray-50 overflow-y-auto p-0">
          <DaySidebar
            itinerary={itinerary}
            selectedDayId={selectedDayId}
            onDaySelect={handleDaySelect}
            onDayUpdate={handleDayUpdate}
          />
        </Container>

        {/* Main Panel - Events */}
        <Container className="flex-1 overflow-y-auto p-0">
          {selectedDay ? (
            <EventPanel
              day={selectedDay}
              onEventUpdate={handleEventUpdate}
            />
          ) : (
            <div className="p-8 text-center">
              <Text className="text-gray-500">
                Select a day to view and manage events
              </Text>
            </div>
          )}
        </Container>
      </Container>

      {/* Delete Confirmation Modal */}
      <Prompt open={showDeleteModal} onOpenChange={setShowDeleteModal}>
        <Prompt.Content>
          <Prompt.Header>
            <Prompt.Title>Delete Itinerary</Prompt.Title>
            <Prompt.Description>
              Are you sure you want to delete "{itinerary?.title}"? This action cannot be undone and will remove all associated days, events, and media.
            </Prompt.Description>
          </Prompt.Header>
          <Prompt.Footer>
            <Prompt.Cancel onClick={() => setShowDeleteModal(false)}>
              Cancel
            </Prompt.Cancel>
            <Prompt.Action
              onClick={handleConfirmDelete}
              disabled={isDeleting}
            >
              {isDeleting ? "Deleting..." : "Delete"}
            </Prompt.Action>
          </Prompt.Footer>
        </Prompt.Content>
      </Prompt>
    </>
  );
};

export default ItineraryBuilder;
