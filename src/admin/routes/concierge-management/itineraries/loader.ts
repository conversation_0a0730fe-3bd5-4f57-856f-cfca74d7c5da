import { QueryClient } from "@tanstack/react-query";

// Types for itinerary screen data
export interface ItineraryScreenFilters {
  limit?: number;
  offset?: number;
  booking_id?: string;
  status?: string;
  sort_by?: string;
  sort_order?: 'asc' | 'desc';
  created_by?: string;
  page?: number;
}

export interface ItineraryScreenData {
  id: string;
  booking_id: string;
  title?: string;
  status: "DRAFT" | "FINALIZED";
  created_by?: string;
  created_at: string;
  updated_at: string;
  days: Array<{
    id: string;
    date: string;
    title?: string;
    events: Array<{
      id: string;
      category: string;
      title: string;
    }>;
  }>;
}

export interface ItineraryScreenResponse {
  itineraries: ItineraryScreenData[];
  count: number;
  limit: number;
  offset: number;
}

/**
 * Loader function for the itinerary screen
 * Fetches itineraries with filtering, sorting, and pagination
 */
export const itineraryScreenLoader = (queryClient: QueryClient) => {
  return async (filters: ItineraryScreenFilters = {}): Promise<ItineraryScreenResponse> => {
    try {
      // Build query parameters
      const params = new URLSearchParams();
      
      if (filters.limit) params.append('limit', filters.limit.toString());
      if (filters.offset) params.append('offset', filters.offset.toString());
      if (filters.booking_id) params.append('booking_id', filters.booking_id);
      if (filters.status) params.append('status', filters.status);
      if (filters.created_by) params.append('created_by', filters.created_by);
      if (filters.sort_by) params.append('sort_by', filters.sort_by);
      if (filters.sort_order) params.append('sort_order', filters.sort_order);

      // Fetch itineraries from the concierge management API
      const response = await fetch(`/admin/concierge-management/itineraries?${params.toString()}`, {
        method: 'GET',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch itineraries: ${response.statusText}`);
      }

      const data = await response.json();
      
      return {
        itineraries: data.itineraries || [],
        count: data.count || 0,
        limit: filters.limit || 20,
        offset: filters.offset || 0,
      };
    } catch (error) {
      console.error('Error in itineraryScreenLoader:', error);
      throw error;
    }
  };
};

/**
 * Query key factory for itinerary-related queries
 */
export const itineraryQueryKeys = {
  all: ['itineraries'] as const,
  lists: () => [...itineraryQueryKeys.all, 'list'] as const,
  list: (filters: ItineraryScreenFilters) => [...itineraryQueryKeys.lists(), filters] as const,
  details: () => [...itineraryQueryKeys.all, 'detail'] as const,
  detail: (id: string) => [...itineraryQueryKeys.details(), id] as const,
};

/**
 * Prefetch itineraries for better performance
 */
export const prefetchItineraries = async (
  queryClient: QueryClient,
  filters: ItineraryScreenFilters = {}
) => {
  await queryClient.prefetchQuery({
    queryKey: itineraryQueryKeys.list(filters),
    queryFn: () => itineraryScreenLoader(queryClient)(filters),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

/**
 * Invalidate itinerary queries
 */
export const invalidateItineraryQueries = (queryClient: QueryClient) => {
  queryClient.invalidateQueries({
    queryKey: itineraryQueryKeys.all,
  });
};

/**
 * Get cached itinerary data
 */
export const getCachedItineraries = (
  queryClient: QueryClient,
  filters: ItineraryScreenFilters = {}
): ItineraryScreenResponse | undefined => {
  return queryClient.getQueryData(itineraryQueryKeys.list(filters));
};

/**
 * Set itinerary data in cache
 */
export const setCachedItineraries = (
  queryClient: QueryClient,
  filters: ItineraryScreenFilters,
  data: ItineraryScreenResponse
) => {
  queryClient.setQueryData(itineraryQueryKeys.list(filters), data);
};

export default itineraryScreenLoader;
