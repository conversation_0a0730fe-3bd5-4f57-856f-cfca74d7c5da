import React, { useState } from "react";
import { Container, <PERSON>ing, Text, Button, toast } from "@camped-ai/ui";
import { Sparkles } from "lucide-react";
import { DestinationData } from "../../../../../types";
import RichTextEditor from "../../../../../components/rich-text-editor";

interface BaileyAITabProps {
  destination: DestinationData | null;
  hasEditPermission: boolean;
}

const BaileyAITab: React.FC<BaileyAITabProps> = ({
  destination,
  hasEditPermission,
}) => {
  const [isSyncing, setIsSyncing] = useState(false);
  const [aiContent, setAiContent] = useState(destination?.ai_content || "");

  // Update local state when destination changes
  React.useEffect(() => {
    setAiContent(destination?.ai_content || "");
  }, [destination?.ai_content]);

  const handleSyncWithBaileyAI = async () => {
    if (!destination?.id) {
      console.error("Cannot sync with Bailey AI: destination ID is missing");
      return;
    }

    setIsSyncing(true);

    try {
      console.log("🤖 Syncing with <PERSON> AI for destination:", destination.id);

      // First, save current AI content to database
      if (aiContent !== destination.ai_content) {
        console.log("📝 Saving current AI content changes...");
        const saveResponse = await fetch(
          `/admin/hotel-management/destinations`,
          {
            method: "PUT",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify({
              id: destination.id,
              ai_content: aiContent,
            }),
          }
        );

        if (!saveResponse.ok) {
          const saveError = await saveResponse.json().catch(() => ({}));
          console.error("❌ Failed to save AI content:", saveError);
          throw new Error("Failed to save current AI content");
        }
      }

      // Use the dedicated Bailey AI sync endpoint
      const response = await fetch(
        `/admin/hotel-management/destinations/${destination.id}/bailey-ai-sync`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
        }
      );

      if (response.ok) {
        const result = await response.json();
        console.log("✅ Bailey AI sync successful:", result);

        toast.success("Success", {
          description: "Successfully synced with Bailey AI",
        });

        // Update the AI content if the API returns updated content
        if (result.destination?.ai_content) {
          setAiContent(result.destination.ai_content);
        }
      } else {
        const errorData = await response.json().catch(() => ({}));
        console.error("❌ Bailey AI sync failed:", response.status, errorData);

        // Check if it's a partial success (database updated but external API failed)
        if (response.status === 207 && errorData.destination) {
          console.log("⚠️ Database updated but external API failed");
          toast.error("Partial Success", {
            description:
              "Content saved but Bailey AI sync failed. Please try again.",
          });

          if (errorData.destination.ai_content) {
            setAiContent(errorData.destination.ai_content);
          }
        } else {
          toast.error("Error", {
            description:
              errorData.message ||
              "Failed to sync with Bailey AI. Please try again.",
          });
        }

        throw new Error(errorData.message || "Bailey AI sync failed");
      }
    } catch (error) {
      console.error("Error syncing with Bailey AI:", error);

      // Only show error toast if we haven't already shown one above
      if (!error.message?.includes("Bailey AI sync failed")) {
        toast.error("Error", {
          description: "An unexpected error occurred. Please try again.",
        });
      }
    } finally {
      setIsSyncing(false);
    }
  };

  if (!destination) {
    return (
      <Container className="p-6">
        <Text className="text-muted-foreground">
          Loading destination details...
        </Text>
      </Container>
    );
  }

  return (
    <Container className="divide-y p-0">
      <div className="flex items-center justify-between px-6 py-4">
        <div className="flex items-center gap-3">
          <Sparkles className="w-5 h-5 text-primary" />
          <Heading level="h3">Bailey AI Content</Heading>
        </div>

        {hasEditPermission && (
          <Button
            onClick={handleSyncWithBaileyAI}
            disabled={isSyncing || !destination.id}
            size="small"
            variant="primary"
            title={
              !destination.id
                ? "Save destination first to sync with Bailey AI"
                : "Sync destination content with Bailey AI"
            }
          >
            <Sparkles className="w-4 h-4 mr-2" />
            {isSyncing ? "Syncing..." : "Sync with Bailey AI"}
          </Button>
        )}
      </div>

      <div className="p-4">
        <RichTextEditor
          value={aiContent}
          onChange={setAiContent}
          placeholder="Enter destination details to guide Bailey AI's conversations…"
          height="400px"
          helpText="This content powers Bailey AI. Add rich, engaging details about the destination to enhance AI-guided conversations and personalized recommendations."
          disabled={!hasEditPermission}
        />

        {destination.id && (
          <div className="mt-4 p-3 bg-muted rounded-lg">
            <Text className="text-sm text-muted-foreground">
              💡 <strong>Tip:</strong> Use the "Sync with Bailey AI" button to
              update Bailey AI with the latest destination content and ensure
              optimal AI-powered conversations.
            </Text>
          </div>
        )}
      </div>
    </Container>
  );
};

export default BaileyAITab;
