import React, { useEffect, useMemo, useState } from "react";
import { Container, Text, Button, Input, Textarea, IconButton, toast } from "@camped-ai/ui";
import {
  HelpCircle,
  ChevronDown,
  ChevronUp,
  Plus,
  Edit,
  Trash2,
  X,
  Save
} from "lucide-react";
import { Heading } from "@camped-ai/ui";

interface FAQ {
  id?: string;
  question: string;
  answer: string;
}

interface FAQsTabProps {
  faqs: FAQ[];
  getTranslatedFaqs: () => FAQ[];
  destinationId?: string;
  hasEditPermission: boolean;
}

const FAQsTab: React.FC<FAQsTabProps> = ({
  faqs,
  getTranslatedFaqs,
  destinationId,
  hasEditPermission,
}) => {
  const [expandedFaqs, setExpandedFaqs] = useState<Set<string>>(new Set());
  const [isAdding, setIsAdding] = useState(false);
  const [newQuestion, setNewQuestion] = useState("");
  const [newAnswer, setNewAnswer] = useState("");
  const [editingId, setEditingId] = useState<string | null>(null);
  const [editQuestion, setEditQuestion] = useState("");
  const [editAnswer, setEditAnswer] = useState("");
  const [saving, setSaving] = useState(false);

  const canPersist = useMemo(() => Boolean(destinationId), [destinationId]);

  // Compute displayFaqs once per render from current props/translations
  const translatedFaqs = getTranslatedFaqs();
  const displayFaqs = translatedFaqs && translatedFaqs.length > 0 ? translatedFaqs : faqs;

  // Local mirror to ensure immediate UI reflect after save
  const [localFaqs, setLocalFaqs] = useState<FAQ[]>(displayFaqs);

  // Normalize FAQ array to avoid duplicates and ensure stable keys
  const normalizeFaqs = (arr: FAQ[]): FAQ[] => {
    if (!Array.isArray(arr)) return [];
    const seen = new Set<string>();
    const result: FAQ[] = [];
    arr.forEach((item, idx) => {
      const id = (item && item.id) ? String(item.id) : `idx-${idx}`;
      if (!seen.has(id)) {
        seen.add(id);
        result.push({
          id: item?.id ?? id,
          question: item?.question ?? "",
          answer: item?.answer ?? "",
        });
      }
    });
    return result;
  };

  // Keep localFaqs in sync when upstream props/translations change.
  // Use deep compare on content to prevent duplicate merge artifacts on reload.
  useEffect(() => {
    const normalized = normalizeFaqs(displayFaqs);
    // Only update if content differs (length or stringified mismatch)
    const differ =
      normalized.length !== localFaqs.length ||
      JSON.stringify(normalized) !== JSON.stringify(localFaqs);
    if (differ) {
      setLocalFaqs(normalized);
      // Reset edit/expanded state to avoid cross-list bleed/grouping
      setEditingId(null);
      setExpandedFaqs(new Set());
    }
  }, [destinationId, JSON.stringify(displayFaqs)]);

  const toggleFaq = (faqId: string) => {
    const newExpanded = new Set(expandedFaqs);
    if (newExpanded.has(faqId)) {
      newExpanded.delete(faqId);
    } else {
      newExpanded.add(faqId);
    }
    setExpandedFaqs(newExpanded);
  };

  const persistFaqs = async (updatedFaqs: FAQ[]) => {
    // Normalize, de-dup and update local state first for immediate UI feedback
    const normalized = normalizeFaqs(updatedFaqs);
    setLocalFaqs(normalized);
    if (!canPersist) return;

    try {
      setSaving(true);
      const res = await fetch(`/admin/hotel-management/destinations`, {
        method: "PUT",
        credentials: "include",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          id: destinationId,
          faqs: updatedFaqs,
        }),
      });

      if (!res.ok) {
        const data = await res.json().catch(() => ({}));
        throw new Error(data.message || "Failed to update FAQs");
      }

      // Ensure we reflect server-truth immediately after create/delete/edit
      const refreshed = await res.json().catch(() => null);
      // Some backends return updated record; if not, fallback to fetch
      if (refreshed && (refreshed.destination || refreshed.faqs)) {
        const serverFaqs =
          refreshed.faqs ||
          refreshed.destination?.faqs ||
          updatedFaqs;
        setLocalFaqs(serverFaqs);
      } else if (destinationId) {
        const getRes = await fetch(
          `/admin/hotel-management/destinations/${destinationId}`,
          { credentials: "include" }
        );
        if (getRes.ok) {
          const getData = await getRes.json().catch(() => ({}));
          const destData = Array.isArray(getData?.destination)
            ? getData.destination[0]
            : getData.destination;
          if (destData?.faqs) {
            setLocalFaqs(destData.faqs);
          }
        }
      }

      toast.success("FAQs updated");
    } catch (e: any) {
      console.error("Failed to persist FAQs", e);
      toast.error(e?.message || "Failed to update FAQs");
      throw e;
    } finally {
      setSaving(false);
    }
  };

  const handleAdd = async () => {
    if (!hasEditPermission) return;
    if (!newQuestion.trim() || !newAnswer.trim()) {
      toast.error("Please provide both question and answer");
      return;
    }
    const base = localFaqs ?? [];
    const updated = [...base, { question: newQuestion.trim(), answer: newAnswer.trim() }];
    try {
      await persistFaqs(updated);
      setNewQuestion("");
      setNewAnswer("");
      setIsAdding(false);
    } catch {}
  };

  const startEdit = (faq: FAQ, id: string) => {
    if (!hasEditPermission) return;
    // Ensure the item is expanded when entering edit mode
    const newExpanded = new Set(expandedFaqs);
    newExpanded.add(id);
    setExpandedFaqs(newExpanded);

    setEditingId(id);
    setEditQuestion(faq.question);
    setEditAnswer(faq.answer);
  };

  const cancelEdit = () => {
    setEditingId(null);
    setEditQuestion("");
    setEditAnswer("");
  };

  const handleSaveEdit = async (id: string) => {
    if (!hasEditPermission) return;
    if (!editQuestion.trim() || !editAnswer.trim()) {
      toast.error("Please provide both question and answer");
      return;
    }
    const base = localFaqs ?? [];
    const updated = base.map((f, idx) => {
      const fid = f.id ?? String(idx);
      if (fid === id) {
        return { ...f, question: editQuestion.trim(), answer: editAnswer.trim() };
      }
      return f;
    });
    try {
      await persistFaqs(updated);
      cancelEdit();
    } catch {}
  };

  const handleDelete = async (id: string) => {
    if (!hasEditPermission) return;
    const base = localFaqs ?? [];
    const updated = base.filter((f, idx) => {
      const fid = f.id ?? String(idx);
      return fid !== id;
    });
    try {
      await persistFaqs(updated);
    } catch {}
  };

  return (
    <Container className="divide-y p-0">
      <div className="flex items-center justify-between px-6 py-4">
        <div className="flex items-center gap-3">
          <Heading level="h2" className="text-lg font-medium text-foreground">
            FAQs
          </Heading>
        </div>
        {hasEditPermission && (
          <Button
            variant="secondary"
            size="small"
            onClick={() => setIsAdding((v) => !v)}
            className="flex items-center gap-1"
            disabled={!canPersist || saving}
          >
            <Plus className="w-4 h-4" />
            {isAdding ? "Close" : "Add FAQ"}
          </Button>
        )}
      </div>

      <div className="px-6">
        {/* Add form */}
        {hasEditPermission && isAdding && (
          <div className="my-6 border border-border rounded-lg p-4 bg-card">
            <div className="flex flex-col gap-3">
              <Input
                placeholder="e.g., What is the best time to visit this destination?"
                value={newQuestion}
                onChange={(e) => setNewQuestion(e.target.value)}
                disabled={saving}
              />
              <Textarea
                placeholder="Write a clear and concise answer. e.g., The best time to visit is between March and June when the weather is pleasant."
                value={newAnswer}
                onChange={(e) => setNewAnswer(e.target.value)}
                rows={4}
                disabled={saving}
              />
              <div className="flex items-center gap-2">
                <Button variant="primary" onClick={handleAdd} disabled={!canPersist || saving}>
                  <Save className="w-4 h-4 mr-1" />
                  Save
                </Button>
                <Button variant="transparent" onClick={() => setIsAdding(false)} disabled={saving}>
                  <X className="w-4 h-4 mr-1" />
                  Cancel
                </Button>
              </div>
            </div>
          </div>
        )}
        {(localFaqs && localFaqs.length > 0) ? (
          <div className="space-y-4 py-6">
            {localFaqs.map((faq: FAQ, index: number) => {
              // Use stable key based on id, fallback to idx-N
              const faqId = faq.id ? String(faq.id) : `idx-${index}`;
              const isExpanded = expandedFaqs.has(faqId);

              return (
                <div
                  key={faqId}
                  className="border border-border rounded-lg bg-card hover:border-muted-foreground/20 transition-colors"
                >
                  <button
                    onClick={() => {
                      // Disable toggle when editing this row
                      if (editingId === faqId) return;
                      toggleFaq(faqId);
                    }}
                    className={`w-full flex items-center justify-between p-4 text-left transition-colors rounded-t-lg ${editingId === faqId ? "bg-muted/60 cursor-default" : "hover:bg-muted/50"}`}
                  >
                    <Text className="font-medium text-foreground pr-4 w-full">
                      {editingId === faqId ? (
                        <div className="w-full">
                          <Input
                            placeholder="e.g., What is the best time to visit this destination?"
                            value={editQuestion}
                            onChange={(e) => setEditQuestion(e.target.value)}
                            onClick={(e) => e.stopPropagation()}
                            disabled={saving}
                          />
                        </div>
                      ) : (
                        faq.question
                      )}
                    </Text>
                    {/* Hide edit/delete and chevrons while in edit mode for this item */}
                    {editingId !== faqId && (
                      <div className="flex items-center gap-2">
                        {hasEditPermission && (
                          <div
                            className="flex gap-1"
                            onClick={(e) => e.stopPropagation()}
                          >
                            <Button
                              variant="transparent"
                              size="small"
                              onClick={() => startEdit(faq, faqId)}
                              className="p-1 h-auto"
                              disabled={saving}
                            >
                              <Edit className="w-3 h-3" />
                            </Button>
                            <Button
                              variant="transparent"
                              size="small"
                              onClick={() => handleDelete(faqId)}
                              className="p-1 h-auto text-red-600 hover:text-red-700"
                              disabled={saving}
                            >
                              <Trash2 className="w-3 h-3" />
                            </Button>
                          </div>
                        )}
                        {isExpanded ? (
                          <ChevronUp className="w-4 h-4 text-muted-foreground flex-shrink-0" />
                        ) : (
                          <ChevronDown className="w-4 h-4 text-muted-foreground flex-shrink-0" />
                        )}
                      </div>
                    )}
                  </button>

                  {isExpanded && (
                    <div className={`px-4 pb-4 border-t border-border ${editingId === faqId ? "bg-muted/40 rounded-b-lg" : ""}`}>
                      {editingId === faqId ? (
                        <div className="flex flex-col gap-3 mt-3">
                          {/* Edit block styled similar to Add form with light background */}
                          <div className="flex flex-col gap-3">
                            <Textarea
                              placeholder="Write a clear and concise answer. e.g., The best time to visit is between March and June when the weather is pleasant."
                              value={editAnswer}
                              onChange={(e) => setEditAnswer(e.target.value)}
                              rows={4}
                              disabled={saving}
                            />
                            <div className="flex items-center gap-2">
                              <Button
                                variant="primary"
                                onClick={() => handleSaveEdit(faqId)}
                                disabled={saving}
                              >
                                <Save className="w-4 h-4 mr-1" />
                                Save
                              </Button>
                              <Button variant="transparent" onClick={cancelEdit} disabled={saving}>
                                <X className="w-4 h-4 mr-1" />
                                Cancel
                              </Button>
                            </div>
                          </div>
                        </div>
                      ) : (
                        <div className="mt-3">
                          <Text className="text-sm text-muted-foreground leading-relaxed">
                            <div
                              dangerouslySetInnerHTML={{
                                __html: faq.answer,
                              }}
                            />
                          </Text>
                        </div>
                      )}
                    </div>
                  )}
                </div>
              );
            })}
          </div>
        ) : (
          <div className="text-center py-12">
            <HelpCircle className="w-12 h-12 text-muted-foreground mx-auto mb-3" />
            <h3 className="text-lg font-medium text-foreground mb-1">
              No FAQs available
            </h3>
            <p className="text-muted-foreground text-sm mb-4">
              Add frequently asked questions to help visitors learn more about
              this destination.
            </p>
            {hasEditPermission && (
              <Button
                variant="secondary"
                onClick={() => setIsAdding(true)}
                className="flex items-center gap-2 mx-auto"
                disabled={!canPersist || saving}
              >
                <Plus className="w-4 h-4" />
                Add First FAQ
              </Button>
            )}
          </div>
        )}
      </div>
    </Container>
  );
};

export default FAQsTab;
