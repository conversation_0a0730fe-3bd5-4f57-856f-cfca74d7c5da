import React, { useMemo, useRef, useState } from "react";
import { Container, Heading, Text, <PERSON><PERSON>, Badge, Drawer, toast } from "@camped-ai/ui";
import { Image as ImageIcon, Upload, X, Star, GripVertical } from "lucide-react";
import OptimizedImageCarousel from "../../../../../components/hotel/optimized-image-carousel";
import type { DestinationData } from "../../../../../types";

interface MediaTabProps {
  destination: DestinationData | null;
  hasEditPermission?: boolean;
}

type MediaItem = {
  id?: string;
  url?: string;
  isThumbnail?: boolean;
};

const DestinationMediaTab: React.FC<MediaTabProps> = ({ destination, hasEditPermission = false }) => {
  const [isUploading, setIsUploading] = useState(false);
  const [drawerOpen, setDrawerOpen] = useState(false);
  const [localImages, setLocalImages] = useState<MediaItem[]>(() => {
    const imgs = (destination as any)?.images || [];
    return Array.isArray(imgs)
      ? imgs.map((img: any) => ({
          id: img.id,
          url: img.url,
          isThumbnail: !!img.isThumbnail,
        }))
      : [];
  });

  const dropRef = useRef<HTMLDivElement | null>(null);
  const images: MediaItem[] = useMemo(() => localImages, [localImages]);
  const thumbnailCount = images.filter((i) => i.isThumbnail).length;

  const refreshImages = async () => {
    if (!destination?.id) return;
    try {
      const res = await fetch(`/admin/hotel-management/destinations/${destination.id}/images`, {
        credentials: "include",
      });
      if (!res.ok) return;
      const data = await res.json();
      const imgs = (data?.images || []).map((img: any) => ({
        id: img.id,
        url: img.url,
        isThumbnail: !!img.isThumbnail,
      }));
      setLocalImages(imgs);
    } catch {
      // no-op
    }
  };

  const handleFilesSelected = async (files: FileList | null) => {
    if (!destination?.id || !files || files.length === 0) return;
    try {
      setIsUploading(true);
      const formData = new FormData();
      Array.from(files).forEach((file) => formData.append("files", file));

      const response = await fetch(`/admin/hotel-management/destinations/${destination.id}/upload`, {
        method: "POST",
        body: formData,
        credentials: "include",
      });

      if (!response.ok) {
        const text = await response.text();
        throw new Error(text || "Upload failed");
      }

      // Only show persisted images with server IDs
      let uploadedArray: any[] = [];
      try {
        const payload = await response.json();
        uploadedArray = Array.isArray(payload)
          ? payload
          : Array.isArray((payload as any)?.files)
          ? (payload as any).files
          : Array.isArray((payload as any)?.images)
          ? (payload as any).images
          : [];
      } catch {
        uploadedArray = [];
      }

      const serverImages = uploadedArray
        .map((u: any) => ({
          id: u.id || u.image_id,
          url: u.url,
          isThumbnail: !!(u.isThumbnail ?? u.is_thumbnail ?? u.metadata?.isThumbnail),
        }))
        .filter((u: any) => u.id && u.url);

      if (serverImages.length > 0) {
        setLocalImages((prev) => [...serverImages, ...prev]);
      } else {
        // Fallback to refresh endpoint to hydrate list
        await refreshImages();
      }

      toast.success("Success", { description: "Images uploaded" });
    } catch (e: any) {
      console.error("Upload error", e);
      toast.error("Error", { description: e?.message || "Failed to upload" });
    } finally {
      setIsUploading(false);
    }
  };

  const handleMakeThumbnail = async (imageId?: string) => {
    if (!imageId || !destination?.id) return;
    try {
      const res = await fetch(`/admin/hotel-management/destinations/${destination.id}/thumbnail`, {
        method: "POST",
        credentials: "include",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ image_id: imageId }),
      });
      if (!res.ok) {
        const text = await res.text().catch(() => "");
        throw new Error(text || "Failed to set thumbnail");
      }
      // Update local state
      setLocalImages((prev) => prev.map((img) => ({ ...img, isThumbnail: img.id === imageId })));
      toast.success("Updated", { description: "Thumbnail updated" });
    } catch (e: any) {
      console.error("Make thumbnail failed:", e);
      toast.error("Error", { description: e?.message || "Could not set thumbnail" });
    }
  };

  const handleDeleteImage = async (imageId?: string) => {
    if (!imageId) return;
    try {
      const res = await fetch(`/admin/hotel-management/destinations/images/${imageId}`, {
        method: "DELETE",
        credentials: "include",
      });
      if (!res.ok) {
        const text = await res.text().catch(() => "");
        throw new Error(text || "Failed to delete image");
      }
      setLocalImages((prev) => prev.filter((img) => img.id !== imageId));
      toast.success("Deleted", { description: "Image removed" });
    } catch (e: any) {
      console.error("Delete image failed:", e);
      toast.error("Error", { description: e?.message || "Could not delete image" });
    }
  };

  return (
    <Container className="divide-y p-0">
      {/* Header */}
      <div className="px-6 py-4 flex items-center justify-between">
        <div className="flex items-center gap-3">
          <ImageIcon className="w-5 h-5 text-muted-foreground" />
          <Heading level="h2" className="text-lg font-medium text-foreground">
            Media
          </Heading>
          {images.length > 0 && (
            <Badge className="text-xs">{images.length} {images.length === 1 ? "image" : "images"}</Badge>
          )}
          {thumbnailCount > 0 && (
            <Badge className="text-xs" color="purple">{thumbnailCount} thumbnail</Badge>
          )}
        </div>

        <div className="flex items-center gap-2">
          {images.length > 0 && (
            <Button variant="secondary" size="small" onClick={() => setDrawerOpen(true)}>
              <ImageIcon className="w-4 h-4 mr-1" />
              View Gallery
            </Button>
          )}
          {hasEditPermission && (
            <label className="inline-flex items-center">
              <input
                type="file"
                accept="image/*"
                multiple
                onChange={(e) => handleFilesSelected(e.target.files)}
                className="hidden"
                disabled={isUploading}
              />
              <span
                className={`inline-flex items-center gap-2 px-3 py-1.5 rounded-md text-sm border ${
                  isUploading ? "opacity-60 cursor-not-allowed" : "cursor-pointer"
                }`}
              >
                <Upload className="w-4 h-4" />
                {isUploading ? "Uploading..." : "Upload"}
              </span>
            </label>
          )}
        </div>
      </div>

      {/* Content */}
      <div className="p-6">
        <div className="mb-6">
          <Text className="text-sm text-muted-foreground">
            Upload images that showcase this destination. The first image or the one marked as thumbnail will be used as the main image.
          </Text>
          <div
            ref={dropRef}
            onDragOver={(e) => {
              e.preventDefault();
              dropRef.current?.classList.add("border-blue-400");
            }}
            onDragLeave={() => dropRef.current?.classList.remove("border-blue-400")}
            onDrop={(e) => {
              e.preventDefault();
              dropRef.current?.classList.remove("border-blue-400");
              const dt = e.dataTransfer;
              if (dt?.files?.length) {
                handleFilesSelected(dt.files);
              }
            }}
            className="mt-3 border-2 border-dashed border-border rounded-lg p-10 text-center bg-muted/30"
          >
            <Upload className="w-6 h-6 mx-auto mb-2 text-muted-foreground" />
            <div className="text-sm font-medium">Upload destination images</div>
            <div className="text-sm text-muted-foreground">
              Drag & drop images here, or{" "}
              <label className="underline cursor-pointer">
                browse files
                <input
                  type="file"
                  accept="image/*"
                  multiple
                  className="hidden"
                  onChange={(e) => handleFilesSelected(e.target.files)}
                  disabled={isUploading}
                />
              </label>
            </div>
          </div>
          <div className="text-right text-xs text-muted-foreground mt-2">
            Drag to reorder • First image is the thumbnail
          </div>
        </div>

        {images.length === 0 ? (
          <div className="text-center py-12">
            <div className="w-16 h-16 rounded-full bg-muted flex items-center justify-center mx-auto mb-4">
              <ImageIcon className="w-8 h-8 text-muted-foreground" />
            </div>
            <Heading level="h3" className="text-lg font-medium text-foreground mb-2">
              No Images
            </Heading>
            <Text className="text-muted-foreground max-w-md mx-auto">
              {hasEditPermission
                ? "You can upload images for this destination using the Upload button."
                : "No images available for this destination."}
            </Text>
          </div>
        ) : (
          <div className="space-y-2">
            {images.map((img, index) => (
              <div
                key={img.id || img.url || index}
                className="flex items-center gap-3 p-3 rounded-lg border border-border bg-card"
                draggable={hasEditPermission}
                onDragStart={(e) => {
                  if (!hasEditPermission) return;
                  e.dataTransfer.setData("text/plain", String(index));
                }}
                onDragOver={(e) => {
                  if (!hasEditPermission) return;
                  e.preventDefault();
                }}
                onDrop={(e) => {
                  if (!hasEditPermission) return;
                  e.preventDefault();
                  const fromIndex = Number(e.dataTransfer.getData("text/plain"));
                  const toIndex = index;
                  if (Number.isNaN(fromIndex)) return;
                  setLocalImages((prev) => {
                    const arr = [...prev];
                    const [moved] = arr.splice(fromIndex, 1);
                    arr.splice(toIndex, 0, moved);
                    return arr;
                  });
                }}
              >
                <GripVertical className="w-4 h-4 text-muted-foreground" />
                <img src={img.url} alt="Destination" className="w-10 h-10 rounded-md object-cover" />
                <div className="truncate flex-1 text-sm">{img.id || img.url}</div>
                {img.isThumbnail && (
                  <Badge className="text-xs" color="purple">
                    Thumbnail
                  </Badge>
                )}
                {hasEditPermission && (
                  <div className="flex items-center gap-2">
                    {!img.isThumbnail && (
                      <Button size="small" variant="secondary" onClick={() => handleMakeThumbnail(img.id)}>
                        <Star className="w-4 h-4 mr-1" />
                        Make Thumbnail
                      </Button>
                    )}
                    <Button size="small" variant="danger" onClick={() => handleDeleteImage(img.id)}>
                      <X className="w-4 h-4 mr-1" />
                      Delete
                    </Button>
                  </div>
                )}
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Gallery Drawer */}
      <Drawer open={drawerOpen} onOpenChange={setDrawerOpen}>
        <Drawer.Content className="max-w-6xl mx-auto">
          {images.length > 0 ? (
            <OptimizedImageCarousel images={images as any} onClose={() => setDrawerOpen(false)} hotelName={(destination as any)?.name || ""} />
          ) : (
            <div className="p-8">
              <div className="text-center py-12">
                <ImageIcon className="w-16 h-16 text-muted-foreground mx-auto mb-4" />
                <Text className="text-muted-foreground text-lg">No images available</Text>
              </div>
            </div>
          )}
        </Drawer.Content>
      </Drawer>
    </Container>
  );
};

export default DestinationMediaTab;
