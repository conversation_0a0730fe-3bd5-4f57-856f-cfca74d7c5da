import React, { useState } from "react";
import { use<PERSON><PERSON>, Controller } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { Save, X } from "lucide-react";
import {
  Container,
  Heading,
  Text,
  Button,
  Input,
  Textarea,
  Label,
  toast,
  Badge,
  InlineTip,
} from "@camped-ai/ui";
import { CountrySelector } from "../../../../components/common/country-selector";
import { CurrencySelector } from "../../../../components/common/currency-selector";
import { DestinationFaqData } from "../../../../components/destination-form";
import VisibilitySettings from "../../../../components/visibility-settings";
import DestinationMediaSection from "../../../../components/destination/destination-media-section";
import LanguageSelector from "../../../../components/language-selector";
import { TextareaField } from "../../../../components/ai-enhanced-inputs";
import { useProjectLanguages } from "../../../../hooks/languages/useProjectLanguages";
import { isValidUrl, normalizeUrl } from "../../../../../utils/url-validation";

// Form validation schema
const destinationSchema = z.object({
  // Basic Info
  name: z.string().min(1, "Name is required"),
  handle: z.string().min(1, "Handle is required"),
  description: z.string().optional(),

  // Status
  is_active: z.boolean().default(true),
  is_featured: z.boolean().default(false),

  // Location
  country: z.string().min(1, "Country is required"),
  location: z.string().optional(),

  // Additional Info
  tags: z.array(z.string()).optional(),
  website: z
    .string()
    .optional()
    .refine((url) => !url || isValidUrl(url), {
      message:
        "Must be a valid URL (e.g., https://example.com or www.example.com)",
    }),

  // New fields for currency/margin enhancement
  internal_web_link: z
    .string()
    .optional()
    .refine((url) => !url || isValidUrl(url), {
      message:
        "Must be a valid URL (e.g., https://example.com or www.example.com)",
    }),
  external_web_link: z
    .string()
    .optional()
    .refine((url) => !url || isValidUrl(url), {
      message:
        "Must be a valid URL (e.g., https://example.com or www.example.com)",
    }),
  currency_code: z.string().optional(),
  margin: z.number().min(0).max(100).optional(),

  // Media and FAQs
  media: z.array(z.any()).optional(),
  faqs: z
    .array(
      z.object({
        question: z.string().min(1, "Question is required"),
        answer: z.string().min(1, "Answer is required"),
      })
    )
    .optional(),
});

type DestinationFormData = z.infer<typeof destinationSchema>;

interface DestinationCreateFormProps {
  onSubmit: (
    data: DestinationFormData
  ) => Promise<{ destination_id: string } | undefined>;
  onCancel: () => void;
  isSubmitting?: boolean;
}

const DestinationCreateForm: React.FC<DestinationCreateFormProps> = ({
  onSubmit,
  onCancel,
  isSubmitting = false,
}) => {
  const [faqs, setFaqs] = useState<DestinationFaqData[]>([]);
  const [tags, setTags] = useState<string[]>([]);
  const [tagInput, setTagInput] = useState("");
  const [selectedLanguage, setSelectedLanguage] = useState("en");

  // Language management
  const { languages: tolgeeLanguages } = useProjectLanguages();
  const isBaseLanguage = selectedLanguage === "en";

  // Form setup
  const form = useForm<DestinationFormData>({
    resolver: zodResolver(destinationSchema),
    defaultValues: {
      name: "",
      handle: "",
      description: "",
      is_active: true,
      is_featured: false,
      country: "",
      location: "",
      tags: [],
      website: "",
      internal_web_link: "",
      external_web_link: "",
      currency_code: "GBP",
      margin: undefined,
      media: [],
      faqs: [],
    },
  });

  const {
    handleSubmit,
    control,
    watch,
    setValue,
    formState: { errors },
  } = form;

  // Watch name field to auto-generate handle
  const nameValue = watch("name");
  React.useEffect(() => {
    if (nameValue) {
      const handle = nameValue
        .toLowerCase()
        .replace(/[^a-z0-9\s-]/g, "")
        .replace(/\s+/g, "-")
        .replace(/-+/g, "-")
        .trim();
      setValue("handle", handle);
    }
  }, [nameValue, setValue]);

  // Tag input helper functions
  const addTag = (tag: string) => {
    const trimmedTag = tag.trim();
    if (trimmedTag && !tags.includes(trimmedTag)) {
      const newTags = [...tags, trimmedTag];
      setTags(newTags);
      setValue("tags", newTags);
      setTagInput("");
    }
  };

  const removeTag = (tagToRemove: string) => {
    const newTags = tags.filter((tag) => tag !== tagToRemove);
    setTags(newTags);
    setValue("tags", newTags);
  };

  const handleTagInputKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === "Enter") {
      e.preventDefault();
      addTag(tagInput);
    } else if (e.key === "Backspace" && tagInput === "" && tags.length > 0) {
      removeTag(tags[tags.length - 1]);
    }
  };

  const handleTagInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    if (value.includes(",")) {
      const newTags = value
        .split(",")
        .map((tag) => tag.trim())
        .filter((tag) => tag);
      newTags.forEach((tag) => {
        if (!tags.includes(tag)) {
          addTag(tag);
        }
      });
      setTagInput("");
    } else {
      setTagInput(value);
    }
  };

  // Form submission
  const handleFormSubmit = async (data: DestinationFormData) => {
    try {
      // Normalize URLs
      if (data.website) {
        data.website = normalizeUrl(data.website);
      }
      if (data.internal_web_link) {
        data.internal_web_link = normalizeUrl(data.internal_web_link);
      }
      if (data.external_web_link) {
        data.external_web_link = normalizeUrl(data.external_web_link);
      }

      // Include FAQs in submission data
      data.faqs = faqs;

      await onSubmit(data);
    } catch (error: any) {
      console.error("Error in form submission:", error);
      toast.error(error.message || "Failed to create destination");
    }
  };

  return (
    <div className="space-y-3">
      {/* Form Header with Language Selector */}
      <div className="flex items-center justify-between">
        <div>
          <Heading level="h1">Add New Destination</Heading>
        </div>
        <LanguageSelector
          selectedLanguage={selectedLanguage}
          onLanguageChange={setSelectedLanguage}
        />
      </div>

      {/* Translation Mode Banner */}
      {!isBaseLanguage && (
        <InlineTip label="Translation Mode">
          You are editing in{" "}
          <strong>
            {tolgeeLanguages.find((lang) => lang.tag === selectedLanguage)?.name || selectedLanguage}
          </strong>{" "}
          language. Only translatable fields (name, description, location, tags, FAQs) can be edited.
          Other fields show base language values and are read-only.
        </InlineTip>
      )}

      {/* Destination Information */}
      <Container className="divide-y p-0">
        <div className="flex items-center justify-between px-6 py-4">
          <Heading level="h2">Destination Information</Heading>
        </div>
        <div className="px-6 py-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="space-y-2">
              <Label>Destination Name *</Label>
              <Controller
                name="name"
                control={control}
                render={({ field }) => (
                  <Input {...field} placeholder="Enter destination name" />
                )}
              />
              {errors.name && (
                <Text className="text-red-600 text-sm mt-1">
                  {errors.name.message}
                </Text>
              )}
            </div>

            <div className="space-y-2">
              <Label>URL Handle *</Label>
              <Controller
                name="handle"
                control={control}
                render={({ field }) => (
                  <Input {...field} placeholder="destination-handle" />
                )}
              />
              {errors.handle && (
                <Text className="text-red-600 text-sm mt-1">
                  {errors.handle.message}
                </Text>
              )}
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-4">
            <div className="space-y-2">
              <Label>Country *</Label>
              <Controller
                name="country"
                control={control}
                render={({ field }) => (
                  <CountrySelector
                    value={field.value}
                    onChange={field.onChange}
                  />
                )}
              />
              {errors.country && (
                <Text className="text-red-600 text-sm mt-1">
                  {errors.country.message}
                </Text>
              )}
            </div>
            <div className="space-y-2">
              <Label>Specific Location</Label>
              <Controller
                name="location"
                control={control}
                render={({ field }) => (
                  <Input
                    {...field}
                    placeholder="Enter specific location"
                    value={field.value || ""}
                  />
                )}
              />
              {errors.location && (
                <Text className="text-red-600 text-sm mt-1">
                  {errors.location.message}
                </Text>
              )}
            </div>
          </div>

          <div className="mt-4 space-y-2">
            <Controller
              name="description"
              control={control}
              render={({ field }) => (
                <TextareaField
                  label="Description"
                  value={field.value || ""}
                  onChange={field.onChange}
                  placeholder="Enter destination description"
                  contentType="description"
                  context={{
                    name: watch("name"),
                    country: watch("country"),
                    location: watch("location"),
                    type: "destination"
                  }}
                  rows={4}
                />
              )}
            />
          </div>
        </div>
      </Container>

      {/* Web Links */}
      <Container className="divide-y p-0">
        <div className="flex items-center justify-between px-6 py-4">
          <Heading level="h2">Web Links</Heading>
        </div>
        <div className="px-6 py-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="space-y-2">
              <Label>Internal Web Link</Label>
              <Controller
                name="internal_web_link"
                control={control}
                render={({ field }) => (
                  <Input
                    {...field}
                    placeholder="https://internal.example.com"
                    value={field.value || ""}
                  />
                )}
              />
              {errors.internal_web_link && (
                <Text className="text-red-600 text-sm mt-1">
                  {errors.internal_web_link.message}
                </Text>
              )}
            </div>

            <div className="space-y-2">
              <Label>External Web Link</Label>
              <Controller
                name="external_web_link"
                control={control}
                render={({ field }) => (
                  <Input
                    {...field}
                    placeholder="https://external.example.com"
                    value={field.value || ""}
                  />
                )}
              />
              {errors.external_web_link && (
                <Text className="text-red-600 text-sm mt-1">
                  {errors.external_web_link.message}
                </Text>
              )}
            </div>
          </div>
        </div>
      </Container>

      {/* Financial Information */}
      <Container className="divide-y p-0">
        <div className="flex items-center justify-between px-6 py-4">
          <Heading level="h2">Financial Information</Heading>
        </div>
        <div className="px-6 py-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="space-y-2">
              <Label>Currency</Label>
              <Controller
                name="currency_code"
                control={control}
                render={({ field }) => (
                  <CurrencySelector
                    value={field.value || ""}
                    onValueChange={field.onChange}
                  />
                )}
              />
            </div>

            <div className="space-y-2">
              <Label>Margin (%)</Label>
              <Controller
                name="margin"
                control={control}
                render={({ field }) => (
                  <Input
                    {...field}
                    type="number"
                    min="0"
                    max="100"
                    step="0.01"
                    placeholder="e.g. 20"
                    onChange={(e) =>
                      field.onChange(
                        e.target.value ? parseFloat(e.target.value) : undefined
                      )
                    }
                  />
                )}
              />
              {errors.margin && (
                <Text className="text-red-600 text-sm mt-1">
                  {errors.margin.message}
                </Text>
              )}
            </div>
          </div>
        </div>
      </Container>

      {/* Tags */}
      <Container className="divide-y p-0">
        <div className="flex items-center justify-between px-6 py-4">
          <Heading level="h2">Tags</Heading>
        </div>
        <div className="px-6 py-4">
          <div className="space-y-4">
            {/* Tags Display */}
            {tags.length > 0 && (
              <div className="flex flex-wrap gap-2 p-3 border border-border rounded-md bg-muted/50">
                {tags.map((tag, index) => (
                  <Badge
                    key={index}
                    className="inline-flex items-center gap-1 px-2 py-1 text-xs font-medium bg-blue-100 text-blue-800 rounded-full"
                  >
                    {tag}
                    <button
                      type="button"
                      onClick={() => removeTag(tag)}
                      className="ml-1 hover:bg-blue-200 rounded-full p-0.5 transition-colors"
                      aria-label={`Remove ${tag} tag`}
                    >
                      <X size={12} />
                    </button>
                  </Badge>
                ))}
              </div>
            )}

            {/* Input Field */}
            <div className="grid grid-cols-3">
              <Input
                value={tagInput}
                onChange={handleTagInputChange}
                onKeyDown={handleTagInputKeyDown}
                placeholder="Type a tag and press Enter (e.g. mountain, beach, family-friendly)"
              />
            </div>
            <Text className="text-xs text-muted-foreground mt-2">
              Press Enter to add a tag, or separate multiple tags with commas
            </Text>
          </div>
        </div>
      </Container>

      {/* Visibility Settings */}
      <Container className="divide-y p-0">
        <div className="flex items-center justify-between px-6 py-4">
          <Heading level="h2">Visibility Settings</Heading>
        </div>
        <div className="px-6 py-4">
          <VisibilitySettings
            title="Status"
            options={[
              {
                id: "is_active",
                label: "Active",
                description:
                  "When active, the destination will be visible to users",
                checked: watch("is_active"),
                onChange: (checked: boolean) => setValue("is_active", checked),
              },
              {
                id: "is_featured",
                label: "Featured",
                description:
                  "Featured destinations will be highlighted and shown prominently to users",
                checked: watch("is_featured") || false,
                onChange: (checked: boolean) =>
                  setValue("is_featured", checked),
              },
            ]}
          />
        </div>
      </Container>

      {/* Destination Images */}
      <Container className="divide-y p-0">
        <div className="flex items-center justify-between px-6 py-4">
          <Heading level="h2">Destination Images</Heading>
          <Button
            type="button"
            variant="secondary"
            size="small"
            onClick={() => {
              // Trigger the hidden file input in DestinationMediaSection
              const fileInput = document.getElementById('media-upload') as HTMLInputElement;
              fileInput?.click();
            }}
          >
            Upload Images
          </Button>
        </div>
        <div className="px-6 py-4">
          <Text className="text-sm text-muted-foreground mb-4">
            Upload images that showcase this destination. The first image or the one marked as thumbnail will be used as the main image.
          </Text>

          <DestinationMediaSection
            form={form as any}
          />
        </div>
      </Container>

      {/* FAQs Section */}
      <Container className="divide-y p-0">
        <div className="flex items-center justify-between px-6 py-4">
          <Heading level="h2">Frequently Asked Questions</Heading>
          <Button
            type="button"
            variant="secondary"
            size="small"
            onClick={() => {
              setFaqs([...faqs, { question: "", answer: "" }]);
            }}
          >
            Add FAQ
          </Button>
        </div>
        <div className="px-6 py-4">
          <Text className="text-sm text-muted-foreground mb-4">
            Add common questions and answers about this destination.
          </Text>

          <div className="space-y-4">
            {faqs.map((faq, index) => (
              <div key={index} className="border border-border rounded-lg p-4">
                <div className="grid grid-cols-1 gap-4">
                  <div className="space-y-2">
                    <Label>Question</Label>
                    <Input
                      value={faq.question}
                      onChange={(e) => {
                        const newFaqs = [...faqs];
                        newFaqs[index].question = e.target.value;
                        setFaqs(newFaqs);
                      }}
                      placeholder="Enter question"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label>Answer</Label>
                    <Textarea
                      value={faq.answer}
                      onChange={(e) => {
                        const newFaqs = [...faqs];
                        newFaqs[index].answer = e.target.value;
                        setFaqs(newFaqs);
                      }}
                      placeholder="Enter answer"
                      rows={3}
                    />
                  </div>
                  <div className="flex justify-end">
                    <Button
                      type="button"
                      variant="secondary"
                      size="small"
                      onClick={() => {
                        const newFaqs = faqs.filter((_, i) => i !== index);
                        setFaqs(newFaqs);
                      }}
                    >
                      Remove FAQ
                    </Button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </Container>

      {/* Form Actions */}
      <div className="flex items-center justify-end gap-3 bg-muted/50">
        <Button
          type="button"
          variant="secondary"
          onClick={onCancel}
          disabled={isSubmitting}
        >
          <X className="w-4 h-4 mr-2" />
          Cancel
        </Button>
        <Button
          type="button"
          onClick={handleSubmit(handleFormSubmit)}
          disabled={isSubmitting}
        >
          <Save className="w-4 h-4 mr-2" />
          {isSubmitting ? "Creating..." : "Create Destination"}
        </Button>
      </div>
    </div>
  );
};

export default DestinationCreateForm;
