import { Container, Heading, Text } from "@camped-ai/ui";
import CartList from "../../../components/cart/cart-list";
import PermissionBasedSidebarHider from "../../../widgets/permission-based-sidebar-hider";
import { useRbac } from "../../../hooks/use-rbac";

const PageClient = () => {
  const { hasAnyPermission, loading: rbacLoading } = useRbac();

  // Allow access with either carts:view or bookings:view since carts are part of booking workflow
  const hasCartAccess = hasAnyPermission(["carts:view", "bookings:view"]);

  // Show loading state while RBAC is loading
  if (rbacLoading) {
    return (
      <>
        <PermissionBasedSidebarHider />
        <Container>
          <div className="p-8 text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
            <Text>Loading permissions...</Text>
          </div>
        </Container>
      </>
    );
  }

  if (!hasCartAccess) {
    return (
      <>
        <PermissionBasedSidebarHider />
        <Container>
          <div className="p-8 text-center">
            <Heading level="h1">Access Denied</Heading>
            <Text className="mt-2">
              You don't have permission to view carts.
              <br />
              Required permissions: carts:view or bookings:view
            </Text>
          </div>
        </Container>
      </>
    );
  }

  return (
    <>
      <PermissionBasedSidebarHider />
      <Container>
        <CartList />
      </Container>
    </>
  );
};

export default PageClient;
