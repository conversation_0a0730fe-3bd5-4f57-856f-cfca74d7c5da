import { Container, Heading, Text } from "@camped-ai/ui";
import { useParams } from "react-router-dom";
import PermissionBasedSidebarHider from "../../../../widgets/permission-based-sidebar-hider";
import CartDetail from "../../../../components/cart/cart-detail";
import { useRbac } from "../../../../hooks/use-rbac";

const PageClient = () => {
  const { id } = useParams();
  const { hasAnyPermission } = useRbac();

  // Allow access with either carts:view or bookings:view since carts are part of booking workflow
  const hasCartAccess = hasAnyPermission(["carts:view", "bookings:view"]);

  if (!hasCartAccess) {
    return (
      <>
        <PermissionBasedSidebarHider />
        <Container>
          <div className="p-8 text-center">
            <Heading level="h1">Access Denied</Heading>
            <Text className="mt-2">
              You don't have permission to view cart details.
              <br />
              Required permissions: carts:view or bookings:view
            </Text>
          </div>
        </Container>
      </>
    );
  }

  return (
    <>
      <PermissionBasedSidebarHider />
      <Container>
        <CartDetail cartId={id} />
      </Container>
    </>
  );
};

export default PageClient;
