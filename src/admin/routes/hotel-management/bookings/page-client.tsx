
import { Container, Heading, Text } from "@camped-ai/ui";
import BookingList from "../../../components/booking/booking-list";
import PermissionBasedSidebarHider from "../../../widgets/permission-based-sidebar-hider";
import ErrorBoundary from "../../../components/shared/error-boundary";
import { useLocation } from "react-router-dom";
import { useRbac } from "../../../hooks/use-rbac";

const PageClient = () => {
  // Get hotel_id from URL query parameters
  const location = useLocation();
  const queryParams = new URLSearchParams(location.search);
  const hotelId = queryParams.get("hotel_id");
  const { hasAnyPermission, loading: rbacLoading } = useRbac();

  // For hotel-specific bookings, allow hotel_management:view as well as bookings:view
  const hasBookingAccess = hasAnyPermission(["bookings:view", "hotel_management:view"]);

  // Show loading state while RBAC is loading
  if (rbacLoading) {
    return (
      <>
        <PermissionBasedSidebarHider />
        <Container>
          <div className="p-8 text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
            <Text>Loading permissions...</Text>
          </div>
        </Container>
      </>
    );
  }

  return (
    <>
      <PermissionBasedSidebarHider />
      {!hasBookingAccess ? (
        <Container>
          <div className="p-8 text-center">
            <Heading level="h1">Access Denied</Heading>
            <Text className="mt-2">
              You don't have permission to view bookings.
              <br />
              Required permissions: bookings:view or hotel_management:view
            </Text>
          </div>
        </Container>
      ) : (
        <Container>
          <ErrorBoundary>
            <BookingList hotelId={hotelId} />
          </ErrorBoundary>
        </Container>
      )}
    </>
  );
};

export default PageClient;
