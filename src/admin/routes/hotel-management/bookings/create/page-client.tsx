import { Container, Heading, Text } from "@camped-ai/ui";
import ImprovedBookingForm from "../../../../components/booking/improved-booking-form";
import PermissionBasedSidebarHider from "../../../../widgets/permission-based-sidebar-hider";
import { RoleGuard } from "../../../../components/rbac/RoleGuard";

const PageClient = () => {
  return (
    <>
      <PermissionBasedSidebarHider />
      <RoleGuard
        requirePermission="bookings:create"
        fallback={
          <Container className="p-8 text-center">
            <Heading level="h1">Access Denied</Heading>
            <Text className="mt-2">
              You don't have permission to create bookings.
            </Text>
          </Container>
        }
      >
        <ImprovedBookingForm />
      </RoleGuard>
    </>
  );
};

export default PageClient;
