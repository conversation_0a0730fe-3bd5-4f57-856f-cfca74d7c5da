import React from "react";
import { Container, Tabs, clx } from "@camped-ai/ui";
import { FileText, Shield, Settings, Sparkles, Image } from "lucide-react";

export type HotelTabId =
  | "overview"
  | "facilities"
  | "insights"
  | "media"
  | "bailey-ai"
  | "settings";

export interface HotelTab {
  id: HotelTabId;
  label: string;
  icon: React.ComponentType<{ className?: string }>;
  count?: number;
}

interface HotelTabsProps {
  activeTab: HotelTabId;
  onTabChange: (tabId: HotelTabId) => void;
  tabs?: HotelTab[];
}

const defaultTabs: HotelTab[] = [
  {
    id: "overview",
    label: "Overview",
    icon: FileText,
  },
  {
    id: "facilities",
    label: "Facilities & Policies",
    icon: Shield,
  },
  {
    id: "media",
    label: "Media",
    icon: Image,
  },
  {
    id: "bailey-ai",
    label: "Bailey AI",
    icon: Sparkles,
  },
  {
    id: "settings",
    label: "Settings",
    icon: Settings,
  },
];

const HotelTabs: React.FC<HotelTabsProps> = ({
  activeTab,
  onTabChange,
  tabs = defaultTabs,
}) => {
  // Match DestinationTabs structure and classes for visual consistency
  return (
    <div className={clx("w-full bg-background")}>
      <Tabs
        value={activeTab}
        onValueChange={(value) => onTabChange(value as HotelTabId)}
      >
        <Container>
          <Tabs.List className={`grid w-full grid-cols-5`}>
            {tabs.map((tab) => {
              const Icon = tab.icon;
              return (
                <Tabs.Trigger
                  key={tab.id}
                  value={tab.id}
                  className="flex items-center gap-2"
                >
                  <Icon className="h-4 w-4" />
                  {tab.label}
                  {typeof tab.count === "number" ? (
                    <span className="ml-2 inline-flex items-center justify-center rounded-full bg-gray-200 px-2 py-0.5 text-xs text-gray-700">
                      {tab.count}
                    </span>
                  ) : null}
                </Tabs.Trigger>
              );
            })}
          </Tabs.List>
        </Container>
      </Tabs>
    </div>
  );
};

export default HotelTabs;
