import React, { useMemo, useRef, useState } from "react";
import { Container, Heading, Text, Badge, Button, Table, FocusModal } from "@camped-ai/ui";
import { Shield, Wifi, Car, Coffee, Dumbbell, Waves, Utensils, Tv, AlertTriangle, BookOpen, Clock, XCircle, Plus, Edit, Trash2, Calendar, CheckCircle, X, Check } from "lucide-react";
import { HotelData, CancellationPolicyData } from "../../../../../types";
import CancellationPolicyForm from "../../../../../components/hotel/cancellation-policy/cancellation-policy-form";
import {
  useAdminCreateCancellationPolicy,
  useAdminUpdateCancellationPolicy,
  useAdminDeleteCancellationPolicy,
  useAdminCancellationPolicies
} from "../../../../../hooks/cancellation-policies/use-admin-cancellation-policies";
import { useRbac } from "../../../../../hooks/use-rbac";

interface AmenitiesTabProps {
  hotel: HotelData | null;
}

const AmenitiesTab: React.FC<AmenitiesTabProps> = ({ hotel }) => {
  const { hasPermission } = useRbac();
  const [isAddPolicyOpen, setIsAddPolicyOpen] = useState(false);
  // Editable container states
  const [isEditingFacilities, setIsEditingFacilities] = useState(false);
  const [isEditingSafety, setIsEditingSafety] = useState(false);
  const [isEditingRules, setIsEditingRules] = useState(false);

  // Local editable copies
  const [editedAmenities, setEditedAmenities] = useState<string[]>(
    Array.isArray((hotel as any)?.amenities)
      ? ([...(hotel as any).amenities] as string[])
      : typeof (hotel as any)?.amenities === "string"
        ? ((hotel as any).amenities as string)
            .split(",")
            .map((s: string) => s.trim())
            .filter((s: string) => s.length > 0)
        : []
  );
  const [amenityInput, setAmenityInput] = useState("");
  const amenityInputRef = useRef<HTMLInputElement | null>(null);
  const facilitiesCount = useMemo(() => editedAmenities.length, [editedAmenities]);
  const [editedSafetyMeasures, setEditedSafetyMeasures] = useState<string[]>(
    Array.isArray((hotel as any)?.safety_measures)
      ? ([...(hotel as any).safety_measures] as string[])
      : typeof (hotel as any)?.safety_measures === "string"
        ? ((hotel as any).safety_measures as string)
            .split(",")
            .map((s: string) => s.trim())
            .filter((s: string) => s.length > 0)
        : []
  );
  const [safetyInput, setSafetyInput] = useState("");
  const safetyInputRef = useRef<HTMLInputElement | null>(null);

  const [editedRules, setEditedRules] = useState<string[]>(
    Array.isArray((hotel as any)?.rules)
      ? ([...(hotel as any).rules] as string[])
      : typeof (hotel as any)?.rules === "string"
        ? ((hotel as any).rules as string)
            .split("\n")
            .map((s: string) => s.trim())
            .filter((s: string) => s.length > 0)
        : []
  );
  const [ruleInput, setRuleInput] = useState("");
  const ruleInputRef = useRef<HTMLInputElement | null>(null);
  const [isEditPolicyOpen, setIsEditPolicyOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [selectedPolicy, setSelectedPolicy] = useState<CancellationPolicyData | null>(null);

  // Booking rules inline edit state
  const [isEditingBookingRules, setIsEditingBookingRules] = useState(false);
  const [editedCheckInTime, setEditedCheckInTime] = useState<string>(hotel?.check_in_time || "");
  const [editedCheckOutTime, setEditedCheckOutTime] = useState<string>(hotel?.check_out_time || "");
  const [editedIsPetsAllowed, setEditedIsPetsAllowed] = useState<boolean>(!!hotel?.is_pets_allowed);

  // API hooks
  const { data: cancellationPoliciesData, isLoading: isPoliciesLoading } = useAdminCancellationPolicies(hotel?.id);
  const createPolicyMutation = useAdminCreateCancellationPolicy();
  const updatePolicyMutation = useAdminUpdateCancellationPolicy(selectedPolicy?.id || "");
  const deletePolicyMutation = useAdminDeleteCancellationPolicy();

  // Use the fetched data instead of hotel.cancellation_policies
  const cancellationPolicies = (cancellationPoliciesData as any)?.cancellation_policies || [];

  if (!hotel) {
    return (
      <Container className="p-6">
        <Text className="text-muted-foreground">Loading hotel details...</Text>
      </Container>
    );
  }

  // Debug logging to help troubleshoot
  console.log("Hotel data in AmenitiesTab:", hotel);
  console.log("Cancellation policies:", cancellationPolicies);

  const handleAddPolicy = () => {
    setSelectedPolicy(null);
    setIsAddPolicyOpen(true);
  };

  const handleEditPolicy = (policy: CancellationPolicyData) => {
    setSelectedPolicy(policy);
    setIsEditPolicyOpen(true);
  };

  const handleDeletePolicy = (policy: CancellationPolicyData) => {
    setSelectedPolicy(policy);
    setIsDeleteDialogOpen(true);
  };

  const handleSavePolicy = async (data: Partial<CancellationPolicyData>) => {
    try {
      if (selectedPolicy) {
        // Update existing policy
        await updatePolicyMutation.mutateAsync(data);
      } else {
        // Create new policy
        await createPolicyMutation.mutateAsync({
          ...data,
          hotel_id: hotel.id,
        } as any);
      }

      setIsAddPolicyOpen(false);
      setIsEditPolicyOpen(false);
      setSelectedPolicy(null);

      // React Query will automatically update the UI through cache invalidation
    } catch (error) {
      console.error("Error saving policy:", error);
      // Error handling is done in the mutation hooks
    }
  };

  const confirmDelete = async () => {
    if (!selectedPolicy) return;

    try {
      await deletePolicyMutation.mutateAsync({
        id: selectedPolicy.id,
        hotelId: hotel.id,
      });

      // Close the dialog and clear selection
      setIsDeleteDialogOpen(false);
      setSelectedPolicy(null);

      // React Query will automatically update the UI through cache invalidation
      // The table row will be removed automatically without page refresh
    } catch (error) {
      // Error handling is done in the mutation hook
    }
  };

  // Icon mapping for common amenities
  const getAmenityIcon = (amenity: string) => {
    const amenityLower = amenity.toLowerCase();
    if (amenityLower.includes('wifi') || amenityLower.includes('internet')) return Wifi;
    if (amenityLower.includes('parking') || amenityLower.includes('car')) return Car;
    if (amenityLower.includes('coffee') || amenityLower.includes('breakfast')) return Coffee;
    if (amenityLower.includes('gym') || amenityLower.includes('fitness')) return Dumbbell;
    if (amenityLower.includes('pool') || amenityLower.includes('swimming')) return Waves;
    if (amenityLower.includes('restaurant') || amenityLower.includes('dining')) return Utensils;
    if (amenityLower.includes('tv') || amenityLower.includes('television')) return Tv;
    return Shield; // Default icon
  };

  const getAmenityColor = (index: number) => {
    const colors = [
      'bg-blue-100 dark:bg-blue-900/50 text-blue-600 dark:text-blue-400',
      'bg-green-100 dark:bg-green-900/50 text-green-600 dark:text-green-400',
      'bg-purple-100 dark:bg-purple-900/50 text-purple-600 dark:text-purple-400',
      'bg-orange-100 dark:bg-orange-900/50 text-orange-600 dark:text-orange-400',
      'bg-pink-100 dark:bg-pink-900/50 text-pink-600 dark:text-pink-400',
      'bg-indigo-100 dark:bg-indigo-900/50 text-indigo-600 dark:text-indigo-400',
    ];
    return colors[index % colors.length];
  };

  return (
    <div className="space-y-6">
      {/* Hotel Facilities Container */}
      <Container className="divide-y p-0">
        <div className="px-6 py-4 flex items-center justify-between bg-muted/50">
          <div className="flex items-center gap-3">
            <Shield className="w-5 h-5 text-muted-foreground flex-shrink-0" />
            <Heading
              level="h2"
              className="text-lg font-medium text-foreground"
            >
              Hotel Facilities
            </Heading>
          </div>
          <div className="flex items-center gap-3">
            <Badge className="text-xs">
              {facilitiesCount} {facilitiesCount === 1 ? "facility" : "facilities"}
            </Badge>
            {hasPermission("hotel_management:edit") && !isEditingFacilities && (
              <Button
                variant="secondary"
                size="small"
                onClick={() => {
                  setEditedAmenities([...(hotel?.amenities || [])]);
                  setIsEditingFacilities(true);
                }}
              >
                <Edit className="w-4 h-4 mr-2" />
                Edit
              </Button>
            )}
          </div>
        </div>
        <div className="p-6">
          {isEditingFacilities ? (
            <div className="space-y-4">
              {/* Tag-based multiselect input */}
              <div>
                <Text className="text-sm font-medium text-foreground">Facilities</Text>
                <div
                  className="mt-2 border border-border rounded-md p-2 text-sm bg-background min-h-[42px] flex flex-wrap gap-2"
                  onClick={() => amenityInputRef.current?.focus()}
                  role="group"
                  aria-label="Facilities tags input"
                >
                  {editedAmenities.map((amenity, idx) => (
                    <span
                      key={`${amenity}-${idx}`}
                      className="inline-flex items-center gap-1 rounded-full bg-muted px-2 py-1 text-xs text-foreground"
                    >
                      {amenity}
                      <button
                        type="button"
                        onClick={() =>
                          setEditedAmenities((prev) =>
                            prev.filter((_, i) => i !== idx)
                          )
                        }
                        className="hover:text-red-600"
                        aria-label={`Remove ${amenity}`}
                      >
                        <X className="w-3 h-3" />
                      </button>
                    </span>
                  ))}
                  <input
                    ref={amenityInputRef}
                    value={amenityInput}
                    onChange={(e) => setAmenityInput(e.target.value)}
                    onKeyDown={(e) => {
                      const submitToken = (token: string) => {
                        const value = token.trim();
                        if (!value) return;
                        setEditedAmenities((prev) => {
                          if (prev.includes(value)) return prev;
                          return [...prev, value];
                        });
                        setAmenityInput("");
                      };

                      if (e.key === "Enter" || e.key === ",") {
                        e.preventDefault();
                        submitToken(amenityInput);
                      } else if (e.key === "Backspace" && amenityInput.length === 0) {
                        // Remove last tag
                        setEditedAmenities((prev) => prev.slice(0, -1));
                      }
                    }}
                    placeholder="Type and press Enter or , to add"
                    className="flex-1 min-w-[160px] outline-none bg-transparent"
                  />
                </div>
                <Text className="text-xs text-muted-foreground mt-1">
                  Type a facility and press Enter or Comma to add. Backspace removes the last item.
                </Text>
              </div>
              <div className="flex justify-end gap-2">
                <Button
                  variant="secondary"
                  size="small"
                  onClick={() => {
                    // Reset to hotel amenities source of truth
                    const resetAmenities = Array.isArray((hotel as any)?.amenities)
                      ? ([...(hotel as any).amenities] as string[])
                      : typeof (hotel as any)?.amenities === "string"
                        ? ((hotel as any).amenities as string)
                            .split(",")
                            .map((s: string) => s.trim())
                            .filter((s: string) => s.length > 0)
                        : [];
                    setEditedAmenities(resetAmenities);
                    setAmenityInput("");
                    setIsEditingFacilities(false);
                  }}
                >
                  <X className="w-4 h-4 mr-2" />
                  Cancel
                </Button>
                <Button
                  variant="primary"
                  size="small"
                  onClick={async () => {
                    try {
                      // Normalize values before persisting
                      const normalized = editedAmenities
                        .map((s) => s.trim())
                        .filter((s) => s.length > 0);
                      const res = await fetch("/admin/hotel-management/hotels", {
                        method: "PUT",
                        credentials: "include",
                        headers: { "Content-Type": "application/json" },
                        body: JSON.stringify({
                          id: hotel!.id,
                          amenities: normalized,
                        }),
                      });
                      if (!res.ok) throw new Error("Failed to update amenities");
                      (hotel as any).amenities = [...normalized];
                      setIsEditingFacilities(false);
                    } catch (e) {
                      console.error(e);
                    }
                  }}
                >
                  <Check className="w-4 h-4 mr-2" />
                  Save
                </Button>
              </div>
            </div>
          ) : (
            <>
              {/* Amenities Grid */}
              {Array.isArray((hotel as any)?.amenities) && (hotel as any).amenities.length > 0 ? (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {(hotel as any).amenities.map((amenity: string, index: number) => {
                    const IconComponent = getAmenityIcon(amenity);
                    const colorClass = getAmenityColor(index);

                    return (
                      <div
                        key={index}
                        className="p-4 bg-card rounded-lg border border-border hover:border-muted-foreground/20 transition-colors"
                      >
                        <div className="flex items-center gap-3">
                          <div className={`w-10 h-10 rounded-full flex items-center justify-center flex-shrink-0 ${colorClass}`}>
                            <IconComponent className="w-5 h-5" />
                          </div>
                          <div className="min-w-0 flex-1">
                            <Text className="font-medium text-foreground break-words">
                              {amenity}
                            </Text>
                          </div>
                        </div>
                      </div>
                    );
                  })}
                </div>
              ) : (
                <div className="text-center py-12">
                  <div className="w-16 h-16 rounded-full bg-muted flex items-center justify-center mx-auto mb-4">
                    <Shield className="w-8 h-8 text-muted-foreground" />
                  </div>
                  <Heading level="h3" className="text-lg font-medium text-foreground mb-2">
                    No Facilities Listed
                  </Heading>
                  <Text className="text-muted-foreground max-w-md mx-auto">
                    This hotel doesn't have any facilities listed yet. Facilities can be added when editing this section.
                  </Text>
                </div>
              )}
            </>
          )}
        </div>
      </Container>

      {/* Safety Measures Container */}
      <Container className="divide-y p-0">
        <div className="px-6 py-4 flex items-center justify-between">
          <div className="flex items-center gap-3">
            <AlertTriangle className="w-5 h-5 text-muted-foreground flex-shrink-0" />
              <Heading
                level="h2"
                className="text-lg font-medium text-foreground"
              >
                Safety Measures
              </Heading>
            </div>
            <div className="flex items-center gap-3">
              {hotel.safety_measures && hotel.safety_measures.length > 0 && (
                <Badge className="text-xs">
                  {hotel.safety_measures.length} measures
                </Badge>
              )}
              {hasPermission("hotel_management:edit") && !isEditingSafety && (
                <Button
                  variant="secondary"
                  size="small"
                  onClick={() => {
                    setEditedSafetyMeasures([...(hotel?.safety_measures || [])]);
                    setIsEditingSafety(true);
                  }}
                >
                  <Edit className="w-4 h-4 mr-2" />
                  Edit
                </Button>
              )}
            </div>
          </div>
          <div className="p-6">
            {isEditingSafety ? (
              <>
                <div>
                  <Text className="text-sm font-medium text-foreground">Safety Measures</Text>
                  <div
                    className="mt-2 border border-border rounded-md p-2 text-sm bg-background min-h-[42px] flex flex-wrap gap-2"
                    onClick={() => safetyInputRef.current?.focus()}
                    role="group"
                    aria-label="Safety measures tags input"
                  >
                    {editedSafetyMeasures.map((measure, idx) => (
                      <span
                        key={`${measure}-${idx}`}
                        className="inline-flex items-center gap-1 rounded-full bg-muted px-2 py-1 text-xs text-foreground"
                      >
                        {measure}
                        <button
                          type="button"
                          onClick={() =>
                            setEditedSafetyMeasures((prev) => prev.filter((_, i) => i !== idx))
                          }
                          className="hover:text-red-600"
                          aria-label={`Remove ${measure}`}
                        >
                          <X className="w-3 h-3" />
                        </button>
                      </span>
                    ))}
                    <input
                      ref={safetyInputRef}
                      value={safetyInput}
                      onChange={(e) => setSafetyInput(e.target.value)}
                      onKeyDown={(e) => {
                        const submitToken = (token: string) => {
                          const value = token.trim();
                          if (!value) return;
                          setEditedSafetyMeasures((prev) => (prev.includes(value) ? prev : [...prev, value]));
                          setSafetyInput("");
                        };
                        if (e.key === "Enter" || e.key === ",") {
                          e.preventDefault();
                          submitToken(safetyInput);
                        } else if (e.key === "Backspace" && safetyInput.length === 0) {
                          setEditedSafetyMeasures((prev) => prev.slice(0, -1));
                        }
                      }}
                      placeholder="Type and press Enter or , to add"
                      className="flex-1 min-w-[160px] outline-none bg-transparent"
                    />
                  </div>
                  <Text className="text-xs text-muted-foreground mt-1">
                    Type a safety measure and press Enter or Comma to add. Backspace removes the last item.
                  </Text>
                </div>
                <div className="flex justify-end gap-2 pt-4">
                  <Button
                    variant="secondary"
                    size="small"
                    onClick={() => {
                      const resetMeasures = Array.isArray((hotel as any)?.safety_measures)
                        ? ([...(hotel as any).safety_measures] as string[])
                        : typeof (hotel as any)?.safety_measures === "string"
                          ? ((hotel as any).safety_measures as string)
                              .split(",")
                              .map((s: string) => s.trim())
                              .filter((s: string) => s.length > 0)
                          : [];
                      setEditedSafetyMeasures(resetMeasures);
                      setSafetyInput("");
                      setIsEditingSafety(false);
                    }}
                  >
                    <X className="w-4 h-4 mr-2" />
                    Cancel
                  </Button>
                  <Button
                    variant="primary"
                    size="small"
                    onClick={async () => {
                      try {
                        const normalized = editedSafetyMeasures.map((s) => s.trim()).filter((s) => s.length > 0);
                        const res = await fetch("/admin/hotel-management/hotels", {
                          method: "PUT",
                          credentials: "include",
                          headers: { "Content-Type": "application/json" },
                          body: JSON.stringify({
                            id: hotel!.id,
                            safety_measures: normalized,
                          }),
                        });
                        if (!res.ok) throw new Error("Failed to update safety measures");
                        (hotel as any).safety_measures = [...normalized];
                        setIsEditingSafety(false);
                      } catch (e) {
                        console.error(e);
                      }
                    }}
                  >
                    <Check className="w-4 h-4 mr-2" />
                    Save
                  </Button>
                </div>
              </>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {(hotel.safety_measures || []).map((measure, index) => (
                  <div
                    key={index}
                    className="p-4 bg-red-50 dark:bg-red-950/30 rounded-lg border border-red-200 dark:border-red-800"
                  >
                    <div className="flex items-center gap-3">
                      <div className="w-8 h-8 rounded-full bg-red-100 dark:bg-red-900/50 flex items-center justify-center flex-shrink-0">
                        <Shield className="w-4 h-4 text-red-600 dark:text-red-400" />
                      </div>
                      <div className="min-w-0 flex-1">
                        <Text className="font-medium text-red-800 dark:text-red-200 break-words">
                          {measure}
                        </Text>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </Container>

      {/* Hotel Rules Container */}
      <Container className="divide-y p-0">
        <div className="px-6 py-4 flex items-center justify-between">
          <div className="flex items-center gap-3">
            <BookOpen className="w-5 h-5 text-muted-foreground flex-shrink-0" />
              <Heading
                level="h2"
                className="text-lg font-medium text-foreground"
              >
                Hotel Rules
              </Heading>
            </div>
            <div className="flex items-center gap-3">
              {hotel.rules && hotel.rules.length > 0 && (
                <Badge className="text-xs">
                  {hotel.rules.length} rules
                </Badge>
              )}
              {hasPermission("hotel_management:edit") && !isEditingRules && (
                <Button
                  variant="secondary"
                  size="small"
                  onClick={() => {
                    setEditedRules([...(hotel?.rules || [])]);
                    setIsEditingRules(true);
                  }}
                >
                  <Edit className="w-4 h-4 mr-2" />
                  Edit
                </Button>
              )}
            </div>
          </div>
          <div className="p-6">
            {isEditingRules ? (
              <>
                <div>
                  <Text className="text-sm font-medium text-foreground">Hotel Rules</Text>
                  <div
                    className="mt-2 border border-border rounded-md p-2 text-sm bg-background min-h-[42px] flex flex-wrap gap-2"
                    onClick={() => ruleInputRef.current?.focus()}
                    role="group"
                    aria-label="Hotel rules tags input"
                  >
                    {editedRules.map((rule, idx) => (
                      <span
                        key={`${rule}-${idx}`}
                        className="inline-flex items-center gap-1 rounded-full bg-muted px-2 py-1 text-xs text-foreground"
                      >
                        {rule}
                        <button
                          type="button"
                          onClick={() =>
                            setEditedRules((prev) => prev.filter((_, i) => i !== idx))
                          }
                          className="hover:text-red-600"
                          aria-label={`Remove ${rule}`}
                        >
                          <X className="w-3 h-3" />
                        </button>
                      </span>
                    ))}
                    <input
                      ref={ruleInputRef}
                      value={ruleInput}
                      onChange={(e) => setRuleInput(e.target.value)}
                      onKeyDown={(e) => {
                        const submitToken = (token: string) => {
                          const value = token.trim();
                          if (!value) return;
                          setEditedRules((prev) => (prev.includes(value) ? prev : [...prev, value]));
                          setRuleInput("");
                        };
                        if (e.key === "Enter" || e.key === ",") {
                          e.preventDefault();
                          submitToken(ruleInput);
                        } else if (e.key === "Backspace" && ruleInput.length === 0) {
                          setEditedRules((prev) => prev.slice(0, -1));
                        }
                      }}
                      placeholder="Type and press Enter or , to add"
                      className="flex-1 min-w-[160px] outline-none bg-transparent"
                    />
                  </div>
                  <Text className="text-xs text-muted-foreground mt-1">
                    Type a rule and press Enter or Comma to add. Backspace removes the last item.
                  </Text>
                </div>
                <div className="flex justify-end gap-2 pt-4">
                  <Button
                    variant="secondary"
                    size="small"
                    onClick={() => {
                      const resetRules = Array.isArray((hotel as any)?.rules)
                        ? ([...(hotel as any).rules] as string[])
                        : typeof (hotel as any)?.rules === "string"
                          ? ((hotel as any).rules as string)
                              .split("\n")
                              .map((s: string) => s.trim())
                              .filter((s: string) => s.length > 0)
                          : [];
                      setEditedRules(resetRules);
                      setRuleInput("");
                      setIsEditingRules(false);
                    }}
                  >
                    <X className="w-4 h-4 mr-2" />
                    Cancel
                  </Button>
                  <Button
                    variant="primary"
                    size="small"
                    onClick={async () => {
                      try {
                        const normalized = editedRules.map((s) => s.trim()).filter((s) => s.length > 0);
                        const res = await fetch("/admin/hotel-management/hotels", {
                          method: "PUT",
                          credentials: "include",
                          headers: { "Content-Type": "application/json" },
                          body: JSON.stringify({
                            id: hotel!.id,
                            rules: normalized,
                          }),
                        });
                        if (!res.ok) throw new Error("Failed to update rules");
                        (hotel as any).rules = [...normalized];
                        setIsEditingRules(false);
                      } catch (e) {
                        console.error(e);
                      }
                    }}
                  >
                    <Check className="w-4 h-4 mr-2" />
                    Save
                  </Button>
                </div>
              </>
            ) : (
              <div className="space-y-3">
                {(hotel.rules || []).map((rule, index) => (
                  <div
                    key={index}
                    className="p-4 bg-yellow-50 dark:bg-yellow-950/30 rounded-lg border border-yellow-200 dark:border-yellow-800"
                  >
                    <Text className="text-yellow-800 dark:text-yellow-200 break-words">
                      {rule}
                    </Text>
                  </div>
                ))}
              </div>
            )}
          </div>
        </Container>

      {/* Booking Rules Container */}
      <Container className="divide-y p-0">
        <div className="px-6 py-4 flex items-center justify-between">
          <div className="flex items-center gap-3">
            <Calendar className="w-5 h-5 text-muted-foreground flex-shrink-0" />
            <Heading
              level="h2"
              className="text-lg font-medium text-foreground"
            >
              Booking Rules
            </Heading>
          </div>
          {hasPermission("hotel_management:edit") && !isEditingBookingRules && (
            <Button
              variant="secondary"
              size="small"
              onClick={() => {
                setEditedCheckInTime(hotel.check_in_time || "");
                setEditedCheckOutTime(hotel.check_out_time || "");
                setEditedIsPetsAllowed(!!hotel.is_pets_allowed);
                setIsEditingBookingRules(true);
              }}
            >
              <Edit className="w-4 h-4 mr-2" />
              Edit
            </Button>
          )}
        </div>
        <div className="p-6">
          {isEditingBookingRules ? (
            <>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                {/* Check-in Time */}
                <div className="p-4 rounded-lg border border-blue-200 dark:border-blue-800 bg-blue-50 dark:bg-blue-950/30">
                  <div className="flex items-center gap-3 mb-2">
                    <div className="w-8 h-8 rounded-full bg-blue-100 dark:bg-blue-900/50 flex items-center justify-center flex-shrink-0">
                      <Clock className="w-4 h-4 text-blue-600 dark:text-blue-400" />
                    </div>
                    <Text className="font-medium text-blue-800 dark:text-blue-200">
                      Check-in Time
                    </Text>
                  </div>
                  <input
                    type="time"
                    className="ml-11 mt-1 border border-border rounded-md px-2 py-1 text-sm bg-background"
                    value={editedCheckInTime}
                    onChange={(e) => setEditedCheckInTime(e.target.value)}
                  />
                </div>

                {/* Check-out Time */}
                <div className="p-4 rounded-lg border border-green-200 dark:border-green-800 bg-green-50 dark:bg-green-950/30">
                  <div className="flex items-center gap-3 mb-2">
                    <div className="w-8 h-8 rounded-full bg-green-100 dark:bg-green-900/50 flex items-center justify-center flex-shrink-0">
                      <Clock className="w-4 h-4 text-green-600 dark:text-green-400" />
                    </div>
                    <Text className="font-medium text-green-800 dark:text-green-200">
                      Check-out Time
                    </Text>
                  </div>
                  <input
                    type="time"
                    className="ml-11 mt-1 border border-border rounded-md px-2 py-1 text-sm bg-background"
                    value={editedCheckOutTime}
                    onChange={(e) => setEditedCheckOutTime(e.target.value)}
                  />
                </div>

                {/* Pets Allowed */}
                <div className="p-4 rounded-lg border border-emerald-200 dark:border-emerald-800 bg-emerald-50 dark:bg-emerald-950/30">
                  <div className="flex items-center gap-3 mb-2">
                    <div className="w-8 h-8 rounded-full bg-emerald-100 dark:bg-emerald-900/50 flex items-center justify-center flex-shrink-0">
                      <CheckCircle className="w-4 h-4 text-emerald-600 dark:text-emerald-400" />
                    </div>
                    <Text className="font-medium text-emerald-800 dark:text-emerald-200">
                      Pets Allowed
                    </Text>
                  </div>
                  <div className="ml-11 mt-1 flex items-center gap-3">
                    <label className="inline-flex items-center gap-2 text-sm">
                      <input
                        type="checkbox"
                        className="h-4 w-4"
                        checked={editedIsPetsAllowed}
                        onChange={(e) => setEditedIsPetsAllowed(e.target.checked)}
                      />
                      <span>Allowed</span>
                    </label>
                  </div>
                </div>
              </div>

              <div className="flex justify-end gap-2 pt-4">
                <Button
                  variant="secondary"
                  size="small"
                  onClick={() => {
                    setEditedCheckInTime(hotel.check_in_time || "");
                    setEditedCheckOutTime(hotel.check_out_time || "");
                    setEditedIsPetsAllowed(!!hotel.is_pets_allowed);
                    setIsEditingBookingRules(false);
                  }}
                >
                  <X className="w-4 h-4 mr-2" />
                  Cancel
                </Button>
                <Button
                  variant="primary"
                  size="small"
                  onClick={async () => {
                    try {
                      const res = await fetch("/admin/hotel-management/hotels", {
                        method: "PUT",
                        credentials: "include",
                        headers: { "Content-Type": "application/json" },
                        body: JSON.stringify({
                          id: hotel!.id,
                          check_in_time: editedCheckInTime || null,
                          check_out_time: editedCheckOutTime || null,
                          is_pets_allowed: editedIsPetsAllowed,
                        }),
                      });
                      if (!res.ok) throw new Error("Failed to update booking rules");
                      (hotel as any).check_in_time = editedCheckInTime || null;
                      (hotel as any).check_out_time = editedCheckOutTime || null;
                      (hotel as any).is_pets_allowed = editedIsPetsAllowed;
                      setIsEditingBookingRules(false);
                    } catch (e) {
                      console.error(e);
                    }
                  }}
                >
                  <Check className="w-4 h-4 mr-2" />
                  Save
                </Button>
              </div>
            </>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {/* Check-in Time */}
              <div className="p-4 bg-blue-50 dark:bg-blue-950/30 rounded-lg border border-blue-200 dark:border-blue-800">
                <div className="flex items-center gap-3 mb-2">
                  <div className="w-8 h-8 rounded-full bg-blue-100 dark:bg-blue-900/50 flex items-center justify-center flex-shrink-0">
                    <Clock className="w-4 h-4 text-blue-600 dark:text-blue-400" />
                  </div>
                  <Text className="font-medium text-blue-800 dark:text-blue-200">
                    Check-in Time
                  </Text>
                </div>
                <Text className="text-blue-700 dark:text-blue-300 text-lg font-semibold ml-11">
                  {hotel.check_in_time || "Not specified"}
                </Text>
              </div>

              {/* Check-out Time */}
              <div className="p-4 bg-green-50 dark:bg-green-950/30 rounded-lg border border-green-200 dark:border-green-800">
                <div className="flex items-center gap-3 mb-2">
                  <div className="w-8 h-8 rounded-full bg-green-100 dark:bg-green-900/50 flex items-center justify-center flex-shrink-0">
                    <Clock className="w-4 h-4 text-green-600 dark:text-green-400" />
                  </div>
                  <Text className="font-medium text-green-800 dark:text-green-200">
                    Check-out Time
                  </Text>
                </div>
                <Text className="text-green-700 dark:text-green-300 text-lg font-semibold ml-11">
                  {hotel.check_out_time || "Not specified"}
                </Text>
              </div>

              {/* Pets Allowed */}
              <div className={`p-4 rounded-lg border ${
                hotel.is_pets_allowed
                  ? "bg-emerald-50 dark:bg-emerald-950/30 border-emerald-200 dark:border-emerald-800"
                  : "bg-red-50 dark:bg-red-950/30 border-red-200 dark:border-red-800"
              }`}>
                <div className="flex items-center gap-3 mb-2">
                  <div className={`w-8 h-8 rounded-full flex items-center justify-center flex-shrink-0 ${
                    hotel.is_pets_allowed
                      ? "bg-emerald-100 dark:bg-emerald-900/50"
                      : "bg-red-100 dark:bg-red-900/50"
                  }`}>
                    {hotel.is_pets_allowed ? (
                      <CheckCircle className="w-4 h-4 text-emerald-600 dark:text-emerald-400" />
                    ) : (
                      <X className="w-4 h-4 text-red-600 dark:text-red-400" />
                    )}
                  </div>
                  <Text className={`font-medium ${
                    hotel.is_pets_allowed
                      ? "text-emerald-800 dark:text-emerald-200"
                      : "text-red-800 dark:text-red-200"
                  }`}>
                    Pets Allowed
                  </Text>
                </div>
                <Text className={`text-lg font-semibold ml-11 ${
                  hotel.is_pets_allowed
                    ? "text-emerald-700 dark:text-emerald-300"
                    : "text-red-700 dark:text-red-300"
                }`}>
                  {hotel.is_pets_allowed ? "Yes" : "No"}
                </Text>
              </div>
            </div>
          )}
        </div>
      </Container>

      {/* Cancellation Policies Container */}
      <Container className="divide-y p-0">
        <div className="px-6 py-4 flex items-center justify-between">
          <div className="flex items-center gap-3">
            <Clock className="w-5 h-5 text-muted-foreground flex-shrink-0" />
            <Heading
              level="h2"
              className="text-lg font-medium text-foreground"
            >
              Cancellation Policies
            </Heading>
            {cancellationPolicies.length > 0 && (
              <Badge className="text-xs">
                {cancellationPolicies.length} policies
              </Badge>
            )}
          </div>
          {hasPermission("hotel_management:create") && (
            <Button
              variant="secondary"
              size="small"
              onClick={handleAddPolicy}
              className="flex items-center gap-2"
            >
              <Plus className="w-4 h-4" />
              Add Policy
            </Button>
          )}
        </div>
        <div className="overflow-hidden">
          {isPoliciesLoading ? (
            <div className="text-center py-12">
              <Text className="text-muted-foreground">Loading cancellation policies...</Text>
            </div>
          ) : cancellationPolicies.length > 0 ? (
            <div className="overflow-x-auto">
              <Table>
                <Table.Header>
                  <Table.Row className="border-b border-border">
                    <Table.HeaderCell className="text-left font-medium text-muted-foreground py-3 px-6">
                      Name
                    </Table.HeaderCell>
                    <Table.HeaderCell className="text-left font-medium text-muted-foreground py-3 px-6">
                      Days Before Check-in
                    </Table.HeaderCell>
                    <Table.HeaderCell className="text-left font-medium text-muted-foreground py-3 px-6">
                      Refund
                    </Table.HeaderCell>
                    <Table.HeaderCell className="text-left font-medium text-muted-foreground py-3 px-6">
                      Status
                    </Table.HeaderCell>
                    <Table.HeaderCell className="text-right font-medium text-muted-foreground py-3 px-6">
                      Actions
                    </Table.HeaderCell>
                  </Table.Row>
                </Table.Header>
                <Table.Body>
                  {cancellationPolicies.map((policy: CancellationPolicyData, index: number) => {
                    const getRefundText = () => {
                      switch (policy.refund_type) {
                        case 'percentage':
                          return `${policy.refund_amount}% refund`;
                        case 'fixed':
                          return `$${policy.refund_amount} refund`;
                        case 'no_refund':
                          return 'No refund';
                        default:
                          return 'Unknown refund type';
                      }
                    };

                    const getRefundBadgeColor = () => {
                      switch (policy.refund_type) {
                        case 'percentage':
                          return 'green';
                        case 'fixed':
                          return 'blue';
                        case 'no_refund':
                          return 'red';
                        default:
                          return 'grey';
                      }
                    };

                    return (
                      <Table.Row
                        key={index}
                        className="border-b border-border hover:bg-muted/50 transition-colors"
                      >
                        <Table.Cell className="py-4 px-6">
                          <div className="space-y-1">
                            <Text className="font-medium text-foreground">
                              {policy.name}
                            </Text>
                            {policy.description && (
                              <Text className="text-sm text-muted-foreground line-clamp-2">
                                {policy.description}
                              </Text>
                            )}
                          </div>
                        </Table.Cell>
                        <Table.Cell className="py-4 px-6">
                          <Text className="text-foreground font-medium">
                            {policy.days_before_checkin === 0
                              ? 'Same day'
                              : `${policy.days_before_checkin} days`
                            }
                          </Text>
                        </Table.Cell>
                        <Table.Cell className="py-4 px-6">
                          <Badge
                            color={getRefundBadgeColor()}
                            className="text-xs font-medium px-2 py-1"
                          >
                            {getRefundText()}
                          </Badge>
                        </Table.Cell>
                        <Table.Cell className="py-4 px-6">
                          <Badge
                            color="green"
                            className="text-xs font-medium px-2 py-1"
                          >
                            Active
                          </Badge>
                        </Table.Cell>
                        <Table.Cell className="py-4 px-6">
                          <div className="flex items-center justify-end gap-2">
                            {hasPermission("hotel_management:edit") && (
                              <button
                                type="button"
                                onClick={(e) => {
                                  e.preventDefault();
                                  e.stopPropagation();
                                  handleEditPolicy(policy);
                                }}
                                className="h-8 w-8 text-muted-foreground hover:text-foreground flex items-center justify-center rounded-md hover:bg-muted transition-colors"
                              >
                                <Edit className="w-4 h-4" />
                              </button>
                            )}
                            {hasPermission("hotel_management:delete") && (
                              <button
                                type="button"
                                onClick={(e) => {
                                  e.preventDefault();
                                  e.stopPropagation();
                                  handleDeletePolicy(policy);
                                }}
                                className="h-8 w-8 text-muted-foreground hover:text-red-600 flex items-center justify-center rounded-md hover:bg-muted transition-colors"
                              >
                                <Trash2 className="w-4 h-4" />
                              </button>
                            )}
                          </div>
                        </Table.Cell>
                      </Table.Row>
                    );
                  })}
                </Table.Body>
              </Table>
            </div>
          ) : (
            <div className="text-center py-12">
              <div className="w-16 h-16 rounded-full bg-muted flex items-center justify-center mx-auto mb-4">
                <Clock className="w-8 h-8 text-muted-foreground" />
              </div>
              <Heading level="h3" className="text-lg font-medium text-foreground mb-2">
                No Cancellation Policies
              </Heading>
              <Text className="text-muted-foreground max-w-md mx-auto">
                This hotel doesn't have any cancellation policies defined yet.
                {hasPermission("hotel_management:create")
                  ? "Policies can be added in the hotel management section."
                  : "Contact an administrator to add cancellation policies."
                }
              </Text>
              {hasPermission("hotel_management:create") && (
                <Button
                  variant="secondary"
                  onClick={handleAddPolicy}
                  className="mt-4"
                >
                  <Plus className="w-4 h-4 mr-2" />
                  Add First Policy
                </Button>
              )}
            </div>
          )}
        </div>
      </Container>

      {/* Add Policy Modal */}
      <FocusModal open={isAddPolicyOpen} onOpenChange={setIsAddPolicyOpen}>
        <FocusModal.Content className="cancellation-policy-modal fixed right-0 top-0 w-[600px] max-w-[90vw] h-full bg-white shadow-2xl overflow-hidden m-0 rounded-none z-[100]">
          <CancellationPolicyForm
            hotelId={hotel.id}
            onSubmit={handleSavePolicy}
            onCancel={() => setIsAddPolicyOpen(false)}
            selectedPolicy={null as any}
          />
        </FocusModal.Content>
      </FocusModal>

      {/* Edit Policy Modal */}
      <FocusModal open={isEditPolicyOpen} onOpenChange={setIsEditPolicyOpen}>
        <FocusModal.Content className="cancellation-policy-modal fixed right-0 top-0 w-[600px] max-w-[90vw] h-full bg-white shadow-2xl overflow-hidden m-0 rounded-none z-[100]">
          {selectedPolicy && (
            <CancellationPolicyForm
              initialData={selectedPolicy}
              hotelId={hotel.id}
              onSubmit={handleSavePolicy}
              onCancel={() => setIsEditPolicyOpen(false)}
              selectedPolicy={selectedPolicy}
            />
          )}
        </FocusModal.Content>
      </FocusModal>

      {/* Delete Confirmation Dialog */}
      {isDeleteDialogOpen && (
        <div className="fixed inset-0 z-[100] flex items-center justify-center">
          {/* Backdrop */}
          <div
            className="absolute inset-0 bg-black/50 backdrop-blur-sm"
            onClick={() => {
              setIsDeleteDialogOpen(false);
              setSelectedPolicy(null);
            }}
          />

          {/* Modal Content */}
          <div className="relative bg-white rounded-lg shadow-xl max-w-md w-full mx-4 overflow-hidden">
            {/* Header */}
            <div className="px-6 py-4 border-b border-gray-200">
              <div className="flex items-center justify-between">
                <h2 className="text-lg font-semibold text-red-600">
                  Delete Cancellation Policy
                </h2>
                <button
                  onClick={() => {
                    setIsDeleteDialogOpen(false);
                    setSelectedPolicy(null);
                  }}
                  className="text-gray-400 hover:text-gray-600 transition-colors"
                >
                  <XCircle className="w-5 h-5" />
                </button>
              </div>
            </div>

            {/* Body */}
            <div className="px-6 py-6">
              <div className="flex items-start gap-4 mb-4">
                <div className="w-12 h-12 rounded-full bg-red-100 flex items-center justify-center flex-shrink-0">
                  <AlertTriangle className="w-6 h-6 text-red-600" />
                </div>
                <div className="flex-1">
                  <h3 className="font-medium text-gray-900 mb-1">
                    Are you sure you want to delete this policy?
                  </h3>
                  {selectedPolicy && (
                    <p className="text-sm text-gray-600 mb-2">
                      Policy: <span className="font-medium">"{selectedPolicy.name}"</span>
                    </p>
                  )}
                  <p className="text-sm text-gray-500">
                    This action cannot be undone. The cancellation policy will be permanently removed.
                  </p>
                </div>
              </div>
            </div>

            {/* Footer */}
            <div className="px-6 py-4 bg-gray-50 border-t border-gray-200 flex justify-end gap-3">
              <button
                type="button"
                onClick={() => {
                  setIsDeleteDialogOpen(false);
                  setSelectedPolicy(null);
                }}
                disabled={deletePolicyMutation.isPending}
                className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              >
                Cancel
              </button>
              <button
                type="button"
                onClick={confirmDelete}
                disabled={deletePolicyMutation.isPending}
                className="px-4 py-2 text-sm font-medium text-white bg-red-600 border border-transparent rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center gap-2"
              >
                {deletePolicyMutation.isPending && (
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                )}
                {deletePolicyMutation.isPending ? "Deleting..." : "Delete Policy"}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default AmenitiesTab;
