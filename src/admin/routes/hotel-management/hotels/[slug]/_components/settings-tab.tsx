import React from "react";
import { Contain<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, Switch, toast } from "@camped-ai/ui";
import { 
  Settings, 
  Calendar, 
  User, 
  Eye, 
  EyeOff, 
  Trash2,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON> as LinkIcon,
  Star,
  Shield,
  DollarSign
} from "lucide-react";
import { HotelData } from "../../../../../types";
import Prompt from "../../../../../components/prompt";

interface HotelSettingsTabProps {
  hotel: HotelData | null;
  onDelete: () => void;
  deleteOpen: boolean;
  setDeleteOpen: (open: boolean) => void;
  hasDeletePermission: boolean;
  hasEditPermission: boolean;
}

const HotelSettingsTab: React.FC<HotelSettingsTabProps> = ({
  hotel,
  onDelete,
  deleteOpen,
  setDeleteOpen,
  hasDeletePermission,
  hasEditPermission,
}) => {
  const [savingVisibility, setSavingVisibility] = React.useState<null | "active" | "featured" | "pets">(null);

  const updateVisibility = async (field: "is_active" | "is_featured" | "is_pets_allowed", value: boolean) => {
    if (!hotel?.id) return;
    try {
      setSavingVisibility(
        field === "is_active" ? "active" : field === "is_featured" ? "featured" : "pets"
      );
      const res = await fetch(`/admin/hotel-management/hotels`, {
        method: "PUT",
        credentials: "include",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ id: hotel.id, [field]: value }),
      });
      if (!res.ok) {
        const text = await res.text().catch(() => "");
        throw new Error(text || "Failed to update visibility");
      }
      (hotel as any)[field] = value;
      toast.success("Updated", { description: "Visibility settings updated" });
    } catch (e: any) {
      toast.error("Error", { description: e?.message || "Failed to update" });
    } finally {
      setSavingVisibility(null);
    }
  };
  if (!hotel) {
    return (
      <Container className="p-6">
        <Text className="text-muted-foreground">Loading hotel settings...</Text>
      </Container>
    );
  }

  const formatDate = (dateString: string | undefined) => {
    if (!dateString) return "Not available";
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  return (
    <div className="space-y-6">
      {/* Basic Settings */}
      <Container className="divide-y p-0">
        <div className="px-6 py-4 flex items-center gap-3">
          <Settings className="w-5 h-5 text-muted-foreground flex-shrink-0" />
          <Heading
            level="h2"
            className="text-lg font-medium text-foreground"
          >
            Hotel Settings
          </Heading>
        </div>
        <div className="p-6 space-y-6">
          {/* Handle */}
          <div className="space-y-2">
            <div className="flex items-center gap-2">
              <LinkIcon className="w-4 h-4 text-muted-foreground" />
              <Text className="text-sm font-medium text-foreground">Handle (URL Slug)</Text>
            </div>
            <div className="p-3 bg-muted/50 rounded-lg border border-border">
              <Text className="text-sm font-mono text-foreground">{hotel.handle}</Text>
            </div>
          </div>
        </div>
      </Container>

      {/* Visibility Settings - separate container */}
      <Container className="divide-y p-0">
        <div className="px-6 py-4 flex items-center gap-3">
          {(hotel.is_active ? <Eye className="w-5 h-5 text-green-600" /> : <EyeOff className="w-5 h-5 text-red-600" />)}
          <Heading level="h2" className="text-lg font-medium text-foreground">
            Visibility Settings
          </Heading>
        </div>
        <div className="p-6">
          <div className="flex flex-col divide-y rounded-lg border border-ui-border-base overflow-hidden">
            {/* Active */}
            <div className="flex items-center justify-between px-4 py-3 bg-background">
              <div className="flex items-center gap-3">
                {(hotel.is_active ? <Eye className="w-4 h-4 text-green-600" /> : <EyeOff className="w-4 h-4 text-red-600" />)}
                <div>
                  <Text className="text-sm font-medium text-foreground">Active</Text>
                  <Text className="text-xs text-muted-foreground">
                    When active, the hotel will be visible to users
                  </Text>
                </div>
              </div>
              <Switch
                checked={!!hotel.is_active}
                disabled={!hasEditPermission || savingVisibility !== null}
                onCheckedChange={(val) => updateVisibility("is_active", !!val)}
              />
            </div>

            {/* Featured */}
            <div className="flex items-center justify-between px-4 py-3 bg-background">
              <div className="flex items-center gap-3">
                <Star className={`w-4 h-4 ${hotel.is_featured ? 'text-yellow-600' : 'text-gray-400'}`} />
                <div>
                  <Text className="text-sm font-medium text-foreground">Featured</Text>
                  <Text className="text-xs text-muted-foreground">
                    Featured hotels will be highlighted and shown prominently to users
                  </Text>
                </div>
              </div>
              <Switch
                checked={!!hotel.is_featured}
                disabled={!hasEditPermission || savingVisibility !== null}
                onCheckedChange={(val) => updateVisibility("is_featured", !!val)}
              />
            </div>

            {/* Pets Allowed */}
            <div className="flex items-center justify-between px-4 py-3 bg-background">
              <div className="flex items-center gap-3">
                <Shield className={`w-4 h-4 ${hotel.is_pets_allowed ? 'text-green-600' : 'text-gray-400'}`} />
                <div>
                  <Text className="text-sm font-medium text-foreground">Pets Allowed</Text>
                  <Text className="text-xs text-muted-foreground">
                    Allow guests to bring pets to this hotel
                  </Text>
                </div>
              </div>
              <Switch
                checked={!!hotel.is_pets_allowed}
                disabled={!hasEditPermission || savingVisibility !== null}
                onCheckedChange={(val) => updateVisibility("is_pets_allowed", !!val)}
              />
            </div>
          </div>
        </div>
      </Container>

      {/* Hotel Information section removed as requested. Rating moved to Overview tab. */}

      {/* Metadata */}
      <Container className="divide-y p-0">
        <div className="px-6 py-4 flex items-center gap-3">
          <Calendar className="w-5 h-5 text-muted-foreground flex-shrink-0" />
          <Heading
            level="h2"
            className="text-lg font-medium text-foreground"
          >
            Metadata
          </Heading>
        </div>
        <div className="p-6 space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-1">
              <Text className="text-xs text-muted-foreground">Created</Text>
              <Text className="text-sm font-medium text-foreground">
                {formatDate(hotel.created_at)}
              </Text>
            </div>
            <div className="space-y-1">
              <Text className="text-xs text-muted-foreground">Last Updated</Text>
              <Text className="text-sm font-medium text-foreground">
                {formatDate(hotel.updated_at)}
              </Text>
            </div>
          </div>
          
          <div className="space-y-1">
            <Text className="text-xs text-muted-foreground">Hotel ID</Text>
            <div className="p-2 bg-muted/50 rounded border border-border">
              <Text className="text-sm font-mono text-foreground">{hotel.id}</Text>
            </div>
          </div>
        </div>
      </Container>

      {/* Danger Zone */}
      {/*hasDeletePermission && (
        <Container className="shadow-sm hover:shadow-md transition-shadow duration-200 p-0 rounded-lg overflow-hidden bg-card border-red-200 dark:border-red-800 border border-border">
          <div className="px-6 py-4 border-b border-red-200 dark:border-red-800 flex items-center gap-3 bg-red-50 dark:bg-red-950/30">
            <AlertTriangle className="w-5 h-5 text-red-600 dark:text-red-400 flex-shrink-0" />
            <Heading
              level="h2"
              className="text-lg font-medium text-red-800 dark:text-red-200"
            >
              Danger Zone
            </Heading>
          </div>
          <div className="p-6">
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
              <div>
                <Text className="font-medium text-foreground mb-1">
                  Delete Hotel
                </Text>
                <Text className="text-sm text-muted-foreground">
                  Permanently delete this hotel and all associated data. This action cannot be undone.
                </Text>
              </div>
              <Prompt
                open={deleteOpen}
                onOpenChange={setDeleteOpen}
                title="Delete Hotel"
                description="Are you sure you want to delete this hotel? This action cannot be undone and will remove all associated rooms, bookings, and data."
                onDelete={onDelete}
                trigger={
                  <Button
                    variant="danger"
                    size="small"
                    className="flex items-center gap-2"
                  >
                    <Trash2 className="w-4 h-4" />
                    Delete Hotel
                  </Button>
                }
              />
            </div>
          </div>
        </Container>
      )*/}
    </div>
  );
};

export default HotelSettingsTab;
