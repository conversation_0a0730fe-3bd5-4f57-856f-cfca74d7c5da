import React from "react";
import { Container, Heading, Text, But<PERSON>, Badge } from "@camped-ai/ui";
import { Bed, Plus, Calendar, Settings } from "lucide-react";
import { HotelData } from "../../../../../types";
import { Link } from "react-router-dom";

interface RoomsTabProps {
  hotel: HotelData | null;
  hasCreatePermission: boolean;
  hasEditPermission: boolean;
}

const RoomsTab: React.FC<RoomsTabProps> = ({
  hotel,
  hasCreatePermission,
  hasEditPermission,
}) => {
  if (!hotel) {
    return (
      <Container className="p-6">
        <Text className="text-muted-foreground">Loading hotel details...</Text>
      </Container>
    );
  }

  return (
    <Container className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <div className="w-10 h-10 rounded-full bg-blue-100 dark:bg-blue-900/50 flex items-center justify-center">
            <Bed className="w-5 h-5 text-blue-600 dark:text-blue-400" />
          </div>
          <div>
            <Heading level="h2" className="text-xl font-semibold text-foreground">
              Room Management
            </Heading>
            <Text className="text-sm text-muted-foreground mt-1">
              Manage room types, availability, and configurations
            </Text>
          </div>
        </div>
      </div>

      {/* Room Types */}
      {hotel.room_types && hotel.room_types.length > 0 && (
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <Heading level="h3" className="text-lg font-medium">
              Room Types
            </Heading>
            <Badge variant="secondary" className="text-xs">
              {hotel.room_types.length} types
            </Badge>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {hotel.room_types.map((roomType, index) => (
              <div
                key={index}
                className="p-4 bg-card rounded-lg border border-border hover:border-muted-foreground/20 transition-colors"
              >
                <div className="flex items-center gap-3">
                  <div className="w-8 h-8 rounded-full bg-green-100 dark:bg-green-900/50 flex items-center justify-center flex-shrink-0">
                    <Bed className="w-4 h-4 text-green-600 dark:text-green-400" />
                  </div>
                  <div className="min-w-0 flex-1">
                    <Text className="font-medium text-foreground break-words">
                      {roomType}
                    </Text>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Quick Actions */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {/* Availability Management */}
        <Link
          to={`/app/hotel-management/hotels/${hotel.handle}/availability`}
          className="block p-6 bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-blue-950/50 dark:to-indigo-950/50 rounded-lg border border-blue-200 dark:border-blue-800 hover:border-blue-300 dark:hover:border-blue-700 transition-all duration-200 hover:shadow-md group"
        >
          <div className="flex items-center gap-4">
            <div className="w-12 h-12 rounded-full bg-blue-100 dark:bg-blue-900/50 flex items-center justify-center group-hover:scale-110 transition-transform duration-200">
              <Calendar className="w-6 h-6 text-blue-600 dark:text-blue-400" />
            </div>
            <div className="flex-1">
              <Text className="font-semibold text-blue-800 dark:text-blue-200 mb-1">
                Room Availability
              </Text>
              <Text className="text-sm text-blue-600 dark:text-blue-300">
                Manage room inventory and availability calendar
              </Text>
            </div>
          </div>
        </Link>

        {/* New Availability System */}
        <Link
          to={`/app/hotel-management/hotels/${hotel.handle}/availability-new`}
          className="block p-6 bg-gradient-to-br from-green-50 to-emerald-50 dark:from-green-950/50 dark:to-emerald-950/50 rounded-lg border border-green-200 dark:border-green-800 hover:border-green-300 dark:hover:border-green-700 transition-all duration-200 hover:shadow-md group"
        >
          <div className="flex items-center gap-4">
            <div className="w-12 h-12 rounded-full bg-green-100 dark:bg-green-900/50 flex items-center justify-center group-hover:scale-110 transition-transform duration-200">
              <Settings className="w-6 h-6 text-green-600 dark:text-green-400" />
            </div>
            <div className="flex-1">
              <Text className="font-semibold text-green-800 dark:text-green-200 mb-1">
                Advanced Availability
              </Text>
              <Text className="text-sm text-green-600 dark:text-green-300">
                Timeline view and bulk management tools
              </Text>
            </div>
          </div>
        </Link>

        {/* Room Configuration */}
        <div className="p-6 bg-gradient-to-br from-purple-50 to-violet-50 dark:from-purple-950/50 dark:to-violet-950/50 rounded-lg border border-purple-200 dark:border-purple-800">
          <div className="flex items-center gap-4">
            <div className="w-12 h-12 rounded-full bg-purple-100 dark:bg-purple-900/50 flex items-center justify-center">
              <Bed className="w-6 h-6 text-purple-600 dark:text-purple-400" />
            </div>
            <div className="flex-1">
              <Text className="font-semibold text-purple-800 dark:text-purple-200 mb-1">
                Room Configuration
              </Text>
              <Text className="text-sm text-purple-600 dark:text-purple-300">
                Configure room types and properties
              </Text>
            </div>
          </div>
        </div>
      </div>

      {/* Empty State */}
      {(!hotel.room_types || hotel.room_types.length === 0) && (
        <div className="text-center py-12">
          <div className="w-16 h-16 rounded-full bg-muted flex items-center justify-center mx-auto mb-4">
            <Bed className="w-8 h-8 text-muted-foreground" />
          </div>
          <Heading level="h3" className="text-lg font-medium text-foreground mb-2">
            No Room Types Configured
          </Heading>
          <Text className="text-muted-foreground mb-6 max-w-md mx-auto">
            Start by configuring room types for this hotel. You can add different room categories, set pricing, and manage availability.
          </Text>
          {hasCreatePermission && (
            <Button size="small" className="inline-flex items-center gap-2">
              <Plus className="w-4 h-4" />
              Configure Room Types
            </Button>
          )}
        </div>
      )}
    </Container>
  );
};

export default RoomsTab;
