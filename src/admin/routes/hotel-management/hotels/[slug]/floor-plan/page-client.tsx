import { useParams, useNavigate } from "react-router-dom";
import { useState, useEffect } from "react";
import {
  Container,
  Heading,
  Text,
  Button,
  Toaster,
  toast,
  Badge,
} from "@camped-ai/ui";
import { ChevronLeft } from "@camped-ai/icons";

import { ArrowLeft, ArrowRight, ArrowUp, Link as LinkIcon } from "lucide-react";
import { Hotel, Bed, Map } from "lucide-react";
import FloorPlanVisualization from "../../../../../components/floor-plan-visualization";

interface Room {
  id: string;
  name: string;
  room_number: string;
  status: string;
  floor: string;
  notes?: string;
  is_active: boolean;
  left_room?: string;
  opposite_room?: string;
  connected_room?: string;
  right_room?: string;
  room_config_id: string;
  hotel_id: string;
  options?: Record<string, string>;
}

interface HotelDetails {
  id: string;
  name: string;
  description?: string;
  address?: string;
  city?: string;
  country?: string;
  is_active: boolean;
}

const PageClient = () => {
  const { slug } = useParams();
  const navigate = useNavigate();
  const [hotel, setHotel] = useState<HotelDetails | null>(null);
  const [rooms, setRooms] = useState<Room[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const fetchHotelDetails = async () => {
      try {
        const response = await fetch(`/admin/hotel-management/hotels/${slug}`);
        if (!response.ok) {
          throw new Error("Failed to fetch hotel details");
        }
        const data = await response.json();
        console.log("Hotel data:", data);
        setHotel(data.hotel?.[0]);
      } catch (error) {
        console.error("Error fetching hotel details:", error);
        toast.error("Failed to load hotel details");
      }
    };

    const fetchRooms = async () => {
      try {
        const response = await fetch(`/admin/direct-rooms?hotel_id=${slug}`);
        if (!response.ok) {
          throw new Error("Failed to fetch rooms");
        }
        const data = await response.json();
        setRooms(data.rooms || []);
      } catch (error) {
        console.error("Error fetching rooms:", error);
        toast.error("Failed to load rooms");
      } finally {
        setIsLoading(false);
      }
    };

    if (slug) {
      fetchHotelDetails();
      fetchRooms();
    }
  }, [slug]);

  const handleRoomClick = (roomId: string) => {
    // Find the room configuration ID for this room
    const room = rooms.find((r) => r.id === roomId);
    if (room) {
      navigate(
        `/hotel-management/hotels/${slug}/room-configs/${room.room_config_id}/rooms`
      );
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className="p-6 max-w-7xl mx-auto">
      <Toaster />

      {/* Header */}
      <div className="mb-6">
        <div className="flex items-center gap-2 mb-2">
          <Button
            variant="secondary"
            size="small"
            onClick={() => navigate(`/hotel-management/hotels/${slug}`)}
          >
            <ChevronLeft className="w-4 h-4" />
            Back to Hotel
          </Button>
        </div>

        <div className="flex justify-between items-center">
          <div className="flex items-center gap-3">
            <div className="bg-indigo-100 p-2 rounded-lg">
              <Hotel className="w-6 h-6 text-indigo-600" />
            </div>
            <div>
              <Heading level="h1" className="text-2xl font-bold">
                {hotel?.name || "Hotel"}
              </Heading>
              <Text className="text-gray-500">
                {hotel?.city}
                {hotel?.city && hotel?.country ? ", " : ""}
                {hotel?.country}
              </Text>
            </div>
            <Badge color={hotel?.is_active ? "green" : "grey"}>
              {hotel?.is_active ? "Active" : "Inactive"}
            </Badge>
          </div>

          <div className="flex items-center gap-2">
            <Button
              variant="secondary"
              onClick={() => navigate(`/hotel-management/hotels/${slug}/rooms`)}
            >
              <Bed className="w-4 h-4 mr-2" />
              View All Rooms
            </Button>
          </div>
        </div>
      </div>

      {/* Floor Plan */}
      <div className="mb-6 flex items-center gap-2">
        <Map className="w-5 h-5 text-gray-500" />
        <Heading level="h2" className="text-xl">
          Interactive Floor Plan
        </Heading>
      </div>

      {rooms.length === 0 ? (
        <Container className="p-8 text-center bg-white border border-gray-200 rounded-lg">
          <Text className="text-gray-500 mb-4">
            No rooms found for this hotel
          </Text>
          <Button
            variant="secondary"
            onClick={() =>
              navigate(`/hotel-management/hotels/${slug}/room-configs`)
            }
          >
            Add Room Configurations
          </Button>
        </Container>
      ) : (
        <FloorPlanVisualization
          rooms={rooms}
          hotelId={slug || ""}
          onRoomClick={handleRoomClick}
        />
      )}

      {/* Room Legend */}
      <Container className="p-6 border border-gray-200 rounded-lg bg-white shadow-sm mt-6">
        <Heading level="h3" className="text-lg mb-4">
          Room Information
        </Heading>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <div className="bg-gray-50 p-4 rounded-lg">
            <Text className="font-semibold mb-2">Total Rooms</Text>
            <Text className="text-2xl">{rooms.length}</Text>
          </div>

          <div className="bg-gray-50 p-4 rounded-lg">
            <Text className="font-semibold mb-2">Available Rooms</Text>
            <Text className="text-2xl text-green-600">
              {
                rooms.filter(
                  (room) => room.status.toLowerCase() === "available"
                ).length
              }
            </Text>
          </div>

          <div className="bg-gray-50 p-4 rounded-lg">
            <Text className="font-semibold mb-2">Occupied Rooms</Text>
            <Text className="text-2xl text-blue-600">
              {
                rooms.filter(
                  (room) =>
                    room.status.toLowerCase() === "occupied" ||
                    room.status.toLowerCase() === "booked"
                ).length
              }
            </Text>
          </div>

          <div className="bg-gray-50 p-4 rounded-lg">
            <Text className="font-semibold mb-2">Maintenance/Cleaning</Text>
            <Text className="text-2xl text-orange-600">
              {
                rooms.filter(
                  (room) =>
                    room.status.toLowerCase() === "maintenance" ||
                    room.status.toLowerCase() === "cleaning"
                ).length
              }
            </Text>
          </div>
        </div>

        <div className="mt-6">
          <Text className="font-semibold mb-2">Room Relationships</Text>
          <Text className="text-sm text-gray-600 mb-4">
            The floor plan visualizes room relationships based on their spatial
            connections:
          </Text>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="flex items-start gap-2">
              <div className="bg-gray-100 p-2 rounded-full">
                <ArrowLeft className="w-4 h-4 text-gray-600" />
              </div>
              <div>
                <Text className="font-medium">Left Room</Text>
                <Text className="text-sm text-gray-600">
                  Indicates a room located to the left of the current room
                </Text>
              </div>
            </div>

            <div className="flex items-start gap-2">
              <div className="bg-gray-100 p-2 rounded-full">
                <ArrowRight className="w-4 h-4 text-gray-600" />
              </div>
              <div>
                <Text className="font-medium">Right Room</Text>
                <Text className="text-sm text-gray-600">
                  Indicates a room located to the right of the current room
                </Text>
              </div>
            </div>

            <div className="flex items-start gap-2">
              <div className="bg-gray-100 p-2 rounded-full">
                <ArrowUp className="w-4 h-4 text-gray-600" />
              </div>
              <div>
                <Text className="font-medium">Opposite Room</Text>
                <Text className="text-sm text-gray-600">
                  Indicates a room located across from the current room
                </Text>
              </div>
            </div>

            <div className="flex items-start gap-2">
              <div className="bg-gray-100 p-2 rounded-full">
                <LinkIcon className="w-4 h-4 text-purple-600" />
              </div>
              <div>
                <Text className="font-medium">Connected Room</Text>
                <Text className="text-sm text-gray-600">
                  Indicates a room that is directly connected to the current
                  room
                </Text>
              </div>
            </div>
          </div>
        </div>
      </Container>
    </div>
  );
};

export default PageClient;
