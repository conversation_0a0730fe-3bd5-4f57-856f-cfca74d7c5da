import { useNavigate } from "react-router-dom";
import { useState } from "react";
import { toast } from "@camped-ai/ui";
import HotelCreateForm from "./page-client";

export type HotelFormData = {
  name: string;
  handle: string;
  description?: string;
  is_active: boolean;
  is_featured: boolean;
  website?: string;
  email?: string;
  destination_id: string;
  rating?: number;
  total_reviews?: number;
  notes?: string;
  location?: string;
  address?: string;
  phone_number?: string;
  timezone?: string;
  available_languages?: string[];
  tax_type?: string;
  tax_number?: string;
  tags?: string[];
  amenities?: string[];
  rules?: string[];
  safety_measures?: string[];
  currency_code?: string;
  margin?: number;
  check_in_time?: string;
  check_out_time?: string;
  is_pets_allowed?: boolean;
  parent_category_id?: string;
  media?: any[];
  faqs?: Array<{
    id?: string;
    question: string;
    answer: string;
  }>;
};

const HotelCreatePage = () => {
  const navigate = useNavigate();
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleSubmit = async (data: HotelFormData) => {
    setIsSubmitting(true);

    try {
      // Helper function to clean undefined/empty values
      const cleanValue = (value: any) => {
        if (value === undefined || value === null || value === "") {
          return undefined;
        }
        return value;
      };

      // Prepare the data for submission (without media)
      const submitData = {
        name: data.name,
        handle: data.handle,
        description: cleanValue(data.description),
        is_active: data.is_active,
        is_featured: data.is_featured || false,
        website: cleanValue(data.website),
        email: cleanValue(data.email),
        destination_id: data.destination_id,
        rating: cleanValue(data.rating),
        total_reviews: cleanValue(data.total_reviews),
        notes: cleanValue(data.notes),
        location: cleanValue(data.location),
        address: cleanValue(data.address),
        phone_number: cleanValue(data.phone_number),
        timezone: cleanValue(data.timezone),
        available_languages: data.available_languages?.length
          ? data.available_languages
          : undefined,
        tax_type: cleanValue(data.tax_type),
        tax_number: cleanValue(data.tax_number),
        tags: data.tags?.length ? data.tags : undefined,
        amenities: data.amenities?.length ? data.amenities : undefined,
        rules: data.rules?.length ? data.rules : undefined,
        safety_measures: data.safety_measures?.length
          ? data.safety_measures
          : undefined,
        currency: cleanValue(data.currency_code),
        margin: cleanValue(data.margin),
        check_in_time: cleanValue(data.check_in_time),
        check_out_time: cleanValue(data.check_out_time),
        is_pets_allowed: data.is_pets_allowed || false,
        parent_category_id: cleanValue(data.parent_category_id),
      };

      console.log("Submitting hotel data:", submitData);

      // Step 1: Create the hotel first
      const response = await fetch("/admin/hotel-management/hotels", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(submitData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || "Failed to create hotel");
      }

      const result = await response.json();
      console.log("Hotel created successfully:", result);

      const hotelId = result.hotel?.id;

      // Step 2: Upload images if any
      if (data.media?.length && hotelId) {
        const mediaWithFiles = data.media.filter(media => media.file);

        if (mediaWithFiles.length > 0) {
          console.log(`Uploading ${mediaWithFiles.length} images for hotel ${hotelId}`);

          const formData = new FormData();

          // Add files to FormData
          mediaWithFiles.forEach((media) => {
            formData.append('files', media.file);
          });

          // Add metadata for thumbnail information
          const metadata = mediaWithFiles.map(media => ({
            isThumbnail: media.isThumbnail || false,
          }));
          formData.append('metadata', JSON.stringify(metadata));

          try {
            const uploadResponse = await fetch(
              `/admin/hotel-management/hotels/${hotelId}/upload`,
              {
                method: "POST",
                body: formData,
              }
            );

            if (!uploadResponse.ok) {
              const uploadError = await uploadResponse.text();
              console.error("Failed to upload images:", uploadError);
              toast.error("Hotel created but failed to upload images. You can upload them later.");
            } else {
              const uploadResult = await uploadResponse.json();
              console.log("Images uploaded successfully:", uploadResult);

              // Set thumbnail if specified
              const thumbnailMedia = mediaWithFiles.find(media => media.isThumbnail);
              if (thumbnailMedia && uploadResult.hotel_images?.length) {
                const thumbnailImage = uploadResult.hotel_images[0]; // Assuming first uploaded image

                try {
                  await fetch(`/admin/hotel-management/hotels/${hotelId}/thumbnail`, {
                    method: "POST",
                    headers: {
                      "Content-Type": "application/json",
                    },
                    body: JSON.stringify({ image_id: thumbnailImage.id }),
                  });
                } catch (thumbnailError) {
                  console.error("Failed to set thumbnail:", thumbnailError);
                }
              }
            }
          } catch (uploadError) {
            console.error("Error uploading images:", uploadError);
            toast.error("Hotel created but failed to upload images. You can upload them later.");
          }
        }
      }

      toast.success("Hotel created successfully!");

      // Navigate to the hotel list or detail page
      navigate("/hotel-management/hotels");
    } catch (error) {
      console.error("Error creating hotel:", error);
      toast.error(
        error instanceof Error ? error.message : "Failed to create hotel"
      );
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleCancel = () => {
    navigate("/hotel-management/hotels");
  };

  return (
    <HotelCreateForm
      onSubmit={handleSubmit}
      onCancel={handleCancel}
      isSubmitting={isSubmitting}
    />
  );
};

export default HotelCreatePage;
