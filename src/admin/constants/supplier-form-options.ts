// Supplier form dropdown options and constants

export const REGIONS = [
  { value: "North America", label: "North America" },
  { value: "South America", label: "South America" },
  { value: "Europe", label: "Europe" },
  { value: "Asia", label: "Asia" },
  { value: "Africa", label: "Africa" },
  { value: "Oceania", label: "Oceania" },
  { value: "Middle East", label: "Middle East" },
];

export const TIMEZONES = [
  { value: "UTC-12 (Baker Island)", label: "UTC-12 (Baker Island)" },
  { value: "UTC-11 (American Samoa)", label: "UTC-11 (American Samoa)" },
  { value: "UTC-10 (Hawaii)", label: "UTC-10 (Hawaii)" },
  { value: "UTC-9 (Alaska)", label: "UTC-9 (Alaska)" },
  { value: "UTC-8 (Pacific Time)", label: "UTC-8 (Pacific Time)" },
  { value: "UTC-7 (Mountain Time)", label: "UTC-7 (Mountain Time)" },
  { value: "UTC-6 (Central Time)", label: "UTC-6 (Central Time)" },
  { value: "UTC-5 (Eastern Time)", label: "UTC-5 (Eastern Time)" },
  { value: "UTC-4 (Atlantic Time)", label: "UTC-4 (Atlantic Time)" },
  { value: "UTC-3 (Argentina)", label: "UTC-3 (Argentina)" },
  { value: "UTC-2 (South Georgia)", label: "UTC-2 (South Georgia)" },
  { value: "UTC-1 (Azores)", label: "UTC-1 (Azores)" },
  { value: "UTC+0 (London, Dublin)", label: "UTC+0 (London, Dublin)" },
  { value: "UTC+1 (Central Europe)", label: "UTC+1 (Central Europe)" },
  { value: "UTC+2 (Eastern Europe)", label: "UTC+2 (Eastern Europe)" },
  { value: "UTC+3 (Moscow)", label: "UTC+3 (Moscow)" },
  { value: "UTC+4 (Dubai)", label: "UTC+4 (Dubai)" },
  { value: "UTC+5 (Pakistan)", label: "UTC+5 (Pakistan)" },
  { value: "UTC+5:30 (India)", label: "UTC+5:30 (India)" },
  { value: "UTC+6 (Bangladesh)", label: "UTC+6 (Bangladesh)" },
  { value: "UTC+7 (Thailand)", label: "UTC+7 (Thailand)" },
  { value: "UTC+8 (China, Singapore)", label: "UTC+8 (China, Singapore)" },
  { value: "UTC+9 (Japan, Korea)", label: "UTC+9 (Japan, Korea)" },
  { value: "UTC+10 (Australia East)", label: "UTC+10 (Australia East)" },
  { value: "UTC+11 (Solomon Islands)", label: "UTC+11 (Solomon Islands)" },
  { value: "UTC+12 (New Zealand)", label: "UTC+12 (New Zealand)" },
];

export const LANGUAGES = [
  { value: "English", label: "English" },
  { value: "French", label: "French" },
  { value: "German", label: "German" },
  { value: "Spanish", label: "Spanish" },
  { value: "Italian", label: "Italian" },
  { value: "Portuguese", label: "Portuguese" },
  { value: "Russian", label: "Russian" },
  { value: "Chinese", label: "Chinese" },
  { value: "Japanese", label: "Japanese" },
  { value: "Korean", label: "Korean" },
];

export const PAYMENT_METHODS = [
  { value: "Bank Transfer", label: "Bank Transfer" },
  { value: "PayPal", label: "PayPal" },
  { value: "Stripe", label: "Stripe" },
  { value: "Wire Transfer", label: "Wire Transfer" },
  { value: "Check", label: "Check" },
  { value: "Cash", label: "Cash" },
  { value: "Manual", label: "Manual" },
];

export const PAYOUT_TERMS = [
  {
    value: "Immediate Payment",
    label: "Immediate Payment",
    description: "Paid as soon as the invoice is received",
  },
  {
    value: "Due on Receipt",
    label: "Due on Receipt",
    description: "Payment is due upon invoice delivery",
  },
  {
    value: "Net 7",
    label: "Net 7",
    description: "Paid within 7 days of invoice",
  },
  { value: "Net 15", label: "Net 15", description: "Paid within 15 days" },
  { value: "Net 30", label: "Net 30", description: "Paid within 30 days" },
  { value: "Net 45", label: "Net 45", description: "Paid within 45 days" },
  { value: "Net 60", label: "Net 60", description: "Paid within 60 days" },
  { value: "Net 90", label: "Net 90", description: "Paid within 90 days" },
  {
    value: "End of Month (EOM)",
    label: "End of Month (EOM)",
    description: "Payment is due at the end of the current month",
  },
  {
    value: "Custom Terms",
    label: "Custom Terms",
    description: "A custom-negotiated agreement",
  },
];

export const CURRENCIES = [
  { value: "CHF", label: "CHF - Swiss Franc" },
  { value: "EUR", label: "EUR - Euro" },
  { value: "USD", label: "USD - US Dollar" },
  { value: "GBP", label: "GBP - British Pound" },
  { value: "CAD", label: "CAD - Canadian Dollar" },
  { value: "AUD", label: "AUD - Australian Dollar" },
  { value: "JPY", label: "JPY - Japanese Yen" },
];

// Utility function to get currency display name from code
export const getCurrencyDisplayName = (currencyCode: string): string => {
  const currency = CURRENCIES.find((c) => c.value === currencyCode);
  return currency ? currency.value : currencyCode;
};

// Utility function to get currency code from display name (for backward compatibility)
export const getCurrencyCodeFromDisplayName = (displayName: string): string => {
  const currency = CURRENCIES.find((c) => c.label === displayName);
  return currency ? currency.value : displayName;
};

export const SUPPLIER_STATUSES = [
  { value: "Active", label: "Active" },
  { value: "Inactive", label: "Inactive" },
];

export const SUPPLIER_TYPES = [
  { value: "Company", label: "Company" },
  { value: "Individual", label: "Individual" },
];
