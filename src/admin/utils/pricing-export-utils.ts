/**
 * DEPRECATED: This utility has been replaced by server-side export API
 * Use the new PricingExportModal component instead
 * 
 * The export functionality is now handled by:
 * - API: /admin/hotel-management/hotels/[id]/pricing/export
 * - Modal: src/admin/components/hotel/pricing/pricing-export-modal.tsx
 */

// Export a simple deprecation notice
export const DEPRECATED_NOTICE = 'This utility has been replaced by server-side export API';

// Re-export types that might be used elsewhere (for backward compatibility)
export interface ExportOptions {
  format: 'excel' | 'csv';
  includeSeasonalPricing?: boolean;
  includeCostMarginData?: boolean;
  currency?: string;
}

export interface ExportResult {
  success: boolean;
  filename: string;
  data?: Blob | string;
  error?: string;
}

// Deprecated function stubs (for backward compatibility)
export const exportHotelPricingData = () => {
  throw new Error('This function has been deprecated. Use the PricingExportModal component instead.');
};

export const downloadFile = () => {
  throw new Error('This function has been deprecated. Use the PricingExportModal component instead.');
};
