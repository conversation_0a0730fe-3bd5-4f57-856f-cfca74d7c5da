import { format, parseISO, isAfter, isBefore, isEqual } from "date-fns";

export type SeasonalPeriod = {
  id: string;
  name: string;
  start_date: string;
  end_date: string;
};

export type SeasonValidationError = {
  type: 'DUPLICATE_NAME' | 'OVERLAPPING_DATES' | 'INVALID_DATE_RANGE';
  message: string;
  conflictingSeason?: SeasonalPeriod;
};

export type SeasonValidationResult = {
  isValid: boolean;
  errors: SeasonValidationError[];
};

/**
 * Validates a new seasonal period against existing ones
 * Checks for:
 * 1. Duplicate names (case-insensitive)
 * 2. Overlapping date ranges
 * 3. Valid date range (start date before end date)
 */
export function validateSeasonalPeriod(
  newSeason: {
    name: string;
    start_date: string | Date;
    end_date: string | Date;
  },
  existingSeasons: SeasonalPeriod[]
): SeasonValidationResult {
  const errors: SeasonValidationError[] = [];

  // Normalize the new season data
  const normalizedNewSeason = {
    name: newSeason.name.trim(),
    start_date: typeof newSeason.start_date === 'string' 
      ? newSeason.start_date 
      : format(newSeason.start_date, 'yyyy-MM-dd'),
    end_date: typeof newSeason.end_date === 'string' 
      ? newSeason.end_date 
      : format(newSeason.end_date, 'yyyy-MM-dd'),
  };

  // Parse dates for comparison
  let newStartDate: Date;
  let newEndDate: Date;

  try {
    newStartDate = parseISO(normalizedNewSeason.start_date);
    newEndDate = parseISO(normalizedNewSeason.end_date);
  } catch (error) {
    errors.push({
      type: 'INVALID_DATE_RANGE',
      message: 'Invalid date format. Please use YYYY-MM-DD format.',
    });
    return { isValid: false, errors };
  }

  // Check if start date is before end date
  if (isAfter(newStartDate, newEndDate) || isEqual(newStartDate, newEndDate)) {
    errors.push({
      type: 'INVALID_DATE_RANGE',
      message: 'End date must be after start date.',
    });
  }

  // Check for duplicate names (case-insensitive)
  const duplicateName = existingSeasons.find(
    (season) => season.name.toLowerCase() === normalizedNewSeason.name.toLowerCase()
  );

  if (duplicateName) {
    errors.push({
      type: 'DUPLICATE_NAME',
      message: `A season with the name "${normalizedNewSeason.name}" already exists.`,
      conflictingSeason: duplicateName,
    });
  }

  // Check for overlapping date ranges
  for (const existingSeason of existingSeasons) {
    try {
      const existingStartDate = parseISO(existingSeason.start_date);
      const existingEndDate = parseISO(existingSeason.end_date);

      // Check if date ranges overlap
      const hasOverlap = checkDateRangeOverlap(
        newStartDate,
        newEndDate,
        existingStartDate,
        existingEndDate
      );

      if (hasOverlap) {
        errors.push({
          type: 'OVERLAPPING_DATES',
          message: `Date range overlaps with existing season "${existingSeason.name}" (${existingSeason.start_date} to ${existingSeason.end_date}).`,
          conflictingSeason: existingSeason,
        });
      }
    } catch (error) {
      console.warn(`Failed to parse dates for existing season ${existingSeason.id}:`, error);
      // Continue checking other seasons
    }
  }

  return {
    isValid: errors.length === 0,
    errors,
  };
}

/**
 * Checks if two date ranges overlap
 * Returns true if there's any overlap between the ranges
 */
function checkDateRangeOverlap(
  start1: Date,
  end1: Date,
  start2: Date,
  end2: Date
): boolean {
  // Two ranges overlap if:
  // - start1 is before or equal to end2 AND
  // - end1 is after or equal to start2
  return (
    (isBefore(start1, end2) || isEqual(start1, end2)) &&
    (isAfter(end1, start2) || isEqual(end1, start2))
  );
}

/**
 * Validates a season for editing (excludes the season being edited from validation)
 */
export function validateSeasonalPeriodForEdit(
  editingSeason: {
    id: string;
    name: string;
    start_date: string | Date;
    end_date: string | Date;
  },
  existingSeasons: SeasonalPeriod[]
): SeasonValidationResult {
  // Filter out the season being edited
  const otherSeasons = existingSeasons.filter(season => season.id !== editingSeason.id);
  
  return validateSeasonalPeriod(editingSeason, otherSeasons);
}

/**
 * Gets a user-friendly error message from validation errors
 */
export function getSeasonValidationErrorMessage(errors: SeasonValidationError[]): string {
  if (errors.length === 0) return '';
  
  if (errors.length === 1) {
    return errors[0].message;
  }
  
  // Multiple errors - combine them
  return errors.map(error => error.message).join(' ');
}

/**
 * Checks if a season name is unique (case-insensitive)
 */
export function isSeasonNameUnique(
  name: string,
  existingSeasons: SeasonalPeriod[],
  excludeId?: string
): boolean {
  const normalizedName = name.trim().toLowerCase();
  return !existingSeasons.some(
    season => 
      season.name.toLowerCase() === normalizedName && 
      season.id !== excludeId
  );
}

/**
 * Finds seasons that overlap with a given date range
 */
export function findOverlappingSeasons(
  startDate: string | Date,
  endDate: string | Date,
  existingSeasons: SeasonalPeriod[],
  excludeId?: string
): SeasonalPeriod[] {
  const start = typeof startDate === 'string' ? parseISO(startDate) : startDate;
  const end = typeof endDate === 'string' ? parseISO(endDate) : endDate;
  
  return existingSeasons.filter(season => {
    if (excludeId && season.id === excludeId) return false;
    
    try {
      const seasonStart = parseISO(season.start_date);
      const seasonEnd = parseISO(season.end_date);
      
      return checkDateRangeOverlap(start, end, seasonStart, seasonEnd);
    } catch (error) {
      console.warn(`Failed to parse dates for season ${season.id}:`, error);
      return false;
    }
  });
}
