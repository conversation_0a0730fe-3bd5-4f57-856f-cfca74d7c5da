import React from "react";
import { Package } from "lucide-react";

/**
 * Category interface for icon mapping
 */
export interface CategoryIconData {
  id: string;
  name: string;
  icon?: string | null;
  category_type?: "Product" | "Service" | "Both";
}

/**
 * Get category icon for display in tables and UI components
 * Supports both emoji icons from category.icon field and fallback icons
 * 
 * @param category - Category data object
 * @returns Object with icon type and value for rendering
 */
export const getCategoryIcon = (category?: CategoryIconData | null) => {
  // If category has a custom icon (emoji), use it
  if (category?.icon) {
    return {
      type: 'emoji' as const,
      value: category.icon,
    };
  }

  // If no category provided, use default package icon
  if (!category) {
    return {
      type: 'component' as const,
      value: Package,
    };
  }

  // Fallback icons based on category name patterns
  const categoryName = category.name.toLowerCase();
  
  // Activity and entertainment categories
  if (categoryName.includes('kids') || categoryName.includes('club')) {
    return { type: 'emoji' as const, value: '👦🏼' };
  }
  
  if (categoryName.includes('activity') || categoryName.includes('tour') || categoryName.includes('excursion')) {
    return { type: 'emoji' as const, value: '🎯' };
  }
  
  if (categoryName.includes('spa') || categoryName.includes('wellness') || categoryName.includes('massage')) {
    return { type: 'emoji' as const, value: '💆' };
  }
  
  if (categoryName.includes('gym') || categoryName.includes('fitness') || categoryName.includes('sport')) {
    return { type: 'emoji' as const, value: '🏋️‍♀️' };
  }

  // Accommodation categories
  if (categoryName.includes('accommodation') || categoryName.includes('hotel') || categoryName.includes('lodging') || categoryName.includes('room')) {
    return { type: 'emoji' as const, value: '🏨' };
  }

  // Transportation categories
  if (categoryName.includes('transfer') || categoryName.includes('transport') || categoryName.includes('vehicle') || categoryName.includes('hire')) {
    return { type: 'emoji' as const, value: '🚗' };
  }
  
  if (categoryName.includes('flight') || categoryName.includes('air') || categoryName.includes('aviation')) {
    return { type: 'emoji' as const, value: '✈️' };
  }
  
  if (categoryName.includes('cruise') || categoryName.includes('ship') || categoryName.includes('boat')) {
    return { type: 'emoji' as const, value: '🚢' };
  }

  // Food and dining categories
  if (categoryName.includes('food') || categoryName.includes('dining') || categoryName.includes('restaurant') || categoryName.includes('meal')) {
    return { type: 'emoji' as const, value: '🍽️' };
  }

  // Shopping and retail categories
  if (categoryName.includes('shop') || categoryName.includes('retail') || categoryName.includes('store')) {
    return { type: 'emoji' as const, value: '🛍️' };
  }

  // Equipment and gear categories
  if (categoryName.includes('equipment') || categoryName.includes('gear') || categoryName.includes('rental')) {
    return { type: 'emoji' as const, value: '⚙️' };
  }

  // Sub items and miscellaneous categories
  if (categoryName.includes('sub items') || categoryName.includes('sub-items') || categoryName.includes('items')) {
    return { type: 'emoji' as const, value: '📋' };
  }

  // Ski and snow sports categories
  if (categoryName.includes('ski') || categoryName.includes('snow') || categoryName.includes('passes')) {
    return { type: 'emoji' as const, value: '🎿' };
  }

  // Default fallback based on category type
  if (category.category_type === 'Service') {
    return { type: 'emoji' as const, value: '🔧' };
  } else if (category.category_type === 'Product') {
    return { type: 'emoji' as const, value: '📦' };
  }

  // Ultimate fallback - use Package component
  return {
    type: 'component' as const,
    value: Package,
  };
};

/**
 * Render category icon component for use in React components
 *
 * @param category - Category data object
 * @param className - Additional CSS classes for styling
 * @returns JSX element for the icon
 */
export const CategoryIcon: React.FC<{
  category?: CategoryIconData | null;
  className?: string;
}> = ({
  category,
  className = "h-4 w-4 text-ui-fg-subtle"
}) => {
  const iconData = getCategoryIcon(category);

  if (iconData.type === 'emoji') {
    return (
      <span
        className="flex items-center justify-center w-4 h-4"
        role="img"
        aria-label={category?.name || 'Category'}
        style={{
          fontSize: '14px',
          lineHeight: '1',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          minWidth: '16px',
          minHeight: '16px'
        }}
      >
        {iconData.value}
      </span>
    );
  }

  // Render Lucide icon component
  const IconComponent = iconData.value;
  return <IconComponent className={className} />;
};
