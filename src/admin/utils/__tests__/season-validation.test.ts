import { 
  validateSeasonalPeriod, 
  validateSeasonalPeriodForEdit,
  getSeasonValidationErrorMessage,
  type SeasonalPeriod
} from '../season-validation';

describe('Season Validation Utility', () => {
  // Sample existing seasons for testing
  const existingSeasons: SeasonalPeriod[] = [
    {
      id: 'season1',
      name: 'Summer Season',
      start_date: '2023-06-01',
      end_date: '2023-08-31',
    },
    {
      id: 'season2',
      name: 'Winter Season',
      start_date: '2023-12-01',
      end_date: '2024-02-28',
    }
  ];

  describe('validateSeasonalPeriod', () => {
    it('should validate a valid season with no conflicts', () => {
      const newSeason = {
        name: 'Fall Season',
        start_date: '2023-09-01',
        end_date: '2023-11-30',
      };

      const result = validateSeasonalPeriod(newSeason, existingSeasons);
      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it('should detect duplicate season names (case-insensitive)', () => {
      const newSeason = {
        name: 'summer season', // Same as existing but different case
        start_date: '2024-06-01',
        end_date: '2024-08-31',
      };

      const result = validateSeasonalPeriod(newSeason, existingSeasons);
      expect(result.isValid).toBe(false);
      expect(result.errors).toHaveLength(1);
      expect(result.errors[0].type).toBe('DUPLICATE_NAME');
    });

    it('should detect overlapping date ranges', () => {
      const newSeason = {
        name: 'Late Summer',
        start_date: '2023-08-15', // Overlaps with Summer Season
        end_date: '2023-09-15',
      };

      const result = validateSeasonalPeriod(newSeason, existingSeasons);
      expect(result.isValid).toBe(false);
      expect(result.errors).toHaveLength(1);
      expect(result.errors[0].type).toBe('OVERLAPPING_DATES');
    });

    it('should detect invalid date ranges (end before start)', () => {
      const newSeason = {
        name: 'Invalid Season',
        start_date: '2023-10-15',
        end_date: '2023-10-01', // End date before start date
      };

      const result = validateSeasonalPeriod(newSeason, existingSeasons);
      expect(result.isValid).toBe(false);
      expect(result.errors).toHaveLength(1);
      expect(result.errors[0].type).toBe('INVALID_DATE_RANGE');
    });

    it('should detect multiple validation errors', () => {
      const newSeason = {
        name: 'Summer Season', // Duplicate name
        start_date: '2023-07-15', // Overlaps with existing Summer Season
        end_date: '2023-07-01', // End date before start date
      };

      const result = validateSeasonalPeriod(newSeason, existingSeasons);
      expect(result.isValid).toBe(false);
      expect(result.errors.length).toBeGreaterThan(1);
    });
  });

  describe('validateSeasonalPeriodForEdit', () => {
    it('should allow editing a season without conflicts', () => {
      const editingSeason = {
        id: 'season1',
        name: 'Summer Season',
        start_date: '2023-06-15', // Changed start date
        end_date: '2023-08-31',
      };

      const result = validateSeasonalPeriodForEdit(editingSeason, existingSeasons);
      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it('should detect conflicts with other seasons when editing', () => {
      const editingSeason = {
        id: 'season1',
        name: 'Summer Season',
        start_date: '2023-11-15', // Now overlaps with Winter Season
        end_date: '2023-12-15',
      };

      const result = validateSeasonalPeriodForEdit(editingSeason, existingSeasons);
      expect(result.isValid).toBe(false);
      expect(result.errors).toHaveLength(1);
      expect(result.errors[0].type).toBe('OVERLAPPING_DATES');
    });

    it('should allow using the same name for the season being edited', () => {
      const editingSeason = {
        id: 'season1',
        name: 'Summer Season', // Same name as before
        start_date: '2023-06-01',
        end_date: '2023-08-31',
      };

      const result = validateSeasonalPeriodForEdit(editingSeason, existingSeasons);
      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });
  });

  describe('getSeasonValidationErrorMessage', () => {
    it('should return empty string for no errors', () => {
      const message = getSeasonValidationErrorMessage([]);
      expect(message).toBe('');
    });

    it('should return the error message for a single error', () => {
      const errors = [{
        type: 'DUPLICATE_NAME',
        message: 'A season with this name already exists.',
      }];
      
      const message = getSeasonValidationErrorMessage(errors);
      expect(message).toBe('A season with this name already exists.');
    });

    it('should combine multiple error messages', () => {
      const errors = [
        {
          type: 'DUPLICATE_NAME',
          message: 'A season with this name already exists.',
        },
        {
          type: 'OVERLAPPING_DATES',
          message: 'Date range overlaps with existing season.',
        }
      ];
      
      const message = getSeasonValidationErrorMessage(errors);
      expect(message).toBe('A season with this name already exists. Date range overlaps with existing season.');
    });
  });
});
