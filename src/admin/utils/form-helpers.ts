/**
 * Utility functions for form handling and data mapping
 */

export interface DropdownOption {
  value: string;
  label: string;
  description?: string;
}

/**
 * Helper function to find matching option value for dropdowns
 * Handles exact matches, case-insensitive matches, and label-to-value mapping
 *
 * @param options - Array of dropdown options with value and label
 * @param dbValue - The value from the database to match
 * @returns The matched option value or empty string if no match found
 */
export const findOptionValue = (
  options: DropdownOption[],
  dbValue: string
): string => {
  if (!dbValue) return "";

  // First try exact match on value
  const exactMatch = options.find((opt) => opt.value === dbValue);
  if (exactMatch) return dbValue;

  // Try case-insensitive match on value
  const caseInsensitiveMatch = options.find(
    (opt) => opt.value.toLowerCase() === dbValue.toLowerCase()
  );
  if (caseInsensitiveMatch) return caseInsensitiveMatch.value;

  // Try match on label (for cases where DB stores label instead of value)
  const labelMatch = options.find(
    (opt) => opt.label.toLowerCase() === dbValue.toLowerCase()
  );
  if (labelMatch) return labelMatch.value;

  return "";
};

/**
 * Helper function to find the display label for a given value
 *
 * @param options - Array of dropdown options with value and label
 * @param value - The value to find the label for
 * @returns The display label or the original value if no match found
 */
export const findOptionLabel = (
  options: DropdownOption[],
  value: string
): string => {
  if (!value) return "—";

  // First try exact match on value
  const exactMatch = options.find((opt) => opt.value === value);
  if (exactMatch) return exactMatch.label;

  // Try case-insensitive match on value
  const caseInsensitiveMatch = options.find(
    (opt) => opt.value.toLowerCase() === value.toLowerCase()
  );
  if (caseInsensitiveMatch) return caseInsensitiveMatch.label;

  // Try match on label (return the properly formatted label)
  const labelMatch = options.find(
    (opt) => opt.label.toLowerCase() === value.toLowerCase()
  );
  if (labelMatch) return labelMatch.label;

  // If no match found, return the original value (fallback)
  return value;
};

/**
 * Helper function to format category names from database format to display format
 * Converts underscore-separated strings to proper case
 *
 * @param category - The category string from database
 * @returns Formatted category name
 */
export const formatCategoryName = (category: string): string => {
  return category.replace(/_/g, " ").replace(/\b\w/g, (l) => l.toUpperCase());
};

/**
 * Helper function to format language preferences array for display
 * Handles both language codes (en, fr) and full names (English, French)
 *
 * @param languages - Array of language codes or names
 * @param languageOptions - Optional language options for mapping codes to labels
 * @returns Formatted string of languages
 */
export const formatLanguagePreferences = (
  languages: string[],
  languageOptions?: DropdownOption[]
): string => {
  if (!languages || languages.length === 0) return "—";

  // If language options are provided, try to map codes to labels
  if (languageOptions) {
    const mappedLanguages = languages.map((lang) => {
      const option = languageOptions.find(
        (opt) =>
          opt.value.toLowerCase() === lang.toLowerCase() ||
          opt.label.toLowerCase() === lang.toLowerCase()
      );
      return option ? option.label : lang;
    });
    return mappedLanguages.join(", ");
  }

  return languages.join(", ");
};

/**
 * Helper function to find the display label for a given value with fallback handling
 * This version handles cases where the database might store labels instead of values
 *
 * @param options - Array of dropdown options with value and label
 * @param value - The value to find the label for
 * @returns The display label or the original value if no match found
 */
export const findOptionLabelWithFallback = (
  options: DropdownOption[],
  value: string
): string => {
  if (!value) return "—";

  // First try exact match on value (most common case)
  const exactMatch = options.find((opt) => opt.value === value);
  if (exactMatch) return exactMatch.label;

  // Try case-insensitive match on value
  const caseInsensitiveMatch = options.find(
    (opt) => opt.value.toLowerCase() === value.toLowerCase()
  );
  if (caseInsensitiveMatch) return caseInsensitiveMatch.label;

  // Try exact match on label (database might store the label)
  const labelMatch = options.find((opt) => opt.label === value);
  if (labelMatch) return labelMatch.label;

  // Try case-insensitive match on label
  const labelCaseInsensitiveMatch = options.find(
    (opt) => opt.label.toLowerCase() === value.toLowerCase()
  );
  if (labelCaseInsensitiveMatch) return labelCaseInsensitiveMatch.label;

  // If no match found, return the original value (it might already be a proper label)
  return value;
};

/**
 * Specific helper for payout terms that handles common edge cases
 */
export const getPayoutTermsLabel = (value: string): string => {
  if (!value) return "—";

  // Common mappings for payout terms
  const payoutTermsMap: Record<string, string> = {
    immediate: "Immediate Payment",
    due_on_receipt: "Due on Receipt",
    net_7: "Net 7",
    net_15: "Net 15",
    net_30: "Net 30",
    net_45: "Net 45",
    net_60: "Net 60",
    net_90: "Net 90",
    end_of_month: "End of Month (EOM)",
    custom: "Custom Terms",
    // Handle variations
    net30: "Net 30",
    "net 30": "Net 30",
    NET_30: "Net 30",
    "NET 30": "Net 30",
  };

  // Try direct mapping first
  const mapped = payoutTermsMap[value.toLowerCase()];
  if (mapped) return mapped;

  // If it's already a proper label, return as-is
  if (
    value.includes("Net ") ||
    value.includes("Immediate") ||
    value.includes("Due on") ||
    value.includes("End of Month")
  ) {
    return value;
  }

  // Fallback
  return value;
};
