/**
 * User status utilities for handling user activation/deactivation via metadata
 */

export interface UserStatusMetadata {
  is_active: boolean;
  deactivated_at: string | null;
  deactivated_by: string | null;
  deactivation_reason: string | null;
  activated_at: string | null;
  activated_by: string | null;
}

export interface UserStatus {
  isActive: boolean;
  deactivatedAt: string | null;
  deactivatedBy: string | null;
  deactivationReason: string | null;
  activatedAt: string | null;
  activatedBy: string | null;
}

/**
 * Extract user status from user metadata
 */
export const getUserStatus = (user: any): UserStatus => {
  const userStatus = user?.metadata?.user_status as UserStatusMetadata | undefined;
  
  return {
    isActive: userStatus?.is_active !== false, // Default to true if not set
    deactivatedAt: userStatus?.deactivated_at || null,
    deactivatedBy: userStatus?.deactivated_by || null,
    deactivationReason: userStatus?.deactivation_reason || null,
    activatedAt: userStatus?.activated_at || null,
    activatedBy: userStatus?.activated_by || null,
  };
};

/**
 * Check if user is active (simplified helper)
 */
export const isUserActive = (user: any): boolean => {
  return getUserStatus(user).isActive;
};

/**
 * Create user status metadata for deactivation
 */
export const createDeactivationMetadata = (
  currentUserId: string,
  reason?: string
): { user_status: UserStatusMetadata } => {
  return {
    user_status: {
      is_active: false,
      deactivated_at: new Date().toISOString(),
      deactivated_by: currentUserId,
      deactivation_reason: reason || null,
      activated_at: null,
      activated_by: null,
    },
  };
};

/**
 * Create user status metadata for activation
 */
export const createActivationMetadata = (
  currentUserId: string
): { user_status: UserStatusMetadata } => {
  return {
    user_status: {
      is_active: true,
      deactivated_at: null,
      deactivated_by: null,
      deactivation_reason: null,
      activated_at: new Date().toISOString(),
      activated_by: currentUserId,
    },
  };
};

/**
 * Get user status display text
 */
export const getUserStatusText = (user: any): string => {
  const { isActive } = getUserStatus(user);
  return isActive ? "Active" : "Inactive";
};

/**
 * Get user status badge variant
 */
export const getUserStatusBadgeVariant = (user: any): "success" | "secondary" => {
  const { isActive } = getUserStatus(user);
  return isActive ? "success" : "secondary";
};
