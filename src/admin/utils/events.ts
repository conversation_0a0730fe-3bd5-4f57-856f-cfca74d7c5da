/**
 * Utility functions for emitting custom events in the admin interface
 * These events help coordinate real-time updates across components
 */

// Event types
export const EVENTS = {
  PRODUCT_SERVICE_UPDATED: 'product-service-updated',
  SUPPLIER_OFFERING_UPDATED: 'supplier-offering-updated',
  COST_HISTORY_CREATED: 'cost-history-created',
} as const;

// Event data interfaces
export interface ProductServiceUpdatedEventData {
  id: string;
  productService?: any;
}

export interface SupplierOfferingUpdatedEventData {
  id: string;
  supplierOffering?: any;
}

export interface CostHistoryCreatedEventData {
  productServiceId?: string;
  supplierOfferingId?: string;
  costHistory: any;
}

// Utility functions to emit events
export const emitProductServiceUpdated = (data: ProductServiceUpdatedEventData) => {
  const event = new CustomEvent(EVENTS.PRODUCT_SERVICE_UPDATED, {
    detail: data,
  });
  window.dispatchEvent(event);
};

export const emitSupplierOfferingUpdated = (data: SupplierOfferingUpdatedEventData) => {
  const event = new CustomEvent(EVENTS.SUPPLIER_OFFERING_UPDATED, {
    detail: data,
  });
  window.dispatchEvent(event);
};

export const emitCostHistoryCreated = (data: CostHistoryCreatedEventData) => {
  const event = new CustomEvent(EVENTS.COST_HISTORY_CREATED, {
    detail: data,
  });
  window.dispatchEvent(event);
};

// Hook to listen for events
export const useEventListener = (
  eventType: string,
  handler: (event: CustomEvent) => void,
  dependencies: any[] = []
) => {
  const { useEffect } = require('react');
  
  useEffect(() => {
    const eventHandler = (event: Event) => {
      handler(event as CustomEvent);
    };

    window.addEventListener(eventType, eventHandler);

    return () => {
      window.removeEventListener(eventType, eventHandler);
    };
  }, dependencies);
};
