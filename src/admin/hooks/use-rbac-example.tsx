// Example usage of the improved useRbac hook with React Query

import React from 'react';
import { useRbac, useRoles, usePermissions, useSetUserRole } from './use-rbac';
import { ScreenPermission } from '../../modules/rbac/types';

// Example 1: Basic permission checking
export const HotelManagementButton = () => {
  const { hasPermission, loading } = useRbac();

  if (loading) return <div>Loading...</div>;

  if (!hasPermission(ScreenPermission.HOTEL_MANAGEMENT_CREATE)) {
    return null; // Hide button if no permission
  }

  return <button>Create Hotel</button>;
};

// Example 2: Using separate hooks for better performance
export const RoleSelector = ({ userId }: { userId: string }) => {
  const { data: roles, isLoading: rolesLoading } = useRoles();
  const setUserRoleMutation = useSetUserRole();

  const handleRoleChange = async (roleId: string) => {
    try {
      await setUserRoleMutation.mutateAsync({
        userId,
        roleOrRoleId: roleId,
      });
      // Success feedback
    } catch (error) {
      // Error handling
      console.error('Failed to set user role:', error);
    }
  };

  if (rolesLoading) return <div>Loading roles...</div>;

  return (
    <select onChange={(e) => handleRoleChange(e.target.value)}>
      <option value="">Select Role</option>
      {roles?.roles?.map((role: any) => (
        <option key={role.id} value={role.id}>
          {role.name}
        </option>
      ))}
    </select>
  );
};

// Example 3: Permission-based component rendering
export const AdminPanel = () => {
  const { 
    currentUser, 
    loading, 
    isAdmin, 
    hasPermission,
    hasAnyPermission 
  } = useRbac();

  if (loading) return <div>Loading user data...</div>;

  if (!currentUser) return <div>Please log in</div>;

  return (
    <div>
      <h1>Admin Panel</h1>
      
      {/* Show for admins only */}
      {isAdmin() && (
        <section>
          <h2>Admin Only Section</h2>
          <button>Manage System Settings</button>
        </section>
      )}

      {/* Show if user has any hotel management permission */}
      {hasAnyPermission([
        ScreenPermission.HOTEL_MANAGEMENT_VIEW,
        ScreenPermission.HOTEL_MANAGEMENT_CREATE,
        ScreenPermission.HOTEL_MANAGEMENT_EDIT,
      ]) && (
        <section>
          <h2>Hotel Management</h2>
          
          {hasPermission(ScreenPermission.HOTEL_MANAGEMENT_VIEW) && (
            <button>View Hotels</button>
          )}
          
          {hasPermission(ScreenPermission.HOTEL_MANAGEMENT_CREATE) && (
            <button>Create Hotel</button>
          )}
          
          {hasPermission(ScreenPermission.HOTEL_MANAGEMENT_EDIT) && (
            <button>Edit Hotels</button>
          )}
        </section>
      )}

      {/* Show user management for users with permission */}
      {hasPermission(ScreenPermission.USER_MANAGEMENT_VIEW) && (
        <section>
          <h2>User Management</h2>
          <UserList />
        </section>
      )}
    </div>
  );
};

// Example 4: Data fetching with React Query benefits
export const UserList = () => {
  const { data: permissions } = usePermissions();
  const { hasPermission } = useRbac();

  // This component will automatically:
  // - Cache the permissions data
  // - Refetch when stale
  // - Share cache with other components using usePermissions
  // - Handle loading/error states

  return (
    <div>
      <h3>Available Permissions</h3>
      {permissions?.permissions?.map((permission: any) => (
        <div key={permission.id}>
          {permission.name} - {permission.description}
        </div>
      ))}
      
      {hasPermission(ScreenPermission.USER_MANAGEMENT_CREATE) && (
        <button>Add New User</button>
      )}
    </div>
  );
};

// Example 5: Using mutations with optimistic updates
export const RoleManagement = () => {
  const { data: roles, isLoading } = useRoles();
  const createRoleMutation = useSetUserRole();

  const handleCreateRole = async (roleData: {
    name: string;
    description: string;
    permissions: string[];
  }) => {
    try {
      await createRoleMutation.mutateAsync(roleData);
      // React Query will automatically invalidate and refetch roles
      // No manual state management needed!
    } catch (error) {
      console.error('Failed to create role:', error);
    }
  };

  if (isLoading) return <div>Loading...</div>;

  return (
    <div>
      <h2>Role Management</h2>
      
      {/* Form to create new role */}
      <form onSubmit={(e) => {
        e.preventDefault();
        const formData = new FormData(e.currentTarget);
        handleCreateRole({
          name: formData.get('name') as string,
          description: formData.get('description') as string,
          permissions: [], // Get from form
        });
      }}>
        <input name="name" placeholder="Role Name" required />
        <input name="description" placeholder="Description" required />
        <button 
          type="submit" 
          disabled={createRoleMutation.isPending}
        >
          {createRoleMutation.isPending ? 'Creating...' : 'Create Role'}
        </button>
      </form>

      {/* List existing roles */}
      <div>
        {roles?.roles?.map((role: any) => (
          <div key={role.id}>
            <h3>{role.name}</h3>
            <p>{role.description}</p>
            <p>Permissions: {role.permissions?.length || 0}</p>
          </div>
        ))}
      </div>
    </div>
  );
};
