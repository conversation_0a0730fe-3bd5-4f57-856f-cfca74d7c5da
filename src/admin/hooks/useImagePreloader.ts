import { useEffect, useRef } from 'react';

interface UseImagePreloaderOptions {
  images: string[];
  priority?: boolean;
  maxConcurrent?: number;
}

export const useImagePreloader = ({ 
  images, 
  priority = false, 
  maxConcurrent = 3 
}: UseImagePreloaderOptions) => {
  const preloadedImages = useRef<Set<string>>(new Set());
  const loadingQueue = useRef<string[]>([]);
  const currentlyLoading = useRef<Set<string>>(new Set());

  const preloadImage = (src: string): Promise<void> => {
    return new Promise((resolve, reject) => {
      if (preloadedImages.current.has(src)) {
        resolve();
        return;
      }

      const img = new Image();
      
      img.onload = () => {
        preloadedImages.current.add(src);
        currentlyLoading.current.delete(src);
        resolve();
        processQueue();
      };
      
      img.onerror = () => {
        currentlyLoading.current.delete(src);
        reject(new Error(`Failed to preload image: ${src}`));
        processQueue();
      };
      
      img.src = src;
      currentlyLoading.current.add(src);
    });
  };

  const processQueue = () => {
    while (
      loadingQueue.current.length > 0 && 
      currentlyLoading.current.size < maxConcurrent
    ) {
      const nextImage = loadingQueue.current.shift();
      if (nextImage && !preloadedImages.current.has(nextImage)) {
        preloadImage(nextImage).catch(() => {
          // Silently handle errors to prevent breaking the queue
        });
      }
    }
  };

  useEffect(() => {
    if (!images.length) return;

    const validImages = images.filter(Boolean);
    
    if (priority) {
      // For priority images, start loading immediately
      validImages.forEach(src => {
        if (!preloadedImages.current.has(src) && !currentlyLoading.current.has(src)) {
          preloadImage(src).catch(() => {
            // Silently handle errors
          });
        }
      });
    } else {
      // For non-priority images, add to queue
      loadingQueue.current = [
        ...loadingQueue.current,
        ...validImages.filter(src => 
          !preloadedImages.current.has(src) && 
          !currentlyLoading.current.has(src) &&
          !loadingQueue.current.includes(src)
        )
      ];
      
      // Start processing queue with a slight delay to not block priority images
      setTimeout(processQueue, 100);
    }
  }, [images, priority, maxConcurrent]);

  return {
    isPreloaded: (src: string) => preloadedImages.current.has(src),
    preloadedCount: preloadedImages.current.size,
    queueLength: loadingQueue.current.length,
    currentlyLoadingCount: currentlyLoading.current.size
  };
};
