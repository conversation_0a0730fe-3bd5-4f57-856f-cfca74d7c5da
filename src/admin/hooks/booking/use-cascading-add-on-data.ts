import { useMemo } from "react";
import { useCategories } from "../supplier-products-services/use-categories";
import { useProductsServices } from "../supplier-products-services/use-products-services";
import { useSupplierOfferings } from "../supplier-products-services/use-supplier-offerings";

interface UseCascadingAddOnDataProps {
  categoryId?: string;
  productServiceId?: string;
  checkInDate?: string;
  checkOutDate?: string;
  existingAddOnIds?: string[];
  disableOfferingsFetch?: boolean; // New parameter to disable automatic offerings fetch
}

interface CascadingAddOnData {
  // Categories
  categories: Array<{ id: string; name: string; category_type: string }>;
  categoriesLoading: boolean;
  categoriesError: any;

  // Products & Services
  products: Array<{ id: string; name: string; category_id: string }>;
  productsLoading: boolean;
  productsError: any;
  productsEnabled: boolean;

  // Supplier Offerings
  offerings: Array<{
    id: string;
    supplier_id: string;
    supplier_name?: string;
    selling_price?: number;
    net_price?: number;
    selling_currency?: string;
    active_from?: Date;
    active_to?: Date;
    availability_notes?: string;
  }>;
  offeringsLoading: boolean;
  offeringsError: any;
  offeringsEnabled: boolean;
}

/**
 * Hook to manage cascading dropdown data for add-on selection
 * Handles the dependency chain: Category -> Product & Service -> Supplier Offering
 */
export const useCascadingAddOnData = ({
  categoryId,
  productServiceId,
  checkInDate,
  checkOutDate,
  existingAddOnIds = [],
  disableOfferingsFetch = false,
}: UseCascadingAddOnDataProps): CascadingAddOnData => {
  // Fetch categories (always enabled)
  const {
    data: categoriesResponse,
    isLoading: categoriesLoading,
    error: categoriesError,
  } = useCategories({
    is_active: true,
    limit: 100,
    sort_by: "name",
    sort_order: "asc",
  });

  // Fetch products/services (enabled when category is selected)
  const productsEnabled = Boolean(categoryId);



  const productsQuery = useProductsServices({
    category_id: categoryId || undefined,
    status: "active",
    limit: 100,
    sort_by: "name",
    sort_order: "asc",
  });

  // Conditionally use the query results based on whether category is selected
  const productsResponse = productsEnabled ? productsQuery.data : undefined;
  const productsLoading = productsEnabled ? productsQuery.isLoading : false;
  const productsError = productsEnabled ? productsQuery.error : null;

  // Fetch supplier offerings (enabled when product/service is selected AND not disabled)
  const offeringsEnabled = Boolean(productServiceId) && !disableOfferingsFetch;
  
  // Build date filters for supplier offerings
  const offeringFilters = useMemo(() => {
    const filters: any = {
      product_service_id: productServiceId || undefined,
      status: "active",
      limit: 100,
      sort_by: "updated_at",
      sort_order: "desc",
    };

    // Add date filtering if check-in/check-out dates are provided
    if (checkInDate) {
      filters.active_from = checkInDate;
    }
    if (checkOutDate) {
      filters.active_to = checkOutDate;
    }

    return filters;
  }, [productServiceId, checkInDate, checkOutDate]);

  const {
    data: offeringsResponse,
    isLoading: offeringsLoading,
    error: offeringsError,
  } = useSupplierOfferings(offeringsEnabled ? offeringFilters : {});

  // Process and filter data
  const categories = useMemo(() => {
    return (categoriesResponse?.categories || []).map(category => ({
      id: category.id,
      name: category.name,
      category_type: category.category_type,
    }));
  }, [categoriesResponse]);

  const products = useMemo(() => {
    const allProducts = productsResponse?.product_services || [];

    // Filter out products that are already added as add-ons
    const filteredProducts = allProducts
      .filter(product => !existingAddOnIds.includes(product.id))
      .map(product => ({
        id: product.id,
        name: product.name,
        category_id: product.category_id,
      }));
    return filteredProducts;
  }, [productsResponse, existingAddOnIds, categoryId]);

  const offerings = useMemo(() => {
    const allOfferings = offeringsResponse?.supplier_offerings || [];

    // Since we're now handling date filtering in the API, we don't need to filter here
    // Just map the offerings to the expected format
    const processedOfferings = allOfferings.map(offering => ({
      id: offering.id,
      supplier_id: offering.supplier_id,
      supplier_name: offering.supplier_name,
      selling_price: offering.selling_price,
      net_price: offering.net_price,
      selling_currency: offering.selling_currency,
      active_from: offering.active_from,
      active_to: offering.active_to,
      availability_notes: offering.availability_notes,
    }));

    return processedOfferings;
  }, [offeringsResponse, productServiceId, checkInDate, checkOutDate]);

  return {
    // Categories
    categories,
    categoriesLoading,
    categoriesError,

    // Products & Services
    products,
    productsLoading: productsEnabled ? productsLoading : false,
    productsError: productsEnabled ? productsError : null,
    productsEnabled,

    // Supplier Offerings
    offerings,
    offeringsLoading: offeringsEnabled ? offeringsLoading : false,
    offeringsError: offeringsEnabled ? offeringsError : null,
    offeringsEnabled,
  };
};

/**
 * Helper function to validate if a date range is available for a supplier offering
 */
export const isOfferingAvailableForDates = (
  offering: {
    active_from?: Date | string;
    active_to?: Date | string;
  },
  checkInDate: string,
  checkOutDate: string
): boolean => {
  const checkIn = new Date(checkInDate);
  const checkOut = new Date(checkOutDate);
  
  // Handle offerings with no date restrictions (always available)
  if (!offering.active_from && !offering.active_to) {
    return true;
  }
  
  const activeFrom = offering.active_from ? new Date(offering.active_from) : null;
  const activeTo = offering.active_to ? new Date(offering.active_to) : null;
  
  // Check if booking dates overlap with offering availability
  if (activeFrom && checkOut < activeFrom) {
    return false; // Booking ends before offering starts
  }
  if (activeTo && checkIn > activeTo) {
    return false; // Booking starts after offering ends
  }
  
  return true;
};

/**
 * Helper function to format offering display text
 */
export const formatOfferingDisplayText = (offering: {
  supplier_name?: string;
  selling_price?: number;
  net_price?: number;
  selling_currency?: string;
  active_from?: Date | string;
  active_to?: Date | string;
}): { primary: string; secondary: string } => {
  const primary = offering.supplier_name || "Unknown Supplier";
  
  let secondary = "";
  
  // Add price information
  const price = offering.selling_price || offering.net_price;
  if (price) {
    secondary += `${price} ${offering.selling_currency || "CHF"}`;
  } else {
    secondary += "Price not set";
  }
  
  // Add validity period if applicable
  if (offering.active_from || offering.active_to) {
    const fromDate = offering.active_from ? new Date(offering.active_from).toLocaleDateString() : "Open";
    const toDate = offering.active_to ? new Date(offering.active_to).toLocaleDateString() : "Open";
    secondary += ` • Valid: ${fromDate} - ${toDate}`;
  }
  
  return { primary, secondary };
};
