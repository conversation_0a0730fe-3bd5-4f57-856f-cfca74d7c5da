import { useState, useEffect } from "react";
import { useSupplierOfferings } from "../supplier-products-services/use-supplier-offerings";

interface UseDateBasedSupplierOfferingsProps {
  productServiceId?: string;
  startDate?: string;
  endDate?: string;
  enabled?: boolean;
}

interface DateBasedSupplierOfferingsResult {
  offerings: Array<{
    id: string;
    supplier_id: string;
    supplier_name?: string;
    selling_price?: number;
    net_price?: number;
    selling_currency?: string;
    calculated_selling_price_selling_currency?: number;
    active_from?: Date;
    active_to?: Date;
    availability_notes?: string;
  }>;
  isLoading: boolean;
  error: any;
  searchOfferings: () => Promise<void>;
  clearOfferings: () => void;
  hasSearched: boolean;
}

/**
 * Hook to fetch supplier offerings based on product service ID and date range
 * This is used for the date-based supplier offering lookup in the add-on form
 */
export const useDateBasedSupplierOfferings = ({
  productServiceId,
  startDate,
  endDate,
  enabled = false,
}: UseDateBasedSupplierOfferingsProps): DateBasedSupplierOfferingsResult => {
  const [hasSearched, setHasSearched] = useState(false);
  const [isManualLoading, setIsManualLoading] = useState(false);
  const [offerings, setOfferings] = useState<any[]>([]);

  // Build filters for supplier offerings API
  const filters = {
    product_service_id: productServiceId || undefined,
    status: "active" as const,
    active_from: startDate || undefined,
    active_to: endDate || undefined,
    limit: 100,
    sort_by: "updated_at" as const,
    sort_order: "desc" as const,
  };

  // Use the existing supplier offerings hook but disable automatic fetching
  const {
    data: offeringsResponse,
    isLoading: queryLoading,
    error,
    refetch,
  } = useSupplierOfferings({});

  // Clear offerings when key parameters change
  useEffect(() => {
    setOfferings([]);
    setHasSearched(false);
  }, [productServiceId, startDate, endDate]);

  // Manual search function
  const searchOfferings = async () => {
    if (!productServiceId || !startDate || !endDate) {
      return;
    }

    setIsManualLoading(true);
    setHasSearched(true);

    try {
      // Make a direct API call with the specific filters
      const params = new URLSearchParams();
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== "") {
          params.append(key, value.toString());
        }
      });

      const response = await fetch(
        `/admin/supplier-management/supplier-offerings?${params.toString()}`
      );

      if (!response.ok) {
        throw new Error("Failed to fetch supplier offerings");
      }

      const data = await response.json();

      // Process offerings data
      const processedOfferings = (data.supplier_offerings || []).map((offering: any) => ({
        id: offering.id,
        supplier_id: offering.supplier_id,
        supplier_name: offering.supplier?.name,
        selling_price: offering.selling_price,
        net_price: offering.net_price,
        selling_currency: offering.selling_currency,
        calculated_selling_price_selling_currency: offering.calculated_selling_price_selling_currency,
        active_from: offering.active_from,
        active_to: offering.active_to,
        availability_notes: offering.availability_notes,
      }));

      setOfferings(processedOfferings);
    } catch (error) {
      console.error("Error fetching supplier offerings:", error);
      setOfferings([]);
    } finally {
      setIsManualLoading(false);
    }
  };

  // Function to manually clear offerings
  const clearOfferings = () => {
    setOfferings([]);
    setHasSearched(false);
  };

  return {
    offerings,
    isLoading: isManualLoading,
    error: null, // We'll handle errors within the searchOfferings function
    searchOfferings,
    clearOfferings,
    hasSearched,
  };
};
