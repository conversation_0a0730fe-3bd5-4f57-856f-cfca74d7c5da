import { useQuery } from "@tanstack/react-query";
import { sdk } from "../lib/sdk";

interface User {
  id: string;
  email: string;
  first_name?: string;
  last_name?: string;
  created_at: string;
  updated_at: string;
  metadata?: any;
}

// Helper function to get user display name (same logic as UserSelector)
export const getUserDisplayName = (user: User): string => {
  if (user.first_name && user.last_name) {
    return `${user.first_name} ${user.last_name}`;
  }
  return user.email;
};

// Hook to fetch all users and create a lookup map
export const useUserLookup = () => {
  const { data: usersResponse, isLoading, error } = useQuery({
    queryKey: ["users", "lookup"],
    queryFn: async () => {
      const response = await sdk.admin.user.list();
      return response;
    },
    staleTime: 5 * 60 * 1000, // Cache for 5 minutes
    gcTime: 10 * 60 * 1000, // Keep in cache for 10 minutes
  });

  const users = (usersResponse?.users || []) as User[];
  
  // Create a lookup map for quick user ID to name resolution
  const userLookup = users.reduce((acc, user) => {
    acc[user.id] = getUserDisplayName(user);
    return acc;
  }, {} as Record<string, string>);

  // Function to get user display name by ID
  const getUserDisplayNameById = (userId: string | null | undefined): string => {
    if (!userId) return "Unassigned";
    return userLookup[userId] || userId; // Fallback to ID if user not found
  };

  return {
    users,
    userLookup,
    getUserDisplayNameById,
    isLoading,
    error,
  };
};
