import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { toast } from "@camped-ai/ui";

// Types
export interface SyncResult {
  total: number;
  synced: number;
  errors: Array<{ id: string; error: string }>;
  results: any[];
}

export interface SyncStatistics {
  total_product_services: number;
  synced_count: number;
  error_count: number;
  pending_count: number;
  last_sync_at?: string;
}

export interface SyncStatus {
  is_synced: boolean;
  last_sync_at?: string;
  last_sync_status?: 'success' | 'error';
  add_on_product_id?: string;
  error_message?: string;
}

export interface SyncLogEntry {
  id: string;
  supplier_product_service_id: string;
  add_on_product_id?: string;
  sync_action: 'create' | 'update' | 'delete';
  sync_status: 'success' | 'error' | 'pending';
  error_message?: string;
  sync_data?: any;
  created_at: string;
}

// API functions
const bulkSyncProductServices = async (options: {
  status?: 'active' | 'inactive';
  type?: 'Product' | 'Service';
  limit?: number;
  offset?: number;
  force_resync?: boolean;
} = {}): Promise<{
  sync_result: SyncResult;
  statistics: SyncStatistics;
}> => {
  const response = await fetch('/admin/supplier-management/products-services/sync', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(options),
  });

  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.message || 'Failed to sync product services');
  }

  return response.json();
};

const syncSingleProductService = async (productServiceId: string, forceResync: boolean = false): Promise<{
  add_on: any;
  sync_status: SyncStatus;
}> => {
  const response = await fetch(`/admin/supplier-management/products-services/${productServiceId}/sync`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({ force_resync: forceResync }),
  });

  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.message || 'Failed to sync product service');
  }

  return response.json();
};

const getSyncStatistics = async (): Promise<{
  statistics: SyncStatistics;
  recent_logs: SyncLogEntry[];
}> => {
  const response = await fetch('/admin/supplier-management/products-services/sync');

  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.message || 'Failed to get sync statistics');
  }

  return response.json();
};

const getSyncStatus = async (productServiceId: string): Promise<{
  product_service_id: string;
  sync_status: SyncStatus;
  sync_logs: SyncLogEntry[];
}> => {
  const response = await fetch(`/admin/supplier-management/products-services/${productServiceId}/sync`);

  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.message || 'Failed to get sync status');
  }

  return response.json();
};

// Hooks
export const useBulkSyncProductServices = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: bulkSyncProductServices,
    onSuccess: (data) => {
      const { sync_result } = data;
      
      if (sync_result.errors.length === 0) {
        toast.success(`Successfully synced ${sync_result.synced} product services to add-ons`);
      } else {
        toast.warning(
          `Synced ${sync_result.synced}/${sync_result.total} product services. ${sync_result.errors.length} errors occurred.`
        );
      }

      // Invalidate related queries
      queryClient.invalidateQueries({ queryKey: ['sync-statistics'] });
      queryClient.invalidateQueries({ queryKey: ['inventory-add-ons'] });
    },
    onError: (error: Error) => {
      toast.error(`Bulk sync failed: ${error.message}`);
    },
  });
};

export const useSyncSingleProductService = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ productServiceId, forceResync }: { productServiceId: string; forceResync?: boolean }) =>
      syncSingleProductService(productServiceId, forceResync),
    onSuccess: (data, variables) => {
      toast.success(`Successfully synced product service to add-on`);

      // Invalidate related queries
      queryClient.invalidateQueries({ queryKey: ['sync-statistics'] });
      queryClient.invalidateQueries({ queryKey: ['sync-status', variables.productServiceId] });
      queryClient.invalidateQueries({ queryKey: ['inventory-add-ons'] });
    },
    onError: (error: Error) => {
      toast.error(`Sync failed: ${error.message}`);
    },
  });
};

export const useSyncStatistics = () => {
  return useQuery({
    queryKey: ['sync-statistics'],
    queryFn: getSyncStatistics,
    refetchInterval: 30000, // Refetch every 30 seconds
  });
};

export const useSyncStatus = (productServiceId: string) => {
  return useQuery({
    queryKey: ['sync-status', productServiceId],
    queryFn: () => getSyncStatus(productServiceId),
    enabled: !!productServiceId,
  });
};
