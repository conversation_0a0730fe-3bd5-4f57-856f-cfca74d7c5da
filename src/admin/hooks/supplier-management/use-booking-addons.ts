import { useQuery } from "@tanstack/react-query";
import { sdk } from "../../lib/sdk";

export interface BookingAddon {
  id: string;
  order_id: string;
  add_on_id: string;
  add_on_name: string;
  quantity: number;
  unit_price: number;
  total_price: number;
  customer_field_responses: Record<string, any>;
  supplier_order_id?: string;
  order_status?:
    | "pending"
    | "confirmed"
    | "in_progress"
    | "completed"
    | "cancelled";
  created_at: string;
  updated_at: string;
  // Related data
  order?: {
    id: string;
    display_id: string;
    customer_id: string;
    email: string;
    metadata?: {
      hotel_name?: string;
      check_in_date?: string;
      check_out_date?: string;
      customer_name?: string;
    };
  };
  add_on?: {
    id: string;
    name: string;
    description?: string;
    selling_price: number;
    selling_currency: string;
    metadata?: {
      supplier_id?: string;
      supplier_name?: string;
    };
  };
}

export interface BookingAddonsResponse {
  booking_addons: BookingAddon[];
  count: number;
  offset: number;
  limit: number;
}

export interface UseBookingAddonsParams {
  supplier_id?: string;
  order_id?: string;
  add_on_id?: string;
  date_from?: string;
  date_to?: string;
  limit?: number;
  offset?: number;
}

export const useBookingAddons = (params: UseBookingAddonsParams = {}) => {
  const queryParams = new URLSearchParams();

  Object.entries(params).forEach(([key, value]) => {
    if (value !== undefined && value !== null && value !== "") {
      queryParams.append(key, value.toString());
    }
  });

  return useQuery<BookingAddonsResponse>({
    queryKey: ["booking-addons", params],
    queryFn: async () => {
      const url = `/admin/booking-addons${
        queryParams.toString() ? `?${queryParams.toString()}` : ""
      }`;
      console.log("🔍 Fetching booking add-ons from:", url);

      try {
        const data = (await sdk.client.fetch(url)) as any;
        console.log("✅ Booking add-ons fetched successfully:", data);
        return data;
      } catch (error) {
        console.error("❌ Failed to fetch booking add-ons:", error);
        throw error;
      }
    },
  });
};
