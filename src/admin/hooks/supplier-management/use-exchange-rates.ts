import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "@camped-ai/ui";

// Types
export interface ExchangeRate {
  id: string;
  date: string;
  base_currency: string;
  selling_currency: string;
  exchange_rate: number | string; // API might return as string
  metadata?: Record<string, any>;
  created_at: string;
  updated_at: string;
}

export interface CreateExchangeRateInput {
  date: string;
  base_currency: string;
  selling_currency: string;
  exchange_rate: number;
  metadata?: Record<string, any>;
}

export interface UpdateExchangeRateInput {
  date?: string;
  base_currency?: string;
  selling_currency?: string;
  exchange_rate?: number;
  metadata?: Record<string, any>;
}

export interface ExchangeRateFilters {
  date?: string;
  date_from?: string;
  date_to?: string;
  base_currency?: string;
  selling_currency?: string;
  exchange_rate_min?: number;
  exchange_rate_max?: number;
  limit?: number;
  offset?: number;
  sort_by?: "date" | "base_currency" | "selling_currency" | "exchange_rate" | "created_at" | "updated_at";
  sort_order?: "asc" | "desc";
}

export interface ExchangeRateListResponse {
  exchange_rates: ExchangeRate[];
  count: number;
  limit: number;
  offset: number;
}

// Query keys
const QUERY_KEYS = {
  all: ["supplier-management", "exchange-rates"] as const,
  lists: () => [...QUERY_KEYS.all, "list"] as const,
  list: (filters: ExchangeRateFilters) => [...QUERY_KEYS.lists(), filters] as const,
  details: () => [...QUERY_KEYS.all, "detail"] as const,
  detail: (id: string) => [...QUERY_KEYS.details(), id] as const,
};

// Hook to fetch exchange rates list
export const useExchangeRates = (filters: ExchangeRateFilters = {}) => {
  return useQuery({
    queryKey: QUERY_KEYS.list(filters),
    queryFn: async (): Promise<ExchangeRateListResponse> => {
      const params = new URLSearchParams();

      // Date filters
      if (filters.date) params.append("date", filters.date);
      if (filters.date_from) params.append("date_from", filters.date_from);
      if (filters.date_to) params.append("date_to", filters.date_to);

      // Currency filters
      if (filters.base_currency) params.append("base_currency", filters.base_currency);
      if (filters.selling_currency) params.append("selling_currency", filters.selling_currency);

      // Exchange rate range filters
      if (filters.exchange_rate_min !== undefined) {
        params.append("exchange_rate_min", filters.exchange_rate_min.toString());
      }
      if (filters.exchange_rate_max !== undefined) {
        params.append("exchange_rate_max", filters.exchange_rate_max.toString());
      }

      // Pagination and sorting
      if (filters.limit) params.append("limit", filters.limit.toString());
      if (filters.offset) params.append("offset", filters.offset.toString());
      if (filters.sort_by) params.append("sort_by", filters.sort_by);
      if (filters.sort_order) params.append("sort_order", filters.sort_order);

      const url = `/admin/supplier-management/exchange-rates${
        params.toString() ? `?${params.toString()}` : ""
      }`;

      const response = await fetch(url, {
        credentials: "include",
        headers: {
          "Content-Type": "application/json",
        },
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.message || `HTTP error! status: ${response.status}`);
      }

      return response.json();
    },
    staleTime: 30000, // 30 seconds
    gcTime: 300000, // 5 minutes
  });
};

// Hook to fetch a single exchange rate
export const useExchangeRate = (id: string) => {
  return useQuery({
    queryKey: QUERY_KEYS.detail(id),
    queryFn: async (): Promise<{ exchange_rate: ExchangeRate }> => {
      const response = await fetch(`/admin/supplier-management/exchange-rates/${id}`, {
        credentials: "include",
        headers: {
          "Content-Type": "application/json",
        },
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.message || `HTTP error! status: ${response.status}`);
      }

      return response.json();
    },
    enabled: !!id,
  });
};

// Hook to create an exchange rate
export const useCreateExchangeRate = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: CreateExchangeRateInput): Promise<{ exchange_rate: ExchangeRate }> => {
      const response = await fetch("/admin/supplier-management/exchange-rates", {
        method: "POST",
        credentials: "include",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.message || `HTTP error! status: ${response.status}`);
      }

      return response.json();
    },
    onSuccess: () => {
      // Invalidate and refetch exchange rates lists
      queryClient.invalidateQueries({ queryKey: QUERY_KEYS.lists() });
      toast.success("Exchange rate created successfully");
    },
    onError: (error: Error) => {
      toast.error(error.message || "Failed to create exchange rate");
    },
  });
};

// Hook to update an exchange rate
export const useUpdateExchangeRate = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({
      id,
      data,
    }: {
      id: string;
      data: UpdateExchangeRateInput;
    }): Promise<{ exchange_rate: ExchangeRate }> => {
      const response = await fetch(`/admin/supplier-management/exchange-rates/${id}`, {
        method: "PUT",
        credentials: "include",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.message || `HTTP error! status: ${response.status}`);
      }

      return response.json();
    },
    onSuccess: (_, variables) => {
      // Invalidate and refetch exchange rates lists
      queryClient.invalidateQueries({ queryKey: QUERY_KEYS.lists() });
      // Invalidate the specific exchange rate detail
      queryClient.invalidateQueries({ queryKey: QUERY_KEYS.detail(variables.id) });
      toast.success("Exchange rate updated successfully");
    },
    onError: (error: Error) => {
      toast.error(error.message || "Failed to update exchange rate");
    },
  });
};

// Hook to delete an exchange rate
export const useDeleteExchangeRate = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (id: string): Promise<{ id: string; deleted: boolean }> => {
      const response = await fetch(`/admin/supplier-management/exchange-rates/${id}`, {
        method: "DELETE",
        credentials: "include",
        headers: {
          "Content-Type": "application/json",
        },
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.message || `HTTP error! status: ${response.status}`);
      }

      return response.json();
    },
    onSuccess: (_, id) => {
      // Invalidate and refetch exchange rates lists
      queryClient.invalidateQueries({ queryKey: QUERY_KEYS.lists() });
      // Remove the specific exchange rate from cache
      queryClient.removeQueries({ queryKey: QUERY_KEYS.detail(id) });
      toast.success("Exchange rate deleted successfully");
    },
    onError: (error: Error) => {
      toast.error(error.message || "Failed to delete exchange rate");
    },
  });
};

// Hook to get exchange rate for a specific currency pair
export const useExchangeRateForCurrencyPair = (
  baseCurrency: string,
  sellingCurrency: string,
  date?: string
) => {
  return useQuery({
    queryKey: [...QUERY_KEYS.all, "currency-pair", baseCurrency, sellingCurrency, date],
    queryFn: async (): Promise<ExchangeRate | null> => {
      const params = new URLSearchParams();
      params.append("base_currency", baseCurrency);
      params.append("selling_currency", sellingCurrency);
      params.append("limit", "1");
      params.append("sort_by", "date");
      params.append("sort_order", "desc");

      if (date) {
        params.append("date", date);
      }

      const url = `/admin/supplier-management/exchange-rates?${params.toString()}`;

      const response = await fetch(url, {
        credentials: "include",
        headers: {
          "Content-Type": "application/json",
        },
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.message || `HTTP error! status: ${response.status}`);
      }

      const result: ExchangeRateListResponse = await response.json();
      return result.exchange_rates[0] || null;
    },
    enabled: !!baseCurrency && !!sellingCurrency,
  });
};
