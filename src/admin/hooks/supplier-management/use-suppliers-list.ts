import { useQuery, useQueryClient } from "@tanstack/react-query";

// Types based on the actual API response structure
export interface SupplierContact {
  id: string;
  name: string;
  email: string;
  phone_number?: string;
  is_whatsapp: boolean;
  is_primary: boolean;
  supplier_id: string;
  created_at: string;
  updated_at: string;
  deleted_at?: string;
}

export interface Supplier {
  id: string;
  supplier_type: string;
  name: string;

  handle: string;
  status: string;

  preference?: "Preferred" | "Backup";
  region?: string;
  timezone?: string;
  language_preference: string[];
  payment_method?: string;
  payout_terms?: string;
  tax_id?: string;
  default_currency?: string;
  bank_account_details?: string;
  categories: string[];
  address?: string;
  metadata?: any;
  created_at: string;
  updated_at: string;
  deleted_at?: string;
  contacts: SupplierContact[];
}

export interface SuppliersListFilters {

  status?: string;
  preference?: string;
  search?: string;
  supplier_name?: string;
  primary_contact_email?: string;
  primary_contact_person?: string;
  categories?: string;
  region?: string;
  sort_by?: "name" | "status" | "created_at" | "updated_at" | "rating" | "total_orders";
  sort_order?: "asc" | "desc";
  limit?: number;
  offset?: number;
}

export interface SuppliersListResponse {
  suppliers: Supplier[];
  count: number;
  limit: number;
  offset: number;
}

// API functions
const fetchSuppliersList = async (
  filters: SuppliersListFilters = {}
): Promise<SuppliersListResponse> => {
  const params = new URLSearchParams();

  // business_type field removed from simplified data model
  if (filters.status) params.append("status", filters.status);
  if (filters.preference) params.append("preference", filters.preference);
  if (filters.search) params.append("search", filters.search);
  if (filters.supplier_name)
    params.append("supplier_name", filters.supplier_name);
  if (filters.primary_contact_email)
    params.append("primary_contact_email", filters.primary_contact_email);
  if (filters.primary_contact_person)
    params.append("primary_contact_person", filters.primary_contact_person);
  if (filters.categories) params.append("categories", filters.categories);
  if (filters.region) params.append("region", filters.region);
  if (filters.sort_by) params.append("sort_by", filters.sort_by);
  if (filters.sort_order) params.append("sort_order", filters.sort_order);
  if (filters.limit) params.append("limit", filters.limit.toString());
  if (filters.offset) params.append("offset", filters.offset.toString());

  const url = `/admin/supplier-management/suppliers${
    params.toString() ? `?${params.toString()}` : ""
  }`;

  const response = await fetch(url, {
    method: "GET",
    headers: {
      "Content-Type": "application/json",
    },
    credentials: "include",
  });

  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.message || "Failed to fetch suppliers list");
  }

  return response.json();
};

// Query Keys
export const suppliersListKeys = {
  all: ["suppliers-list"] as const,
  lists: () => [...suppliersListKeys.all, "list"] as const,
  list: (filters: SuppliersListFilters) =>
    [...suppliersListKeys.lists(), filters] as const,
};

// Hooks
export const useSuppliersList = (filters: SuppliersListFilters = {}) => {
  return useQuery({
    queryKey: suppliersListKeys.list(filters),
    queryFn: () => fetchSuppliersList(filters),
    staleTime: 1000 * 30, // 30 seconds cache duration (reduced for better real-time updates)
    refetchOnWindowFocus: true, // Refetch when user returns to the tab
    refetchOnMount: true, // Always refetch when component mounts to ensure fresh data
    refetchOnReconnect: true, // Refetch when network reconnects
  });
};

export const useRefreshSuppliersList = () => {
  const queryClient = useQueryClient();

  return () => {
    // Invalidate all supplier list queries
    queryClient.invalidateQueries({ queryKey: suppliersListKeys.all });
    // Also force refetch any active queries
    queryClient.refetchQueries({ queryKey: suppliersListKeys.all });
  };
};
