import { useQuery } from "@tanstack/react-query";

// Types
export interface DashboardMetrics {
  // KPI metrics (for KPI cards)
  active_suppliers_count: number;
  products_services_count: number;
  active_offerings_count: number;
  pending_orders_count: number;
  contracts_expiring_soon_count: number;

  // Total counts (for Core Functions)
  total_suppliers_count: number;
  total_offerings_count: number;

  last_updated: string;
}

export interface DashboardMetricsResponse {
  metrics: DashboardMetrics;
  message: string;
}

// API function
const fetchDashboardMetrics = async (): Promise<DashboardMetricsResponse> => {
  const response = await fetch("/admin/supplier-management/dashboard/metrics");

  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.error || "Failed to fetch dashboard metrics");
  }

  return response.json();
};

// Query Keys
export const dashboardMetricsKeys = {
  all: ["dashboard-metrics"] as const,
  metrics: () => [...dashboardMetricsKeys.all, "metrics"] as const,
};

// Hook
export const useDashboardMetrics = () => {
  return useQuery({
    queryKey: dashboardMetricsKeys.metrics(),
    queryFn: fetchDashboardMetrics,
    staleTime: 5 * 60 * 1000, // 5 minutes
    refetchInterval: 10 * 60 * 1000, // Refetch every 10 minutes
    select: (data) => data.metrics,
  });
};
