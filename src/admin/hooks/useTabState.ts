import { useState, useEffect } from "react";
import { useSearchParams } from "react-router-dom";
import { TabId } from "../components/destination/destination-tabs";

interface UseTabStateOptions {
  defaultTab?: TabId;
  paramName?: string;
}

export const useTabState = (options: UseTabStateOptions = {}) => {
  const { defaultTab = "overview", paramName = "tab" } = options;
  const [searchParams, setSearchParams] = useSearchParams();
  
  // Get initial tab from URL or use default
  const getInitialTab = (): TabId => {
    const tabFromUrl = searchParams.get(paramName) as TabId;
    const validTabs: TabId[] = ["overview", "hotels", "faqs", "insights", "bailey-ai", "settings"];

    if (tabFromUrl && validTabs.includes(tabFromUrl)) {
      return tabFromUrl;
    }

    return defaultTab;
  };

  const [activeTab, setActiveTab] = useState<TabId>(getInitialTab);

  // Update URL when tab changes
  const handleTabChange = (newTab: TabId) => {
    setActiveTab(newTab);
    
    // Update URL search params
    const newSearchParams = new URLSearchParams(searchParams);
    if (newTab === defaultTab) {
      // Remove tab param if it's the default tab
      newSearchParams.delete(paramName);
    } else {
      newSearchParams.set(paramName, newTab);
    }
    
    setSearchParams(newSearchParams, { replace: true });
  };

  // Listen for URL changes (browser back/forward)
  useEffect(() => {
    const tabFromUrl = searchParams.get(paramName) as TabId;
    const validTabs: TabId[] = ["overview", "hotels", "faqs", "insights", "bailey-ai", "settings"];

    if (tabFromUrl && validTabs.includes(tabFromUrl) && tabFromUrl !== activeTab) {
      setActiveTab(tabFromUrl);
    } else if (!tabFromUrl && activeTab !== defaultTab) {
      setActiveTab(defaultTab);
    }
  }, [searchParams, paramName, activeTab, defaultTab]);

  return {
    activeTab,
    setActiveTab: handleTabChange,
  };
};
