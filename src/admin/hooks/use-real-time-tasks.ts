import { useEffect, useRef, useCallback } from "react";
import { useQueryClient } from "@tanstack/react-query";
import type { TaskScreenData } from "../routes/concierge-management/tasks/loader";

interface UseRealTimeTasksOptions {
  enabled?: boolean;
  pollingInterval?: number;
  filters?: any;
}

/**
 * Hook for real-time task synchronization
 * Uses polling to keep task data synchronized across multiple users
 */
export const useRealTimeTasks = (options: UseRealTimeTasksOptions = {}) => {
  const {
    enabled = true,
    pollingInterval = 30000, // 30 seconds default
    filters = {},
  } = options;

  const queryClient = useQueryClient();
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const lastUpdateRef = useRef<string | null>(null);

  // Function to fetch latest task updates
  const fetchTaskUpdates = useCallback(async () => {
    try {
      // Build query parameters for incremental updates
      const params = new URLSearchParams();
      
      // Add filters
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== "") {
          params.append(key, String(value));
        }
      });

      // Note: The API doesn't support updated_since yet, so we fetch all tasks
      // In a production environment, you'd want to add this parameter to the API

      const response = await fetch(`/admin/concierge-management/tasks?${params.toString()}`, {
        credentials: "include",
        headers: {
          "Content-Type": "application/json",
        },
      });

      if (!response.ok) {
        console.warn("Failed to fetch task updates:", response.statusText);
        return;
      }

      const data = await response.json();
      
      if (data.tasks && data.tasks.length > 0) {
        // Update the current timestamp
        const latestUpdate = data.tasks.reduce((latest: string, task: TaskScreenData) => {
          return task.updated_at > latest ? task.updated_at : latest;
        }, lastUpdateRef.current || "");

        if (latestUpdate && latestUpdate !== lastUpdateRef.current) {
          lastUpdateRef.current = latestUpdate;

          // Only update caches if we have newer data
          const queryCache = queryClient.getQueryCache();
          let updatedCount = 0;

          // Update task-screen caches
          queryCache.findAll({ queryKey: ["task-screen"] }).forEach((query) => {
            queryClient.setQueryData(query.queryKey, (oldData: any) => {
              if (!oldData || !oldData.tasks) return oldData;

              let hasChanges = false;
              const updatedTasks = oldData.tasks.map((existingTask: TaskScreenData) => {
                const updatedTask = data.tasks.find((t: TaskScreenData) => t.id === existingTask.id);
                if (updatedTask && updatedTask.updated_at > existingTask.updated_at) {
                  hasChanges = true;
                  updatedCount++;
                  return updatedTask;
                }
                return existingTask;
              });

              return hasChanges ? { ...oldData, tasks: updatedTasks } : oldData;
            });
          });

          // Update individual task caches
          data.tasks.forEach((task: TaskScreenData) => {
            queryClient.setQueryData(["concierge-task", task.id], (oldData: any) => {
              if (!oldData || task.updated_at > oldData.updated_at) {
                return task;
              }
              return oldData;
            });
          });

          // Update concierge-tasks caches
          queryCache.findAll({ queryKey: ["concierge-tasks"] }).forEach((query) => {
            queryClient.setQueryData(query.queryKey, (oldData: any) => {
              if (!oldData || !oldData.tasks) return oldData;

              let hasChanges = false;
              const updatedTasks = oldData.tasks.map((existingTask: any) => {
                const updatedTask = data.tasks.find((t: TaskScreenData) => t.id === existingTask.id);
                if (updatedTask && updatedTask.updated_at > existingTask.updated_at) {
                  hasChanges = true;
                  return updatedTask;
                }
                return existingTask;
              });

              return hasChanges ? { ...oldData, tasks: updatedTasks } : oldData;
            });
          });

          if (updatedCount > 0) {
            console.log(`Real-time sync: Updated ${updatedCount} tasks`);
          }
        }
      }
    } catch (error) {
      console.warn("Error fetching task updates:", error);
    }
  }, [filters, queryClient]);

  // Initialize last update timestamp from current cache
  const initializeLastUpdate = useCallback(() => {
    const currentData = queryClient.getQueryData(["task-screen", filters]) as any;
    if (currentData?.tasks?.length > 0) {
      const latestUpdate = currentData.tasks.reduce((latest: string, task: TaskScreenData) => {
        return task.updated_at > latest ? task.updated_at : latest;
      }, "");
      lastUpdateRef.current = latestUpdate;
    }
  }, [filters, queryClient]);

  // Start polling
  const startPolling = useCallback(() => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
    }

    initializeLastUpdate();
    
    intervalRef.current = setInterval(() => {
      fetchTaskUpdates();
    }, pollingInterval);

    // Also fetch immediately
    fetchTaskUpdates();
  }, [fetchTaskUpdates, pollingInterval, initializeLastUpdate]);

  // Stop polling
  const stopPolling = useCallback(() => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
      intervalRef.current = null;
    }
  }, []);

  // Effect to manage polling lifecycle
  useEffect(() => {
    if (enabled) {
      startPolling();
    } else {
      stopPolling();
    }

    return () => {
      stopPolling();
    };
  }, [enabled, startPolling, stopPolling]);

  // Handle visibility change to pause/resume polling
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (document.hidden) {
        stopPolling();
      } else if (enabled) {
        startPolling();
      }
    };

    document.addEventListener("visibilitychange", handleVisibilityChange);
    
    return () => {
      document.removeEventListener("visibilitychange", handleVisibilityChange);
    };
  }, [enabled, startPolling, stopPolling]);

  return {
    startPolling,
    stopPolling,
    isPolling: intervalRef.current !== null,
  };
};
