import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "@camped-ai/ui";
import { SelectedAddOn } from "../../components/booking/add-on-selection/AddOnSelectionStep";

interface BookingAddOn {
  id: string;
  order_id: string;
  add_on_variant_id: string;
  add_on_name: string;
  quantity: number;
  unit_price: number;
  total_price: number;
  currency_code: string;
  customer_field_responses: Record<string, any>;
  add_on_metadata: Record<string, any>;
}

interface ConciergeOrderItem {
  id: string;
  concierge_order_id: string;
  concierge_order: {
    id: string;
  };
  line_item_id: string | null;
  variant_id: string;
  quantity: number;
  unit_price: number;
  title: string;
  status: string;
  is_active: boolean;
  added_by: string | null;
  finalized_by: string | null;
  added_at: string;
  finalized_at: string | null;
  metadata: Record<string, any>;
  created_at: string;
  updated_at: string;
  deleted_at: string | null;
}

interface ConciergeOrderItemsResponse {
  concierge_order_items: ConciergeOrderItem[];
  count: number;
  limit: number;
  offset: number;
}

interface AvailableAddOnsResponse {
  add_ons: any[];
  categories: any[];
  count: number;
  destination?: {
    id: string;
    name: string;
  };
}

interface CreateBookingAddOnInput {
  concierge_order_id?: string;
  order_id?: string;
  add_on_variant_id: string; // This will be the product_service_id
  add_on_name: string;
  quantity: number;
  unit_price: number;
  currency_code: string;
  customer_field_responses: Record<string, any>;
  add_on_metadata: Record<string, any>;
  // New fields for category and date range
  category_id?: string;
  start_date?: string;
  end_date?: string;
}

// Fetch booking add-ons for a specific booking
export const useBookingAddOns = (bookingId: string) => {
  return useQuery({
    queryKey: ["booking-add-ons", bookingId],
    queryFn: async (): Promise<{ add_ons: BookingAddOn[]; count: number }> => {
      const response = await fetch(`/admin/hotel-management/bookings/${bookingId}/add-ons`);
      if (!response.ok) {
        throw new Error("Failed to fetch booking add-ons");
      }
      return response.json();
    },
    enabled: !!bookingId,
  });
};

// Fetch available add-ons for a destination
export const useAvailableAddOns = (destinationId?: string, enabled: boolean = true) => {
  return useQuery({
    queryKey: ["available-add-ons", destinationId],
    queryFn: async (): Promise<AvailableAddOnsResponse> => {
      let url = "/admin/bookings/add-ons/available";
      if (destinationId) {
        url = `/store/hotel-management/destinations/${destinationId}/add-ons`;
      }
      
      const response = await fetch(url);
      if (!response.ok) {
        throw new Error("Failed to fetch available add-ons");
      }
      return response.json();
    },
    enabled: enabled,
  });
};

// Add an add-on to a booking
export const useAddBookingAddOn = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (input: CreateBookingAddOnInput): Promise<BookingAddOn> => {
      const response = await fetch("/admin/booking-addons", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(input),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || "Failed to add add-on to booking");
      }

      return response.json();
    },
    onSuccess: (data, variables) => {
      // Invalidate and refetch booking add-ons
      queryClient.invalidateQueries({
        queryKey: ["booking-add-ons", variables.order_id],
      });
      
      toast.success("Success", {
        description: `${variables.add_on_name} added to booking successfully!`,
      });
    },
    onError: (error: Error) => {
      toast.error("Error", {
        description: error.message || "Failed to add add-on to booking",
      });
    },
  });
};

// Helper function to convert SelectedAddOn to CreateBookingAddOnInput
export const convertSelectedAddOnToInput = (
  selectedAddOn: SelectedAddOn,
  bookingId: string
): CreateBookingAddOnInput => {
  // For supplier products/services, convert product_service_id to variant_id
  const productServiceId = selectedAddOn.metadata?.product_service_id || selectedAddOn.id;
  const variantId = productServiceId.startsWith('ps_')
    ? `variant_addon_${productServiceId.replace('ps_', '')}`
    : selectedAddOn.adult_variant_id || selectedAddOn.id;
  let unitPrice = selectedAddOn.selling_price;

  // If it's a child-specific add-on or has child pricing
  if (selectedAddOn.per_day_child_price > 0) {
    unitPrice = selectedAddOn.per_day_child_price;
  }

  console.log("🔄 Converting SelectedAddOn to input:", {
    originalId: selectedAddOn.id,
    productServiceId,
    variantId,
    addOnName: selectedAddOn.title
  });

  return {
    order_id: bookingId,
    add_on_variant_id: variantId,
    add_on_name: selectedAddOn.title,
    quantity: selectedAddOn.quantity || 1,
    unit_price: unitPrice * 100, // Convert to cents
    currency_code: selectedAddOn.selling_currency || "CHF",
    customer_field_responses: selectedAddOn.customerFieldResponses || {},
    add_on_metadata: {
      product_service_id: selectedAddOn.metadata?.product_service_id,
      product_service_type: selectedAddOn.metadata?.type,
      add_on_id: selectedAddOn.id,
      destination_id: selectedAddOn.destination_id,
      destination_name: selectedAddOn.destination_name,
      hotel_id: selectedAddOn.hotel_id,
      category: selectedAddOn.category,
      service_level: selectedAddOn.service_level,
      custom_fields: selectedAddOn.metadata?.custom_fields,
      base_cost: selectedAddOn.metadata?.base_cost,
      highest_price: selectedAddOn.metadata?.highest_price,
      images: selectedAddOn.images || [],
    },
  };
};

// Update booking add-on interface
interface UpdateBookingAddOnInput {
  quantity?: number;
  unit_price?: number;
  description?: string;
  customer_field_responses?: Record<string, any>;
  currency_code?: string;
  supplier_order_id?: string | null;
  order_status?: "pending" | "confirmed" | "in_progress" | "completed" | "cancelled" | null;
}

// Update an existing booking add-on
export const useUpdateBookingAddOn = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ id, ...updateData }: UpdateBookingAddOnInput & { id: string }): Promise<BookingAddOn> => {
      const response = await fetch(`/admin/booking-addons/${id}`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(updateData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || "Failed to update booking add-on");
      }

      const result = await response.json();
      return result.booking_add_on;
    },
    onSuccess: (data, variables) => {
      // Invalidate and refetch booking add-ons for the order
      queryClient.invalidateQueries({
        queryKey: ["booking-add-ons", data.order_id],
      });

      toast.success("Success", {
        description: "Add-on updated successfully!",
      });
    },
    onError: (error: Error) => {
      toast.error("Error", {
        description: error.message || "Failed to update add-on",
      });
    },
  });
};

// Delete a booking add-on
export const useDeleteBookingAddOn = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (id: string): Promise<void> => {
      const response = await fetch(`/admin/booking-addons/${id}`, {
        method: "DELETE",
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || "Failed to delete booking add-on");
      }
    },
    onSuccess: (_, id) => {
      // Invalidate all booking add-ons queries since we don't know the order_id
      queryClient.invalidateQueries({
        queryKey: ["booking-add-ons"],
      });

      toast.success("Success", {
        description: "Add-on removed successfully!",
      });
    },
    onError: (error: Error) => {
      toast.error("Error", {
        description: error.message || "Failed to remove add-on",
      });
    },
  });
};

// Hook to manage the complete add-on addition flow
export const useAddOnManagement = (bookingId: string) => {
  const addBookingAddOnMutation = useAddBookingAddOn();

  const addSelectedAddOnToBooking = async (selectedAddOn: SelectedAddOn) => {
    const input = convertSelectedAddOnToInput(selectedAddOn, bookingId);
    return addBookingAddOnMutation.mutateAsync(input);
  };

  return {
    addSelectedAddOnToBooking,
    isAdding: addBookingAddOnMutation.isPending,
    error: addBookingAddOnMutation.error,
  };
};

// Update a concierge order item
export const useUpdateConciergeOrderItem = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({
      conciergeOrderId,
      itemId,
      ...updateData
    }: {
      conciergeOrderId: string;
      itemId: string;
      quantity?: number;
      unit_price?: number;
      title?: string;
      status?: string;
      is_active?: boolean;
      metadata?: Record<string, any>;
    }): Promise<ConciergeOrderItem> => {
      const response = await fetch(`/admin/concierge-management/orders/${conciergeOrderId}/items/${itemId}`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(updateData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || "Failed to update concierge order item");
      }

      return response.json();
    },
    onSuccess: (data, variables) => {
      // Invalidate and refetch concierge order items
      queryClient.invalidateQueries({
        queryKey: ["concierge-order-items", variables.conciergeOrderId],
      });

      toast.success("Success", {
        description: "Item updated successfully!",
      });
    },
    onError: (error: Error) => {
      toast.error("Error", {
        description: error.message || "Failed to update item",
      });
    },
  });
};

// Delete a concierge order item
export const useDeleteConciergeOrderItem = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({
      conciergeOrderId,
      itemId
    }: {
      conciergeOrderId: string;
      itemId: string;
    }): Promise<void> => {
      const response = await fetch(`/admin/concierge-management/orders/${conciergeOrderId}/items/${itemId}`, {
        method: "DELETE",
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || "Failed to delete concierge order item");
      }
    },
    onSuccess: (data, variables) => {
      // Invalidate and refetch concierge order items
      queryClient.invalidateQueries({
        queryKey: ["concierge-order-items", variables.conciergeOrderId],
      });

      toast.success("Success", {
        description: "Item removed successfully!",
      });
    },
    onError: (error: Error) => {
      toast.error("Error", {
        description: error.message || "Failed to remove item",
      });
    },
  });
};
