import { useCallback } from 'react';
import { handleAccountDeactivation, isAccountDeactivatedResponse } from '../lib/auth-interceptor';

/**
 * React hook for handling authentication-related errors
 * Use this in components that make direct API calls
 */
export const useAuthHandler = () => {
  /**
   * Handle API response errors, checking for account deactivation
   */
  const handleApiError = useCallback((error: any) => {
    // Check if error indicates account deactivation
    if (error && error.response) {
      const { status, data } = error.response;
      if (status === 403 && isAccountDeactivatedResponse(data)) {
        handleAccountDeactivation(data);
        return true; // Indicates the error was handled
      }
    }
    
    // Check direct error object
    if (isAccountDeactivatedResponse(error)) {
      handleAccountDeactivation(error);
      return true; // Indicates the error was handled
    }
    
    return false; // Error was not handled
  }, []);

  /**
   * Handle fetch response, checking for account deactivation
   */
  const handleFetchResponse = useCallback(async (response: Response) => {
    if (response.status === 403) {
      try {
        const data = await response.clone().json();
        if (isAccountDeactivatedResponse(data)) {
          handleAccountDeactivation(data);
          throw new Error('Account deactivated');
        }
      } catch (jsonError) {
        // If we can't parse JSON, it's not our specific error format
        // Let the original response be handled normally
      }
    }
    return response;
  }, []);

  /**
   * Wrapper for async functions that might encounter auth errors
   */
  const withAuthHandling = useCallback(<T extends (...args: any[]) => Promise<any>>(
    asyncFn: T
  ): T => {
    return (async (...args: any[]) => {
      try {
        return await asyncFn(...args);
      } catch (error) {
        const wasHandled = handleApiError(error);
        if (!wasHandled) {
          throw error; // Re-throw if not an auth error
        }
      }
    }) as T;
  }, [handleApiError]);

  return {
    handleApiError,
    handleFetchResponse,
    withAuthHandling,
  };
};

export default useAuthHandler;
