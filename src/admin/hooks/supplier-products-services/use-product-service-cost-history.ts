import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { useEffect } from "react";
import { toast } from "@camped-ai/ui";

// Types
export interface ProductServiceCostHistory {
  id: string;
  product_service_id: string;
  previous_cost?: number;
  new_cost?: number;
  change_reason?: string;
  changed_by_user_id?: string;
  created_at: string;
  updated_at: string;
  product_service?: {
    id: string;
    name: string;
    type: string;
  };
}

export interface ProductServiceCostHistoryStats {
  total_changes: number;
  cost_increases: number;
  cost_decreases: number;
  average_cost_change_percentage?: number;
  most_recent_change?: ProductServiceCostHistory;
  most_frequent_changer?: {
    user_id: string;
    change_count: number;
  };
}

export interface ProductServiceCostHistoryFilters {
  limit?: number;
  offset?: number;
  changed_by_user_id?: string;
  date_from?: string;
  date_to?: string;
  has_cost_change?: boolean;
}

// Utility functions for cost change analysis
export const getCostChangeType = (
  history: ProductServiceCostHistory
): "increase" | "decrease" | "no-change" => {
  if (!history.previous_cost || !history.new_cost) return "no-change";

  // Convert to numbers if they're strings
  const previousCost =
    typeof history.previous_cost === "string"
      ? parseFloat(history.previous_cost)
      : history.previous_cost;
  const newCost =
    typeof history.new_cost === "string"
      ? parseFloat(history.new_cost)
      : history.new_cost;

  // Check if conversion resulted in valid numbers
  if (isNaN(previousCost) || isNaN(newCost)) return "no-change";

  if (newCost > previousCost) return "increase";
  if (newCost < previousCost) return "decrease";
  return "no-change";
};

export const formatCostChange = (
  history: ProductServiceCostHistory
): string => {
  if (!history.previous_cost || !history.new_cost) return "—";

  // Convert to numbers if they're strings
  const previousCost =
    typeof history.previous_cost === "string"
      ? parseFloat(history.previous_cost)
      : history.previous_cost;
  const newCost =
    typeof history.new_cost === "string"
      ? parseFloat(history.new_cost)
      : history.new_cost;

  // Check if conversion resulted in valid numbers
  if (isNaN(previousCost) || isNaN(newCost)) return "—";

  const change = newCost - previousCost;
  const percentage = (change / previousCost) * 100;

  const sign = change > 0 ? "+" : "";
  return `${sign}${change.toFixed(2)} CHF (${sign}${percentage.toFixed(1)}%)`;
};

// Hook to fetch cost history for a specific product service with real-time updates
export const useProductServiceCostHistory = (
  productServiceId: string,
  filters: ProductServiceCostHistoryFilters = {}
) => {
  const queryClient = useQueryClient();

  // Set up automatic refresh when product service is updated
  useEffect(() => {
    const handleProductServiceUpdate = () => {
      // Invalidate and refetch cost history when product service is updated
      queryClient.invalidateQueries({
        queryKey: ["product-service-cost-history", productServiceId],
      });
      queryClient.invalidateQueries({
        queryKey: ["product-service-cost-history-stats", productServiceId],
      });
    };

    // Listen for product service update events via custom event
    window.addEventListener(
      "product-service-updated",
      handleProductServiceUpdate
    );

    return () => {
      window.removeEventListener(
        "product-service-updated",
        handleProductServiceUpdate
      );
    };
  }, [productServiceId, queryClient]);

  return useQuery({
    queryKey: ["product-service-cost-history", productServiceId, filters],
    queryFn: async () => {
      const params = new URLSearchParams();

      if (filters.limit) params.append("limit", filters.limit.toString());
      if (filters.offset) params.append("offset", filters.offset.toString());
      if (filters.changed_by_user_id)
        params.append("changed_by_user_id", filters.changed_by_user_id);
      if (filters.date_from) params.append("date_from", filters.date_from);
      if (filters.date_to) params.append("date_to", filters.date_to);
      if (filters.has_cost_change !== undefined) {
        params.append("has_cost_change", filters.has_cost_change.toString());
      }

      const response = await fetch(
        `/admin/supplier-management/products-services/${productServiceId}/cost-history?${params}`,
        {
          credentials: "include",
        }
      );

      if (!response.ok) {
        throw new Error(`Failed to fetch cost history: ${response.statusText}`);
      }

      const data = await response.json();
      return {
        data: data.cost_history || [],
        count: data.count || 0,
        limit: data.limit || 25,
        offset: data.offset || 0,
      };
    },
    enabled: !!productServiceId,
  });
};

// Hook to fetch cost history stats for a specific product service
export const useProductServiceCostHistoryStats = (productServiceId: string) => {
  return useQuery({
    queryKey: ["product-service-cost-history-stats", productServiceId],
    queryFn: async () => {
      const response = await fetch(
        `/admin/supplier-management/products-services/${productServiceId}/cost-history/stats`,
        {
          credentials: "include",
        }
      );

      if (!response.ok) {
        throw new Error(
          `Failed to fetch cost history stats: ${response.statusText}`
        );
      }

      const data = await response.json();
      return data.stats;
    },
    enabled: !!productServiceId,
  });
};

// Hook to create cost history entry
export const useCreateProductServiceCostHistory = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: {
      product_service_id: string;
      previous_cost?: number;
      new_cost?: number;
      change_reason?: string;
    }) => {
      const response = await fetch(
        `/admin/supplier-management/products-services/${data.product_service_id}/cost-history`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          credentials: "include",
          body: JSON.stringify(data),
        }
      );

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || "Failed to create cost history");
      }

      return response.json();
    },
    onSuccess: (data, variables) => {
      // Invalidate and refetch cost history queries
      queryClient.invalidateQueries({
        queryKey: [
          "product-service-cost-history",
          variables.product_service_id,
        ],
      });
      queryClient.invalidateQueries({
        queryKey: [
          "product-service-cost-history-stats",
          variables.product_service_id,
        ],
      });

      toast.success("Cost history entry created successfully");
    },
    onError: (error: Error) => {
      toast.error(`Failed to create cost history: ${error.message}`);
    },
  });
};

// Hook for real-time cost history updates with automatic refresh on product service updates
export const useProductServiceCostHistoryRealtime = (
  productServiceId: string,
  filters: ProductServiceCostHistoryFilters = {}
) => {
  const queryClient = useQueryClient();
  const queryKey = ["product-service-cost-history", productServiceId, filters];

  // Set up automatic refresh when product service is updated
  useEffect(() => {
    const handleProductServiceUpdate = () => {
      // Invalidate and refetch cost history when product service is updated
      queryClient.invalidateQueries({
        queryKey: ["product-service-cost-history", productServiceId],
      });
      queryClient.invalidateQueries({
        queryKey: ["product-service-cost-history-stats", productServiceId],
      });
    };

    // Listen for product service update events via custom event
    window.addEventListener(
      "product-service-updated",
      handleProductServiceUpdate
    );

    return () => {
      window.removeEventListener(
        "product-service-updated",
        handleProductServiceUpdate
      );
    };
  }, [productServiceId, queryClient]);

  // Set up polling for real-time updates as fallback
  const query = useQuery({
    queryKey,
    queryFn: async () => {
      const params = new URLSearchParams();

      if (filters.limit) params.append("limit", filters.limit.toString());
      if (filters.offset) params.append("offset", filters.offset.toString());
      if (filters.changed_by_user_id)
        params.append("changed_by_user_id", filters.changed_by_user_id);
      if (filters.date_from) params.append("date_from", filters.date_from);
      if (filters.date_to) params.append("date_to", filters.date_to);
      if (filters.has_cost_change !== undefined) {
        params.append("has_cost_change", filters.has_cost_change.toString());
      }

      const response = await fetch(
        `/admin/supplier-management/products-services/${productServiceId}/cost-history?${params}`,
        {
          credentials: "include",
        }
      );

      if (!response.ok) {
        throw new Error(`Failed to fetch cost history: ${response.statusText}`);
      }

      const data = await response.json();
      return {
        data: data.cost_history || [],
        count: data.count || 0,
        limit: data.limit || 25,
        offset: data.offset || 0,
      };
    },
    enabled: !!productServiceId,
    refetchInterval: 60000, // Poll every 60 seconds as fallback
    refetchIntervalInBackground: false,
  });

  return query;
};
