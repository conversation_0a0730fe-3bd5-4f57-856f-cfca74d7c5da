import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "@camped-ai/ui";
import { sdk } from "../../lib/sdk";

export interface BusinessTypeValue {
  name: string;
  description: string; // Keep for backward compatibility
  service_categories: string[]; // Store category IDs
  is_active: boolean;
}

export interface BusinessType {
  id: string;
  entity_name: string;
  value: string; // JSON stringified BusinessTypeValue
  created_at: string;
  updated_at: string;
  // Computed properties for easier access
  parsed_value?: BusinessTypeValue;
}

export interface CreateBusinessTypeInput {
  name: string;
  description: string;
  service_categories: string[]; // Category IDs
  is_active?: boolean;
}

export interface UpdateBusinessTypeInput {
  name?: string;
  description?: string;
  service_categories?: string[]; // Category IDs
  is_active?: boolean;
}

export interface BusinessTypeFilters {
  name?: string;
  is_active?: boolean;
  limit?: number;
  offset?: number;
}

export interface BusinessTypeListResponse {
  lookups: BusinessType[];
  count: number;
  limit: number;
  offset: number;
}

// Helper functions for JSON value handling
const parseBusinessTypeValue = (value: string): BusinessTypeValue => {
  try {
    const parsed = JSON.parse(value);
    // Handle backward compatibility - if service_categories doesn't exist, create empty array
    return {
      name: parsed.name || "Unknown",
      description: parsed.description || "",
      service_categories: parsed.service_categories || [],
      is_active: parsed.is_active !== undefined ? parsed.is_active : true,
    };
  } catch {
    // Fallback for legacy data or invalid JSON
    return {
      name: "Unknown",
      description: value,
      service_categories: [],
      is_active: true,
    };
  }
};

const stringifyBusinessTypeValue = (
  data: CreateBusinessTypeInput | UpdateBusinessTypeInput
): string => {
  const value: BusinessTypeValue = {
    name: data.name || "",
    description: data.description || "",
    service_categories: data.service_categories || [],
    is_active: data.is_active !== undefined ? data.is_active : true,
  };
  return JSON.stringify(value);
};

const QUERY_KEYS = {
  all: ["supplier-products-services", "business-types"] as const,
  lists: () => [...QUERY_KEYS.all, "list"] as const,
  list: (filters: BusinessTypeFilters) =>
    [...QUERY_KEYS.lists(), filters] as const,
  details: () => [...QUERY_KEYS.all, "detail"] as const,
  detail: (id: string) => [...QUERY_KEYS.details(), id] as const,
};

export const useBusinessTypes = (filters: BusinessTypeFilters = {}) => {
  return useQuery({
    queryKey: QUERY_KEYS.list(filters),
    queryFn: async (): Promise<BusinessTypeListResponse> => {
      const params = new URLSearchParams();

      if (filters.name) params.append("name", filters.name);
      if (filters.is_active !== undefined)
        params.append("is_active", filters.is_active.toString());
      if (filters.limit) params.append("limit", filters.limit.toString());
      if (filters.offset) params.append("offset", filters.offset.toString());

      const response = await sdk.client.fetch(
        `/admin/lookups/entity_name/business_types?${params.toString()}`
      );

      const data = response as BusinessTypeListResponse;

      // Parse JSON values and add computed properties
      if (data.lookups) {
        data.lookups = data.lookups.map((businessType) => ({
          ...businessType,
          parsed_value: parseBusinessTypeValue(businessType.value),
        }));
      }

      return data;
    },
  });
};

export const useBusinessType = (id: string) => {
  return useQuery({
    queryKey: QUERY_KEYS.detail(id),
    queryFn: async (): Promise<{ lookup: BusinessType }> => {
      const response = await sdk.client.fetch(`/admin/lookups/${id}`);
      const data = response as { lookup: BusinessType };

      // Parse JSON value and add computed property
      if (data.lookup) {
        data.lookup.parsed_value = parseBusinessTypeValue(data.lookup.value);
      }

      return data;
    },
    enabled: !!id,
  });
};

export const useCreateBusinessType = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (
      data: CreateBusinessTypeInput
    ): Promise<{ lookup: BusinessType }> => {
      console.log("Creating business type with data:", data);

      const jsonValue = stringifyBusinessTypeValue(data);

      const response = await sdk.client.fetch("/admin/lookups", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: {
          entity_name: "business_types",
          value: jsonValue,
        },
      });

      console.log("Create business type success:", response);
      const result = response as { lookup: BusinessType };

      // Parse JSON value and add computed property
      if (result.lookup) {
        result.lookup.parsed_value = parseBusinessTypeValue(
          result.lookup.value
        );
      }

      return result;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: QUERY_KEYS.lists() });
      toast.success("Business type created successfully");
    },
    onError: (error: Error) => {
      console.error("Create business type mutation error:", error);
      toast.error(error.message || "Failed to create business type");
    },
  });
};

export const useUpdateBusinessType = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({
      id,
      data,
    }: {
      id: string;
      data: UpdateBusinessTypeInput;
    }): Promise<{ lookup: BusinessType }> => {
      console.log("Updating business type with data:", { id, data });

      const jsonValue = stringifyBusinessTypeValue(data);

      const response = await sdk.client.fetch(`/admin/lookups`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: {
          id,
          value: jsonValue,
        },
      });

      console.log("Update business type success:", response);
      const result = response as { lookup: BusinessType };

      // Parse JSON value and add computed property
      if (result.lookup) {
        result.lookup.parsed_value = parseBusinessTypeValue(
          result.lookup.value
        );
      }

      return result;
    },
    onSuccess: (_, { id }) => {
      queryClient.invalidateQueries({ queryKey: QUERY_KEYS.lists() });
      queryClient.invalidateQueries({ queryKey: QUERY_KEYS.detail(id) });
      toast.success("Business type updated successfully");
    },
    onError: (error: Error) => {
      console.error("Update business type mutation error:", error);
      toast.error(error.message || "Failed to update business type");
    },
  });
};

export const useDeleteBusinessType = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (id: string): Promise<{ lookup: BusinessType }> => {
      console.log("Deleting business type with id:", id);

      const response = await sdk.client.fetch(`/admin/lookups`, {
        method: "DELETE",
        headers: {
          "Content-Type": "application/json",
        },
        body: {
          ids: [id],
        },
      });

      console.log("Delete business type success:", response);
      return response as { lookup: BusinessType };
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: QUERY_KEYS.lists() });
      toast.success("Business type deleted successfully");
    },
    onError: (error: Error) => {
      console.error("Delete business type mutation error:", error);
      toast.error(error.message || "Failed to delete business type");
    },
  });
};
