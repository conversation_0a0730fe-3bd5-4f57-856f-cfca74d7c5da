import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { useEffect } from "react";
import { toast } from "@camped-ai/ui";

// Types
export interface SupplierOfferingCostHistory {
  id: string;
  supplier_offering_id: string;
  previous_cost?: number;
  new_cost?: number;
  previous_currency?: string;
  new_currency?: string;
  change_reason?: string;
  changed_by_user_id?: string;
  created_at: string;
  updated_at: string;
  supplier_offering?: {
    id: string;
    product_service?: {
      id: string;
      name: string;
      type: string;
    };
    supplier?: {
      id: string;
      name: string;
      type: string;
    };
  };
}

export interface SupplierOfferingCostHistoryStats {
  total_changes: number;
  cost_increases: number;
  cost_decreases: number;
  currency_changes: number;
  average_cost_change_percentage?: number;
  most_recent_change?: SupplierOfferingCostHistory;
  most_frequent_changer?: {
    user_id: string;
    change_count: number;
  };
}

export interface CreateSupplierOfferingCostHistoryData {
  supplier_offering_id: string;
  previous_cost?: number;
  new_cost?: number;
  previous_currency?: string;
  new_currency?: string;
  change_reason?: string;
  changed_by_user_id?: string;
}

export interface SupplierOfferingCostHistoryFilters {
  supplier_offering_id?: string;
  changed_by_user_id?: string;
  date_from?: string;
  date_to?: string;
  has_cost_change?: boolean;
  has_currency_change?: boolean;
  limit?: number;
  offset?: number;
}

// Query keys
const COST_HISTORY_QUERY_KEY = "supplier-offering-cost-history";

// Hook to fetch cost history for a specific supplier offering with real-time updates
export const useSupplierOfferingCostHistory = (
  supplierOfferingId: string,
  filters: Omit<SupplierOfferingCostHistoryFilters, "supplier_offering_id"> = {}
) => {
  const queryClient = useQueryClient();

  // Set up automatic refresh when supplier offering is updated
  useEffect(() => {
    const handleSupplierOfferingUpdate = () => {
      // Invalidate and refetch cost history when supplier offering is updated
      queryClient.invalidateQueries({
        queryKey: [COST_HISTORY_QUERY_KEY, supplierOfferingId],
      });
      queryClient.invalidateQueries({
        queryKey: [COST_HISTORY_QUERY_KEY, "stats", supplierOfferingId],
      });
    };

    // Listen for supplier offering update events via custom event
    window.addEventListener(
      "supplier-offering-updated",
      handleSupplierOfferingUpdate
    );

    return () => {
      window.removeEventListener(
        "supplier-offering-updated",
        handleSupplierOfferingUpdate
      );
    };
  }, [supplierOfferingId, queryClient]);

  return useQuery({
    queryKey: [COST_HISTORY_QUERY_KEY, supplierOfferingId, filters],
    queryFn: async () => {
      const params = new URLSearchParams();

      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== "") {
          if (value instanceof Date) {
            params.append(key, value.toISOString().split("T")[0]);
          } else {
            params.append(key, value.toString());
          }
        }
      });

      const response = await fetch(
        `/admin/supplier-management/supplier-offerings/${supplierOfferingId}/cost-history?${params.toString()}`
      );

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || "Failed to fetch cost history");
      }

      return response.json();
    },
    enabled: !!supplierOfferingId,
  });
};

// Hook to create cost history entry
export const useCreateSupplierOfferingCostHistory = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: CreateSupplierOfferingCostHistoryData) => {
      const response = await fetch(
        `/admin/supplier-management/supplier-offerings/${data.supplier_offering_id}/cost-history`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify(data),
        }
      );

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || "Failed to create cost history");
      }

      return response.json();
    },
    onSuccess: (_, variables) => {
      // Invalidate related queries
      queryClient.invalidateQueries({
        queryKey: [COST_HISTORY_QUERY_KEY, variables.supplier_offering_id],
      });
      toast.success("Cost history entry created successfully");
    },
    onError: (error: Error) => {
      toast.error(error.message || "Failed to create cost history");
    },
  });
};

// Hook to get cost history stats for a specific offering
export const useSupplierOfferingCostHistoryStats = (
  supplierOfferingId: string
) => {
  return useQuery({
    queryKey: [COST_HISTORY_QUERY_KEY, "stats", supplierOfferingId],
    queryFn: async () => {
      const response = await fetch(
        `/admin/supplier-management/supplier-offerings/${supplierOfferingId}/cost-history`
      );

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || "Failed to fetch cost history stats");
      }

      const data = await response.json();
      return data.stats;
    },
    enabled: !!supplierOfferingId,
  });
};

// Utility function to format cost change
export const formatCostChange = (
  previousCost?: number | string,
  newCost?: number | string,
  currency?: string
): string => {
  if (previousCost === undefined || newCost === undefined) {
    return "N/A";
  }

  // Convert to numbers if they're strings
  const prevCost =
    typeof previousCost === "string" ? parseFloat(previousCost) : previousCost;
  const newCostNum =
    typeof newCost === "string" ? parseFloat(newCost) : newCost;

  // Check if conversion resulted in valid numbers
  if (isNaN(prevCost) || isNaN(newCostNum)) {
    return "N/A";
  }

  const change = newCostNum - prevCost;
  const percentage = prevCost > 0 ? (change / prevCost) * 100 : 0;
  const sign = change > 0 ? "+" : "";
  const currencySymbol = currency || "";

  return `${sign}${change.toFixed(
    2
  )} ${currencySymbol} (${sign}${percentage.toFixed(1)}%)`;
};

// Utility function to get change type
export const getCostChangeType = (
  previousCost?: number | string,
  newCost?: number | string
): "increase" | "decrease" | "no-change" | "unknown" => {
  if (previousCost === undefined || newCost === undefined) {
    return "unknown";
  }

  // Convert to numbers if they're strings
  const prevCost =
    typeof previousCost === "string" ? parseFloat(previousCost) : previousCost;
  const newCostNum =
    typeof newCost === "string" ? parseFloat(newCost) : newCost;

  // Check if conversion resulted in valid numbers
  if (isNaN(prevCost) || isNaN(newCostNum)) {
    return "unknown";
  }

  if (newCostNum > prevCost) {
    return "increase";
  } else if (newCostNum < prevCost) {
    return "decrease";
  } else {
    return "no-change";
  }
};
