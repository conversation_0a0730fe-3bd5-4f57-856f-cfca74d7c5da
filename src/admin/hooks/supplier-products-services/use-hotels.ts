import { useQuery } from "@tanstack/react-query";
import { sdk } from "../../lib/sdk";

// Types
export interface Hotel {
  id: string;
  name: string;
  handle: string;
  description?: string;
  is_active: boolean;
  destination_id: string;
  address?: string;
  phone?: string;
  email?: string;
  website?: string;
  star_rating?: number;
  check_in_time?: string;
  check_out_time?: string;
  policies?: Record<string, any>;
  amenities?: string[];
  tags?: string[];
  metadata?: Record<string, any>;
  created_at: string;
  updated_at: string;
}

export interface HotelsResponse {
  hotels: Hotel[];
  count: number;
  limit: number;
  offset: number;
}

export interface HotelFilters {
  limit?: number;
  offset?: number;
  is_active?: boolean;
  destination_id?: string;
  star_rating?: number;
}

// Query Keys
export const hotelKeys = {
  all: ["hotels"] as const,
  lists: () => [...hotelKeys.all, "list"] as const,
  list: (filters: HotelFilters) => [...hotelKeys.lists(), filters] as const,
  detail: (id: string) => [...hotelKeys.all, "detail", id] as const,
};

// Hook to fetch hotels for products-services
export const useHotels = (filters: HotelFilters = {}) => {
  return useQuery({
    queryKey: hotelKeys.list(filters),
    queryFn: async (): Promise<HotelsResponse> => {
      const params = new URLSearchParams();

      if (filters.limit) params.append("limit", filters.limit.toString());
      if (filters.offset) params.append("offset", filters.offset.toString());
      if (filters.is_active !== undefined)
        params.append("is_active", filters.is_active.toString());
      if (filters.destination_id)
        params.append("destination_id", filters.destination_id);
      if (filters.star_rating)
        params.append("star_rating", filters.star_rating.toString());

      const url = `/admin/hotel-management/hotels${
        params.toString() ? `?${params.toString()}` : ""
      }`;

      try {
        const result = (await sdk.client.fetch(url)) as any;
        return result;
      } catch (error) {
        throw error;
      }
    },
    staleTime: 1000 * 60 * 5, // 5 minutes cache
    refetchOnWindowFocus: false,
  });
};

// Hook to fetch a single hotel
export const useHotel = (id: string) => {
  return useQuery({
    queryKey: hotelKeys.detail(id),
    queryFn: async (): Promise<{ hotel: Hotel }> => {
      try {
        const result = (await sdk.client.fetch(
          `/admin/hotel-management/hotels/${id}`
        )) as any;
        return result;
      } catch (error) {
        throw error;
      }
    },
    enabled: !!id,
  });
};
