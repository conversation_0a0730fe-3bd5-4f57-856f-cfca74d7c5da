import { useQuery } from "@tanstack/react-query";
import { sdk } from "../../lib/sdk";

// Types
export interface Destination {
  id: string;
  name: string;
  handle: string;
  description?: string;
  is_active: boolean;
  country: string;
  location?: string;
  tags?: string[];
  is_featured: boolean;
  ai_content?: string;
  metadata?: Record<string, any>;
  created_at: string;
  updated_at: string;
}

export interface DestinationsResponse {
  destinations: Destination[];
  count: number;
  limit: number;
  offset: number;
}

export interface DestinationFilters {
  limit?: number;
  offset?: number;
  is_featured?: boolean;
  is_active?: boolean;
  search?: string;
}

// Query Keys
export const destinationKeys = {
  all: ["destinations"] as const,
  lists: () => [...destinationKeys.all, "list"] as const,
  list: (filters: DestinationFilters) =>
    [...destinationKeys.lists(), filters] as const,
  detail: (id: string) => [...destinationKeys.all, "detail", id] as const,
};

// Hook to fetch destinations for products-services
export const useDestinations = (filters: DestinationFilters = {}) => {
  return useQuery({
    queryKey: destinationKeys.list(filters),
    queryFn: async (): Promise<DestinationsResponse> => {
      const params = new URLSearchParams();

      if (filters.limit) params.append("limit", filters.limit.toString());
      if (filters.offset) params.append("offset", filters.offset.toString());
      if (filters.is_featured !== undefined)
        params.append("is_featured", filters.is_featured.toString());
      if (filters.is_active !== undefined)
        params.append("is_active", filters.is_active.toString());
      if (filters.search) params.append("search", filters.search);

      const url = `/admin/hotel-management/destinations${
        params.toString() ? `?${params.toString()}` : ""
      }`;

      try {
        const result = (await sdk.client.fetch(url)) as any;
        return result;
      } catch (error) {
        throw error;
      }
    },
    staleTime: 1000 * 60 * 5, // 5 minutes cache
    refetchOnWindowFocus: false,
  });
};

// Hook specifically for hotel form dropdown - optimized for form usage
export const useDestinationsForHotelForm = () => {
  return useQuery({
    queryKey: destinationKeys.list({ is_active: true, limit: 100 }),
    queryFn: async (): Promise<DestinationsResponse> => {
      try {
        const result = (await sdk.client.fetch(
          "/admin/hotel-management/destinations?is_active=true&limit=100"
        )) as any;
        return result;
      } catch (error) {
        throw error;
      }
    },
    staleTime: 1000 * 60 * 15, // 15 minutes cache for form dropdowns
    gcTime: 1000 * 60 * 30, // 30 minutes garbage collection
    refetchOnWindowFocus: false,
    refetchOnReconnect: true,
    retry: 2,
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
  });
};

// Hook for hotel management page - pre-loads all destinations for mapping
export const useDestinationsForHotelManagement = () => {
  return useQuery({
    queryKey: destinationKeys.list({ limit: 1000 }), // Get all destinations
    queryFn: async (): Promise<DestinationsResponse> => {
      try {
        const result = (await sdk.client.fetch(
          "/admin/hotel-management/destinations?limit=1000"
        )) as any;
        return result;
      } catch (error) {
        throw error;
      }
    },
    staleTime: 1000 * 60 * 10, // 10 minutes cache
    gcTime: 1000 * 60 * 30, // 30 minutes garbage collection
    refetchOnWindowFocus: false,
    refetchOnReconnect: true,
    retry: 2,
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
  });
};

// Hook for batch destination lookup by IDs
export const useDestinationsBatch = (destinationIds: string[]) => {
  return useQuery({
    queryKey: [...destinationKeys.all, "batch", destinationIds.sort()],
    queryFn: async (): Promise<Record<string, Destination>> => {
      if (destinationIds.length === 0) {
        return {};
      }

      // Use the general destinations endpoint and filter client-side for now
      // In the future, this could be optimized with a batch API endpoint
      try {
        const data: DestinationsResponse = (await sdk.client.fetch(
          "/admin/hotel-management/destinations?limit=1000"
        )) as any;

        // Create a map of destination ID to destination object
        const destinationMap: Record<string, Destination> = {};
        data.destinations.forEach((destination) => {
          if (destinationIds.includes(destination.id)) {
            destinationMap[destination.id] = destination;
          }
        });

        return destinationMap;
      } catch (error) {
        throw error;
      }
    },
    enabled: destinationIds.length > 0,
    staleTime: 1000 * 60 * 15, // 15 minutes cache
    gcTime: 1000 * 60 * 30, // 30 minutes garbage collection
    refetchOnWindowFocus: false,
    retry: 2,
  });
};

// Hook to fetch a single destination
export const useDestination = (id: string) => {
  return useQuery({
    queryKey: destinationKeys.detail(id),
    queryFn: async (): Promise<{ destination: Destination }> => {
      try {
        const result = (await sdk.client.fetch(
          `/admin/hotel-management/destinations/${id}`
        )) as any;
        return result;
      } catch (error) {
        throw error;
      }
    },
    enabled: !!id,
  });
};
