import { describe, it, expect, beforeEach, jest } from '@jest/globals';
import { renderHook, waitFor } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import React from 'react';
import {
  useSupplierOfferingImportExport,
  generateSupplierOfferingTemplate,
  validateSupplierOfferingImportData,
} from '../use-supplier-offerings';

// Mock fetch globally
global.fetch = jest.fn();

const createWrapper = () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false },
    },
  });
  
  return ({ children }: { children: React.ReactNode }) =>
    React.createElement(QueryClientProvider, { client: queryClient }, children);
};

describe('useSupplierOfferingImportExport', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('generateSupplierOfferingTemplate', () => {
    it('should generate template with sample data', async () => {
      // Mock API responses
      (fetch as jest.MockedFunction<typeof fetch>)
        .mockResolvedValueOnce({
          ok: true,
          json: async () => ({
            suppliers: [
              { id: 'sup_1', name: 'Test Supplier 1' },
              { id: 'sup_2', name: 'Test Supplier 2' },
            ],
          }),
        } as Response)
        .mockResolvedValueOnce({
          ok: true,
          json: async () => ({
            product_services: [
              { id: 'ps_1', name: 'Test Product 1', category_id: 'cat_1' },
              { id: 'ps_2', name: 'Test Service 1', category_id: 'cat_2' },
            ],
          }),
        } as Response)
        .mockResolvedValueOnce({
          ok: true,
          json: async () => ({
            categories: [
              {
                id: 'cat_1',
                name: 'Test Category 1',
                dynamic_field_schema: [
                  {
                    key: 'test_field',
                    label: 'Test Field',
                    type: 'text',
                    required: true,
                  },
                ],
              },
              { id: 'cat_2', name: 'Test Category 2', dynamic_field_schema: [] },
            ],
          }),
        } as Response);

      const template = await generateSupplierOfferingTemplate();

      expect(template).toHaveLength(2);
      expect(template[0]).toMatchObject({
        supplier_name: 'Test Supplier 1',
        supplier_id: 'sup_1',
        product_service_name: 'Test Product 1',
        product_service_id: 'ps_1',
        category_name: 'Test Category 1',
        category_id: 'cat_1',
        status: 'active',
      });
      expect(template[0]).toHaveProperty('custom_field_test_field');
    });

    it('should return basic template when API calls fail', async () => {
      (fetch as jest.MockedFunction<typeof fetch>).mockRejectedValue(new Error('API Error'));

      const template = await generateSupplierOfferingTemplate();

      expect(template).toHaveLength(1);
      expect(template[0]).toMatchObject({
        supplier_name: 'Example Supplier Ltd',
        product_service_name: 'Sample Product/Service',
        status: 'active',
      });
    });
  });

  describe('validateSupplierOfferingImportData', () => {
    const mockSuppliers = [
      { id: 'sup_1', name: 'Test Supplier 1' },
      { id: 'sup_2', name: 'Test Supplier 2' },
    ];

    const mockProductsServices = [
      { id: 'ps_1', name: 'Test Product 1', category_id: 'cat_1' },
      { id: 'ps_2', name: 'Test Service 1', category_id: 'cat_2' },
    ];

    const mockCategories = [
      {
        id: 'cat_1',
        name: 'Test Category 1',
        dynamic_field_schema: [
          {
            key: 'required_field',
            label: 'Required Field',
            type: 'text',
            required: true,
          },
          {
            key: 'optional_field',
            label: 'Optional Field',
            type: 'number',
            required: false,
          },
        ],
      },
    ];

    it('should validate required fields', async () => {
      const invalidData = [
        {
          // Missing supplier_name and supplier_id
          product_service_name: 'Test Product 1',
        },
        {
          supplier_name: 'Test Supplier 1',
          // Missing product_service_name and product_service_id
        },
      ];

      const errors = await validateSupplierOfferingImportData(
        invalidData,
        mockSuppliers,
        mockProductsServices,
        mockCategories
      );

      expect(errors).toHaveLength(2);
      expect(errors[0]).toMatchObject({
        row: 2,
        field: 'supplier_name',
        message: 'Supplier name or ID is required',
      });
      expect(errors[1]).toMatchObject({
        row: 3,
        field: 'product_service_name',
        message: 'Product/Service name or ID is required',
      });
    });

    it('should validate supplier and product/service existence', async () => {
      const invalidData = [
        {
          supplier_name: 'Non-existent Supplier',
          product_service_name: 'Non-existent Product',
        },
      ];

      const errors = await validateSupplierOfferingImportData(
        invalidData,
        mockSuppliers,
        mockProductsServices,
        mockCategories
      );

      expect(errors).toHaveLength(2);
      expect(errors[0].message).toContain('Supplier "Non-existent Supplier" not found');
      expect(errors[1].message).toContain('Product/Service "Non-existent Product" not found');
    });

    it('should validate dynamic fields based on category schema', async () => {
      const invalidData = [
        {
          supplier_name: 'Test Supplier 1',
          product_service_name: 'Test Product 1',
          // Missing required custom field
          custom_field_optional_field: 'not a number',
        },
      ];

      const errors = await validateSupplierOfferingImportData(
        invalidData,
        mockSuppliers,
        mockProductsServices,
        mockCategories
      );

      expect(errors).toHaveLength(2);
      expect(errors.some(e => e.message.includes('Required Field is required'))).toBe(true);
      expect(errors.some(e => e.message.includes('Optional Field must be a valid number'))).toBe(true);
    });

    it('should validate date fields', async () => {
      const invalidData = [
        {
          supplier_name: 'Test Supplier 1',
          product_service_name: 'Test Product 1',
          active_from: 'invalid-date',
          active_to: 'another-invalid-date',
        },
      ];

      const errors = await validateSupplierOfferingImportData(
        invalidData,
        mockSuppliers,
        mockProductsServices,
        mockCategories
      );

      expect(errors.some(e => e.field === 'active_from' && e.message.includes('valid date'))).toBe(true);
      expect(errors.some(e => e.field === 'active_to' && e.message.includes('valid date'))).toBe(true);
    });

    it('should validate status field', async () => {
      const invalidData = [
        {
          supplier_name: 'Test Supplier 1',
          product_service_name: 'Test Product 1',
          status: 'invalid-status',
        },
      ];

      const errors = await validateSupplierOfferingImportData(
        invalidData,
        mockSuppliers,
        mockProductsServices,
        mockCategories
      );

      expect(errors.some(e => e.field === 'status' && e.message.includes('active" or "inactive'))).toBe(true);
    });

    it('should validate mandatory pricing calculator fields', async () => {
      const invalidData = [
        {
          supplier_name: 'Test Supplier 1',
          product_service_name: 'Test Product 1',
          // Missing all mandatory pricing fields
        },
      ];

      const errors = await validateSupplierOfferingImportData(
        invalidData,
        mockSuppliers,
        mockProductsServices,
        mockCategories
      );

      // Check that all mandatory pricing fields are validated
      expect(errors.some(e => e.field === 'gross_price' && e.message.includes('required'))).toBe(true);
      expect(errors.some(e => e.field === 'net_cost' && e.message.includes('required'))).toBe(true);
      expect(errors.some(e => e.field === 'margin_rate' && e.message.includes('required'))).toBe(true);
      expect(errors.some(e => e.field === 'selling_price' && e.message.includes('required'))).toBe(true);
      expect(errors.some(e => e.field === 'selling_currency' && e.message.includes('required'))).toBe(true);
      expect(errors.some(e => e.field === 'exchange_rate' && e.message.includes('required'))).toBe(true);
      expect(errors.some(e => e.field === 'selling_price_selling_currency' && e.message.includes('required'))).toBe(true);
    });

    it('should validate pricing field value ranges', async () => {
      const invalidData = [
        {
          supplier_name: 'Test Supplier 1',
          product_service_name: 'Test Product 1',
          gross_price: '-10', // Invalid: negative
          commission: '150', // Invalid: > 100%
          net_cost: '0', // Invalid: must be positive
          margin_rate: '100', // Invalid: must be < 100%
          selling_price: 'invalid', // Invalid: not a number
          selling_currency: 'INVALID', // Invalid: not 3-letter code
          exchange_rate: '-1', // Invalid: negative
          selling_price_selling_currency: '0', // Invalid: must be positive
        },
      ];

      const errors = await validateSupplierOfferingImportData(
        invalidData,
        mockSuppliers,
        mockProductsServices,
        mockCategories
      );

      expect(errors.some(e => e.field === 'gross_price' && e.message.includes('positive number'))).toBe(true);
      expect(errors.some(e => e.field === 'commission' && e.message.includes('between 0% and 100%'))).toBe(true);
      expect(errors.some(e => e.field === 'net_cost' && e.message.includes('positive number'))).toBe(true);
      expect(errors.some(e => e.field === 'margin_rate' && e.message.includes('between 0% and 99.99%'))).toBe(true);
      expect(errors.some(e => e.field === 'selling_price' && e.message.includes('positive number'))).toBe(true);
      expect(errors.some(e => e.field === 'selling_currency' && e.message.includes('3-letter code'))).toBe(true);
      expect(errors.some(e => e.field === 'exchange_rate' && e.message.includes('positive number'))).toBe(true);
      expect(errors.some(e => e.field === 'selling_price_selling_currency' && e.message.includes('positive number'))).toBe(true);
    });

    it('should accept valid pricing calculator data', async () => {
      const validData = [
        {
          supplier_name: 'Test Supplier 1',
          product_service_name: 'Test Product 1',
          gross_price: '100.00',
          commission: '10', // Optional but valid
          net_cost: '90.00',
          margin_rate: '20.5',
          selling_price: '112.50',
          selling_currency: 'CHF',
          exchange_rate: '1.0',
          selling_price_selling_currency: '112.50',
          status: 'active',
        },
      ];

      const errors = await validateSupplierOfferingImportData(
        validData,
        mockSuppliers,
        mockProductsServices,
        mockCategories
      );

      // Should not have any pricing-related errors
      const pricingFields = ['gross_price', 'commission', 'net_cost', 'margin_rate', 'selling_price', 'selling_currency', 'exchange_rate', 'selling_price_selling_currency'];
      const pricingErrors = errors.filter(e => pricingFields.includes(e.field));
      expect(pricingErrors).toHaveLength(0);
    });
  });

  describe('useSupplierOfferingImportExport hook', () => {
    it('should provide all necessary functions', () => {
      const wrapper = createWrapper();
      const { result } = renderHook(() => useSupplierOfferingImportExport(), { wrapper });

      expect(result.current).toHaveProperty('parseImportFile');
      expect(result.current).toHaveProperty('importSupplierOfferings');
      expect(result.current).toHaveProperty('isImporting');
      expect(result.current).toHaveProperty('importProgress');
      expect(result.current).toHaveProperty('generateTemplate');
      expect(result.current).toHaveProperty('validateData');
      expect(result.current).toHaveProperty('exportToCSV');
      expect(result.current).toHaveProperty('exportToExcel');
    });
  });
});
