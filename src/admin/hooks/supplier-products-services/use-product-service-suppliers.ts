import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "@camped-ai/ui";

// Types
export interface ProductServiceSupplier {
  id: string;
  product_service_id: string;
  supplier_id: string;
  cost: number;
  currency_code: string;
  availability: string;
  max_capacity?: number;
  season?: string;
  valid_from?: string;
  valid_until?: string;
  notes?: string;
  lead_time_days?: number;
  minimum_order?: number;
  is_active: boolean;
  is_preferred: boolean;
  created_by?: string;
  updated_by?: string;
  created_at: string;
  updated_at: string;
  // Populated supplier information
  supplier?: {
    id: string;
    name: string;
    type: string;
    status: string;
    primary_contact_name?: string;
    primary_contact_email?: string;
  };
}

export interface CreateProductServiceSupplierInput {
  product_service_id: string;
  supplier_id: string;
  cost: number;
  currency_code?: string;
  availability: string;
  max_capacity?: number;
  season?: string;
  valid_from?: string; // ISO date string
  valid_until?: string; // ISO date string
  notes?: string;
  lead_time_days?: number;
  minimum_order?: number;
  is_active?: boolean;
  is_preferred?: boolean;
}

export interface UpdateProductServiceSupplierInput {
  cost?: number;
  currency_code?: string;
  availability?: string;
  max_capacity?: number;
  season?: string;
  valid_from?: string; // ISO date string
  valid_until?: string; // ISO date string
  notes?: string;
  lead_time_days?: number;
  minimum_order?: number;
  is_active?: boolean;
  is_preferred?: boolean;
}

export interface ProductServiceSupplierFilters {
  product_service_id?: string;
  supplier_id?: string;
  is_active?: boolean;
  is_preferred?: boolean;
  season?: string;
  limit?: number;
  offset?: number;
}

export interface ProductServiceSupplierListResponse {
  product_service_suppliers: ProductServiceSupplier[];
  count: number;
  limit: number;
  offset: number;
}

// Query keys
const QUERY_KEYS = {
  all: ["product-service-suppliers"] as const,
  lists: () => [...QUERY_KEYS.all, "list"] as const,
  list: (filters: ProductServiceSupplierFilters) => [...QUERY_KEYS.lists(), filters] as const,
  details: () => [...QUERY_KEYS.all, "detail"] as const,
  detail: (id: string) => [...QUERY_KEYS.details(), id] as const,
  byProductService: (productServiceId: string) => [...QUERY_KEYS.all, "by-product-service", productServiceId] as const,
};

// Hook to fetch product service suppliers
export const useProductServiceSuppliers = (filters: ProductServiceSupplierFilters = {}) => {
  return useQuery({
    queryKey: QUERY_KEYS.list(filters),
    queryFn: async (): Promise<ProductServiceSupplierListResponse> => {
      const params = new URLSearchParams();
      
      if (filters.product_service_id) params.append("product_service_id", filters.product_service_id);
      if (filters.supplier_id) params.append("supplier_id", filters.supplier_id);
      if (filters.is_active !== undefined) params.append("is_active", filters.is_active.toString());
      if (filters.is_preferred !== undefined) params.append("is_preferred", filters.is_preferred.toString());
      if (filters.season) params.append("season", filters.season);
      if (filters.limit) params.append("limit", filters.limit.toString());
      if (filters.offset) params.append("offset", filters.offset.toString());

      const url = `/admin/supplier-management/products-services/suppliers${params.toString() ? `?${params.toString()}` : ''}`;

      const response = await fetch(url, {
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
      });
      
      if (!response.ok) {
        throw new Error("Failed to fetch product service suppliers");
      }
      
      return response.json();
    },
  });
};

// Hook to fetch suppliers for a specific product/service
export const useProductServiceSuppliersForProduct = (productServiceId: string) => {
  return useQuery({
    queryKey: QUERY_KEYS.byProductService(productServiceId),
    queryFn: async (): Promise<ProductServiceSupplierListResponse> => {
      const response = await fetch(
        `/admin/supplier-management/products-services/${productServiceId}/suppliers`,
        {
          credentials: 'include',
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
      
      if (!response.ok) {
        throw new Error("Failed to fetch suppliers for product/service");
      }
      
      return response.json();
    },
    enabled: !!productServiceId,
  });
};

// Hook to create a product service supplier link
export const useCreateProductServiceSupplier = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: CreateProductServiceSupplierInput): Promise<{ product_service_supplier: ProductServiceSupplier }> => {
      console.log("Creating product service supplier with data:", data);

      const response = await fetch("/admin/supplier-management/products-services/suppliers", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
        credentials: "include",
      });

      if (!response.ok) {
        const error = await response.json();
        console.error("API Error Response:", error);
        throw new Error(error.message || "Failed to link supplier to product/service");
      }

      return response.json();
    },
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: QUERY_KEYS.lists() });
      queryClient.invalidateQueries({ queryKey: QUERY_KEYS.byProductService(variables.product_service_id) });
      toast.success("Supplier linked successfully");
    },
    onError: (error: Error) => {
      toast.error(error.message);
    },
  });
};

// Hook to update a product service supplier link
export const useUpdateProductServiceSupplier = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ id, data }: { id: string; data: UpdateProductServiceSupplierInput }): Promise<{ product_service_supplier: ProductServiceSupplier }> => {
      const response = await fetch(
        `/admin/supplier-management/products-services/suppliers/${id}`,
        {
          method: "PUT",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify(data),
          credentials: "include",
        }
      );

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || "Failed to update supplier link");
      }

      return response.json();
    },
    onSuccess: (result) => {
      queryClient.invalidateQueries({ queryKey: QUERY_KEYS.lists() });
      queryClient.invalidateQueries({ queryKey: QUERY_KEYS.byProductService(result.product_service_supplier.product_service_id) });
      toast.success("Supplier link updated successfully");
    },
    onError: (error: Error) => {
      toast.error(error.message);
    },
  });
};

// Hook to delete a product service supplier link
export const useDeleteProductServiceSupplier = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (id: string): Promise<{ deleted: boolean; id: string }> => {
      const response = await fetch(
        `/admin/supplier-management/products-services/suppliers/${id}`,
        {
          method: "DELETE",
          headers: {
            "Content-Type": "application/json",
          },
          credentials: "include",
        }
      );

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || "Failed to remove supplier link");
      }

      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: QUERY_KEYS.lists() });
      toast.success("Supplier link removed successfully");
    },
    onError: (error: Error) => {
      toast.error(error.message);
    },
  });
};
