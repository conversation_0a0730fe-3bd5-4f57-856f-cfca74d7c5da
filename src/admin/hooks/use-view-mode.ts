import { useState, useEffect } from "react";

export type ViewMode = "table" | "kanban";

interface UseViewModeOptions {
  defaultMode?: ViewMode;
  storageKey?: string;
}

export const useViewMode = (options: UseViewModeOptions = {}) => {
  const {
    defaultMode = "table",
    storageKey = "tasks-view-mode",
  } = options;

  // Initialize state with default mode
  const [viewMode, setViewMode] = useState<ViewMode>(defaultMode);

  // Load saved preference from sessionStorage on mount
  useEffect(() => {
    try {
      const savedMode = sessionStorage.getItem(storageKey);
      if (savedMode && (savedMode === "table" || savedMode === "kanban")) {
        setViewMode(savedMode as ViewMode);
      }
    } catch (error) {
      // Silently fail if sessionStorage is not available
      console.warn("Failed to load view mode from sessionStorage:", error);
    }
  }, [storageKey]);

  // Save preference to sessionStorage when it changes
  const updateViewMode = (mode: ViewMode) => {
    setViewMode(mode);
    try {
      sessionStorage.setItem(storageKey, mode);
    } catch (error) {
      // Silently fail if sessionStorage is not available
      console.warn("Failed to save view mode to sessionStorage:", error);
    }
  };

  return {
    viewMode,
    setViewMode: updateViewMode,
    isTableView: viewMode === "table",
    isKanbanView: viewMode === "kanban",
  };
};
