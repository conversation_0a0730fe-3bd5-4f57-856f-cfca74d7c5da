import { useState, useEffect, useCallback } from "react";

// Types for the inventory add-ons

export interface InventoryAddOn {
  id: string;
  title: string;
  sku?: string;
  created_at: string;
  updated_at: string;

  // Core fields
  category: string;
  status: string;
  description: string;

  // Industry-standard pricing columns
  cost: number;
  cost_currency: string;
  selling_margin: number;
  selling_price_cost_currency: number;
  selling_price: number;
  selling_currency: string;

  // Additional metadata
  service_level?: string;
  max_capacity?: number;
  is_active: boolean;

  // Location information
  hotel_id?: string;
  hotel_name?: string;
  destination_id?: string;
  destination_name?: string;

  // Custom fields as object
  custom_fields?: Record<string, any>;

  // Metadata for additional configuration
  metadata?: Record<string, any>;
}

export interface InventoryAddOnFilters {
  search?: string;
  supplier_id?: string;
  category?: string;
  status?: string;
  is_active?: boolean;
  currency_code?: string;
  sort_by?:
    | "title"
    | "created_at"
    | "category"
    | "base_cost"
    | "status"
    | "updated_at";
  sort_order?: "asc" | "desc";
  limit?: number;
  offset?: number;
}

export interface InventoryAddOnFilterOptions {
  categories: string[];
  statuses: string[];
  suppliers: Array<{ id: string; name: string }>;
}

export interface InventoryAddOnSummary {
  total_add_ons: number;
  active_add_ons: number;
  unique_categories: number;
  unique_statuses: number;
}

export interface InventoryAddOnResponse {
  add_ons: InventoryAddOn[];
  count: number;
  limit: number;
  offset: number;
  filters: InventoryAddOnFilterOptions;
  summary: InventoryAddOnSummary;
}

export interface UseInventoryAddOnsOptions extends InventoryAddOnFilters {
  enabled?: boolean;
}

export interface UseInventoryAddOnsResult {
  addOns: InventoryAddOn[] | null;
  loading: boolean;
  error: string | null;
  summary: InventoryAddOnSummary | null;
  filterOptions: InventoryAddOnFilterOptions | null;
  refetch: () => void;
  totalCount: number;
}

/**
 * Hook for fetching and managing inventory add-ons data
 */
export const useInventoryAddOns = (
  options: UseInventoryAddOnsOptions = {}
): UseInventoryAddOnsResult => {
  const [addOns, setAddOns] = useState<InventoryAddOn[] | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [summary, setSummary] = useState<InventoryAddOnSummary | null>(null);
  const [filterOptions, setFilterOptions] =
    useState<InventoryAddOnFilterOptions | null>(null);
  const [totalCount, setTotalCount] = useState(0);

  const {
    enabled = true,
    search,
    supplier_id,
    category,
    status,
    is_active,
    currency_code = "CHF",
    sort_by = "title",
    sort_order = "asc",
    limit = 20,
    offset = 0,
  } = options;

  const fetchAddOns = useCallback(async () => {
    if (!enabled) return;

    setLoading(true);
    setError(null);

    try {
      // Build query parameters
      const params = new URLSearchParams();

      if (search) params.append("search", search);
      if (supplier_id) params.append("supplier_id", supplier_id);
      if (category) params.append("category", category);
      if (status) params.append("status", status);
      if (is_active !== undefined)
        params.append("is_active", is_active ? "true" : "false");
      if (currency_code) params.append("currency_code", currency_code);
      if (sort_by) params.append("sort_by", sort_by);
      if (sort_order) params.append("sort_order", sort_order);
      if (limit) params.append("limit", limit.toString());
      if (offset) params.append("offset", offset.toString());

      // Make the API call
      const response = await fetch(
        `/admin/inventory/add-ons?${params.toString()}`,
        {
          method: "GET",
          headers: {
            "Content-Type": "application/json",
          },
          credentials: "include",
        }
      );

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(
          errorData.message || `HTTP error! status: ${response.status}`
        );
      }

      const data: InventoryAddOnResponse = await response.json();

      setAddOns(data.add_ons);
      setSummary(data.summary);
      setFilterOptions(data.filters);
      setTotalCount(data.count);
    } catch (err) {
      console.error("Error fetching inventory add-ons:", err);
      setError(err instanceof Error ? err.message : "Failed to fetch add-ons");
      setAddOns(null);
      setSummary(null);
      setFilterOptions(null);
      setTotalCount(0);
    } finally {
      setLoading(false);
    }
  }, [
    enabled,
    search,
    supplier_id,
    category,
    status,
    is_active,
    currency_code,
    sort_by,
    sort_order,
    limit,
    offset,
  ]);

  // Fetch data when dependencies change
  useEffect(() => {
    fetchAddOns();
  }, [fetchAddOns]);

  return {
    addOns,
    loading,
    error,
    summary,
    filterOptions,
    refetch: fetchAddOns,
    totalCount,
  };
};

/**
 * Hook for fetching a single inventory add-on by ID
 */
export const useInventoryAddOn = (id: string) => {
  const [addOn, setAddOn] = useState<InventoryAddOn | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchAddOn = useCallback(async () => {
    if (!id) return;

    setLoading(true);
    setError(null);

    try {
      const response = await fetch(`/admin/inventory/add-ons/${id}`, {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
        },
        credentials: "include",
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(
          errorData.message || `HTTP error! status: ${response.status}`
        );
      }

      const data = await response.json();
      setAddOn(data.add_on);
    } catch (err) {
      console.error("Error fetching inventory add-on:", err);
      setError(err instanceof Error ? err.message : "Failed to fetch add-on");
      setAddOn(null);
    } finally {
      setLoading(false);
    }
  }, [id]);

  useEffect(() => {
    fetchAddOn();
  }, [fetchAddOn]);

  return {
    addOn,
    loading,
    error,
    refetch: fetchAddOn,
  };
};
