import { useSearchParams } from "react-router-dom";
import { useCallback, useRef, useMemo } from "react";

interface QueryParamUpdates {
  [key: string]: string | string[] | number[] | boolean | null | undefined;
}

interface UseOptimizedQueryParamsOptions {
  debounceMs?: number;
  batchUpdates?: boolean;
}

/**
 * Custom hook for optimized query parameter handling with debouncing and batching
 * Reduces re-renders and improves performance for frequent URL updates
 */
export function useOptimizedQueryParams(
  options: UseOptimizedQueryParamsOptions = {}
) {
  const { debounceMs = 300, batchUpdates = true } = options;
  const [searchParams, setSearchParams] = useSearchParams();

  // Refs for debouncing and batching
  const debounceTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const pendingUpdatesRef = useRef<QueryParamUpdates>({});
  const isUpdatingRef = useRef(false);

  // Memoized current params object for efficient access
  const currentParams = useMemo(() => {
    const params: Record<string, string | null> = {};
    for (const [key, value] of searchParams.entries()) {
      params[key] = value;
    }
    return params;
  }, [searchParams]);

  // Helper to convert values to URL-safe strings
  const normalizeValue = useCallback(
    (
      value: string | string[] | number[] | boolean | null | undefined
    ): string | null => {
      if (value === null || value === undefined || value === "") {
        return null;
      }
      // Handle boolean false explicitly - it should be converted to "false" string, not null
      if (value === false) {
        return "false";
      }
      if (Array.isArray(value)) {
        return value.length === 0 ? null : value.join(",");
      }
      return String(value);
    },
    []
  );

  // Batch update function
  const flushUpdates = useCallback(() => {
    if (
      isUpdatingRef.current ||
      Object.keys(pendingUpdatesRef.current).length === 0
    ) {
      return;
    }

    isUpdatingRef.current = true;
    const newParams = new URLSearchParams(searchParams);

    Object.entries(pendingUpdatesRef.current).forEach(([key, value]) => {
      const normalizedValue = normalizeValue(value);
      if (normalizedValue === null) {
        newParams.delete(key);
      } else {
        newParams.set(key, normalizedValue);
      }
    });

    // Clear pending updates
    pendingUpdatesRef.current = {};

    // Update URL
    setSearchParams(newParams, { replace: true });

    // Reset updating flag after a brief delay to prevent rapid successive updates
    setTimeout(() => {
      isUpdatingRef.current = false;
    }, 50);
  }, [searchParams, setSearchParams, normalizeValue]);

  // Debounced update function
  const updateParams = useCallback(
    (updates: QueryParamUpdates, immediate = false) => {
      // Clear existing timeout
      if (debounceTimeoutRef.current) {
        clearTimeout(debounceTimeoutRef.current);
      }

      if (batchUpdates) {
        // Add to pending updates
        Object.assign(pendingUpdatesRef.current, updates);
      } else {
        // Replace pending updates
        pendingUpdatesRef.current = { ...updates };
      }

      if (immediate) {
        flushUpdates();
      } else {
        // Set new timeout
        debounceTimeoutRef.current = setTimeout(flushUpdates, debounceMs);
      }
    },
    [debounceMs, batchUpdates, flushUpdates]
  );

  // Immediate update function (no debouncing)
  const updateParamsImmediate = useCallback(
    (updates: QueryParamUpdates) => {
      updateParams(updates, true);
    },
    [updateParams]
  );

  // Get a specific parameter value with type safety
  const getParam = useCallback(
    (key: string): string | null => {
      return currentParams[key] || null;
    },
    [currentParams]
  );

  // Get array parameter (comma-separated values)
  const getArrayParam = useCallback(
    (key: string): string[] => {
      const value = getParam(key);
      return value ? value.split(",").filter(Boolean) : [];
    },
    [getParam]
  );

  // Get number array parameter
  const getNumberArrayParam = useCallback(
    (key: string): number[] => {
      return getArrayParam(key)
        .map(Number)
        .filter((n) => !isNaN(n));
    },
    [getArrayParam]
  );

  // Get boolean parameter
  const getBooleanParam = useCallback(
    (key: string): boolean | null => {
      const value = getParam(key);
      if (value === "true") return true;
      if (value === "false") return false;
      return null;
    },
    [getParam]
  );

  // Cleanup on unmount
  useCallback(() => {
    return () => {
      if (debounceTimeoutRef.current) {
        clearTimeout(debounceTimeoutRef.current);
      }
    };
  }, []);

  return {
    // Current params
    searchParams,
    currentParams,

    // Update functions
    updateParams,
    updateParamsImmediate,

    // Getter functions
    getParam,
    getArrayParam,
    getNumberArrayParam,
    getBooleanParam,

    // Utility
    flushUpdates,
  };
}

// Specialized hook for search with debouncing
export function useOptimizedSearch(key = "search", debounceMs = 300) {
  const { getParam, updateParams } = useOptimizedQueryParams({ debounceMs });

  const searchValue = getParam(key) || "";

  const updateSearch = useCallback(
    (value: string) => {
      updateParams({ [key]: value });
    },
    [key, updateParams]
  );

  return [searchValue, updateSearch] as const;
}
