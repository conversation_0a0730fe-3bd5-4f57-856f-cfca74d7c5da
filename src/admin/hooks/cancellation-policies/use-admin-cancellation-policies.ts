import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { sdk } from "../../lib/sdk";
import { toast } from "@camped-ai/ui";
import { CancellationPolicyData } from "../../../types";

// Query keys for caching
export const cancellationPolicyKeys = {
  all: ["admin", "cancellation-policies"] as const,
  lists: () => [...cancellationPolicyKeys.all, "list"] as const,
  list: (filters: Record<string, any>) => [...cancellationPolicyKeys.lists(), { filters }] as const,
  details: () => [...cancellationPolicyKeys.all, "detail"] as const,
  detail: (id: string) => [...cancellationPolicyKeys.details(), id] as const,
};

// Types for API requests
export type CreateCancellationPolicyData = {
  name: string;
  description?: string;
  hotel_id: string;
  days_before_checkin: number;
  refund_type: "percentage" | "fixed" | "no_refund";
  refund_amount: number;
  is_active?: boolean;
  metadata?: Record<string, any>;
};

export type UpdateCancellationPolicyData = Partial<CreateCancellationPolicyData>;

// Hook to fetch cancellation policies for a hotel
export const useAdminCancellationPolicies = (hotelId?: string) => {
  return useQuery({
    queryKey: cancellationPolicyKeys.list({ hotel_id: hotelId }),
    queryFn: async () => {
      const searchParams = new URLSearchParams();
      if (hotelId) searchParams.set("hotel_id", hotelId);

      const url = `/admin/hotel-management/cancellation-policies${
        searchParams.toString() ? `?${searchParams.toString()}` : ""
      }`;

      return sdk.client.fetch(url);
    },
    enabled: !!hotelId,
  });
};

// Hook to fetch a single cancellation policy
export const useAdminCancellationPolicy = (id: string) => {
  return useQuery({
    queryKey: cancellationPolicyKeys.detail(id),
    queryFn: async () => {
      return sdk.client.fetch(`/admin/hotel-management/cancellation-policies/${id}`);
    },
    enabled: !!id,
  });
};

// Hook to create a new cancellation policy
export const useAdminCreateCancellationPolicy = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: CreateCancellationPolicyData) => {
      return sdk.client.fetch("/admin/hotel-management/cancellation-policies", {
        method: "POST",
        body: data,
      });
    },
    onSuccess: (data, variables) => {
      // Invalidate and refetch policies for this hotel
      queryClient.invalidateQueries({
        queryKey: cancellationPolicyKeys.list({ hotel_id: variables.hotel_id }),
      });
      queryClient.invalidateQueries({
        queryKey: cancellationPolicyKeys.lists(),
      });
      toast.success("Cancellation policy created successfully");
    },
    onError: (error: Error) => {
      toast.error(`Failed to create policy: ${error.message}`);
    },
  });
};

// Hook to update a cancellation policy
export const useAdminUpdateCancellationPolicy = (id: string) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: UpdateCancellationPolicyData) => {
      console.log("Updating policy with data:", data);

      // Filter out read-only fields that shouldn't be sent to the API
      const {
        id: _id,
        hotel_id: _hotel_id,
        created_at: _created_at,
        updated_at: _updated_at,
        deleted_at: _deleted_at,
        ...updateData
      } = data as any;

      console.log("Filtered update data:", updateData);

      // Use native fetch for UPDATE operation
      const response = await fetch(`/admin/hotel-management/cancellation-policies/${id}`, {
        method: "POST", // API uses POST for updates
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(updateData),
        credentials: "include",
      });

      console.log("Update response status:", response.status);

      if (!response.ok) {
        let errorMessage = `Failed to update policy: ${response.statusText}`;
        try {
          const errorData = await response.json();
          console.error("Update error response:", errorData);
          errorMessage = errorData.message || errorData.error || errorMessage;
        } catch (e) {
          const errorText = await response.text();
          console.error("Update error response (text):", errorText);
          errorMessage = errorText || errorMessage;
        }
        throw new Error(errorMessage);
      }

      const result = await response.json();
      console.log("Update successful:", result);
      return result;
    },
    onSuccess: (data, variables) => {
      // Invalidate related queries
      queryClient.invalidateQueries({
        queryKey: cancellationPolicyKeys.detail(id),
      });
      queryClient.invalidateQueries({
        queryKey: cancellationPolicyKeys.lists(),
      });
      // Also invalidate hotel-specific queries if we have hotel_id
      if (data?.cancellation_policy?.hotel_id) {
        queryClient.invalidateQueries({
          queryKey: cancellationPolicyKeys.list({ hotel_id: data.cancellation_policy.hotel_id }),
        });
      }
      toast.success("Cancellation policy updated successfully");
    },
    onError: (error: Error) => {
      toast.error(`Failed to update policy: ${error.message}`);
    },
  });
};

// Hook to delete a cancellation policy
export const useAdminDeleteCancellationPolicy = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ id, hotelId }: { id: string; hotelId: string }) => {
      // Use native fetch for DELETE operation
      const response = await fetch(`/admin/hotel-management/cancellation-policies/${id}`, {
        method: "DELETE",
        credentials: "include",
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Failed to delete policy: ${response.statusText}`);
      }

      return { id, hotelId };
    },
    onSuccess: (data) => {
      // Invalidate related queries to update the UI
      queryClient.invalidateQueries({
        queryKey: cancellationPolicyKeys.detail(data.id),
      });
      queryClient.invalidateQueries({
        queryKey: cancellationPolicyKeys.lists(),
      });
      queryClient.invalidateQueries({
        queryKey: cancellationPolicyKeys.list({ hotel_id: data.hotelId }),
      });
      toast.success("Cancellation policy deleted successfully");
    },
    onError: (error: Error) => {
      toast.error(`Failed to delete policy: ${error.message}`);
    },
  });
};
