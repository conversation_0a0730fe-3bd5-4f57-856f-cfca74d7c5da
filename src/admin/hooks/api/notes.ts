import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "@camped-ai/ui";

// Types
export interface Note {
  id: string;
  title: string;
  content?: string;
  entity: string;
  entity_id: string;
  deleted: boolean;
  deleted_at?: string;
  created_by_id?: string;
  updated_by_id?: string;
  created_at: string;
  updated_at: string;
}

export interface NotesResponse {
  notes: Note[];
  count: number;
  limit: number;
  offset: number;
}

export interface NoteFilters {
  limit?: number;
  offset?: number;
  q?: string;
  entity?: string;
  entity_id?: string;
  created_by_id?: string;
  created_at_gte?: string;
  created_at_lte?: string;
  order?: string;
  sort_order?: "asc" | "desc";
}

export interface CreateNoteData {
  title: string;
  content?: string;
  entity: string;
  entity_id: string;
}

export interface UpdateNoteData {
  title?: string;
  content?: string;
}

// API functions
const fetchNotes = async (filters: NoteFilters = {}): Promise<NotesResponse> => {
  const params = new URLSearchParams();
  
  Object.entries(filters).forEach(([key, value]) => {
    if (value !== undefined && value !== null && value !== "") {
      params.append(key, String(value));
    }
  });
  
  const response = await fetch(`/admin/concierge-management/notes?${params.toString()}`, {
    credentials: "include",
  });
  
  if (!response.ok) {
    throw new Error("Failed to fetch notes");
  }
  
  return response.json();
};

const fetchNote = async (noteId: string): Promise<{ note: Note }> => {
  const response = await fetch(`/admin/concierge-management/notes/${noteId}`, {
    credentials: "include",
  });
  
  if (!response.ok) {
    throw new Error("Failed to fetch note");
  }
  
  return response.json();
};

const createNote = async (data: CreateNoteData): Promise<{ note: Note }> => {
  const response = await fetch("/admin/concierge-management/notes", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    credentials: "include",
    body: JSON.stringify(data),
  });
  
  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.message || "Failed to create note");
  }
  
  return response.json();
};

const updateNote = async (noteId: string, data: UpdateNoteData): Promise<{ note: Note }> => {
  const response = await fetch(`/admin/concierge-management/notes/${noteId}`, {
    method: "PUT",
    headers: {
      "Content-Type": "application/json",
    },
    credentials: "include",
    body: JSON.stringify(data),
  });
  
  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.message || "Failed to update note");
  }
  
  return response.json();
};

const deleteNote = async (noteId: string): Promise<void> => {
  const response = await fetch(`/admin/concierge-management/notes/${noteId}`, {
    method: "DELETE",
    credentials: "include",
  });
  
  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.message || "Failed to delete note");
  }
};

// React Query hooks
export const useNotes = (filters: NoteFilters = {}) => {
  return useQuery({
    queryKey: ["notes", filters],
    queryFn: () => fetchNotes(filters),
    staleTime: 30000, // 30 seconds
  });
};

export const useEntityNotes = (entity: string, entityId: string, filters: Omit<NoteFilters, 'entity' | 'entity_id'> = {}) => {
  return useQuery({
    queryKey: ["entity-notes", entity, entityId, filters],
    queryFn: () => fetchNotes({ ...filters, entity, entity_id: entityId }),
    staleTime: 30000, // 30 seconds
    enabled: !!entity && !!entityId,
  });
};

export const useNote = (noteId: string) => {
  return useQuery({
    queryKey: ["note", noteId],
    queryFn: () => fetchNote(noteId),
    enabled: !!noteId,
  });
};

export const useCreateNote = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: createNote,
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ["notes"] });
      queryClient.invalidateQueries({ queryKey: ["entity-notes", data.note.entity, data.note.entity_id] });
      toast.success("Note created successfully");
    },
    onError: (error: Error) => {
      toast.error(error.message);
    },
  });
};

export const useUpdateNote = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: ({ noteId, data }: { noteId: string; data: UpdateNoteData }) => 
      updateNote(noteId, data),
    onSuccess: (data, { noteId }) => {
      queryClient.invalidateQueries({ queryKey: ["note", noteId] });
      queryClient.invalidateQueries({ queryKey: ["notes"] });
      queryClient.invalidateQueries({ queryKey: ["entity-notes", data.note.entity, data.note.entity_id] });
      toast.success("Note updated successfully");
    },
    onError: (error: Error) => {
      toast.error(error.message);
    },
  });
};

export const useDeleteNote = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: deleteNote,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["notes"] });
      queryClient.invalidateQueries({ queryKey: ["entity-notes"] });
      toast.success("Note deleted successfully");
    },
    onError: (error: Error) => {
      toast.error(error.message);
    },
  });
};
