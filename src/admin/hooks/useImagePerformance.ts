import { useCallback, useRef } from 'react';

interface ImageLoadMetrics {
  src: string;
  loadTime: number;
  timestamp: number;
  success: boolean;
  fromCache: boolean;
}

export const useImagePerformance = () => {
  const metrics = useRef<ImageLoadMetrics[]>([]);
  const loadStartTimes = useRef<Map<string, number>>(new Map());

  const startImageLoad = useCallback((src: string) => {
    loadStartTimes.current.set(src, performance.now());
  }, []);

  const endImageLoad = useCallback((src: string, success: boolean = true) => {
    const startTime = loadStartTimes.current.get(src);
    if (startTime) {
      const loadTime = performance.now() - startTime;
      const fromCache = loadTime < 10; // Assume cached if loaded very quickly
      
      metrics.current.push({
        src,
        loadTime,
        timestamp: Date.now(),
        success,
        fromCache
      });

      loadStartTimes.current.delete(src);

      // Keep only last 100 metrics to prevent memory leaks
      if (metrics.current.length > 100) {
        metrics.current = metrics.current.slice(-100);
      }
    }
  }, []);

  const getPerformanceStats = useCallback(() => {
    const recentMetrics = metrics.current.filter(
      m => Date.now() - m.timestamp < 60000 // Last minute
    );

    if (recentMetrics.length === 0) {
      return {
        averageLoadTime: 0,
        successRate: 100,
        cacheHitRate: 0,
        totalImages: 0,
        slowImages: []
      };
    }

    const successfulLoads = recentMetrics.filter(m => m.success);
    const averageLoadTime = successfulLoads.reduce((sum, m) => sum + m.loadTime, 0) / successfulLoads.length;
    const successRate = (successfulLoads.length / recentMetrics.length) * 100;
    const cacheHitRate = (recentMetrics.filter(m => m.fromCache).length / recentMetrics.length) * 100;
    const slowImages = recentMetrics
      .filter(m => m.loadTime > 2000) // Images taking more than 2 seconds
      .sort((a, b) => b.loadTime - a.loadTime)
      .slice(0, 5);

    return {
      averageLoadTime: Math.round(averageLoadTime),
      successRate: Math.round(successRate),
      cacheHitRate: Math.round(cacheHitRate),
      totalImages: recentMetrics.length,
      slowImages: slowImages.map(m => ({
        src: m.src,
        loadTime: Math.round(m.loadTime)
      }))
    };
  }, []);

  const logPerformanceStats = useCallback(() => {
    const stats = getPerformanceStats();
    if (stats.totalImages > 0) {
      console.group('🖼️ Image Loading Performance');
      console.log(`📊 Total images loaded: ${stats.totalImages}`);
      console.log(`⚡ Average load time: ${stats.averageLoadTime}ms`);
      console.log(`✅ Success rate: ${stats.successRate}%`);
      console.log(`💾 Cache hit rate: ${stats.cacheHitRate}%`);
      
      if (stats.slowImages.length > 0) {
        console.log('🐌 Slowest images:');
        stats.slowImages.forEach(img => {
          console.log(`  - ${img.src.substring(0, 50)}... (${img.loadTime}ms)`);
        });
      }
      console.groupEnd();
    }
  }, [getPerformanceStats]);

  return {
    startImageLoad,
    endImageLoad,
    getPerformanceStats,
    logPerformanceStats
  };
};
