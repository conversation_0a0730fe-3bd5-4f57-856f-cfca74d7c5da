import { useQuery } from "@tanstack/react-query";

export interface User {
  id: string;
  email: string;
  first_name?: string;
  last_name?: string;
  metadata?: any;
}

/**
 * Hook to fetch user details by user ID
 * Includes caching to avoid repeated API calls
 */
export const useUserLookup = (userId: string | null | undefined) => {
  return useQuery({
    queryKey: ["user", userId],
    queryFn: async (): Promise<User | null> => {
      if (!userId) return null;

      try {
        const response = await fetch(`/admin/users/${userId}`, {
          credentials: "include",
        });

        if (!response.ok) {
          // If user not found or other error, return null
          console.warn(`Failed to fetch user ${userId}:`, response.status);
          return null;
        }

        const data = await response.json();
        return data.user;
      } catch (error) {
        console.warn(`Error fetching user ${userId}:`, error);
        return null;
      }
    },
    enabled: !!userId,
    staleTime: 5 * 60 * 1000, // Cache for 5 minutes
    cacheTime: 10 * 60 * 1000, // Keep in cache for 10 minutes
  });
};

/**
 * Hook to fetch multiple users by their IDs
 * Useful for batch lookups
 */
export const useUsersLookup = (userIds: (string | null | undefined)[]) => {
  const validUserIds = userIds.filter((id): id is string => !!id);

  return useQuery({
    queryKey: ["users", "batch", validUserIds.sort()],
    queryFn: async (): Promise<Record<string, User>> => {
      if (validUserIds.length === 0) return {};

      try {
        // Fetch all users in parallel
        const promises = validUserIds.map(async (userId) => {
          try {
            const response = await fetch(`/admin/users/${userId}`, {
              credentials: "include",
            });

            if (!response.ok) {
              console.warn(`Failed to fetch user ${userId}:`, response.status);
              return null;
            }

            const data = await response.json();
            return { id: userId, user: data.user };
          } catch (error) {
            console.warn(`Error fetching user ${userId}:`, error);
            return null;
          }
        });

        const results = await Promise.all(promises);
        
        // Convert to record format
        const userRecord: Record<string, User> = {};
        results.forEach((result) => {
          if (result && result.user) {
            userRecord[result.id] = result.user;
          }
        });

        return userRecord;
      } catch (error) {
        console.error("Error in batch user lookup:", error);
        return {};
      }
    },
    enabled: validUserIds.length > 0,
    staleTime: 5 * 60 * 1000, // Cache for 5 minutes
    cacheTime: 10 * 60 * 1000, // Keep in cache for 10 minutes
  });
};

/**
 * Utility function to format user display name
 */
export const formatUserDisplay = (user: User | null): string => {
  if (!user) return "Unknown User";
  
  if (user.email) {
    return user.email;
  }
  
  if (user.first_name || user.last_name) {
    return [user.first_name, user.last_name].filter(Boolean).join(" ");
  }
  
  return user.id;
};

/**
 * Utility function to get user display from user ID with fallback
 */
export const getUserDisplayFromId = (userId: string | null | undefined): string => {
  if (!userId) return "System";
  
  // If it looks like an email, return it directly
  if (userId.includes("@")) {
    return userId;
  }
  
  // If it's a user ID, format it nicely as fallback
  if (userId.startsWith("user_")) {
    return `User ${userId.slice(5, 13)}...`;
  }
  
  return userId;
};
