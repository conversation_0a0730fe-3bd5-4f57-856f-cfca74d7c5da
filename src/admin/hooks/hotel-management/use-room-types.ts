import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { reactQueryApiClient } from "../../lib/api-client";
import { cacheSettings } from "../../lib/react-query-config";
import { toast } from "@camped-ai/ui";

// Types
export interface RoomType {
  id: string;
  name: string;
  handle: string;
  description?: string;
  is_active: boolean;
  is_internal: boolean;
  created_at: string;
  updated_at: string;
}

export interface RoomTypesResponse {
  roomTypes: RoomType[];
  count?: number;
  limit?: number;
  offset?: number;
}

export interface RoomTypeFilters {
  limit?: number;
  offset?: number;
  search?: string;
  is_active?: boolean;
}

// Query Keys
export const roomTypeKeys = {
  all: ["room-types"] as const,
  lists: () => [...roomTypeKeys.all, "list"] as const,
  list: (filters: RoomTypeFilters) =>
    [...roomTypeKeys.lists(), filters] as const,
  detail: (id: string) => [...roomTypeKeys.all, "detail", id] as const,
};

// Hook to fetch room types
export const useRoomTypes = (filters: RoomTypeFilters = {}) => {
  return useQuery({
    queryKey: roomTypeKeys.list(filters),
    queryFn: async (): Promise<RoomTypesResponse> => {
      const params = new URLSearchParams();

      if (filters.limit) params.append("limit", filters.limit.toString());
      if (filters.offset) params.append("offset", filters.offset.toString());
      if (filters.search) params.append("search", filters.search);
      if (filters.is_active !== undefined) {
        params.append("is_active", filters.is_active.toString());
      }

      const url = `/admin/room-types${
        params.toString() ? `?${params.toString()}` : ""
      }`;

      return reactQueryApiClient.get<RoomTypesResponse>(url);
    },
    staleTime: cacheSettings.configurations.staleTime, // 15 minutes
    gcTime: cacheSettings.configurations.gcTime, // 1 hour
    refetchOnWindowFocus: false,
    refetchOnReconnect: true,
    retry: 2,
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
  });
};

// Hook specifically for dropdowns - gets all room types
export const useRoomTypesForDropdown = () => {
  return useQuery({
    queryKey: roomTypeKeys.list({ limit: 100 }),
    queryFn: async (): Promise<RoomTypesResponse> => {
      return reactQueryApiClient.get<RoomTypesResponse>(
        "/admin/room-types?limit=100"
      );
    },
    staleTime: cacheSettings.configurations.staleTime, // 15 minutes
    gcTime: cacheSettings.configurations.gcTime, // 1 hour
    refetchOnWindowFocus: false,
    refetchOnReconnect: true,
    retry: 2,
    select: (data) => ({
      ...data,
      roomTypes: data.roomTypes || [],
    }),
  });
};

// Hook to fetch a single room type
export const useRoomType = (id: string) => {
  return useQuery({
    queryKey: roomTypeKeys.detail(id),
    queryFn: async (): Promise<{ roomType: RoomType }> => {
      return reactQueryApiClient.get<{ roomType: RoomType }>(
        `/admin/room-types/${id}`
      );
    },
    enabled: !!id,
    staleTime: cacheSettings.configurations.staleTime, // 15 minutes
    gcTime: cacheSettings.configurations.gcTime, // 1 hour
    refetchOnWindowFocus: false,
    retry: 2,
  });
};

// Mutation to create a room type
export const useCreateRoomType = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (
      data: Omit<RoomType, "id" | "created_at" | "updated_at">
    ) => {
      return reactQueryApiClient.post("/admin/room-types", data);
    },
    onSuccess: () => {
      // Invalidate and refetch room type lists
      queryClient.invalidateQueries({
        queryKey: roomTypeKeys.lists(),
      });

      toast.success("Success", {
        description: "Room type created successfully",
      });
    },
    onError: (error: any) => {
      console.error("Error creating room type:", error);
      toast.error("Error", {
        description: "Failed to create room type",
      });
    },
  });
};

// Mutation to update a room type
export const useUpdateRoomType = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({
      id,
      data,
    }: {
      id: string;
      data: Partial<Omit<RoomType, "id" | "created_at" | "updated_at">>;
    }) => {
      return reactQueryApiClient.put(`/admin/room-types/${id}`, data);
    },
    onSuccess: (_, { id }) => {
      // Invalidate specific room type and room type lists
      queryClient.invalidateQueries({
        queryKey: roomTypeKeys.detail(id),
      });
      queryClient.invalidateQueries({
        queryKey: roomTypeKeys.lists(),
      });

      toast.success("Success", {
        description: "Room type updated successfully",
      });
    },
    onError: (error: any) => {
      console.error("Error updating room type:", error);
      toast.error("Error", {
        description: "Failed to update room type",
      });
    },
  });
};

// Mutation to delete a room type
export const useDeleteRoomType = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (id: string) => {
      return reactQueryApiClient.delete(`/admin/room-types/${id}`);
    },
    onSuccess: () => {
      // Invalidate room type lists
      queryClient.invalidateQueries({
        queryKey: roomTypeKeys.lists(),
      });

      toast.success("Success", {
        description: "Room type deleted successfully",
      });
    },
    onError: (error: any) => {
      console.error("Error deleting room type:", error);
      toast.error("Error", {
        description: "Failed to delete room type",
      });
    },
  });
};

// Utility function to invalidate all room type queries
export const invalidateRoomTypeQueries = (
  queryClient: ReturnType<typeof useQueryClient>
) => {
  queryClient.invalidateQueries({
    queryKey: roomTypeKeys.all,
  });
};
