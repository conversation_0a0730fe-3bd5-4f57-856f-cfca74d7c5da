import { useState } from "react";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "@camped-ai/ui";

export type ImportValidationError = {
  row: number;
  field: string;
  message: string;
  value?: any;
};

export type ImportResult = {
  success: boolean;
  message: string;
  imported: number;
  errors: ImportValidationError[];
  total_processed?: number;
};

export type ImportProgress = {
  processed: number;
  total: number;
  percentage: number;
};

export type ParseResult = {
  success: boolean;
  message: string;
  pricing_data: any[];
  errors: ImportValidationError[];
  total_rows: number;
  valid_rows: number;
  error_count: number;
};

// Helper function to validate date format
const isValidDate = (dateString: string): boolean => {
  const date = new Date(dateString);
  return !isNaN(date.getTime()) && !!dateString.match(/^\d{4}-\d{2}-\d{2}$/);
};

/**
 * Custom hook for hotel pricing import functionality
 */
export const useHotelPricingImport = (hotelId: string) => {
  const [importProgress, setImportProgress] = useState<ImportProgress>({
    processed: 0,
    total: 0,
    percentage: 0,
  });

  const queryClient = useQueryClient();

  // File parsing function
  const parseImportFile = async (file: File): Promise<ParseResult> => {
    try {
      // Validate file type
      const validTypes = [
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'application/vnd.ms-excel',
        'text/csv',
        'application/csv'
      ];

      if (!validTypes.includes(file.type) && !file.name.endsWith('.xlsx') && !file.name.endsWith('.csv')) {
        throw new Error('Please upload an Excel (.xlsx) or CSV (.csv) file');
      }

      // Validate file size (10MB limit)
      if (file.size > 10 * 1024 * 1024) {
        throw new Error('File size must be less than 10MB');
      }

      const formData = new FormData();
      formData.append('file', file);

      const response = await fetch(`/admin/hotel-management/hotels/${hotelId}/pricing/parse`, {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.message || `Failed to parse file: ${response.statusText}`);
      }

      const result = await response.json();

      if (!result.success) {
        throw new Error(result.message || 'Failed to parse file');
      }

      return result;
    } catch (error) {
      return {
        success: false,
        message: error instanceof Error ? error.message : 'Failed to parse file',
        pricing_data: [],
        errors: [{
          row: 0,
          field: 'file',
          message: error instanceof Error ? error.message : 'Failed to parse file',
          value: file.name
        }],
        total_rows: 0,
        valid_rows: 0,
        error_count: 1
      };
    }
  };

  // Import mutation
  const importMutation = useMutation({
    mutationFn: async ({ pricingData, currency }: { pricingData: any[]; currency: string }): Promise<ImportResult> => {
      try {
        setImportProgress({
          processed: 0,
          total: pricingData.length,
          percentage: 0,
        });

        const response = await fetch(`/admin/hotel-management/hotels/${hotelId}/pricing/import`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            pricing_data: pricingData,
            currency: currency,
          }),
        });

        if (!response.ok) {
          const errorData = await response.json().catch(() => ({}));
          throw new Error(errorData.message || `Import failed: ${response.statusText}`);
        }

        const result = await response.json();

        // Update progress to completion
        setImportProgress({
          processed: result.imported || 0,
          total: pricingData.length,
          percentage: 100,
        });

        return result;
      } catch (error) {
        throw new Error(error instanceof Error ? error.message : 'Import failed');
      }
    },
    onSuccess: (result: ImportResult) => {
      // Invalidate all pricing-related queries to refresh data
      queryClient.invalidateQueries({
        queryKey: ['hotel-pricing', hotelId]
      });
      queryClient.invalidateQueries({
        queryKey: ['hotel-comprehensive-pricing', hotelId]
      });

      if (result.success) {
        toast.success(result.message || "Import completed successfully");
      } else if (result.imported > 0) {
        toast.success(`Imported ${result.imported} records with ${result.errors.length} errors`);
      }
    },
    onError: (error: Error) => {
      console.error("Import mutation error:", error);
      toast.error(error.message || "Import failed");
    },
  });

  // Template download function - supports both single hotel and bulk templates
  const downloadTemplate = async (options: {
    currency?: string;
    fromDate?: string;
    toDate?: string;
    hotelIds?: string[];
  } = {}) => {
    const { currency = 'CHF', fromDate, toDate, hotelIds } = options;

    try {
      let response;
      let filename;

      // Use bulk template API if date range and hotels are specified
      if (fromDate && toDate && hotelIds && hotelIds.length > 0) {
        const targetHotelIds = hotelIds.includes(hotelId)
          ? hotelIds.join(',')
          : [hotelId, ...hotelIds].join(',');

        response = await fetch(
          `/admin/hotel-management/pricing/bulk-template?currency=${currency}&from_date=${fromDate}&to_date=${toDate}&hotel_ids=${targetHotelIds}`
        );
        filename = `hotel_pricing_bulk_template_${currency}_${fromDate}_to_${toDate}.xlsx`;
      } else {
        // Use single hotel template API
        response = await fetch(`/admin/hotel-management/hotels/${hotelId}/pricing/template?currency=${currency}`);
        filename = `hotel_pricing_import_template_${currency}.xlsx`;
      }

      if (!response.ok) {
        throw new Error('Failed to download template');
      }

      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = filename;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);

      toast.success("Template downloaded successfully");
    } catch (error) {
      console.error("Template download error:", error);
      toast.error("Failed to download template");
    }
  };

  // Validation function for parsed data - supports both old and new template formats
  const validatePricingData = (data: any[]): ImportValidationError[] => {
    const errors: ImportValidationError[] = [];

    data.forEach((row, index) => {
      // Check if this is the new template format (has date_from, date_to, hotel_name)
      const isNewFormat = row.hasOwnProperty('date_from') || row.hasOwnProperty('hotel_name');

      if (isNewFormat) {
        // New template format validation
        if (!row.date_from || row.date_from.toString().trim() === '') {
          errors.push({
            row: index + 1,
            field: 'date_from',
            message: 'Date From is required',
            value: row.date_from
          });
        }

        if (!row.date_to || row.date_to.toString().trim() === '') {
          errors.push({
            row: index + 1,
            field: 'date_to',
            message: 'Date To is required',
            value: row.date_to
          });
        }

        if (!row.hotel_name || row.hotel_name.toString().trim() === '') {
          errors.push({
            row: index + 1,
            field: 'hotel_name',
            message: 'Hotel Name is required',
            value: row.hotel_name
          });
        }

        // Validate date format
        if (row.date_from && !isValidDate(row.date_from)) {
          errors.push({
            row: index + 1,
            field: 'date_from',
            message: 'Date From must be in YYYY-MM-DD format',
            value: row.date_from
          });
        }

        if (row.date_to && !isValidDate(row.date_to)) {
          errors.push({
            row: index + 1,
            field: 'date_to',
            message: 'Date To must be in YYYY-MM-DD format',
            value: row.date_to
          });
        }

        // Validate cost/margin fields (new format)
        const cost = row.cost;
        const fixedMargin = row.fixed_margin;
        const marginPercentage = row.margin_percentage;

        // At least one pricing field should be provided
        if (!cost && !fixedMargin && !marginPercentage) {
          errors.push({
            row: index + 1,
            field: 'pricing',
            message: 'At least one of Cost, Fixed Margin, or Margin Percentage must be provided',
            value: null
          });
        }

        // Validate cost (should be in pence/cents - no decimals)
        if (cost !== undefined && cost !== null && cost !== '') {
          const numericCost = parseFloat(cost.toString());
          if (isNaN(numericCost) || numericCost < 0) {
            errors.push({
              row: index + 1,
              field: 'cost',
              message: 'Cost must be a valid positive number (in pence/cents)',
              value: cost
            });
          }
        }

        // Validate fixed margin
        if (fixedMargin !== undefined && fixedMargin !== null && fixedMargin !== '') {
          const numericMargin = parseFloat(fixedMargin.toString());
          if (isNaN(numericMargin)) {
            errors.push({
              row: index + 1,
              field: 'fixed_margin',
              message: 'Fixed Margin must be a valid number',
              value: fixedMargin
            });
          }
        }

        // Validate margin percentage
        if (marginPercentage !== undefined && marginPercentage !== null && marginPercentage !== '') {
          const numericPercentage = parseFloat(marginPercentage.toString());
          if (isNaN(numericPercentage) || numericPercentage < 0 || numericPercentage > 100) {
            errors.push({
              row: index + 1,
              field: 'margin_percentage',
              message: 'Margin Percentage must be a valid number between 0 and 100',
              value: marginPercentage
            });
          }
        }

        // Check that both fixed margin and margin percentage are not provided
        if (fixedMargin && marginPercentage &&
            fixedMargin.toString().trim() !== '' && marginPercentage.toString().trim() !== '') {
          errors.push({
            row: index + 1,
            field: 'margin_conflict',
            message: 'Either Fixed Margin OR Margin Percentage should be provided, not both',
            value: null
          });
        }
      } else {
        // Legacy template format validation
        if (!row.currency_code || row.currency_code.toString().trim() === '') {
          errors.push({
            row: index + 1,
            field: 'currency_code',
            message: 'Currency Code is required',
            value: row.currency_code
          });
        }
      }

      // Common validations for both formats
      if (!row.room_config_name || row.room_config_name.toString().trim() === '') {
        errors.push({
          row: index + 1,
          field: 'room_config_name',
          message: 'Room Config Name is required',
          value: row.room_config_name
        });
      }

      if (!row.occupancy_name || row.occupancy_name.toString().trim() === '') {
        errors.push({
          row: index + 1,
          field: 'occupancy_name',
          message: 'Occupancy Name is required',
          value: row.occupancy_name
        });
      }

      if (!row.meal_plan_name || row.meal_plan_name.toString().trim() === '') {
        errors.push({
          row: index + 1,
          field: 'meal_plan_name',
          message: 'Meal Plan Name is required',
          value: row.meal_plan_name
        });
      }

      // Legacy format: Validate weekday prices and cost/margin data
      if (!isNewFormat) {
        const weekdays = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'];
        weekdays.forEach(day => {
        // Validate weekday prices
        const priceField = `${day}_price`;
        const price = row[priceField];

        if (price !== undefined && price !== null && price !== '') {
          const numericPrice = parseFloat(price.toString());
          if (isNaN(numericPrice) || numericPrice < 0) {
            errors.push({
              row: index + 1,
              field: priceField,
              message: `${day.charAt(0).toUpperCase() + day.slice(1)} price must be a valid positive number`,
              value: price
            });
          }
        }

        // Validate weekday cost fields
        const costField = `${day}_gross_cost`;
        const cost = row[costField];
        if (cost !== undefined && cost !== null && cost !== '') {
          const numericCost = parseFloat(cost.toString());
          if (isNaN(numericCost) || numericCost < 0) {
            errors.push({
              row: index + 1,
              field: costField,
              message: `${day.charAt(0).toUpperCase() + day.slice(1)} gross cost must be a valid positive number`,
              value: cost
            });
          }
        }

        // Validate weekday fixed margin fields
        const fixedMarginField = `${day}_fixed_margin`;
        const fixedMargin = row[fixedMarginField];
        if (fixedMargin !== undefined && fixedMargin !== null && fixedMargin !== '') {
          const numericMargin = parseFloat(fixedMargin.toString());
          if (isNaN(numericMargin) || numericMargin < 0) {
            errors.push({
              row: index + 1,
              field: fixedMarginField,
              message: `${day.charAt(0).toUpperCase() + day.slice(1)} fixed margin must be a valid positive number`,
              value: fixedMargin
            });
          }
        }

        // Validate weekday margin percentage fields
        const marginPercentageField = `${day}_margin_percentage`;
        const marginPercentage = row[marginPercentageField];
        if (marginPercentage !== undefined && marginPercentage !== null && marginPercentage !== '') {
          const numericPercentage = parseFloat(marginPercentage.toString());
          if (isNaN(numericPercentage) || numericPercentage < 0 || numericPercentage > 100) {
            errors.push({
              row: index + 1,
              field: marginPercentageField,
              message: `${day.charAt(0).toUpperCase() + day.slice(1)} margin percentage must be between 0 and 100`,
              value: marginPercentage
            });
          }
        }
        });

        // Validate default cost/margin fields (legacy format only)
        const defaultFields = [
          { field: 'default_gross_cost', name: 'Default Gross Cost', min: 0 },
          { field: 'default_fixed_margin', name: 'Default Fixed Margin', min: 0 },
          { field: 'default_margin_percentage', name: 'Default Margin Percentage', min: 0, max: 100 }
        ];

        defaultFields.forEach(({ field, name, min, max }) => {
          const value = row[field];
          if (value !== undefined && value !== null && value !== '') {
            const numericValue = parseFloat(value.toString());
            if (isNaN(numericValue) || numericValue < min || (max !== undefined && numericValue > max)) {
              const rangeText = max !== undefined ? `between ${min} and ${max}` : `at least ${min}`;
              errors.push({
                row: index + 1,
                field: field,
                message: `${name} must be a valid number ${rangeText}`,
                value: value
              });
            }
          }
        });
      } // End of legacy format validation
    });

    return errors;
  };

  return {
    // Functions
    parseImportFile,
    importPricingData: importMutation.mutateAsync,
    downloadTemplate,
    validatePricingData,
    
    // State
    isImporting: importMutation.isPending,
    importProgress,
    
    // Results
    importError: importMutation.error,
    importResult: importMutation.data,
  };
};
