import { useQuery } from "@tanstack/react-query";
import { sdk } from "../../lib/sdk";

// Customer type based on API response
export interface Customer {
  id: string;
  company_name: string | null;
  first_name: string | null;
  last_name: string | null;
  email: string;
  phone: string | null;
  metadata: any;
  has_account: boolean;
  created_by: string | null;
  created_at: string;
  updated_at: string;
  deleted_at: string | null;
}

export interface CustomersListResponse {
  customers: Customer[];
  count: number;
  limit: number;
  offset: number;
}

export interface CustomersListFilters {
  limit?: number;
  offset?: number;
  search?: string;
  has_account?: boolean;
  sort_by?: "name" | "email" | "created_at" | "updated_at";
  sort_order?: "asc" | "desc";
}

// Fetch function
const fetchCustomersList = async (filters: CustomersListFilters = {}): Promise<CustomersListResponse> => {
  const params = new URLSearchParams();

  // Add pagination parameters
  if (filters.limit !== undefined) {
    params.append("limit", filters.limit.toString());
  }
  if (filters.offset !== undefined) {
    params.append("offset", filters.offset.toString());
  }

  // Add search parameter
  if (filters.search && filters.search.trim()) {
    params.append("search", filters.search.trim());
  }

  // Add account status filter
  if (filters.has_account !== undefined) {
    params.append("has_account", filters.has_account.toString());
  }

  // Add sorting parameters
  if (filters.sort_by) {
    params.append("sort_by", filters.sort_by);
  }
  if (filters.sort_order) {
    params.append("sort_order", filters.sort_order);
  }

  const url = `/admin/all-customers${params.toString() ? `?${params.toString()}` : ""}`;

  try {
    const result = (await sdk.client.fetch(url)) as any;
    return result;
  } catch (error) {
    throw error;
  }
};

// Query Keys
export const customersListKeys = {
  all: ["customers-list"] as const,
  lists: () => [...customersListKeys.all, "list"] as const,
  list: (filters: CustomersListFilters) =>
    [...customersListKeys.lists(), filters] as const,
};

// Hooks
export const useCustomersList = (filters: CustomersListFilters = {}) => {
  return useQuery({
    queryKey: customersListKeys.list(filters),
    queryFn: () => fetchCustomersList(filters),
    staleTime: 1000 * 60 * 2, // 2 minutes cache duration
    refetchOnWindowFocus: false, // Don't refetch when user returns to the tab
    refetchOnMount: false, // Don't always refetch when component mounts
    refetchOnReconnect: true, // Refetch when network reconnects
  });
};

// Hook for getting customers count
export const useCustomersCount = () => {
  return useQuery({
    queryKey: ["customers-count"],
    queryFn: async (): Promise<{ count: number }> => {
      try {
        const result = (await sdk.client.fetch("/admin/all-customers?limit=1&offset=0")) as any;
        return { count: result.count || 0 };
      } catch (error) {
        throw error;
      }
    },
    staleTime: 1000 * 60 * 5, // 5 minutes cache duration
  });
};

// Hook for getting a single customer
export const useCustomer = (id: string) => {
  return useQuery({
    queryKey: ["customer", id],
    queryFn: async (): Promise<{ customer: Customer }> => {
      try {
        const result = (await sdk.client.fetch(`/admin/all-customers/${id}`)) as any;
        return result;
      } catch (error) {
        throw error;
      }
    },
    enabled: !!id,
    staleTime: 1000 * 60 * 5, // 5 minutes cache duration
  });
};
