import { useQuery, useQueryClient } from "@tanstack/react-query";
import { sdk } from "../../lib/sdk";

// Types for customer details
export interface CustomerTraveller {
  id: string;
  customer_id: string;
  first_name: string;
  last_name: string;
  gender: "male" | "female" | "other";
  date_of_birth: string;
  relationship?: string;
  created_at: string;
  updated_at: string;
}

export interface CustomerBooking {
  id: string;
  customer_id: string;
  hotel_name: string;
  check_in_date: string;
  check_out_date: string;
  total_amount: number;
  currency: string;
  status: string;
  created_at: string;
  updated_at: string;
}

export interface CustomerDetail {
  id: string;
  company_name: string | null;
  first_name: string | null;
  last_name: string | null;
  email: string;
  phone: string | null;
  metadata: any;
  has_account: boolean;
  created_by: string | null;
  created_at: string;
  updated_at: string;
  deleted_at: string | null;
}

export interface CustomerDetailsResponse {
  customer: CustomerDetail;
}

export interface CustomerTravellersResponse {
  travellers: CustomerTraveller[];
  count: number;
}

export interface CustomerBookingsResponse {
  bookings: CustomerBooking[];
  count: number;
}

// Query Keys
export const customerDetailsKeys = {
  all: ["customer-details"] as const,
  details: (id: string) => [...customerDetailsKeys.all, "detail", id] as const,
  travellers: (id: string) => [...customerDetailsKeys.all, "travellers", id] as const,
  bookings: (id: string) => [...customerDetailsKeys.all, "bookings", id] as const,
};

// Fetch functions
const fetchCustomerDetail = async (id: string): Promise<CustomerDetailsResponse> => {
  try {
    const result = await sdk.client.fetch(`/admin/all-customers/${id}`);
    return result as CustomerDetailsResponse;
  } catch (error) {
    throw error;
  }
};

const fetchCustomerTravellers = async (id: string): Promise<CustomerTravellersResponse> => {
  try {
    const result = await sdk.client.fetch(`/admin/all-customers/${id}/travellers`);
    return result as CustomerTravellersResponse;
  } catch (error) {
    throw error;
  }
};

const fetchCustomerBookings = async (id: string): Promise<CustomerBookingsResponse> => {
  try {
    const result = await sdk.client.fetch(`/admin/all-customers/${id}/bookings`);
    return result as CustomerBookingsResponse;
  } catch (error) {
    // Return empty array if bookings endpoint doesn't exist yet
    return { bookings: [], count: 0 };
  }
};

// Hooks
export const useCustomerDetail = (id: string) => {
  return useQuery({
    queryKey: customerDetailsKeys.details(id),
    queryFn: () => fetchCustomerDetail(id),
    enabled: !!id,
    staleTime: 1000 * 60 * 5, // 5 minutes
    refetchOnWindowFocus: false,
  });
};

export const useCustomerTravellers = (id: string) => {
  return useQuery({
    queryKey: customerDetailsKeys.travellers(id),
    queryFn: () => fetchCustomerTravellers(id),
    enabled: !!id,
    staleTime: 1000 * 60 * 2, // 2 minutes
    refetchOnWindowFocus: false,
  });
};

export const useCustomerBookings = (id: string) => {
  return useQuery({
    queryKey: customerDetailsKeys.bookings(id),
    queryFn: () => fetchCustomerBookings(id),
    enabled: !!id,
    staleTime: 1000 * 60 * 5, // 5 minutes
    refetchOnWindowFocus: false,
  });
};

// Combined hook for all customer data
export const useCustomerDetails = (id: string) => {
  const customerQuery = useCustomerDetail(id);
  const travellersQuery = useCustomerTravellers(id);
  const bookingsQuery = useCustomerBookings(id);

  return {
    // Customer data
    customer: customerQuery.data?.customer,
    customerLoading: customerQuery.isLoading,
    customerError: customerQuery.error,

    // Travellers data
    travellers: travellersQuery.data?.travellers || [],
    travellersLoading: travellersQuery.isLoading,
    travellersError: travellersQuery.error,

    // Bookings data
    bookings: bookingsQuery.data?.bookings || [],
    bookingsLoading: bookingsQuery.isLoading,
    bookingsError: bookingsQuery.error,

    // Combined states
    isLoading: customerQuery.isLoading || travellersQuery.isLoading || bookingsQuery.isLoading,
    isError: customerQuery.isError || travellersQuery.isError || bookingsQuery.isError,
    error: customerQuery.error || travellersQuery.error || bookingsQuery.error,

    // Refetch functions
    refetchCustomer: customerQuery.refetch,
    refetchTravellers: travellersQuery.refetch,
    refetchBookings: bookingsQuery.refetch,
    refetchAll: () => {
      customerQuery.refetch();
      travellersQuery.refetch();
      bookingsQuery.refetch();
    },
  };
};

// Mutation helpers for invalidating cache
export const useInvalidateCustomerDetails = () => {
  const queryClient = useQueryClient();

  return {
    invalidateCustomer: (id: string) => {
      queryClient.invalidateQueries({ queryKey: customerDetailsKeys.details(id) });
    },
    invalidateTravellers: (id: string) => {
      queryClient.invalidateQueries({ queryKey: customerDetailsKeys.travellers(id) });
    },
    invalidateBookings: (id: string) => {
      queryClient.invalidateQueries({ queryKey: customerDetailsKeys.bookings(id) });
    },
    invalidateAll: (id: string) => {
      queryClient.invalidateQueries({ queryKey: customerDetailsKeys.all });
    },
  };
};
