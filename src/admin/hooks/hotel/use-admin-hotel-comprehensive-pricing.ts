import { useQuery } from "@tanstack/react-query";
import { sdk } from "../../lib/sdk";
import { useMemo } from "react";
import { cacheSettings } from "../../lib/react-query-config";

type OccupancyConfig = {
  id: string;
  name: string;
  type: string;
  min_age: number | null;
  max_age: number | null;
  min_occupancy: number;
  max_occupancy: number;
  is_default: boolean;
  created_at: string;
  updated_at: string;
};

type MealPlan = {
  id: string;
  name: string;
  type: string;
  is_default: boolean;
  applicable_occupancy_types?: string[];
  metadata?: Record<string, any>;
  created_at: string;
  updated_at: string;
};

type RoomConfig = {
  id: string;
  title: string;
  handle: string;
  description: string;
  metadata: Record<string, any>;
};

type WeekdayPrices = {
  mon: number;
  tue: number;
  wed: number;
  thu: number;
  fri: number;
  sat: number;
  sun: number;
};

type WeekdayRule = {
  id: string;
  occupancy_type_id: string;
  meal_plan_id: string | null;
  amount: number;
  room_config_id: string;
  weekday_prices: WeekdayPrices;
  default_values?: {
    gross_cost: number | null;
    fixed_margin: number | null;
    margin_percentage: number | null;
    total: number | null;
  };
  currency_code: string;
  occupancy_type?: OccupancyConfig;
  meal_plan?: MealPlan;
  created_at: string;
  updated_at: string;
};

type SeasonalPrice = {
  id: string;
  name: string;
  start_date: string;
  end_date: string;
  currency_code: string;
  weekday_rules: Array<{
    id: string;
    occupancy_type_id: string;
    meal_plan_id: string | null;
    weekday_prices: WeekdayPrices;
    currency_code: string;
    priority: number;
  }>;
};

type RoomPricingData = {
  room_config_id: string;
  room_config: RoomConfig;
  weekday_rules: WeekdayRule[];
  seasonal_prices: SeasonalPrice[];
  error?: string;
};

type ComprehensivePricingResponse = {
  hotel: {
    id: string;
    name: string;
    handle: string;
  };
  room_configs: RoomConfig[];
  occupancy_configs: OccupancyConfig[];
  meal_plans: MealPlan[];
  room_pricing_data: RoomPricingData[];
};

export const useAdminHotelComprehensivePricing = (hotelId: string, currency?: string) => {
  const { data, isLoading, isError, refetch } = useQuery({
    queryKey: ["hotel-comprehensive-pricing", hotelId, currency],
    queryFn: async (): Promise<ComprehensivePricingResponse> => {
      const url = currency
        ? `/admin/hotel-management/hotels/${hotelId}/pricing?currency=${currency}`
        : `/admin/hotel-management/hotels/${hotelId}/pricing`;
      const response = await sdk.client.fetch(url);
      return response as ComprehensivePricingResponse;
    },
    enabled: !!hotelId,
    retry: 2,
    // Use optimized cache settings for hotel pricing
    ...cacheSettings.hotelPricing,
    // Don't refetch on window focus or reconnect for pricing data
    refetchOnWindowFocus: false,
    refetchOnReconnect: false,
  });

  // Memoize processed data to prevent unnecessary re-renders
  const processedData = useMemo(() => {
    if (!data) return null;

    console.log("[useAdminHotelComprehensivePricing] Raw API data:", data);
    console.log(
      "[useAdminHotelComprehensivePricing] Room configs:",
      data.room_configs?.length || 0
    );
    console.log(
      "[useAdminHotelComprehensivePricing] Room pricing data:",
      data.room_pricing_data?.length || 0
    );

    // Transform room pricing data into the format expected by the frontend
    const pricingDataRecord: Record<string, any> = {};
    const allSeasonalPeriods: Array<{
      id: string;
      name: string;
      start_date: string;
      end_date: string;
      currency_code: string;
    }> = [];

    console.log("=== RAW API DATA ===", data);

    data.room_pricing_data.forEach((roomData) => {
      // Group weekday rules by currency
      const currencyGroups: Record<string, any> = {};

      roomData.weekday_rules.forEach((rule) => {
        const currency = rule.currency_code || "USD";
        if (!currencyGroups[currency]) {
          currencyGroups[currency] = {
            currency_code: currency,
            weekday_rules: [],
            seasonal_prices: [],
          };
        }
        currencyGroups[currency].weekday_rules.push(rule);
      });

      // Add seasonal prices to currency groups
      roomData.seasonal_prices.forEach((seasonalPrice) => {
        const currency = seasonalPrice.currency_code || "USD";
        if (!currencyGroups[currency]) {
          currencyGroups[currency] = {
            currency_code: currency,
            weekday_rules: [],
            seasonal_prices: [],
          };
        }
        currencyGroups[currency].seasonal_prices.push(seasonalPrice);

        // Add to global seasonal periods list (avoid duplicates)
        const existingPeriod = allSeasonalPeriods.find(
          (p) => p.id === seasonalPrice.id
        );
        if (!existingPeriod) {
          allSeasonalPeriods.push({
            id: seasonalPrice.id,
            name: seasonalPrice.name,
            start_date: seasonalPrice.start_date,
            end_date: seasonalPrice.end_date,
            currency_code: seasonalPrice.currency_code,
          });
        }
      });

      pricingDataRecord[roomData.room_config_id] = {
        data: currencyGroups,
      };
    });

    return {
      hotel: data.hotel,
      roomConfigs: data.room_configs,
      occupancyConfigs: data.occupancy_configs,
      mealPlans: data.meal_plans,
      pricingData: pricingDataRecord,
      room_pricing_data: data.room_pricing_data,
      seasonalPeriods: allSeasonalPeriods,
    };
  }, [data]);

  return {
    data: processedData,
    isLoading,
    isError,
    refetch,
    // Expose raw data if needed
    rawData: data,
  };
};
