import { useQuery } from "@tanstack/react-query";
import { format } from "date-fns";

export interface RoomAvailabilityData {
  room_id: string;
  room_config_id: string;
  date: string;
  status: string;
  available_quantity: number;
  is_available: boolean;
}

export interface PricingCalendarAvailabilityResponse {
  availability: RoomAvailabilityData[];
  room_configs: Array<{
    id: string;
    title: string;
    variants: Array<{
      id: string;
      title: string;
    }>;
  }>;
}

export interface UsePricingCalendarAvailabilityParams {
  hotelId: string;
  startDate: Date;
  endDate: Date;
  roomConfigId?: string;
  enabled?: boolean;
}

/**
 * Hook to fetch room availability data for the pricing calendar
 * This integrates with the existing availability system to show pricing only for available dates
 */
export const usePricingCalendarAvailability = ({
  hotelId,
  startDate,
  endDate,
  roomConfigId,
  enabled = true,
}: UsePricingCalendarAvailabilityParams) => {
  const startDateStr = format(startDate, "yyyy-MM-dd");
  const endDateStr = format(endDate, "yyyy-MM-dd");

  return useQuery({
    queryKey: [
      "pricing-calendar-availability",
      hotelId,
      startDateStr,
      endDateStr,
      roomConfigId,
    ],
    queryFn: async (): Promise<PricingCalendarAvailabilityResponse> => {
      const params = new URLSearchParams({
        hotel_id: hotelId,
        start_date: startDateStr,
        end_date: endDateStr,
        consolidate: "true", // Use consolidated format like your working API test
      });

      if (roomConfigId) {
        params.append("room_config_id", roomConfigId);
      }

      const response = await fetch(
        `/admin/hotel-management/availability?${params.toString()}`
      );

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || "Failed to fetch availability data");
      }

      const data = await response.json();

      console.log("[Pricing Calendar Availability] Raw API response:", data);

      // Transform the data to match our expected format
      const availability: RoomAvailabilityData[] = [];

      // Create a mapping from room_id to room_config_id using the rooms data
      const roomToConfigMap = new Map<string, string>();
      if (data.rooms && Array.isArray(data.rooms)) {
        data.rooms.forEach((room: any) => {
          roomToConfigMap.set(room.id, room.room_config_id);
        });
      }

      if (data.availability && Array.isArray(data.availability)) {
        data.availability.forEach((entry: any) => {
          const roomId = entry.room_id || entry.inventory_item_id;
          const roomConfigId = entry.room_config_id || roomToConfigMap.get(roomId) || "";

          // Handle both consolidated and non-consolidated response formats
          if (entry.from_date && entry.to_date) {
            // Consolidated format (when consolidate=true) - has from_date and to_date
            // Need to expand to daily records
            const fromDate = new Date(entry.from_date);
            const toDate = new Date(entry.to_date);

            // Generate daily records from from_date to to_date (exclusive for to_date)
            let currentDate = new Date(fromDate);
            while (currentDate < toDate) {
              availability.push({
                room_id: roomId,
                room_config_id: roomConfigId,
                date: format(currentDate, "yyyy-MM-dd"),
                status: entry.status,
                available_quantity: entry.quantity || entry.available_quantity || 1,
                is_available: entry.status === "available",
              });

              // Move to next day
              currentDate.setDate(currentDate.getDate() + 1);
            }
          } else if (entry.date) {
            // Daily format - already has individual date
            availability.push({
              room_id: roomId,
              room_config_id: roomConfigId,
              date: entry.date,
              status: entry.status,
              available_quantity: entry.quantity || entry.available_quantity || 1,
              is_available: entry.status === "available",
            });
          } else {
            // Fallback: treat as single day if we have the necessary data
            console.warn("[Pricing Calendar Availability] Unknown entry format:", entry);
          }
        });
      }

      console.log("[Pricing Calendar Availability] Transformed availability data:", availability.slice(0, 5));

      return {
        availability,
        room_configs: data.room_configs || [],
      };
    },
    enabled: enabled && !!hotelId,
    staleTime: 2 * 60 * 1000, // 2 minutes - availability data should be relatively fresh
    gcTime: 5 * 60 * 1000, // 5 minutes
    refetchOnWindowFocus: true, // Refetch when user returns to tab for fresh availability
  });
};

/**
 * Helper function to check if a room config is available on a specific date
 */
export const isRoomConfigAvailableOnDate = (
  roomConfigId: string,
  date: Date,
  availabilityData: RoomAvailabilityData[]
): boolean => {
  const dateStr = format(date, "yyyy-MM-dd");

  // Find availability entries for this room config on this date
  const roomConfigAvailability = availabilityData.filter(
    (entry) => entry.room_config_id === roomConfigId && entry.date === dateStr
  );

  // Debug logging for specific dates
  if (dateStr === "2025-08-01" || dateStr === "2025-08-15") {
    console.log(`[isRoomConfigAvailableOnDate] Checking ${roomConfigId} on ${dateStr}:`, {
      totalAvailabilityRecords: availabilityData.length,
      matchingRecords: roomConfigAvailability.length,
      matchingRecordsData: roomConfigAvailability,
      allRoomConfigIds: [...new Set(availabilityData.map(a => a.room_config_id))],
    });
  }

  // If no availability data found, assume available (fallback to original behavior)
  if (roomConfigAvailability.length === 0) {
    console.log(`[isRoomConfigAvailableOnDate] No availability data found for ${roomConfigId} on ${dateStr}, defaulting to available`);
    return true;
  }

  // Check if any room of this config is available on this date
  const isAvailable = roomConfigAvailability.some((entry) => entry.is_available);

  if (dateStr === "2025-08-01" || dateStr === "2025-08-15") {
    console.log(`[isRoomConfigAvailableOnDate] Final result for ${roomConfigId} on ${dateStr}: ${isAvailable}`);
  }

  return isAvailable;
};

/**
 * Helper function to get availability status for a room config on a specific date
 */
export const getRoomConfigAvailabilityStatus = (
  roomConfigId: string,
  date: Date,
  availabilityData: RoomAvailabilityData[]
): {
  isAvailable: boolean;
  availableRooms: number;
  totalRooms: number;
  status: "available" | "partially_available" | "unavailable";
} => {
  const dateStr = format(date, "yyyy-MM-dd");
  
  // Find availability entries for this room config on this date
  const roomConfigAvailability = availabilityData.filter(
    (entry) => entry.room_config_id === roomConfigId && entry.date === dateStr
  );

  if (roomConfigAvailability.length === 0) {
    return {
      isAvailable: false,
      availableRooms: 0,
      totalRooms: 0,
      status: "unavailable",
    };
  }

  const availableRooms = roomConfigAvailability.filter((entry) => entry.is_available).length;
  const totalRooms = roomConfigAvailability.length;

  let status: "available" | "partially_available" | "unavailable";
  if (availableRooms === 0) {
    status = "unavailable";
  } else if (availableRooms === totalRooms) {
    status = "available";
  } else {
    status = "partially_available";
  }

  return {
    isAvailable: availableRooms > 0,
    availableRooms,
    totalRooms,
    status,
  };
};

/**
 * Helper function to get available room count for a room config on a specific date
 */
export const getAvailableRoomCount = (
  roomConfigId: string,
  date: Date,
  availabilityData: RoomAvailabilityData[]
): number => {
  const dateStr = format(date, "yyyy-MM-dd");
  
  return availabilityData
    .filter(
      (entry) => 
        entry.room_config_id === roomConfigId && 
        entry.date === dateStr && 
        entry.is_available
    )
    .reduce((total, entry) => total + entry.available_quantity, 0);
};
