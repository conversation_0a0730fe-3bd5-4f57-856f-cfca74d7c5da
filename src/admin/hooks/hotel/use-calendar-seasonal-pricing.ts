import { useQuery } from "@tanstack/react-query";
import { format } from "date-fns";

export type CalendarSeasonalRule = {
  id: string;
  base_price_rule_id: string;
  start_date: string;
  end_date: string;
  currency_code: string;
  priority?: number;
  name?: string;
  description?: string;
  weekday_prices?: {
    mon: number;
    tue: number;
    wed: number;
    thu: number;
    fri: number;
    sat: number;
    sun: number;
  };
  default_values?: {
    gross_cost: number;
    fixed_margin: number;
    margin_percentage: number;
    total: number;
  };
  weekday_values?: {
    mon?: { gross_cost: number; fixed_margin: number; margin_percentage: number; };
    tue?: { gross_cost: number; fixed_margin: number; margin_percentage: number; };
    wed?: { gross_cost: number; fixed_margin: number; margin_percentage: number; };
    thu?: { gross_cost: number; fixed_margin: number; margin_percentage: number; };
    fri?: { gross_cost: number; fixed_margin: number; margin_percentage: number; };
    sat?: { gross_cost: number; fixed_margin: number; margin_percentage: number; };
    sun?: { gross_cost: number; fixed_margin: number; margin_percentage: number; };
  };
  base_price_rule: {
    id: string;
    room_config_id: string;
    occupancy_type_id: string;
    meal_plan_id: string;
    currency_code: string;
  };
};

export type CalendarSeasonalPricingResponse = {
  room_config_id: string;
  date_range: {
    start_date: string;
    end_date: string;
  };
  filters: {
    occupancy_type_id: string | null;
    meal_plan_id: string | null;
  };
  seasonal_rules: CalendarSeasonalRule[];
  total_count: number;
};

export type UseCalendarSeasonalPricingParams = {
  roomConfigId: string;
  startDate: Date;
  endDate: Date;
  occupancyTypeId?: string;
  mealPlanId?: string;
  enabled?: boolean;
};

/**
 * Hook to fetch seasonal pricing rules for a specific date range in calendar view
 */
export const useCalendarSeasonalPricing = ({
  roomConfigId,
  startDate,
  endDate,
  occupancyTypeId,
  mealPlanId,
  enabled = true,
}: UseCalendarSeasonalPricingParams) => {
  const startDateStr = format(startDate, "yyyy-MM-dd");
  const endDateStr = format(endDate, "yyyy-MM-dd");

  return useQuery({
    queryKey: [
      "calendar-seasonal-pricing",
      roomConfigId,
      startDateStr,
      endDateStr,
      occupancyTypeId,
      mealPlanId,
    ],
    queryFn: async (): Promise<CalendarSeasonalPricingResponse> => {
      // Return empty data if no roomConfigId is provided
      if (!roomConfigId) {
        return {
          seasonal_rules: [],
          base_price_rules: [],
        };
      }

      const params = new URLSearchParams({
        start_date: startDateStr,
        end_date: endDateStr,
      });

      if (occupancyTypeId) {
        params.append("occupancy_type_id", occupancyTypeId);
      }

      if (mealPlanId) {
        params.append("meal_plan_id", mealPlanId);
      }

      const response = await fetch(
        `/admin/hotel-management/room-configs/${roomConfigId}/seasonal-pricing/calendar?${params.toString()}`
      );

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || "Failed to fetch calendar seasonal pricing");
      }

      return response.json();
    },
    enabled: enabled && !!roomConfigId,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  });
};

/**
 * Helper function to find seasonal rules that apply to a specific date
 */
export const findSeasonalRulesForDate = (
  date: Date,
  seasonalRules: CalendarSeasonalRule[],
  occupancyTypeId: string,
  mealPlanId: string
): CalendarSeasonalRule[] => {
  const dateStr = format(date, "yyyy-MM-dd");

  return seasonalRules
    .filter(rule => {
      // Check if the rule applies to the specific date
      const ruleStart = rule.start_date;
      const ruleEnd = rule.end_date;
      
      // Check if date is within the rule's date range
      const isWithinRange = dateStr >= ruleStart && dateStr <= ruleEnd;
      
      // Check if the rule matches the occupancy and meal plan
      const matchesContext = 
        rule.base_price_rule.occupancy_type_id === occupancyTypeId &&
        rule.base_price_rule.meal_plan_id === mealPlanId;

      return isWithinRange && matchesContext;
    })
    .sort((a, b) => (b.priority || 100) - (a.priority || 100)); // Higher priority first
};

/**
 * Helper function to get the price for a specific date and day of week
 */
export const getSeasonalPriceForDate = (
  date: Date,
  seasonalRules: CalendarSeasonalRule[],
  occupancyTypeId: string,
  mealPlanId: string
): number | null => {
  const applicableRules = findSeasonalRulesForDate(date, seasonalRules, occupancyTypeId, mealPlanId);
  
  if (applicableRules.length === 0) {
    return null; // No seasonal rule applies
  }

  const rule = applicableRules[0]; // Highest priority rule
  const dayOfWeek = format(date, "eee").toLowerCase() as keyof typeof rule.weekday_prices;

  // Use weekday_prices if available
  if (rule.weekday_prices && rule.weekday_prices[dayOfWeek] !== undefined) {
    return rule.weekday_prices[dayOfWeek];
  }

  // Fallback to amount if available (legacy support)
  return (rule as any).amount || null;
};

/**
 * Helper function to get cost and margin data for a specific date
 */
export const getSeasonalCostMarginForDate = (
  date: Date,
  seasonalRules: CalendarSeasonalRule[],
  occupancyTypeId: string,
  mealPlanId: string
): { cost: number | null; fixedMargin: number | null; marginPercentage: number | null } | null => {
  const applicableRules = findSeasonalRulesForDate(date, seasonalRules, occupancyTypeId, mealPlanId);
  
  if (applicableRules.length === 0) {
    return null; // No seasonal rule applies
  }

  const rule = applicableRules[0]; // Highest priority rule
  const dayOfWeek = format(date, "eee").toLowerCase() as keyof typeof rule.weekday_values;

  // Check weekday-specific values first
  if (rule.weekday_values && rule.weekday_values[dayOfWeek]) {
    const dayValues = rule.weekday_values[dayOfWeek];
    return {
      cost: dayValues.gross_cost || null,
      fixedMargin: dayValues.fixed_margin || null,
      marginPercentage: dayValues.margin_percentage || null,
    };
  }

  // Fallback to default values
  if (rule.default_values) {
    return {
      cost: rule.default_values.gross_cost || null,
      fixedMargin: rule.default_values.fixed_margin || null,
      marginPercentage: rule.default_values.margin_percentage || null,
    };
  }

  return null;
};
