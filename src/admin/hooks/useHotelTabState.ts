import { useState, useEffect } from "react";
import { useSearchParams } from "react-router-dom";
import { HotelTabId } from "../routes/hotel-management/hotels/[slug]/_components/hotel-tabs";

interface UseHotelTabStateOptions {
  defaultTab?: HotelTabId;
  paramName?: string;
}

export const useHotelTabState = (options: UseHotelTabStateOptions = {}) => {
  const { defaultTab = "overview", paramName = "tab" } = options;
  const [searchParams, setSearchParams] = useSearchParams();
  
  // Get initial tab from URL or use default
  const getInitialTab = (): HotelTabId => {
    const tabFromUrl = searchParams.get(paramName) as HotelTabId;
    const validTabs: HotelTabId[] = ["overview", "facilities", "insights", "bailey-ai", "settings"];

    if (tabFromUrl && validTabs.includes(tabFromUrl)) {
      return tabFromUrl;
    }

    return defaultTab;
  };

  const [activeTab, setActiveTab] = useState<HotelTabId>(getInitialTab);

  // Update URL when tab changes
  const handleTabChange = (newTab: HotelTabId) => {
    setActiveTab(newTab);
    
    // Update URL search params
    const newSearchParams = new URLSearchParams(searchParams);
    if (newTab === defaultTab) {
      // Remove tab param if it's the default tab
      newSearchParams.delete(paramName);
    } else {
      newSearchParams.set(paramName, newTab);
    }
    
    setSearchParams(newSearchParams, { replace: true });
  };

  // Listen for URL changes (browser back/forward)
  useEffect(() => {
    const tabFromUrl = searchParams.get(paramName) as HotelTabId;
    const validTabs: HotelTabId[] = ["overview", "facilities", "insights", "bailey-ai", "settings"];

    if (tabFromUrl && validTabs.includes(tabFromUrl) && tabFromUrl !== activeTab) {
      setActiveTab(tabFromUrl);
    } else if (!tabFromUrl && activeTab !== defaultTab) {
      setActiveTab(defaultTab);
    }
  }, [searchParams, paramName, activeTab, defaultTab]);

  return {
    activeTab,
    setActiveTab: handleTabChange,
  };
};
