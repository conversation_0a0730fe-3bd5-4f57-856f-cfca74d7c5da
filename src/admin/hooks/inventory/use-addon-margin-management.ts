import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "@camped-ai/ui";

// Types
export interface UpdateMarginRequest {
  addOnId: string;
  marginPercentage: number;
}

export interface UpdateMarginResponse {
  success: boolean;
  addOn: {
    id: string;
    title: string;
    pricing: {
      base_cost: number;
      base_currency: string;
      total_selling_price: number;
      calculated_margin: number;
    };
  };
}

// API functions
const updateAddOnMargin = async (addOnId: string, marginPercentage: number): Promise<UpdateMarginResponse> => {
  const response = await fetch(`/admin/inventory/add-ons/${addOnId}/margin`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({ margin_percentage: marginPercentage }),
  });

  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.message || 'Failed to update margin');
  }

  return response.json();
};

const bulkUpdateMargins = async (updates: Array<{ addOnId: string; marginPercentage: number }>): Promise<{
  success: boolean;
  updated_count: number;
  errors: Array<{ addOnId: string; error: string }>;
}> => {
  const response = await fetch('/admin/inventory/add-ons/bulk-margin-update', {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({ updates }),
  });

  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.message || 'Failed to update margins');
  }

  return response.json();
};

// Hooks
export const useUpdateAddOnMargin = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ addOnId, marginPercentage }: UpdateMarginRequest) =>
      updateAddOnMargin(addOnId, marginPercentage),
    onSuccess: (data, variables) => {
      toast.success(`Margin updated successfully for ${data.addOn.title}`);

      // Invalidate related queries
      queryClient.invalidateQueries({ queryKey: ['inventory-add-ons'] });
      queryClient.invalidateQueries({ queryKey: ['add-on', variables.addOnId] });
    },
    onError: (error: Error) => {
      toast.error(`Failed to update margin: ${error.message}`);
    },
  });
};

export const useBulkUpdateMargins = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: bulkUpdateMargins,
    onSuccess: (data) => {
      if (data.errors.length === 0) {
        toast.success(`Successfully updated margins for ${data.updated_count} add-ons`);
      } else {
        toast.warning(
          `Updated ${data.updated_count} margins. ${data.errors.length} errors occurred.`
        );
      }

      // Invalidate related queries
      queryClient.invalidateQueries({ queryKey: ['inventory-add-ons'] });
    },
    onError: (error: Error) => {
      toast.error(`Bulk margin update failed: ${error.message}`);
    },
  });
};

// Utility functions for margin calculations
// Formula: price = Net cost / (1 - margin/100)
export const calculateSellingPriceFromMargin = (baseCost: number, marginPercentage: number): number => {
  // Ensure margin is not 100% or higher to avoid division by zero/negative
  if (marginPercentage >= 100) {
    throw new Error(`Invalid margin percentage: ${marginPercentage}%. Margin must be less than 100%.`);
  }
  return baseCost / (1 - marginPercentage / 100);
};

export const calculateMarginFromSellingPrice = (baseCost: number, sellingPrice: number): number => {
  if (baseCost === 0 || sellingPrice === 0) return 0;
  // Reverse formula: margin = (1 - cost/price) * 100
  return (1 - baseCost / sellingPrice) * 100;
};

export const calculateProfitAmount = (baseCost: number, sellingPrice: number): number => {
  return sellingPrice - baseCost;
};

export const formatMarginPercentage = (margin: number): string => {
  return `${margin.toFixed(1)}%`;
};

export const formatCurrency = (amount: number, currency: string = "CHF"): string => {
  return new Intl.NumberFormat("en-CH", {
    style: "currency",
    currency: currency,
  }).format(amount);
};

// Validation functions
export const validateMargin = (marginPercentage: number): { isValid: boolean; error?: string } => {
  if (isNaN(marginPercentage)) {
    return { isValid: false, error: "Margin must be a valid number" };
  }

  if (marginPercentage < -100) {
    return { isValid: false, error: "Margin cannot be less than -100%" };
  }

  if (marginPercentage > 1000) {
    return { isValid: false, error: "Margin cannot exceed 1000%" };
  }

  return { isValid: true };
};

export const validateSellingPrice = (baseCost: number, sellingPrice: number): { isValid: boolean; error?: string } => {
  if (isNaN(sellingPrice)) {
    return { isValid: false, error: "Selling price must be a valid number" };
  }

  if (sellingPrice < 0) {
    return { isValid: false, error: "Selling price cannot be negative" };
  }

  if (sellingPrice < baseCost * 0.1) {
    return { isValid: false, error: "Selling price seems too low compared to base cost" };
  }

  return { isValid: true };
};
