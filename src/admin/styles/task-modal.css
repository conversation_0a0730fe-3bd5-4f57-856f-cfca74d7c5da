/**
 * Task Modal Styling
 * Provides 50% width modal with responsive behavior and improved UX
 */

/* ===== TASK MODAL SPECIFIC STYLING ===== */

/* Force right-side positioning for task modal */
[data-radix-portal] .task-modal-content,
[data-radix-portal] .task-modal-content [data-focus-modal-content],
.task-modal-content,
.task-modal-content [data-focus-modal-content] {
  position: fixed !important;
  right: 0 !important;
  top: 0 !important;
  left: auto !important;
  bottom: 0 !important;
  width: 50vw !important;
  max-width: 50vw !important;
  height: 100vh !important;
  margin: 0 !important;
  transform: none !important;
  z-index: 100 !important;
  inset: 0 0 0 auto !important;
}

/* Ensure the modal overlay covers the full screen */
.task-modal-content ~ [data-focus-modal-overlay],
[data-focus-modal-overlay] {
  position: fixed !important;
  inset: 0 !important;
  z-index: 99 !important;
  background: rgba(0, 0, 0, 0.5) !important;
}

/* Responsive behavior for smaller screens */
@media (max-width: 1200px) {
  .task-modal-content,
  .task-modal-content [data-focus-modal-content] {
    width: 60vw !important;
    max-width: 60vw !important;
  }
}

@media (max-width: 992px) {
  .task-modal-content,
  .task-modal-content [data-focus-modal-content] {
    width: 75vw !important;
    max-width: 75vw !important;
  }
}

@media (max-width: 768px) {
  .task-modal-content,
  .task-modal-content [data-focus-modal-content] {
    width: 95vw !important;
    max-width: 95vw !important;
    position: fixed !important;
    right: 0 !important;
    left: auto !important;
    inset: 0 0 0 auto !important;
  }
}

@media (max-width: 480px) {
  .task-modal-content,
  .task-modal-content [data-focus-modal-content] {
    width: 100vw !important;
    max-width: 100vw !important;
    right: 0 !important;
    left: 0 !important;
    inset: 0 !important;
  }
}

/* ===== MODAL STRUCTURE IMPROVEMENTS ===== */

/* Modal structure - ensure proper layout */
.task-modal-content [data-focus-modal-content] {
  display: flex !important;
  flex-direction: column !important;
  height: 100vh !important;
  overflow: hidden !important;
}

/* Modal header styling */
.task-modal-header {
  padding: 1.5rem !important;
  border-bottom: 1px solid rgb(229, 231, 235) !important;
  background: rgb(249, 250, 251) !important;
  flex-shrink: 0 !important;
}

/* Modal body styling with better spacing */
.task-modal-body {
  padding: 1.5rem !important;
  flex: 1 1 auto !important;
  overflow-y: auto !important;
  overflow-x: hidden !important;
  background: white !important;
}

/* Modal footer styling */
.task-modal-footer {
  padding: 1rem 1.5rem !important;
  border-top: 1px solid rgb(229, 231, 235) !important;
  background: rgb(249, 250, 251) !important;
  flex-shrink: 0 !important;
}

/* ===== FORM FIELD IMPROVEMENTS ===== */

/* Form field spacing */
.task-form-field {
  margin-bottom: 1.5rem !important;
}

.task-form-field:last-child {
  margin-bottom: 0 !important;
}

/* Label styling */
.task-form-label {
  font-weight: 500 !important;
  margin-bottom: 0.5rem !important;
  display: block !important;
  color: var(--ui-fg-base) !important;
}

/* Required field indicator */
.task-form-required {
  color: var(--ui-fg-error) !important;
  margin-left: 0.25rem !important;
}

/* Input field styling */
.task-form-input {
  width: 100% !important;
  transition: all 0.2s ease-in-out !important;
}

.task-form-input:focus {
  box-shadow: 0 0 0 2px var(--ui-border-interactive) !important;
}

/* Textarea styling */
.task-form-textarea {
  width: 100% !important;
  min-height: 80px !important;
  resize: vertical !important;
  transition: all 0.2s ease-in-out !important;
}

/* ===== DROPDOWN/SELECT IMPROVEMENTS ===== */

/* Select component styling for task modal */
.task-modal-content [data-radix-select-trigger],
.task-modal-content .task-form-field [data-radix-select-trigger] {
  width: 100% !important;
  min-height: 2.5rem !important;
  padding: 0.5rem 0.75rem !important;
  border: 1px solid rgb(209, 213, 219) !important;
  border-radius: 0.375rem !important;
  background: white !important;
  color: rgb(17, 24, 39) !important;
  font-size: 0.875rem !important;
  transition: all 0.2s ease-in-out !important;
  cursor: pointer !important;
}

.task-modal-content [data-radix-select-trigger]:hover {
  border-color: rgb(156, 163, 175) !important;
  background-color: rgb(249, 250, 251) !important;
}

.task-modal-content [data-radix-select-trigger]:focus {
  outline: none !important;
  border-color: rgb(59, 130, 246) !important;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1) !important;
}

.task-modal-content [data-radix-select-trigger][data-disabled] {
  background-color: rgb(243, 244, 246) !important;
  color: rgb(156, 163, 175) !important;
  cursor: not-allowed !important;
  opacity: 0.6 !important;
}

/* Error state for select triggers */
.task-modal-content .border-red-500[data-radix-select-trigger] {
  border-color: rgb(239, 68, 68) !important;
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1) !important;
}

/* Select content styling */
.task-modal-content [data-radix-select-content] {
  z-index: 102 !important;
  background: white !important;
  border: 1px solid rgb(209, 213, 219) !important;
  border-radius: 0.375rem !important;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05) !important;
}

/* Select item styling */
.task-modal-content [data-radix-select-item] {
  padding: 0.5rem 0.75rem !important;
  font-size: 0.875rem !important;
  cursor: pointer !important;
}

.task-modal-content [data-radix-select-item]:hover {
  background-color: rgb(249, 250, 251) !important;
}

.task-modal-content [data-radix-select-item][data-highlighted] {
  background-color: rgb(59, 130, 246) !important;
  color: white !important;
}

/* ===== GRID LAYOUT IMPROVEMENTS ===== */

/* Two-column grid for form fields */
.task-form-grid {
  display: grid !important;
  grid-template-columns: 1fr 1fr !important;
  gap: 1rem !important;
}

/* Single column on smaller screens */
@media (max-width: 768px) {
  .task-form-grid {
    grid-template-columns: 1fr !important;
  }
}

/* ===== ERROR HANDLING ===== */

/* Error message styling */
.task-form-error {
  color: var(--ui-fg-error) !important;
  font-size: 0.75rem !important;
  margin-top: 0.25rem !important;
  display: block !important;
}

/* Error state for inputs */
.task-form-input.error,
.task-form-textarea.error,
.task-form-select.error select {
  border-color: var(--ui-fg-error) !important;
  box-shadow: 0 0 0 1px var(--ui-fg-error) !important;
}

/* ===== LOADING STATES ===== */

/* Loading overlay for form */
.task-form-loading {
  position: relative !important;
}

.task-form-loading::after {
  content: '' !important;
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  background: rgba(255, 255, 255, 0.8) !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  z-index: 102 !important;
}

/* ===== BUTTON IMPROVEMENTS ===== */

/* Button spacing in footer */
.task-form-buttons {
  display: flex !important;
  gap: 0.75rem !important;
  justify-content: flex-end !important;
  align-items: center !important;
}

/* ===== ACCESSIBILITY IMPROVEMENTS ===== */

/* Focus visible improvements */
.task-form-input:focus-visible,
.task-form-textarea:focus-visible,
.task-form-select select:focus-visible {
  outline: 2px solid var(--ui-border-interactive) !important;
  outline-offset: 2px !important;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .task-form-input,
  .task-form-textarea,
  .task-form-select select {
    border-width: 2px !important;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .task-form-input,
  .task-form-textarea,
  .task-form-select select {
    transition: none !important;
  }
}

/* ===== Z-INDEX MANAGEMENT ===== */

/* Ensure proper z-index hierarchy */
.task-modal-content {
  z-index: 100 !important;
}

.task-modal-content [data-radix-select-content],
.task-modal-content [data-radix-popper-content] {
  z-index: 102 !important;
}

.task-modal-content [role="listbox"],
.task-modal-content [role="combobox"],
.task-modal-content .select-dropdown {
  z-index: 102 !important;
}

/* ===== CONTEXT AWARENESS STYLING ===== */

/* Hidden fields when in booking context */
.task-form-hidden-field {
  display: none !important;
}

/* Context indicator */
.task-form-context-indicator {
  background: var(--ui-bg-highlight) !important;
  color: var(--ui-fg-base) !important;
  padding: 0.75rem !important;
  border-radius: 0.375rem !important;
  margin-bottom: 1rem !important;
  font-size: 0.875rem !important;
  border-left: 4px solid var(--ui-border-interactive) !important;
}
