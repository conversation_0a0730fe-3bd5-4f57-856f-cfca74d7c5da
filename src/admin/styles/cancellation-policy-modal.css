/**
 * CSS fixes for Cancellation Policy modal styling
 * Ensures the modal appears on the right side with full height and proper z-index
 */

/* Target the FocusModal overlay for cancellation policy modals */
[data-focus-modal-overlay] {
  z-index: 100 !important;
}

/* Target the FocusModal content for cancellation policy modals */
[data-focus-modal-content] {
  z-index: 100 !important;
}

/* Ensure modal backdrop has correct z-index */
.camped-ui-focus-modal-overlay,
[data-focus-modal-overlay] {
  z-index: 100 !important;
  background-color: rgba(0, 0, 0, 0.5) !important;
}

/* Right-side modal positioning */
.cancellation-policy-modal,
[data-focus-modal-content].cancellation-policy-modal,
div[role="dialog"].cancellation-policy-modal,
.cancellation-policy-modal[data-state="open"],
.cancellation-policy-modal[data-state="closed"] {
  position: fixed !important;
  right: 0 !important;
  top: 0 !important;
  left: auto !important;
  bottom: 0 !important;
  height: 100vh !important;
  width: 600px !important;
  max-width: 90vw !important;
  margin: 0 !important;
  border-radius: 0 !important;
  z-index: 100 !important;
  box-shadow: -4px 0 20px rgba(0, 0, 0, 0.15) !important;
  transform: none !important;
  background: white !important;
  inset: 0 0 0 auto !important;
}

/* Animation for right-side slide in */
@keyframes slideInRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideOutRight {
  from {
    transform: translateX(0);
    opacity: 1;
  }
  to {
    transform: translateX(100%);
    opacity: 0;
  }
}

.cancellation-policy-modal,
[data-focus-modal-content].cancellation-policy-modal {
  animation: slideInRight 0.3s ease-out;
}

.cancellation-policy-modal[data-state="closed"],
[data-focus-modal-content].cancellation-policy-modal[data-state="closed"] {
  animation: slideOutRight 0.3s ease-in;
}

/* Ensure the modal content fills the full height */
.cancellation-policy-modal .camped-ui-focus-modal-content,
.cancellation-policy-modal [data-focus-modal-content] {
  height: 100vh !important;
  display: flex !important;
  flex-direction: column !important;
  overflow: hidden !important;
}

/* Ensure the form content is scrollable */
.cancellation-policy-modal .camped-ui-focus-modal-body,
.cancellation-policy-modal [data-focus-modal-body] {
  flex: 1 !important;
  overflow-y: auto !important;
  overflow-x: hidden !important;
}

/* Override FocusModal default positioning */
[data-radix-portal] .cancellation-policy-modal,
[data-radix-portal] [data-focus-modal-content].cancellation-policy-modal {
  position: fixed !important;
  right: 0 !important;
  top: 0 !important;
  left: auto !important;
  bottom: 0 !important;
  width: 600px !important;
  height: 100vh !important;
  max-width: 90vw !important;
  margin: 0 !important;
  transform: none !important;
}

/* Force the modal to stay on the right */
.cancellation-policy-modal {
  inset: 0 0 0 auto !important;
}

/* Override FocusModal overlay positioning */
[data-focus-modal-overlay] {
  position: fixed !important;
  inset: 0 !important;
  z-index: 100 !important;
  background: rgba(0, 0, 0, 0.5) !important;
}

/* Ensure the content wrapper doesn't interfere */
[data-focus-modal-content].cancellation-policy-modal {
  position: fixed !important;
  right: 0 !important;
  top: 0 !important;
  left: auto !important;
  margin: 0 !important;
  max-height: none !important;
  height: 100vh !important;
  width: 600px !important;
  max-width: 90vw !important;
  border-radius: 0 !important;
  transform: none !important;
  inset: 0 0 0 auto !important;
}

/* Additional targeting for any nested modal content */
.cancellation-policy-modal * {
  box-sizing: border-box;
}

/* Ensure modal content is properly contained */
.cancellation-policy-modal .camped-ui-focus-modal-content,
.cancellation-policy-modal [data-focus-modal-content] {
  position: relative !important;
  right: auto !important;
  top: auto !important;
  left: auto !important;
  width: 100% !important;
  height: 100% !important;
  margin: 0 !important;
  transform: none !important;
}

/* Force right-side positioning for any modal with cancellation-policy-modal class */
body .cancellation-policy-modal,
html .cancellation-policy-modal,
[data-radix-portal] .cancellation-policy-modal {
  position: fixed !important;
  right: 0 !important;
  top: 0 !important;
  left: auto !important;
  bottom: 0 !important;
  width: 600px !important;
  max-width: 90vw !important;
  height: 100vh !important;
  margin: 0 !important;
  transform: none !important;
  z-index: 100 !important;
  inset: 0 0 0 auto !important;
}
