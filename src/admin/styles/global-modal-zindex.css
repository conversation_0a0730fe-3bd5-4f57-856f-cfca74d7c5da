/**
 * Global Modal Z-Index Standardization
 * Ensures all modals, dialogs, and overlays use z-index 100 consistently
 */

/* ===== FOCUS MODAL COMPONENTS ===== */
.camped-ui-focus-modal,
[data-focus-modal],
.focus-modal {
  z-index: 100 !important;
}

.camped-ui-focus-modal-overlay,
[data-focus-modal-overlay],
.focus-modal-overlay {
  z-index: 100 !important;
}

.camped-ui-focus-modal-content,
[data-focus-modal-content],
.focus-modal-content {
  z-index: 100 !important;
}

/* ===== DIALOG COMPONENTS ===== */
.camped-ui-dialog,
[data-dialog-content],
[role="dialog"] {
  z-index: 100 !important;
}

.camped-ui-dialog-overlay,
[data-dialog-overlay] {
  z-index: 100 !important;
}

.camped-ui-dialog-content {
  z-index: 100 !important;
}

/* ===== RADIX UI COMPONENTS ===== */
[data-radix-portal],
[data-radix-popper-content-wrapper] {
  z-index: 100 !important;
}

/* ===== SPECIFIC MODAL TYPES ===== */

/* Cancellation Policy Modals */
.cancellation-policy-modal {
  z-index: 100 !important;
}

/* Destination Modals */
.destination-modal {
  z-index: 100 !important;
}

/* Hotel Modals */
.hotel-modal {
  z-index: 100 !important;
}

/* Room Config Modals */
.room-config-modal {
  z-index: 100 !important;
}

/* ===== MODAL CONTENT ELEMENTS ===== */

/* Headers */
.camped-ui-focus-modal-header,
[data-focus-modal-header] {
  z-index: 101 !important;
}

/* Bodies */
.camped-ui-focus-modal-body,
[data-focus-modal-body] {
  z-index: 100 !important;
}

/* Footers */
.camped-ui-focus-modal-footer,
[data-focus-modal-footer] {
  z-index: 101 !important;
}

/* ===== NESTED ELEMENTS ===== */

/* Dropdowns and Selects within modals */
.modal select,
.modal .select-dropdown,
.modal [role="listbox"],
.modal [role="combobox"],
[data-focus-modal-content] select,
[data-focus-modal-content] .select-dropdown,
[data-focus-modal-content] [role="listbox"],
[data-focus-modal-content] [role="combobox"] {
  z-index: 101 !important;
}

/* Tooltips and Popovers within modals */
.modal [data-tooltip],
.modal [role="tooltip"],
.modal .tooltip,
[data-focus-modal-content] [data-tooltip],
[data-focus-modal-content] [role="tooltip"],
[data-focus-modal-content] .tooltip {
  z-index: 102 !important;
}

/* ===== OVERLAY STANDARDIZATION ===== */
.modal-overlay,
.dialog-overlay,
[data-overlay],
.bg-ui-bg-overlay {
  z-index: 100 !important;
  background-color: rgba(0, 0, 0, 0.5) !important;
}

/* ===== PROMPT AND CONFIRMATION DIALOGS ===== */
.prompt-dialog,
.confirmation-dialog,
.alert-dialog {
  z-index: 100 !important;
}

/* ===== UI BACKGROUND OVERLAYS ===== */
.bg-ui-bg-overlay,
.ui-bg-overlay,
[class*="bg-ui-bg-overlay"] {
  z-index: 100 !important;
}

/* ===== TOAST AND NOTIFICATION OVERRIDES ===== */
.toast-container,
.notification-container {
  z-index: 110 !important; /* Slightly higher than modals */
}

/* ===== MOBILE RESPONSIVE ===== */
@media (max-width: 768px) {
  /* Ensure modals work properly on mobile */
  .camped-ui-focus-modal-content,
  [data-focus-modal-content] {
    z-index: 100 !important;
    max-width: 95vw !important;
  }
}

/* ===== ACCESSIBILITY ===== */
/* Ensure focus trapping works with proper z-index */
[data-focus-trap] {
  z-index: 100 !important;
}

/* ===== DEBUGGING HELPER ===== */
/* Uncomment for debugging z-index issues */
/*
.debug-zindex * {
  position: relative !important;
}

.debug-zindex *:before {
  content: attr(class) " z:" attr(style);
  position: absolute;
  top: 0;
  left: 0;
  background: yellow;
  color: black;
  font-size: 10px;
  padding: 2px;
  z-index: 9999;
  pointer-events: none;
}
*/

/* ===== IMPORTANT OVERRIDES ===== */
/* Force all modal-related elements to use z-index 100 */
div[role="dialog"],
div[role="alertdialog"],
div[role="tooltip"],
div[data-state="open"][role="dialog"],
div[data-state="open"][role="alertdialog"] {
  z-index: 100 !important;
}

/* Ensure backdrop/overlay elements are properly layered */
div[data-state="open"] + div[data-radix-portal],
div[data-radix-portal] div[data-state="open"] {
  z-index: 100 !important;
}

/* Final catch-all for any missed modal elements */
[class*="modal"],
[class*="dialog"],
[class*="overlay"],
[id*="modal"],
[id*="dialog"],
.bg-ui-bg-overlay,
[class*="bg-ui-bg-overlay"] {
  z-index: 100 !important;
}
