/**
 * Modern tab styling enhancements
 * Provides additional visual effects for active tabs
 */

/* Enhanced active tab styling with modern effects */
.hotel-tab-active,
.destination-tab-active,
.supplier-product-service-tab-active {
  position: relative;
  overflow: hidden;
}

/* Subtle animated background for active tabs */
.hotel-tab-active::before,
.destination-tab-active::before,
.supplier-product-service-tab-active::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.1),
    transparent
  );
  animation: shimmer 2s infinite;
  pointer-events: none;
}

@keyframes shimmer {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}

/* Enhanced hover effects for inactive tabs */
.hotel-tab-inactive:hover,
.destination-tab-inactive:hover,
.supplier-product-service-tab-inactive:hover {
  transform: translateY(-1px);
  transition: all 0.2s ease-out;
}

/* Smooth transitions for all tab states */
.hotel-tab,
.destination-tab,
.supplier-product-service-tab {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Active tab glow effect */
.hotel-tab-active,
.destination-tab-active,
.supplier-product-service-tab-active {
  box-shadow: 0 10px 25px -5px rgba(59, 130, 246, 0.3),
    0 4px 6px -2px rgba(59, 130, 246, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

/* Enhanced focus states */
.hotel-tab:focus-visible,
.destination-tab:focus-visible,
.supplier-product-service-tab:focus-visible {
  outline: none;
  ring: 2px;
  ring-color: rgb(59 130 246 / 0.5);
  ring-offset: 2px;
  ring-offset-color: white;
}

/* Improved tab container backdrop */
.hotel-tabs-container,
.destination-tabs-container {
  backdrop-filter: blur(12px);
  -webkit-backdrop-filter: blur(12px);
  background: rgba(249, 250, 251, 0.9);
  border-bottom: 1px solid rgba(229, 231, 235, 0.8);
}

/* Modern scrollbar for tab navigation */
.hotel-tabs-nav::-webkit-scrollbar,
.destination-tabs-nav::-webkit-scrollbar {
  height: 4px;
}

.hotel-tabs-nav::-webkit-scrollbar-track,
.destination-tabs-nav::-webkit-scrollbar-track {
  background: rgba(243, 244, 246, 0.5);
  border-radius: 2px;
}

.hotel-tabs-nav::-webkit-scrollbar-thumb,
.destination-tabs-nav::-webkit-scrollbar-thumb {
  background: rgba(156, 163, 175, 0.5);
  border-radius: 2px;
}

.hotel-tabs-nav::-webkit-scrollbar-thumb:hover,
.destination-tabs-nav::-webkit-scrollbar-thumb:hover {
  background: rgba(107, 114, 128, 0.7);
}

/* Enhanced badge styling */
.hotel-tab-badge,
.destination-tab-badge {
  transition: all 0.2s ease-out;
}

.hotel-tab-active .hotel-tab-badge,
.destination-tab-active .destination-tab-badge {
  animation: pulse-subtle 2s infinite;
}

@keyframes pulse-subtle {
  0%,
  100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.9;
    transform: scale(1.05);
  }
}

/* Responsive enhancements */
@media (max-width: 768px) {
  .hotel-tab,
  .destination-tab {
    padding: 0.75rem 1rem;
  }

  .hotel-tab-active,
  .destination-tab-active {
    transform: translateY(-2px);
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .hotel-tabs-container,
  .destination-tabs-container {
    background: rgba(17, 24, 39, 0.9);
    border-bottom-color: rgba(75, 85, 99, 0.8);
  }

  .hotel-tab-inactive,
  .destination-tab-inactive {
    background: rgba(31, 41, 55, 0.8);
  }

  .hotel-tab-inactive:hover,
  .destination-tab-inactive:hover {
    background: rgba(55, 65, 81, 0.9);
  }
}

/* Accessibility improvements */
@media (prefers-reduced-motion: reduce) {
  .hotel-tab,
  .destination-tab,
  .hotel-tab-badge,
  .destination-tab-badge,
  .hotel-tab-active::before,
  .destination-tab-active::before {
    animation: none;
    transition: none;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .hotel-tab-active,
  .destination-tab-active {
    border: 2px solid;
    border-color: rgb(59 130 246);
  }

  .hotel-tab-inactive,
  .destination-tab-inactive {
    border: 1px solid;
    border-color: rgb(156 163 175);
  }
}
