/**
 * CSS fixes for Destination modal z-index issues
 * This ensures the destination create/edit modals appear above all other elements
 */

/* Target the FocusModal component for destinations */
.camped-ui-focus-modal,
[data-focus-modal] {
  z-index: 100 !important;
}

.camped-ui-focus-modal-overlay,
[data-focus-modal-overlay] {
  z-index: 100 !important;
}

.camped-ui-focus-modal-content,
[data-focus-modal-content] {
  z-index: 100 !important;
}

/* Target the specific modal elements by their DOM structure */
div[role="dialog"] {
  z-index: 100 !important;
}

/* Ensure modal content appears above everything else */
.focus-modal-content {
  z-index: 100 !important;
}

.focus-modal-overlay {
  z-index: 100 !important;
}

/* Additional targeting for nested modal elements */
[data-radix-popper-content-wrapper] {
  z-index: 100 !important;
}

/* Ensure any dropdowns or selects within the modal also have proper z-index */
.destination-modal select,
.destination-modal .select-dropdown,
.destination-modal [role="listbox"],
.destination-modal [role="combobox"] {
  z-index: 101 !important;
}

/* Target any tooltips or popovers within the destination modal */
.destination-modal [data-tooltip],
.destination-modal [role="tooltip"],
.destination-modal .tooltip {
  z-index: 102 !important;
}
