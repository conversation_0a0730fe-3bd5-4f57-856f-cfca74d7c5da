# Exchange Rate Management RBAC Implementation

## Overview

This document outlines the implementation of a new RBAC permission system for Exchange Rates management that requires users to have BOTH `supplier_management:view` AND `exchange_rate_management:view` permissions to access exchange rate functionality.

## Implementation Summary

### 1. New Permissions Added

**RBAC Types** (`src/modules/rbac/types.ts`):
- `EXCHANGE_RATE_MANAGEMENT_VIEW = "exchange_rate_management:view"`
- `EXCHANGE_RATE_MANAGEMENT_CREATE = "exchange_rate_management:create"`
- `EXCHANGE_RATE_MANAGEMENT_EDIT = "exchange_rate_management:edit"`
- `EXCHANGE_RATE_MANAGEMENT_DELETE = "exchange_rate_management:delete"`
- `EXCHANGE_RATE_MANAGEMENT_BULK_OPERATIONS = "exchange_rate_management:bulk_operations"`

**Permission Metadata** (`src/modules/rbac/permissions.ts`):
- Added metadata for all exchange rate management permissions with group `"exchange_rate_management"`

### 2. Role Management UI Updated

**Permission Table** (`src/admin/components/rbac/PermissionTable.tsx`):
- Added "Exchange Rate Management" as a new module in the permission matrix
- Positioned after "Supplier Management" and before "Supplier Cost Management"
- Includes all standard CRUD permissions plus bulk operations

### 3. Exchange Rates Page Updated

**Page Client** (`src/admin/routes/supplier-management/config/exchange-rates/page-client.tsx`):
- Added permission check requiring BOTH permissions: `supplier_management:view` AND `exchange_rate_management:view`
- Updated action buttons to use specific exchange rate permissions:
  - Create button: `exchange_rate_management:create`
  - Edit action: `exchange_rate_management:edit`
  - Delete action: `exchange_rate_management:delete`
- Shows access denied message if user lacks either required permission

### 4. API Middleware Updated

**New Middleware Function** (`src/api/middlewares/rbac.ts`):
- Added `requireAllPermissions()` function that enforces AND logic for multiple permissions
- Checks that user has ALL specified permissions, not just any one

**Middleware Configuration** (`src/api/middlewares.ts`):
- Added exchange rates API endpoints with dual permission requirements:
  - GET `/admin/supplier-management/exchange-rates`: requires `supplier_management:view` AND `exchange_rate_management:view`
  - POST `/admin/supplier-management/exchange-rates`: requires `supplier_management:view` AND `exchange_rate_management:create`
  - GET `/admin/supplier-management/exchange-rates/:id`: requires `supplier_management:view` AND `exchange_rate_management:view`
  - PUT `/admin/supplier-management/exchange-rates/:id`: requires `supplier_management:view` AND `exchange_rate_management:edit`
  - DELETE `/admin/supplier-management/exchange-rates/:id`: requires `supplier_management:view` AND `exchange_rate_management:delete`

### 5. Sidebar Navigation Updated

**Permission-Based Sidebar Hider** (`src/admin/widgets/permission-based-sidebar-hider.tsx`):
- Updated exchange rates menu item to require both permissions: `["supplier_management:view", "exchange_rate_management:view"]`
- Implemented special AND logic for exchange rates (different from other items that use OR logic)
- Updated logging to show "AND" vs "or" in permission messages

## Permission Hierarchy

### Expected Behavior

| User Permissions | Exchange Rates Access | Explanation |
|------------------|----------------------|-------------|
| None | ❌ No Access | Missing both required permissions |
| `supplier_management:view` only | ❌ No Access | Missing `exchange_rate_management:view` |
| `exchange_rate_management:view` only | ❌ No Access | Missing `supplier_management:view` |
| Both `supplier_management:view` AND `exchange_rate_management:view` | ✅ View Access | Has both required permissions |
| Both permissions + `exchange_rate_management:create` | ✅ View + Create | Can view and create exchange rates |
| Both permissions + `exchange_rate_management:edit` | ✅ View + Edit | Can view and edit exchange rates |
| Both permissions + `exchange_rate_management:delete` | ✅ View + Delete | Can view and delete exchange rates |
| Admin role | ✅ Full Access | Admin bypasses all permission checks |

### Test Cases

#### Test Case 1: No Permissions
**Setup**: User with no permissions
**Expected Results**:
- ❌ Cannot see exchange rates menu item in sidebar
- ❌ Cannot access `/supplier-management/config/exchange-rates` (should redirect or show access denied)
- ❌ API calls return 403 Forbidden

#### Test Case 2: Only Supplier Management Permission
**Setup**: User with only `supplier_management:view`
**Expected Results**:
- ❌ Cannot see exchange rates menu item in sidebar
- ❌ Cannot access exchange rates page (shows access denied message)
- ❌ API calls return 403 Forbidden with message about missing `exchange_rate_management:view`

#### Test Case 3: Only Exchange Rate Management Permission
**Setup**: User with only `exchange_rate_management:view`
**Expected Results**:
- ❌ Cannot see exchange rates menu item in sidebar
- ❌ Cannot access exchange rates page (shows access denied message)
- ❌ API calls return 403 Forbidden with message about missing `supplier_management:view`

#### Test Case 4: Both View Permissions
**Setup**: User with `supplier_management:view` AND `exchange_rate_management:view`
**Expected Results**:
- ✅ Can see exchange rates menu item in sidebar
- ✅ Can access exchange rates page and view data
- ✅ API GET calls succeed
- ❌ Cannot see Create/Edit/Delete buttons (missing specific permissions)
- ❌ API POST/PUT/DELETE calls return 403 Forbidden

#### Test Case 5: Full Exchange Rate Permissions
**Setup**: User with `supplier_management:view` AND all `exchange_rate_management:*` permissions
**Expected Results**:
- ✅ Can see exchange rates menu item in sidebar
- ✅ Can access exchange rates page and view data
- ✅ Can see and use Create/Edit/Delete buttons
- ✅ All API calls succeed

#### Test Case 6: Admin User
**Setup**: User with admin role
**Expected Results**:
- ✅ Full access to all exchange rate functionality
- ✅ All API calls succeed regardless of specific permissions

## Technical Implementation Details

### New Middleware Function

```typescript
export const requireAllPermissions = (permissions: string[]) => {
  return async (req: MedusaRequest, res: MedusaResponse, next: NextFunction) => {
    // Check that user has ALL specified permissions
    for (const permission of permissions) {
      const hasPermission = await rbacService.hasPermission(user, permission);
      if (!hasPermission) {
        return res.status(403).json({
          type: "permission_denied",
          message: `Access denied. Required permissions: ${permissions.join(" AND ")}`,
          missing_permission: permission,
        });
      }
    }
    return next();
  };
};
```

### Permission Check Logic

```typescript
// Frontend permission check
const hasSupplierManagementAccess = hasPermission("supplier_management:view");
const hasExchangeRateAccess = hasPermission("exchange_rate_management:view");
const hasFullAccess = hasSupplierManagementAccess && hasExchangeRateAccess;

if (!hasFullAccess) {
  // Show access denied message
}
```

### Sidebar Permission Logic

```typescript
// Special AND logic for exchange rates
if (normalizedText.includes("exchange rates")) {
  hasRequiredPermission = permission.every(perm => hasPermission(perm));
} else {
  // OR logic for other items
  hasRequiredPermission = hasAnyPermission(permission);
}
```

## Security Considerations

1. **Defense in Depth**: Permission checks are implemented at multiple layers:
   - Frontend UI (sidebar visibility, button visibility, page access)
   - API middleware (endpoint protection)
   - Component level (access denied messages)

2. **Principle of Least Privilege**: Users need specific permissions for each action:
   - View permission for reading data
   - Create permission for adding new rates
   - Edit permission for modifying existing rates
   - Delete permission for removing rates

3. **Hierarchical Access**: Exchange rates are considered part of supplier management, so users need supplier management access as a prerequisite

4. **Admin Override**: Admin users bypass all permission checks for system administration purposes

## Rollback Plan

If issues arise, the implementation can be rolled back by:

1. Reverting permission checks in exchange rates page to use only `supplier_management:view`
2. Removing exchange rate specific permissions from middleware configuration
3. Updating sidebar logic to use single permission check
4. Removing new permission types from RBAC system (optional, for clean rollback)

## Monitoring

After deployment, monitor for:
- Reduced access to exchange rates functionality (expected for users without proper permissions)
- No increase in 500 errors (should only see 403 Forbidden for unauthorized access)
- Proper functionality for users with correct permissions
- Admin users maintaining full access
