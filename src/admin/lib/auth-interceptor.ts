import { toast } from "@camped-ai/ui";

/**
 * Global authentication interceptor for handling deactivated user responses
 * This should be called whenever a 403 response with type "account_deactivated" is received
 */

export interface DeactivatedAccountResponse {
  type: "account_deactivated";
  message: string;
}

// Global flag to prevent multiple redirects
let isRedirecting = false;

/**
 * Handle account deactivation by clearing auth data and redirecting to login
 */
export const handleAccountDeactivation = (
  response?: DeactivatedAccountResponse
) => {
  // Prevent multiple redirects
  if (isRedirecting) return;
  isRedirecting = true;

  console.log("🔒 Account deactivation detected, handling logout...", response);

  // Clear all authentication-related data
  clearAuthenticationData();

  // Show informative toast
  const message =
    response?.message ||
    "Your account has been deactivated. Please contact an administrator for assistance.";
  toast.error("Account Deactivated", {
    description: message,
    duration: 8000, // Show for 8 seconds
  });

  // Redirect to login page after a short delay to allow toast to show
  setTimeout(() => {
    redirectToLogin();
  }, 1000);
};

/**
 * Clear all authentication cookies, tokens, and session data
 */
const clearAuthenticationData = () => {
  try {
    // Clear all cookies (including session cookies)
    document.cookie.split(";").forEach((cookie) => {
      const eqPos = cookie.indexOf("=");
      const name = eqPos > -1 ? cookie.substr(0, eqPos).trim() : cookie.trim();

      // Clear cookie for current domain
      document.cookie = `${name}=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/`;
      document.cookie = `${name}=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/;domain=${window.location.hostname}`;

      // Clear cookie for parent domain (if subdomain)
      const domain = window.location.hostname.split(".").slice(-2).join(".");
      if (domain !== window.location.hostname) {
        document.cookie = `${name}=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/;domain=.${domain}`;
      }
    });

    // Clear localStorage
    localStorage.clear();

    // Clear sessionStorage
    sessionStorage.clear();

    console.log("Authentication data cleared successfully");
  } catch (error) {
    console.error("Error clearing authentication data:", error);
  }
};

/**
 * Redirect to login page
 */
const redirectToLogin = () => {
  try {
    // Check if we're in the admin panel
    const currentPath = window.location.pathname;

    if (currentPath.startsWith("/app")) {
      // Admin panel - redirect to admin login
      window.location.href = "/app/login";
    } else {
      // Fallback - redirect to root which should handle auth
      window.location.href = "/";
    }
  } catch (error) {
    console.error("Error redirecting to login:", error);
    // Fallback redirect
    window.location.href = "/";
  }
};

/**
 * Check if a response indicates account deactivation
 */
export const isAccountDeactivatedResponse = (
  response: any
): response is DeactivatedAccountResponse => {
  return response && response.type === "account_deactivated";
};

/**
 * Initialize global fetch interceptor
 * Call this once when the app starts
 */
export const initializeAuthInterceptor = () => {
  // Store original fetch
  const originalFetch = window.fetch;

  // Override global fetch
  window.fetch = async (
    input: RequestInfo | URL,
    init?: RequestInit
  ): Promise<Response> => {
    try {
      const response = await originalFetch(input, init);

      // Only check admin API calls
      const url = typeof input === "string" ? input : input.toString();
      if (url.includes("/admin/")) {
        // Check for 403 responses
        if (response.status === 403) {
          try {
            // Clone response to avoid consuming the body
            const clonedResponse = response.clone();
            const data = await clonedResponse.json();

            if (isAccountDeactivatedResponse(data)) {
              console.log(
                "🔒 Detected account deactivation in fetch response:",
                data
              );
              handleAccountDeactivation(data);
            }
          } catch (jsonError) {
            // Ignore JSON parsing errors
            console.log("Could not parse response as JSON, ignoring");
          }
        }
      }

      return response;
    } catch (error) {
      throw error;
    }
  };

  console.log("🔒 Global fetch interceptor initialized");

  // Add test function to global window for easy testing
  if (typeof window !== "undefined") {
    (window as any).testAccountDeactivation = testAccountDeactivation;
    console.log("🧪 Test function available: window.testAccountDeactivation()");
  }
};

/**
 * Test function to simulate account deactivation
 * Call this from browser console to test the functionality
 */
export const testAccountDeactivation = () => {
  console.log("🧪 Testing account deactivation handler...");
  handleAccountDeactivation({
    type: "account_deactivated",
    message: "Test: Your account has been deactivated for testing purposes.",
  });
};

/**
 * Global fetch wrapper that handles account deactivation responses
 * This can be used to wrap existing fetch calls
 */
export const fetchWithAuthInterceptor = async (
  url: string,
  options?: RequestInit
): Promise<Response> => {
  try {
    const response = await fetch(url, {
      credentials: "include", // Include cookies for session auth
      ...options,
    });

    // Check for 403 responses
    if (response.status === 403) {
      try {
        const responseData = await response.clone().json();
        if (isAccountDeactivatedResponse(responseData)) {
          handleAccountDeactivation(responseData);
          throw new Error("Account deactivated");
        }
      } catch (jsonError) {
        // If we can't parse JSON, it's not our specific error format
        // Let the original response be handled normally
      }
    }

    return response;
  } catch (error) {
    // Re-throw the error for normal error handling
    throw error;
  }
};

/**
 * Wrapper for SDK client fetch that adds auth interceptor
 */
export const createAuthInterceptedSDK = (originalSDK: any) => {
  const originalFetch = originalSDK.client.fetch;

  originalSDK.client.fetch = async (url: string, options?: any) => {
    try {
      const response = await originalFetch(url, options);

      // Check if response indicates account deactivation
      if (
        response &&
        typeof response === "object" &&
        isAccountDeactivatedResponse(response)
      ) {
        handleAccountDeactivation(response);
        throw new Error("Account deactivated");
      }

      return response;
    } catch (error) {
      // Handle different error formats

      // Case 1: Error has response property (axios-like)
      if (error && typeof error === "object" && error.response) {
        const errorResponse = error.response;
        if (errorResponse.status === 403) {
          try {
            const errorData =
              typeof errorResponse.data === "object"
                ? errorResponse.data
                : await errorResponse.json();
            if (isAccountDeactivatedResponse(errorData)) {
              handleAccountDeactivation(errorData);
              throw new Error("Account deactivated");
            }
          } catch (jsonError) {
            // Ignore JSON parsing errors
          }
        }
      }

      // Case 2: Error is a fetch Response object
      if (error && typeof error === "object" && error.status === 403) {
        try {
          const errorData = await error.json();
          if (isAccountDeactivatedResponse(errorData)) {
            handleAccountDeactivation(errorData);
            throw new Error("Account deactivated");
          }
        } catch (jsonError) {
          // Ignore JSON parsing errors
        }
      }

      // Case 3: Error message contains 403 and has data
      if (error && error.message && error.message.includes("403")) {
        if (isAccountDeactivatedResponse(error)) {
          handleAccountDeactivation(error);
          throw new Error("Account deactivated");
        }
      }

      // Case 4: Direct error object check
      if (isAccountDeactivatedResponse(error)) {
        handleAccountDeactivation(error);
        throw new Error("Account deactivated");
      }

      // Re-throw original error if not handled
      throw error;
    }
  };

  return originalSDK;
};

/**
 * React Query error handler for account deactivation
 */
export const handleReactQueryError = (error: any) => {
  // Check if error indicates account deactivation
  if (error && error.message && error.message.includes("403")) {
    // Try to extract response data
    if (error.response) {
      try {
        const errorData = error.response.data || error.response;
        if (isAccountDeactivatedResponse(errorData)) {
          handleAccountDeactivation(errorData);
          return;
        }
      } catch (parseError) {
        // Ignore parsing errors
      }
    }
  }

  // Check direct error object
  if (isAccountDeactivatedResponse(error)) {
    handleAccountDeactivation(error);
  }
};
