import { format } from "date-fns";

export const formatDateRange = (startDate: Date, endDate: Date): string => {
  const startDay = format(startDate, "dd");
  const endDay = format(endDate, "dd");
  const startMonth = format(startDate, "MMM");
  const endMonth = format(endDate, "MMM");
  const startYear = format(startDate, "yyyy");
  const endYear = format(endDate, "yyyy");

  if (startYear !== endYear) {
    return `${startDay} ${startMonth} ${startYear} - ${endDay} ${endMonth} ${endYear}`;
  }

  if (startMonth !== endMonth) {
    return `${startDay} ${startMonth} - ${endDay} ${endMonth} ${startYear}`;
  }

  return `${startDay} - ${endDay} ${startMonth} ${startYear}`;
};