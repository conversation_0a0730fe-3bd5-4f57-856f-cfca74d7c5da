import { QueryClient } from "@tanstack/react-query";
import { handleReactQueryError } from "./auth-interceptor";

// Create a custom query client with optimized caching settings
export const createOptimizedQueryClient = () => {
  return new QueryClient({
    defaultOptions: {
      queries: {
        // Cache data for 5 minutes by default
        staleTime: 5 * 60 * 1000,
        // Keep data in cache for 10 minutes
        gcTime: 10 * 60 * 1000,
        // Retry failed requests 2 times instead of 3
        retry: 2,
        // Don't refetch on window focus for pricing data
        refetchOnWindowFocus: false,
        // Don't refetch on reconnect for pricing data
        refetchOnReconnect: false,
        // Global error handler for authentication issues
        onError: handleReactQueryError,
      },
      mutations: {
        // Retry failed mutations once
        retry: 1,
        // Global error handler for authentication issues
        onError: handleReactQueryError,
      },
    },
  });
};

// Specific cache settings for different data types
export const cacheSettings = {
  // Hotel pricing data - cache longer since it changes less frequently
  hotelPricing: {
    staleTime: 10 * 60 * 1000, // 10 minutes
    gcTime: 30 * 60 * 1000, // 30 minutes
  },

  // Room configurations - cache moderately
  roomConfigs: {
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 15 * 60 * 1000, // 15 minutes
  },

  // Occupancy configs and meal plans - cache longer
  configurations: {
    staleTime: 15 * 60 * 1000, // 15 minutes
    gcTime: 60 * 60 * 1000, // 1 hour
  },

  // Real-time data like availability - cache shorter
  realTime: {
    staleTime: 1 * 60 * 1000, // 1 minute
    gcTime: 5 * 60 * 1000, // 5 minutes
  },
};
