import { fetchWithAuthInterceptor, handleReactQueryError } from "./auth-interceptor";

/**
 * Enhanced API client that automatically handles account deactivation responses
 * Use this instead of direct fetch calls for admin API requests
 */

export interface ApiResponse<T = any> {
  data?: T;
  error?: string;
  message?: string;
  type?: string;
}

/**
 * Enhanced fetch function with automatic auth handling
 */
export const apiClient = {
  /**
   * GET request with auth interceptor
   */
  get: async <T = any>(url: string, options?: RequestInit): Promise<T> => {
    const response = await fetchWithAuthInterceptor(url, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        ...options?.headers,
      },
      ...options,
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.message || `HTTP ${response.status}: ${response.statusText}`);
    }

    return response.json();
  },

  /**
   * POST request with auth interceptor
   */
  post: async <T = any>(url: string, data?: any, options?: RequestInit): Promise<T> => {
    const response = await fetchWithAuthInterceptor(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        ...options?.headers,
      },
      body: data ? JSON.stringify(data) : undefined,
      ...options,
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.message || `HTTP ${response.status}: ${response.statusText}`);
    }

    return response.json();
  },

  /**
   * PUT request with auth interceptor
   */
  put: async <T = any>(url: string, data?: any, options?: RequestInit): Promise<T> => {
    const response = await fetchWithAuthInterceptor(url, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        ...options?.headers,
      },
      body: data ? JSON.stringify(data) : undefined,
      ...options,
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.message || `HTTP ${response.status}: ${response.statusText}`);
    }

    return response.json();
  },

  /**
   * DELETE request with auth interceptor
   */
  delete: async <T = any>(url: string, options?: RequestInit): Promise<T> => {
    const response = await fetchWithAuthInterceptor(url, {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json',
        ...options?.headers,
      },
      ...options,
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.message || `HTTP ${response.status}: ${response.statusText}`);
    }

    return response.json();
  },

  /**
   * PATCH request with auth interceptor
   */
  patch: async <T = any>(url: string, data?: any, options?: RequestInit): Promise<T> => {
    const response = await fetchWithAuthInterceptor(url, {
      method: 'PATCH',
      headers: {
        'Content-Type': 'application/json',
        ...options?.headers,
      },
      body: data ? JSON.stringify(data) : undefined,
      ...options,
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.message || `HTTP ${response.status}: ${response.statusText}`);
    }

    return response.json();
  },
};

/**
 * React Query compatible API client
 * Use this for React Query queries and mutations
 */
export const reactQueryApiClient = {
  get: async <T = any>(url: string, options?: RequestInit): Promise<T> => {
    try {
      return await apiClient.get<T>(url, options);
    } catch (error) {
      handleReactQueryError(error);
      throw error;
    }
  },

  post: async <T = any>(url: string, data?: any, options?: RequestInit): Promise<T> => {
    try {
      return await apiClient.post<T>(url, data, options);
    } catch (error) {
      handleReactQueryError(error);
      throw error;
    }
  },

  put: async <T = any>(url: string, data?: any, options?: RequestInit): Promise<T> => {
    try {
      return await apiClient.put<T>(url, data, options);
    } catch (error) {
      handleReactQueryError(error);
      throw error;
    }
  },

  delete: async <T = any>(url: string, options?: RequestInit): Promise<T> => {
    try {
      return await apiClient.delete<T>(url, options);
    } catch (error) {
      handleReactQueryError(error);
      throw error;
    }
  },

  patch: async <T = any>(url: string, data?: any, options?: RequestInit): Promise<T> => {
    try {
      return await apiClient.patch<T>(url, data, options);
    } catch (error) {
      handleReactQueryError(error);
      throw error;
    }
  },
};

export default apiClient;
