import { defineWidgetConfig } from "@camped-ai/admin-sdk";
import { useEffect } from "react";
import { initializeAuthInterceptor } from "../lib/auth-interceptor";

/**
 * Widget to initialize the global authentication interceptor
 * This runs once when the admin app loads
 */
const AuthInterceptorInitializer = () => {
  useEffect(() => {
    // Initialize the global fetch interceptor
    initializeAuthInterceptor();
    
    console.log("🔒 Authentication interceptor initialized for deactivated user handling");
  }, []);

  // This widget doesn't render anything visible
  return null;
};

export const config = defineWidgetConfig({
  zone: "layout.before", // Run early in the app lifecycle
});

export default AuthInterceptorInitializer;
