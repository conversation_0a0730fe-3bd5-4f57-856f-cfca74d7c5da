import { defineWidgetConfig } from "@camped-ai/admin-sdk";
import {
  Container,
  Heading,
  Text,
  Badge,
  Skeleton,
  Alert,
} from "@camped-ai/ui";
import { useProjectLanguages } from "../hooks/languages/useProjectLanguages";
import ChangePasswordPage from "../routes/settings/change-password/page";

const InternationalizationWidget = () => {
  // Fetch languages from Tolgee
  const {
    languages: tolgeeLanguages,
    loading: languagesLoading,
    error: languagesError,
  } = useProjectLanguages();

  // Fallback mock data if Tolgee is not available
  const mockLanguages = [
    {
      id: 1488911118,
      name: "English",
      tag: "en",
      originalName: "English",
      flagEmoji: "🇬🇧",
      base: true,
    },
    {
      id: 1488911116,
      name: "German",
      tag: "de",
      originalName: "Deutsch",
      flagEmoji: "🇩🇪",
      base: false,
    },
    {
      id: 1488911117,
      name: "Japanese",
      tag: "ja",
      originalName: "日本語",
      flagEmoji: "🇯🇵",
      base: false,
    },
  ];

  // Use Tolgee data if available, otherwise use mock data
  const availableLanguagesData =
    tolgeeLanguages.length > 0 ? tolgeeLanguages : mockLanguages;

  // Handle loading state
  if (languagesLoading) {
    return (
      <Container className="p-6">
        <Skeleton className="mb-4 h-10" />
        <Skeleton className="mb-2 h-5" />
        <Skeleton className="mb-2 h-5" />
      </Container>
    );
  }

  // Handle error state
  if (languagesError) {
    return (
      <Container className="p-4">
        <Alert variant="error">
          Failed to load languages: {languagesError.message}
        </Alert>
      </Container>
    );
  }

  return (
    <Container className="divide-y p-0">
      <ChangePasswordPage />
    </Container>
  );
};

export const config = defineWidgetConfig({
  zone: "profile.details.after",
});

export default InternationalizationWidget;
