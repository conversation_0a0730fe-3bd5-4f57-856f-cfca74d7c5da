# RBAC Permission Enforcement Fixes - Test Plan

## Issues Fixed

### Issue 1: Concierge Management Bookings
**Problem**: Users with `concierge_management:view` permission saw "No bookings found" instead of existing concierge bookings
**Root Causes**:
1. Concierge bookings page was fetching from `/admin/hotel-management/bookings` instead of `/admin/concierge-management/bookings`
2. Concierge bookings API required `requireAdminOrHotelManager` instead of `concierge_management:view` permission

**Fixes Applied**:
- **File**: `src/admin/routes/concierge-management/bookings/loader.ts`
  - **Line 102**: Changed API endpoint from `/admin/hotel-management/bookings` → `/admin/concierge-management/bookings`
- **File**: `src/api/middlewares.ts`
  - **Lines 624-628**: Changed permission from `requireAdminOrHotelManager` → `requirePermission("concierge_management:view")`
  - **Lines 629-633**: Changed permission for booking details endpoint
  - **Lines 634-638**: Changed permission for booking sub-routes

### Issue 2: Hotel Management Bookings and Carts
**Problem**: Users with `bookings:view` permission couldn't see bookings data due to missing URL parameters
**Root Cause**: BookingList component required `page` and `limit` URL parameters before fetching data

**Fix Applied**:
- **File**: `src/admin/components/booking/booking-list.tsx`
  - **Lines 333-358**: Added automatic initialization of default URL parameters when missing
  - Sets default values: `page=1`, `limit=10`, `pageSize=10`, `offset=0`

### Issue 3: Supplier Management Products & Services
**Problem**: Users with only `supplier_management:view` permission saw create/import buttons instead of read-only interface
**Root Cause**: PageHeader component was passed hardcoded `hasCreate`, `hasExport`, `hasImport` props without permission checks

**Fixes Applied**:
- **File**: `src/admin/routes/supplier-management/products-services/page.tsx`
  - **Line 14**: Added `useRbac` import
  - **Line 22**: Added `hasPermission` hook usage
  - **Lines 127-133**: Updated PageHeader props with permission checks:
    - `hasCreate={hasPermission("supplier_management:create")}`
    - `hasExport={hasPermission("supplier_management:view")}`
    - `hasImport={hasPermission("supplier_management:bulk_operations")}`

## Test Cases

### Test Case 1: Concierge Management Bookings
**Setup**: Create role with `concierge_management:view` permission
**Expected Results**:
- ✅ Users should see existing concierge bookings (not "No bookings found")
- ✅ API calls to `/admin/concierge-management/bookings` should work
- ✅ Booking details and sub-routes should be accessible

**Test Steps**:
1. Create user role with `concierge_management:view` permission
2. Login as user with this role
3. Navigate to `/concierge-management/bookings`
4. Verify existing bookings are displayed
5. Click on a booking to view details
6. Verify booking details load successfully

### Test Case 2: Hotel Management Bookings
**Setup**: Create role with `bookings:view` permission
**Expected Results**:
- ✅ Users should see existing bookings immediately upon page load
- ✅ No manual URL parameter setup required
- ✅ Pagination should work correctly

**Test Steps**:
1. Create user role with `bookings:view` permission
2. Login as user with this role
3. Navigate to `/hotel-management/bookings`
4. Verify bookings load automatically without URL parameters
5. Test pagination functionality
6. Verify filtering and sorting work correctly

### Test Case 3: Hotel Management Carts
**Setup**: Create role with `carts:view` or `bookings:view` permission
**Expected Results**:
- ✅ Users should see existing carts
- ✅ Cart data should load automatically

**Test Steps**:
1. Create user role with `carts:view` or `bookings:view` permission
2. Login as user with this role
3. Navigate to `/hotel-management/carts`
4. Verify carts are displayed
5. Click on a cart to view details

### Test Case 4: Supplier Management Products & Services (View Only)
**Setup**: Create role with only `supplier_management:view` permission
**Expected Results**:
- ✅ Users should see products & services list
- ❌ Create button should NOT be visible
- ❌ Import button should NOT be visible
- ✅ Export button should be visible (requires only view permission)

**Test Steps**:
1. Create user role with only `supplier_management:view` permission
2. Login as user with this role
3. Navigate to `/supplier-management/products-services`
4. Verify products & services are displayed
5. Verify Create button is hidden
6. Verify Import button is hidden
7. Verify Export button is visible

### Test Case 5: Supplier Management Products & Services (Full Access)
**Setup**: Create role with `supplier_management:view`, `supplier_management:create`, and `supplier_management:bulk_operations` permissions
**Expected Results**:
- ✅ Users should see products & services list
- ✅ Create button should be visible
- ✅ Import button should be visible
- ✅ Export button should be visible

**Test Steps**:
1. Create user role with full supplier management permissions
2. Login as user with this role
3. Navigate to `/supplier-management/products-services`
4. Verify all buttons (Create, Import, Export) are visible
5. Test functionality of each button

## Permission Hierarchy Verification

### Concierge Management Module
- **View Permission**: `concierge_management:view`
- **Controls**: Concierge bookings access, API endpoints
- **API Endpoints**: `/admin/concierge-management/bookings/*`

### Hotel Management Bookings
- **View Permissions**: `bookings:view` OR `hotel_management:view`
- **Controls**: Hotel bookings list access
- **API Endpoints**: `/admin/hotel-management/bookings/*`

### Hotel Management Carts
- **View Permissions**: `carts:view` OR `bookings:view`
- **Controls**: Cart list and details access
- **API Endpoints**: `/admin/hotel-management/carts/*`

### Supplier Management Products & Services
- **View Permission**: `supplier_management:view`
- **Create Permission**: `supplier_management:create`
- **Bulk Operations Permission**: `supplier_management:bulk_operations`
- **Controls**: UI button visibility and functionality

## Security Impact
- **High**: Prevents unauthorized access to concierge booking data
- **Medium**: Ensures proper data loading for authorized users
- **Medium**: Prevents unauthorized creation/import operations in supplier management
- **Low**: Improves user experience by showing appropriate UI elements

## Rollback Plan
If issues arise, revert the following changes:
1. Restore `/admin/hotel-management/bookings` endpoint in concierge loader
2. Restore `requireAdminOrHotelManager` middleware for concierge bookings
3. Remove automatic URL parameter initialization in BookingList
4. Restore hardcoded PageHeader props in products & services page
