import React, { useState, useEffect } from "react";
import { Input, Label, Button, Text } from "@camped-ai/ui";

// Simulates a heavy component with complex logic
const HeavyFormComponent = () => {
  const [data, setData] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);

  // Simulate heavy data loading
  useEffect(() => {
    const timer = setTimeout(() => {
      setData([
        { id: 1, name: "Complex Field 1", value: "" },
        { id: 2, name: "Complex Field 2", value: "" },
        { id: 3, name: "Complex Field 3", value: "" },
      ]);
      setLoading(false);
    }, 1000); // Simulate 1 second load time

    return () => clearTimeout(timer);
  }, []);

  if (loading) {
    return (
      <div className="space-y-4">
        <Text>Loading complex form data...</Text>
        <div className="animate-pulse space-y-2">
          {[1, 2, 3].map((i) => (
            <div key={i} className="h-10 bg-gray-200 rounded"></div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <Text className="text-sm text-muted-foreground">
        This component simulates heavy form logic that would slow down initial page load
        if not lazy loaded.
      </Text>
      {data.map((field) => (
        <div key={field.id}>
          <Label>{field.name}</Label>
          <Input
            value={field.value}
            onChange={(e) => {
              setData(prev => prev.map(item => 
                item.id === field.id ? { ...item, value: e.target.value } : item
              ));
            }}
            placeholder={`Enter ${field.name.toLowerCase()}`}
          />
        </div>
      ))}
      <Button variant="secondary" size="small">
        Process Complex Data
      </Button>
    </div>
  );
};

export default HeavyFormComponent;
