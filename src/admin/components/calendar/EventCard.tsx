import { getCategoryColor } from "../../../utils/calendar-utils";
import { Text } from "@camped-ai/ui";

interface EventCardProps {
  offering: any;
  onClick: (offering: any) => void;
  compact?: boolean;
}

export function EventCard({
  offering,
  onClick,
  compact = false,
}: EventCardProps) {
  const colorClass = getCategoryColor(offering.product_service.category.name);

  const handleClick = () => {
    onClick(offering);
  };

  if (compact) {
    return (
      <div
        onClick={handleClick}
        className={`${colorClass} text-white rounded-lg p-2 cursor-pointer hover:shadow-md transition-shadow`}
      >
        <div className="flex items-center space-x-2">
          <span className="text-sm">
            {offering.product_service.category.icon}
          </span>
          <div className="flex-1 min-w-0">
            <Text className="text-xs font-medium truncate">
              {offering.product_service.name}
            </Text>
            <Text className="text-xs opacity-90 truncate">
              {offering.supplier.name}
            </Text>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div
      onClick={handleClick}
      className={`${colorClass} text-white rounded-lg p-3 cursor-pointer hover:shadow-md transition-shadow`}
    >
      <div className="flex items-start space-x-3">
        <span className="text-lg">
          {offering.product_service.category.icon}
        </span>
        <div className="flex-1 min-w-0">
          <Text className="text-sm font-medium mb-1 truncate">
            {offering.product_service.name}
          </Text>
          <Text className="text-xs opacity-90 mb-1 truncate">
            {offering.supplier.name}
          </Text>
          <Text className="text-xs opacity-90">
            {offering.cost} {offering.currency}
          </Text>
          {offering.custom_fields?.vehicle_type && (
            <Text className="text-xs opacity-90">
              {offering.custom_fields.vehicle_type}
              {offering.custom_fields.passenger_capacity &&
                ` • ${offering.custom_fields.passenger_capacity} passengers`}
            </Text>
          )}
        </div>
      </div>
    </div>
  );
}
