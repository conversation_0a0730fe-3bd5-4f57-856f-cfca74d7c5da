import { useState } from "react";
import { CalendarViewType } from "./calendar";
import { EventCard } from "./EventCard";
import { Text, Heading, Button } from "@camped-ai/ui";
import {
  startOfWeek,
  addDays,
  format,
  startOfMonth,
  endOfMonth,
  startOfDay,
  endOfDay,
  addMonths,
  subMonths,
} from "date-fns";

interface CalendarViewProps {
  offerings: any;
  currentDate: Date;
  viewType: CalendarViewType;
  onEventClick: (event: any) => void;
  expandedDays?: Set<string>;
  onExpandedDaysChange?: (expandedDays: Set<string>) => void;
}

export function CalendarView({
  offerings,
  currentDate,
  viewType,
  onEventClick,
  expandedDays = new Set(),
  onExpandedDaysChange,
}: CalendarViewProps) {
  const [localExpandedDays, setLocalExpandedDays] = useState<Set<string>>(
    new Set()
  );

  // Use prop expandedDays if provided, otherwise use local state
  const activeExpandedDays = onExpandedDaysChange
    ? expandedDays
    : localExpandedDays;
  const setExpandedDays = onExpandedDaysChange || setLocalExpandedDays;

  const getWeekDays = () => {
    const start = startOfWeek(currentDate, { weekStartsOn: 0 });
    return Array.from({ length: 7 }, (_, i) => addDays(start, i));
  };

  const getOfferingsForDate = (date: Date) => {
    return offerings.filter((offering) => {
      const activeFrom = new Date(offering.active_from);
      const activeTo = new Date(offering.active_to);
      const dayStart = startOfDay(date);
      const dayEnd = endOfDay(date);

      return (
        (activeFrom <= dayEnd && activeTo >= dayStart) ||
        (activeFrom <= dayStart && activeTo >= dayEnd)
      );
    });
  };

  const renderWeekView = () => {
    const weekDays = getWeekDays();

    return (
      <div className="h-full overflow-y-auto">
        {/* Calendar Header */}
        <div className="bg-card border-b border-border sticky top-0 z-10">
          <div className="grid grid-cols-8 gap-0">
            <div className="p-4 text-center">
              <Text className="text-xs text-textSecondary font-medium">
                TIME
              </Text>
            </div>
            {weekDays.map((day) => (
              <div
                key={day.toISOString()}
                className="p-4 text-center border-l border-border"
              >
                <Text className="text-xs text-textSecondary font-medium mb-1">
                  {format(day, "EEE").toUpperCase()}
                </Text>
                <Text className="text-2xl font-light text-textPrimary">
                  {format(day, "d")}
                </Text>
              </div>
            ))}
          </div>
        </div>

        {/* Calendar Grid */}
        <div className="grid grid-cols-8 gap-0 min-h-screen">
          {/* Time Column */}
          <div className="bg-surface border-r border-border">
            <div className="space-y-0">
              <div className="h-16 flex items-center justify-center border-b border-border">
                <Text className="text-xs text-textSecondary font-medium">
                  All Day
                </Text>
              </div>
              {Array.from({ length: 24 }, (_, i) => (
                <div
                  key={i}
                  className="h-16 flex items-center justify-center border-b border-border"
                >
                  <Text className="text-xs text-textSecondary">
                    {i === 0
                      ? "12 AM"
                      : i < 12
                      ? `${i} AM`
                      : i === 12
                      ? "12 PM"
                      : `${i - 12} PM`}
                  </Text>
                </div>
              ))}
            </div>
          </div>

          {/* Day Columns */}
          {weekDays.map((day) => {
            const dayOfferings = getOfferingsForDate(day);
            return (
              <div
                key={day.toISOString()}
                className="border-r border-border relative"
              >
                {/* All Day Events */}
                <div className="h-16 border-b border-border p-2 space-y-1">
                  {dayOfferings.map((offering) => (
                    <EventCard
                      key={offering.id}
                      offering={offering}
                      onClick={onEventClick}
                      compact={true}
                    />
                  ))}
                </div>

                {/* Hour Slots */}
                {Array.from({ length: 24 }, (_, i) => (
                  <div key={i} className="h-16 border-b border-border"></div>
                ))}
              </div>
            );
          })}
        </div>
      </div>
    );
  };

  const renderMonthView = () => {
    const monthStart = startOfMonth(currentDate);
    const monthEnd = endOfMonth(currentDate);
    const startDate = startOfWeek(monthStart, { weekStartsOn: 0 });
    const endDate = endOfMonth(monthEnd);

    const days = [];
    let day = startDate;
    while (day <= endDate) {
      days.push(day);
      day = addDays(day, 1);
    }

    return (
      <div className="h-full overflow-y-auto">
        {/* Month Header */}
        <div className="bg-card border-b border-border sticky top-0 z-10">
          <div className="grid grid-cols-7 gap-0">
            {["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"].map(
              (dayName) => (
                <div
                  key={dayName}
                  className="p-4 text-center border-r border-border last:border-r-0"
                >
                  <Text className="text-xs text-textSecondary font-medium">
                    {dayName.toUpperCase()}
                  </Text>
                </div>
              )
            )}
          </div>
        </div>

        {/* Month Grid */}
        <div className="grid grid-cols-7 gap-0 min-h-[calc(100vh-200px)]">
          {days.map((day) => {
            const dayOfferings = getOfferingsForDate(day);
            const isCurrentMonth = day.getMonth() === currentDate.getMonth();

            return (
              <div
                key={day.toISOString()}
                className={`border-r border-b border-border p-2 ${
                  activeExpandedDays.has(day.toISOString())
                    ? "min-h-[200px]"
                    : "min-h-[120px]"
                } ${!isCurrentMonth ? "bg-gray-50" : ""}`}
              >
                <Text
                  className={`text-sm font-medium mb-2 ${
                    isCurrentMonth ? "text-textPrimary" : "text-textSecondary"
                  }`}
                >
                  {format(day, "d")}
                </Text>
                <div className="space-y-1">
                  {(activeExpandedDays.has(day.toISOString())
                    ? dayOfferings
                    : dayOfferings.slice(0, 3)
                  ).map((offering) => (
                    <EventCard
                      key={offering.id}
                      offering={offering}
                      onClick={onEventClick}
                      compact={true}
                    />
                  ))}
                  {dayOfferings.length > 3 && (
                    <Button
                      variant="transparent"
                      size="small"
                      className="text-xs text-blue-600 hover:text-blue-800 cursor-pointer p-0 h-auto"
                      onClick={(e) => {
                        e.stopPropagation();
                        const dayKey = day.toISOString();
                        const newExpanded = new Set(activeExpandedDays);
                        if (activeExpandedDays.has(dayKey)) {
                          newExpanded.delete(dayKey);
                        } else {
                          newExpanded.add(dayKey);
                        }
                        setExpandedDays(newExpanded);
                      }}
                    >
                      {activeExpandedDays.has(day.toISOString())
                        ? `Show less`
                        : `+${dayOfferings.length - 3} more`}
                    </Button>
                  )}
                </div>
              </div>
            );
          })}
        </div>
      </div>
    );
  };

  const renderDayView = () => {
    const dayOfferings = getOfferingsForDate(currentDate);

    return (
      <div className="h-full overflow-y-auto">
        {/* Day Header */}
        <div className="bg-card border-b border-border sticky top-0 z-10">
          <div className="flex">
            <div className="w-16 flex-shrink-0 p-2 text-center border-r border-border">
              <Text className="text-xs text-textSecondary font-medium">
                TIME
              </Text>
            </div>
            <div className="flex-1 p-4 text-center">
              <Text className="text-xs text-textSecondary font-medium mb-1">
                {format(currentDate, "EEEE").toUpperCase()}
              </Text>
              <Text className="text-3xl font-light text-textPrimary">
                {format(currentDate, "d")}
              </Text>
            </div>
          </div>
        </div>

        {/* Day Grid */}
        <div className="flex min-h-screen">
          {/* Time Column - Made narrower */}
          <div className="w-16 flex-shrink-0 bg-surface border-r border-border">
            <div className="space-y-0">
              <div className="h-16 flex items-center justify-center border-b border-border px-1">
                <Text className="text-xs text-textSecondary font-medium">
                  All Day
                </Text>
              </div>
              {Array.from({ length: 24 }, (_, i) => (
                <div
                  key={i}
                  className="h-16 flex items-center justify-center border-b border-border px-1"
                >
                  <Text className="text-xs text-textSecondary transform -rotate-90 whitespace-nowrap">
                    {i === 0
                      ? "12a"
                      : i < 12
                      ? `${i}a`
                      : i === 12
                      ? "12p"
                      : `${i - 12}p`}
                  </Text>
                </div>
              ))}
            </div>
          </div>

          {/* Day Column - Maximum Width */}
          <div className="flex-1 min-w-0">
            {/* All Day Events */}
            <div className="h-16 border-b border-border p-4">
              <Text className="text-sm font-medium text-textPrimary mb-2">
                All Day Events
              </Text>
              <div className="space-y-2">
                {dayOfferings.map((offering) => (
                  <EventCard
                    key={offering.id}
                    offering={offering}
                    onClick={onEventClick}
                    compact={false}
                  />
                ))}
                {dayOfferings.length === 0 && (
                  <Text className="text-textSecondary text-sm">
                    No events scheduled for this day
                  </Text>
                )}
              </div>
            </div>

            {/* Hour Slots */}
            {Array.from({ length: 24 }, (_, i) => (
              <div
                key={i}
                className="h-16 border-b border-border flex items-center px-6"
              >
                {/* Time slot events could be displayed here */}
                <Text className="w-full text-textSecondary text-sm">
                  {/* Placeholder for timed events */}
                </Text>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  };

  const renderListView = () => {
    // Filter offerings based on the current date range for consistent date-based filtering
    const getDateFilteredOfferings = () => {
      if (viewType === "list") {
        // For list view, show offerings within a reasonable range of current date
        const startDate = startOfMonth(subMonths(currentDate, 1)); // 1 month before
        const endDate = endOfMonth(addMonths(currentDate, 2)); // 2 months after

        return offerings.filter((offering) => {
          const offeringStart = new Date(offering.active_from);
          const offeringEnd = new Date(offering.active_to);

          // Check if offering overlaps with our date range
          return offeringStart <= endDate && offeringEnd >= startDate;
        });
      }
      return offerings;
    };

    // Sort offerings by date
    const sortedOfferings = [...getDateFilteredOfferings()].sort(
      (a, b) =>
        new Date(a.active_from).getTime() - new Date(b.active_from).getTime()
    );

    return (
      <div className="h-full overflow-y-auto">
        <div className="p-6">
          <div className="flex items-center justify-between mb-4">
            <Heading
              level="h3"
              className="text-lg font-medium text-textPrimary"
            >
              All Bookings ({sortedOfferings.length})
            </Heading>
            <Text className="text-sm text-textSecondary">
              {format(startOfMonth(subMonths(currentDate, 1)), "MMM yyyy")} -{" "}
              {format(endOfMonth(addMonths(currentDate, 2)), "MMM yyyy")}
            </Text>
          </div>

          <div className="space-y-4">
            {sortedOfferings.map((offering) => (
              <div
                key={offering.id}
                className="bg-card border border-border rounded-lg p-4 hover:shadow-md transition-shadow cursor-pointer"
                onClick={() => onEventClick(offering)}
              >
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center space-x-2 mb-2">
                      <span className="text-lg">
                        {offering.product_service.category.icon}
                      </span>
                      <Text className="font-medium text-textPrimary">
                        {offering.product_service.name}
                      </Text>
                    </div>

                    <div className="text-sm text-textSecondary mb-2">
                      <Text>Supplier: {offering.supplier.name}</Text>
                      <Text>
                        Category: {offering.product_service.category.name}
                      </Text>
                    </div>

                    <div className="text-sm text-textPrimary">
                      <Text>
                        {format(new Date(offering.active_from), "MMM d, yyyy")}{" "}
                        - {format(new Date(offering.active_to), "MMM d, yyyy")}
                      </Text>
                      <Text>
                        {format(new Date(offering.active_from), "h:mm a")} -{" "}
                        {format(new Date(offering.active_to), "h:mm a")}
                      </Text>
                    </div>
                  </div>

                  <div className="text-right">
                    <Text className="text-lg font-medium text-textPrimary">
                      {offering.cost} {offering.currency}
                    </Text>
                    <div
                      className={`text-xs px-2 py-1 rounded-full ${
                        offering.status === "active"
                          ? "bg-green-100 text-green-800"
                          : offering.status === "blocked"
                          ? "bg-red-100 text-red-800"
                          : "bg-gray-100 text-gray-800"
                      }`}
                    >
                      {offering.status}
                    </div>
                  </div>
                </div>
              </div>
            ))}

            {sortedOfferings.length === 0 && (
              <div className="text-center py-12">
                <Text className="text-textSecondary text-lg">
                  No bookings found
                </Text>
                <Text className="text-textSecondary text-sm mt-2">
                  Try adjusting your filters or date range
                </Text>
              </div>
            )}
          </div>
        </div>
      </div>
    );
  };

  if (viewType === "week") {
    return renderWeekView();
  } else if (viewType === "month") {
    return renderMonthView();
  } else if (viewType === "list") {
    return renderListView();
  } else {
    return renderDayView();
  }
}
