import { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { CalendarView } from "./CalendarView";
import { FilterSidebar } from "./FilterSidebar";
import { CalendarHeader } from "./CalendarHeader";
import { EventModal } from "./EventModal";
import { Text } from "@camped-ai/ui";
import {
  addDays,
  startOfMonth,
  endOfMonth,
  startOfWeek,
  startOfDay,
  endOfDay,
} from "date-fns";

export type CalendarViewType = "day" | "week" | "month" | "list";

export interface CalendarFilters {
  search: string;
  categories: string[];
  suppliers: string[];
  vehicleTypes: string[];
}

export default function CalendarPage() {
  const [currentDate, setCurrentDate] = useState(new Date());
  const [viewType, setViewType] = useState<CalendarViewType>("week");
  const [selectedEvent, setSelectedEvent] = useState<any | null>(null);
  const [expandedDays, setExpandedDays] = useState<Set<string>>(new Set());
  const [filters, setFilters] = useState<CalendarFilters>({
    search: "",
    categories: [],
    suppliers: [],
    vehicleTypes: [],
  });

  const { data: offerings = [], isLoading } = useQuery<any[]>({
    queryKey: ["/api/supplier-offerings"],
  });

  const { data: suppliers = [] } = useQuery({
    queryKey: ["/api/suppliers"],
  });

  const { data: categories = [] } = useQuery({
    queryKey: ["/api/categories"],
  });

  const filteredOfferings = offerings.filter((offering) => {
    // Search filter
    if (
      filters.search &&
      !offering.product_service.name
        .toLowerCase()
        .includes(filters.search.toLowerCase()) &&
      !offering.supplier.name
        .toLowerCase()
        .includes(filters.search.toLowerCase())
    ) {
      return false;
    }

    // Category filter
    if (
      filters.categories.length > 0 &&
      !filters.categories.includes(offering.product_service.category_id!)
    ) {
      return false;
    }

    // Supplier filter
    if (
      filters.suppliers.length > 0 &&
      !filters.suppliers.includes(offering.supplier_id!)
    ) {
      return false;
    }

    // Vehicle type filter
    if (filters.vehicleTypes.length > 0) {
      const vehicleType =
        offering.custom_fields?.vehicle_type ||
        offering.product_service.custom_fields?.vehicle_type;
      if (!vehicleType || !filters.vehicleTypes.includes(vehicleType)) {
        return false;
      }
    }

    return true;
  });

  const handlePreviousDate = () => {
    const newDate = new Date(currentDate);
    if (viewType === "day") {
      newDate.setDate(newDate.getDate() - 1);
    } else if (viewType === "week") {
      newDate.setDate(newDate.getDate() - 7);
    } else if (viewType === "month") {
      newDate.setMonth(newDate.getMonth() - 1);
    }
    setCurrentDate(newDate);
  };

  const handleNextDate = () => {
    const newDate = new Date(currentDate);
    if (viewType === "day") {
      newDate.setDate(newDate.getDate() + 1);
    } else if (viewType === "week") {
      newDate.setDate(newDate.getDate() + 7);
    } else if (viewType === "month") {
      newDate.setMonth(newDate.getMonth() + 1);
    }
    setCurrentDate(newDate);
  };

  const handleToday = () => {
    setCurrentDate(new Date());
  };

  const handleShowAll = () => {
    // Find all days with more than 3 events and expand them
    const monthStart = startOfMonth(currentDate);
    const monthEnd = endOfMonth(currentDate);
    const startDate = startOfWeek(monthStart, { weekStartsOn: 0 });
    const endDate = endOfMonth(monthEnd);

    const newExpanded = new Set<string>();
    let day = startDate;
    while (day <= endDate) {
      const dayOfferings = filteredOfferings.filter((offering) => {
        const activeFrom = new Date(offering.active_from);
        const activeTo = new Date(offering.active_to);
        const dayStart = startOfDay(day);
        const dayEnd = endOfDay(day);

        return (
          (activeFrom <= dayEnd && activeTo >= dayStart) ||
          (activeFrom <= dayStart && activeTo >= dayEnd)
        );
      });

      if (dayOfferings.length > 3) {
        newExpanded.add(day.toISOString());
      }

      day = addDays(day, 1);
    }

    setExpandedDays(newExpanded);
  };

  const handleCollapseAll = () => {
    setExpandedDays(new Set());
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Text className="text-lg">Loading calendar...</Text>
      </div>
    );
  }

  return (
    <div className="h-screen bg-surface">
      <CalendarHeader
        currentDate={currentDate}
        viewType={viewType}
        onViewTypeChange={setViewType}
        onPreviousDate={handlePreviousDate}
        onNextDate={handleNextDate}
        onToday={handleToday}
        onShowAll={handleShowAll}
        onCollapseAll={handleCollapseAll}
        hasExpandedDays={expandedDays.size > 0}
      />

      <div className="flex h-[calc(100vh-80px)]">
        <FilterSidebar
          filters={filters}
          onFiltersChange={setFilters}
          suppliers={suppliers}
          categories={categories}
          offeringsCount={filteredOfferings.length}
        />

        <CalendarView
          offerings={filteredOfferings}
          currentDate={currentDate}
          viewType={viewType}
          onEventClick={setSelectedEvent}
          expandedDays={expandedDays}
          onExpandedDaysChange={setExpandedDays}
        />
      </div>

      <EventModal
        event={selectedEvent}
        onClose={() => setSelectedEvent(null)}
      />
    </div>
  );
}
