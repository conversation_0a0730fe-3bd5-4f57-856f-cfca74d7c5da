import { But<PERSON>, Text, Heading } from "@camped-ai/ui";
import { ChevronLeft, ChevronRight, Plus } from "lucide-react";
import { CalendarViewType } from "./calendar";
import { format } from "date-fns";

interface CalendarHeaderProps {
  currentDate: Date;
  viewType: CalendarViewType;
  onViewTypeChange: (view: CalendarViewType) => void;
  onPreviousDate: () => void;
  onNextDate: () => void;
  onToday: () => void;
  onShowAll?: () => void;
  onCollapseAll?: () => void;
  hasExpandedDays?: boolean;
}

export function CalendarHeader({
  currentDate,
  viewType,
  onViewTypeChange,
  onPreviousDate,
  onNextDate,
  onToday,
  onShowAll,
  onCollapseAll,
  hasExpandedDays,
}: CalendarHeaderProps) {
  const formatDateTitle = () => {
    if (viewType === "month") {
      return format(currentDate, "MMMM yyyy");
    } else if (viewType === "week") {
      return format(currentDate, "MMMM yyyy");
    } else {
      return format(currentDate, "MMMM d, yyyy");
    }
  };

  return (
    <header className="bg-card shadow-sm border-b border-border">
      <div className="px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Heading
              level="h1"
              className="text-2xl font-medium text-textPrimary"
            >
              Booking Management
            </Heading>
            <div className="flex items-center space-x-2 ml-8">
              <Button
                variant="primary"
                className="bg-primary hover:bg-primary/90"
              >
                <Plus className="w-4 h-4 mr-2" />
                New Booking
              </Button>
            </div>
          </div>

          <div className="flex items-center space-x-4">
            {/* View Toggle */}
            <div className="flex bg-gray-100 rounded-lg p-1">
              <Button
                variant={viewType === "day" ? "primary" : "transparent"}
                size="small"
                onClick={() => onViewTypeChange("day")}
                className={
                  viewType === "day"
                    ? "bg-white shadow-sm text-black"
                    : "text-gray-700 hover:text-black"
                }
              >
                Day
              </Button>
              <Button
                variant={viewType === "week" ? "primary" : "transparent"}
                size="small"
                onClick={() => onViewTypeChange("week")}
                className={
                  viewType === "week"
                    ? "bg-white shadow-sm text-black"
                    : "text-gray-700 hover:text-black"
                }
              >
                Week
              </Button>
              <Button
                variant={viewType === "month" ? "primary" : "transparent"}
                size="small"
                onClick={() => onViewTypeChange("month")}
                className={
                  viewType === "month"
                    ? "bg-white shadow-sm text-black"
                    : "text-gray-700 hover:text-black"
                }
              >
                Month
              </Button>
              <Button
                variant={viewType === "list" ? "primary" : "transparent"}
                size="small"
                onClick={() => onViewTypeChange("list")}
                className={
                  viewType === "list"
                    ? "bg-white shadow-sm text-black"
                    : "text-gray-700 hover:text-black"
                }
              >
                List
              </Button>
            </div>

            {/* Date Navigation */}
            <div className="flex items-center space-x-2">
              <Button
                variant="transparent"
                size="small"
                onClick={onPreviousDate}
                className="p-2 hover:bg-gray-100"
              >
                <ChevronLeft className="w-4 h-4" />
              </Button>
              <Text className="text-lg font-medium text-textPrimary px-4">
                {formatDateTitle()}
              </Text>
              <Button
                variant="transparent"
                size="small"
                onClick={onNextDate}
                className="p-2 hover:bg-gray-100"
              >
                <ChevronRight className="w-4 h-4" />
              </Button>
              <Button
                variant="secondary"
                size="small"
                onClick={onToday}
                className="ml-4"
              >
                Today
              </Button>

              {/* Show All / Collapse All for Month View */}
              {viewType === "month" && (onShowAll || onCollapseAll) && (
                <Button
                  variant="secondary"
                  size="small"
                  onClick={hasExpandedDays ? onCollapseAll : onShowAll}
                  className="ml-2"
                >
                  {hasExpandedDays ? "Collapse All" : "Show All"}
                </Button>
              )}
            </div>
          </div>
        </div>
      </div>
    </header>
  );
}
