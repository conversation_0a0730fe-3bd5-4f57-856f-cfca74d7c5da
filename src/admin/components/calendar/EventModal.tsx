import { FocusModal, <PERSON><PERSON>, <PERSON>ge, <PERSON>, Heading } from "@camped-ai/ui";
import { format } from "date-fns";
import { Edit, Copy, Trash2, Calendar } from "lucide-react";

interface EventModalProps {
  event: any | null;
  onClose: () => void;
}

export function EventModal({ event, onClose }: EventModalProps) {
  if (!event) return null;

  const formatDateRange = () => {
    const from = format(new Date(event.active_from), "MMMM d, yyyy");
    const to = format(new Date(event.active_to), "MMMM d, yyyy");
    return `${from} - ${to}`;
  };

  const renderCustomFields = () => {
    const customFields = { ...event.custom_fields };

    return Object.entries(customFields).map(([key, value]) => (
      <div key={key} className="flex justify-between items-center">
        <Text className="text-sm text-textSecondary capitalize">
          {key.replace(/([A-Z])/g, " $1").trim()}:
        </Text>
        <Text className="text-sm text-textPrimary font-medium">
          {typeof value === "boolean" ? (value ? "Yes" : "No") : String(value)}
        </Text>
      </div>
    ));
  };

  return (
    <FocusModal>
      <FocusModal.Content className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <FocusModal.Header>
          <div className="flex items-center space-x-2">
            <span className="text-xl">
              {event.product_service.category.icon}
            </span>
            <Heading level="h2">{event.product_service.name}</Heading>
          </div>
        </FocusModal.Header>

        <div className="space-y-6">
          {/* Event Overview */}
          <div className="bg-surface rounded-lg p-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Text className="block text-sm font-medium text-textSecondary mb-1">
                  Supplier
                </Text>
                <Text className="text-sm text-textPrimary">
                  {event.supplier.name}
                </Text>
              </div>
              <div>
                <Text className="block text-sm font-medium text-textSecondary mb-1">
                  Category
                </Text>
                <Text className="text-sm text-textPrimary">
                  {event.product_service.category.name}
                </Text>
              </div>
              <div>
                <Text className="block text-sm font-medium text-textSecondary mb-1">
                  Cost
                </Text>
                <Text className="text-sm text-textPrimary font-medium">
                  {event.cost} {event.currency}
                </Text>
              </div>
              <div>
                <Text className="block text-sm font-medium text-textSecondary mb-1">
                  Status
                </Text>
                <Badge className="bg-green-100 text-green-800">
                  {event.status}
                </Badge>
              </div>
            </div>
          </div>

          {/* Date Range */}
          <div>
            <Text className="block text-sm font-medium text-textSecondary mb-2">
              Availability Period
            </Text>
            <div className="bg-surface rounded-lg p-3">
              <div className="flex items-center space-x-2">
                <Calendar className="w-4 h-4 text-textSecondary" />
                <Text className="text-sm text-textPrimary">
                  {formatDateRange()}
                </Text>
              </div>
            </div>
          </div>

          {/* Custom Fields */}
          {Object.keys(event.custom_fields || {}).length > 0 && (
            <div>
              <Text className="block text-sm font-medium text-textSecondary mb-2">
                Details
              </Text>
              <div className="bg-surface rounded-lg p-4">
                <div className="space-y-3">{renderCustomFields()}</div>
              </div>
            </div>
          )}

          {/* Actions */}
          <div className="grid grid-cols-2 gap-4">
            <Button
              variant="secondary"
              className="flex items-center justify-center"
            >
              <Copy className="w-4 h-4 mr-2" />
              Duplicate
            </Button>
            <Button
              variant="danger"
              className="flex items-center justify-center"
            >
              <Trash2 className="w-4 h-4 mr-2" />
              Delete
            </Button>
          </div>
        </div>

        {/* Footer */}
        <div className="flex items-center justify-end space-x-3 pt-4 border-t border-border">
          <Button variant="secondary" onClick={onClose}>
            Cancel
          </Button>
          <Button variant="primary" className="bg-primary hover:bg-primary/90">
            <Edit className="w-4 h-4 mr-2" />
            Edit Booking
          </Button>
        </div>
      </FocusModal.Content>
    </FocusModal>
  );
}
