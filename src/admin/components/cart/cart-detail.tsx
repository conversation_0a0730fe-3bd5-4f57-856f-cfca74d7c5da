import { useState, useEffect } from "react";
import {
  But<PERSON>,
  <PERSON>ing,
  Text,
  Tabs,
  Toaster,
  toast,
  FocusModal,
} from "@camped-ai/ui";
import { format } from "date-fns";
import { useNavigate } from "react-router-dom";
import Spinner from "../shared/spinner";
import { useRbac } from "../../hooks/use-rbac";

type CartDetailProps = {
  cartId: string;
  isInSidebar?: boolean;
};

const CartDetail = ({ cartId, isInSidebar = false }: CartDetailProps) => {
  const navigate = useNavigate();
  const { hasPermission } = useRbac();
  const [cart, setCart] = useState<any>(null);
  const [hotelDetails, setHotelDetails] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isCompletingCart, setIsCompletingCart] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);

  // Fetch cart details
  const fetchCartDetails = async () => {
    if (!cartId) {
      setIsLoading(false);
      return;
    }

    try {
      setIsLoading(true);

      const response = await fetch(`/admin/hotel-management/carts/${cartId}`);

      if (!response.ok) {
        throw new Error(
          `Failed to fetch cart details: ${response.status} ${response.statusText}`
        );
      }

      const data = await response.json();

      if (!data.cart) {
        throw new Error("No cart data in response");
      }

      // Process cart data
      const processedCart = { ...data.cart };

      // Parse metadata if it's a string
      if (typeof processedCart.metadata === "string") {
        try {
          processedCart.metadata = JSON.parse(processedCart.metadata);
        } catch (e) {
          console.error("Failed to parse metadata string:", e);
        }
      }

      setCart(processedCart);

      // Fetch hotel details if hotel_id is available (check both cart and line item metadata)
      const hotelId = processedCart.hotel_id ||
                     processedCart.line_items?.[0]?.metadata?.hotel_id;

      if (hotelId) {
        fetchHotelDetails(hotelId);
      }
    } catch (error) {
      console.error("Error fetching cart details:", error);
      toast.error("Failed to fetch cart details");
    } finally {
      setIsLoading(false);
    }
  };

  // Fetch hotel details
  const fetchHotelDetails = async (hotelId: string) => {
    try {
      const response = await fetch(`/admin/hotel-management/hotels/${hotelId}`);

      if (!response.ok) {
        throw new Error("Failed to fetch hotel details");
      }

      const data = await response.json();
      setHotelDetails(data.hotel);
    } catch (error) {
      console.error("Error fetching hotel details:", error);
    }
  };

  // Delete cart
  const handleDeleteCart = async () => {
    try {
      const response = await fetch(`/admin/hotel-management/carts/${cartId}`, {
        method: "DELETE",
      });

      if (!response.ok) {
        throw new Error("Failed to delete cart");
      }

      toast.success("Cart successfully deleted");
      navigate("/hotel-management/carts");
    } catch (error) {
      console.error("Error deleting cart:", error);
      toast.error("Failed to delete cart");
    } finally {
      setIsDeleteDialogOpen(false);
    }
  };

  // Format date
  const formatDate = (dateString: string | undefined): string => {
    if (!dateString) return "Not specified";
    try {
      return format(new Date(dateString), "MMM dd, yyyy");
    } catch (error) {
      return "Invalid date";
    }
  };

  // Go back to carts list
  const handleGoBack = () => {
    navigate("/hotel-management/carts");
  };

  // Helper function to extract hotel and booking information from line items
  const extractDetailsFromLineItems = () => {
    const lineItems = cart?.line_items || [];
    if (lineItems.length === 0) return {};

    // Get the first item (assuming it contains the hotel booking information)
    const firstItem = lineItems[0];
    const itemMetadata = firstItem?.metadata || {};

    // Calculate total number of rooms based on line items
    const totalRooms = lineItems.length;

    // Calculate total guests - sum up quantities or use metadata
    const totalGuests = lineItems.reduce((total: number, item: any) => {
      const guestsFromMetadata = item?.metadata?.number_of_guests || 1;
      const quantity = item?.quantity || 1;
      return total + Math.max(guestsFromMetadata, quantity);
    }, 0);

    return {
      hotel_id: itemMetadata.hotel_id,
      hotel_name: itemMetadata.hotel_name,
      room_config_name: itemMetadata.room_config_name,
      room_type: itemMetadata.room_config_name || itemMetadata.room_name || itemMetadata.room_type,
      check_in_date: itemMetadata.check_in_date,
      check_out_date: itemMetadata.check_out_date,
      check_in_time: itemMetadata.check_in_time,
      check_out_time: itemMetadata.check_out_time,
      guest_name: itemMetadata.guest_name,
      guest_email: itemMetadata.guest_email,
      guest_phone: itemMetadata.guest_phone,
      number_of_guests: totalGuests || itemMetadata.number_of_guests || cart?.number_of_guests || 1,
      number_of_rooms: totalRooms || itemMetadata.number_of_rooms || cart?.number_of_rooms || 1,
      special_requests: itemMetadata.special_requests,
    };
  };

  // Extract details from line items
  const extractedDetails = cart ? extractDetailsFromLineItems() : {};

  // Debug logging to help troubleshoot
  console.log("🛒 Cart Debug Info:");
  console.log("Cart data:", cart);
  console.log("Line items:", cart?.line_items);
  console.log("Line items metadata:", cart?.line_items?.map((item: any, index: number) => ({
    index: index + 1,
    id: item.id,
    title: item.title,
    metadata: item.metadata,
    // Extract key metadata fields for easier viewing
    hotel_name: item.metadata?.hotel_name,
    room_config_name: item.metadata?.room_config_name,
    guest_name: item.metadata?.guest_name,
    guest_email: item.metadata?.guest_email,
  })));
  console.log("Extracted details:", extractedDetails);

  // Fetch cart details on component mount
  useEffect(() => {
    fetchCartDetails();
  }, [cartId]);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-8">
        <Spinner size="medium" />
        <div className="ml-4 text-muted-foreground">
          Loading cart details...
        </div>
      </div>
    );
  }

  if (!cart) {
    return (
      <div className="text-center py-8">
        <Heading>Cart Not Found</Heading>
        <Text className="mt-2">
          The cart you're looking for doesn't exist or has been removed.
        </Text>
        <Button className="mt-4" onClick={handleGoBack}>
          Back to Carts
        </Button>
      </div>
    );
  }

  return (
    <div className={`space-y-6 ${isInSidebar ? "p-4" : ""}`}>
      <Toaster />

      {/* Header */}
      <div className="flex justify-between items-center mb-4">
        <div>
          <Button variant="secondary" onClick={handleGoBack} className="mb-2">
            ← Back to Carts
          </Button>
          <div className="flex items-center gap-3">
            <Heading className="text-2xl">
              Cart #{cart.id?.substring(0, 8)}
            </Heading>
          </div>
        </div>
        <div className="flex gap-2">
          {hasPermission("bookings:delete") && (
            <Button
              variant="secondary"
              color="red"
              onClick={() => setIsDeleteDialogOpen(true)}
            >
              Delete Cart
            </Button>
          )}
        </div>
      </div>

      {/* Tabs */}
      <Tabs defaultValue="details">
        <Tabs.List>
          <Tabs.Trigger value="details">Details</Tabs.Trigger>
          <Tabs.Trigger value="items">Items</Tabs.Trigger>
          <Tabs.Trigger value="customer">Customer</Tabs.Trigger>
          {/* Payment tab removed as payment_sessions are not available */}
        </Tabs.List>

        {/* Details Tab */}
        <Tabs.Content value="details" className="pt-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Left Column */}
            <div className="space-y-6">
              {/* Hotel Information */}
              <div className="bg-card p-6 rounded-lg shadow-sm border border-border">
                <Heading className="text-lg mb-4">Hotel Information</Heading>
                <div className="space-y-3">
                  <div>
                    <Text className="text-sm text-muted-foreground">
                      Hotel Name
                    </Text>
                    <Text className="font-medium">
                      {extractedDetails.hotel_name || cart.hotel_name || hotelDetails?.name || "Unknown Hotel"}
                    </Text>
                  </div>
                  {hotelDetails && (
                    <>
                      <div>
                        <Text className="text-sm text-muted-foreground">
                          Location
                        </Text>
                        <Text className="font-medium">
                          {hotelDetails.location || "Not specified"}
                        </Text>
                      </div>
                      <div>
                        <Text className="text-sm text-muted-foreground">
                          Contact
                        </Text>
                        <Text className="font-medium">
                          {hotelDetails.phone_number || "Not specified"}
                        </Text>
                      </div>
                    </>
                  )}
                </div>
              </div>

              {/* Booking Details */}
              <div className="bg-card p-6 rounded-lg shadow-sm border border-border">
                <Heading className="text-lg mb-4">Booking Details</Heading>
                <div className="space-y-3">
                  <div>
                    <Text className="text-sm text-muted-foreground">
                      Room Type
                    </Text>
                    <Text className="font-medium">
                      {extractedDetails.room_type || cart.room_type || "Junior Suite"}
                    </Text>
                  </div>
                  <div>
                    <Text className="text-sm text-muted-foreground">
                      Check-in Date
                    </Text>
                    <Text className="font-medium">
                      {formatDate(extractedDetails.check_in_date || cart.check_in_date)}{" "}
                      {extractedDetails.check_in_time || cart.check_in_time || "12:00"}
                    </Text>
                  </div>
                  <div>
                    <Text className="text-sm text-muted-foreground">
                      Check-out Date
                    </Text>
                    <Text className="font-medium">
                      {formatDate(extractedDetails.check_out_date || cart.check_out_date)}{" "}
                      {extractedDetails.check_out_time || cart.check_out_time || "12:00"}
                    </Text>
                  </div>
                  <div>
                    <Text className="text-sm text-muted-foreground">
                      Number of Rooms
                    </Text>
                    <Text className="font-medium">
                      {extractedDetails.number_of_rooms}
                    </Text>
                  </div>
                  <div>
                    <Text className="text-sm text-muted-foreground">
                      Number of Guests
                    </Text>
                    <Text className="font-medium">
                      {extractedDetails.number_of_guests}
                    </Text>
                  </div>
                  <div>
                    <Text className="text-sm text-muted-foreground">
                      Special Requests
                    </Text>
                    <Text className="font-medium">
                      {extractedDetails.special_requests || cart.special_requests || "None"}
                    </Text>
                  </div>
                </div>
              </div>
            </div>

            {/* Right Column */}
            <div className="space-y-6">
              {/* Guest Information */}
              <div className="bg-card p-6 rounded-lg shadow-sm border border-border">
                <Heading className="text-lg mb-4">Guest Information</Heading>
                <div className="space-y-3">
                  <div>
                    <Text className="text-sm text-muted-foreground">
                      Guest Name
                    </Text>
                    <Text className="font-medium">
                      {extractedDetails.guest_name || cart.guest_name || "Guest"}
                    </Text>
                  </div>
                  <div>
                    <Text className="text-sm text-muted-foreground">Email</Text>
                    <Text className="font-medium">
                      {extractedDetails.guest_email || cart.guest_email || "No email provided"}
                    </Text>
                  </div>
                  <div>
                    <Text className="text-sm text-muted-foreground">Phone</Text>
                    <Text className="font-medium">
                      {extractedDetails.guest_phone || cart.guest_phone || "No phone provided"}
                    </Text>
                  </div>
                </div>
              </div>

              {/* Payment Information */}
              <div className="bg-card p-6 rounded-lg shadow-sm border border-border">
                <Heading className="text-lg mb-4">Payment Information</Heading>
                <div className="space-y-3">
                  <div>
                    <Text className="text-sm text-muted-foreground">
                      Total Amount
                    </Text>
                    <Text className="font-medium text-xl">
                      {new Intl.NumberFormat("en-US", {
                        style: "currency",
                        currency: cart.currency_code || "USD",
                      }).format(cart.total_amount || 0)}
                    </Text>
                  </div>
                  <div>
                    <Text className="text-sm text-muted-foreground">
                      Created At
                    </Text>
                    <Text className="font-medium">
                      {formatDate(cart.created_at)}
                    </Text>
                  </div>
                  <div>
                    <Text className="text-sm text-muted-foreground">
                      Updated At
                    </Text>
                    <Text className="font-medium">
                      {formatDate(cart.updated_at)}
                    </Text>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </Tabs.Content>

        {/* Items Tab */}
        <Tabs.Content value="items" className="pt-4">
          <div className="bg-card p-6 rounded-lg shadow-sm border border-border">
            <Heading className="text-lg mb-4">Cart Items</Heading>
            {cart.line_items && cart.line_items.length > 0 ? (
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-border">
                  <thead>
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">
                        Item
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">
                        Quantity
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">
                        Unit Price
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">
                        Total
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-background divide-y divide-border">
                    {cart.line_items.map((item: any) => (
                      <tr key={item.id}>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm font-medium text-foreground">
                            {item.title}
                          </div>
                          {item.metadata && item.metadata.room_type && (
                            <div className="text-sm text-muted-foreground">
                              {item.metadata.room_type}
                            </div>
                          )}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-muted-foreground">
                          {item.quantity}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-muted-foreground">
                          {new Intl.NumberFormat("en-US", {
                            style: "currency",
                            currency: cart.currency_code || "USD",
                          }).format(item.unit_price / 100)}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-muted-foreground">
                          {new Intl.NumberFormat("en-US", {
                            style: "currency",
                            currency: cart.currency_code || "USD",
                          }).format((item.unit_price * item.quantity) / 100)}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            ) : (
              <Text className="text-muted-foreground">No items in cart</Text>
            )}
          </div>
        </Tabs.Content>

        {/* Customer Tab */}
        <Tabs.Content value="customer" className="pt-4">
          <div className="bg-card p-6 rounded-lg shadow-sm border border-border">
            <Heading className="text-lg mb-4">Customer Information</Heading>
            {(extractedDetails.guest_name && extractedDetails.guest_name !== "Guest") ||
             (extractedDetails.guest_email && extractedDetails.guest_email !== "No email provided") ||
             (extractedDetails.guest_phone && extractedDetails.guest_phone !== "No phone provided") ||
             cart.customer ? (
              <div className="space-y-4">
                {cart.customer && (
                  <div>
                    <Text className="text-sm text-muted-foreground">
                      Customer ID
                    </Text>
                    <Text className="font-medium">{cart.customer.id}</Text>
                  </div>
                )}
                <div>
                  <Text className="text-sm text-muted-foreground">Name</Text>
                  <Text className="font-medium">
                    {cart.customer ?
                      `${cart.customer.first_name} ${cart.customer.last_name}` :
                      extractedDetails.guest_name || "Not specified"}
                  </Text>
                </div>
                <div>
                  <Text className="text-sm text-muted-foreground">Email</Text>
                  <Text className="font-medium">
                    {cart.customer?.email || extractedDetails.guest_email || "Not specified"}
                  </Text>
                </div>
                <div>
                  <Text className="text-sm text-muted-foreground">Phone</Text>
                  <Text className="font-medium">
                    {cart.customer?.phone || extractedDetails.guest_phone || "Not specified"}
                  </Text>
                </div>

                {/* Shipping Address */}
                {cart.shipping_address && (
                  <div className="mt-6">
                    <Heading className="text-md mb-2">Shipping Address</Heading>
                    <div className="space-y-1">
                      <Text>
                        {cart.shipping_address.address_1}
                        {cart.shipping_address.address_2 &&
                          `, ${cart.shipping_address.address_2}`}
                      </Text>
                      <Text>
                        {cart.shipping_address.city}
                        {cart.shipping_address.province &&
                          `, ${cart.shipping_address.province}`}
                        {cart.shipping_address.postal_code &&
                          ` ${cart.shipping_address.postal_code}`}
                      </Text>
                      <Text>
                        {cart.shipping_address.country_code?.toUpperCase()}
                      </Text>
                    </div>
                  </div>
                )}

                {/* Billing Address */}
                {cart.billing_address && (
                  <div className="mt-6">
                    <Heading className="text-md mb-2">Billing Address</Heading>
                    <div className="space-y-1">
                      <Text>
                        {cart.billing_address.address_1}
                        {cart.billing_address.address_2 &&
                          `, ${cart.billing_address.address_2}`}
                      </Text>
                      <Text>
                        {cart.billing_address.city}
                        {cart.billing_address.province &&
                          `, ${cart.billing_address.province}`}
                        {cart.billing_address.postal_code &&
                          ` ${cart.billing_address.postal_code}`}
                      </Text>
                      <Text>
                        {cart.billing_address.country_code?.toUpperCase()}
                      </Text>
                    </div>
                  </div>
                )}
              </div>
            ) : (
              <div className="space-y-4">
                <Text className="text-muted-foreground mb-4">
                  No registered customer found. Showing guest information from booking:
                </Text>
                <div>
                  <Text className="text-sm text-muted-foreground">Guest Name</Text>
                  <Text className="font-medium">
                    {extractedDetails.guest_name || "Not specified"}
                  </Text>
                </div>
                <div>
                  <Text className="text-sm text-muted-foreground">Email</Text>
                  <Text className="font-medium">
                    {extractedDetails.guest_email || "Not specified"}
                  </Text>
                </div>
                <div>
                  <Text className="text-sm text-muted-foreground">Phone</Text>
                  <Text className="font-medium">
                    {extractedDetails.guest_phone || "Not specified"}
                  </Text>
                </div>
              </div>
            )}
          </div>
        </Tabs.Content>

        {/* Payment Tab removed as payment_sessions are not available */}
      </Tabs>

      {/* Delete Confirmation Dialog */}
      <FocusModal
        open={isDeleteDialogOpen}
        onOpenChange={setIsDeleteDialogOpen}
      >
        <FocusModal.Content>
          <FocusModal.Header>
            <Heading>Delete Cart</Heading>
          </FocusModal.Header>
          <FocusModal.Body className="py-4">
            <Text>
              Are you sure you want to delete this cart? This action cannot be
              undone.
            </Text>
          </FocusModal.Body>
          <FocusModal.Footer>
            <div className="flex justify-end gap-2">
              <Button
                variant="secondary"
                onClick={() => setIsDeleteDialogOpen(false)}
              >
                Cancel
              </Button>
              <Button variant="primary" color="red" onClick={handleDeleteCart}>
                Delete
              </Button>
            </div>
          </FocusModal.Footer>
        </FocusModal.Content>
      </FocusModal>
    </div>
  );
};

export default CartDetail;
