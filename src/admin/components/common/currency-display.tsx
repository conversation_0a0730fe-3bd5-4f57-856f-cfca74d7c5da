import React from "react";
import { useAdminCurrency } from "../../hooks/use-admin-currencies";
import { formatCurrencyAmount, getCurrencySymbol } from "../../utils/currency-utils";
import { getCurrencyInputDisplayValue, createCurrencyInputHandler } from "../../utils/currency-helpers";

interface CurrencyDisplayProps {
  amount: number;
  currencyCode: string;
  className?: string;
  showSymbol?: boolean;
  showCode?: boolean;
  symbolPosition?: "before" | "after";
  fallbackSymbol?: string;
}

export const CurrencyDisplay: React.FC<CurrencyDisplayProps> = ({
  amount,
  currencyCode,
  className = "",
  showSymbol = true,
  showCode = false,
  symbolPosition = "before",
  fallbackSymbol,
}) => {
  const { currency, isLoading } = useAdminCurrency(currencyCode);

  if (isLoading) {
    return (
      <span className={`inline-block bg-gray-200 rounded animate-pulse ${className}`}>
        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
      </span>
    );
  }

  // Use currency from hook if available, otherwise create a fallback
  const displayCurrency = currency || {
    currency_code: currencyCode.toUpperCase(),
    symbol: fallbackSymbol || getCurrencySymbol(currencyCode),
    decimal_digits: 2,
    name: currencyCode.toUpperCase(),
    is_default: false,
  };

  const formattedAmount = formatCurrencyAmount(amount, displayCurrency, {
    showSymbol,
    showCode,
    symbolPosition,
  });

  return <span className={className}>{formattedAmount}</span>;
};

interface CurrencyInputProps {
  value: number; // Value in smallest currency unit (e.g., cents)
  onChange: (value: number) => void; // Callback receives value in smallest currency unit
  currencyCode: string;
  className?: string;
  placeholder?: string;
  disabled?: boolean;
  min?: number; // Min value in smallest currency unit
  max?: number; // Max value in smallest currency unit
  step?: number;
}

export const CurrencyInput: React.FC<CurrencyInputProps> = ({
  value,
  onChange,
  currencyCode,
  className = "",
  placeholder = "0.00",
  disabled = false,
  min,
  max,
  step,
}) => {
  const { currency } = useAdminCurrency(currencyCode);

  const decimalPlaces = currency?.decimal_digits || 2;

  // Use the standardized currency helpers
  const displayValue = getCurrencyInputDisplayValue(value, currencyCode);
  const handleChange = createCurrencyInputHandler(currencyCode, onChange);

  const stepValue = step || 1 / Math.pow(10, decimalPlaces);

  return (
    <input
      type="number"
      value={displayValue}
      onChange={handleChange}
      className={`border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${className}`}
      placeholder={placeholder}
      disabled={disabled}
      min={min ? min / Math.pow(10, decimalPlaces) : undefined}
      max={max ? max / Math.pow(10, decimalPlaces) : undefined}
      step={stepValue}
    />
  );
};
