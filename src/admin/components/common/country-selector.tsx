import { Combobox } from "./combobox";
import { useState, useMemo } from "react";
import { countries } from "../../utils/constants";

interface CountrySelectorProps {
  value?: string;
  onChange?: (value?: string) => void;
  placeholder?: string;
  allowClear?: boolean;
  disabled?: boolean;
  className?: string;
}

export const CountrySelector = ({
  value,
  onChange,
  placeholder = "Search countries...",
  allowClear = true,
  disabled = false,
  className,
}: CountrySelectorProps) => {
  const [searchValue, setSearchValue] = useState("");

  // Convert countries to combobox options - use label as value to store country name
  const options = useMemo(() => {
    return countries.map((country) => ({
      value: country.label, // Store the country name instead of code
      label: country.label,
    }));
  }, []);

  // Filter countries based on search - search by name and country code
  const filteredOptions = useMemo(() => {
    if (!searchValue) return options;

    return options.filter((country) => {
      const searchLower = searchValue.toLowerCase();
      const countryData = countries.find((c) => c.label === country.label);

      return (
        country.label.toLowerCase().includes(searchLower) ||
        (countryData && countryData.value.toLowerCase().includes(searchLower))
      );
    });
  }, [options, searchValue]);

  return (
    <Combobox
      value={value}
      onChange={onChange}
      searchValue={searchValue}
      onSearchValueChange={setSearchValue}
      options={filteredOptions}
      placeholder={placeholder}
      allowClear={allowClear}
      disabled={disabled}
      className={className}
      noResultsPlaceholder={
        <div className="flex items-center gap-x-2 rounded-[4px] px-2 py-1.5">
          <span className="text-ui-fg-subtle text-sm">
            No countries found matching "{searchValue}"
          </span>
        </div>
      }
    />
  );
};

// Multi-select version for cases where multiple countries might be needed
interface MultiCountrySelectorProps {
  value?: string[];
  onChange?: (value?: string[]) => void;
  placeholder?: string;
  allowClear?: boolean;
  disabled?: boolean;
  className?: string;
}

export const MultiCountrySelector = ({
  value,
  onChange,
  placeholder = "Search and select countries...",
  allowClear = true,
  disabled = false,
  className,
}: MultiCountrySelectorProps) => {
  const [searchValue, setSearchValue] = useState("");

  const options = useMemo(() => {
    return countries.map((country) => ({
      value: country.label, // Store the country name instead of code
      label: country.label,
    }));
  }, []);

  return (
    <Combobox<string[]>

      value={value}
      onChange={onChange}
      searchValue={searchValue}
      onSearchValueChange={setSearchValue}
      options={options}
      placeholder={placeholder}
      allowClear={allowClear}
      disabled={disabled}
      className={className}
      noResultsPlaceholder={
        <div className="flex items-center gap-x-2 rounded-[4px] px-2 py-1.5">
          <span className="text-ui-fg-subtle text-sm">
            No countries found matching "{searchValue}"
          </span>
        </div>
      }
    />
  );
};

export default CountrySelector;
