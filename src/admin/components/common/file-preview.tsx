import { Button, Text } from "@camped-ai/ui"

interface FilePreviewProps {
  filename: string
  loading?: boolean
  activity?: string
  actions?: Array<{
    actions: Array<{
      label: string
      icon: React.ReactNode
      onClick: () => void
    }>
  }>
  url?: string
}

export const FilePreview = ({ 
  filename, 
  loading, 
  activity, 
  actions,
  url 
}: FilePreviewProps) => {
  const handleDownload = () => {
    if (url) {
      const link = document.createElement('a')
      link.href = url
      link.download = filename
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
    }
  }

  return (
    <div className="border border-gray-200 rounded-lg p-4 flex items-center justify-between bg-white hover:bg-gray-50 transition-colors">
      <div className="flex items-center gap-3">
        <div className="w-10 h-10 bg-gray-100 rounded flex items-center justify-center">
          <svg className="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
          </svg>
        </div>
        <div>
          <Text className="font-medium text-gray-900">{filename}</Text>
          {loading && activity && (
            <Text className="text-sm text-gray-500">{activity}</Text>
          )}
        </div>
      </div>
      <div className="flex items-center gap-2">
        {actions?.map((actionGroup, index) => 
          actionGroup.actions.map((action, actionIndex) => (
            <Button 
              key={`${index}-${actionIndex}`}
              variant="transparent" 
              size="small" 
              onClick={action.onClick}
              className="p-2"
            >
              {action.icon}
            </Button>
          ))
        )}
        {url && (
          <Button variant="transparent" size="small" onClick={handleDownload} className="p-2">
            <svg className="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
          </Button>
        )}
      </div>
    </div>
  )
}
