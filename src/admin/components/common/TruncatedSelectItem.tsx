import React, { useState } from "react";
import { Button } from "@camped-ai/ui";

interface TruncatedSelectItemProps {
  text: string;
  maxLength?: number;
  className?: string;
  children?: React.ReactNode;
}

export const TruncatedSelectItem: React.FC<TruncatedSelectItemProps> = ({
  text,
  maxLength = 100,
  className = "",
  children,
}) => {
  const [showFull, setShowFull] = useState(false);
  const isLongText = text && text.length > maxLength;
  const displayText = showFull || !isLongText ? text : `${text.substring(0, maxLength)}...`;

  return (
    <div className={`w-full ${className}`}>
      <div className="flex flex-col gap-1">
        <span className="break-words">{displayText}</span>
        {children}
        {isLongText && (
          <Button
            variant="transparent"
            size="small"
            onClick={(e) => {
              e.stopPropagation();
              setShowFull(!showFull);
            }}
            className="text-blue-600 hover:text-blue-700 p-0 h-auto font-normal text-xs self-start"
          >
            {/* {showFull ? "Show Less" : "Show More"} */}
          </Button>
        )}
      </div>
    </div>
  );
};

export default TruncatedSelectItem;
