import React from "react";
import { Select } from "@camped-ai/ui";
import { useAdminCurrencies } from "../../hooks/use-admin-currencies";

interface CurrencySelectorProps {
  value?: string;
  onValueChange?: (currencyCode: string) => void;
  onChange?: (currencyCode: string) => void; // Keep for backward compatibility
  placeholder?: string;
  disabled?: boolean;
  className?: string;
}

export const CurrencySelector: React.FC<CurrencySelectorProps> = ({
  value,
  onValueChange,
  onChange, // Keep for backward compatibility
  placeholder = "Select currency",
  disabled = false,
  className = "",
}) => {
  const { currencyOptions, isLoading, defaultCurrency } = useAdminCurrencies();

  // Use default currency if no value is provided
  const currentValue = value || defaultCurrency?.currency_code || "";

  // Handle value change - support both new and legacy prop names
  const handleValueChange = (newValue: string) => {
    if (onValueChange) {
      onValueChange(newValue);
    } else if (onChange) {
      onChange(newValue);
    }
  };

  // If there's only one currency, don't show the selector - just display the currency
  if (!isLoading && currencyOptions.length === 1) {
    const singleCurrency = currencyOptions[0];

    // Ensure the form has the correct value set
    if (currentValue !== singleCurrency.value) {
      handleValueChange(singleCurrency.value);
    }

    return (
      <Select value={singleCurrency.value} disabled>
        <Select.Trigger className={className}>
          <Select.Value>{singleCurrency.label}</Select.Value>
        </Select.Trigger>
        <Select.Content>
          <Select.Item value={singleCurrency.value}>
            {singleCurrency.label}
          </Select.Item>
        </Select.Content>
      </Select>
    );
  }

  if (isLoading) {
    return (
      <Select disabled>
        <Select.Trigger className={className}>
          <Select.Value placeholder="Loading currencies..." />
        </Select.Trigger>
        <Select.Content>
          <Select.Item value="loading">Loading...</Select.Item>
        </Select.Content>
      </Select>
    );
  }

  return (
    <Select
      value={currentValue}
      onValueChange={handleValueChange}
      disabled={disabled}
    >
      <Select.Trigger className={className}>
        <Select.Value placeholder={placeholder} />
      </Select.Trigger>
      <Select.Content>
        {currencyOptions.map((option) => (
          <Select.Item key={option.value} value={option.value}>
            {option.label}
          </Select.Item>
        ))}
      </Select.Content>
    </Select>
  );
};
