import React, { Suspense, lazy, useState } from "react";
import { Tabs, Text, Button, Input, Label } from "@camped-ai/ui";
import { Building, Info, Image, HelpCircle } from "lucide-react";

// Lazy load heavy components to improve initial tab loading
const HeavyFormComponent = lazy(() => import("./heavy-form-component"));
const MediaUploadComponent = lazy(() => import("./media-upload-component"));
const FAQManagementComponent = lazy(() => import("./faq-management-component"));

// Loading skeleton component
const TabLoadingSkeleton = () => (
  <div className="space-y-4 animate-pulse">
    <div className="h-4 bg-gray-200 rounded w-3/4"></div>
    <div className="h-4 bg-gray-200 rounded w-1/2"></div>
    <div className="h-32 bg-gray-200 rounded"></div>
  </div>
);

// Optimized Tab Demo Component
export function OptimizedTabsDemo() {
  const [formData, setFormData] = useState({
    name: "",
    description: "",
    location: "",
  });

  return (
    <div className="w-full max-w-4xl mx-auto p-6">
      <h2 className="text-2xl font-bold mb-6">Optimized Tabs Implementation</h2>
      
      <Tabs defaultValue="basics" className="w-full">
        <Tabs.List className="grid w-full grid-cols-4 mb-6">
          <Tabs.Trigger value="basics" className="flex items-center gap-2">
            <Building size={18} />
            <span>Basic Info</span>
          </Tabs.Trigger>
          <Tabs.Trigger value="details" className="flex items-center gap-2">
            <Info size={18} />
            <span>Details</span>
          </Tabs.Trigger>
          <Tabs.Trigger value="media" className="flex items-center gap-2">
            <Image size={18} />
            <span>Media</span>
          </Tabs.Trigger>
          <Tabs.Trigger value="faqs" className="flex items-center gap-2">
            <HelpCircle size={18} />
            <span>FAQs</span>
          </Tabs.Trigger>
        </Tabs.List>

        {/* Basic Information Tab - Lightweight, loads immediately */}
        <Tabs.Content value="basics" className="mt-0">
          <div className="space-y-6">
            <div className="bg-card p-6 rounded-lg shadow-sm border border-border">
              <h3 className="text-lg font-medium mb-4">Basic Information</h3>
              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="name">Name</Label>
                    <Input
                      id="name"
                      value={formData.name}
                      onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                      placeholder="Enter name"
                    />
                  </div>
                  <div>
                    <Label htmlFor="location">Location</Label>
                    <Input
                      id="location"
                      value={formData.location}
                      onChange={(e) => setFormData({ ...formData, location: e.target.value })}
                      placeholder="Enter location"
                    />
                  </div>
                </div>
                <div>
                  <Label htmlFor="description">Description</Label>
                  <Input
                    id="description"
                    value={formData.description}
                    onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                    placeholder="Enter description"
                  />
                </div>
              </div>
            </div>
          </div>
        </Tabs.Content>

        {/* Details Tab - Lazy loaded for better performance */}
        <Tabs.Content value="details" className="mt-0">
          <div className="space-y-6">
            <div className="bg-card p-6 rounded-lg shadow-sm border border-border">
              <h3 className="text-lg font-medium mb-4">Additional Details</h3>
              <Suspense fallback={<TabLoadingSkeleton />}>
                <HeavyFormComponent />
              </Suspense>
            </div>
          </div>
        </Tabs.Content>

        {/* Media Tab - Lazy loaded, only renders when accessed */}
        <Tabs.Content value="media" className="mt-0">
          <div className="space-y-6">
            <div className="bg-card p-6 rounded-lg shadow-sm border border-border">
              <h3 className="text-lg font-medium mb-4">Media Upload</h3>
              <Suspense fallback={<TabLoadingSkeleton />}>
                <MediaUploadComponent />
              </Suspense>
            </div>
          </div>
        </Tabs.Content>

        {/* FAQs Tab - Lazy loaded, complex state management only when needed */}
        <Tabs.Content value="faqs" className="mt-0">
          <div className="space-y-6">
            <div className="bg-card p-6 rounded-lg shadow-sm border border-border">
              <h3 className="text-lg font-medium mb-4">FAQs Management</h3>
              <Suspense fallback={<TabLoadingSkeleton />}>
                <FAQManagementComponent />
              </Suspense>
            </div>
          </div>
        </Tabs.Content>
      </Tabs>

      {/* Performance Tips */}
      <div className="mt-8 p-4 bg-blue-50 rounded-lg border border-blue-200">
        <h4 className="font-semibold text-blue-900 mb-2">Performance Optimizations Applied:</h4>
        <ul className="text-sm text-blue-800 space-y-1">
          <li>• <strong>Lazy Loading:</strong> Heavy components only load when their tab is accessed</li>
          <li>• <strong>Proper Tab Structure:</strong> Using @camped-ai/ui Tabs component for optimal rendering</li>
          <li>• <strong>Suspense Boundaries:</strong> Graceful loading states for async components</li>
          <li>• <strong>Conditional Rendering:</strong> Only active tab content is in the DOM</li>
          <li>• <strong>Skeleton Loading:</strong> Immediate visual feedback while components load</li>
        </ul>
      </div>
    </div>
  );
}

export default OptimizedTabsDemo;
