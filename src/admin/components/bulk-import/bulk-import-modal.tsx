import { useState } from "react";
import {
  Button,
  FocusModal,
  Text,
  Heading,
  toast,
  Tabs,
  Table,
} from "@camped-ai/ui";
import { FileIcon, DownloadIcon, UploadIcon, CheckCircleIcon } from "lucide-react";
import * as XLSX from 'xlsx';

type BulkImportModalProps = {
  open: boolean;
  onClose: () => void;
  onImportComplete?: () => void;
};

interface PreviewData {
  destinations: { headers: string[], rows: any[] };
  hotels: { headers: string[], rows: any[] };
  roomConfigs: { headers: string[], rows: any[] };
  rooms: { headers: string[], rows: any[] };
}

interface ImportResult {
  message: string;
  processing_time: string;
  summary: {
    destinations: { total: number; successful: number; failed: number; };
    hotels: { total: number; successful: number; failed: number; };
    room_configs: { total: number; successful: number; failed: number; };
    rooms: { total: number; successful: number; failed: number; };
  };
  results: {
    destinations: { successful: number; failed: number; created: any[]; errors: any[]; };
    hotels: { successful: number; failed: number; created: any[]; errors: any[]; };
    room_configs: { successful: number; failed: number; created: any[]; errors: any[]; };
    rooms: { successful: number; failed: number; created: any[]; errors: any[]; };
  };
}

const BulkImportModal = ({ open, onClose, onImportComplete }: BulkImportModalProps) => {
  const [file, setFile] = useState<File | null>(null);
  const [isUploading, setIsUploading] = useState(false);
  const [uploadResult, setUploadResult] = useState<ImportResult | null>(null);
  const [previewData, setPreviewData] = useState<PreviewData | null>(null);
  const [previewError, setPreviewError] = useState<string>("");

  const handleDownloadTemplate = async () => {
    try {
      window.open("/admin/bulk-import/template", "_blank");
    } catch (error) {
      console.error("Error downloading template:", error);
      toast.error("Error", {
        description: "Failed to download template",
      });
    }
  };

  const handleDownloadSalesforceTemplate = async () => {
    try {
      window.open("/admin/bulk-import/salesforce-template", "_blank");
    } catch (error) {
      console.error("Error downloading Salesforce template:", error);
      toast.error("Error", {
        description: "Failed to download Salesforce template",
      });
    }
  };

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = event.target.files?.[0];
    if (selectedFile) {
      setFile(selectedFile);
      setPreviewError("");
      setUploadResult(null);
      handleFilePreview(selectedFile);
    }
  };

  const handleFilePreview = async (file: File) => {
    try {
      const arrayBuffer = await file.arrayBuffer();
      const workbook = XLSX.read(arrayBuffer, { type: 'array' });

      const preview: PreviewData = {
        destinations: { headers: [], rows: [] },
        hotels: { headers: [], rows: [] },
        roomConfigs: { headers: [], rows: [] },
        rooms: { headers: [], rows: [] }
      };

      // Process each sheet
      const sheetMappings = [
        { name: 'Destinations', key: 'destinations' as keyof PreviewData },
        { name: 'Hotels', key: 'hotels' as keyof PreviewData },
        { name: 'RoomConfigs', key: 'roomConfigs' as keyof PreviewData },
        { name: 'Rooms', key: 'rooms' as keyof PreviewData }
      ];

      for (const { name, key } of sheetMappings) {
        const worksheet = workbook.Sheets[name];
        if (worksheet) {
          const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });
          if (jsonData.length > 0) {
            preview[key].headers = jsonData[0] as string[];
            preview[key].rows = jsonData.slice(1) as any[]; // Show all rows for preview
          }
        }
      }

      setPreviewData(preview);
    } catch (error) {
      console.error("Error previewing file:", error);
      setPreviewError("Failed to preview file. Please ensure it's a valid Excel file.");
    }
  };

  const handleImport = async () => {
    if (!file) {
      toast.error("Error", {
        description: "Please select a file first",
      });
      return;
    }

    setIsUploading(true);
    setUploadResult(null);

    try {
      const formData = new FormData();
      formData.append('file', file);

      const response = await fetch('/admin/bulk-import/upload', {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Import failed');
      }

      const result: ImportResult = await response.json();
      setUploadResult(result);

      const totalSuccessful = result.summary.destinations.successful + 
                             result.summary.hotels.successful + 
                             result.summary.room_configs.successful + 
                             result.summary.rooms.successful;

      if (totalSuccessful > 0) {
        toast.success("Success", {
          description: `${totalSuccessful} entities imported successfully`,
        });

        if (onImportComplete) {
          onImportComplete();
        }
      }

    } catch (error: any) {
      console.error("Error importing data:", error);
      toast.error("Error", {
        description: error.message || "Failed to import data",
      });
    } finally {
      setIsUploading(false);
    }
  };

  const resetModalState = () => {
    setFile(null);
    setPreviewData(null);
    setPreviewError("");
    setUploadResult(null);
    setIsUploading(false);
  };

  const truncateText = (text: string, maxLength: number = 30): string => {
    if (!text || text.length <= maxLength) return text;
    return text.substring(0, maxLength) + '...';
  };

  const renderPreviewTable = (headers: string[], rows: any[], title: string) => {
    if (headers.length === 0) {
      return (
        <div className="text-center py-8 text-gray-500">
          <Text>No {title.toLowerCase()} data found in the file</Text>
        </div>
      );
    }

    const maxPreviewRows = 10;
    const displayRows = rows.slice(0, maxPreviewRows);
    const hasMoreRows = rows.length > maxPreviewRows;

    return (
      <div className="space-y-3">
        <div className="flex items-center justify-between">
          <Text className="text-sm font-medium text-gray-700">
            {title} Preview
          </Text>
          <Text className="text-sm text-gray-500">
            Showing {displayRows.length} of {rows.length} rows
          </Text>
        </div>

        <div className="border rounded-lg overflow-hidden">
          <div className="overflow-x-auto">
            <Table>
              <Table.Header>
                <Table.Row>
                  {headers.map((header, index) => (
                    <Table.HeaderCell key={index} className="text-xs font-medium">
                      {header}
                    </Table.HeaderCell>
                  ))}
                </Table.Row>
              </Table.Header>
              <Table.Body>
                {displayRows.map((row, rowIndex) => (
                  <Table.Row key={rowIndex}>
                    {headers.map((_, colIndex) => (
                      <Table.Cell key={colIndex} className="text-xs">
                        {truncateText(String(row[colIndex] || ''))}
                      </Table.Cell>
                    ))}
                  </Table.Row>
                ))}
              </Table.Body>
            </Table>
          </div>
        </div>

        {hasMoreRows && (
          <div className="text-center py-2">
            <Text className="text-sm text-gray-500">
              ... and {rows.length - maxPreviewRows} more rows will be processed
            </Text>
          </div>
        )}
      </div>
    );
  };

  const renderResults = (result: ImportResult) => {
    const entities = [
      { key: 'destinations', label: 'Destinations', icon: '🌍' },
      { key: 'hotels', label: 'Hotels', icon: '🏨' },
      { key: 'room_configs', label: 'Room Configs', icon: '🛏️' },
      { key: 'rooms', label: 'Rooms', icon: '🏠' }
    ];

    return (
      <div className="space-y-6 p-6">
        <div className="text-center">
          <CheckCircleIcon className="w-16 h-16 text-green-500 mx-auto mb-4" />
          <Heading level="h3" className="text-lg font-semibold text-gray-900 mb-2">
            Import Completed
          </Heading>
          <Text className="text-gray-600 mb-4">
            {result.message}
          </Text>
          <Text className="text-sm text-gray-500">
            Processing time: {result.processing_time}
          </Text>
        </div>

        <div className="grid grid-cols-2 gap-4">
          {entities.map(({ key, label, icon }) => {
            const summary = result.summary[key as keyof typeof result.summary];
            return (
              <div key={key} className="bg-gray-50 rounded-lg p-4">
                <div className="flex items-center gap-2 mb-2">
                  <span className="text-lg">{icon}</span>
                  <Text className="font-medium text-gray-900">{label}</Text>
                </div>
                <div className="space-y-1">
                  <div className="flex justify-between text-sm">
                    <Text className="text-gray-600">Total:</Text>
                    <Text className="font-medium">{summary.total}</Text>
                  </div>
                  <div className="flex justify-between text-sm">
                    <Text className="text-green-600">Successful:</Text>
                    <Text className="font-medium text-green-600">{summary.successful}</Text>
                  </div>
                  {summary.failed > 0 && (
                    <div className="flex justify-between text-sm">
                      <Text className="text-red-600">Failed:</Text>
                      <Text className="font-medium text-red-600">{summary.failed}</Text>
                    </div>
                  )}
                </div>
              </div>
            );
          })}
        </div>

        {/* Show errors if any */}
        {Object.values(result.results).some(r => r.errors.length > 0) && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-4">
            <Heading level="h3" className="text-sm font-medium text-red-900 mb-2">
              Import Errors
            </Heading>
            <div className="space-y-2 max-h-40 overflow-y-auto">
              {Object.entries(result.results).map(([entityType, entityResult]) => 
                entityResult.errors.map((error: any, index: number) => (
                  <div key={`${entityType}-${index}`} className="text-sm text-red-700">
                    <Text className="font-medium">{entityType} (Row {error.row}):</Text>
                    <Text className="ml-2">{error.error}</Text>
                  </div>
                ))
              )}
            </div>
          </div>
        )}
      </div>
    );
  };

  return (
    <FocusModal
      open={open}
      onOpenChange={(isOpen) => {
        if (!isOpen) {
          resetModalState();
          onClose();
        }
      }}
    >
      <FocusModal.Content className="flex flex-col h-full max-h-[95vh]">
        <FocusModal.Header className="flex-shrink-0">
          <div className="flex justify-between items-center w-full py-4">
            <Heading level="h2" className="text-xl font-semibold">
              Bulk Import Data
            </Heading>
          </div>
        </FocusModal.Header>

        <FocusModal.Body className="flex-1 overflow-y-auto">
          {!uploadResult ? (
            <div className="space-y-6 p-6">
              {/* Step 1: Download Template */}
              <div className="bg-gradient-to-r from-blue-50 to-indigo-50 p-6 rounded-lg border border-blue-200">
                <div className="flex items-start gap-3">
                  <div className="flex-shrink-0 w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                    <span className="text-blue-600 font-semibold text-sm">1</span>
                  </div>
                  <div className="flex-1">
                    <Heading level="h3" className="text-lg font-medium text-gray-900 mb-2">
                      Download Import Template
                    </Heading>
                    <Text className="text-gray-600 mb-4">
                      Choose the appropriate template for your data source:
                    </Text>

                    <div className="space-y-3">
                      {/* Standard Template */}
                      <div className="bg-white p-4 rounded-md border border-gray-200">
                        <Text className="font-medium text-gray-900 mb-1">Standard Template</Text>
                        <Text className="text-sm text-gray-600 mb-3">
                          Excel template with standard field names for destinations, hotels, room configs, and rooms.
                        </Text>
                        <Button
                          variant="secondary"
                          onClick={handleDownloadTemplate}
                          className="flex items-center gap-2 bg-blue-600 text-white hover:bg-blue-700"
                        >
                          <DownloadIcon className="w-4 h-4" />
                          Download Standard Template
                        </Button>
                      </div>

                      {/* Salesforce Template */}
                      <div className="bg-white p-4 rounded-md border border-orange-200">
                        <Text className="font-medium text-gray-900 mb-1">Salesforce Template</Text>
                        <Text className="text-sm text-gray-600 mb-3">
                          Excel template with Salesforce field names (Country__c, Current__c, etc.) for direct migration from Salesforce exports.
                        </Text>
                        <Button
                          variant="secondary"
                          onClick={handleDownloadSalesforceTemplate}
                          className="flex items-center gap-2 bg-orange-600 text-white hover:bg-orange-700"
                        >
                          <DownloadIcon className="w-4 h-4" />
                          Download Salesforce Template
                        </Button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Step 2: Upload File */}
              <div className="bg-white p-6 rounded-lg border border-gray-200">
                <div className="flex items-start gap-3">
                  <div className="flex-shrink-0 w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                    <span className="text-green-600 font-semibold text-sm">2</span>
                  </div>
                  <div className="flex-1">
                    <Heading level="h3" className="text-lg font-medium text-gray-900 mb-2">
                      Upload Your Data
                    </Heading>
                    <Text className="text-gray-600 mb-4">
                      Select your completed Excel file to preview and import your data.
                    </Text>
                    
                    <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-gray-400 transition-colors">
                      <input
                        type="file"
                        accept=".xlsx,.xls"
                        onChange={handleFileSelect}
                        className="hidden"
                        id="file-upload"
                      />
                      <label htmlFor="file-upload" className="cursor-pointer">
                        <FileIcon className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                        <Text className="text-lg font-medium text-gray-900 mb-2">
                          {file ? file.name : "Choose Excel file"}
                        </Text>
                        <Text className="text-gray-500">
                          Click to browse or drag and drop your .xlsx file here
                        </Text>
                      </label>
                    </div>

                    {previewError && (
                      <div className="mt-4 p-3 bg-red-50 border border-red-200 rounded-md">
                        <Text className="text-sm text-red-700">{previewError}</Text>
                      </div>
                    )}
                  </div>
                </div>
              </div>

              {/* Step 3: Preview Data */}
              {previewData && (
                <div className="bg-white p-6 rounded-lg border border-gray-200">
                  <div className="flex items-start gap-3 mb-4">
                    <div className="flex-shrink-0 w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
                      <span className="text-purple-600 font-semibold text-sm">3</span>
                    </div>
                    <div className="flex-1">
                      <Heading level="h3" className="text-lg font-medium text-gray-900 mb-2">
                        Preview Your Data
                      </Heading>
                      <Text className="text-gray-600 mb-4">
                        Review your data before importing. Only the first 5 rows are shown for each sheet.
                      </Text>
                    </div>
                  </div>

                  <Tabs defaultValue="destinations" className="w-full">
                    <Tabs.List className="grid w-full grid-cols-4">
                      <Tabs.Trigger value="destinations">
                        Destinations ({previewData.destinations.rows.length})
                      </Tabs.Trigger>
                      <Tabs.Trigger value="hotels">
                        Hotels ({previewData.hotels.rows.length})
                      </Tabs.Trigger>
                      <Tabs.Trigger value="roomConfigs">
                        Room Configs ({previewData.roomConfigs.rows.length})
                      </Tabs.Trigger>
                      <Tabs.Trigger value="rooms">
                        Rooms ({previewData.rooms.rows.length})
                      </Tabs.Trigger>
                    </Tabs.List>

                    <Tabs.Content value="destinations" className="mt-4">
                      {renderPreviewTable(previewData.destinations.headers, previewData.destinations.rows, "Destinations")}
                    </Tabs.Content>

                    <Tabs.Content value="hotels" className="mt-4">
                      {renderPreviewTable(previewData.hotels.headers, previewData.hotels.rows, "Hotels")}
                    </Tabs.Content>

                    <Tabs.Content value="roomConfigs" className="mt-4">
                      {renderPreviewTable(previewData.roomConfigs.headers, previewData.roomConfigs.rows, "Room Configs")}
                    </Tabs.Content>

                    <Tabs.Content value="rooms" className="mt-4">
                      {renderPreviewTable(previewData.rooms.headers, previewData.rooms.rows, "Rooms")}
                    </Tabs.Content>
                  </Tabs>
                </div>
              )}

              {/* Step 4: Import */}
              {file && previewData && (
                <div className="bg-white p-6 rounded-lg border border-gray-200">
                  <div className="flex items-start gap-3">
                    <div className="flex-shrink-0 w-8 h-8 bg-orange-100 rounded-full flex items-center justify-center">
                      <span className="text-orange-600 font-semibold text-sm">4</span>
                    </div>
                    <div className="flex-1">
                      <Heading level="h3" className="text-lg font-medium text-gray-900 mb-2">
                        Start Import
                      </Heading>
                      <Text className="text-gray-600 mb-4">
                        Ready to import your data? This will create all destinations, hotels, room configs, and rooms.
                      </Text>
                      
                      <Button
                        onClick={handleImport}
                        disabled={isUploading}
                        className="flex items-center gap-2 bg-orange-600 text-white hover:bg-orange-700"
                      >
                        <UploadIcon className="w-4 h-4" />
                        {isUploading ? "Importing..." : "Start Import"}
                      </Button>
                    </div>
                  </div>
                </div>
              )}
            </div>
          ) : (
            renderResults(uploadResult)
          )}
        </FocusModal.Body>
      </FocusModal.Content>
    </FocusModal>
  );
};

export default BulkImportModal;
