import React, { useMemo, useRef } from "react";
import ReactQuill from "react-quill";
import "react-quill/dist/quill.snow.css";
import { Label, Text } from "@camped-ai/ui";

interface RichTextEditorProps {
  id?: string;
  label?: string;
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  required?: boolean;
  helpText?: string;
  error?: string;
  disabled?: boolean;
  className?: string;
  height?: string;
}

/**
 * A rich text editor component
 *
 * @example
 * <RichTextEditor
 *   label="AI Content"
 *   value={aiContent}
 *   onChange={setAiContent}
 *   placeholder="Enter rich text content"
 *   height="300px"
 * />
 */
const RichTextEditor: React.FC<RichTextEditorProps> = ({
  id,
  label,
  value,
  onChange,
  placeholder = "Enter content...",
  required = false,
  helpText,
  error,
  disabled = false,
  className = "",
  height = "200px",
}) => {
  const quillRef = useRef<ReactQuill>(null);

  const modules = useMemo(
    () => ({
      toolbar: [
        [{ header: [1, 2, 3, false] }],
        ["bold", "italic", "underline", "strike"],
        [{ list: "ordered" }, { list: "bullet" }],
        [{ indent: "-1" }, { indent: "+1" }],
        ["link"],
        [{ align: [] }],
        ["clean"],
      ],
    }),
    []
  );

  const formats = [
    "header",
    "bold",
    "italic",
    "underline",
    "strike",
    "list",
    "bullet",
    "indent",
    "link",
    "align",
  ];

  return (
    <div className={className}>
      {label && (
        <Label htmlFor={id} className="block font-medium mb-2">
          {label} {required && <span className="text-red-500">*</span>}
        </Label>
      )}

      <div className="relative">
        <ReactQuill
          ref={quillRef}
          theme="snow"
          value={value}
          onChange={onChange}
          placeholder={placeholder}
          modules={modules}
          formats={formats}
          readOnly={disabled}
          style={{
            height: height,
            marginBottom: "42px", // Space for toolbar
          }}
          className={`
            ${disabled ? "opacity-50 cursor-not-allowed" : ""}
            ${error ? "border-red-500" : ""}
          `}
        />
      </div>

      {helpText && !error && (
        <Text className="text-xs text-gray-500 pt-3">{helpText}</Text>
      )}

      {error && (
        <Text className="text-xs text-red-500 mt-1">{error}</Text>
      )}
    </div>
  );
};

export default RichTextEditor;
