import {
  Input,
  FocusModal,
  Text,
  Label,
  Textarea,
  Tooltip,
  Heading,
  Button,
  InlineTip,
  Tabs,
  Select,
} from "@camped-ai/ui";
import "../styles/theme-variables.css";
import { useForm } from "react-hook-form";
import HotelMediaSection from "./hotel/hotel-media-section";
import { MediaField } from "./hotel/media-item";
import { useState, useEffect, useRef } from "react";
import { useDestinationsForHotelForm } from "../hooks/supplier-products-services/use-destinations";
import { TextareaField, InputField as AIInput } from "./ai-enhanced-inputs";
import { Info, Mail, Tag, Building, Image, Loader2 } from "lucide-react";
import Spinner from "./shared/spinner";

import Rating from "@mui/material/Rating";
import VisibilitySettings from "./visibility-settings";
import LanguageSelector from "./language-selector";
import { useProjectLanguages } from "../hooks/languages/useProjectLanguages";
import { useHotelTranslations } from "../hooks/translations/useHotelTranslations";
import { useHotelArrayTranslations } from "../hooks/translations/useHotelArrayTranslations";
import TranslatableTagInput from "./translatable-tag-input";
import AITranslateButton from "./ai-translate-button";
import { CurrencySelector } from "./common/currency-selector";
import { useAdminCurrencies } from "../hooks/use-admin-currencies";

// Define which fields are translatable
const TRANSLATABLE_FIELDS = [
  "name",
  "description",
  "location",
  "address",
  "notes",
] as const;

// Define which fields are translatable arrays
const TRANSLATABLE_ARRAY_FIELDS = [
  "tags",
  "amenities",
  "rules",
  "safety_measures",
] as const;

// Helper function to check if a field is translatable
const isFieldTranslatable = (fieldName: string): boolean => {
  return (
    TRANSLATABLE_FIELDS.includes(fieldName as any) ||
    TRANSLATABLE_ARRAY_FIELDS.includes(fieldName as any)
  );
};

// Helper function to determine if a field should be enabled based on language selection
const isFieldEnabled = (
  fieldName: string,
  isBaseLanguage: boolean
): boolean => {
  // If it's the base language, all fields are enabled
  if (isBaseLanguage) return true;

  // For non-base languages, only translatable fields are enabled
  return isFieldTranslatable(fieldName);
};

export type HotelFormData = {
  name: string;
  handle: string;
  description: string;
  is_active: boolean;
  is_featured?: boolean;
  website: string | null;
  email: string | null;
  destination_id: string;
  rating?: number;
  total_reviews?: number;
  notes?: string;
  location?: string;
  address?: string;
  phone_number?: string;
  timezone?: string;
  available_languages?: string[];
  tax_type?: string;
  tax_number?: string;
  tags?: string[];
  amenities?: string[];
  rules?: string[];
  safety_measures?: string[];
  currency?: string;
  margin?: number; // New margin field
  check_in_time: string;
  check_out_time: string;
  is_pets_allowed?: boolean;
  parent_category_id?: string | null;
  media?: MediaField[];
  image_ids?: string[];
  thumbnail_image_id?: string;
  id?: string;
};

type HotelFormProps = {
  formData: HotelFormData;
  onSubmit: (data: HotelFormData) => Promise<boolean>;
  closeModal: () => void;
  onSubmitRef?: React.MutableRefObject<(() => Promise<void>) | null>;
};

const HotelFormModern = ({
  formData,
  onSubmit,
  closeModal,
  onSubmitRef,
}: HotelFormProps) => {
  // Fetch destinations using TanStack Query
  const {
    data: destinationsData,
    isLoading: destinationsLoading,
    error: destinationsError,
  } = useDestinationsForHotelForm();

  const destinations = destinationsData?.destinations || [];

  // Get currency data for fallback
  const { defaultCurrency } = useAdminCurrencies();

  // Initialize form early so it can be used in helper functions
  const form = useForm<HotelFormData>({
    mode: "onChange", // Enable real-time validation
    reValidateMode: "onChange", // Re-validate on every change
    defaultValues: {
      ...formData,
      tags: Array.isArray(formData.tags) ? formData.tags : [],
      amenities: Array.isArray(formData.amenities) ? formData.amenities : [],
      rules: Array.isArray(formData.rules) ? formData.rules : [],
      safety_measures: Array.isArray(formData.safety_measures)
        ? formData.safety_measures
        : [],
    },
  });

  const [tags, setTags] = useState<string[]>(() => {
    if (!formData.tags) return [];
    if (Array.isArray(formData.tags)) return formData.tags;
    if (typeof formData.tags === "string") {
      try {
        // Try to parse if it's a JSON string
        const parsed = JSON.parse(formData.tags);
        return Array.isArray(parsed) ? parsed : [];
      } catch (e) {
        // If not a valid JSON, split by comma
        return (formData.tags as string)
          .split(",")
          .map((tag: string) => tag.trim())
          .filter((tag: string) => tag);
      }
    }
    return [];
  });

  const [tagInput, setTagInput] = useState("");



  const handleTagInputKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === "Enter") {
      e.preventDefault();
      addTag(tagInput);
    } else if (e.key === "Backspace" && tagInput === "" && tags.length > 0) {
      // Remove last tag when backspace is pressed on empty input
      removeTag(tags[tags.length - 1]);
    }
  };

  const handleTagInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    // Handle comma-separated input
    if (value.includes(",")) {
      const newTags = value
        .split(",")
        .map((tag) => tag.trim())
        .filter((tag) => tag);
      newTags.forEach((tag) => {
        if (!tags.includes(tag)) {
          addTag(tag);
        }
      });
      setTagInput("");
    } else {
      setTagInput(value);
    }
  };

  // Helper functions for amenities management
  const addAmenity = (amenity: string) => {
    const trimmedAmenity = amenity.trim();
    if (trimmedAmenity && !amenities.includes(trimmedAmenity)) {
      const newAmenities = [...amenities, trimmedAmenity];
      setAmenities(newAmenities);
      form.setValue("amenities", newAmenities);
      setAmenityInput("");
    }
  };

  const removeAmenity = (amenityToRemove: string) => {
    const newAmenities = amenities.filter(
      (amenity) => amenity !== amenityToRemove
    );
    setAmenities(newAmenities);
    form.setValue("amenities", newAmenities);
  };

  const handleAmenityInputKeyDown = (
    e: React.KeyboardEvent<HTMLInputElement>
  ) => {
    if (e.key === "Enter") {
      e.preventDefault();
      addAmenity(amenityInput);
    } else if (
      e.key === "Backspace" &&
      amenityInput === "" &&
      amenities.length > 0
    ) {
      removeAmenity(amenities[amenities.length - 1]);
    }
  };

  const handleAmenityInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    if (value.includes(",")) {
      const newAmenities = value
        .split(",")
        .map((item) => item.trim())
        .filter((item) => item);
      newAmenities.forEach((amenity) => {
        if (!amenities.includes(amenity)) {
          addAmenity(amenity);
        }
      });
      setAmenityInput("");
    } else {
      setAmenityInput(value);
    }
  };

  // Helper functions for rules management
  const addRule = (rule: string) => {
    const trimmedRule = rule.trim();
    if (trimmedRule && !rules.includes(trimmedRule)) {
      const newRules = [...rules, trimmedRule];
      setRules(newRules);
      form.setValue("rules", newRules);
      setRuleInput("");
    }
  };

  const removeRule = (ruleToRemove: string) => {
    const newRules = rules.filter((rule) => rule !== ruleToRemove);
    setRules(newRules);
    form.setValue("rules", newRules);
  };

  const handleRuleInputKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === "Enter") {
      e.preventDefault();
      addRule(ruleInput);
    } else if (e.key === "Backspace" && ruleInput === "" && rules.length > 0) {
      removeRule(rules[rules.length - 1]);
    }
  };

  const handleRuleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    if (value.includes(",")) {
      const newRules = value
        .split(",")
        .map((item) => item.trim())
        .filter((item) => item);
      newRules.forEach((rule) => {
        if (!rules.includes(rule)) {
          addRule(rule);
        }
      });
      setRuleInput("");
    } else {
      setRuleInput(value);
    }
  };

  // Helper functions for safety measures management
  const addSafetyMeasure = (measure: string) => {
    const trimmedMeasure = measure.trim();
    if (trimmedMeasure && !safetyMeasures.includes(trimmedMeasure)) {
      const newSafetyMeasures = [...safetyMeasures, trimmedMeasure];
      setSafetyMeasures(newSafetyMeasures);
      form.setValue("safety_measures", newSafetyMeasures);
      setSafetyMeasureInput("");
    }
  };

  const removeSafetyMeasure = (measureToRemove: string) => {
    const newSafetyMeasures = safetyMeasures.filter(
      (measure) => measure !== measureToRemove
    );
    setSafetyMeasures(newSafetyMeasures);
    form.setValue("safety_measures", newSafetyMeasures);
  };

  const handleSafetyMeasureInputKeyDown = (
    e: React.KeyboardEvent<HTMLInputElement>
  ) => {
    if (e.key === "Enter") {
      e.preventDefault();
      addSafetyMeasure(safetyMeasureInput);
    } else if (
      e.key === "Backspace" &&
      safetyMeasureInput === "" &&
      safetyMeasures.length > 0
    ) {
      removeSafetyMeasure(safetyMeasures[safetyMeasures.length - 1]);
    }
  };

  const handleSafetyMeasureInputChange = (
    e: React.ChangeEvent<HTMLInputElement>
  ) => {
    const value = e.target.value;
    if (value.includes(",")) {
      const newSafetyMeasures = value
        .split(",")
        .map((item) => item.trim())
        .filter((item) => item);
      newSafetyMeasures.forEach((measure) => {
        if (!safetyMeasures.includes(measure)) {
          addSafetyMeasure(measure);
        }
      });
      setSafetyMeasureInput("");
    } else {
      setSafetyMeasureInput(value);
    }
  };

  const [amenities, setAmenities] = useState<string[]>(() => {
    if (!formData.amenities) return [];
    if (Array.isArray(formData.amenities)) return formData.amenities;
    if (typeof formData.amenities === "string") {
      try {
        const parsed = JSON.parse(formData.amenities);
        return Array.isArray(parsed) ? parsed : [];
      } catch (e) {
        return (formData.amenities as string)
          .split(",")
          .map((item: string) => item.trim())
          .filter((item: string) => item);
      }
    }
    return [];
  });

  const [rules, setRules] = useState<string[]>(() => {
    if (!formData.rules) return [];
    if (Array.isArray(formData.rules)) return formData.rules;
    if (typeof formData.rules === "string") {
      try {
        const parsed = JSON.parse(formData.rules);
        return Array.isArray(parsed) ? parsed : [];
      } catch (e) {
        return (formData.rules as string)
          .split(",")
          .map((item: string) => item.trim())
          .filter((item: string) => item);
      }
    }
    return [];
  });

  const [safetyMeasures, setSafetyMeasures] = useState<string[]>(() => {
    if (!formData.safety_measures) return [];
    if (Array.isArray(formData.safety_measures))
      return formData.safety_measures;
    if (typeof formData.safety_measures === "string") {
      try {
        const parsed = JSON.parse(formData.safety_measures);
        return Array.isArray(parsed) ? parsed : [];
      } catch (e) {
        return (formData.safety_measures as string)
          .split(",")
          .map((item: string) => item.trim())
          .filter((item: string) => item);
      }
    }
    return [];
  });

  const [amenityInput, setAmenityInput] = useState("");
  const [ruleInput, setRuleInput] = useState("");
  const [safetyMeasureInput, setSafetyMeasureInput] = useState("");

  // Destination visibility settings state (UI only)
  const [destinationVisibility, setDestinationVisibility] = useState({
    show_in_destination_listings: false,
    show_in_destination_highlights: false,
    show_in_destination_map: false,
    allow_destination_booking: false,
  });



  const isEdit = !!formData.id;
  const hotelId = formData.id || "new";

  // Language management
  const { languages: tolgeeLanguages, loading: languagesLoading } =
    useProjectLanguages();
  const [selectedLanguage, setSelectedLanguage] = useState("en"); // Default to English
  const isBaseLanguage = selectedLanguage === "en";

  // Translation management
  const {
    saveTranslations,
    loadTranslations,
    getTranslationKeys,
    translations,
    saveArrayTranslations,
    arrayTranslations,
    setArrayTranslations,
    loading,
    error,
    getArrayTranslation,
  } = useHotelTranslations(hotelId);

  // Array field translation management - pass shared state from main useHotelTranslations
  const tagsTranslation = useHotelArrayTranslations(
    hotelId,
    selectedLanguage,
    "tags",
    tags,
    arrayTranslations,
    loading,
    error,
    getArrayTranslation,
    setArrayTranslations,
    saveArrayTranslations
  );
  const amenitiesTranslation = useHotelArrayTranslations(
    hotelId,
    selectedLanguage,
    "amenities",
    amenities,
    arrayTranslations,
    loading,
    error,
    getArrayTranslation,
    setArrayTranslations,
    saveArrayTranslations
  );
  const rulesTranslation = useHotelArrayTranslations(
    hotelId,
    selectedLanguage,
    "rules",
    rules,
    arrayTranslations,
    loading,
    error,
    getArrayTranslation,
    setArrayTranslations,
    saveArrayTranslations
  );
  const safetyMeasuresTranslation = useHotelArrayTranslations(
    hotelId,
    selectedLanguage,
    "safety_measures",
    safetyMeasures,
    arrayTranslations,
    loading,
    error,
    getArrayTranslation,
    setArrayTranslations,
    saveArrayTranslations
  );

  // Track translated values for the current language
  const [allTranslatedValues, setAllTranslatedValues] = useState<
    Record<string, Record<string, string>>
  >({});

  // Track which languages have user input to prevent overwriting
  const userInputLanguages = useRef<Set<string>>(new Set());

  // Track which languages we're currently loading to prevent duplicate API calls
  const loadingLanguagesRef = useRef<Set<string>>(new Set());

  // Loading states for better UX
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSavingTranslations, setIsSavingTranslations] = useState(false);

  // Handle validation state
  const [handleError, setHandleError] = useState<string | null>(null);

  // Handle validation functions
  const validateHandle = (value: string): string | boolean => {
    if (!value) return "Handle is required";

    // Check for valid characters (alphanumeric and hyphens only)
    const validPattern = /^[a-zA-Z0-9-]+$/;
    if (!validPattern.test(value)) {
      return "Handle can only contain letters, numbers, and hyphens";
    }

    // Check for consecutive hyphens
    if (value.includes("--")) {
      return "Handle cannot contain consecutive hyphens";
    }

    // Check if starts or ends with hyphen
    if (value.startsWith("-") || value.endsWith("-")) {
      return "Handle cannot start or end with a hyphen";
    }

    return true;
  };

  const validateHandleForState = (value: string): string | null => {
    const result = validateHandle(value);
    return typeof result === "string" ? result : null;
  };

  const formatHandle = (value: string): string => {
    return value
      .toLowerCase()
      .replace(/[^a-zA-Z0-9\s-]/g, "") // Remove invalid characters
      .replace(/\s+/g, "-") // Convert spaces to hyphens
      .replace(/-+/g, "-") // Replace multiple hyphens with single hyphen
      .replace(/^-+|-+$/g, ""); // Remove leading/trailing hyphens
  };

  // Reset form when formData changes (e.g., when creating a new hotel after submitting one)
  useEffect(() => {
    // Reset the form with the new formData
    form.reset(formData);

    // Reset all array states and sync with form
    let newTags: string[] = [];
    if (!formData.tags) {
      newTags = [];
    } else if (Array.isArray(formData.tags)) {
      newTags = formData.tags;
    } else if (typeof formData.tags === "string") {
      try {
        const parsed = JSON.parse(formData.tags);
        newTags = Array.isArray(parsed) ? parsed : [];
      } catch (e) {
        newTags = (formData.tags as string)
          .split(",")
          .map((tag: string) => tag.trim())
          .filter((tag: string) => tag);
      }
    }
    setTags(newTags);
    form.setValue("tags", newTags);
    setTagInput("");

    // Reset amenities
    let newAmenities: string[] = [];
    if (!formData.amenities) {
      newAmenities = [];
    } else if (Array.isArray(formData.amenities)) {
      newAmenities = formData.amenities;
    } else if (typeof formData.amenities === "string") {
      try {
        const parsed = JSON.parse(formData.amenities);
        newAmenities = Array.isArray(parsed) ? parsed : [];
      } catch (e) {
        newAmenities = (formData.amenities as string)
          .split(",")
          .map((item: string) => item.trim())
          .filter((item: string) => item);
      }
    }
    setAmenities(newAmenities);
    form.setValue("amenities", newAmenities);
    setAmenityInput("");

    // Reset rules
    let newRules: string[] = [];
    if (!formData.rules) {
      newRules = [];
    } else if (Array.isArray(formData.rules)) {
      newRules = formData.rules;
    } else if (typeof formData.rules === "string") {
      try {
        const parsed = JSON.parse(formData.rules);
        newRules = Array.isArray(parsed) ? parsed : [];
      } catch (e) {
        newRules = (formData.rules as string)
          .split(",")
          .map((item: string) => item.trim())
          .filter((item: string) => item);
      }
    }
    setRules(newRules);
    form.setValue("rules", newRules);
    setRuleInput("");

    // Reset safety measures
    let newSafetyMeasures: string[] = [];
    if (!formData.safety_measures) {
      newSafetyMeasures = [];
    } else if (Array.isArray(formData.safety_measures)) {
      newSafetyMeasures = formData.safety_measures;
    } else if (typeof formData.safety_measures === "string") {
      try {
        const parsed = JSON.parse(formData.safety_measures);
        newSafetyMeasures = Array.isArray(parsed) ? parsed : [];
      } catch (e) {
        newSafetyMeasures = (formData.safety_measures as string)
          .split(",")
          .map((item: string) => item.trim())
          .filter((item: string) => item);
      }
    }
    setSafetyMeasures(newSafetyMeasures);
    form.setValue("safety_measures", newSafetyMeasures);
    setSafetyMeasureInput("");
  }, [formData, form]);

  // Auto-populate currency and margin from selected destination
  useEffect(() => {
    const selectedDestinationId = form.watch("destination_id");
    if (selectedDestinationId && destinations.length > 0) {
      const selectedDestination = destinations.find(d => d.id === selectedDestinationId);
      if (selectedDestination) {
        // Auto-populate currency if destination has one and hotel doesn't
        if (selectedDestination.currency && !form.watch("currency")) {
          form.setValue("currency", selectedDestination.currency);
        }

        // Auto-populate margin if destination has one and hotel doesn't
        if (selectedDestination.margin !== null && selectedDestination.margin !== undefined && !form.watch("margin")) {
          form.setValue("margin", selectedDestination.margin);
        }
      }
    } else if (!form.watch("currency") && defaultCurrency) {
      // Fallback to system default currency if no destination selected
      form.setValue("currency", defaultCurrency.currency_code);
    }
  }, [form.watch("destination_id"), destinations, defaultCurrency, form]);

  // Ensure initial arrays are synced with form on component mount
  useEffect(() => {
    form.setValue("tags", tags);
    form.setValue("amenities", amenities);
    form.setValue("rules", rules);
    form.setValue("safety_measures", safetyMeasures);
  }, []);

  // Load translations when language changes - FIXED TO PREVENT INFINITE LOOPS
  useEffect(() => {
    const loadTranslationsForLanguage = async () => {
      // Skip loading for base language or if already loading this language
      if (isBaseLanguage || loadingLanguagesRef.current.has(selectedLanguage)) {
        return;
      }

      // For new hotels, initialize with base language values ONLY if no translations exist yet
      const hasUserInput = userInputLanguages.current.has(selectedLanguage);
      const existingTranslations = allTranslatedValues[selectedLanguage];
      const hasExistingTranslations =
        existingTranslations && Object.keys(existingTranslations).length > 0;

      // Only initialize if we have NO existing translations AND no user input
      if (!hasExistingTranslations && !hasUserInput) {
        console.log(
          `🆕 First time switching to ${selectedLanguage} - initializing with base values`
        );
        const initialTranslations: Record<string, string> = {};
        TRANSLATABLE_FIELDS.forEach((fieldName) => {
          const baseValue = form.watch(
            fieldName as keyof HotelFormData
          ) as string;
          if (baseValue && typeof baseValue === "string") {
            initialTranslations[fieldName] = baseValue;
          }
        });

        if (Object.keys(initialTranslations).length > 0) {
          console.log(
            `🔧 Setting initial translations for ${selectedLanguage}:`,
            initialTranslations
          );
          setAllTranslatedValues((prev) => ({
            ...prev,
            [selectedLanguage]: initialTranslations,
          }));
        }
      } else if (hasExistingTranslations) {
        console.log(
          `🔄 Returning to ${selectedLanguage} - preserving existing translations:`,
          existingTranslations
        );
      }

      // Skip API loading if no hotel ID for existing hotels
      if (!hotelId || hotelId === "new") {
        return;
      }

      console.log(
        `🔄 Loading translations for hotel ${hotelId} in language: ${selectedLanguage}`
      );

      // Mark this language as loading
      loadingLanguagesRef.current.add(selectedLanguage);

      try {
        await loadTranslations(selectedLanguage);
        console.log(
          `✅ Successfully loaded translations for ${selectedLanguage}`
        );
      } catch (error) {
        console.error(
          `❌ Failed to load translations for ${selectedLanguage}:`,
          error
        );
      } finally {
        // Remove from loading set
        loadingLanguagesRef.current.delete(selectedLanguage);
      }
    };

    loadTranslationsForLanguage();
  }, [selectedLanguage, isBaseLanguage, hotelId]); // Minimal dependencies to prevent infinite loop

  // Get translation keys once
  const translationKeys = getTranslationKeys();

  // Update translated values when translations change in the hook - ONLY if no user input exists
  useEffect(() => {
    if (!isBaseLanguage && translations[selectedLanguage]) {
      const currentTranslations = translations[selectedLanguage];
      const newTranslatedValues: Record<string, string> = {};

      // Extract only the values for our translatable fields
      TRANSLATABLE_FIELDS.forEach((fieldName) => {
        const key = translationKeys[fieldName];
        if (key && currentTranslations[key]) {
          newTranslatedValues[fieldName] = currentTranslations[key];
          console.log(
            `Found API translation for ${fieldName}: ${currentTranslations[key]}`
          );
        }
      });

      // Only update if we don't already have user input for this language
      const hasUserInput = userInputLanguages.current.has(selectedLanguage);
      const existingTranslations = allTranslatedValues[selectedLanguage] || {};
      const hasExistingUserTranslations =
        Object.keys(existingTranslations).length > 0;

      // Only load API translations if we have NO user input AND NO existing translations
      if (
        !hasUserInput &&
        !hasExistingUserTranslations &&
        Object.keys(newTranslatedValues).length > 0
      ) {
        console.log(
          `📥 Loading API translations for ${selectedLanguage} (no user input or existing translations):`,
          newTranslatedValues
        );
        setAllTranslatedValues((prev) => ({
          ...prev,
          [selectedLanguage]: newTranslatedValues,
        }));
      } else if (hasUserInput || hasExistingUserTranslations) {
        console.log(
          `⚠️ Skipping API translation load for ${selectedLanguage} - user input or existing translations exist`,
          { hasUserInput, hasExistingUserTranslations, existingTranslations }
        );
      }
    }
  }, [translations, selectedLanguage, isBaseLanguage, translationKeys]);

  // Helper function to get current translated value for a field
  const getTranslatedValue = (fieldName: string): string => {
    if (isBaseLanguage) {
      return (form.watch(fieldName as keyof HotelFormData) as string) || "";
    }

    // For non-base languages, try translated value first, then fallback to base language value
    const translatedValue = allTranslatedValues[selectedLanguage]?.[fieldName];
    if (translatedValue) {
      return translatedValue;
    }

    // Fallback to base language (English) value from form
    const baseValue =
      (form.watch(fieldName as keyof HotelFormData) as string) || "";
    return baseValue;
  };

  // Helper function to set translated value for a field
  const setTranslatedValue = (fieldName: string, value: string) => {
    console.log(
      `🎯 setTranslatedValue called: ${fieldName} = "${value}" (lang: ${selectedLanguage}, isBase: ${isBaseLanguage})`
    );

    if (isBaseLanguage) {
      form.setValue(fieldName as keyof HotelFormData, value);
      console.log(`📝 Updated base form field: ${fieldName} = "${value}"`);
    } else {
      // Mark this language as having user input
      userInputLanguages.current.add(selectedLanguage);
      console.log(`👤 Marked ${selectedLanguage} as having user input`);

      setAllTranslatedValues((prev) => {
        const updated = {
          ...prev,
          [selectedLanguage]: {
            ...prev[selectedLanguage],
            [fieldName]: value,
          },
        };
        console.log(
          `🔄 Updated allTranslatedValues for ${selectedLanguage}:`,
          updated[selectedLanguage]
        );
        return updated;
      });
    }
  };

  // Handle form submission
  const handleSubmit = async () => {
    // Prevent multiple submissions
    if (isSubmitting) return;

    // Trigger validation for all fields
    const isValid = await form.trigger();

    // Check if there are any validation errors
    if (!isValid || Object.keys(form.formState.errors).length > 0) {
      console.log("Form validation failed:", form.formState.errors);
      return;
    }

    setIsSubmitting(true);

    try {
      // Get the form values including media
      const formValues = form.getValues();

      // Basic validation for required fields
      if (
        !formValues.name ||
        !formValues.handle ||
        !formValues.destination_id ||
        !formValues.check_in_time ||
        !formValues.check_out_time
      ) {
        console.error("Missing required fields:", {
          name: !formValues.name,
          handle: !formValues.handle,
          destination_id: !formValues.destination_id,
          check_in_time: !formValues.check_in_time,
          check_out_time: !formValues.check_out_time,
        });
        return;
      }

      // Validate handle format
      const handleValidationResult = validateHandle(formValues.handle);
      if (handleValidationResult !== true) {
        console.error("Invalid handle format:", handleValidationResult);
        setHandleError(
          typeof handleValidationResult === "string"
            ? handleValidationResult
            : "Invalid handle format"
        );
        return;
      }

      // Use our local state arrays directly (they are the source of truth)
      const finalTags = tags.length > 0 ? tags : undefined;
      const finalAmenities = amenities.length > 0 ? amenities : undefined;
      const finalRules = rules.length > 0 ? rules : undefined;
      const finalSafetyMeasures =
        safetyMeasures.length > 0 ? safetyMeasures : undefined;

      // Ensure check-in and check-out times are properly formatted
      const checkInTime = formValues.check_in_time || "14:00";
      const checkOutTime = formValues.check_out_time || "11:00";

      // Prepare the data for submission
      const submissionData = {
        ...formValues,
        tags: finalTags,
        amenities: finalAmenities,
        rules: finalRules,
        safety_measures: finalSafetyMeasures,
        check_in_time: checkInTime,
        check_out_time: checkOutTime,
      };

      // Debug: Log the submission data to verify all arrays are included
      console.log("Hotel form submission data:", {
        tags: finalTags,
        amenities: finalAmenities,
        rules: finalRules,
        safetyMeasures: finalSafetyMeasures,
        formValues: {
          tags: formValues.tags,
          amenities: formValues.amenities,
          rules: formValues.rules,
          safety_measures: formValues.safety_measures,
        },
        localStates: {
          tags,
          amenities,
          rules,
          safetyMeasures,
        },
        submissionData,
      });

      // Generate and log translation keys for debugging
      if (hotelId && hotelId !== "new") {
        const translationKeys = getTranslationKeys();
        console.log("Translation keys for hotel:", translationKeys);

        // Save translations for ALL modified languages, not just the currently selected one
        // Get all languages that have any kind of translations
        const allTagTranslations = tagsTranslation.getAllLanguageTranslations();
        const allAmenitiesTranslations =
          amenitiesTranslation.getAllLanguageTranslations();
        const allRulesTranslations =
          rulesTranslation.getAllLanguageTranslations();
        const allSafetyMeasuresTranslations =
          safetyMeasuresTranslation.getAllLanguageTranslations();

        // Combine all languages that have translations
        const allLanguagesWithTranslations = new Set([
          ...Object.keys(allTranslatedValues).filter((lang) => {
            const langTranslations = allTranslatedValues[lang] || {};
            return Object.keys(langTranslations).length > 0;
          }),
          ...Object.keys(allTagTranslations).filter(
            (lang) =>
              allTagTranslations[lang] && allTagTranslations[lang].length > 0
          ),
          ...Object.keys(allAmenitiesTranslations).filter(
            (lang) =>
              allAmenitiesTranslations[lang] &&
              allAmenitiesTranslations[lang].length > 0
          ),
          ...Object.keys(allRulesTranslations).filter(
            (lang) =>
              allRulesTranslations[lang] &&
              allRulesTranslations[lang].length > 0
          ),
          ...Object.keys(allSafetyMeasuresTranslations).filter(
            (lang) =>
              allSafetyMeasuresTranslations[lang] &&
              allSafetyMeasuresTranslations[lang].length > 0
          ),
        ]);

        console.log(
          "🌍 Languages with translations for existing hotel:",
          Array.from(allLanguagesWithTranslations)
        );

        if (allLanguagesWithTranslations.size > 0) {
          setIsSavingTranslations(true);
          try {
            console.log(
              `🌐 Saving translations for ${allLanguagesWithTranslations.size} languages:`,
              Array.from(allLanguagesWithTranslations)
            );

            // Save translations for each language that has translated content
            for (const language of allLanguagesWithTranslations) {
              if (language === "en") continue; // Skip base language

              console.log(
                `💾 Processing ${language} translations for existing hotel...`
              );

              // Save regular field translations
              const languageTranslations = allTranslatedValues[language] || {};
              if (Object.keys(languageTranslations).length > 0) {
                console.log(
                  `💾 Saving field translations for ${language}:`,
                  languageTranslations
                );

                const translationsToSave: Record<string, string> = {};
                TRANSLATABLE_FIELDS.forEach((fieldName) => {
                  const translatedValue = languageTranslations[fieldName];
                  if (translatedValue && typeof translatedValue === "string") {
                    const key = translationKeys[fieldName];
                    if (key) {
                      translationsToSave[key] = translatedValue;
                    }
                  }
                });

                if (Object.keys(translationsToSave).length > 0) {
                  await saveTranslations(language, translationsToSave);
                  console.log(
                    `✅ Hotel field translations saved successfully for ${language}`
                  );
                }
              }

              // Save array translations for this language
              const arrayFieldsData = [
                { field: "tags" as const, translations: allTagTranslations },
                {
                  field: "amenities" as const,
                  translations: allAmenitiesTranslations,
                },
                { field: "rules" as const, translations: allRulesTranslations },
                {
                  field: "safety_measures" as const,
                  translations: allSafetyMeasuresTranslations,
                },
              ];

              for (const { field, translations } of arrayFieldsData) {
                const valuesForLanguage = translations[language];
                if (valuesForLanguage && valuesForLanguage.length > 0) {
                  console.log(
                    `💾 Saving ${field} translations for ${language}:`,
                    valuesForLanguage
                  );
                  await saveArrayTranslations(
                    language,
                    field,
                    valuesForLanguage
                  );
                  console.log(
                    `✅ Hotel ${field} translations saved successfully for ${language}`
                  );
                }
              }
            }
          } catch (error) {
            console.error("Error saving hotel translations:", error);
            // Continue with form submission even if translation saving fails
          } finally {
            setIsSavingTranslations(false);
          }
        }
      }

      const success = await onSubmit(submissionData);
      if (success) {
        closeModal();
      }
    } catch (error) {
      console.error("Error submitting hotel form:", error);
      // Handle form submission error here if needed
    } finally {
      setIsSubmitting(false);
    }
  };

  // Expose handleSubmit function to parent component
  useEffect(() => {
    if (onSubmitRef) {
      onSubmitRef.current = handleSubmit;
    }
  }, [onSubmitRef, handleSubmit]);

  return (
    <>
      <FocusModal.Header className="flex justify-between items-center w-full bg-background border-b border-border">
        <div className="flex justify-between items-center w-full">
          <Heading level="h2" className="text-xl font-semibold text-foreground">
            {isEdit ? "Edit Hotel" : "Add Hotel"}
          </Heading>
          <LanguageSelector
            selectedLanguage={selectedLanguage}
            onLanguageChange={setSelectedLanguage}
          />
        </div>
      </FocusModal.Header>

      <div className="flex flex-col overflow-y-auto flex-1 px-6 pb-4 w-full bg-background">
        {!isBaseLanguage && (
          <div className="mb-6">
            <InlineTip
              label="Translation Mode"
              className="bg-muted/50 border-border text-foreground"
            >
              You are editing in{" "}
              {tolgeeLanguages.find((lang) => lang.tag === selectedLanguage)
                ?.name || selectedLanguage}{" "}
              language. Only translatable fields (name, description, location,
              address, notes, tags, amenities, rules, safety measures) can be
              edited. Other fields show base language values and are read-only.
              {loadingLanguagesRef.current.has(selectedLanguage) && (
                <div className="mt-2 text-sm text-primary flex items-center gap-2">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary"></div>
                  Loading translations for{" "}
                  {tolgeeLanguages.find((lang) => lang.tag === selectedLanguage)
                    ?.name || selectedLanguage}
                  ...
                </div>
              )}
              {hotelId &&
                hotelId !== "new" &&
                (() => {
                  const keys = getTranslationKeys();
                  console.log("Generated translation keys:", keys);
                  return null;
                })()}
            </InlineTip>
          </div>
        )}

        <Tabs defaultValue="basics" className="w-full mt-5">
          <Tabs.List className="mb-6">
            <Tabs.Trigger value="basics" className="flex items-center gap-2">
              <Building size={18} />
              <span>Basic Information</span>
            </Tabs.Trigger>
            <Tabs.Trigger value="contact" className="flex items-center gap-2">
              <Mail size={18} />
              <span>Contact Details</span>
            </Tabs.Trigger>
            <Tabs.Trigger value="features" className="flex items-center gap-2">
              <Tag size={18} />
              <span>Features & Amenities</span>
            </Tabs.Trigger>
            <Tabs.Trigger value="media" className="flex items-center gap-2">
              <Image size={18} />
              <span>Media</span>
            </Tabs.Trigger>
          </Tabs.List>

          <Tabs.Content value="basics">
            <div className="space-y-6">
              <div className="bg-card pt-4 p-6 rounded-lg shadow-sm border border-border transition-colors duration-200">
                <h3 className="text-lg font-medium mb-4 text-card-foreground">
                  Hotel Information
                </h3>
                <div className="space-y-4">
                  {/* Hotel Name and Handle in same row */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <div className="flex items-center justify-between mb-1">
                        <Label className="font-medium">
                          Hotel Name <span className="text-destructive">*</span>
                        </Label>
                        {/* AI Translate button for hotel name - Only show for non-base languages */}
                        {!isBaseLanguage && formData.name && (
                          <AITranslateButton
                            sourceText={formData.name}
                            targetLanguage={selectedLanguage}
                            fieldType="name"
                            onTranslate={(translatedText) => {
                              console.log(
                                `🤖 AI translated hotel name: "${translatedText}"`
                              );
                              setTranslatedValue("name", translatedText);
                            }}
                            context={{
                              entityType: "hotel",
                              entityName: formData.name,
                              location: formData.location || "",
                            }}
                            size="small"
                            variant="transparent"
                            showText={false}
                          />
                        )}
                      </div>
                      <AIInput
                        id="name"
                        value={getTranslatedValue("name")}
                        onChange={(value) => {
                          setTranslatedValue("name", value);
                          if (isBaseLanguage) {
                            const formattedHandle = formatHandle(value);
                            form.setValue("handle", formattedHandle);
                            // Validate the generated handle
                            const error =
                              validateHandleForState(formattedHandle);
                            setHandleError(error);
                          }
                        }}
                        placeholder="e.g. Grand Hotel"
                        contentType="name"
                        context={{
                          type: "hotel",
                          destination: destinations.find(
                            (d) => d.id === form.watch("destination_id")
                          )?.name,
                        }}
                        disabled={!isFieldEnabled("name", isBaseLanguage)}
                      />
                      <Text className="text-xs text-muted-foreground mt-1">
                        The name of the hotel as it will appear to users
                      </Text>
                    </div>

                    <div>
                      <Label
                        htmlFor="handle"
                        className="block mb-1 font-medium text-foreground"
                      >
                        Handle <span className="text-destructive">*</span>
                      </Label>
                      <Input
                        id="handle"
                        {...form.register("handle", {
                          required: "Handle is required",
                          validate: validateHandle,
                        })}
                        placeholder="e.g. grand-hotel"
                        className={`w-full bg-background border-input text-foreground placeholder:text-muted-foreground focus:border-ring focus:ring-ring/20 ${
                          handleError
                            ? "border-destructive focus:border-destructive"
                            : ""
                        }`}
                        disabled={!isBaseLanguage}
                        onChange={(e) => {
                          const value = e.target.value;
                          const formattedValue = formatHandle(value);

                          // Update form value with formatted handle
                          form.setValue("handle", formattedValue);

                          // Validate and set error
                          const error = validateHandleForState(formattedValue);
                          setHandleError(error);

                          // Update the input value to show formatted version
                          e.target.value = formattedValue;
                        }}
                      />
                      {handleError && (
                        <Text className="text-xs text-destructive mt-1">
                          {handleError}
                        </Text>
                      )}
                      <Text className="text-xs text-muted-foreground mt-1">
                        Used for the URL and internal references. Only letters,
                        numbers, and hyphens allowed.
                      </Text>
                    </div>
                  </div>

                  {/* Destination and Location in same row */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label
                        htmlFor="destination"
                        className="block mb-1 font-medium text-foreground"
                      >
                        Destination <span className="text-destructive">*</span>
                      </Label>
                      <Select
                        value={form.watch("destination_id") || ""}
                        onValueChange={(value) => {
                          console.log("Destination changed to:", value);
                          form.setValue("destination_id", value);

                          // Auto-populate currency and margin from selected destination
                          const selectedDestination = destinations.find(d => d.id === value);
                          if (selectedDestination) {
                            // Auto-populate currency if destination has one
                            if (selectedDestination.currency) {
                              form.setValue("currency", selectedDestination.currency);
                            }

                            // Auto-populate margin if destination has one
                            if (selectedDestination.margin !== null && selectedDestination.margin !== undefined) {
                              form.setValue("margin", selectedDestination.margin);
                            }
                          } else if (defaultCurrency) {
                            // Fallback to system default currency
                            form.setValue("currency", defaultCurrency.currency_code);
                          }
                        }}
                        disabled={!isBaseLanguage || destinationsLoading}
                      >
                        <Select.Trigger className="w-full">
                          <Select.Value
                            placeholder={
                              destinationsLoading
                                ? "Loading destinations..."
                                : destinationsError
                                ? "Failed to load destinations"
                                : "Select a destination"
                            }
                          />
                        </Select.Trigger>
                        <Select.Content>
                          {destinationsLoading && (
                            <div className="flex items-center gap-2 px-3 py-2 text-sm text-ui-fg-subtle">
                              <Spinner size="small" />
                              Loading destinations...
                            </div>
                          )}
                          {destinationsError && (
                            <div className="px-3 py-2 text-sm text-red-600">
                              Failed to load destinations
                            </div>
                          )}
                          {!destinationsLoading &&
                            !destinationsError &&
                            destinations.length === 0 && (
                              <div className="px-3 py-2 text-sm text-ui-fg-subtle">
                                No destinations available
                              </div>
                            )}
                          {!destinationsLoading &&
                            !destinationsError &&
                            destinations.map((destination) => (
                              <Select.Item
                                key={destination.id}
                                value={destination.id}
                              >
                                {destination.name}{" "}
                                {destination.location
                                  ? `(${destination.location})`
                                  : ""}
                              </Select.Item>
                            ))}
                        </Select.Content>
                      </Select>
                    </div>

                    <div>
                      <div className="flex items-center justify-between mb-1">
                        <div className="flex items-center gap-1">
                          <Label htmlFor="location" className="font-medium">
                            Specific Location
                          </Label>
                          <Tooltip content="More specific location within the country (e.g. city, region)">
                            <Info size={12} className="text-muted-foreground" />
                          </Tooltip>
                        </div>
                        {/* AI Translate button for location - Only show for non-base languages */}
                        {!isBaseLanguage && formData.location && (
                          <AITranslateButton
                            sourceText={formData.location}
                            targetLanguage={selectedLanguage}
                            fieldType="content"
                            onTranslate={(translatedText) => {
                              console.log(
                                `🤖 AI translated hotel location: "${translatedText}"`
                              );
                              setTranslatedValue("location", translatedText);
                            }}
                            context={{
                              entityType: "hotel",
                              entityName: formData.name,
                              location: formData.location,
                            }}
                            size="small"
                            variant="transparent"
                            showText={false}
                          />
                        )}
                      </div>
                      <div className="relative">
                        <Input
                          id="location"
                          value={getTranslatedValue("location")}
                          onChange={(e) =>
                            setTranslatedValue("location", e.target.value)
                          }
                          placeholder="e.g. 123 Main Street"
                          className="w-full bg-background border-input text-foreground placeholder:text-muted-foreground focus:border-ring focus:ring-ring/20"
                          disabled={!isFieldEnabled("location", isBaseLanguage)}
                        />
                      </div>
                    </div>
                  </div>

                  {/* Currency and Margin Section */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="currency" className="block mb-1 font-medium text-foreground">
                        Currency
                      </Label>
                      <CurrencySelector
                        value={form.watch("currency") || ""}
                        onChange={(currencyCode) => {
                          form.setValue("currency", currencyCode);
                        }}
                        placeholder="Select currency"
                        disabled={!isBaseLanguage}
                        showLabel={false}
                      />
                      <Text className="text-xs text-muted-foreground mt-1">
                        Currency for this hotel (auto-populated from destination)
                      </Text>
                    </div>

                    <div>
                      <Label htmlFor="margin" className="block mb-1 font-medium text-foreground">
                        Margin (%)
                      </Label>
                      <Input
                        id="margin"
                        type="number"
                        min="0"
                        max="100"
                        step="0.01"
                        {...form.register("margin", {
                          setValueAs: (value) => {
                            if (value === "" || value === null || value === undefined) {
                              return undefined;
                            }
                            const numValue = parseFloat(value);
                            return isNaN(numValue) ? undefined : numValue;
                          },
                          validate: {
                            range: (value) => {
                              if (value === undefined || value === null) return true;
                              const numValue = typeof value === 'string' ? parseFloat(value) : value;
                              if (isNaN(numValue)) {
                                return "Please enter a valid number";
                              }
                              if (numValue < 0) {
                                return "Margin cannot be negative";
                              }
                              if (numValue > 100) {
                                return "Margin cannot exceed 100%";
                              }
                              return true;
                            }
                          }
                        })}
                        placeholder="0.00"
                        className={`w-full bg-background border-input text-foreground placeholder:text-muted-foreground focus:border-ring focus:ring-ring/20 ${
                          form.formState.errors.margin ? "border-destructive focus:border-destructive" : ""
                        }`}
                        disabled={!isBaseLanguage}
                      />
                      {form.formState.errors.margin && (
                        <Text className="text-xs text-destructive mt-1">
                          {form.formState.errors.margin.message}
                        </Text>
                      )}
                      <Text className="text-xs text-muted-foreground mt-1">
                        Margin percentage for this hotel (auto-populated from destination)
                      </Text>
                    </div>
                  </div>

                  <div>
                    <Label
                      htmlFor="rating"
                      className="block mb-1 font-medium text-foreground"
                    >
                      Rating
                    </Label>
                    <div className="mt-2">
                      <Input
                        id="rating"
                        type="number"
                        step="0.1"
                        min="1"
                        max="5"
                        {...form.register("rating", {
                          setValueAs: (value) => {
                            // Convert to number and handle empty values
                            if (value === "" || value === null || value === undefined) {
                              return undefined;
                            }
                            const numValue = parseFloat(value);
                            return isNaN(numValue) ? undefined : numValue;
                          },
                          validate: {
                            range: (value) => {
                              if (value === undefined || value === null) {
                                return true; // Allow empty values
                              }
                              const numValue = typeof value === 'string' ? parseFloat(value) : value;
                              if (isNaN(numValue)) {
                                return "Please enter a valid number";
                              }
                              if (numValue < 1) {
                                return "Rating must be at least 1";
                              }
                              if (numValue > 5) {
                                return "Rating cannot exceed 5";
                              }
                              return true;
                            }
                          }
                        })}
                        placeholder="e.g. 4.5"
                        className={`w-full bg-background border-input text-foreground placeholder:text-muted-foreground focus:border-ring focus:ring-ring/20 ${
                          form.formState.errors.rating ? "border-destructive focus:border-destructive" : ""
                        }`}
                        disabled={!isBaseLanguage}
                        onChange={(e) => {
                          const value = e.target.value;
                          form.setValue("rating", value === "" ? undefined : parseFloat(value));
                          form.trigger("rating"); // Trigger validation immediately
                        }}
                        onBlur={() => form.trigger("rating")} // Trigger validation on blur
                      />
                      {form.formState.errors.rating && (
                        <Text className="text-xs text-destructive mt-1">
                          {form.formState.errors.rating.message}
                        </Text>
                      )}
                      <Rating
                        name="size-small"
                        value={Number(form.watch("rating")) || 0}
                        readOnly
                        precision={0.5}
                        className="mt-3"
                        sx={{
                          "& .MuiRating-iconFilled": {
                            color: "hsl(var(--primary))",
                          },
                          "& .MuiRating-iconEmpty": {
                            color: "hsl(var(--muted-foreground))",
                          },
                        }}
                      />
                      <Text className="text-xs text-muted-foreground mt-1">
                        Enter a rating between 1 and 5 stars
                      </Text>
                    </div>
                  </div>

                  <div>
                    <div className="flex items-center justify-between mb-1">
                      <Label className="font-medium">Description</Label>
                      {/* AI Translate button for description - Only show for non-base languages */}
                      {!isBaseLanguage && formData.description && (
                        <AITranslateButton
                          sourceText={formData.description}
                          targetLanguage={selectedLanguage}
                          fieldType="description"
                          onTranslate={(translatedText) => {
                            console.log(
                              `🤖 AI translated hotel description: "${translatedText}"`
                            );
                            setTranslatedValue("description", translatedText);
                          }}
                          context={{
                            entityType: "hotel",
                            entityName: formData.name,
                            location: formData.location || "",
                          }}
                          size="small"
                          variant="transparent"
                          showText={false}
                        />
                      )}
                    </div>
                    <TextareaField
                      id="description"
                      value={getTranslatedValue("description")}
                      onChange={(value) =>
                        setTranslatedValue("description", value)
                      }
                      placeholder="Enter a detailed description of the hotel"
                      rows={5}
                      contentType="description"
                      context={{
                        name: getTranslatedValue("name"),
                        type: "hotel",
                        destination: destinations.find(
                          (d) => d.id === form.watch("destination_id")
                        )?.name,
                      }}
                      disabled={!isFieldEnabled("description", isBaseLanguage)}
                    />
                  </div>
                </div>
              </div>

              <VisibilitySettings
                title="Visibility Settings"
                options={[
                  {
                    id: "is_active",
                    label: "Active",
                    description:
                      "When active, the hotel will be visible to users",
                    checked: form.watch("is_active"),
                    onChange: (checked) => form.setValue("is_active", checked),
                  },
                  {
                    id: "is_featured",
                    label: "Featured",
                    description:
                      "Featured hotels will be highlighted and shown prominently to users",
                    checked: form.watch("is_featured") || false,
                    onChange: (checked) =>
                      form.setValue("is_featured", checked),
                  },
                  {
                    id: "is_pets_allowed",
                    label: "Pets Allowed",
                    description: "When enabled, this hotel allows pets",
                    checked: form.watch("is_pets_allowed") || false,
                    onChange: (checked) =>
                      form.setValue("is_pets_allowed", checked),
                  },
                ]}
              />
            </div>
          </Tabs.Content>

          <Tabs.Content value="contact">
            <div className="space-y-6">
              <div className="bg-card p-6 rounded-lg shadow-sm border border-border transition-colors duration-200">
                <h3 className="text-lg font-medium mb-4 text-card-foreground">
                  Contact Information
                </h3>
                <div className="space-y-4">
                  {/* 3-column grid for Website, Email, and Phone */}
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                      <Label
                        htmlFor="website"
                        className="block mb-1 font-medium text-foreground"
                      >
                        Website
                      </Label>
                      <div className="relative">
                        <Input
                          id="website"
                          {...form.register("website")}
                          placeholder="e.g. https://www.grandhotel.com"
                          className="w-full bg-background border-input text-foreground placeholder:text-muted-foreground focus:border-ring focus:ring-ring/20"
                          disabled={!isBaseLanguage}
                        />
                      </div>
                    </div>

                    <div>
                      <Label
                        htmlFor="email"
                        className="block mb-1 font-medium text-foreground"
                      >
                        Email Address
                      </Label>
                      <div className="relative">
                        <Input
                          id="email"
                          type="email"
                          {...form.register("email", {
                            validate: {
                              validEmail: (value) => {
                                if (!value || value.trim() === "") {
                                  return true; // Allow empty values
                                }
                                const emailRegex = /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i;
                                if (!emailRegex.test(value)) {
                                  return "Please enter a valid email address";
                                }
                                return true;
                              }
                            }
                          })}
                          placeholder="e.g. <EMAIL>"
                          className={`w-full bg-background border-input text-foreground placeholder:text-muted-foreground focus:border-ring focus:ring-ring/20 ${
                            form.formState.errors.email ? "border-destructive focus:border-destructive" : ""
                          }`}
                          disabled={!isBaseLanguage}
                          onChange={(e) => {
                            form.setValue("email", e.target.value);
                            form.trigger("email"); // Trigger validation immediately
                          }}
                          onBlur={() => form.trigger("email")} // Trigger validation on blur
                        />
                        {form.formState.errors.email && (
                          <Text className="text-xs text-destructive mt-1">
                            {form.formState.errors.email.message}
                          </Text>
                        )}
                      </div>
                    </div>

                    <div>
                      <Label
                        htmlFor="phone_number"
                        className="block mb-1 font-medium text-foreground"
                      >
                        Phone Number
                      </Label>
                      <div className="relative">
                        <Input
                          id="phone_number"
                          {...form.register("phone_number", {
                            validate: {
                              validPhone: (value) => {
                                if (!value || value.trim() === "") {
                                  return true; // Allow empty values
                                }
                                // More comprehensive phone validation
                                const phoneRegex = /^[\+]?[(]?[\d\s\-\(\)]{7,20}$/;
                                if (!phoneRegex.test(value)) {
                                  return "Please enter a valid phone number";
                                }
                                return true;
                              }
                            }
                          })}
                          placeholder="e.g. +****************"
                          className={`w-full bg-background border-input text-foreground placeholder:text-muted-foreground focus:border-ring focus:ring-ring/20 ${
                            form.formState.errors.phone_number ? "border-destructive focus:border-destructive" : ""
                          }`}
                          disabled={!isBaseLanguage}
                          onChange={(e) => {
                            form.setValue("phone_number", e.target.value);
                            form.trigger("phone_number"); // Trigger validation immediately
                          }}
                          onBlur={() => form.trigger("phone_number")} // Trigger validation on blur
                        />
                        {form.formState.errors.phone_number && (
                          <Text className="text-xs text-destructive mt-1">
                            {form.formState.errors.phone_number.message}
                          </Text>
                        )}
                      </div>
                    </div>
                  </div>

                  <div>
                    <div className="flex items-center justify-between mb-1">
                      <Label htmlFor="address" className="font-medium">
                        Full Address
                      </Label>
                      {/* AI Translate button for address - Only show for non-base languages */}
                      {!isBaseLanguage && formData.address && (
                        <AITranslateButton
                          sourceText={formData.address}
                          targetLanguage={selectedLanguage}
                          fieldType="content"
                          onTranslate={(translatedText) => {
                            console.log(
                              `🤖 AI translated hotel address: "${translatedText}"`
                            );
                            setTranslatedValue("address", translatedText);
                          }}
                          context={{
                            entityType: "hotel",
                            entityName: formData.name,
                            location: formData.location || "",
                          }}
                          size="small"
                          variant="transparent"
                          showText={false}
                        />
                      )}
                    </div>
                    <Textarea
                      id="address"
                      value={getTranslatedValue("address")}
                      onChange={(e) =>
                        setTranslatedValue("address", e.target.value)
                      }
                      placeholder="Enter the complete address"
                      className="w-full bg-background border-input text-foreground placeholder:text-muted-foreground focus:border-ring focus:ring-ring/20 resize-none"
                      disabled={!isFieldEnabled("address", isBaseLanguage)}
                    />
                  </div>
                </div>
              </div>

              <div className="bg-card p-6 rounded-lg shadow-sm border border-border transition-colors duration-200">
                <h3 className="text-lg font-medium mb-4 text-card-foreground">
                  Additional Information
                </h3>
                <div className="space-y-4">
                  {/* Grid layout for check-in and check-out times */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label
                        htmlFor="check_in_time"
                        className="mb-1 font-medium flex items-center gap-1 text-foreground"
                      >
                        Check-in Time{" "}
                        <span className="text-destructive">*</span>
                        <Tooltip content="The standard check-in time for the hotel">
                          <Info size={14} className="text-muted-foreground" />
                        </Tooltip>
                      </Label>
                      <div className="relative">
                        <Input
                          id="check_in_time"
                          type="time"
                          {...form.register("check_in_time", {
                            required: true,
                          })}
                          placeholder="14:00"
                          className="w-full bg-background border-input text-foreground focus:border-ring focus:ring-ring/20"
                          defaultValue={form.watch("check_in_time") || "14:00"}
                          disabled={!isBaseLanguage}
                        />
                      </div>
                      <Text className="text-xs text-muted-foreground mt-1">
                        Standard check-in time in 24-hour format (e.g., 14:00
                        for 2:00 PM)
                      </Text>
                    </div>

                    <div>
                      <Label
                        htmlFor="check_out_time"
                        className="mb-1 font-medium flex items-center gap-1 text-foreground"
                      >
                        Check-out Time{" "}
                        <span className="text-destructive">*</span>
                        <Tooltip content="The standard check-out time for the hotel">
                          <Info size={14} className="text-muted-foreground" />
                        </Tooltip>
                      </Label>
                      <div className="relative">
                        <Input
                          id="check_out_time"
                          type="time"
                          {...form.register("check_out_time", {
                            required: true,
                          })}
                          placeholder="11:00"
                          className="w-full bg-background border-input text-foreground focus:border-ring focus:ring-ring/20"
                          defaultValue={form.watch("check_out_time") || "11:00"}
                          disabled={!isBaseLanguage}
                        />
                      </div>
                      <Text className="text-xs text-muted-foreground mt-1">
                        Standard check-out time in 24-hour format (e.g., 11:00
                        for 11:00 AM)
                      </Text>
                    </div>

                    {/* Third column placeholder for future fields */}
                    <div className="hidden md:block">
                      {/* Reserved space for potential third field */}
                    </div>
                  </div>

                  {/* <div>
                    <Label
                      htmlFor="currency"
                      className="block mb-1 font-medium"
                    >
                      Currency
                    </Label>
                    <div className="relative">
                      <DollarSign
                        size={16}
                        className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"
                      />
                      <Input
                        id="currency"
                        {...form.register("currency")}
                        placeholder="e.g. USD"
                        className="w-full "
                      />
                    </div>
                  </div> */}
                </div>
              </div>
            </div>
          </Tabs.Content>

          <Tabs.Content value="features">
            <div className="space-y-6">
              <div className="bg-card p-6 rounded-lg shadow-sm border border-border transition-colors duration-200">
                <div className="space-y-4">
                  <div>
                    <TranslatableTagInput
                      label="Tags"
                      baseTags={tags}
                      translatedTags={tagsTranslation.translatedValues}
                      onBaseTagsChange={setTags}
                      onTranslatedTagsChange={
                        tagsTranslation.setTranslatedValues
                      }
                      isBaseLanguage={tagsTranslation.isBaseLanguage}
                      disabled={!isFieldEnabled("tags", isBaseLanguage)}
                      placeholder="Type a tag and press Enter"
                      helpText="Add tags to categorize this hotel (e.g. luxury, business, family-friendly)"
                      currentLanguage={selectedLanguage}
                      hasUnsavedChanges={tagsTranslation.hasUnsavedChanges}
                      colorScheme="blue"
                      fieldType="tags"
                      entityType="hotel"
                      entityName={getTranslatedValue("name") || formData.name}
                      entityLocation={
                        getTranslatedValue("location") || formData.location
                      }
                      enableAITranslation={true}
                    />
                  </div>

                  <div>
                    <TranslatableTagInput
                      label="Amenities"
                      baseTags={amenities}
                      translatedTags={amenitiesTranslation.translatedValues}
                      onBaseTagsChange={setAmenities}
                      onTranslatedTagsChange={
                        amenitiesTranslation.setTranslatedValues
                      }
                      isBaseLanguage={amenitiesTranslation.isBaseLanguage}
                      disabled={!isFieldEnabled("amenities", isBaseLanguage)}
                      placeholder="Type an amenity and press Enter"
                      helpText="List amenities available at the hotel (e.g. WiFi, pool, gym, spa)"
                      currentLanguage={selectedLanguage}
                      hasUnsavedChanges={amenitiesTranslation.hasUnsavedChanges}
                      colorScheme="green"
                      fieldType="amenities"
                      entityType="hotel"
                      entityName={getTranslatedValue("name") || formData.name}
                      entityLocation={
                        getTranslatedValue("location") || formData.location
                      }
                      enableAITranslation={true}
                    />
                  </div>

                  <div>
                    <TranslatableTagInput
                      label="Rules"
                      baseTags={rules}
                      translatedTags={rulesTranslation.translatedValues}
                      onBaseTagsChange={setRules}
                      onTranslatedTagsChange={
                        rulesTranslation.setTranslatedValues
                      }
                      isBaseLanguage={rulesTranslation.isBaseLanguage}
                      disabled={!isFieldEnabled("rules", isBaseLanguage)}
                      placeholder="Type a rule and press Enter"
                      helpText="List rules and policies for the hotel (e.g. no smoking, check-in after 3pm)"
                      currentLanguage={selectedLanguage}
                      hasUnsavedChanges={rulesTranslation.hasUnsavedChanges}
                      colorScheme="orange"
                      fieldType="rules"
                      entityType="hotel"
                      entityName={getTranslatedValue("name") || formData.name}
                      entityLocation={
                        getTranslatedValue("location") || formData.location
                      }
                      enableAITranslation={true}
                    />
                  </div>

                  <div>
                    <TranslatableTagInput
                      label="Safety Measures"
                      baseTags={safetyMeasures}
                      translatedTags={
                        safetyMeasuresTranslation.translatedValues
                      }
                      onBaseTagsChange={setSafetyMeasures}
                      onTranslatedTagsChange={
                        safetyMeasuresTranslation.setTranslatedValues
                      }
                      isBaseLanguage={safetyMeasuresTranslation.isBaseLanguage}
                      disabled={
                        !isFieldEnabled("safety_measures", isBaseLanguage)
                      }
                      placeholder="Type a safety measure and press Enter"
                      helpText="List safety measures implemented at the hotel (e.g. 24/7 security, fire safety, sanitization)"
                      currentLanguage={selectedLanguage}
                      hasUnsavedChanges={
                        safetyMeasuresTranslation.hasUnsavedChanges
                      }
                      colorScheme="red"
                      fieldType="safety_measures"
                      entityType="hotel"
                      entityName={getTranslatedValue("name") || formData.name}
                      entityLocation={
                        getTranslatedValue("location") || formData.location
                      }
                      enableAITranslation={true}
                    />
                  </div>

                  <div>
                    <div className="flex items-center justify-between mb-1">
                      <Label className="font-medium">Additional Notes</Label>
                      {/* AI Translate button for notes - Only show for non-base languages */}
                      {!isBaseLanguage && formData.notes && (
                        <AITranslateButton
                          sourceText={formData.notes}
                          targetLanguage={selectedLanguage}
                          fieldType="description"
                          onTranslate={(translatedText) => {
                            console.log(
                              `🤖 AI translated hotel notes: "${translatedText}"`
                            );
                            setTranslatedValue("notes", translatedText);
                          }}
                          context={{
                            entityType: "hotel",
                            entityName: formData.name,
                            location: formData.location || "",
                          }}
                          size="small"
                          variant="transparent"
                          showText={false}
                        />
                      )}
                    </div>
                    <TextareaField
                      id="notes"
                      value={getTranslatedValue("notes")}
                      onChange={(value) => setTranslatedValue("notes", value)}
                      placeholder="Any additional information about the hotel"
                      rows={5}
                      contentType="description"
                      context={{
                        name: getTranslatedValue("name"),
                        type: "hotel notes",
                        description: getTranslatedValue("description"),
                      }}
                      helpText="Add any special instructions, policies, or other information about the hotel"
                      disabled={!isFieldEnabled("notes", isBaseLanguage)}
                    />
                  </div>
                </div>
              </div>
            </div>
          </Tabs.Content>

          <Tabs.Content value="media">
            <div className="space-y-6">
              <div className="bg-card p-6 rounded-lg shadow-sm border border-border transition-colors duration-200">
                <h3 className="text-lg font-medium mb-4 text-card-foreground">
                  Hotel Images
                </h3>
                <Text className="text-sm text-muted-foreground mb-4">
                  Upload images that showcase this hotel. The first image or the
                  one marked as thumbnail will be used as the main image.
                </Text>

                <HotelMediaSection form={form} />
              </div>
            </div>
          </Tabs.Content>
        </Tabs>
      </div>

      <div className="flex justify-end gap-2 p-4 border-t border-border bg-background/95 backdrop-blur-sm">
        <Button
          variant="secondary"
          onClick={closeModal}
          className="transition-all duration-200 hover:bg-secondary/80"
        >
          Cancel
        </Button>
        <Button
          variant="primary"
          onClick={handleSubmit}
          disabled={
            !form.watch("name") ||
            !form.watch("handle") ||
            !form.watch("destination_id") ||
            !form.watch("check_in_time") ||
            !form.watch("check_out_time") ||
            !!handleError ||
            Object.keys(form.formState.errors).length > 0 ||
            isSubmitting
          }
          isLoading={isSubmitting}
          className="transition-all duration-200"
        >
          {isSubmitting
            ? isSavingTranslations
              ? (() => {
                  const languagesToSave = Object.keys(
                    allTranslatedValues
                  ).filter((lang) => lang !== "en");
                  if (languagesToSave.length === 1) {
                    const langName =
                      tolgeeLanguages.find(
                        (lang) => lang.tag === languagesToSave[0]
                      )?.name || languagesToSave[0];
                    return `Saving ${langName} translations...`;
                  } else if (languagesToSave.length > 1) {
                    return `Saving translations for ${languagesToSave.length} languages...`;
                  } else {
                    return "Saving translations...";
                  }
                })()
              : `${isEdit ? "Updating" : "Creating"} hotel...`
            : isEdit
            ? "Update Hotel"
            : "Create Hotel"}
        </Button>
      </div>
    </>
  );
};

export default HotelFormModern;
