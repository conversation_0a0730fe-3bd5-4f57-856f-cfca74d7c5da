import React from "react";
import { Tabs } from "@camped-ai/ui";
import { FileText, History } from "lucide-react";

export type SupplierProductServiceTabId = "overview" | "cost-history";

export interface SupplierProductServiceTab {
  id: SupplierProductServiceTabId;
  label: string;
  icon: React.ComponentType<{ className?: string }>;
  count?: number;
}

interface SupplierProductServiceTabsProps {
  activeTab: SupplierProductServiceTabId;
  onTabChange: (tabId: SupplierProductServiceTabId) => void;
  tabs?: SupplierProductServiceTab[];
  className?: string;
  children: React.ReactNode;
}

const defaultTabs: SupplierProductServiceTab[] = [
  {
    id: "overview",
    label: "Overview",
    icon: FileText,
  },
  {
    id: "cost-history",
    label: "Cost History",
    icon: History,
  },
];

const SupplierProductServiceTabs: React.FC<SupplierProductServiceTabsProps> = ({
  activeTab,
  onTabChange,
  tabs = defaultTabs,
  className,
  children,
}) => {
  const handleValueChange = (value: string) => {
    onTabChange(value as SupplierProductServiceTabId);
  };

  return (
    <div className={className}>
      <Tabs value={activeTab} onValueChange={handleValueChange}>
        <Tabs.List>
          {tabs.map((tab) => {
            const Icon = tab.icon;
            return (
              <Tabs.Trigger key={tab.id} value={tab.id}>
                <div className="flex items-center gap-2">
                  <Icon className="w-4 h-4" />
                  <span>{tab.label}</span>
                  {tab.count !== undefined && (
                    <span className="ml-1 px-2 py-0.5 text-xs rounded-full bg-gray-100 text-gray-600 font-medium">
                      {tab.count}
                    </span>
                  )}
                </div>
              </Tabs.Trigger>
            );
          })}
        </Tabs.List>
        <div className="mt-2">{children}</div>
      </Tabs>
    </div>
  );
};

export default SupplierProductServiceTabs;
