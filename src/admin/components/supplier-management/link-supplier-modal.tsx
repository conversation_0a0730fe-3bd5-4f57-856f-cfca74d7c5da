import { useState, useEffect } from "react";
import {
  FocusModal,
  Button,
  Input,
  Select,
  Textarea,
  Text,
  Badge,
  Checkbox,
  toast,
} from "@camped-ai/ui";
import { Search, Building, Star } from "lucide-react";
import { useSuppliers } from "../../hooks/vendor-management/use-suppliers";
import { CURRENCIES } from "../../constants/supplier-form-options";
import {
  useCreateProductServiceSupplier,
  type CreateProductServiceSupplierInput,
} from "../../hooks/supplier-products-services/use-product-service-suppliers";

interface LinkSupplierModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  productServiceId: string;
  productServiceName: string;
}

interface SupplierOption {
  id: string;
  name: string;
  type: string;
  status: string;
  primary_contact_name?: string;
  primary_contact_email?: string;
}

export const LinkSupplierModal = ({
  open,
  onOpenChange,
  productServiceId,
  productServiceName,
}: LinkSupplierModalProps) => {
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedSupplier, setSelectedSupplier] =
    useState<SupplierOption | null>(null);
  const [formData, setFormData] = useState({
    cost: "",
    currency_code: "CHF",
    availability: "",
    max_capacity: "",
    season: "",
    valid_from: "",
    valid_until: "",
    notes: "",
    lead_time_days: "",
    minimum_order: "",
    is_preferred: false,
  });

  // API hooks
  const { data: suppliersData, isLoading: suppliersLoading } = useSuppliers({
    search: searchTerm || undefined,
    status: "active",
    limit: 50,
  });
  const createSupplierLink = useCreateProductServiceSupplier();

  const suppliers = suppliersData?.suppliers || [];

  // Reset form when modal opens/closes
  useEffect(() => {
    if (!open) {
      setSelectedSupplier(null);
      setSearchTerm("");
      setFormData({
        cost: "",
        currency_code: "CHF",
        availability: "",
        max_capacity: "",
        season: "",
        valid_from: "",
        valid_until: "",
        notes: "",
        lead_time_days: "",
        minimum_order: "",
        is_preferred: false,
      });
    }
  }, [open]);

  const handleSubmit = async () => {
    if (!selectedSupplier) {
      toast.error("Please select a supplier");
      return;
    }

    if (!formData.cost || !formData.availability) {
      toast.error("Please fill in required fields (Cost and Availability)");
      return;
    }

    const submitData: CreateProductServiceSupplierInput = {
      product_service_id: productServiceId,
      supplier_id: selectedSupplier.id,
      cost: parseFloat(formData.cost),
      currency_code: formData.currency_code,
      availability: formData.availability,
      max_capacity:
        formData.max_capacity && formData.max_capacity.trim()
          ? parseInt(formData.max_capacity)
          : undefined,
      season:
        formData.season && formData.season.trim() ? formData.season : undefined,
      valid_from:
        formData.valid_from && formData.valid_from.trim()
          ? new Date(formData.valid_from).toISOString()
          : undefined,
      valid_until:
        formData.valid_until && formData.valid_until.trim()
          ? new Date(formData.valid_until).toISOString()
          : undefined,
      notes:
        formData.notes && formData.notes.trim() ? formData.notes : undefined,
      lead_time_days:
        formData.lead_time_days && formData.lead_time_days.trim()
          ? parseInt(formData.lead_time_days)
          : undefined,
      minimum_order:
        formData.minimum_order && formData.minimum_order.trim()
          ? parseInt(formData.minimum_order)
          : undefined,
      is_preferred: formData.is_preferred,
    };

    console.log("Submitting supplier link data:", submitData);

    try {
      await createSupplierLink.mutateAsync(submitData);
      toast.success("Supplier linked successfully!");
      onOpenChange(false);
    } catch (error) {
      console.error("Error linking supplier:", error);
      // Error is handled by the mutation
    }
  };

  const handleInputChange = (field: string, value: string | boolean) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
  };

  return (
    <FocusModal open={open} onOpenChange={onOpenChange}>
      <FocusModal.Content className="max-w-2xl">
        <FocusModal.Header>
          <FocusModal.Title>Link Supplier</FocusModal.Title>
          <FocusModal.Description>
            Link a supplier to "{productServiceName}" and set pricing and
            availability information.
          </FocusModal.Description>
        </FocusModal.Header>

        <FocusModal.Body className="space-y-6">
          {/* Supplier Selection */}
          <div>
            <Text size="small" weight="plus" className="mb-2">
              Select Supplier *
            </Text>

            {!selectedSupplier ? (
              <div className="space-y-3">
                <Input
                  placeholder="Search suppliers by name..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  prefix={<Search className="h-4 w-4" />}
                />

                <div className="max-h-48 overflow-y-auto border rounded-lg">
                  {suppliersLoading ? (
                    <div className="p-4 space-y-2">
                      {Array.from({ length: 3 }).map((_, index) => (
                        <div
                          key={`skeleton-${index}`}
                          className="flex items-center space-x-3 p-2"
                        >
                          <div className="h-4 bg-gray-200 rounded animate-pulse w-1/3" />
                          <div className="h-4 bg-gray-200 rounded animate-pulse w-1/4" />
                        </div>
                      ))}
                    </div>
                  ) : suppliers.length === 0 ? (
                    <div className="p-4 text-center">
                      <Text className="text-ui-fg-subtle">
                        {searchTerm
                          ? "No suppliers found matching your search"
                          : "No active suppliers available"}
                      </Text>
                    </div>
                  ) : (
                    <div className="divide-y">
                      {suppliers.map((supplier) => (
                        <button
                          key={supplier.id}
                          className="w-full p-3 text-left hover:bg-gray-50 flex items-center justify-between"
                          onClick={() => setSelectedSupplier(supplier)}
                        >
                          <div className="flex items-center gap-3">
                            <Building className="h-4 w-4 text-gray-400" />
                            <div>
                              <div className="font-medium">{supplier.name}</div>
                              {supplier.primary_contact_name && (
                                <div className="text-sm text-ui-fg-subtle">
                                  Contact: {supplier.primary_contact_name}
                                </div>
                              )}
                            </div>
                          </div>
                          <Badge variant="grey" size="small">
                            {supplier.type}
                          </Badge>
                        </button>
                      ))}
                    </div>
                  )}
                </div>
              </div>
            ) : (
              <div className="flex items-center justify-between p-3 border rounded-lg bg-blue-50">
                <div className="flex items-center gap-3">
                  <Building className="h-4 w-4 text-blue-600" />
                  <div>
                    <div className="font-medium">{selectedSupplier.name}</div>
                    {selectedSupplier.primary_contact_name && (
                      <div className="text-sm text-ui-fg-subtle">
                        Contact: {selectedSupplier.primary_contact_name}
                      </div>
                    )}
                  </div>
                </div>
                <Button
                  variant="secondary"
                  size="small"
                  onClick={() => setSelectedSupplier(null)}
                >
                  Change
                </Button>
              </div>
            )}
          </div>

          {/* Pricing Information */}
          {selectedSupplier && (
            <>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Text size="small" weight="plus" className="mb-2">
                    Cost *
                  </Text>
                  <Input
                    type="number"
                    step="0.01"
                    placeholder="0.00"
                    value={formData.cost}
                    onChange={(e) => handleInputChange("cost", e.target.value)}
                  />
                </div>
                <div>
                  <Text size="small" weight="plus" className="mb-2">
                    Currency
                  </Text>
                  <Select
                    value={formData.currency_code}
                    onValueChange={(value) =>
                      handleInputChange("currency_code", value)
                    }
                  >
                    <Select.Trigger>
                      <Select.Value />
                    </Select.Trigger>
                    <Select.Content>
                      {CURRENCIES.map((currency) => (
                        <Select.Item
                          key={currency.value}
                          value={currency.value}
                        >
                          {currency.label}
                        </Select.Item>
                      ))}
                    </Select.Content>
                  </Select>
                </div>
              </div>

              {/* Availability Information */}
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Text size="small" weight="plus" className="mb-2">
                    Availability *
                  </Text>
                  <Input
                    placeholder="e.g., 10/day, Weekends, On demand"
                    value={formData.availability}
                    onChange={(e) =>
                      handleInputChange("availability", e.target.value)
                    }
                  />
                </div>
                <div>
                  <Text size="small" weight="plus" className="mb-2">
                    Max Capacity
                  </Text>
                  <Input
                    type="number"
                    placeholder="Maximum capacity"
                    value={formData.max_capacity}
                    onChange={(e) =>
                      handleInputChange("max_capacity", e.target.value)
                    }
                  />
                </div>
              </div>

              {/* Seasonal Information */}
              <div className="grid grid-cols-3 gap-4">
                <div>
                  <Text size="small" weight="plus" className="mb-2">
                    Season
                  </Text>
                  <Input
                    placeholder="e.g., Dec – Mar, Year-round"
                    value={formData.season}
                    onChange={(e) =>
                      handleInputChange("season", e.target.value)
                    }
                  />
                </div>
                <div>
                  <Text size="small" weight="plus" className="mb-2">
                    Valid From
                  </Text>
                  <Input
                    type="date"
                    value={formData.valid_from}
                    onChange={(e) =>
                      handleInputChange("valid_from", e.target.value)
                    }
                  />
                </div>
                <div>
                  <Text size="small" weight="plus" className="mb-2">
                    Valid Until
                  </Text>
                  <Input
                    type="date"
                    value={formData.valid_until}
                    onChange={(e) =>
                      handleInputChange("valid_until", e.target.value)
                    }
                  />
                </div>
              </div>

              {/* Additional Details */}
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Text size="small" weight="plus" className="mb-2">
                    Lead Time (days)
                  </Text>
                  <Input
                    type="number"
                    placeholder="Days notice required"
                    value={formData.lead_time_days}
                    onChange={(e) =>
                      handleInputChange("lead_time_days", e.target.value)
                    }
                  />
                </div>
                <div>
                  <Text size="small" weight="plus" className="mb-2">
                    Minimum Order
                  </Text>
                  <Input
                    type="number"
                    placeholder="Minimum order quantity"
                    value={formData.minimum_order}
                    onChange={(e) =>
                      handleInputChange("minimum_order", e.target.value)
                    }
                  />
                </div>
              </div>

              {/* Notes */}
              <div>
                <Text size="small" weight="plus" className="mb-2">
                  Notes
                </Text>
                <Textarea
                  placeholder="Internal notes about this supplier relationship..."
                  value={formData.notes}
                  onChange={(e) => handleInputChange("notes", e.target.value)}
                  rows={3}
                />
              </div>

              {/* Preferred Supplier */}
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="is_preferred"
                  checked={formData.is_preferred}
                  onCheckedChange={(checked) =>
                    handleInputChange("is_preferred", checked as boolean)
                  }
                />
                <label
                  htmlFor="is_preferred"
                  className="text-sm cursor-pointer flex items-center gap-2"
                >
                  <Star className="h-4 w-4 text-yellow-500" />
                  Mark as preferred supplier
                </label>
              </div>
            </>
          )}
        </FocusModal.Body>

        <FocusModal.Footer>
          <div className="flex items-center justify-end gap-2">
            <Button
              variant="secondary"
              onClick={() => onOpenChange(false)}
              disabled={createSupplierLink.isPending}
            >
              Cancel
            </Button>
            <Button
              onClick={handleSubmit}
              disabled={!selectedSupplier || createSupplierLink.isPending}
            >
              {createSupplierLink.isPending ? "Linking..." : "Link Supplier"}
            </Button>
          </div>
        </FocusModal.Footer>
      </FocusModal.Content>
    </FocusModal>
  );
};
