import React, { useState, useEffect, useRef } from "react";
import { Input, Text, Select, Button, DatePicker, Label, Prompt as NativePrompt } from "@camped-ai/ui";
import { Calculator, Plus, Search } from "lucide-react";
import { AddonLineItem } from "./addon-line-item";
import { useSuppliers } from "../../hooks/vendor-management/use-suppliers";
import { MultiSelect } from "../common/MultiSelect";
import { useAdminCurrencies } from "../../hooks/use-admin-currencies";
import { useExchangeRateForCurrencyPair } from "../../hooks/supplier-management/use-exchange-rates";

interface PricingData {
  commission?: number;
  grossPrice?: number; // Gross price
  supplierPrice?: number; // Net cost to supplier
  marginRate?: number;
  sellingPrice?: number;

  // Currency fields
  currency?: string; // Cost currency

  // Selling currency fields
  sellingCurrency?: string;
  sellingPriceSellingCurrency?: number;
  exchangeRate?: number;
  exchangeRateDate?: Date;

  // Addon line items
  addonLineItems?: AddonLineItem[];
}

interface PricingCalculatorProps {
  initialData?: PricingData;
  onChange?: (
    data: PricingData & {
      calculatedSupplierPrice?: number;
      calculatedNetPrice?: number;
      calculatedSellingPrice?: number;
      calculatedSellingPriceSellingCurrency?: number;
      calculatedTotalWithAddons?: number;
      calculatedTotalWithAddonsSellingCurrency?: number;
      addonsTotalPrice?: number;
      addonsTotalPriceSellingCurrency?: number;
      isPricingComplete?: boolean;
      pricingErrors?: string[];
    }
  ) => void;
  disabled?: boolean;
  costCurrency?: string; // The cost currency (e.g., CHF)
  onAddonSelectionChange?: (addonIds: string[]) => void; // Callback for when addons are selected from custom fields
  errors?: Record<string, string>; // Validation errors from parent form
  showValidation?: boolean; // Whether to show validation errors
  defaultSupplierId?: string; // Default supplier ID to pre-select for addons
  availableAddons?: { value: string; label: string }[]; // Available addons from custom fields
  // New props for product service name resolution
  productService?: any; // The product service object
  hotels?: any[]; // Hotels data for name resolution
  destinations?: any[]; // Destinations data for name resolution
  resolveProductServiceName?: (
    productService: any,
    hotels: any[],
    destinations: any[]
  ) => string; // Function to resolve product service names
  currentlySelectedAddons?: string[]; // Currently selected addon IDs from custom fields
  // Props for pre-populating modal date filters
  defaultActiveFrom?: string; // Default Active From date from parent form
  defaultActiveTo?: string; // Default Active To date from parent form
}

const PricingCalculator: React.FC<PricingCalculatorProps> = ({
  initialData = {},
  onChange,
  disabled = false,
  costCurrency = "CHF",
  onAddonSelectionChange,
  errors: validationErrors = {},
  showValidation = true,
  defaultSupplierId,
  availableAddons = [],
  currentlySelectedAddons = [],
  // New props for product service name resolution
  productService,
  hotels = [],
  destinations = [],
  resolveProductServiceName,
  // Props for pre-populating modal date filters
  defaultActiveFrom,
  defaultActiveTo,
}) => {
  const [pricingData, setPricingData] = useState<PricingData>({});
  const [errors, setErrors] = useState<string[]>([]);
  const [addonLineItems, setAddonLineItems] = useState<AddonLineItem[]>([]);
  const initializedRef = useRef(false);

  // Modal state for supplier offering selection
  const [showAddonModal, setShowAddonModal] = useState(false);
  const [selectedSupplierOfferingIds, setSelectedSupplierOfferingIds] =
    useState<string[]>([]);
  const [availableSupplierOfferings, setAvailableSupplierOfferings] = useState<
    any[]
  >([]);
  const [loadingSupplierOfferings, setLoadingSupplierOfferings] =
    useState(false);

  // Date filter state for supplier offerings modal
  const [activeFromDate, setActiveFromDate] = useState<Date | null>(null);
  const [activeToDate, setActiveToDate] = useState<Date | null>(null);
  const [dateFilterErrors, setDateFilterErrors] = useState<string[]>([]);

  // Fetch suppliers for addon supplier selection
  const { data: suppliersData } = useSuppliers({
    status: "Active",
    limit: 100,
  });
  const suppliers = suppliersData?.suppliers || [];

  // Fetch store currencies to get default currency
  const { defaultCurrency, currencyOptions } = useAdminCurrencies();
  const defaultCurrencyCode = defaultCurrency?.currency_code || "CHF";

  // Get exchange rate for currency conversion
  const { data: exchangeRateData } = useExchangeRateForCurrencyPair(
    pricingData.currency || costCurrency || defaultCurrencyCode,
    defaultCurrencyCode
  );

  // State to track supplier offerings for each addon
  const [addonSupplierOfferings, setAddonSupplierOfferings] = useState<
    Record<string, any[]>
  >({});

  // Update modal date filters when default values change (only if modal is not open)
  useEffect(() => {
    if (!showAddonModal) {
      setActiveFromDate(defaultActiveFrom ? new Date(defaultActiveFrom) : null);
      setActiveToDate(defaultActiveTo ? new Date(defaultActiveTo) : null);
    }
  }, [defaultActiveFrom, defaultActiveTo, showAddonModal]);

  // Function to get supplier offerings for a specific addon
  const getSupplierOfferingsForAddon = (addonId: string) => {
    return addonSupplierOfferings[addonId] || [];
  };

  // Function to fetch supplier offerings for a specific addon
  const fetchSupplierOfferingsForAddon = async (addonId: string) => {
    try {
      const response = await fetch(
        `/admin/supplier-management/supplier-offerings?product_service_id=${addonId}&status=active&limit=100`
      );

      if (!response.ok) {
        throw new Error("Failed to fetch supplier offerings");
      }

      const data = await response.json();
      const offerings = data.supplier_offerings || [];

      // Enrich offerings with supplier information
      const enrichedOfferings = offerings.map((offering: any) => {
        const supplier = suppliers.find((s) => s.id === offering.supplier_id);
        return {
          ...offering,
          supplier_name: supplier?.name || "Unknown Supplier",
          display_name: `${supplier?.name || "Unknown Supplier"} - ${
            offering.product_service?.name || "Unknown Product"
          }`,
        };
      });

      setAddonSupplierOfferings((prev) => ({
        ...prev,
        [addonId]: enrichedOfferings,
      }));

      return enrichedOfferings;
    } catch (error) {
      console.error("Error fetching supplier offerings for addon:", error);
      return [];
    }
  };

  // Fetch supplier offerings for addons when addon line items change
  useEffect(() => {
    addonLineItems.forEach((addon) => {
      if (addon.addon_id && !addonSupplierOfferings[addon.addon_id]) {
        fetchSupplierOfferingsForAddon(addon.addon_id);
      }
    });
  }, [addonLineItems, suppliers]);

  // Auto-populate pricing for addons with default supplier when suppliers are loaded
  useEffect(() => {
    if (defaultSupplierId && suppliers.length > 0) {
      addonLineItems.forEach(async (addon) => {
        // Only auto-populate if addon has default supplier but no pricing data yet
        if (
          addon.supplier_id === defaultSupplierId &&
          addon.addon_id &&
          !addon.gross_price &&
          !addon.commission &&
          !addon.net_cost
        ) {
          const pricing = await getSupplierOfferingPricing(
            addon.addon_id,
            defaultSupplierId
          );
          if (pricing) {
            setAddonLineItems((prev) =>
              prev.map((item) =>
                item.id === addon.id
                  ? {
                      ...item,
                      // Auto-populate pricing from supplier offering
                      gross_price: pricing.gross_price,
                      commission: pricing.commission,
                      net_cost: pricing.net_cost,
                      margin_rate: pricing.margin_rate,
                      selling_price: pricing.selling_price,
                      currency: pricing.currency,
                      selling_currency: pricing.selling_currency,
                      exchange_rate: pricing.exchange_rate,
                      selling_price_selling_currency:
                        pricing.selling_price_selling_currency,
                    }
                  : item
              )
            );
          }
        }
      });
    }
  }, [addonLineItems, defaultSupplierId, suppliers]);

  // Function to validate date filters
  const validateDateFilters = () => {
    const errors: string[] = [];

    if (activeFromDate && activeToDate && activeFromDate >= activeToDate) {
      errors.push("Active From date must be before Active To date");
    }

    setDateFilterErrors(errors);
    return errors.length === 0;
  };

  // Function to fetch all supplier offerings for available addons (for modal)
  const fetchAllSupplierOfferingsForAddons = async () => {
    if (availableAddons.length === 0) return;

    // Validate date filters before fetching
    if (!validateDateFilters()) {
      return;
    }

    setLoadingSupplierOfferings(true);
    try {
      const allOfferings: any[] = [];

      // Fetch supplier offerings for each available addon
      for (const addon of availableAddons) {
        // Build query parameters
        const params = new URLSearchParams({
          product_service_id: addon.value,
          status: 'active',
          limit: '100'
        });

        // Add date filters if provided
        if (activeFromDate) {
          params.append('active_from', activeFromDate.toISOString().split('T')[0]);
        }
        if (activeToDate) {
          params.append('active_to', activeToDate.toISOString().split('T')[0]);
        }

        const response = await fetch(
          `/admin/supplier-management/supplier-offerings?${params.toString()}`
        );

        if (response.ok) {
          const data = await response.json();
          const offerings = data.supplier_offerings || [];

          // Enrich offerings with supplier and addon information
          const enrichedOfferings = offerings.map((offering: any) => {
            const supplier = suppliers.find(
              (s) => s.id === offering.supplier_id
            );
            const productServiceName =
              offering.product_service?.name || addon.label;
            const displayName = `${
              supplier?.name || "Unknown Supplier"
            } - ${productServiceName}`;
            console.log(
              "Creating enriched offering with display_name:",
              displayName
            );
            return {
              ...offering,
              addon_id: addon.value,
              addon_name: addon.label,
              supplier_name: supplier?.name || "Unknown Supplier",
              display_name: displayName,
              // Create unique ID for selection
              selection_id: `${offering.id}_${addon.value}`,
            };
          });

          allOfferings.push(...enrichedOfferings);
        }
      }

      setAvailableSupplierOfferings(allOfferings);
    } catch (error) {
      console.error("Error fetching supplier offerings for modal:", error);
      setAvailableSupplierOfferings([]);
    } finally {
      setLoadingSupplierOfferings(false);
    }
  };

  // Function to get supplier offering pricing for an addon
  const getSupplierOfferingPricing = async (
    addonId: string,
    supplierId: string
  ) => {
    try {
      // Fetch supplier offerings for this addon and supplier
      const response = await fetch(
        `/admin/supplier-management/supplier-offerings?product_service_id=${addonId}&supplier_id=${supplierId}&status=active&limit=1`
      );

      if (!response.ok) {
        throw new Error("Failed to fetch supplier offering");
      }

      const data = await response.json();
      const offerings = data.supplier_offerings || [];

      if (offerings.length > 0) {
        const offering = offerings[0];
        return {
          gross_price: offering.gross_price
            ? parseFloat(offering.gross_price)
            : undefined,
          commission: offering.commission
            ? parseFloat(offering.commission) * 100
            : undefined, // Convert to percentage
          net_cost: offering.net_cost
            ? parseFloat(offering.net_cost)
            : undefined,
          margin_rate: offering.margin_rate
            ? parseFloat(offering.margin_rate) * 100
            : undefined, // Convert to percentage
          selling_price: offering.selling_price
            ? parseFloat(offering.selling_price)
            : undefined,
          currency: offering.currency,
          selling_currency: offering.selling_currency,
          exchange_rate: offering.exchange_rate
            ? parseFloat(offering.exchange_rate)
            : undefined,
          selling_price_selling_currency:
            offering.selling_price_selling_currency
              ? parseFloat(offering.selling_price_selling_currency)
              : undefined,
        };
      }

      return null;
    } catch (error) {
      console.error("Error fetching supplier offering pricing:", error);
      return null;
    }
  };

  // Initialize data only once when initialData has meaningful values
  useEffect(() => {
    // Check if initialData has any meaningful values and we haven't initialized yet
    const hasData =
      initialData.commission !== undefined ||
      initialData.grossPrice !== undefined ||
      initialData.supplierPrice !== undefined ||
      initialData.marginRate !== undefined ||
      initialData.sellingPrice !== undefined ||
      (initialData.addonLineItems && initialData.addonLineItems.length > 0);

    if (hasData && !initializedRef.current) {
      setPricingData({
        commission: initialData.commission,
        grossPrice: initialData.grossPrice,
        supplierPrice: initialData.supplierPrice,
        marginRate: initialData.marginRate,
        sellingPrice: initialData.sellingPrice,
        currency: initialData.currency,
        sellingCurrency: initialData.sellingCurrency,
        sellingPriceSellingCurrency: initialData.sellingPriceSellingCurrency,
        exchangeRate: initialData.exchangeRate,
        exchangeRateDate: initialData.exchangeRateDate,
      });

      if (initialData.addonLineItems) {
        // Set default supplier for addons that don't have a supplier selected
        const addonsWithDefaultSupplier = initialData.addonLineItems.map(
          (addon) => ({
            ...addon,
            supplier_id: addon.supplier_id || defaultSupplierId || undefined,
          })
        );
        setAddonLineItems(addonsWithDefaultSupplier);
      }

      initializedRef.current = true;
    }
  }, [initialData]);

  // Separate effect to handle addon line items updates after initialization
  useEffect(() => {
    if (initializedRef.current && initialData.addonLineItems) {
      // Set default supplier for addons that don't have a supplier selected
      const addonsWithDefaultSupplier = initialData.addonLineItems.map(
        (addon) => ({
          ...addon,
          supplier_id: addon.supplier_id || defaultSupplierId || undefined,
        })
      );
      setAddonLineItems(addonsWithDefaultSupplier);
    }
  }, [initialData.addonLineItems, defaultSupplierId]);

  // Calculate derived values with progressive auto-calculation
  const calculatePricing = (data: PricingData) => {
    const calculationErrors: string[] = [];
    let calculatedSupplierPrice: number | undefined;
    let calculatedSellingPrice: number | undefined;

    try {
      // Ensure numeric values, using 0 as default for empty fields during calculations
      const commission =
        typeof data.commission === "string"
          ? parseFloat(data.commission)
          : data.commission;
      const grossPrice =
        typeof data.grossPrice === "string"
          ? parseFloat(data.grossPrice)
          : data.grossPrice;
      const supplierPrice =
        typeof data.supplierPrice === "string"
          ? parseFloat(data.supplierPrice)
          : data.supplierPrice;
      const marginRate =
        typeof data.marginRate === "string"
          ? parseFloat(data.marginRate)
          : data.marginRate;

      // Progressive calculation: if we have gross price, calculate net cost
      if (grossPrice !== undefined && !isNaN(grossPrice) && grossPrice >= 0) {
        // Use commission value or default to 0 for calculation
        const effectiveCommission = (commission !== undefined && !isNaN(commission)) ? commission : 0;

        if (effectiveCommission < 0 || effectiveCommission > 1) {
          calculationErrors.push(
            "Commission must be between 0 and 1 (0% to 100%)"
          );
        } else {
          // Calculate Net Cost: Gross Price - (Gross Price × Commission)
          const differenceValue = grossPrice * effectiveCommission;
          calculatedSupplierPrice = grossPrice - differenceValue;
        }
      }

      // Use calculated net cost or manually entered net cost
      const finalSupplierPrice =
        calculatedSupplierPrice !== undefined
          ? calculatedSupplierPrice
          : (supplierPrice && !isNaN(supplierPrice) ? supplierPrice : undefined);

      // Progressive calculation: if we have net cost, calculate selling price
      if (finalSupplierPrice !== undefined && finalSupplierPrice >= 0) {
        // Use margin rate value or default to 0 for calculation
        const effectiveMarginRate = (marginRate !== undefined && !isNaN(marginRate)) ? marginRate : 0;

        if (effectiveMarginRate < 0 || effectiveMarginRate >= 1) {
          calculationErrors.push(
            "Margin rate must be between 0 and 1 (0% to 99.99%)"
          );
        } else {
          // Calculate Selling Price: Net Cost ÷ (1 - Margin Rate)
          calculatedSellingPrice = finalSupplierPrice / (1 - effectiveMarginRate);
        }
      }
    } catch (error) {
      calculationErrors.push("Error in pricing calculations");
    }

    // Calculate selling price in selling currency
    let calculatedSellingPriceSellingCurrency: number | undefined;
    if (calculatedSellingPrice) {
      const currentCurrency = data.currency || costCurrency || defaultCurrencyCode;

      if (currentCurrency === defaultCurrencyCode) {
        // Same currency, no conversion needed
        calculatedSellingPriceSellingCurrency = calculatedSellingPrice;
      } else {
        // Use exchange rate from API or fallback to manual rate
        const exchangeRate = exchangeRateData?.exchange_rate || data.exchangeRate || 1.0;
        const rate = typeof exchangeRate === "string" ? parseFloat(exchangeRate) : exchangeRate;

        if (!isNaN(rate)) {
          calculatedSellingPriceSellingCurrency = calculatedSellingPrice * rate;
        }
      }
    }

    // Pricing is complete if we have either:
    // 1. Commission + Gross Price (calculated net cost), OR
    // 2. Direct net cost entry
    // Plus margin rate and selling price
    // Calculate addon totals in cost currency
    const addonsTotalPrice = addonLineItems.reduce((sum, item) => {
      return sum + (item.selling_price || 0);
    }, 0);

    // Calculate addon totals in selling currency (each addon uses its own exchange rate)
    const addonsTotalPriceSellingCurrency = addonLineItems.reduce((sum, addon) => {
      let sellingPrice: number | undefined;
      let netCost: number | undefined;

      // Use manual net cost if available, otherwise use calculated net cost
      if (addon.net_cost_manual !== undefined) {
        netCost = addon.net_cost_manual;
      } else if (addon.gross_price && addon.commission) {
        netCost = addon.gross_price - addon.gross_price * (addon.commission / 100);
      } else {
        netCost = addon.net_cost;
      }

      if (netCost && addon.margin_rate) {
        sellingPrice = netCost / (1 - addon.margin_rate / 100);
      } else {
        sellingPrice = addon.selling_price;
      }

      // Calculate selling price in selling currency using addon's own exchange rate
      let sellingPriceInSellingCurrency: number | undefined;
      if (sellingPrice && addon.exchange_rate) {
        // If addon has same cost and selling currency, no conversion needed
        if (addon.currency === addon.selling_currency) {
          sellingPriceInSellingCurrency = sellingPrice;
        } else {
          // Use addon's own exchange rate for conversion
          sellingPriceInSellingCurrency = sellingPrice * addon.exchange_rate;
        }
      } else {
        sellingPriceInSellingCurrency = addon.selling_price_selling_currency;
      }

      return sum + (sellingPriceInSellingCurrency || 0);
    }, 0);

    // Calculate total with addons
    let calculatedTotalWithAddons: number | undefined;
    let calculatedTotalWithAddonsSellingCurrency: number | undefined;

    if (calculatedSellingPrice !== undefined) {
      calculatedTotalWithAddons = calculatedSellingPrice + addonsTotalPrice;

      // Calculate total in selling currency by summing main product and addons in their respective selling currencies
      if (calculatedSellingPriceSellingCurrency !== undefined) {
        calculatedTotalWithAddonsSellingCurrency =
          calculatedSellingPriceSellingCurrency + addonsTotalPriceSellingCurrency;
      }
    }

    const hasNetCost = !!(calculatedSupplierPrice || data.supplierPrice);
    const isPricingComplete = !!(
      hasNetCost &&
      data.marginRate !== undefined &&
      calculatedSellingPrice
    );

    return {
      calculatedSupplierPrice,
      calculatedSellingPrice,
      calculatedSellingPriceSellingCurrency,
      calculatedTotalWithAddons,
      calculatedTotalWithAddonsSellingCurrency,
      addonsTotalPrice,
      addonsTotalPriceSellingCurrency,
      isPricingComplete,
      pricingErrors: calculationErrors,
    };
  };

  // Update exchange rate when currency changes or exchange rate data is fetched
  useEffect(() => {
    if (exchangeRateData && exchangeRateData.exchange_rate) {
      setPricingData((prev) => ({
        ...prev,
        exchangeRate: typeof exchangeRateData.exchange_rate === 'string'
          ? parseFloat(exchangeRateData.exchange_rate)
          : exchangeRateData.exchange_rate,
        exchangeRateDate: exchangeRateData.date ? new Date(exchangeRateData.date) : undefined,
      }));
    }
  }, [exchangeRateData]);

  // Update calculations when data changes
  useEffect(() => {
    const calculations = calculatePricing(pricingData);
    setErrors(calculations.pricingErrors);

    if (onChange) {
      onChange({
        ...pricingData,
        addonLineItems,
        ...calculations,
      });
    }
  }, [pricingData, addonLineItems, onChange]);

  const handleInputChange = (
    field: keyof PricingData,
    value: number | undefined
  ) => {
    setPricingData((prev) => {
      const newData = {
        ...prev,
        [field]: value,
      };

      // Progressive auto-calculation based on field changes
      if (field === "grossPrice" && value !== undefined && !isNaN(value) && value >= 0) {
        // Auto-calculate net cost when gross price changes
        const effectiveCommission = (prev.commission !== undefined && !isNaN(prev.commission)) ? prev.commission : 0;
        const calculatedNetCost = value - (value * effectiveCommission);

        // Clear manual supplier price since we're auto-calculating
        newData.supplierPrice = undefined;

        // Auto-calculate selling price if we have margin rate
        const effectiveMarginRate = (prev.marginRate !== undefined && !isNaN(prev.marginRate)) ? prev.marginRate : 0;
        if (effectiveMarginRate >= 0 && effectiveMarginRate < 1) {
          // Don't auto-set selling price, let the calculation effect handle it
        }
      }

      if (field === "commission" && prev.grossPrice !== undefined && !isNaN(prev.grossPrice) && prev.grossPrice >= 0) {
        // Auto-calculate net cost when commission changes
        const effectiveCommission = (value !== undefined && !isNaN(value)) ? value : 0;
        const calculatedNetCost = prev.grossPrice - (prev.grossPrice * effectiveCommission);

        // Clear manual supplier price since we're auto-calculating
        newData.supplierPrice = undefined;
      }

      if (field === "marginRate" && value !== undefined && !isNaN(value)) {
        // Auto-calculate selling price when margin rate changes
        // The calculation will be handled by the effect that watches pricingData changes
      }

      return newData;
    });
  };

  // Handle currency change with automatic exchange rate application
  const handleCurrencyChange = (newCurrency: string) => {
    setPricingData((prev) => ({
      ...prev,
      currency: newCurrency,
      // Update exchange rate data when currency changes
      exchangeRate: exchangeRateData?.exchange_rate
        ? (typeof exchangeRateData.exchange_rate === 'string'
           ? parseFloat(exchangeRateData.exchange_rate)
           : exchangeRateData.exchange_rate)
        : 1.0,
      exchangeRateDate: exchangeRateData?.date ? new Date(exchangeRateData.date) : undefined,
    }));
  };

  // Handle addon input changes
  const handleAddonInputChange = async (
    addonId: string,
    field: keyof AddonLineItem,
    value: any
  ) => {
    // If supplier offering is being changed, auto-populate pricing from the selected offering
    if (field === "supplier_offering_id" && value) {
      const addon = addonLineItems.find((item) => item.id === addonId);
      if (addon?.addon_id) {
        // Find the selected offering from our cached data
        const availableOfferings = getSupplierOfferingsForAddon(addon.addon_id);
        const selectedOffering = availableOfferings.find(
          (offering) => offering.id === value
        );

        if (selectedOffering) {
          // Extract pricing data from the supplier offering
          const pricing = {
            gross_price: selectedOffering.gross_price
              ? parseFloat(selectedOffering.gross_price.toString())
              : undefined,
            commission: selectedOffering.commission
              ? parseFloat(selectedOffering.commission.toString()) * 100
              : undefined, // Convert to percentage
            net_cost: selectedOffering.net_cost
              ? parseFloat(selectedOffering.net_cost.toString())
              : undefined,
            margin_rate: selectedOffering.margin_rate
              ? parseFloat(selectedOffering.margin_rate.toString()) * 100
              : undefined, // Convert to percentage
            selling_price: selectedOffering.selling_price
              ? parseFloat(selectedOffering.selling_price.toString())
              : undefined,
            currency: selectedOffering.currency,
            selling_currency: selectedOffering.selling_currency,
            exchange_rate: selectedOffering.exchange_rate
              ? parseFloat(selectedOffering.exchange_rate.toString())
              : undefined,
            selling_price_selling_currency:
              selectedOffering.selling_price_selling_currency
                ? parseFloat(
                    selectedOffering.selling_price_selling_currency.toString()
                  )
                : undefined,
          };

          setAddonLineItems((prev) =>
            prev.map((item) =>
              item.id === addonId
                ? {
                    ...item,
                    [field]: value,
                    // Auto-populate pricing from supplier offering
                    gross_price: pricing.gross_price,
                    commission: pricing.commission,
                    net_cost: pricing.net_cost,
                    net_cost_manual: undefined, // Clear manual override
                    margin_rate: pricing.margin_rate,
                    selling_price: pricing.selling_price,
                    currency: pricing.currency,
                    selling_currency: pricing.selling_currency,
                    exchange_rate: pricing.exchange_rate,
                    selling_price_selling_currency:
                      pricing.selling_price_selling_currency,
                  }
                : item
            )
          );
          return;
        }
      }
    }

    // Legacy support: If supplier is being changed, auto-populate pricing from supplier offering
    if (field === "supplier_id" && value) {
      const addon = addonLineItems.find((item) => item.id === addonId);
      if (addon?.addon_id) {
        const pricing = await getSupplierOfferingPricing(addon.addon_id, value);
        if (pricing) {
          setAddonLineItems((prev) =>
            prev.map((item) =>
              item.id === addonId
                ? {
                    ...item,
                    [field]: value,
                    // Auto-populate pricing from supplier offering
                    gross_price: pricing.gross_price,
                    commission: pricing.commission,
                    net_cost: pricing.net_cost,
                    net_cost_manual: undefined, // Clear manual override
                    margin_rate: pricing.margin_rate,
                    selling_price: pricing.selling_price,
                    currency: pricing.currency,
                    selling_currency: pricing.selling_currency,
                    exchange_rate: pricing.exchange_rate,
                    selling_price_selling_currency:
                      pricing.selling_price_selling_currency,
                  }
                : item
            )
          );
          return;
        }
      }
    }

    // Default behavior for other field changes with progressive auto-calculation
    setAddonLineItems((prev) =>
      prev.map((addon) => {
        if (addon.id !== addonId) return addon;

        const updatedAddon = { ...addon, [field]: value };

        // Progressive auto-calculation for addon line items
        if (field === "gross_price" && value !== undefined && !isNaN(value) && value >= 0) {
          // Auto-calculate net cost when gross price changes
          const effectiveCommission = (addon.commission !== undefined && !isNaN(addon.commission)) ? addon.commission / 100 : 0;
          const calculatedNetCost = value - (value * effectiveCommission);
          updatedAddon.net_cost = calculatedNetCost;
          updatedAddon.net_cost_manual = undefined; // Clear manual override

          // Auto-calculate selling price if we have margin rate
          const effectiveMarginRate = (addon.margin_rate !== undefined && !isNaN(addon.margin_rate)) ? addon.margin_rate / 100 : 0;
          if (effectiveMarginRate >= 0 && effectiveMarginRate < 1) {
            updatedAddon.selling_price = calculatedNetCost / (1 - effectiveMarginRate);
          }
        }

        if (field === "commission" && addon.gross_price !== undefined && !isNaN(addon.gross_price) && addon.gross_price >= 0) {
          // Auto-calculate net cost when commission changes
          const effectiveCommission = (value !== undefined && !isNaN(value)) ? value / 100 : 0;
          const calculatedNetCost = addon.gross_price - (addon.gross_price * effectiveCommission);
          updatedAddon.net_cost = calculatedNetCost;
          updatedAddon.net_cost_manual = undefined; // Clear manual override

          // Auto-calculate selling price if we have margin rate
          const effectiveMarginRate = (addon.margin_rate !== undefined && !isNaN(addon.margin_rate)) ? addon.margin_rate / 100 : 0;
          if (effectiveMarginRate >= 0 && effectiveMarginRate < 1) {
            updatedAddon.selling_price = calculatedNetCost / (1 - effectiveMarginRate);
          }
        }

        if (field === "margin_rate" && value !== undefined && !isNaN(value)) {
          // Auto-calculate selling price when margin rate changes
          const netCost = updatedAddon.net_cost || 0;
          const effectiveMarginRate = value / 100;
          if (effectiveMarginRate >= 0 && effectiveMarginRate < 1 && netCost > 0) {
            updatedAddon.selling_price = netCost / (1 - effectiveMarginRate);
          }
        }

        return updatedAddon;
      })
    );
  };

  const calculations = calculatePricing(pricingData);

  // Debug logging (removed to prevent infinite calls)

  // Handle supplier offering selection from modal
  const handleAddonModalSave = async () => {
    if (selectedSupplierOfferingIds.length === 0) {
      setShowAddonModal(false);
      return;
    }

    try {
      // Convert supplier offering selections to addon line items
      const newLineItems: any[] = [];

      for (const selectionId of selectedSupplierOfferingIds) {
        const selectedOffering = availableSupplierOfferings.find(
          (offering) => offering.selection_id === selectionId
        );

        if (selectedOffering) {
          console.log("Selected offering:", selectedOffering);
          console.log("Display name:", selectedOffering.display_name);

          // Extract pricing data from the supplier offering
          const pricing = {
            gross_price: selectedOffering.gross_price
              ? parseFloat(selectedOffering.gross_price.toString())
              : undefined,
            commission: selectedOffering.commission
              ? parseFloat(selectedOffering.commission.toString()) * 100
              : undefined, // Convert to percentage
            net_cost: selectedOffering.net_cost
              ? parseFloat(selectedOffering.net_cost.toString())
              : undefined,
            margin_rate: selectedOffering.margin_rate
              ? parseFloat(selectedOffering.margin_rate.toString()) * 100
              : undefined, // Convert to percentage
            selling_price: selectedOffering.selling_price
              ? parseFloat(selectedOffering.selling_price.toString())
              : undefined,
            currency: selectedOffering.currency,
          };

          const lineItem = {
            id: `addon_${Date.now()}_${Math.random()
              .toString(36)
              .substring(2, 11)}`,
            addon_id: selectedOffering.addon_id,
            name: selectedOffering.addon_name,
            supplier_id: selectedOffering.supplier_id,
            supplier_name: selectedOffering.supplier_name,
            supplier_offering_id: selectedOffering.id,
            supplier_offering_display_name: (() => {
              const displayName =
                selectedOffering.display_name ||
                `${selectedOffering.supplier_name} - ${selectedOffering.addon_name}`;
              console.log(
                "Setting supplier_offering_display_name to:",
                displayName
              );
              return displayName;
            })(),

            // Auto-populate pricing from supplier offering
            gross_price: pricing.gross_price,
            commission: pricing.commission,
            net_cost: pricing.net_cost,
            net_cost_manual: undefined,
            margin_rate: pricing.margin_rate,
            selling_price: pricing.selling_price,
            currency: pricing.currency,

            is_auto_populated: true,
            is_manual: false,
          };

          newLineItems.push(lineItem);
        }
      }

      // Add new line items to existing ones
      setAddonLineItems((prev) => [...prev, ...newLineItems]);

      // Also call the parent's addon selection change handler with unique addon IDs
      if (onAddonSelectionChange) {
        const uniqueAddonIds = [
          ...new Set(newLineItems.map((item) => item.addon_id)),
        ];
        const allAddonIds = [...currentlySelectedAddons, ...uniqueAddonIds];
        onAddonSelectionChange([...new Set(allAddonIds)]);
      }

      // Reset modal state
      setSelectedSupplierOfferingIds([]);
      setShowAddonModal(false);
    } catch (error) {
      console.error("Error adding supplier offering rows:", error);
    }
  };

  const handleAddonModalCancel = () => {
    setSelectedSupplierOfferingIds([]);
    setActiveFromDate(defaultActiveFrom ? new Date(defaultActiveFrom) : null);
    setActiveToDate(defaultActiveTo ? new Date(defaultActiveTo) : null);
    setDateFilterErrors([]);
    setShowAddonModal(false);
  };

  // Handle Find button click
  const handleFindSupplierOfferings = async () => {
    await fetchAllSupplierOfferingsForAddons();
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <Calculator className="h-5 w-5" />
          <Text weight="plus" size="large">
            Pricing Calculator
          </Text>
        </div>
        {availableAddons.length > 0 && (
          <Button
            type="button"
            variant="secondary"
            size="small"
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
              // Initialize modal with default values from parent form
              setSelectedSupplierOfferingIds([]);
              setActiveFromDate(defaultActiveFrom ? new Date(defaultActiveFrom) : null);
              setActiveToDate(defaultActiveTo ? new Date(defaultActiveTo) : null);
              setDateFilterErrors([]);
              setAvailableSupplierOfferings([]);
              setShowAddonModal(true);
            }}
            disabled={disabled}
          >
            <Plus className="h-4 w-4" />
            Add Rows
          </Button>
        )}
      </div>

      {/* Basic Pricing Fields - Horizontal Table Format */}
      <div className="overflow-x-auto">
        <table className="w-full border-collapse border border-ui-border-base">
          <thead>
            <tr className="bg-ui-bg-subtle">
              <th className="border border-ui-border-base px-2 py-2 text-left text-sm font-medium min-w-[250px]">
                Product/Service
              </th>
              <th className="border border-ui-border-base px-2 py-2 text-left text-sm font-medium min-w-[140px]">
                Cost Currency <span className="text-red-500">*</span>
              </th>
              <th className="border border-ui-border-base px-2 py-2 text-left text-sm font-medium min-w-[120px]">
                Gross Cost <span className="text-red-500">*</span>
              </th>
              <th className="border border-ui-border-base px-2 py-2 text-left text-sm font-medium min-w-[120px]">
                Commission (%)
              </th>
              <th className="border border-ui-border-base px-2 py-2 text-left text-sm font-medium min-w-[120px]">
                Net Cost <span className="text-red-500">*</span>
              </th>
              <th className="border border-ui-border-base px-2 py-2 text-left text-sm font-medium min-w-[120px]">
                Margin Rate (%) <span className="text-red-500">*</span>
              </th>
              <th className="border border-ui-border-base px-2 py-2 text-left text-sm font-medium min-w-[120px]">
                Selling Price (Cost Currency) <span className="text-red-500">*</span>
              </th>
              <th className="border border-ui-border-base px-2 py-2 text-left text-sm font-medium min-w-[160px]">
                Selling Price ({defaultCurrencyCode}){" "}
                <span className="text-red-500">*</span>
              </th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td className="border border-ui-border-base px-2 py-2">
                <div className="space-y-1">
                  <Text weight="plus" size="small" className="text-gray-900">
                    {(() => {
                      // Use resolved product service name if available
                      if (productService && resolveProductServiceName) {
                        return resolveProductServiceName(
                          productService,
                          hotels,
                          destinations
                        );
                      }
                      // Fallback to product service name or default
                      return productService?.name || "Main Product/Service";
                    })()}
                  </Text>
                </div>
              </td>
              <td className="border border-ui-border-base px-2 py-2">
                <div className="space-y-1">
                  <Select
                    value={pricingData.currency || costCurrency || defaultCurrencyCode}
                    onValueChange={handleCurrencyChange}
                    disabled={disabled}
                  >
                    <Select.Trigger
                      className={`w-full text-sm ${
                        showValidation && validationErrors.currency
                          ? "border-red-500 focus:border-red-500"
                          : ""
                      }`}
                    >
                      <Select.Value placeholder="Currency" />
                    </Select.Trigger>
                    <Select.Content>
                      {currencyOptions.map((currency) => (
                        <Select.Item key={currency.value} value={currency.value}>
                          {currency.label}
                        </Select.Item>
                      ))}
                    </Select.Content>
                  </Select>
                  {showValidation && validationErrors.currency && (
                    <Text size="small" className="text-red-500">
                      {validationErrors.currency}
                    </Text>
                  )}
                  {/* Show exchange rate info when different from default currency */}
                  {exchangeRateData && exchangeRateData.exchange_rate &&
                   (typeof exchangeRateData.exchange_rate === 'string'
                    ? parseFloat(exchangeRateData.exchange_rate)
                    : exchangeRateData.exchange_rate) !== 1.0 && (
                    <Text size="small" className="text-ui-fg-subtle">
                      Rate: {(typeof exchangeRateData.exchange_rate === 'string'
                        ? parseFloat(exchangeRateData.exchange_rate)
                        : exchangeRateData.exchange_rate).toFixed(4)} (vs {defaultCurrencyCode})
                    </Text>
                  )}
                </div>
              </td>
              {/* Gross Price */}
              <td className="border border-ui-border-base px-2 py-2">
                <div className="space-y-1">
                  <Input
                    id="grossPrice"
                    type="number"
                    step="0.01"
                    min="0"
                    placeholder="0.00"
                    value={pricingData.grossPrice?.toString() || ""}
                    onChange={(e) => {
                      const value = e.target.value
                        ? parseFloat(e.target.value)
                        : undefined;
                      handleInputChange("grossPrice", value);
                    }}
                    disabled={disabled}
                    className={`w-full text-sm ${
                      showValidation && validationErrors.gross_price
                        ? "border-red-500 focus:border-red-500"
                        : ""
                    }`}
                  />
                  {showValidation && validationErrors.gross_price && (
                    <Text size="small" className="text-red-500">
                      {validationErrors.gross_price}
                    </Text>
                  )}
                </div>
              </td>
              <td className="border border-ui-border-base px-2 py-2">
                <div className="space-y-1">
                  <Input
                    id="commission"
                    type="number"
                    step="0.01"
                    min="0"
                    max="100"
                    placeholder="0"
                    value={
                      pricingData.commission !== undefined
                        ? (pricingData.commission * 100).toString()
                        : ""
                    }
                    onChange={(e) => {
                      const value =
                        e.target.value !== ""
                          ? parseFloat(e.target.value) / 100
                          : undefined;
                      handleInputChange("commission", value);
                    }}
                    disabled={disabled}
                    className={`w-full text-sm ${
                      showValidation && validationErrors.commission
                        ? "border-red-500 focus:border-red-500"
                        : ""
                    }`}
                  />
                  {showValidation && validationErrors.commission && (
                    <Text size="small" className="text-red-500">
                      {validationErrors.commission}
                    </Text>
                  )}
                </div>
              </td>
              <td className="border border-ui-border-base px-2 py-2">
                <div className="space-y-1">
                  <Input
                    id="supplierPrice"
                    type="number"
                    step="0.01"
                    min="0"
                    placeholder={
                      calculations.calculatedSupplierPrice
                        ? calculations.calculatedSupplierPrice.toFixed(2)
                        : "0.00"
                    }
                    value={
                      pricingData.supplierPrice?.toString() ||
                      (calculations.calculatedSupplierPrice
                        ? calculations.calculatedSupplierPrice.toString()
                        : "")
                    }
                    onChange={(e) => {
                      const value = e.target.value
                        ? parseFloat(e.target.value)
                        : undefined;
                      handleInputChange("supplierPrice", value);
                    }}
                    disabled={
                      disabled ||
                      (pricingData.commission !== undefined &&
                        pricingData.grossPrice !== undefined)
                    }
                    className={`w-full text-sm ${
                      pricingData.commission !== undefined &&
                      pricingData.grossPrice !== undefined
                        ? "bg-ui-bg-subtle cursor-not-allowed"
                        : showValidation && validationErrors.net_cost
                        ? "border-red-500 focus:border-red-500"
                        : ""
                    }`}
                  />
                  {showValidation && validationErrors.net_cost && (
                    <Text size="small" className="text-red-500">
                      {validationErrors.net_cost}
                    </Text>
                  )}
                </div>
              </td>
              <td className="border border-ui-border-base px-2 py-2">
                <div className="space-y-1">
                  <Input
                    id="marginRate"
                    type="number"
                    step="0.01"
                    min="0"
                    max="99.99"
                    placeholder="0"
                    value={
                      pricingData.marginRate !== undefined
                        ? (pricingData.marginRate * 100).toString()
                        : ""
                    }
                    onChange={(e) => {
                      const value =
                        e.target.value !== ""
                          ? parseFloat(e.target.value) / 100
                          : undefined;
                      handleInputChange("marginRate", value);
                    }}
                    disabled={disabled}
                    className={`w-full text-sm ${
                      showValidation && validationErrors.margin_rate
                        ? "border-red-500 focus:border-red-500"
                        : ""
                    }`}
                  />
                  {showValidation && validationErrors.margin_rate && (
                    <Text size="small" className="text-red-500">
                      {validationErrors.margin_rate}
                    </Text>
                  )}
                </div>
              </td>
              <td className="border border-ui-border-base px-2 py-2 bg-ui-bg-subtle">
                <Text weight="plus" size="small">
                  {(() => {
                    const value = calculations.calculatedSellingPrice;
                    if (value == null) return "0.00";
                    const numValue =
                      typeof value === "string" ? parseFloat(value) : value;
                    return isNaN(numValue) ? "0.00" : numValue.toFixed(2);
                  })()}
                </Text>
              </td>
              <td className="border border-ui-border-base px-2 py-2 bg-ui-bg-subtle">
                <Text weight="plus" size="small">
                  {(() => {
                    const value =
                      calculations.calculatedSellingPriceSellingCurrency;
                    if (value == null) return "0.00";
                    const numValue =
                      typeof value === "string" ? parseFloat(value) : value;
                    return isNaN(numValue) ? "0.00" : numValue.toFixed(2);
                  })()}
                </Text>
              </td>
            </tr>

            {/* Addon Rows - Editable */}
            {addonLineItems.map((addon) => (
              <tr key={addon.id} className="">
                {/* Supplier Offering Selection (Combined Line Item + Supplier) */}
                <td className="border border-ui-border-base px-2 py-2">
                  {(() => {
                    return (
                      <div className="space-y-2 text-xs bg-white">
                        {/* Addon name display */}

                        {addon.supplier_name + " - " + addon.name ||
                          "No supplier offering selected"}
                      </div>
                    );
                  })()}
                </td>
                {/* Cost Currency - Each addon maintains its own currency */}
                <td className="border border-ui-border-base px-2 py-2 bg-ui-bg-subtle">
                  <Text weight="plus" size="small">
                    {addon.currency || defaultCurrencyCode}
                  </Text>
                </td>
                {/* Gross Cost */}
                <td className="border border-ui-border-base px-2 py-2">
                  <Input
                    id={`addon-gross-price-${addon.id}`}
                    type="number"
                    value={addon.gross_price?.toString() || ""}
                    onChange={(e) =>
                      handleAddonInputChange(
                        addon.id,
                        "gross_price",
                        parseFloat(e.target.value) || undefined
                      )
                    }
                    placeholder="0.00"
                    disabled={disabled}
                    className="w-full text-sm bg-white"
                    step="0.01"
                    min="0"
                  />
                </td>
                <td className="border border-ui-border-base px-2 py-2">
                  <Input
                    id={`addon-commission-${addon.id}`}
                    type="number"
                    value={addon.commission?.toString() || ""}
                    onChange={(e) =>
                      handleAddonInputChange(
                        addon.id,
                        "commission",
                        parseFloat(e.target.value) || undefined
                      )
                    }
                    placeholder="0.00"
                    disabled={disabled}
                    className="w-full text-sm bg-white"
                    step="0.01"
                    min="0"
                    max="100"
                  />
                </td>
                <td className="border border-ui-border-base px-2 py-2">
                  <Input
                    id={`addon-net-cost-${addon.id}`}
                    type="number"
                    value={(() => {
                      // Priority: manual value first, then calculated value, then fallback to net_cost
                      if (addon.net_cost_manual !== undefined) {
                        return addon.net_cost_manual.toString();
                      }
                      if (addon.gross_price && addon.commission) {
                        const calculatedNetCost =
                          addon.gross_price -
                          addon.gross_price * (addon.commission / 100);
                        return calculatedNetCost.toString();
                      }
                      return addon.net_cost !== undefined
                        ? addon.net_cost.toString()
                        : "";
                    })()}
                    onChange={(e) =>
                      handleAddonInputChange(
                        addon.id,
                        "net_cost_manual",
                        parseFloat(e.target.value) || undefined
                      )
                    }
                    placeholder={(() => {
                      if (addon.gross_price && addon.commission) {
                        const calculatedNetCost =
                          addon.gross_price -
                          addon.gross_price * (addon.commission / 100);
                        return calculatedNetCost.toFixed(2);
                      }
                      return "0.00";
                    })()}
                    disabled={
                      disabled ||
                      (addon.gross_price !== undefined &&
                        addon.commission !== undefined)
                    }
                    className={`w-full text-sm ${
                      addon.gross_price !== undefined &&
                      addon.commission !== undefined
                        ? "bg-ui-bg-subtle cursor-not-allowed"
                        : "bg-white"
                    }`}
                    step="0.01"
                    min="0"
                  />
                </td>
                <td className="border border-ui-border-base px-2 py-2">
                  <Input
                    id={`addon-margin-rate-${addon.id}`}
                    type="number"
                    value={addon.margin_rate?.toString() || ""}
                    onChange={(e) =>
                      handleAddonInputChange(
                        addon.id,
                        "margin_rate",
                        parseFloat(e.target.value) || undefined
                      )
                    }
                    placeholder="0.00"
                    disabled={disabled}
                    className="w-full text-sm bg-white"
                    step="0.01"
                    min="0"
                    max="100"
                  />
                </td>
                <td className="border border-ui-border-base px-2 py-2">
                  <Text size="small" className="py-1">
                    {(() => {
                      // Calculate selling price: Net Cost ÷ (1 - Margin Rate)
                      let netCost: number | undefined;

                      // Use manual net cost if available, otherwise use calculated net cost
                      if (addon.net_cost_manual !== undefined) {
                        netCost = addon.net_cost_manual;
                      } else if (addon.gross_price && addon.commission) {
                        netCost =
                          addon.gross_price -
                          addon.gross_price * (addon.commission / 100);
                      } else {
                        netCost = addon.net_cost;
                      }

                      if (netCost && addon.margin_rate) {
                        const calculatedSellingPrice =
                          netCost / (1 - addon.margin_rate / 100);
                        return calculatedSellingPrice.toFixed(2);
                      }

                      return addon.selling_price !== undefined
                        ? addon.selling_price.toFixed(2)
                        : "0.00";
                    })()}
                  </Text>
                </td>
                {/* Selling Price (Default Currency) */}
                <td className="border border-ui-border-base px-2 py-2 bg-ui-bg-subtle">
                  <Text size="small" className="py-1">
                    {(() => {
                      // Calculate selling price in default currency
                      let sellingPrice: number | undefined;
                      let netCost: number | undefined;

                      // Use manual net cost if available, otherwise use calculated net cost
                      if (addon.net_cost_manual !== undefined) {
                        netCost = addon.net_cost_manual;
                      } else if (addon.gross_price && addon.commission) {
                        netCost =
                          addon.gross_price -
                          addon.gross_price * (addon.commission / 100);
                      } else {
                        netCost = addon.net_cost;
                      }

                      if (netCost && addon.margin_rate) {
                        sellingPrice = netCost / (1 - addon.margin_rate / 100);
                      } else {
                        sellingPrice = addon.selling_price;
                      }

                      return sellingPrice !== undefined
                        ? sellingPrice.toFixed(2)
                        : "0.00";
                    })()}
                  </Text>
                </td>
              </tr>
            ))}

            {/* Total Row - Only show when addons are present */}
            {addonLineItems.length > 0 && (
              <tr className="bg-ui-bg-base border-t-2 border-ui-border-strong">
                <td className="border border-ui-border-base px-2 py-3">
                  <Text weight="plus" size="small" className="text-ui-fg-base">
                    TOTAL
                  </Text>
                </td>
                <td className="border border-ui-border-base px-2 py-3"></td>
                <td className="border border-ui-border-base px-2 py-3"></td>
                <td className="border border-ui-border-base px-2 py-3"></td>
                <td className="border border-ui-border-base px-2 py-3"></td>
                <td className="border border-ui-border-base px-2 py-3"></td>
                <td className="border border-ui-border-base px-2 py-3"></td>
                <td className="border border-ui-border-base px-2 py-3">
                  <Text weight="plus" size="small" className="text-ui-fg-base">
                    {(() => {
                      // Calculate main selling price in selling currency
                      const mainSellingPriceSellingCurrency =
                        calculations.calculatedSellingPriceSellingCurrency ||
                        pricingData.sellingPriceSellingCurrency ||
                        0;

                      // Calculate addons total in selling currency using same logic as display
                      const addonsSellingCurrencyTotal = addonLineItems.reduce(
                        (sum, addon) => {
                          let sellingPrice: number | undefined;
                          let netCost: number | undefined;

                          // Use manual net cost if available, otherwise use calculated net cost
                          if (addon.net_cost_manual !== undefined) {
                            netCost = addon.net_cost_manual;
                          } else if (addon.gross_price && addon.commission) {
                            netCost =
                              addon.gross_price -
                              addon.gross_price * (addon.commission / 100);
                          } else {
                            netCost = addon.net_cost;
                          }

                          if (netCost && addon.margin_rate) {
                            sellingPrice =
                              netCost / (1 - addon.margin_rate / 100);
                          } else {
                            sellingPrice = addon.selling_price;
                          }

                          // Calculate selling price in selling currency
                          let sellingPriceInSellingCurrency: number | undefined;
                          if (sellingPrice && addon.exchange_rate) {
                            sellingPriceInSellingCurrency =
                              sellingPrice * addon.exchange_rate;
                          } else {
                            sellingPriceInSellingCurrency =
                              addon.selling_price_selling_currency;
                          }

                          return sum + (sellingPriceInSellingCurrency || 0);
                        },
                        0
                      );

                      if (
                        pricingData.sellingCurrency &&
                        (mainSellingPriceSellingCurrency > 0 ||
                          addonsSellingCurrencyTotal > 0)
                      ) {
                        const total =
                          mainSellingPriceSellingCurrency +
                          addonsSellingCurrencyTotal;
                        return `${total.toFixed(2)}`;
                      }
                      return "0.00";
                    })()}
                  </Text>
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>

      {/* Errors */}
      {errors.length > 0 && (
        <div className="space-y-2">
          <Text weight="plus" className="text-ui-fg-error">
            Pricing Errors:
          </Text>
          {errors.map((error, index) => (
            <Text key={index} size="small" className="text-ui-fg-error">
              • {error}
            </Text>
          ))}
        </div>
      )}

      {/* Addon Selection Modal */}
      <NativePrompt open={showAddonModal} onOpenChange={(open) => {
        if (!open) {
          handleAddonModalCancel();
        }
      }}>
        <NativePrompt.Content style={{ zIndex: 10000, maxWidth: '800px', width: '90vw' }}>
          <NativePrompt.Header>
            <NativePrompt.Title>
              Select Supplier Offerings
            </NativePrompt.Title>
            <NativePrompt.Description>
              <div className="space-y-6 mt-4">

                <Text size="small" className="text-ui-fg-subtle mb-4">
                  Choose supplier offerings to add to the pricing calculator.
                  These will appear as additional rows in the pricing table with
                  pre-populated pricing.
                </Text>
              {/* Date Filter Section */}
              <div className="space-y-4">
                <div>
                  <Text weight="plus" size="base" className="mb-3">
                    Filter by Active Dates
                  </Text>
                  <Text size="small" className="text-ui-fg-subtle mb-4">
                    Set date range to filter supplier offerings by their active period. Leave empty to show all offerings.
                  </Text>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="active-from">Active From</Label>
                    <DatePicker
                      value={activeFromDate}
                      onChange={setActiveFromDate}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="active-to">Active To</Label>
                    <DatePicker
                      value={activeToDate}
                      onChange={setActiveToDate}
                    />
                  </div>
                </div>

                {/* Date validation errors */}
                {dateFilterErrors.length > 0 && (
                  <div className="space-y-1">
                    {dateFilterErrors.map((error, index) => (
                      <Text key={index} size="small" className="text-ui-fg-error">
                        {error}
                      </Text>
                    ))}
                  </div>
                )}

                {/* Find Button */}
                <div className="flex justify-end">
                  <Button
                    type="button"
                    onClick={handleFindSupplierOfferings}
                    disabled={loadingSupplierOfferings}
                    size="base"
                    className="flex items-center gap-2"
                  >
                    <Search className="h-4 w-4" />
                    {loadingSupplierOfferings ? "Searching..." : "Find"}
                  </Button>
                </div>
              </div>

              {/* Results Section */}
              <div className="border-t border-ui-border-base pt-6">
                <div className="min-h-[150px]">
                  {loadingSupplierOfferings ? (
                    <div className="text-center py-8 text-ui-fg-subtle">
                      <Text>Loading supplier offerings...</Text>
                    </div>
                  ) : (
                    <>
                      <MultiSelect
                        options={availableSupplierOfferings.map(offering => ({
                          value: offering.selection_id,
                          label: offering.display_name,
                        }))}
                        selectedValues={selectedSupplierOfferingIds}
                        onChange={setSelectedSupplierOfferingIds}
                        placeholder="Select supplier offerings to add..."
                        showSelectAll={true}
                        showSelectedTags={true}
                        maxHeight="max-h-48"
                      />

                      {availableSupplierOfferings.length === 0 && !loadingSupplierOfferings && (
                        <div className="text-center py-8 text-ui-fg-subtle">
                          <Text>
                            {/* Show different message based on whether user has searched yet */}
                            {activeFromDate || activeToDate ?
                              "No supplier offerings available for the selected criteria. Try adjusting the date filters or click Find to search again." :
                              "Click the Find button above to search for available supplier offerings."
                            }
                          </Text>
                        </div>
                      )}
                    </>
                  )}
                </div>
              </div>
              </div>
            </NativePrompt.Description>
          </NativePrompt.Header>
          <NativePrompt.Footer>
            <NativePrompt.Cancel onClick={handleAddonModalCancel}>
              Cancel
            </NativePrompt.Cancel>
            <NativePrompt.Action
              onClick={handleAddonModalSave}
              disabled={selectedSupplierOfferingIds.length === 0}
            >
              Add Selected ({selectedSupplierOfferingIds.length})
            </NativePrompt.Action>
          </NativePrompt.Footer>
        </NativePrompt.Content>
      </NativePrompt>
    </div>
  );
};

export default PricingCalculator;
