import React, { useState } from "react";
import { <PERSON><PERSON>, <PERSON>ge, Tooltip, FocusModal, Text } from "@camped-ai/ui";
import { RefreshCw, AlertCircle, CheckCircle, Clock, X } from "lucide-react";
import { useBulkSyncProductServices, useSyncStatistics } from "../../hooks/supplier-management/use-product-service-sync";

interface SyncToAddonsButtonProps {
  variant?: "primary" | "secondary" | "outline";
  size?: "sm" | "md" | "lg";
  className?: string;
}

export const SyncToAddonsButton: React.FC<SyncToAddonsButtonProps> = ({
  variant = "outline",
  size = "md",
  className = "",
}) => {
  const [showModal, setShowModal] = useState(false);
  const [syncOptions, setSyncOptions] = useState({
    status: undefined as 'active' | 'inactive' | undefined,
    type: undefined as 'Product' | 'Service' | undefined,
    force_resync: false,
  });

  const bulkSyncMutation = useBulkSyncProductServices();
  const { data: statsData, isLoading: statsLoading } = useSyncStatistics();

  const handleSync = () => {
    bulkSyncMutation.mutate(syncOptions, {
      onSuccess: () => {
        setShowModal(false);
      },
    });
  };

  const getSyncStatusBadge = () => {
    if (statsLoading || !statsData) {
      return <Badge variant="secondary">Loading...</Badge>;
    }

    const { statistics } = statsData;
    const syncPercentage = statistics.total_product_services > 0 
      ? Math.round((statistics.synced_count / statistics.total_product_services) * 100)
      : 0;

    if (statistics.error_count > 0) {
      return (
        <Tooltip content={`${statistics.error_count} sync errors`}>
          <Badge variant="destructive" className="flex items-center gap-1">
            <AlertCircle size={12} />
            {syncPercentage}%
          </Badge>
        </Tooltip>
      );
    }

    if (statistics.pending_count > 0) {
      return (
        <Tooltip content={`${statistics.pending_count} pending syncs`}>
          <Badge variant="warning" className="flex items-center gap-1">
            <Clock size={12} />
            {syncPercentage}%
          </Badge>
        </Tooltip>
      );
    }

    if (syncPercentage === 100) {
      return (
        <Tooltip content="All product services synced">
          <Badge variant="success" className="flex items-center gap-1">
            <CheckCircle size={12} />
            {syncPercentage}%
          </Badge>
        </Tooltip>
      );
    }

    return (
      <Tooltip content={`${statistics.synced_count}/${statistics.total_product_services} synced`}>
        <Badge variant="secondary">{syncPercentage}%</Badge>
      </Tooltip>
    );
  };

  return (
    <>
      <div className="flex items-center gap-2">
        <Button
          variant={variant}
          size={size}
          className={className}
          onClick={() => setShowModal(true)}
          disabled={bulkSyncMutation.isPending}
        >
          <RefreshCw size={16} className={bulkSyncMutation.isPending ? "animate-spin" : ""} />
          Sync All to Add-ons
        </Button>
        {getSyncStatusBadge()}
      </div>

      <FocusModal open={showModal} onOpenChange={setShowModal}>
        <FocusModal.Content>
          <FocusModal.Header>
            <FocusModal.Title>Sync Product Services to Add-ons</FocusModal.Title>
            <FocusModal.Description>
              This will synchronize product services from the supplier management system to the add-ons inventory.
              Existing add-ons will be updated with the latest information.
            </FocusModal.Description>
          </FocusModal.Header>

          <FocusModal.Body>
            <div className="flex flex-col gap-4">
              {/* Sync Statistics */}
              {statsData && (
                <div className="p-4 bg-gray-50 rounded-lg">
                  <Text className="font-medium mb-2">Current Status</Text>
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <Text className="text-gray-600">Total Product Services</Text>
                      <Text className="font-medium">{statsData.statistics.total_product_services}</Text>
                    </div>
                    <div>
                      <Text className="text-gray-600">Already Synced</Text>
                      <Text className="font-medium text-green-600">{statsData.statistics.synced_count}</Text>
                    </div>
                    <div>
                      <Text className="text-gray-600">Errors</Text>
                      <Text className="font-medium text-red-600">{statsData.statistics.error_count}</Text>
                    </div>
                    <div>
                      <Text className="text-gray-600">Pending</Text>
                      <Text className="font-medium text-yellow-600">{statsData.statistics.pending_count}</Text>
                    </div>
                  </div>
                </div>
              )}

              {/* Sync Options */}
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium mb-2">Filter by Status</label>
                  <select
                    className="w-full p-2 border rounded-md"
                    value={syncOptions.status || ''}
                    onChange={(e) => setSyncOptions(prev => ({ 
                      ...prev, 
                      status: e.target.value as 'active' | 'inactive' | undefined || undefined 
                    }))}
                  >
                    <option value="">All Statuses</option>
                    <option value="active">Active Only</option>
                    <option value="inactive">Inactive Only</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium mb-2">Filter by Type</label>
                  <select
                    className="w-full p-2 border rounded-md"
                    value={syncOptions.type || ''}
                    onChange={(e) => setSyncOptions(prev => ({ 
                      ...prev, 
                      type: e.target.value as 'Product' | 'Service' | undefined || undefined 
                    }))}
                  >
                    <option value="">All Types</option>
                    <option value="Product">Products Only</option>
                    <option value="Service">Services Only</option>
                  </select>
                </div>

                <div className="flex items-center gap-2">
                  <input
                    type="checkbox"
                    id="force-resync"
                    checked={syncOptions.force_resync}
                    onChange={(e) => setSyncOptions(prev => ({ 
                      ...prev, 
                      force_resync: e.target.checked 
                    }))}
                  />
                  <label htmlFor="force-resync" className="text-sm">
                    Force re-sync (update existing add-ons)
                  </label>
                </div>
              </div>

              {/* Progress indicator */}
              {bulkSyncMutation.isPending && (
                <div className="flex items-center gap-2 p-3 bg-blue-50 rounded-lg">
                  <RefreshCw size={16} className="animate-spin text-blue-600" />
                  <Text className="text-blue-800">Synchronizing product services...</Text>
                </div>
              )}

              {/* Error display */}
              {bulkSyncMutation.isError && (
                <div className="flex items-center gap-2 p-3 bg-red-50 rounded-lg">
                  <AlertCircle size={16} className="text-red-600" />
                  <Text className="text-red-800">
                    {bulkSyncMutation.error?.message || 'Sync failed'}
                  </Text>
                </div>
              )}
            </div>
          </FocusModal.Body>

          <FocusModal.Footer>
            <Button
              variant="outline"
              onClick={() => setShowModal(false)}
              disabled={bulkSyncMutation.isPending}
            >
              Cancel
            </Button>
            <Button
              onClick={handleSync}
              disabled={bulkSyncMutation.isPending}
            >
              {bulkSyncMutation.isPending ? (
                <>
                  <RefreshCw size={16} className="animate-spin" />
                  Syncing...
                </>
              ) : (
                <>
                  <RefreshCw size={16} />
                  Start Sync
                </>
              )}
            </Button>
          </FocusModal.Footer>
        </FocusModal.Content>
      </FocusModal>
    </>
  );
};
