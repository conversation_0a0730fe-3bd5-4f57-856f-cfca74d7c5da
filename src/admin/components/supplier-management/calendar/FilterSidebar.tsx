import { Input, But<PERSON>, Text, Heading } from "@camped-ai/ui";
import { Search, CalendarPlus, FileText, Settings } from "lucide-react";
import { CalendarFilters } from "./calendar";

interface FilterSidebarProps {
  filters: CalendarFilters;
  onFiltersChange: (filters: CalendarFilters) => void;
  suppliers: any;
  categories: any;
  offeringsCount: number;
}

export function FilterSidebar({
  filters,
  onFiltersChange,
  suppliers,
  categories,
  offeringsCount,
}: FilterSidebarProps) {
  const handleSearchChange = (value: string) => {
    onFiltersChange({ ...filters, search: value });
  };

  const handleCategoryChange = (categoryId: string, checked: boolean) => {
    const newCategories = checked
      ? [...filters.categories, categoryId]
      : filters.categories.filter((id) => id !== categoryId);
    onFiltersChange({ ...filters, categories: newCategories });
  };

  const handleSupplierChange = (supplierId: string, checked: boolean) => {
    const newSuppliers = checked
      ? [...filters.suppliers, supplierId]
      : filters.suppliers.filter((id) => id !== supplierId);
    onFiltersChange({ ...filters, suppliers: newSuppliers });
  };

  const handleVehicleTypeChange = (vehicleType: string, checked: boolean) => {
    const newVehicleTypes = checked
      ? [...filters.vehicleTypes, vehicleType]
      : filters.vehicleTypes.filter((type) => type !== vehicleType);
    onFiltersChange({ ...filters, vehicleTypes: newVehicleTypes });
  };

  const vehicleTypes = ["Sedan", "Minivan", "Bus", "Luxury Car"];

  return (
    <div className="w-80 bg-card border-r border-border overflow-y-auto">
      <div className="p-6">
        <Heading
          level="h2"
          className="text-lg font-medium text-textPrimary mb-4"
        >
          Filters
        </Heading>

        {/* Search */}
        <div className="mb-6">
          <div className="relative">
            <Search className="absolute left-3 top-3 h-4 w-4 text-textSecondary" />
            <Input
              placeholder="Search suppliers or services..."
              value={filters.search}
              onChange={(e) => handleSearchChange(e.target.value)}
              className="pl-10"
            />
          </div>
        </div>

        {/* Service Categories */}
        <div className="mb-6">
          <Heading
            level="h3"
            className="text-sm font-medium text-textPrimary mb-3"
          >
            Service Categories
          </Heading>
          <div className="space-y-2">
            {categories.map((category) => {
              const count = 0; // TODO: Calculate actual count
              return (
                <label
                  key={category.id}
                  className="flex items-center space-x-3 cursor-pointer hover:bg-gray-50 p-2 rounded-lg"
                >
                  <input
                    type="checkbox"
                    checked={filters.categories.includes(category.id)}
                    onChange={(e) =>
                      handleCategoryChange(category.id, e.target.checked)
                    }
                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                  <div className="flex items-center space-x-2">
                    <span className="text-base">{category.icon}</span>
                    <Text className="text-sm text-textPrimary">
                      {category.name}
                    </Text>
                  </div>
                  <Text className="ml-auto text-xs text-textSecondary">
                    ({count})
                  </Text>
                </label>
              );
            })}
          </div>
        </div>

        {/* Suppliers */}
        <div className="mb-6">
          <Heading
            level="h3"
            className="text-sm font-medium text-textPrimary mb-3"
          >
            Suppliers
          </Heading>
          <div className="space-y-2">
            {suppliers.map((supplier) => (
              <label
                key={supplier.id}
                className="flex items-center space-x-3 cursor-pointer hover:bg-gray-50 p-2 rounded-lg"
              >
                <input
                  type="checkbox"
                  checked={filters.suppliers.includes(supplier.id)}
                  onChange={(e) =>
                    handleSupplierChange(supplier.id, e.target.checked)
                  }
                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
                <Text className="text-sm text-textPrimary">
                  {supplier.name}
                </Text>
              </label>
            ))}
          </div>
        </div>

        {/* Vehicle Types */}
        <div className="mb-6">
          <Heading
            level="h3"
            className="text-sm font-medium text-textPrimary mb-3"
          >
            Vehicle Type
          </Heading>
          <div className="space-y-2">
            {vehicleTypes.map((vehicleType) => (
              <label
                key={vehicleType}
                className="flex items-center space-x-3 cursor-pointer hover:bg-gray-50 p-2 rounded-lg"
              >
                <input
                  type="checkbox"
                  checked={filters.vehicleTypes.includes(vehicleType)}
                  onChange={(e) =>
                    handleVehicleTypeChange(vehicleType, e.target.checked)
                  }
                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
                <Text className="text-sm text-textPrimary">{vehicleType}</Text>
              </label>
            ))}
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="p-6 border-t border-border">
        <Heading
          level="h3"
          className="text-sm font-medium text-textPrimary mb-3"
        >
          Quick Actions
        </Heading>
        <div className="space-y-2">
          <Button
            variant="transparent"
            className="w-full justify-start text-textSecondary hover:bg-gray-50"
          >
            <CalendarPlus className="w-4 h-4 mr-2" />
            Add New Availability
          </Button>
          <Button
            variant="transparent"
            className="w-full justify-start text-textSecondary hover:bg-gray-50"
          >
            <FileText className="w-4 h-4 mr-2" />
            Export Schedule
          </Button>
          <Button
            variant="transparent"
            className="w-full justify-start text-textSecondary hover:bg-gray-50"
          >
            <Settings className="w-4 h-4 mr-2" />
            Settings
          </Button>
        </div>
      </div>
    </div>
  );
}
