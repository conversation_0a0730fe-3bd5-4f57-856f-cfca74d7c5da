import React, { useState } from "react";
import {
  FocusModal,
  Button,
  Text,
  Heading,
  Select,
  Table,
  Badge,
  Input,
  Textarea,
} from "@camped-ai/ui";
import { X } from "lucide-react";
import { BookingAddon } from "../../../hooks/supplier-management/use-booking-addons";
import { useActiveSuppliersForDropdown } from "../../../hooks/supplier-management/use-suppliers";
import { useCategories } from "../../../hooks/supplier-products-services/use-categories";
import { useSupplierOrders } from "../../../hooks/vendor-management/use-supplier-orders";
import { useAdminCurrencies } from "../../../hooks/use-admin-currencies";
import {
  formatCurrencyAmount,
  fromSmallestUnit,
} from "../../../utils/currency-utils";

interface CreateSupplierOrdersModalProps<T extends BookingAddon> {
  isOpen: boolean;
  onClose: () => void;
  addons: T[];
  onCreateOrders: (orderData: SupplierOrderCreationData[]) => void;
  selectedCategory?: string; // Optional category for auto-populating suppliers
  isNewOrder: boolean;
  isAppendMode?: boolean; // New prop to show only append field
}

export interface SupplierOrderCreationData {
  supplier_id: string;
  supplier_name: string;
  delivery_date?: string;
  notes?: string;
  order_name?: string;
  append_to_existing_order?: string; // ID of existing order to append to
  items: {
    addon_id: string;
    addon_name: string;
    quantity: number;
    unit_price: number;
    customer_info: string;
    service_date?: string;
    specifications: Record<string, any>;
  }[];
}

interface AddonAllocation {
  addon_id: string;
  supplier_id: string;
  supplier_name: string;
  delivery_date?: string;
  notes?: string;
  order_name?: string;
  append_to_existing_order?: string;
}

export const CreateSupplierOrdersModal: <T extends BookingAddon>(
  props: CreateSupplierOrdersModalProps<T>
) => React.ReactElement = ({
  isOpen,
  onClose,
  addons,
  onCreateOrders,
  selectedCategory,
  isNewOrder,
  isAppendMode = false,
}) => {
  const [allocations, setAllocations] = useState<
    Record<string, AddonAllocation>
  >({});
  const [globalAppendToOrder, setGlobalAppendToOrder] = useState("");
  const [selectedExistingOrder, setSelectedExistingOrder] = useState<any>(null);
  const [selectedSupplier, setSelectedSupplier] = useState("");
  const [removedAddonIds, setRemovedAddonIds] = useState<Set<string>>(new Set());

  // Get default currency from store settings
  const { defaultCurrency } = useAdminCurrencies();

  // Format currency using the default currency from store settings
  const formatCurrency = (amount: number) => {
    const currency = defaultCurrency || {
      currency_code: "GBP",
      decimal_digits: 2,
    };

    // Convert from smallest unit (cents) to display unit
    const displayAmount = fromSmallestUnit(amount, currency);

    return formatCurrencyAmount(displayAmount, currency, {
      showSymbol: true,
      showCode: false,
      symbolPosition: "before",
    });
  };

  // Fetch active suppliers for dropdown
  const { data: allSuppliers = [], isLoading: isLoadingSuppliers } =
    useActiveSuppliersForDropdown();

  // Fetch categories for export functionality
  const { data: categoriesData } = useCategories({
    is_active: true,
    limit: 100,
  });
  const categories = categoriesData?.categories || [];

  // Fetch existing supplier orders for append functionality
  // Only show active orders that can be appended to
  const { data: existingOrdersData, isLoading: isLoadingOrders } =
    useSupplierOrders({
      limit: 100,
      status: "pending", // Start with pending orders
    });

  // Also fetch confirmed and in_progress orders
  const { data: confirmedOrdersData } = useSupplierOrders({
    limit: 100,
    status: "confirmed",
  });

  const { data: inProgressOrdersData } = useSupplierOrders({
    limit: 100,
    status: "in_progress",
  });

  // Combine all active orders
  const existingOrders = [
    ...(existingOrdersData?.orders || []),
    ...(confirmedOrdersData?.orders || []),
    ...(inProgressOrdersData?.orders || []),
  ];

  // Filter suppliers based on selected category
  // Filter out add-ons that already have supplier orders and removed items
  const availableAddons = React.useMemo(() => {
    return addons.filter((addon) => !addon.supplier_order_id && !removedAddonIds.has(addon.id));
  }, [addons, removedAddonIds]);

  const suppliers = React.useMemo(() => {
    if (!selectedCategory) {
      return allSuppliers;
    }

    return allSuppliers.filter((supplier) => {
      // Handle different possible formats of categories
      let supplierCategories: string[] = [];

      // If categories is null or undefined, return false
      if (!supplier.categories) {
        return false;
      }

      // If categories is a string (comma-separated), convert to array
      if (typeof supplier.categories === "string") {
        supplierCategories = (supplier.categories as string)
          .split(",")
          .map((cat: string) => cat.trim());
      } else if (Array.isArray(supplier.categories)) {
        supplierCategories = supplier.categories as string[];
      } else {
        // Handle any other unexpected format
        return false;
      }

      // Check if the selected category is in the supplier's categories
      return supplierCategories.includes(selectedCategory);
    });
  }, [allSuppliers, selectedCategory]);

  // Initialize allocations with default values and auto-populate suppliers
  React.useEffect(() => {
    if (availableAddons.length > 0 && suppliers.length > 0) {
      const initialAllocations: Record<string, AddonAllocation> = {};
      availableAddons.forEach((addon) => {
        // Try to use existing supplier info if available
        let defaultSupplierId = addon.add_on?.metadata?.supplier_id || "";
        let defaultSupplierName = addon.add_on?.metadata?.supplier_name || "";

        // Auto-populate with first available supplier if category is selected and no existing supplier
        if (!defaultSupplierId && selectedCategory && suppliers.length > 0) {
          const firstSupplier = suppliers[0];
          defaultSupplierId = firstSupplier.id;
          defaultSupplierName = firstSupplier.name;
        }

        initialAllocations[addon.id] = {
          addon_id: addon.id,
          supplier_id: defaultSupplierId,
          supplier_name: defaultSupplierName,
          delivery_date: addon.order?.metadata?.check_in_date || "",
          notes: "",
        };
      });
      setAllocations(initialAllocations);
    }
  }, [availableAddons, suppliers, selectedCategory]);

  // Update allocations when an existing order is selected
  React.useEffect(() => {
    if (selectedExistingOrder && availableAddons.length > 0) {
      setAllocations((prev) => {
        const updated = { ...prev };

        // Ensure we have allocations for all available addons
        availableAddons.forEach((addon) => {
          if (!updated[addon.id]) {
            updated[addon.id] = {
              addon_id: addon.id,
              supplier_id: "",
              supplier_name: "",
              delivery_date: addon.order?.metadata?.check_in_date || "",
              notes: "",
            };
          }
        });

        // Update all allocations with the selected order's supplier
        Object.keys(updated).forEach((addonId) => {
          updated[addonId] = {
            ...updated[addonId],
            supplier_id: selectedExistingOrder.supplier_id,
            supplier_name:
              (selectedExistingOrder as any).supplier_name ||
              "Unknown Supplier",
          };
        });

        return updated;
      });
    }
  }, [selectedExistingOrder, availableAddons]);

  const handleSupplierChange = (addonId: string, supplierId: string) => {
    // Prevent changes if an existing order is selected (supplier should be locked)
    if (selectedExistingOrder) {
      return;
    }

    // Handle clear selection
    if (supplierId === "CLEAR_SELECTION") {
      setAllocations((prev) => ({
        ...prev,
        [addonId]: {
          ...prev[addonId],
          supplier_id: "",
          supplier_name: "",
        },
      }));
      return;
    }

    const supplier = suppliers.find((s) => s.id === supplierId);
    setAllocations((prev) => ({
      ...prev,
      [addonId]: {
        ...prev[addonId],
        supplier_id: supplierId,
        supplier_name: supplier?.name || "",
      },
    }));
  };

  // Handle global append to order change
  const handleGlobalAppendToOrderChange = (orderId: string) => {
    setGlobalAppendToOrder(orderId);

    if (orderId && orderId !== "CREATE_NEW") {
      // Find the selected order
      const order = existingOrders.find((o) => o.id === orderId);
      if (order) {

        setSelectedExistingOrder(order);

        // Auto-populate all allocations with the order's supplier
        setAllocations((prev) => {
          const updated = { ...prev };

          // Ensure we have allocations for all available addons
          availableAddons.forEach((addon) => {
            if (!updated[addon.id]) {
              updated[addon.id] = {
                addon_id: addon.id,
                supplier_id: "",
                supplier_name: "",
                delivery_date: addon.order?.metadata?.check_in_date || "",
                notes: "",
              };
            }
          });

          // Now update all allocations with the selected order's supplier
          Object.keys(updated).forEach((addonId) => {
            updated[addonId] = {
              ...updated[addonId],
              supplier_id: order.supplier_id,
              supplier_name: (order as any).supplier_name || "Unknown Supplier",
            };
          });
          return updated;
        });
      }
    } else {
      // Clear the selected order and allow manual supplier selection
      console.log("🔍 Clearing existing order selection");
      setSelectedExistingOrder(null);
    }
  };

  // Apply the same supplier to all add-ons
  const handleApplySupplierToAll = (supplierId: string) => {
    // Prevent changes if an existing order is selected
    if (selectedExistingOrder) {
      return;
    }

    const supplier = suppliers.find((s) => s.id === supplierId);
    if (!supplier) return;

    setAllocations((prev) => {
      const updated = { ...prev };
      Object.keys(updated).forEach((addonId) => {
        updated[addonId] = {
          ...updated[addonId],
          supplier_id: supplierId,
          supplier_name: supplier.name,
        };
      });
      return updated;
    });
  };

  const handleDeliveryDateChange = (addonId: string, date: string) => {
    setAllocations((prev) => ({
      ...prev,
      [addonId]: {
        ...prev[addonId],
        delivery_date: date,
      },
    }));
  };

  const handleNotesChange = (addonId: string, notes: string) => {
    setAllocations((prev) => ({
      ...prev,
      [addonId]: {
        ...prev[addonId],
        notes,
      },
    }));
  };

  const handleAppendToOrderChange = (addonId: string, orderId: string) => {
    setAllocations((prev) => ({
      ...prev,
      [addonId]: {
        ...prev[addonId],
        append_to_existing_order: orderId,
      },
    }));
  };

  // Clear supplier selection
  const handleClearSupplier = (addonId: string) => {
    setAllocations((prev) => ({
      ...prev,
      [addonId]: {
        ...prev[addonId],
        supplier_id: "",
        supplier_name: "",
      },
    }));
  };

  // Remove addon from the list
  const handleRemoveAddon = (addonId: string) => {
    setRemovedAddonIds((prev) => new Set([...prev, addonId]));
    // Also remove from allocations
    setAllocations((prev) => {
      const updated = { ...prev };
      delete updated[addonId];
      return updated;
    });
  };

  // Export functionality
  const handleExportData = () => {
    const exportData = availableAddons.map((addon) => {
      const allocation = allocations[addon.id];
      const categoryId = (addon.add_on?.metadata as any)?.category;
      const categoryName =
        selectedCategory &&
        categories.find((c: any) => c.id === categoryId)?.name;

      return {
        "Add-on Service": addon.add_on_name,
        Category: categoryName || "N/A",
        "Order ID": addon.order?.display_id || addon.order_id,
        Customer:
          addon.order?.metadata?.customer_name ||
          addon.order?.email ||
          "Unknown",
        Quantity: addon.quantity,
        "Unit Price": addon.unit_price,
        "Total Price": addon.total_price,
        Currency: "CHF",
        "Assigned Supplier": allocation?.supplier_name || "Unassigned",
        "Delivery Date": allocation?.delivery_date || "",
        Notes: allocation?.notes || "",
        "Customer Email": addon.order?.email || "",
        "Check-in Date": addon.order?.metadata?.check_in_date || "",
        "Check-out Date": addon.order?.metadata?.check_out_date || "",
        Hotel: addon.order?.metadata?.hotel_name || "",
        "Customer Responses": JSON.stringify(
          addon.customer_field_responses || {}
        ),
        "Created At": addon.created_at || "",
      };
    });

    // Convert to CSV
    const headers = Object.keys(exportData[0] || {});
    const csvContent = [
      headers.join(","),
      ...exportData.map((row) =>
        headers
          .map((header) => {
            const value = row[header as keyof typeof row];
            // Escape commas and quotes in CSV
            return typeof value === "string" &&
              (value.includes(",") || value.includes('"'))
              ? `"${value.replace(/"/g, '""')}"`
              : value;
          })
          .join(",")
      ),
    ].join("\n");

    // Download CSV file
    const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8;" });
    const link = document.createElement("a");
    const url = URL.createObjectURL(blob);
    link.setAttribute("href", url);
    link.setAttribute(
      "download",
      `supplier-orders-${new Date().toISOString().split("T")[0]}.csv`
    );
    link.style.visibility = "hidden";
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const handleCreateOrders = () => {
    // Group allocations by supplier and append preference
    const supplierGroups: Record<string, SupplierOrderCreationData> = {};

    Object.values(allocations).forEach((allocation) => {
      if (!allocation.supplier_id) return; // Skip unallocated items

      // Create unique key based on supplier and append preference
      // Use individual allocation's append preference, or fall back to global
      let appendPreference =
        allocation.append_to_existing_order || globalAppendToOrder;

      // Handle special values for append preference
      let appendKey = appendPreference;
      if (!appendKey || appendKey === "CREATE_NEW") {
        appendKey = "new";
      }
      const key = `${allocation.supplier_id}_${appendKey}`;

      if (!supplierGroups[key]) {
        // Only set append_to_existing_order if it's a real order ID
        const appendToOrder =
          appendPreference && appendPreference !== "CREATE_NEW"
            ? appendPreference
            : undefined;

        supplierGroups[key] = {
          supplier_id: allocation.supplier_id,
          supplier_name: allocation.supplier_name,
          delivery_date: allocation.delivery_date,
          notes: allocation.notes,
          order_name: undefined, // Will be auto-generated
          append_to_existing_order: appendToOrder,
          items: [],
        };
      }

      const addon = addons.find((a) => a.id === allocation.addon_id);
      if (addon) {
        supplierGroups[key].items.push({
          addon_id: addon.id,
          addon_name: addon.add_on_name,
          quantity: addon.quantity,
          unit_price: addon.unit_price,
          customer_info:
            addon.order?.metadata?.customer_name ||
            addon.order?.email ||
            "Unknown",
          service_date: allocation.delivery_date,
          specifications: addon.customer_field_responses || {},
        });
      }
    });

    onCreateOrders(Object.values(supplierGroups));
  };

  const unallocatedCount = Object.values(allocations).filter(
    (a) => !a.supplier_id
  ).length;
  const supplierCount = new Set(
    Object.values(allocations)
      .map((a) => a.supplier_id)
      .filter(Boolean)
  ).size;

  return (
    <FocusModal open={isOpen} onOpenChange={onClose}>
      <FocusModal.Content className="max-h-[90vh] flex flex-col">
        <FocusModal.Header className="flex-shrink-0">
          <div className="flex items-start justify-between w-full">
            <div className="flex ">
              <FocusModal.Title>
                <p className="font-extrabold">
                  {isAppendMode ? "Append to Existing Order" : "Create Supplier Orders"}
                </p>
              </FocusModal.Title>
            </div>
            <FocusModal.Description>
              {isAppendMode
                ? `Add ${availableAddons.length} booking add-ons to an existing supplier order`
                : `Allocate ${availableAddons.length} booking add-ons to suppliers`
              }
              {addons.length > availableAddons.length && (
                <span className="text-orange-600 ml-2">
                  ({addons.length - availableAddons.length} already have
                  orders)
                </span>
              )}
            </FocusModal.Description>
            {/* <Button variant="secondary" size="small" onClick={handleExportData}>
              Export Data
            </Button> */}
          </div>
        </FocusModal.Header>

        <FocusModal.Body className="flex-1 overflow-y-auto space-y-6 pt-4">
          {isAppendMode ? (
            // Append mode: Show only the append field
            <div className="px-6">
              <div className="md:col-span-2">
                <Text className="font-medium mb-2">Append to Existing Order</Text>
                <Select
                  value={globalAppendToOrder}
                  onValueChange={handleGlobalAppendToOrderChange}
                >
                  <Select.Trigger className="w-full">
                    <Select.Value placeholder="Select existing order..." />
                  </Select.Trigger>
                  <Select.Content>
                    {isLoadingOrders ? (
                      <Select.Item value="loading" disabled>
                        Loading orders...
                      </Select.Item>
                    ) : existingOrders.length === 0 ? (
                      <Select.Item value="no-orders" disabled>
                        No existing orders available
                      </Select.Item>
                    ) : (
                      existingOrders.map((order) => (
                        <Select.Item key={order.id} value={order.id}>
                          {order.order_number}
                          <span className="text-xs text-textSecondary ml-2">
                            ({order.status})
                          </span>
                        </Select.Item>
                      ))
                    )}
                  </Select.Content>
                </Select>
                <Text className="text-xs text-textSecondary mt-1">
                  Select an existing order to add new items to it
                  {selectedExistingOrder && (
                    <span className="text-blue-600 font-medium block mt-1">
                      ⚠️ Items will be added to "{(selectedExistingOrder as any)?.supplier_name || "Unknown"}" supplier order
                    </span>
                  )}
                </Text>
              </div>
            </div>
          ) : (
            // Normal mode: Show all fields
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 px-6">
              {!isNewOrder ? (
                <div className="md:col-span-2">
                  <Text className="font-medium mb-2">Append to Existing Order (optional)</Text>
                  <Select
                    value={globalAppendToOrder}
                    onValueChange={handleGlobalAppendToOrderChange}
                  >
                    <Select.Trigger className="w-full">
                      <Select.Value placeholder="Create new order or select existing..." />
                    </Select.Trigger>
                    <Select.Content>
                      <Select.Item value="CREATE_NEW">Create New Order</Select.Item>
                      {isLoadingOrders ? (
                        <Select.Item value="loading" disabled>
                          Loading orders...
                        </Select.Item>
                      ) : existingOrders.length === 0 ? (
                        <Select.Item value="no-orders" disabled>
                          No existing orders available
                        </Select.Item>
                      ) : (
                        existingOrders.map((order) => (
                          <Select.Item key={order.id} value={order.id}>
                            {order.order_number}
                            <span className="text-xs text-textSecondary ml-2">
                              ({order.status})
                            </span>
                          </Select.Item>
                        ))
                      )}
                    </Select.Content>
                  </Select>
                  <Text className="text-xs text-textSecondary mt-1">
                    Select an existing order to add new items to it instead of creating a new order
                    {selectedExistingOrder && (
                      <span className="text-blue-600 font-medium block mt-1">
                        ⚠️ Supplier selection is locked to "
                        {(selectedExistingOrder as any)?.supplier_name || "Unknown"}"
                        for consistency with the existing order
                      </span>
                    )}
                  </Text>
                </div>
              ) : (
              <>
                {/* Supplier */}
                <div>
                  <Text className="font-medium mb-2">Supplier</Text>
                  <Select
                    value={selectedSupplier}
                    onValueChange={(val) => {
                      setSelectedSupplier(val);
                      // Automatically apply supplier to all add-ons
                      handleApplySupplierToAll(val);
                    }}
                  >
                    <Select.Trigger className="w-full">
                      <Select.Value placeholder="Select a supplier..." />
                    </Select.Trigger>
                    <Select.Content>
                      {isLoadingSuppliers ? (
                        <Select.Item value="loading" disabled>
                          Loading suppliers...
                        </Select.Item>
                      ) : suppliers.length === 0 ? (
                        <Select.Item value="no-suppliers" disabled>
                          No suppliers available
                        </Select.Item>
                      ) : (
                        suppliers.map((supplier) => (
                          <Select.Item key={supplier.id} value={supplier.id}>
                            {supplier.name}
                          </Select.Item>
                        ))
                      )}
                    </Select.Content>
                  </Select>
                  <Text className="text-xs text-textSecondary mt-1">
                    Automatically applied to all add-ons (ignored if appending to existing)
                  </Text>
                </div>
              </>
            )}


            </div>
          )}

          {/* Add-ons Allocation Table - Show only in normal mode */}
          {!isAppendMode && (
          <div className="px-6">
            <Heading level="h3" className="mb-4">
              Allocate Add-ons to Suppliers
            </Heading>
            <Table>
              <Table.Header>
                <Table.Row>
                  <Table.HeaderCell>Add-on Service</Table.HeaderCell>
                  <Table.HeaderCell>Customer</Table.HeaderCell>
                  <Table.HeaderCell>Quantity</Table.HeaderCell>
                  <Table.HeaderCell>Price</Table.HeaderCell>
                  <Table.HeaderCell>Delivery Date</Table.HeaderCell>
                  <Table.HeaderCell>Notes</Table.HeaderCell>
                  <Table.HeaderCell>Remove</Table.HeaderCell>
                </Table.Row>
              </Table.Header>
              <Table.Body>
                {availableAddons.map((addon) => (
                  <Table.Row key={addon.id}>
                    <Table.Cell>
                      <div>
                        <Text className="font-medium">{addon.add_on_name}</Text>
                        <Text className="text-xs text-textSecondary">
                          Order: {addon.order?.display_id || addon.order_id}
                        </Text>
                      </div>
                    </Table.Cell>
                    <Table.Cell>
                      <Text>
                        {addon.order?.metadata?.customer_name ||
                          addon.order?.email}
                      </Text>
                    </Table.Cell>
                    <Table.Cell>
                      <Badge>{addon.quantity}</Badge>
                    </Table.Cell>
                    <Table.Cell>
                      <Text className="font-medium">
                        {formatCurrency(addon.total_price)}
                      </Text>
                    </Table.Cell>
                    <Table.Cell>
                      <Input
                        type="date"
                        value={allocations[addon.id]?.delivery_date || ""}
                        onChange={(e) =>
                          handleDeliveryDateChange(addon.id, e.target.value)
                        }
                        className="w-full"
                      />
                    </Table.Cell>
                    <Table.Cell>
                      <Input
                        value={allocations[addon.id]?.notes || ""}
                        onChange={(e) =>
                          handleNotesChange(addon.id, e.target.value)
                        }
                        placeholder="Item notes..."
                        className="w-full"
                      />
                    </Table.Cell>
                    <Table.Cell>
                      <Button
                        variant="secondary"
                        size="small"
                        onClick={() => handleRemoveAddon(addon.id)}
                        className="text-red-600 hover:text-red-800"
                      >
                        <X className="h-4 w-4" />
                      </Button>
                    </Table.Cell>
                  </Table.Row>
                ))}
              </Table.Body>
            </Table>
          </div>
          )}
        </FocusModal.Body>

        <FocusModal.Footer className="flex-shrink-0">
          <div className="flex justify-between items-center w-full">
            <Text className="text-sm text-textSecondary">
              {isAppendMode ? (
                !globalAppendToOrder ? (
                  <span className="text-red-600">
                    Please select an existing order to append to
                  </span>
                ) : (
                  <span className="text-green-600">
                    Items will be added to the selected order
                  </span>
                )
              ) : (
                unallocatedCount > 0 && (
                  <span className="text-red-600">
                    {unallocatedCount} add-ons need supplier assignment
                  </span>
                )
              )}
            </Text>
            <div className="flex space-x-3">
              <Button variant="secondary" onClick={onClose}>
                Cancel
              </Button>
              <Button
                variant="primary"
                onClick={handleCreateOrders}
                disabled={isAppendMode ? !globalAppendToOrder : unallocatedCount > 0}
              >
                {isAppendMode
                  ? "Append to Order"
                  : `Create ${supplierCount} Supplier Order${supplierCount !== 1 ? "s" : ""}`
                }
              </Button>
            </div>
          </div>
        </FocusModal.Footer>
      </FocusModal.Content>
    </FocusModal>
  );
};
