import { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { CalendarView } from "./CalendarView";
import { CalendarHeader } from "./CalendarHeader";
import { EventModal } from "./EventModal";
import { Text } from "@camped-ai/ui";
import {
  addDays,
  startOfMonth,
  endOfMonth,
  startOfWeek,
  startOfDay,
  endOfDay,
} from "date-fns";
import { useBookingAddons } from "../../../hooks/supplier-management/use-booking-addons";

export type CalendarViewType = "day" | "week" | "month" | "list";

export default function CalendarPage() {
  const [currentDate, setCurrentDate] = useState(new Date());
  const [viewType, setViewType] = useState<CalendarViewType>("list");
  const [selectedEvent, setSelectedEvent] = useState<any | null>(null);
  const [expandedDays, setExpandedDays] = useState<Set<string>>(new Set());
  const { data: offerings = [], isLoading } = useQuery<any[]>({
    queryKey: ["/api/supplier-offerings"],
  });

  // Fetch booking add-ons for list view
  const {
    data: bookingAddonsData,
    isLoading: isLoadingAddons,
    refetch: refetchBookingAddons,
  } = useBookingAddons({
    limit: 100,
  });

  // Use all offerings without filtering for now
  const filteredOfferings = offerings;

  const handlePreviousDate = () => {
    const newDate = new Date(currentDate);
    if (viewType === "day") {
      newDate.setDate(newDate.getDate() - 1);
    } else if (viewType === "week") {
      newDate.setDate(newDate.getDate() - 7);
    } else if (viewType === "month") {
      newDate.setMonth(newDate.getMonth() - 1);
    }
    setCurrentDate(newDate);
  };

  const handleNextDate = () => {
    const newDate = new Date(currentDate);
    if (viewType === "day") {
      newDate.setDate(newDate.getDate() + 1);
    } else if (viewType === "week") {
      newDate.setDate(newDate.getDate() + 7);
    } else if (viewType === "month") {
      newDate.setMonth(newDate.getMonth() + 1);
    }
    setCurrentDate(newDate);
  };

  const handleToday = () => {
    setCurrentDate(new Date());
  };

  const handleShowAll = () => {
    // Find all days with more than 3 events and expand them
    const monthStart = startOfMonth(currentDate);
    const monthEnd = endOfMonth(currentDate);
    const startDate = startOfWeek(monthStart, { weekStartsOn: 0 });
    const endDate = endOfMonth(monthEnd);

    const newExpanded = new Set<string>();
    let day = startDate;
    while (day <= endDate) {
      const dayOfferings = filteredOfferings.filter((offering) => {
        const activeFrom = new Date(offering.active_from);
        const activeTo = new Date(offering.active_to);
        const dayStart = startOfDay(day);
        const dayEnd = endOfDay(day);

        return (
          (activeFrom <= dayEnd && activeTo >= dayStart) ||
          (activeFrom <= dayStart && activeTo >= dayEnd)
        );
      });

      if (dayOfferings.length > 3) {
        newExpanded.add(day.toISOString());
      }

      day = addDays(day, 1);
    }

    setExpandedDays(newExpanded);
  };

  const handleCollapseAll = () => {
    setExpandedDays(new Set());
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Text className="text-lg">Loading calendar...</Text>
      </div>
    );
  }

  return (
    <div className="h-screen bg-white overflow-hidden">
      <CalendarHeader
        currentDate={currentDate}
        viewType={viewType}
        onViewTypeChange={setViewType}
        onPreviousDate={handlePreviousDate}
        onNextDate={handleNextDate}
        onToday={handleToday}
        onShowAll={handleShowAll}
        onCollapseAll={handleCollapseAll}
        hasExpandedDays={expandedDays.size > 0}
      />

      <div className="h-[calc(100vh-80px)] overflow-auto bg-white">
        <CalendarView
          offerings={filteredOfferings}
          currentDate={currentDate}
          viewType={viewType}
          onEventClick={setSelectedEvent}
          expandedDays={expandedDays}
          onExpandedDaysChange={setExpandedDays}
          bookingAddonsData={bookingAddonsData}
          isLoadingAddons={isLoadingAddons}
          onRefreshData={() => refetchBookingAddons()}
        />
      </div>

      <EventModal
        event={selectedEvent}
        onClose={() => setSelectedEvent(null)}
      />
    </div>
  );
}
