import { But<PERSON>, Text, Heading } from "@camped-ai/ui";
import { ChevronLeft, ChevronRight } from "lucide-react";
import { CalendarViewType } from "./calendar";
import { format } from "date-fns";

interface CalendarHeaderProps {
  currentDate: Date;
  viewType: CalendarViewType;
  onViewTypeChange: (view: CalendarViewType) => void;
  onPreviousDate: () => void;
  onNextDate: () => void;
  onToday: () => void;
  onShowAll?: () => void;
  onCollapseAll?: () => void;
  hasExpandedDays?: boolean;
}

export function CalendarHeader({
  currentDate,
  viewType,
  onViewTypeChange,
  onPreviousDate,
  onNextDate,
  onToday,
  onShowAll,
  onCollapseAll,
  hasExpandedDays,
}: CalendarHeaderProps) {
  const formatDateTitle = () => {
    if (viewType === "month") {
      return format(currentDate, "MMMM yyyy");
    } else if (viewType === "week") {
      return format(currentDate, "MMMM yyyy");
    } else {
      return format(currentDate, "MMMM d, yyyy");
    }
  };

  return (
    <header className="bg-white shadow-sm border-b border-border sticky top-0 z-50">
      <div className="px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Heading
              level="h1"
              className="text-2xl font-medium text-textPrimary"
            >
              Booked Add-ons
            </Heading>
          </div>

          <div className="flex items-center space-x-4">
            {/* View Toggle */}
            <div className="flex bg-gray-50 rounded-lg p-1 border border-gray-200">
              <Button
                variant="transparent"
                size="small"
                onClick={() => onViewTypeChange("day")}
                className={
                  viewType === "day"
                    ? "bg-white shadow-sm text-gray-900 font-medium border border-gray-200 rounded-md"
                    : "text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-md transition-colors duration-150"
                }
              >
                Day
              </Button>
              <Button
                variant="transparent"
                size="small"
                onClick={() => onViewTypeChange("week")}
                className={
                  viewType === "week"
                    ? "bg-white shadow-sm text-gray-900 font-medium border border-gray-200 rounded-md"
                    : "text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-md transition-colors duration-150"
                }
              >
                Week
              </Button>
              <Button
                variant="transparent"
                size="small"
                onClick={() => onViewTypeChange("month")}
                className={
                  viewType === "month"
                    ? "bg-white shadow-sm text-gray-900 font-medium border border-gray-200 rounded-md"
                    : "text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-md transition-colors duration-150"
                }
              >
                Month
              </Button>
              <Button
                variant="transparent"
                size="small"
                onClick={() => onViewTypeChange("list")}
                className={
                  viewType === "list"
                    ? "bg-white shadow-sm text-gray-900 font-medium border border-gray-200 rounded-md"
                    : "text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-md transition-colors duration-150"
                }
              >
                List
              </Button>
            </div>

            {/* Date Navigation */}
            <div className="flex items-center space-x-2">
              <Button
                variant="transparent"
                size="small"
                onClick={onPreviousDate}
                className="p-2 hover:bg-gray-100"
              >
                <ChevronLeft className="w-4 h-4" />
              </Button>
              <Text className="text-lg font-medium text-textPrimary px-4">
                {formatDateTitle()}
              </Text>
              <Button
                variant="transparent"
                size="small"
                onClick={onNextDate}
                className="p-2 hover:bg-gray-100"
              >
                <ChevronRight className="w-4 h-4" />
              </Button>
              <Button
                variant="secondary"
                size="small"
                onClick={onToday}
                className="ml-4"
              >
                Today
              </Button>

              {/* Show All / Collapse All for Month View */}
              {viewType === "month" && (onShowAll || onCollapseAll) && (
                <Button
                  variant="secondary"
                  size="small"
                  onClick={hasExpandedDays ? onCollapseAll : onShowAll}
                  className="ml-2"
                >
                  {hasExpandedDays ? "Collapse All" : "Show All"}
                </Button>
              )}
            </div>
          </div>
        </div>
      </div>
    </header>
  );
}
