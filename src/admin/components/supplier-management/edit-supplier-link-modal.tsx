import { useState, useEffect } from "react";
import {
  FocusModal,
  Button,
  Input,
  Select,
  Textarea,
  Text,
  Checkbox,
  toast,
} from "@camped-ai/ui";
import { Star, Building } from "lucide-react";
import { 
  useUpdateProductServiceSupplier,
  type ProductServiceSupplier,
  type UpdateProductServiceSupplierInput 
} from "../../hooks/supplier-products-services/use-product-service-suppliers";

interface EditSupplierLinkModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  supplierLink: ProductServiceSupplier | null;
}

export const EditSupplierLinkModal = ({ 
  open, 
  onOpenChange, 
  supplierLink 
}: EditSupplierLinkModalProps) => {
  const [formData, setFormData] = useState({
    cost: "",
    currency_code: "CHF",
    availability: "",
    max_capacity: "",
    season: "",
    valid_from: "",
    valid_until: "",
    notes: "",
    lead_time_days: "",
    minimum_order: "",
    is_preferred: false,
  });

  const updateSupplierLink = useUpdateProductServiceSupplier();

  // Populate form when supplier link changes
  useEffect(() => {
    if (supplierLink && open) {
      setFormData({
        cost: supplierLink.cost.toString(),
        currency_code: supplierLink.currency_code,
        availability: supplierLink.availability,
        max_capacity: supplierLink.max_capacity?.toString() || "",
        season: supplierLink.season || "",
        valid_from: supplierLink.valid_from ? supplierLink.valid_from.split('T')[0] : "",
        valid_until: supplierLink.valid_until ? supplierLink.valid_until.split('T')[0] : "",
        notes: supplierLink.notes || "",
        lead_time_days: supplierLink.lead_time_days?.toString() || "",
        minimum_order: supplierLink.minimum_order?.toString() || "",
        is_preferred: supplierLink.is_preferred,
      });
    }
  }, [supplierLink, open]);

  // Reset form when modal closes
  useEffect(() => {
    if (!open) {
      setFormData({
        cost: "",
        currency_code: "CHF",
        availability: "",
        max_capacity: "",
        season: "",
        valid_from: "",
        valid_until: "",
        notes: "",
        lead_time_days: "",
        minimum_order: "",
        is_preferred: false,
      });
    }
  }, [open]);

  const handleSubmit = async () => {
    if (!supplierLink) {
      toast.error("No supplier link selected");
      return;
    }

    if (!formData.cost || !formData.availability) {
      toast.error("Please fill in required fields (Cost and Availability)");
      return;
    }

    const submitData: UpdateProductServiceSupplierInput = {
      cost: parseFloat(formData.cost),
      currency_code: formData.currency_code,
      availability: formData.availability,
      max_capacity: formData.max_capacity && formData.max_capacity.trim() ? parseInt(formData.max_capacity) : undefined,
      season: formData.season && formData.season.trim() ? formData.season : undefined,
      valid_from: formData.valid_from && formData.valid_from.trim() ? new Date(formData.valid_from).toISOString() : undefined,
      valid_until: formData.valid_until && formData.valid_until.trim() ? new Date(formData.valid_until).toISOString() : undefined,
      notes: formData.notes && formData.notes.trim() ? formData.notes : undefined,
      lead_time_days: formData.lead_time_days && formData.lead_time_days.trim() ? parseInt(formData.lead_time_days) : undefined,
      minimum_order: formData.minimum_order && formData.minimum_order.trim() ? parseInt(formData.minimum_order) : undefined,
      is_preferred: formData.is_preferred,
    };

    try {
      await updateSupplierLink.mutateAsync({ id: supplierLink.id, data: submitData });
      onOpenChange(false);
    } catch (error) {
      // Error is handled by the mutation
    }
  };

  const handleInputChange = (field: string, value: string | boolean) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  if (!supplierLink) {
    return null;
  }

  return (
    <FocusModal open={open} onOpenChange={onOpenChange}>
      <FocusModal.Content className="max-w-2xl">
        <FocusModal.Header>
          <FocusModal.Title>Edit Supplier Link</FocusModal.Title>
          <FocusModal.Description>
            Update pricing and availability information for "{supplierLink.supplier?.name || 'this supplier'}".
          </FocusModal.Description>
        </FocusModal.Header>
        
        <FocusModal.Body className="space-y-6">
          {/* Supplier Info (Read-only) */}
          <div className="flex items-center gap-3 p-3 border rounded-lg bg-gray-50">
            <Building className="h-4 w-4 text-gray-600" />
            <div>
              <div className="font-medium">{supplierLink.supplier?.name || `Supplier ${supplierLink.supplier_id}`}</div>
              {supplierLink.supplier?.primary_contact_name && (
                <div className="text-sm text-ui-fg-subtle">
                  Contact: {supplierLink.supplier.primary_contact_name}
                </div>
              )}
            </div>
          </div>

          {/* Pricing Information */}
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Text size="small" weight="plus" className="mb-2">
                Cost *
              </Text>
              <Input
                type="number"
                step="0.01"
                placeholder="0.00"
                value={formData.cost}
                onChange={(e) => handleInputChange("cost", e.target.value)}
              />
            </div>
            <div>
              <Text size="small" weight="plus" className="mb-2">
                Currency
              </Text>
              <Select
                value={formData.currency_code}
                onValueChange={(value) => handleInputChange("currency_code", value)}
              >
                <Select.Trigger>
                  <Select.Value />
                </Select.Trigger>
                <Select.Content>
                  <Select.Item value="CHF">CHF</Select.Item>
                  <Select.Item value="EUR">EUR</Select.Item>
                  <Select.Item value="USD">USD</Select.Item>
                </Select.Content>
              </Select>
            </div>
          </div>

          {/* Availability Information */}
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Text size="small" weight="plus" className="mb-2">
                Availability *
              </Text>
              <Input
                placeholder="e.g., 10/day, Weekends, On demand"
                value={formData.availability}
                onChange={(e) => handleInputChange("availability", e.target.value)}
              />
            </div>
            <div>
              <Text size="small" weight="plus" className="mb-2">
                Max Capacity
              </Text>
              <Input
                type="number"
                placeholder="Maximum capacity"
                value={formData.max_capacity}
                onChange={(e) => handleInputChange("max_capacity", e.target.value)}
              />
            </div>
          </div>

          {/* Seasonal Information */}
          <div className="grid grid-cols-3 gap-4">
            <div>
              <Text size="small" weight="plus" className="mb-2">
                Season
              </Text>
              <Input
                placeholder="e.g., Dec – Mar, Year-round"
                value={formData.season}
                onChange={(e) => handleInputChange("season", e.target.value)}
              />
            </div>
            <div>
              <Text size="small" weight="plus" className="mb-2">
                Valid From
              </Text>
              <Input
                type="date"
                value={formData.valid_from}
                onChange={(e) => handleInputChange("valid_from", e.target.value)}
              />
            </div>
            <div>
              <Text size="small" weight="plus" className="mb-2">
                Valid Until
              </Text>
              <Input
                type="date"
                value={formData.valid_until}
                onChange={(e) => handleInputChange("valid_until", e.target.value)}
              />
            </div>
          </div>

          {/* Additional Details */}
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Text size="small" weight="plus" className="mb-2">
                Lead Time (days)
              </Text>
              <Input
                type="number"
                placeholder="Days notice required"
                value={formData.lead_time_days}
                onChange={(e) => handleInputChange("lead_time_days", e.target.value)}
              />
            </div>
            <div>
              <Text size="small" weight="plus" className="mb-2">
                Minimum Order
              </Text>
              <Input
                type="number"
                placeholder="Minimum order quantity"
                value={formData.minimum_order}
                onChange={(e) => handleInputChange("minimum_order", e.target.value)}
              />
            </div>
          </div>

          {/* Notes */}
          <div>
            <Text size="small" weight="plus" className="mb-2">
              Notes
            </Text>
            <Textarea
              placeholder="Internal notes about this supplier relationship..."
              value={formData.notes}
              onChange={(e) => handleInputChange("notes", e.target.value)}
              rows={3}
            />
          </div>

          {/* Preferred Supplier */}
          <div className="flex items-center space-x-2">
            <Checkbox
              id="is_preferred"
              checked={formData.is_preferred}
              onCheckedChange={(checked) => handleInputChange("is_preferred", checked as boolean)}
            />
            <label htmlFor="is_preferred" className="text-sm cursor-pointer flex items-center gap-2">
              <Star className="h-4 w-4 text-yellow-500" />
              Mark as preferred supplier
            </label>
          </div>
        </FocusModal.Body>

        <FocusModal.Footer>
          <div className="flex items-center justify-end gap-2">
            <Button
              variant="secondary"
              onClick={() => onOpenChange(false)}
              disabled={updateSupplierLink.isPending}
            >
              Cancel
            </Button>
            <Button
              onClick={handleSubmit}
              disabled={updateSupplierLink.isPending}
            >
              {updateSupplierLink.isPending ? "Updating..." : "Update Link"}
            </Button>
          </div>
        </FocusModal.Footer>
      </FocusModal.Content>
    </FocusModal>
  );
};
