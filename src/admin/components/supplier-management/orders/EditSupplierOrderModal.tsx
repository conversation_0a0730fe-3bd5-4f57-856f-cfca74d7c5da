import React, { useState, useEffect } from "react";
import {
  FocusModal,
  Text,
  Button,
  Select,
  Textarea,
  Container,
  Heading,
  Badge,
  toast,
} from "@camped-ai/ui";
import { Package, FileText } from "lucide-react";
import {
  useSupplierOrder,
  useUpdateSupplierOrder,
} from "../../../hooks/vendor-management/use-supplier-orders";
import { formatCurrencyDisplay } from "../../../utils/currency-helpers";

interface EditSupplierOrderModalProps {
  isOpen: boolean;
  onClose: () => void;
  orderId: string;
}

const ORDER_STATUSES = [
  { value: "pending", label: "Pending" },
  { value: "confirmed", label: "Confirmed" },
  { value: "in_progress", label: "In Progress" },
  { value: "completed", label: "Completed" },
  { value: "cancelled", label: "Cancelled" },
];

export const EditSupplierOrderModal: React.FC<EditSupplierOrderModalProps> = ({
  isOpen,
  onClose,
  orderId,
}) => {
  const { data, isLoading, error } = useSupplierOrder(orderId);
  const updateOrderMutation = useUpdateSupplierOrder();
  const order = data?.order;

  const [formData, setFormData] = useState({
    status: "",
    notes: "",
  });

  const [hasChanges, setHasChanges] = useState(false);

  // Initialize form data when order loads
  useEffect(() => {
    if (order) {
      const initialData = {
        status: order.status || "",
        notes: order.notes || "",
      };
      setFormData(initialData);
      setHasChanges(false);
    }
  }, [order]);

  // Track changes
  useEffect(() => {
    if (order) {
      const hasStatusChange = formData.status !== (order.status || "");
      const hasNotesChange = formData.notes !== (order.notes || "");
      setHasChanges(hasStatusChange || hasNotesChange);
    }
  }, [formData, order]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!order || !hasChanges) return;

    try {
      await updateOrderMutation.mutateAsync({
        id: order.id,
        status: formData.status,
        notes: formData.notes,
      });

      toast.success("Order updated successfully");
      onClose();
    } catch (error) {
      console.error("Failed to update order:", error);
      // Error toast is handled by the mutation hook
    }
  };

  const handleCancel = () => {
    if (hasChanges) {
      const confirmDiscard = window.confirm(
        "You have unsaved changes. Are you sure you want to discard them?"
      );
      if (!confirmDiscard) return;
    }
    onClose();
  };

  const getStatusBadgeVariant = (status: string) => {
    switch (status.toLowerCase()) {
      case "confirmed":
        return "green";
      case "pending":
        return "orange";
      case "in_progress":
        return "blue";
      case "completed":
        return "green";
      case "cancelled":
        return "red";
      default:
        return "grey";
    }
  };

  if (error) {
    return (
      <FocusModal open={isOpen} onOpenChange={onClose}>
        <FocusModal.Content>
          <FocusModal.Header>
            <FocusModal.Title>Error</FocusModal.Title>
          </FocusModal.Header>
          <FocusModal.Body>
            <Text className="text-red-600">
              Failed to load order details: {error.message}
            </Text>
          </FocusModal.Body>
        </FocusModal.Content>
      </FocusModal>
    );
  }

  return (
    <FocusModal open={isOpen} onOpenChange={handleCancel}>
      <FocusModal.Content className=" max-h-[90vh] flex flex-col">
        <FocusModal.Header className="flex-shrink-0">
          <FocusModal.Title>
            {isLoading ? "Loading..." : `Edit Order ${order?.order_number}`}
          </FocusModal.Title>
          <FocusModal.Description>
            Update order status and notes
          </FocusModal.Description>
        </FocusModal.Header>

        <form onSubmit={handleSubmit} className="flex flex-col flex-1 min-h-0">
          <FocusModal.Body className="space-y-6 overflow-y-auto flex-1 min-h-0 p-6">
            {isLoading ? (
              <div className="flex items-center justify-center py-8">
                <Text>Loading order details...</Text>
              </div>
            ) : order ? (
              <>
                {/* Order Overview */}
                <Container className="p-4 bg-ui-bg-subtle rounded-lg">
                  <div className="flex items-center gap-4">
                    <div className="flex items-center gap-2">
                      <Package className="h-4 w-4 text-ui-fg-muted" />
                      <div>
                        <Text className="text-sm text-ui-fg-muted">
                          Order Number
                        </Text>
                        <Text className="font-medium">
                          {order.order_number}
                        </Text>
                      </div>
                    </div>
                    <div>
                      <Text className="text-sm text-ui-fg-muted">Supplier</Text>
                      <Text className="font-medium">{order.supplier_name}</Text>
                    </div>
                    <div>
                      <Text className="text-sm text-ui-fg-muted">
                        Current Status
                      </Text>
                      <Badge variant={getStatusBadgeVariant(order.status)}>
                        {order.status}
                      </Badge>
                    </div>
                  </div>
                </Container>

                {/* Editable Fields */}
                <div className="space-y-4">
                  <Heading level="h3">Order Details</Heading>

                  {/* Status Field */}
                  <div>
                    <Text className="text-sm font-medium mb-2">
                      Order Status
                    </Text>
                    <Select
                      value={formData.status}
                      onValueChange={(value) =>
                        setFormData((prev) => ({ ...prev, status: value }))
                      }
                    >
                      <Select.Trigger>
                        <Select.Value placeholder="Select status" />
                      </Select.Trigger>
                      <Select.Content>
                        {ORDER_STATUSES.map((status) => (
                          <Select.Item key={status.value} value={status.value}>
                            {status.label}
                          </Select.Item>
                        ))}
                      </Select.Content>
                    </Select>
                  </div>

                  {/* Notes Field */}
                  <div>
                    <div className="flex items-center gap-2 mb-2">
                      <FileText className="h-4 w-4 text-ui-fg-muted" />
                      <Text className="text-sm font-medium">Order Notes</Text>
                    </div>
                    <Textarea
                      value={formData.notes}
                      onChange={(e) =>
                        setFormData((prev) => ({
                          ...prev,
                          notes: e.target.value,
                        }))
                      }
                      placeholder="Add notes about this order..."
                      rows={4}
                      className="w-full"
                    />
                    <Text className="text-xs text-ui-fg-muted mt-1">
                      These notes will be visible to all team members
                    </Text>
                  </div>
                </div>

                {/* Order Summary (Read-only) */}
                <Container className="p-4 bg-ui-bg-base border rounded-lg">
                  <Heading level="h4" className="mb-3">
                    Order Summary
                  </Heading>
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <Text className="text-ui-fg-muted">Order Type</Text>
                      <Text className="font-medium">{order.order_type}</Text>
                    </div>
                    <div>
                      <Text className="text-ui-fg-muted">Total Amount</Text>
                      <Text className="font-medium">
                        {formatCurrencyDisplay(order.total_amount, order.currency_code, "en-US")}
                      </Text>
                    </div>
                    <div>
                      <Text className="text-ui-fg-muted">Items Count</Text>
                      <Text className="font-medium">
                        {order.items_count} items
                      </Text>
                    </div>
                    <div>
                      <Text className="text-ui-fg-muted">Created</Text>
                      <Text className="font-medium">
                        {new Date(order.created_at).toLocaleDateString()}
                      </Text>
                    </div>
                  </div>
                </Container>
              </>
            ) : (
              <div className="text-center py-8">
                <Text>Order not found</Text>
              </div>
            )}
          </FocusModal.Body>

          <FocusModal.Footer className="flex-shrink-0">
            <div className="flex items-center justify-between w-full">
              <div>
                {hasChanges && (
                  <Text className="text-sm text-ui-fg-muted">
                    You have unsaved changes
                  </Text>
                )}
              </div>
              <div className="flex items-center gap-2">
                <Button
                  type="button"
                  variant="secondary"
                  onClick={handleCancel}
                  disabled={updateOrderMutation.isPending}
                >
                  Cancel
                </Button>
                <Button
                  type="submit"
                  variant="primary"
                  disabled={
                    !hasChanges || updateOrderMutation.isPending || !order
                  }
                  loading={updateOrderMutation.isPending}
                >
                  {updateOrderMutation.isPending ? "Saving..." : "Save Changes"}
                </Button>
              </div>
            </div>
          </FocusModal.Footer>
        </form>
      </FocusModal.Content>
    </FocusModal>
  );
};
