import React, { useState } from "react";
import {
  FocusModal,
  But<PERSON>,
  Text,
  Select,
  Badge,
  toast,
} from "@camped-ai/ui";
import { AlertCircle } from "lucide-react";

interface BulkStatusChangeModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  selectedIds: string[];
  selectedItems: Array<{ id: string; name: string; status: string }>;
  onStatusChange: (ids: string[], newStatus: string) => Promise<void>;
}

const STATUS_OPTIONS = [
  { value: "active", label: "Active" },
  { value: "inactive", label: "Inactive" },
];

export const BulkStatusChangeModal: React.FC<BulkStatusChangeModalProps> = ({
  open,
  onOpenChange,
  selectedIds,
  selectedItems,
  onStatusChange,
}) => {
  const [newStatus, setNewStatus] = useState<string>("active");
  const [isUpdating, setIsUpdating] = useState(false);

  const handleSubmit = async () => {
    if (!newStatus) {
      toast.error("Please select a status");
      return;
    }

    if (selectedIds.length === 0) {
      toast.error("No items selected");
      return;
    }

    setIsUpdating(true);
    try {
      await onStatusChange(selectedIds, newStatus);
      onOpenChange(false);
      toast.success(`Successfully updated status for ${selectedIds.length} items`);
    } catch (error) {
      console.error("Status change error:", error);
      toast.error(`Failed to update status: ${error instanceof Error ? error.message : "Unknown error"}`);
    } finally {
      setIsUpdating(false);
    }
  };

  const handleCancel = () => {
    setNewStatus("active");
    onOpenChange(false);
  };

  const getStatusBadgeVariant = (status: string) => {
    switch (status) {
      case "active":
        return "green";
      case "inactive":
        return "red";
      default:
        return "grey";
    }
  };

  return (
    <FocusModal open={open} onOpenChange={onOpenChange}>
      <FocusModal.Content className="max-w-md">
        <FocusModal.Header>
          <FocusModal.Title>Change Status</FocusModal.Title>
          <FocusModal.Description>
            Update the status for {selectedIds.length} selected item{selectedIds.length !== 1 ? 's' : ''}.
          </FocusModal.Description>
        </FocusModal.Header>

        <FocusModal.Body className="space-y-4">
          {/* Status Selection */}
          <div>
            <Text size="small" weight="plus" className="mb-2">
              New Status *
            </Text>
            <Select
              value={newStatus}
              onValueChange={setNewStatus}
              disabled={isUpdating}
            >
              <Select.Trigger>
                <Select.Value placeholder="Select new status" />
              </Select.Trigger>
              <Select.Content>
                {STATUS_OPTIONS.map((option) => (
                  <Select.Item key={option.value} value={option.value}>
                    <div className="flex items-center gap-2">
                      <Badge variant={getStatusBadgeVariant(option.value) as any}>
                        {option.label}
                      </Badge>
                    </div>
                  </Select.Item>
                ))}
              </Select.Content>
            </Select>
          </div>

          {/* Simple status summary for multiple items */}
          {selectedIds.length > 1 && (
            <div className="p-3 bg-ui-bg-subtle rounded-lg">
              <div className="flex items-center gap-2">
                <Text size="small" className="text-ui-fg-base">
                  {selectedIds.length} items will be updated to
                </Text>
                <Badge variant={getStatusBadgeVariant(newStatus) as any}>
                  {newStatus}
                </Badge>
              </div>
            </div>
          )}

          {/* Single item preview */}
          {selectedIds.length === 1 && selectedItems.length > 0 && (
            <div className="p-3 bg-ui-bg-subtle rounded-lg">
              <div className="flex items-center justify-between">
                <Text size="small" className="text-ui-fg-base truncate flex-1 mr-2">
                  {selectedItems[0].name}
                </Text>
                <div className="flex items-center gap-2 flex-shrink-0">
                  <Badge variant={getStatusBadgeVariant(selectedItems[0].status) as any} size="small">
                    {selectedItems[0].status}
                  </Badge>
                  <span className="text-ui-fg-muted">→</span>
                  <Badge variant={getStatusBadgeVariant(newStatus) as any} size="small">
                    {newStatus}
                  </Badge>
                </div>
              </div>
            </div>
          )}

          {/* Warning for no change */}
          {selectedItems.length > 0 && selectedItems.every(item => item.status === newStatus) && (
            <div className="flex items-start gap-2 p-3 bg-ui-bg-base border border-ui-border-base rounded-lg">
              <AlertCircle className="h-4 w-4 text-ui-fg-muted mt-0.5 flex-shrink-0" />
              <div>
                <Text size="small" className="text-ui-fg-subtle">
                  {selectedItems.length === 1 ? 'This item is' : 'All selected items are'} already set to "{newStatus}".
                </Text>
              </div>
            </div>
          )}
        </FocusModal.Body>

        <FocusModal.Footer>
          <div className="flex items-center justify-end gap-2">
            <Button
              variant="secondary"
              onClick={handleCancel}
              disabled={isUpdating}
            >
              Cancel
            </Button>
            <Button
              variant="primary"
              onClick={handleSubmit}
              disabled={isUpdating || (selectedItems.length > 0 && selectedItems.every(item => item.status === newStatus))}
              loading={isUpdating}
            >
              {isUpdating ? "Updating..." : "Update Status"}
            </Button>
          </div>
        </FocusModal.Footer>
      </FocusModal.Content>
    </FocusModal>
  );
};
