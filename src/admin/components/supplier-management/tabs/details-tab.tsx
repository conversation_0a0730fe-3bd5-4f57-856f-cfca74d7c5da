import React from "react";
import { Heading, Text } from "@camped-ai/ui";
import { formatCustomFieldValue } from "../../../utils/format-custom-field-value";

interface DetailsTabProps {
  productService: any;
  hotels: any[];
  destinations: any[];
  productServices?: any[];
  getHotelNames: (hotelIds: string) => string;
  getDestinationNames: (destinationIds: string) => string;
}

const DetailsTab: React.FC<DetailsTabProps> = ({
  productService,
  hotels,
  destinations,
  productServices = [],
  getHotelNames,
  getDestinationNames,
}) => {
  return (
    <div className="p-6 space-y-6">
      {/* Description Section */}
      {productService.description && (
        <div>
          <Heading level="h3" className="mb-4">
            Description
          </Heading>
          <Text className="whitespace-pre-wrap">
            {productService.description}
          </Text>
        </div>
      )}

      {/* Custom Fields Section */}
      {productService.custom_fields &&
        productService.category?.dynamic_field_schema &&
        (() => {
          // Filter custom fields to only show those that exist in the current category schema
          const currentSchemaFields =
            productService.category.dynamic_field_schema.filter(
              (field: any) => field.used_in_product !== false // Show field if used_in_product is true or undefined
            );

          const validCustomFields = currentSchemaFields
            .map((fieldSchema: any) => ({
              key: fieldSchema.key,
              value: productService.custom_fields?.[fieldSchema.key],
              schema: fieldSchema,
            }))
            .filter(
              ({ value }) =>
                value !== undefined && value !== null && value !== ""
            );

          return validCustomFields.length > 0 ? (
            <div>
              <Heading level="h3" className="mb-4">
                Custom Fields
              </Heading>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {validCustomFields.map(({ key, value, schema }) => {
                  const fieldLabel =
                    schema.label ||
                    key.charAt(0).toUpperCase() +
                      key.slice(1).replace(/_/g, " ");

                  // For hotel and destination fields, use the same helper functions as the Information tab
                  let displayValue;
                  if (schema.type === "hotels" && value) {
                    displayValue = getHotelNames(
                      typeof value === "string" ? value : JSON.stringify(value)
                    );
                  } else if (schema.type === "destinations" && value) {
                    displayValue = getDestinationNames(
                      typeof value === "string" ? value : JSON.stringify(value)
                    );
                  } else {
                    // Use the utility function to format the value for other field types
                    displayValue = formatCustomFieldValue(
                      value,
                      schema as any,
                      {
                        hotels,
                        destinations,
                        productServices,
                      }
                    );
                  }

                  return (
                    <div key={key}>
                      <Text
                        size="small"
                        weight="plus"
                        className="text-ui-fg-subtle mb-1"
                      >
                        {fieldLabel}
                      </Text>
                      <Text>{displayValue}</Text>
                    </div>
                  );
                })}
              </div>
            </div>
          ) : null;
        })()}
    </div>
  );
};

export default DetailsTab;
