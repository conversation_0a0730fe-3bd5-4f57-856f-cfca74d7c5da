import React from "react";
import { Heading, Text } from "@camped-ai/ui";

interface InformationTabProps {
  productService: any;
}

const InformationTab: React.FC<InformationTabProps> = ({ productService }) => {
  return (
    <div className="p-6 space-y-6">
      {/* Information Section */}
      <div>
        <Heading level="h3" className="mb-4">
          Information
        </Heading>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div>
            <Text size="small" weight="plus" className="text-ui-fg-subtle mb-1">
              Created
            </Text>
            <Text size="small">
              {new Date(productService.created_at).toLocaleDateString()}
            </Text>
          </div>
          <div>
            <Text size="small" weight="plus" className="text-ui-fg-subtle mb-1">
              Last Updated
            </Text>
            <Text size="small">
              {new Date(productService.updated_at).toLocaleDateString()}
            </Text>
          </div>
          <div>
            <Text size="small" weight="plus" className="text-ui-fg-subtle mb-1">
              ID
            </Text>
            <Text size="small" className="font-mono">
              {productService.id}
            </Text>
          </div>
        </div>
      </div>
    </div>
  );
};

export default InformationTab;
