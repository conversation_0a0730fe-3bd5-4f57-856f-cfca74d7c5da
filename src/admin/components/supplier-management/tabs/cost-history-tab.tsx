import React from "react";
import ProductServiceCostHistoryTable from "../product-service-cost-history-table";

interface CostHistoryTabProps {
  productServiceId: string;
  productServiceName: string;
}

const CostHistoryTab: React.FC<CostHistoryTabProps> = ({
  productServiceId,
  productServiceName,
}) => {
  return (
    <div className="p-2">
      {/* Cost History Section */}
      <ProductServiceCostHistoryTable
        productServiceId={productServiceId}
        productServiceName={productServiceName}
      />
    </div>
  );
};

export default CostHistoryTab;
