{"product_services": [{"id": "ps_01K04NDG99VPKE9BHAV4S98KAK", "name": "Kids Club – 4-12", "type": "Service", "description": "Sample kids club description", "base_cost": "4309.99", "highest_price": null, "highest_price_currency": null, "price_flag_active": false, "price_flag_created_at": null, "price_flag_supplier_offering_id": null, "custom_fields": {"age_range": {"max": 12, "min": 4}, "club_type": ["PB Creche", "Yeti Primer"]}, "status": "active", "service_level": "hotel", "hotel_id": null, "destination_id": null, "category_id": "psc_01JZC2BBJFQE091TMMH5EHND1Q", "category": {"id": "psc_01JZC2BBJFQE091TMMH5EHND1Q", "name": "Kids Club", "description": "Activities and services for childrens", "category_type": "Service", "icon": "👦🏼", "dynamic_field_schema": [{"key": "age_range", "type": "number-range", "label": "Age Range", "order": 2, "required": true, "used_in_product": true, "used_in_filtering": true, "locked_in_offerings": true, "used_in_supplier_offering": true}, {"key": "club_type", "type": "multi-select", "label": "Club Type", "order": 1, "options": ["PB Creche", "Yeti Primer", "Yeti Club", "SnoZone Club", "PB Ski Academy", "Teen Guiding", "Cantillon Group", "Carve to Cruise"], "required": false, "used_in_product": true, "used_in_filtering": true, "locked_in_offerings": true, "used_in_supplier_offering": true}], "is_active": true, "created_at": "2025-07-05T01:13:57.584Z", "updated_at": "2025-07-11T12:26:01.735Z", "deleted_at": null}, "unit_type_id": "psut_01JYZN4PQZBC65BR2YYECJVK4F", "unit_type": {"id": "psut_01JYZN4PQZBC65BR2YYECJVK4F", "name": "Per day", "description": null, "is_active": true, "created_at": "2025-06-30T05:32:14.975Z", "updated_at": "2025-06-30T05:32:14.975Z", "deleted_at": null}, "created_at": "2025-07-14T14:28:57.258Z", "updated_at": "2025-07-15T07:09:07.274Z", "deleted_at": null, "tags": [{"id": "pst_01JZQZF5330HA7SMZTY8BT0YKV", "name": "Kids", "color": "#84CC16", "is_active": true, "created_at": "2025-07-09T16:14:29.476Z", "updated_at": "2025-07-09T16:14:29.476Z", "deleted_at": null}]}, {"id": "ps_01K04NE9DPMZVM3DH4B32W8XEC", "name": "Transfers – 100 – Sedan – Hotel Adula, Waldhaus Flims Wellness Resort", "type": "Service", "description": "Sample transfers description", "base_cost": "300", "highest_price": null, "highest_price_currency": null, "price_flag_active": false, "price_flag_created_at": null, "price_flag_supplier_offering_id": null, "custom_fields": {"from": ["Bern Airport (BRN)", "Zurich Airport (ZHR)"], "hotels": ["01JWDQPYHPJT3VMYWEAHB7BSHV", "01JWDR8NDEXZJ012FFY2V7QZ9F"], "to_location": ["Hotel Adula", "Waldhaus Flims Wellness Resort"], "vehicle_type": "Sedan", "passenger_capacity": 100}, "status": "active", "service_level": "hotel", "hotel_id": "[\"01JWDQPYHPJT3VMYWEAHB7BSHV\",\"01JWDR8NDEXZJ012FFY2V7QZ9F\"]", "destination_id": null, "category_id": "psc_01JZC2BC7MDDRH7H30E0JS8TF5", "category": {"id": "psc_01JZC2BC7MDDRH7H30E0JS8TF5", "name": "Transfers", "description": "Vehicle and transport services", "category_type": "Service", "icon": "🚗", "dynamic_field_schema": [{"key": "passenger_capacity", "type": "number", "label": "Passenger Capacity", "order": 2, "required": true, "used_in_product": true, "used_in_filtering": true, "locked_in_offerings": true, "used_in_supplier_offering": true}, {"key": "vehicle_type", "type": "dropdown", "label": "Vehicle Type", "order": 1, "options": ["Sedan", "Minivan", "Bus", "Luxury Car"], "required": true, "used_in_product": true, "used_in_filtering": true, "locked_in_offerings": true, "used_in_supplier_offering": true}, {"key": "to_location", "type": "multi-select", "label": "To Location", "order": 3, "options": ["Waldhaus Flims Wellness Resort", "Hotel Adula", "Hotel Belvedere", "Tschuggen Grand Hotel", "The Chedi Andermatt", "<PERSON><PERSON><PERSON>", "Kulm Hotel", "Badrutt’s Palace"], "required": true, "used_in_product": true, "used_in_filtering": true, "locked_in_offerings": true, "used_in_supplier_offering": true}, {"key": "from", "type": "multi-select", "label": "From", "order": 4, "options": ["Zurich Airport (ZHR)", "Bern Airport (BRN)"], "required": false, "used_in_product": true, "used_in_filtering": false, "locked_in_offerings": true, "used_in_supplier_offering": true}, {"key": "night_operation", "type": "boolean", "label": "night operation", "required": false, "used_in_product": false, "used_in_filtering": true, "locked_in_offerings": false, "used_in_supplier_offering": true}, {"key": "hotels", "type": "hotels", "label": "Hotels", "required": false, "used_in_product": true, "used_in_filtering": false, "locked_in_offerings": false, "used_in_supplier_offering": false}], "is_active": true, "created_at": "2025-07-05T01:13:58.260Z", "updated_at": "2025-07-14T11:24:07.788Z", "deleted_at": null}, "unit_type_id": "psut_01JYZN4PQZBC65BR2YYECJVK4F", "unit_type": {"id": "psut_01JYZN4PQZBC65BR2YYECJVK4F", "name": "Per day", "description": null, "is_active": true, "created_at": "2025-06-30T05:32:14.975Z", "updated_at": "2025-06-30T05:32:14.975Z", "deleted_at": null}, "created_at": "2025-07-14T14:29:22.999Z", "updated_at": "2025-07-15T07:40:27.891Z", "deleted_at": null, "tags": [{"id": "pst_01JZQZF5330HA7SMZTY8BT0YKV", "name": "Kids", "color": "#84CC16", "is_active": true, "created_at": "2025-07-09T16:14:29.476Z", "updated_at": "2025-07-09T16:14:29.476Z", "deleted_at": null}, {"id": "pst_01JZSAJMVYZBRMQM3SJ6EAACS4", "name": "home", "color": "#3B82F6", "is_active": true, "created_at": "2025-07-10T04:47:52.702Z", "updated_at": "2025-07-10T04:47:52.702Z", "deleted_at": null}]}, {"id": "ps_01K04Q9Z66C95R2Q4DHF2M2RJ8", "name": "Vehicle Hire", "type": "Product", "description": "Sample vehicle hire description", "base_cost": "50", "highest_price": "150", "highest_price_currency": "CHF", "price_flag_active": true, "price_flag_created_at": "2025-07-15T08:25:09.539Z", "price_flag_supplier_offering_id": "so_01K06K02A3KXD90SAA8GPCCEY1", "custom_fields": {"no_of_person": 100}, "status": "active", "service_level": "hotel", "hotel_id": null, "destination_id": null, "category_id": "psc_01K04AT83N1NN5HYMXHYZJCWAB", "category": {"id": "psc_01K04AT83N1NN5HYMXHYZJCWAB", "name": "Vehicle Hire", "description": "vehicle hire", "category_type": "Both", "icon": "🚗", "dynamic_field_schema": [{"key": "no_of_person", "type": "number", "label": "No of person", "required": true, "used_in_filtering": false}], "is_active": true, "created_at": "2025-07-14T11:23:40.534Z", "updated_at": "2025-07-14T11:23:40.534Z", "deleted_at": null}, "unit_type_id": "psut_01JYZN4PQZBC65BR2YYECJVK4F", "unit_type": {"id": "psut_01JYZN4PQZBC65BR2YYECJVK4F", "name": "Per day", "description": null, "is_active": true, "created_at": "2025-06-30T05:32:14.975Z", "updated_at": "2025-06-30T05:32:14.975Z", "deleted_at": null}, "created_at": "2025-07-14T15:01:58.598Z", "updated_at": "2025-07-15T08:25:10.104Z", "deleted_at": null, "tags": [{"id": "pst_01JZQZF5330HA7SMZTY8BT0YKV", "name": "Kids", "color": "#84CC16", "is_active": true, "created_at": "2025-07-09T16:14:29.476Z", "updated_at": "2025-07-09T16:14:29.476Z", "deleted_at": null}, {"id": "pst_01JZSAJMVYZBRMQM3SJ6EAACS4", "name": "home", "color": "#3B82F6", "is_active": true, "created_at": "2025-07-10T04:47:52.702Z", "updated_at": "2025-07-10T04:47:52.702Z", "deleted_at": null}]}, {"id": "ps_01K04XZ8NA0FZ6DAHE5BP1ED6Q", "name": "Test – Sample Text – 100 – A, B, 1 – 14/07/2025 – Yes", "type": "Product", "description": "Sample test description", "base_cost": "50", "highest_price": "140", "highest_price_currency": "CHF", "price_flag_active": true, "price_flag_created_at": "2025-07-15T08:25:19.986Z", "price_flag_supplier_offering_id": "so_01K06K0CHG3EKJN8CB8VWRKJEE", "custom_fields": {"date": "2025-07-14", "text": "Sample Text", "hotel": ["01JWDQPYHPJT3VMYWEAHB7BSHV", "01JWDR8NDEXZJ012FFY2V7QZ9F"], "multi": ["1", "A", "B"], "yesno": true, "number": 100, "dropdowm": "Test 1", "destination": ["01JSBNCPRYENAAM64WY5QW8XS5"], "numberrange": "10-20"}, "status": "active", "service_level": "hotel", "hotel_id": null, "destination_id": null, "category_id": "psc_01K04XWF7HK2M5R9S536B221YB", "category": {"id": "psc_01K04XWF7HK2M5R9S536B221YB", "name": "Test", "description": "Test", "category_type": "Product", "icon": null, "dynamic_field_schema": [{"key": "text", "type": "text", "label": "Text", "required": true, "used_in_product": true, "used_in_filtering": false, "locked_in_offerings": false, "used_in_supplier_offering": false}, {"key": "number", "type": "number", "label": "Number", "required": true, "used_in_product": true, "used_in_filtering": false, "locked_in_offerings": false, "used_in_supplier_offering": false}, {"key": "multi", "type": "multi-select", "label": "Multi", "options": ["1", "2", "3"], "required": true, "used_in_product": true, "used_in_filtering": false, "locked_in_offerings": false, "used_in_supplier_offering": false}, {"key": "date", "type": "date", "label": "Date", "required": true, "used_in_product": true, "used_in_filtering": false, "locked_in_offerings": false, "used_in_supplier_offering": false}, {"key": "yesno", "type": "boolean", "label": "YesNo", "required": true, "used_in_product": true, "used_in_filtering": false, "locked_in_offerings": false, "used_in_supplier_offering": false}], "is_active": true, "created_at": "2025-07-14T16:56:56.305Z", "updated_at": "2025-07-14T17:12:20.168Z", "deleted_at": null}, "unit_type_id": "psut_01JYZN4PQZBC65BR2YYECJVK4F", "unit_type": {"id": "psut_01JYZN4PQZBC65BR2YYECJVK4F", "name": "Per day", "description": null, "is_active": true, "created_at": "2025-06-30T05:32:14.975Z", "updated_at": "2025-06-30T05:32:14.975Z", "deleted_at": null}, "created_at": "2025-07-14T16:58:27.883Z", "updated_at": "2025-07-15T08:25:20.366Z", "deleted_at": null, "tags": [{"id": "pst_01JZQZF5330HA7SMZTY8BT0YKV", "name": "Kids", "color": "#84CC16", "is_active": true, "created_at": "2025-07-09T16:14:29.476Z", "updated_at": "2025-07-09T16:14:29.476Z", "deleted_at": null}, {"id": "pst_01JZSAJMVYZBRMQM3SJ6EAACS4", "name": "home", "color": "#3B82F6", "is_active": true, "created_at": "2025-07-10T04:47:52.702Z", "updated_at": "2025-07-10T04:47:52.702Z", "deleted_at": null}]}, {"id": "ps_01K054AF6SGBNVZWZT9YKQATA0", "name": "Accommodation – WiF<PERSON>, Balcony – 01JSBNCPRYENAAM64WY5QW8XS5 – 01JWDQPYHPJT3VMYWEAHB7BSHV,01JWDR8NDEXZJ012FFY2V7QZ9F – Yes – Standard", "type": "Product", "description": "Sample accommodation description", "base_cost": "50", "highest_price": "500", "highest_price_currency": "CHF", "price_flag_active": true, "price_flag_created_at": "2025-07-15T08:25:39.817Z", "price_flag_supplier_offering_id": "so_01K06K0ZSPYWMY1JRPRY6CZ0RV", "custom_fields": {"hotels": ["01JWDQPYHPJT3VMYWEAHB7BSHV", "01JWDR8NDEXZJ012FFY2V7QZ9F"], "amenities": ["Balcony", "WiFi"], "room_type": "Standard", "destination": ["01JSBNCPRYENAAM64WY5QW8XS5"], "pet_friendly": true}, "status": "active", "service_level": "hotel", "hotel_id": null, "destination_id": null, "category_id": "psc_01JZC2BCCAK2X0Y77C7XJXXZNJ", "category": {"id": "psc_01JZC2BCCAK2X0Y77C7XJXXZNJ", "name": "Accommodation", "description": "Hotel rooms and lodging services", "category_type": "Product", "icon": "🏨", "dynamic_field_schema": [{"key": "room_type", "type": "dropdown", "label": "Room Type", "options": ["Standard", "Deluxe", "Suite", "Family Room"], "required": true, "used_in_product": true, "used_in_filtering": true, "locked_in_offerings": true, "used_in_supplier_offering": true}, {"key": "amenities", "type": "multi-select", "label": "Amenities", "options": ["WiFi", "Balcony", "Sea View", "Mini Bar", "<PERSON><PERSON><PERSON><PERSON>"], "required": false, "used_in_product": true, "used_in_filtering": true, "locked_in_offerings": false, "used_in_supplier_offering": true}, {"key": "destination", "type": "destinations", "label": "Destination", "required": true, "used_in_product": true, "used_in_filtering": true, "locked_in_offerings": true, "used_in_supplier_offering": true}, {"key": "pet_friendly", "type": "boolean", "label": "Pet Friendly", "required": false, "used_in_product": true, "used_in_filtering": true, "locked_in_offerings": true, "used_in_supplier_offering": true}, {"key": "hotels", "type": "hotels", "label": "Hotels", "required": true, "used_in_product": true, "used_in_filtering": true, "locked_in_offerings": true, "used_in_supplier_offering": true}], "is_active": true, "created_at": "2025-07-05T01:13:58.410Z", "updated_at": "2025-07-15T05:08:30.386Z", "deleted_at": null}, "unit_type_id": "psut_01JYZN4PQZBC65BR2YYECJVK4F", "unit_type": {"id": "psut_01JYZN4PQZBC65BR2YYECJVK4F", "name": "Per day", "description": null, "is_active": true, "created_at": "2025-06-30T05:32:14.975Z", "updated_at": "2025-06-30T05:32:14.975Z", "deleted_at": null}, "created_at": "2025-07-14T18:49:26.490Z", "updated_at": "2025-07-15T08:25:40.220Z", "deleted_at": null, "tags": [{"id": "pst_01JZQZF5330HA7SMZTY8BT0YKV", "name": "Kids", "color": "#84CC16", "is_active": true, "created_at": "2025-07-09T16:14:29.476Z", "updated_at": "2025-07-09T16:14:29.476Z", "deleted_at": null}, {"id": "pst_01JZSAJMVYZBRMQM3SJ6EAACS4", "name": "home", "color": "#3B82F6", "is_active": true, "created_at": "2025-07-10T04:47:52.702Z", "updated_at": "2025-07-10T04:47:52.702Z", "deleted_at": null}]}, {"id": "ps_01K059K2GS4XAYQQMKFEMF6FN2", "name": "Test2", "type": "Product", "description": "", "base_cost": "20", "highest_price": "140", "highest_price_currency": "CHF", "price_flag_active": true, "price_flag_created_at": "2025-07-15T08:25:49.709Z", "price_flag_supplier_offering_id": "so_01K06K19DZQVW5V7HGXA9JFCV5", "custom_fields": null, "status": "active", "service_level": "hotel", "hotel_id": null, "destination_id": null, "category_id": "psc_01K0594NM6EZ49JXJA30J5CVAM", "category": {"id": "psc_01K0594NM6EZ49JXJA30J5CVAM", "name": "Test2", "description": null, "category_type": "Product", "icon": null, "dynamic_field_schema": [{"key": "duration", "type": "text", "label": "Duration", "required": true, "field_context": "customer", "used_in_product": false, "used_in_filtering": false, "locked_in_offerings": false, "used_in_supplier_offering": false}], "is_active": true, "created_at": "2025-07-14T20:13:39.335Z", "updated_at": "2025-07-14T20:13:39.335Z", "deleted_at": null}, "unit_type_id": "psut_01JYZN4PQZBC65BR2YYECJVK4F", "unit_type": {"id": "psut_01JYZN4PQZBC65BR2YYECJVK4F", "name": "Per day", "description": null, "is_active": true, "created_at": "2025-06-30T05:32:14.975Z", "updated_at": "2025-06-30T05:32:14.975Z", "deleted_at": null}, "created_at": "2025-07-14T20:21:31.290Z", "updated_at": "2025-07-15T08:25:50.033Z", "deleted_at": null, "tags": []}, {"id": "ps_01K065XGDMEX8MKYYY0JXRXKDG", "name": "Kids Club – <PERSON><PERSON> Creche, Yeti Primer – 4-24", "type": "Product", "description": "Sample kids club description", "base_cost": "350", "highest_price": "999", "highest_price_currency": "CHF", "price_flag_active": true, "price_flag_created_at": "2025-07-15T06:02:35.042Z", "price_flag_supplier_offering_id": "so_01K06AV04QHS9M2QGRCJXGGD0B", "custom_fields": {"age_range": "4-24", "club_type": ["PB Creche", "Yeti Primer"]}, "status": "inactive", "service_level": "hotel", "hotel_id": null, "destination_id": null, "category_id": "psc_01JZC2BBJFQE091TMMH5EHND1Q", "category": {"id": "psc_01JZC2BBJFQE091TMMH5EHND1Q", "name": "Kids Club", "description": "Activities and services for childrens", "category_type": "Service", "icon": "👦🏼", "dynamic_field_schema": [{"key": "age_range", "type": "number-range", "label": "Age Range", "order": 2, "required": true, "used_in_product": true, "used_in_filtering": true, "locked_in_offerings": true, "used_in_supplier_offering": true}, {"key": "club_type", "type": "multi-select", "label": "Club Type", "order": 1, "options": ["PB Creche", "Yeti Primer", "Yeti Club", "SnoZone Club", "PB Ski Academy", "Teen Guiding", "Cantillon Group", "Carve to Cruise"], "required": false, "used_in_product": true, "used_in_filtering": true, "locked_in_offerings": true, "used_in_supplier_offering": true}], "is_active": true, "created_at": "2025-07-05T01:13:57.584Z", "updated_at": "2025-07-11T12:26:01.735Z", "deleted_at": null}, "unit_type_id": "psut_01JZ4NBS2SNAF9S03AHVHFJXY1", "unit_type": {"id": "psut_01JZ4NBS2SNAF9S03AHVHFJXY1", "name": "Test Unit", "description": "Test unit type for product service creation", "is_active": true, "created_at": "2025-07-02T04:12:18.906Z", "updated_at": "2025-07-02T04:12:18.906Z", "deleted_at": null}, "created_at": "2025-07-15T04:36:33.332Z", "updated_at": "2025-07-15T06:02:35.275Z", "deleted_at": null, "tags": [{"id": "pst_01JZQZF5330HA7SMZTY8BT0YKV", "name": "Kids", "color": "#84CC16", "is_active": true, "created_at": "2025-07-09T16:14:29.476Z", "updated_at": "2025-07-09T16:14:29.476Z", "deleted_at": null}]}, {"id": "ps_01K068806T6F10BG5J7CNZ585D", "name": "Gym – 01JWT8VVPSCRECG0GBSWBREST7", "type": "Product", "description": "ABCD", "base_cost": "999", "highest_price": null, "highest_price_currency": null, "price_flag_active": false, "price_flag_created_at": null, "price_flag_supplier_offering_id": null, "custom_fields": {"hotel": ["01JWT8VVPSCRECG0GBSWBREST7"]}, "status": "active", "service_level": "hotel", "hotel_id": null, "destination_id": null, "category_id": "psc_01JZXD1EVJT3DZKWYZGWJAYSA1", "category": {"id": "psc_01JZXD1EVJT3DZKWYZGWJAYSA1", "name": "Gym", "description": null, "category_type": "Product", "icon": "🏋️‍♀️", "dynamic_field_schema": [{"key": "hotel", "type": "hotels", "label": "Hotel", "required": false, "used_in_product": true, "used_in_filtering": false, "locked_in_offerings": false, "used_in_supplier_offering": false}], "is_active": true, "created_at": "2025-07-11T18:47:52.948Z", "updated_at": "2025-07-12T11:46:24.650Z", "deleted_at": null}, "unit_type_id": "psut_01JYZN4PQZBC65BR2YYECJVK4F", "unit_type": {"id": "psut_01JYZN4PQZBC65BR2YYECJVK4F", "name": "Per day", "description": null, "is_active": true, "created_at": "2025-06-30T05:32:14.975Z", "updated_at": "2025-06-30T05:32:14.975Z", "deleted_at": null}, "created_at": "2025-07-15T05:17:14.331Z", "updated_at": "2025-07-15T05:31:56.420Z", "deleted_at": null, "tags": [{"id": "pst_01JZQZF5330HA7SMZTY8BT0YKV", "name": "Kids", "color": "#84CC16", "is_active": true, "created_at": "2025-07-09T16:14:29.476Z", "updated_at": "2025-07-09T16:14:29.476Z", "deleted_at": null}, {"id": "pst_01JZSAJMVYZBRMQM3SJ6EAACS4", "name": "home", "color": "#3B82F6", "is_active": true, "created_at": "2025-07-10T04:47:52.702Z", "updated_at": "2025-07-10T04:47:52.702Z", "deleted_at": null}]}, {"id": "ps_01K0696N1W36ZCB949W1MH5XFP", "name": "Gym – 01JWR4FVYJ5DFC8C2VM879QF6T", "type": "Product", "description": "Test Product", "base_cost": "8", "highest_price": null, "highest_price_currency": null, "price_flag_active": false, "price_flag_created_at": null, "price_flag_supplier_offering_id": null, "custom_fields": {"hotel": ["01JWR4FVYJ5DFC8C2VM879QF6T"]}, "status": "active", "service_level": "hotel", "hotel_id": null, "destination_id": null, "category_id": "psc_01JZXD1EVJT3DZKWYZGWJAYSA1", "category": {"id": "psc_01JZXD1EVJT3DZKWYZGWJAYSA1", "name": "Gym", "description": null, "category_type": "Product", "icon": "🏋️‍♀️", "dynamic_field_schema": [{"key": "hotel", "type": "hotels", "label": "Hotel", "required": false, "used_in_product": true, "used_in_filtering": false, "locked_in_offerings": false, "used_in_supplier_offering": false}], "is_active": true, "created_at": "2025-07-11T18:47:52.948Z", "updated_at": "2025-07-12T11:46:24.650Z", "deleted_at": null}, "unit_type_id": "psut_01JYZQBJGY3E2VBRNXR6PXCHTE", "unit_type": {"id": "psut_01JYZQBJGY3E2VBRNXR6PXCHTE", "name": "per trip", "description": null, "is_active": true, "created_at": "2025-06-30T06:10:57.183Z", "updated_at": "2025-06-30T06:10:57.183Z", "deleted_at": null}, "created_at": "2025-07-15T05:33:58.717Z", "updated_at": "2025-07-15T05:51:42.444Z", "deleted_at": null, "tags": [{"id": "pst_01JZSAJMVYZBRMQM3SJ6EAACS4", "name": "home", "color": "#3B82F6", "is_active": true, "created_at": "2025-07-10T04:47:52.702Z", "updated_at": "2025-07-10T04:47:52.702Z", "deleted_at": null}]}, {"id": "ps_01K06A8BPP1CCF8TCDSA4TPHBZ", "name": "Gym", "type": "Product", "description": "Test Product", "base_cost": "10", "highest_price": null, "highest_price_currency": null, "price_flag_active": false, "price_flag_created_at": null, "price_flag_supplier_offering_id": null, "custom_fields": {"hotel": ["01JY1GMSBWC6FA5DTRMPB5K9KV"]}, "status": "active", "service_level": "hotel", "hotel_id": "[\"01JY1GMSBWC6FA5DTRMPB5K9KV\"]", "destination_id": null, "category_id": "psc_01JZXD1EVJT3DZKWYZGWJAYSA1", "category": {"id": "psc_01JZXD1EVJT3DZKWYZGWJAYSA1", "name": "Gym", "description": null, "category_type": "Product", "icon": "🏋️‍♀️", "dynamic_field_schema": [{"key": "hotel", "type": "hotels", "label": "Hotel", "required": false, "used_in_product": true, "used_in_filtering": false, "locked_in_offerings": false, "used_in_supplier_offering": false}], "is_active": true, "created_at": "2025-07-11T18:47:52.948Z", "updated_at": "2025-07-12T11:46:24.650Z", "deleted_at": null}, "unit_type_id": "psut_01JYZQBJGY3E2VBRNXR6PXCHTE", "unit_type": {"id": "psut_01JYZQBJGY3E2VBRNXR6PXCHTE", "name": "per trip", "description": null, "is_active": true, "created_at": "2025-06-30T06:10:57.183Z", "updated_at": "2025-06-30T06:10:57.183Z", "deleted_at": null}, "created_at": "2025-07-15T05:52:23.255Z", "updated_at": "2025-07-15T07:09:09.256Z", "deleted_at": null, "tags": [{"id": "pst_01JZSAJMVYZBRMQM3SJ6EAACS4", "name": "home", "color": "#3B82F6", "is_active": true, "created_at": "2025-07-10T04:47:52.702Z", "updated_at": "2025-07-10T04:47:52.702Z", "deleted_at": null}]}], "count": 10, "limit": 25, "offset": 0}