import React, { useState, useEffect } from "react";
import {
  Bad<PERSON>,
  Text,
  toast,
  Popover,
  Button,
  Switch,
  Select,
} from "@camped-ai/ui";
import { Loader2 } from "lucide-react";
import { CircularProgress } from "@mui/material";

interface PopoverEditStatusCellProps {
  value: string;
  onSave: (status: string) => Promise<void>;
  isLoading?: boolean;
  className?: string;
}

const PopoverEditStatusCell: React.FC<PopoverEditStatusCellProps> = ({
  value,
  onSave,
  isLoading = false,
  className = "",
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [editValue, setEditValue] = useState(value);
  const [isSaving, setIsSaving] = useState(false);

  useEffect(() => {
    setEditValue(value);
  }, [value]);

  const getStatusBadgeVariant = (status: string) => {
    switch (status) {
      case "active":
        return "green";
      case "inactive":
        return "red";
      default:
        return "grey";
    }
  };

  const handleSave = async () => {
    if (editValue === value) {
      setIsOpen(false);
      return;
    }

    setIsSaving(true);
    try {
      await onSave(editValue);
      setIsOpen(false);
      toast.success("Status updated successfully");
    } catch (error: any) {
      // Handle specific error types for better user experience
      let errorMessage = "Failed to update status";

      if (error?.response?.status === 409) {
        errorMessage = error?.response?.data?.message || error.message || "Conflict with existing data";
      } else if (error?.response?.status === 400) {
        errorMessage = error?.response?.data?.message || error.message || "Invalid data provided";
      } else if (error instanceof Error) {
        errorMessage = error.message;
      }

      toast.error(errorMessage);
      setEditValue(value); // Reset to original value
    } finally {
      setIsSaving(false);
    }
  };

  const handleCancel = () => {
    setEditValue(value);
    setIsOpen(false);
  };

  const handleToggleChange = (checked: boolean) => {
    setEditValue(checked ? "active" : "inactive");
  };

  const handleSelectChange = (newValue: string) => {
    setEditValue(newValue);
  };

  return (
    <Popover open={isOpen} onOpenChange={setIsOpen}>
      <Popover.Trigger asChild>
        <div
          className={`cursor-pointer hover:bg-ui-bg-subtle rounded px-2 py-1 transition-colors border border-transparent hover:border-ui-border-base ${isLoading ? 'opacity-50' : ''} ${className}`}
          title={isLoading ? "Updating..." : "Click to edit"}
        >
          <div className="flex items-center gap-2">
            <Badge variant={getStatusBadgeVariant(value) as any}>
              {value}
            </Badge>
            {isLoading && <CircularProgress size={12} />}
          </div>
          <div className="text-xs text-ui-fg-subtle mt-1">
            {isLoading ? "Updating..." : "Click to edit"}
          </div>
        </div>
      </Popover.Trigger>
      <Popover.Content side="top" align="start" className="w-[250px] p-4">
        <div className="space-y-4">
          <div>
            <Text size="small" weight="plus" className="mb-3">
              Status
            </Text>
            
            {/* Toggle Switch Option */}
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <Text size="small">Active</Text>
                <Switch
                  checked={editValue === "active"}
                  onCheckedChange={handleToggleChange}
                  disabled={isSaving}
                />
              </div>
              
              
            </div>
          </div>

          <div className="flex items-center justify-end gap-2 pt-2 border-t">
            <Button
              variant="secondary"
              size="small"
              onClick={handleCancel}
              disabled={isSaving}
            >
              Cancel
            </Button>
            <Button
              variant="primary"
              size="small"
              onClick={handleSave}
              disabled={isSaving}
            >
              {isSaving ? <Loader2 className="h-3 w-3 animate-spin mr-1" /> : null}
              Save
            </Button>
          </div>
        </div>
      </Popover.Content>
    </Popover>
  );
};

export default PopoverEditStatusCell;
