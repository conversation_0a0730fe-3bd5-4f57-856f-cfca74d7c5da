import React, { useState, useEffect } from "react";
import {
  Table,
  Text,
  Badge,
  <PERSON><PERSON>,
  Date<PERSON>icker,
  Container,
  Heading,
} from "@camped-ai/ui";
import { TrendingUp, TrendingDown } from "lucide-react";
import {
  useProductServiceCostHistory,
  useProductServiceCostHistoryStats,
  getCostChangeType,
  type ProductServiceCostHistory,
  type ProductServiceCostHistoryFilters,
} from "../../hooks/supplier-products-services/use-product-service-cost-history";
import {
  useUsersLookup,
  formatUserDisplay,
  getUserDisplayFromId,
} from "../../hooks/use-user-lookup";

interface ProductServiceCostHistoryTableProps {
  productServiceId: string;
  productServiceName?: string;
}

const ProductServiceCostHistoryTable: React.FC<
  ProductServiceCostHistoryTableProps
> = ({ productServiceId, productServiceName }) => {
  const [filters, setFilters] = useState<ProductServiceCostHistoryFilters>({
    limit: 25,
    offset: 0,
  });

  const [dateFrom, setDateFrom] = useState<Date | undefined>(undefined);
  const [dateTo, setDateTo] = useState<Date | undefined>(undefined);

  // Fetch cost history data
  const {
    data: costHistoryData,
    isLoading,
    error,
    refetch,
  } = useProductServiceCostHistory(productServiceId, filters);

  // Fetch cost history stats
  const {
    data: statsData,
    isLoading: statsLoading,
    error: statsError,
  } = useProductServiceCostHistoryStats(productServiceId);

  // Get unique user IDs from cost history data for batch lookup
  const userIds = React.useMemo(() => {
    const ids =
      costHistoryData?.data
        ?.map(
          (history: ProductServiceCostHistory) => history.changed_by_user_id
        )
        .filter((id: string | null | undefined): id is string => !!id) || [];
    return [...new Set(ids)]; // Remove duplicates
  }, [costHistoryData?.data]);

  // Fetch user details for all user IDs
  const { data: users = {} } = useUsersLookup(userIds as string[]);

  // Function to display user information with proper lookup
  const displayUser = (userId: string | null | undefined): string => {
    if (!userId) return "System";

    // Check if we have user data from the lookup
    const user = users[userId];
    if (user) {
      return formatUserDisplay(user); // This will return email address first
    }

    // Fallback to utility function for formatting
    return getUserDisplayFromId(userId);
  };

  // Apply filters
  useEffect(() => {
    const newFilters: ProductServiceCostHistoryFilters = {
      limit: 25,
      offset: 0,
    };

    if (dateFrom) {
      newFilters.date_from = dateFrom.toISOString();
    }

    if (dateTo) {
      newFilters.date_to = dateTo.toISOString();
    }

    setFilters(newFilters);
  }, [dateFrom, dateTo]);

  const handleClearFilters = () => {
    setDateFrom(undefined);
    setDateTo(undefined);
    setFilters({ limit: 25, offset: 0 });
  };

  const handleRefresh = () => {
    refetch();
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString("en-US", {
      month: "short",
      day: "numeric",
      year: "numeric",
      hour: "numeric",
      minute: "2-digit",
      hour12: true,
    });
  };

  const formatCurrency = (amount: number | string | null | undefined) => {
    if (amount === null || amount === undefined) return "—";

    // Convert to number if it's a string
    const numAmount = typeof amount === "string" ? parseFloat(amount) : amount;

    // Check if conversion resulted in a valid number
    if (isNaN(numAmount)) return "—";

    return `${numAmount.toFixed(2)} CHF`;
  };

  const getCostChangeBadge = (history: ProductServiceCostHistory) => {
    const changeType = getCostChangeType(history);

    if (changeType === "increase") {
      return (
        <Badge className="bg-green-100  text-green-800 border-green-200 ">
          <TrendingUp className="w-3 h-3 mr-1" />
          Up
        </Badge>
      );
    } else if (changeType === "decrease") {
      return (
        <Badge className=" bg-red-100 text-red-800  border-red-200">
          <TrendingDown className="w-3 h-3 mr-1" />
          Down
        </Badge>
      );
    }

    return (
      <Badge className="pt-5 pb-5">No Change</Badge>
    );
  };

  const filteredData = costHistoryData?.data || [];

  if (error) {
    return (
      <Container>
        <Text className="text-red-600">
          Failed to load cost history: {error.message}
        </Text>
      </Container>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <Heading level="h3">Cost History</Heading>
          <Text size="small" className="text-ui-fg-subtle">
            Track of all cost and currency changes for this offering
          </Text>
        </div>
        <Button onClick={handleRefresh} variant="secondary" size="small">
          Refresh
        </Button>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div className="bg-ui-bg-subtle rounded-lg p-4">
          <Text size="small" className="text-ui-fg-subtle mb-1">
            Total Changes
          </Text>
          <Text size="xlarge" weight="plus">
            {statsLoading ? "—" : statsData?.total_changes || 0}
          </Text>
        </div>

        <div className="bg-green-50 rounded-lg p-4">
          <Text size="small" className="text-ui-fg-subtle mb-1">
            Cost Increases
          </Text>
          <Text size="xlarge" weight="plus" className="text-green-600">
            {statsLoading ? "—" : statsData?.cost_increases || 0}
          </Text>
        </div>

        <div className="bg-red-50 rounded-lg p-4">
          <Text size="small" className="text-ui-fg-subtle mb-1">
            Cost Decreases
          </Text>
          <Text size="xlarge" weight="plus" className="text-red-600">
            {statsLoading ? "—" : statsData?.cost_decreases || 0}
          </Text>
        </div>

        <div className="bg-blue-50 rounded-lg p-4">
          <Text size="small" className="text-ui-fg-subtle mb-1">
            Currency Changes
          </Text>
          <Text size="xlarge" weight="plus" className="text-blue-600">
            0
          </Text>
        </div>
      </div>

      {/* Filters */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 p-4 bg-ui-bg-subtle rounded-lg">
        <div>
          <Text size="small" className="text-ui-fg-subtle mb-1">
            From Date
          </Text>
          <DatePicker
            value={dateFrom}
            onChange={(value) => setDateFrom(value || undefined)}
          />
        </div>

        <div>
          <Text size="small" className="text-ui-fg-subtle mb-1">
            To Date
          </Text>
          <DatePicker
            value={dateTo}
            onChange={(value) => setDateTo(value || undefined)}
          />
        </div>

        <div className="flex items-end">
          <Button
            onClick={handleClearFilters}
            variant="secondary"
            size="small"
            className="w-full"
          >
            Clear Filters
          </Button>
        </div>
      </div>

      {/* Desktop Table */}
      <div className="hidden md:block">
        <Table>
          <Table.Header>
            <Table.Row>
              <Table.HeaderCell>Change</Table.HeaderCell>
              <Table.HeaderCell>Old Cost</Table.HeaderCell>
              <Table.HeaderCell>New Cost</Table.HeaderCell>
              <Table.HeaderCell>Currency</Table.HeaderCell>
              <Table.HeaderCell>Notes</Table.HeaderCell>
              <Table.HeaderCell>Changed By</Table.HeaderCell>
              <Table.HeaderCell>Date</Table.HeaderCell>
            </Table.Row>
          </Table.Header>
          <Table.Body>
            {isLoading ? (
              <Table.Row>
                <Table.Cell
                  className="text-center"
                  style={{ gridColumn: "1 / -1" }}
                >
                  <Text className="text-center py-4">Loading...</Text>
                </Table.Cell>
              </Table.Row>
            ) : filteredData.length === 0 ? (
              <Table.Row>
                <Table.Cell
                  className="text-center"
                  style={{ gridColumn: "1 / -1" }}
                >
                  <Text className="text-center py-4 text-ui-fg-subtle">
                    No cost history found
                  </Text>
                </Table.Cell>
              </Table.Row>
            ) : (
              filteredData.map((history: ProductServiceCostHistory) => (
                <Table.Row key={history.id}>
                  <Table.Cell>
                    <div className="flex items-center gap-2">
                      {getCostChangeBadge(history)}
                    </div>
                  </Table.Cell>
                  <Table.Cell>
                    <Text size="small">
                      {formatCurrency(history.previous_cost)}
                    </Text>
                  </Table.Cell>
                  <Table.Cell>
                    <Text size="small">{formatCurrency(history.new_cost)}</Text>
                  </Table.Cell>
                  <Table.Cell>
                    <Text size="small">CHF</Text>
                  </Table.Cell>
                  <Table.Cell>
                    <div className="max-w-xs">
                      <Text
                        size="small"
                        className="line-clamp-2"
                        title={history.change_reason || "No notes provided"}
                      >
                        {history.change_reason || "Updated via admin interface"}
                      </Text>
                    </div>
                  </Table.Cell>
                  <Table.Cell>
                    <Text size="small">
                      {displayUser(history.changed_by_user_id)}
                    </Text>
                  </Table.Cell>
                  <Table.Cell>
                    <Text size="small">{formatDate(history.created_at)}</Text>
                  </Table.Cell>
                </Table.Row>
              ))
            )}
          </Table.Body>
        </Table>
      </div>

      {/* Mobile Cards */}
      <div className="md:hidden space-y-4">
        {isLoading ? (
          <Text className="text-center py-4">Loading...</Text>
        ) : filteredData.length === 0 ? (
          <Text className="text-center py-4 text-ui-fg-subtle">
            No cost history found
          </Text>
        ) : (
          filteredData.map((history: ProductServiceCostHistory) => (
            <div
              key={history.id}
              className="bg-ui-bg-base border border-ui-border-base rounded-lg p-4 space-y-3"
            >
              <div className="flex items-center justify-between">
                <Text size="small" className="text-ui-fg-subtle">
                  {formatDate(history.created_at)}
                </Text>
                {getCostChangeBadge(history)}
              </div>

              <div className="grid grid-cols-3 gap-4">
                <div>
                  <Text size="small" className="text-ui-fg-subtle">
                    Old Cost
                  </Text>
                  <Text size="small">
                    {formatCurrency(history.previous_cost)}
                  </Text>
                </div>
                <div>
                  <Text size="small" className="text-ui-fg-subtle">
                    New Cost
                  </Text>
                  <Text size="small">{formatCurrency(history.new_cost)}</Text>
                </div>
                <div>
                  <Text size="small" className="text-ui-fg-subtle">
                    Currency
                  </Text>
                  <Text size="small">CHF</Text>
                </div>
              </div>

              <div>
                <Text size="small" className="text-ui-fg-subtle">
                  Notes
                </Text>
                <Text
                  size="small"
                  className="line-clamp-3 whitespace-pre-wrap"
                  title={history.change_reason || "Updated via admin interface"}
                >
                  {history.change_reason || "Updated via admin interface"}
                </Text>
              </div>

              <div>
                <Text size="small" className="text-ui-fg-subtle">
                  Changed By
                </Text>
                <Text size="small">
                  {displayUser(history.changed_by_user_id)}
                </Text>
              </div>
            </div>
          ))
        )}
      </div>

      {/* Pagination Info */}
      {costHistoryData && (
        <div className="flex items-center justify-between text-sm text-ui-fg-subtle">
          <Text size="small">
            Showing {filteredData.length} of {costHistoryData.count} entries
          </Text>
        </div>
      )}
    </div>
  );
};

export default ProductServiceCostHistoryTable;
