import { Container } from "@camped-ai/ui";

export const ProductServiceEditSkeleton = () => {
  return (
    <Container>
      <div className="animate-pulse space-y-6">
        {/* Header with buttons skeleton */}
        <div className="flex flex-row items-center justify-between">
          <div className="flex flex-row items-center gap-4">
            <div className="h-8 w-8 bg-ui-bg-subtle dark:bg-ui-bg-subtle rounded" />
            <div className="space-y-1">
              <div className="h-6 w-48 bg-ui-bg-subtle dark:bg-ui-bg-subtle rounded" />
              <div className="h-4 w-64 bg-ui-bg-subtle dark:bg-ui-bg-subtle rounded" />
            </div>
          </div>
          <div className="flex items-center gap-4">
            <div className="h-9 w-16 bg-ui-bg-subtle dark:bg-ui-bg-subtle rounded" />
            <div className="h-9 w-24 bg-ui-bg-subtle dark:bg-ui-bg-subtle rounded" />
          </div>
        </div>

        {/* Tab navigation skeleton - matching actual flat design */}
        <div className="space-y-6">
          <div className="flex items-center gap-6">
            <div className="flex items-center gap-2">
              <div className="h-4 w-4 bg-ui-bg-subtle dark:bg-ui-bg-subtle rounded" />
              <div className="h-5 w-28 bg-ui-bg-subtle dark:bg-ui-bg-subtle rounded" />
            </div>
            <div className="flex items-center gap-2">
              <div className="h-4 w-4 bg-ui-bg-subtle dark:bg-ui-bg-subtle rounded" />
              <div className="h-5 w-24 bg-ui-bg-subtle dark:bg-ui-bg-subtle rounded" />
            </div>
            <div className="flex items-center gap-2">
              <div className="h-4 w-4 bg-ui-bg-subtle dark:bg-ui-bg-subtle rounded" />
              <div className="h-5 w-26 bg-ui-bg-subtle dark:bg-ui-bg-subtle rounded" />
            </div>
            <div className="flex items-center gap-2">
              <div className="h-4 w-4 bg-ui-bg-subtle dark:bg-ui-bg-subtle rounded" />
              <div className="h-5 w-22 bg-ui-bg-subtle dark:bg-ui-bg-subtle rounded" />
            </div>
          </div>

          {/* Tab content skeleton */}
          <div className="bg-ui-bg-base dark:bg-ui-bg-base rounded-lg border border-ui-border-base dark:border-ui-border-base p-6 space-y-6">
            <div className="h-6 w-40 bg-ui-bg-subtle dark:bg-ui-bg-subtle rounded" />
            
            {/* Form fields skeleton */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <div className="h-4 w-24 bg-ui-bg-subtle dark:bg-ui-bg-subtle rounded" />
                <div className="h-10 w-full bg-ui-bg-subtle dark:bg-ui-bg-subtle rounded" />
              </div>
              <div className="space-y-2">
                <div className="h-4 w-16 bg-ui-bg-subtle dark:bg-ui-bg-subtle rounded" />
                <div className="h-10 w-full bg-ui-bg-subtle dark:bg-ui-bg-subtle rounded" />
              </div>
              <div className="md:col-span-2 space-y-2">
                <div className="h-4 w-20 bg-ui-bg-subtle dark:bg-ui-bg-subtle rounded" />
                <div className="h-20 w-full bg-ui-bg-subtle dark:bg-ui-bg-subtle rounded" />
              </div>
              <div className="space-y-2">
                <div className="h-4 w-28 bg-ui-bg-subtle dark:bg-ui-bg-subtle rounded" />
                <div className="h-10 w-full bg-ui-bg-subtle dark:bg-ui-bg-subtle rounded" />
              </div>
              <div className="space-y-2">
                <div className="h-4 w-16 bg-ui-bg-subtle dark:bg-ui-bg-subtle rounded" />
                <div className="h-10 w-full bg-ui-bg-subtle dark:bg-ui-bg-subtle rounded" />
              </div>
              <div className="md:col-span-2 space-y-2">
                <div className="h-4 w-12 bg-ui-bg-subtle dark:bg-ui-bg-subtle rounded" />
                <div className="h-10 w-full bg-ui-bg-subtle dark:bg-ui-bg-subtle rounded" />
                <div className="h-4 w-64 bg-ui-bg-subtle dark:bg-ui-bg-subtle rounded" />
              </div>
            </div>
          </div>
        </div>
      </div>
    </Container>
  );
};
