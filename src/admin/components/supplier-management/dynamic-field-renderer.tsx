import React from "react";
import { Input, Label, Select, Text, Heading, Badge } from "@camped-ai/ui";
import { X } from "lucide-react";
import { useHotels } from "../../hooks/supplier-products-services/use-hotels";
import { useDestinations } from "../../hooks/supplier-products-services/use-destinations";
import { useProductsServices } from "../../hooks/supplier-products-services/use-products-services";
import { MultiSelect } from "../common/MultiSelect";

export interface DynamicFieldSchema {
  label: string;
  key: string;
  type:
  | "text"
  | "number"
  | "dropdown"
  | "multi-select"
  | "date"
  | "time"
  | "time-range"
  | "date-range"
  | "boolean"
  | "number-range"
  | "hotels"
  | "destinations"
  | "addons";
  options?: string[];
  required: boolean;
  used_in_filtering?: boolean;
  used_in_supplier_offering?: boolean;
  used_in_product?: boolean;
  used_in_product_services?: boolean; // NEW: Field for product and services usage
  locked_in_offerings?: boolean;
  field_context?: "supplier" | "customer"; // NEW: Context filtering for fields

}

interface DynamicFieldRendererProps {
  schema: DynamicFieldSchema[];
  values: Record<string, any>;
  onChange: (key: string, value: any) => void;
  errors?: Record<string, string>;
  disabled?: boolean;
  inheritedValues?: Record<string, any>; // Values inherited from product/service
  showInheritanceIndicators?: boolean; // Whether to show inheritance indicators
  fieldContext?: "supplier" | "customer"; // NEW: Filter fields by context (default: show all)
  onAddonSelectionChange?: (
    fieldKey: string,
    selectedAddonIds: string[]
  ) => void; // Callback for addon selection changes
  excludeProductServiceId?: string; // ID of current product service to exclude from addon options
}

const DynamicFieldRenderer: React.FC<DynamicFieldRendererProps> = ({
  schema,
  values,
  onChange,
  errors = {},
  disabled = false,
  inheritedValues = {},
  showInheritanceIndicators = false,
  fieldContext, // NEW: Context filter
  onAddonSelectionChange,
  excludeProductServiceId,
}) => {
  // Fetch hotels, destinations, and product services data
  const { data: hotelsResponse } = useHotels({ is_active: true });
  const { data: destinationsResponse } = useDestinations({ is_active: true });
  const { data: productServicesResponse } = useProductsServices({
    status: "active",
    limit: 1000,
  });

  const hotels = hotelsResponse?.hotels || [];
  const destinations = destinationsResponse?.destinations || [];
  const productServices = productServicesResponse?.product_services || [];

  if (!schema || schema.length === 0) {
    return null;
  }

  // Filter schema by field context if specified
  const filteredSchema : any = fieldContext
    ? schema.filter(
      (field) => !field.field_context || field.field_context === fieldContext
    )
    : schema;

  if (filteredSchema.length === 0) {
    return null;
  }


  const renderFieldLabel = (field: DynamicFieldSchema) => {
    const isLocked = field.locked_in_offerings && showInheritanceIndicators;
    if(isLocked && field?.type === 'addons'){
      return;
    }
    return (
      <div className="flex items-center gap-2">
        <Label htmlFor={`custom_field_${field.key}`}>
          {field.label}
          {field.required && <span className="text-red-500 ml-1">*</span>}
        </Label>
      </div>
    );
  };



  const renderField = (field: DynamicFieldSchema) => {
    const isLocked = field.locked_in_offerings && showInheritanceIndicators;
    if(isLocked && field?.type === 'addons'){
      return;
    }
    const inheritedValue = inheritedValues[field.key];
    const displayValue =
      isLocked && inheritedValue !== undefined
        ? inheritedValue
        : values[field.key];
    const error = errors[field.key];
    const fieldId = `custom_field_${field.key}`;
    const isFieldDisabled = disabled || isLocked;

    switch (field.type) {
      case "text":
        return (
          <div key={field.key} className="space-y-2">
            {renderFieldLabel(field)}
            <Input
              id={fieldId}
              value={displayValue || ""}
              onChange={(e) => onChange(field.key, e.target.value)}
              placeholder={`Enter ${field.label.toLowerCase()}`}
              disabled={isFieldDisabled}
              className={error ? "border-red-500" : ""}
            />
            {error && (
              <Text size="small" className="text-red-600">
                {error}
              </Text>
            )}
          </div>
        );

      case "number":
        return (
          <div key={field.key} className="space-y-2">
            {renderFieldLabel(field)}
            <Input
              id={fieldId}
              type="number"
              value={displayValue || ""}
              onChange={(e) =>
                onChange(
                  field.key,
                  e.target.value ? parseFloat(e.target.value) : ""
                )
              }
              placeholder={`Enter ${field.label.toLowerCase()}`}
              disabled={isFieldDisabled}
              className={error ? "border-red-500" : ""}
            />
            {error && (
              <Text size="small" className="text-red-600">
                {error}
              </Text>
            )}
          </div>
        );

      case "dropdown":
        return (
          <div key={field.key} className="space-y-2">
            {renderFieldLabel(field)}
            <Select
              value={displayValue || ""}
              onValueChange={(selectedValue) =>
                onChange(field.key, selectedValue)
              }
              disabled={isFieldDisabled}
            >
              <Select.Trigger className={error ? "border-red-500" : ""}>
                <Select.Value
                  placeholder={`Select ${field.label.toLowerCase()}`}
                />
              </Select.Trigger>
              <Select.Content>
                {field.options?.map((option) => (
                  <Select.Item key={option} value={option}>
                    {option}
                  </Select.Item>
                ))}
              </Select.Content>
            </Select>
            {error && (
              <Text size="small" className="text-red-600">
                {error}
              </Text>
            )}
          </div>
        );

      case "multi-select":
        const selectedValues = Array.isArray(displayValue) ? displayValue : [];
        // Sort selected values alphabetically for consistent display
        const sortedSelectedValues = [...selectedValues].sort();
        return (
          <div key={field.key} className="space-y-2">
            {renderFieldLabel(field)}
            <Select
              value=""
              onValueChange={(selectedValue) => {
                if (selectedValue && !selectedValues.includes(selectedValue)) {
                  onChange(field.key, [...selectedValues, selectedValue]);
                }
              }}
              disabled={isFieldDisabled}
            >
              <Select.Trigger className={error ? "border-red-500" : ""}>
                <Select.Value
                  placeholder={`Select ${field.label.toLowerCase()}`}
                />
              </Select.Trigger>
              <Select.Content>
                {field.options
                  ?.filter((option) => !selectedValues.includes(option))
                  .sort() // Sort options alphabetically
                  .map((option) => (
                    <Select.Item key={option} value={option}>
                      {option}
                    </Select.Item>
                  ))}
              </Select.Content>
            </Select>
            {sortedSelectedValues.length > 0 && (
              <div className="flex flex-wrap gap-2 mt-2">
                {sortedSelectedValues.map((selectedValue) => (
                  <div
                    key={selectedValue}
                    className="flex items-center gap-1 px-2 py-1 bg-gray-100 rounded-md text-sm"
                  >
                    {selectedValue}
                    <button
                      type="button"
                      onClick={() => {
                        onChange(
                          field.key,
                          selectedValues.filter((v) => v !== selectedValue)
                        );
                      }}
                      className="ml-1 text-gray-500 hover:text-red-500"
                      disabled={isFieldDisabled}
                    >
                      <X className="h-3 w-3" />
                    </button>
                  </div>
                ))}
              </div>
            )}
            {error && (
              <Text size="small" className="text-red-600">
                {error}
              </Text>
            )}
          </div>
        );

      case "date":
        // Ensure date value is in YYYY-MM-DD format for HTML date input
        let dateValue = "";
        if (displayValue) {
          if (typeof displayValue === "string") {
            // Try to parse and format the date
            const date = new Date(displayValue);
            if (!isNaN(date.getTime())) {
              dateValue = date.toISOString().split("T")[0]; // YYYY-MM-DD format
            } else {
              dateValue = displayValue; // Use as-is if it's already in correct format
            }
          }
        }

        return (
          <div key={field.key} className="space-y-2">
            {renderFieldLabel(field)}
            <Input
              id={fieldId}
              type="date"
              value={dateValue}
              onChange={(e) => onChange(field.key, e.target.value)}
              disabled={isFieldDisabled}
              className={error ? "border-red-500" : ""}
            />
            {error && (
              <Text size="small" className="text-red-600">
                {error}
              </Text>
            )}
          </div>
        );

      case "time":
        // Ensure time value is in HH:MM format for HTML time input
        let timeValue = "";
        if (displayValue) {
          if (typeof displayValue === "string") {
            // If it's already in HH:MM format, use as-is
            if (/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/.test(displayValue)) {
              timeValue = displayValue;
            } else {
              // Try to parse as a date and extract time
              const date = new Date(displayValue);
              if (!isNaN(date.getTime())) {
                timeValue = date.toTimeString().slice(0, 5); // HH:MM format
              } else {
                timeValue = displayValue; // Use as-is if it's already in correct format
              }
            }
          }
        }

        return (
          <div key={field.key} className="space-y-2">
            {renderFieldLabel(field)}
            <Input
              id={fieldId}
              type="time"
              value={timeValue}
              onChange={(e) => onChange(field.key, e.target.value)}
              disabled={isFieldDisabled}
              className={error ? "border-red-500" : ""}
            />
            {error && (
              <Text size="small" className="text-red-600">
                {error}
              </Text>
            )}
          </div>
        );

      case "time-range":
        // Parse time range value - expect JSON object with "from" and "to" properties
        let timeRangeValue = { from: "", to: "" };
        if (displayValue) {
          if (typeof displayValue === "string") {
            try {
              const parsed = JSON.parse(displayValue);
              if (parsed && typeof parsed === "object") {
                timeRangeValue = {
                  from: parsed.from || "",
                  to: parsed.to || "",
                };
              }
            } catch {
              // If parsing fails, keep default empty values
            }
          } else if (typeof displayValue === "object" && displayValue !== null) {
            timeRangeValue = {
              from: displayValue.from || "",
              to: displayValue.to || "",
            };
          }
        }

        const handleTimeRangeChange = (type: "from" | "to", value: string) => {
          const newValue = {
            ...timeRangeValue,
            [type]: value,
          };

          // Always update the value, but show validation error if needed
          onChange(field.key, JSON.stringify(newValue));
        };

        // Validate time range for error display (but don't prevent updates)
        let timeRangeError = error;
        if (timeRangeValue.from && timeRangeValue.to) {
          const fromTime = new Date(`1970-01-01T${timeRangeValue.from}:00`);
          const toTime = new Date(`1970-01-01T${timeRangeValue.to}:00`);

          if (toTime <= fromTime) {
            timeRangeError = "End time must be after start time";
          }
        }

        return (
          <div key={field.key} className="space-y-2">
            {renderFieldLabel(field)}
            <div className="grid grid-cols-2 gap-3">
              <div className="space-y-1">
                <Label htmlFor={`${fieldId}_from`} className="text-xs text-gray-600">
                  From Time
                </Label>
                <Input
                  id={`${fieldId}_from`}
                  type="time"
                  value={timeRangeValue.from}
                  onChange={(e) => handleTimeRangeChange("from", e.target.value)}
                  disabled={isFieldDisabled}
                  className={timeRangeError ? "border-red-500" : ""}
                />
              </div>
              <div className="space-y-1">
                <Label htmlFor={`${fieldId}_to`} className="text-xs text-gray-600">
                  To Time
                </Label>
                <Input
                  id={`${fieldId}_to`}
                  type="time"
                  value={timeRangeValue.to}
                  onChange={(e) => handleTimeRangeChange("to", e.target.value)}
                  disabled={isFieldDisabled}
                  className={timeRangeError ? "border-red-500" : ""}
                />
              </div>
            </div>
            {timeRangeError && (
              <Text size="small" className="text-red-600">
                {timeRangeError}
              </Text>
            )}
          </div>
        );

      case "date-range":
        // Parse date range value - expect JSON object with "from" and "to" properties
        let dateRangeValue = { from: "", to: "" };
        if (displayValue) {
          if (typeof displayValue === "string") {
            try {
              const parsed = JSON.parse(displayValue);
              if (parsed && typeof parsed === "object") {
                dateRangeValue = {
                  from: parsed.from || "",
                  to: parsed.to || "",
                };
              }
            } catch {
              // If parsing fails, keep default empty values
            }
          } else if (typeof displayValue === "object" && displayValue !== null) {
            dateRangeValue = {
              from: displayValue.from || "",
              to: displayValue.to || "",
            };
          }
        }

        const handleDateRangeChange = (type: "from" | "to", value: string) => {
          const newValue = {
            ...dateRangeValue,
            [type]: value,
          };

          // Always update the value, but show validation error if needed
          onChange(field.key, JSON.stringify(newValue));
        };

        // Validate date range for error display (but don't prevent updates)
        let dateRangeError = error;
        if (dateRangeValue.from && dateRangeValue.to) {
          const fromDate = new Date(dateRangeValue.from);
          const toDate = new Date(dateRangeValue.to);

          if (toDate <= fromDate) {
            dateRangeError = "End date must be after start date";
          }
        }

        return (
          <div key={field.key} className="space-y-2">
            {renderFieldLabel(field)}
            <div className="grid grid-cols-2 gap-3">
              <div className="space-y-1">
                <Label htmlFor={`${fieldId}_from`} className="text-xs text-gray-600">
                  From Date
                </Label>
                <Input
                  id={`${fieldId}_from`}
                  type="date"
                  value={dateRangeValue.from}
                  onChange={(e) => handleDateRangeChange("from", e.target.value)}
                  disabled={isFieldDisabled}
                  className={dateRangeError ? "border-red-500" : ""}
                />
              </div>
              <div className="space-y-1">
                <Label htmlFor={`${fieldId}_to`} className="text-xs text-gray-600">
                  To Date
                </Label>
                <Input
                  id={`${fieldId}_to`}
                  type="date"
                  value={dateRangeValue.to}
                  onChange={(e) => handleDateRangeChange("to", e.target.value)}
                  disabled={isFieldDisabled}
                  className={dateRangeError ? "border-red-500" : ""}
                />
              </div>
            </div>
            {dateRangeError && (
              <Text size="small" className="text-red-600">
                {dateRangeError}
              </Text>
            )}
          </div>
        );

      case "boolean":
        const isLocked = field.locked_in_offerings && showInheritanceIndicators;
        return (
          <div key={field.key} className="space-y-2">
            <div className="flex items-center gap-2">
              <input
                id={fieldId}
                type="checkbox"
                checked={displayValue || false}
                onChange={(e) => onChange(field.key, e.target.checked)}
                disabled={isFieldDisabled}
                className="rounded border-gray-300"
              />
              <Label htmlFor={fieldId}>
                {field.label}
                {field.required && <span className="text-red-500 ml-1">*</span>}
              </Label>
              {isLocked && (
                <Badge size="small">
                  Inherited
                </Badge>
              )}
            </div>
            {error && (
              <Text size="small" className="text-red-600">
                {error}
              </Text>
            )}
          </div>
        );

      case "number-range":
        // Handle both object format {min: x, max: y} and string format "x-y"
        let rangeValue = { min: "", max: "" };
        if (displayValue) {
          if (
            typeof displayValue === "object" &&
            displayValue.min !== undefined &&
            displayValue.max !== undefined
          ) {
            rangeValue = displayValue;
          } else if (
            typeof displayValue === "string" &&
            displayValue.includes("-")
          ) {
            const [min, max] = displayValue.split("-");
            rangeValue = { min: min.trim(), max: max.trim() };
          } else if (typeof displayValue === "string") {
            // If it's just a single number, put it in min
            rangeValue = { min: displayValue, max: "" };
          }
        }

        return (
          <div key={field.key} className="space-y-2">
            {renderFieldLabel(field)}
            <div className="flex items-center gap-2">
              <Input
                type="number"
                value={rangeValue.min || ""}
                onChange={(e) =>
                  onChange(field.key, {
                    ...rangeValue,
                    min: e.target.value ? parseFloat(e.target.value) : "",
                  })
                }
                placeholder="Min"
                disabled={isFieldDisabled}
                className={`flex-1 ${error ? "border-red-500" : ""}`}
              />
              <Text className="text-ui-fg-subtle">to</Text>
              <Input
                type="number"
                value={rangeValue.max || ""}
                onChange={(e) =>
                  onChange(field.key, {
                    ...rangeValue,
                    max: e.target.value ? parseFloat(e.target.value) : "",
                  })
                }
                placeholder="Max"
                disabled={isFieldDisabled}
                className={`flex-1 ${error ? "border-red-500" : ""}`}
              />
            </div>
            {error && (
              <Text size="small" className="text-red-600">
                {error}
              </Text>
            )}
          </div>
        );

      case "hotels":
        const selectedHotels = Array.isArray(displayValue) ? displayValue : [];

        const hotelOptions = hotels.map((hotel) => ({
          value: hotel.id,
          label: hotel.name,
        }));

        return (
          <div key={field.key} className="space-y-2">
            {renderFieldLabel(field)}
            <MultiSelect
              options={hotelOptions}
              selectedValues={selectedHotels}
              onChange={(values) => onChange(field.key, values)}
              placeholder="Select hotels"
              disabled={isFieldDisabled}
              showSelectAll={true}
              showSelectedTags={true}
              className={error ? "border-red-500" : ""}
            />
            {error && (
              <Text size="small" className="text-red-600">
                {error}
              </Text>
            )}
          </div>
        );

      case "destinations":
        const selectedDestinations = Array.isArray(displayValue)
          ? displayValue
          : [];

        const destinationOptions = destinations.map((dest) => ({
          value: dest.id,
          label: dest.name,
        }));

        return (
          <div key={field.key} className="space-y-2">
            {renderFieldLabel(field)}
            <MultiSelect
              options={destinationOptions}
              selectedValues={selectedDestinations}
              onChange={(values) => onChange(field.key, values)}
              placeholder="Select destinations"
              disabled={isFieldDisabled}
              showSelectAll={true}
              showSelectedTags={true}
              className={error ? "border-red-500" : ""}
            />
            {error && (
              <Text size="small" className="text-red-600">
                {error}
              </Text>
            )}
          </div>
        );

      case "addons":
        const selectedAddons = Array.isArray(displayValue) ? displayValue : [];

        // Filter out the current product service to prevent self-selection
        const filteredProductServices = productServices.filter(
          (addon) => addon.id !== excludeProductServiceId
        );

        const addonOptions = filteredProductServices.map((addon) => ({
          value: addon.id,
          label: addon.name,
        }));

        const handleAddonChange = (values: string[]) => {
          onChange(field.key, values);
          // Notify parent component about addon selection change
          if (onAddonSelectionChange) {
            onAddonSelectionChange(field.key, values);
          }
        };

        return (
          <div key={field.key} className="space-y-2">
            {renderFieldLabel(field)}
            <MultiSelect
              options={addonOptions}
              selectedValues={selectedAddons}
              onChange={handleAddonChange}
              placeholder="Select add-ons"
              disabled={isFieldDisabled}
              showSelectAll={true}
              showSelectedTags={true}
              className={error ? "border-red-500" : ""}
            />

            {error && (
              <Text size="small" className="text-red-600">
                {error}
              </Text>
            )}
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
      {filteredSchema.map(renderField)}
    </div>
  );
};

export default DynamicFieldRenderer;
