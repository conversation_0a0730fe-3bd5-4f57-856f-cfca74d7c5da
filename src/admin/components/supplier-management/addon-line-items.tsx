import React, { useState, useEffect } from "react";
import { Button, Text } from "@camped-ai/ui";
import { Plus, ChevronDown, ChevronUp } from "lucide-react";
import AddonLineItemComponent, { AddonLineItem } from "./addon-line-item";

interface AddonLineItemsProps {
  items: AddonLineItem[];
  onAddToNetCost: (item: AddonLineItem) => void;
  disabled?: boolean;
  currency?: string;
}

const AddonLineItems: React.FC<AddonLineItemsProps> = ({
  items,
  onAddToNetCost,
  disabled = false,
  currency = "CHF",
}) => {
  const [isExpanded, setIsExpanded] = useState(true);

  // Handle add to net cost
  const handleAddToNetCost = (itemId: string) => {
    const item = items.find((i) => i.id === itemId);
    if (item) {
      onAddToNetCost(item);
    }
  };

  // Auto-expand when items are added
  useEffect(() => {
    if (items.length > 0 && !isExpanded) {
      setIsExpanded(true);
    }
  }, [items.length]);

  return (
    <div className="border border-gray-200 dark:border-gray-700 rounded-lg bg-white dark:bg-gray-900">
      {/* Header */}
      <div
        className="flex items-center justify-between p-4 cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors duration-150"
        onClick={() => setIsExpanded(!isExpanded)}
      >
        <div className="flex items-center gap-3">
          <div className="flex items-center gap-2">
            {isExpanded ? (
              <ChevronUp className="h-4 w-4 text-gray-500" />
            ) : (
              <ChevronDown className="h-4 w-4 text-gray-500" />
            )}
            <Text className="font-medium">Add-on Items</Text>
          </div>
          {items.length > 0 && (
            <span className="px-2 py-1 bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 text-xs rounded-full">
              {items.length}
            </span>
          )}
        </div>

        <div className="flex items-center gap-4">
          <Text className="text-gray-500 text-sm">
            Available add-ons from your selection
          </Text>
        </div>
      </div>

      {/* Content */}
      {isExpanded && (
        <div className="border-t border-gray-200 dark:border-gray-700 p-4">
          {/* Line Items */}
          {items.length > 0 ? (
            <div className="space-y-3">
              {items.map((item) => (
                <AddonLineItemComponent
                  key={item.id}
                  item={item}
                  onAddToNetCost={handleAddToNetCost}
                  disabled={disabled}
                  currency={currency}
                />
              ))}
            </div>
          ) : (
            <div className="text-center py-8 text-gray-500">
              <Text size="small">
                No add-on items available. Select add-ons from the custom fields
                above.
              </Text>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default AddonLineItems;
