import React from "react";
import { <PERSON>, Text, Badge, Button, Container } from "@camped-ai/ui";
import { TrendingUp, TrendingDown, Minus, Calendar, User } from "lucide-react";
import {
  SupplierOfferingCostHistory,
  SupplierOfferingCostHistoryStats,
  formatCostChange,
  getCostChangeType,
} from "../../hooks/supplier-products-services/use-supplier-offering-cost-history";
import {
  useUsersLookup,
  formatUserDisplay,
  getUserDisplayFromId,
} from "../../hooks/use-user-lookup";

interface CostHistoryTableProps {
  costHistory: SupplierOfferingCostHistory[];
  stats?: SupplierOfferingCostHistoryStats;
  isLoading?: boolean;
  showSupplierOffering?: boolean; // Whether to show supplier offering details
  onLoadMore?: () => void;
  hasMore?: boolean;
}

const CostHistoryTable: React.FC<CostHistoryTableProps> = ({
  costHistory,
  stats,
  isLoading = false,
  showSupplierOffering = false,
  onLoadMore,
  hasMore = false,
}) => {
  // Extract unique user IDs from cost history
  const userIds = React.useMemo(() => {
    const ids = costHistory
      .map((history) => history.changed_by_user_id)
      .filter((id): id is string => !!id);
    return [...new Set(ids)]; // Remove duplicates
  }, [costHistory]);

  // Fetch user details for all user IDs
  const { data: users = {} } = useUsersLookup(userIds);

  // Function to display user information with proper lookup
  const displayUser = (userId: string | null | undefined): string => {
    if (!userId) return "System";

    // Check if we have user data from the lookup
    const user = users[userId];
    if (user) {
      return formatUserDisplay(user);
    }

    // Fallback to utility function for formatting
    return getUserDisplayFromId(userId);
  };
  const getCostChangeIcon = (previousCost?: number, newCost?: number) => {
    const changeType = getCostChangeType(previousCost, newCost);

    switch (changeType) {
      case "increase":
        return <TrendingUp className="h-4 w-4 text-green-600" />;
      case "decrease":
        return <TrendingDown className="h-4 w-4 text-red-600" />;
      case "no-change":
        return <Minus className="h-4 w-4 text-gray-500" />;
      default:
        return null;
    }
  };

  const getCostChangeBadge = (previousCost?: number, newCost?: number) => {
    const changeType = getCostChangeType(previousCost, newCost);

    switch (changeType) {
      case "increase":
        return (
          <Badge
            className="bg-green-100 text-green-800 border-green-200"
            size="small"
          >
            Up
          </Badge>
        );
      case "decrease":
        return (
          <Badge
            className="bg-red-100 text-red-800 border-red-200"
            size="small"
          >
            Down
          </Badge>
        );
      case "no-change":
        return (
          <Badge
            className="bg-gray-100 text-gray-800 border-gray-200"
            size="small"
          >
            No Change
          </Badge>
        );
      default:
        return (
          <Badge
            className="bg-gray-100 text-gray-800 border-gray-200"
            size="small"
          >
            Unknown
          </Badge>
        );
    }
  };

  const formatCurrency = (amount?: number, currency?: string) => {
    if (amount === undefined || amount === null) {
      return "—";
    }

    // Convert to number if it's a string
    const numericAmount =
      typeof amount === "string" ? parseFloat(amount) : amount;

    if (isNaN(numericAmount)) {
      return "—";
    }

    return `${numericAmount.toFixed(2)} ${currency || ""}`.trim();
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  if (isLoading) {
    return (
      <Container className="p-6">
        <div className="text-center">
          <Text>Loading cost history...</Text>
        </div>
      </Container>
    );
  }

  if (!costHistory || costHistory.length === 0) {
    return (
      <Container className="p-6">
        <div className="text-center">
          <Text className="text-ui-fg-subtle">No cost history found</Text>
        </div>
      </Container>
    );
  }

  return (
    <div className="space-y-6">
      {/* Stats Summary */}
      {stats && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="bg-ui-bg-subtle rounded-lg p-4">
            <Text size="small" className="text-ui-fg-subtle">
              Total Changes
            </Text>
            <Text size="large" weight="plus">
              {stats.total_changes}
            </Text>
          </div>
          <div className="bg-green-50 rounded-lg p-4">
            <Text size="small" className="text-green-700">
              Cost Increases
            </Text>
            <Text size="large" weight="plus" className="text-green-800">
              {stats.cost_increases}
            </Text>
          </div>
          <div className="bg-red-50 rounded-lg p-4">
            <Text size="small" className="text-red-700">
              Cost Decreases
            </Text>
            <Text size="large" weight="plus" className="text-red-800">
              {stats.cost_decreases}
            </Text>
          </div>
          <div className="bg-blue-50 rounded-lg p-4">
            <Text size="small" className="text-blue-700">
              Currency Changes
            </Text>
            <Text size="large" weight="plus" className="text-blue-800">
              {stats.currency_changes}
            </Text>
          </div>
        </div>
      )}

      {/* Desktop Table */}
      <div className="hidden md:block">
        <Table>
          <Table.Header>
            <Table.Row>
              <Table.HeaderCell>Change</Table.HeaderCell>
              <Table.HeaderCell>Old Cost</Table.HeaderCell>
              <Table.HeaderCell>New Cost</Table.HeaderCell>
              <Table.HeaderCell>Currency</Table.HeaderCell>
              {showSupplierOffering && (
                <Table.HeaderCell>Supplier Offering</Table.HeaderCell>
              )}
              <Table.HeaderCell>Notes</Table.HeaderCell>
              <Table.HeaderCell>Changed By</Table.HeaderCell>
              <Table.HeaderCell>Date</Table.HeaderCell>
            </Table.Row>
          </Table.Header>
          <Table.Body>
            {costHistory.map((history) => (
              <Table.Row key={history.id}>
                <Table.Cell>
                  <div className="flex items-center gap-2">
                    {getCostChangeIcon(history.previous_cost, history.new_cost)}
                    {getCostChangeBadge(
                      history.previous_cost,
                      history.new_cost
                    )}
                  </div>
                </Table.Cell>
                <Table.Cell>
                  <Text>
                    {formatCurrency(
                      history.previous_cost,
                      history.previous_currency
                    )}
                  </Text>
                </Table.Cell>
                <Table.Cell>
                  <Text>
                    {formatCurrency(history.new_cost, history.new_currency)}
                  </Text>
                </Table.Cell>
                <Table.Cell>
                  <div className="space-y-1">
                    {history.previous_currency !== history.new_currency ? (
                      <>
                        <Text size="small" className="text-ui-fg-subtle">
                          {history.previous_currency} → {history.new_currency}
                        </Text>
                        <Badge
                          className="bg-orange-100 text-orange-800 border-orange-200"
                          size="small"
                        >
                          Currency Changed
                        </Badge>
                      </>
                    ) : (
                      <Text>
                        {history.new_currency ||
                          history.previous_currency ||
                          "—"}
                      </Text>
                    )}
                  </div>
                </Table.Cell>
                {showSupplierOffering && (
                  <Table.Cell>
                    <div className="space-y-1">
                      <Text size="small" weight="plus">
                        {history.supplier_offering?.product_service?.name ||
                          "—"}
                      </Text>
                      <Text size="small" className="text-ui-fg-subtle">
                        by {history.supplier_offering?.supplier?.name || "—"}
                      </Text>
                    </div>
                  </Table.Cell>
                )}
                <Table.Cell>
                  <div className="max-w-xs">
                    <Text
                      size="small"
                      className="line-clamp-2"
                      title={history.change_reason || "No notes provided"}
                    >
                      {history.change_reason || "—"}
                    </Text>
                  </div>
                </Table.Cell>
                <Table.Cell>
                  <div className="flex items-center gap-1">
                    <User className="h-3 w-3 text-ui-fg-subtle" />
                    <Text size="small">
                      {displayUser(history.changed_by_user_id)}
                    </Text>
                  </div>
                </Table.Cell>
                <Table.Cell>
                  <div className="flex items-center gap-1">
                    <Calendar className="h-3 w-3 text-ui-fg-subtle" />
                    <Text size="small">{formatDate(history.created_at)}</Text>
                  </div>
                </Table.Cell>
              </Table.Row>
            ))}
          </Table.Body>
        </Table>
      </div>

      {/* Mobile Cards */}
      <div className="md:hidden space-y-4">
        {costHistory.map((history) => (
          <div
            key={history.id}
            className="bg-ui-bg-base border rounded-lg p-4 space-y-3"
          >
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                {getCostChangeIcon(history.previous_cost, history.new_cost)}
                {getCostChangeBadge(history.previous_cost, history.new_cost)}
              </div>
              <Text size="small" className="text-ui-fg-subtle">
                {formatDate(history.created_at)}
              </Text>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <Text size="small" className="text-ui-fg-subtle">
                  Old Cost
                </Text>
                <Text>
                  {formatCurrency(
                    history.previous_cost,
                    history.previous_currency
                  )}
                </Text>
              </div>
              <div>
                <Text size="small" className="text-ui-fg-subtle">
                  New Cost
                </Text>
                <Text>
                  {formatCurrency(history.new_cost, history.new_currency)}
                </Text>
              </div>
            </div>

            {history.previous_currency !== history.new_currency && (
              <div>
                <Text size="small" className="text-ui-fg-subtle">
                  Currency Change
                </Text>
                <div className="flex items-center gap-2">
                  <Text size="small">
                    {history.previous_currency} → {history.new_currency}
                  </Text>
                  <Badge
                    className="bg-orange-100 text-orange-800 border-orange-200"
                    size="small"
                  >
                    Changed
                  </Badge>
                </div>
              </div>
            )}

            {showSupplierOffering && history.supplier_offering && (
              <div>
                <Text size="small" className="text-ui-fg-subtle">
                  Supplier Offering
                </Text>
                <Text size="small" weight="plus">
                  {history.supplier_offering.product_service?.name}
                </Text>
                <Text size="small" className="text-ui-fg-subtle">
                  by {history.supplier_offering.supplier?.name}
                </Text>
              </div>
            )}

            <div>
              <Text size="small" className="text-ui-fg-subtle">
                Notes
              </Text>
              <Text
                size="small"
                className="line-clamp-3 whitespace-pre-wrap"
                title={history.change_reason || "No notes provided"}
              >
                {history.change_reason || "No notes provided"}
              </Text>
            </div>

            <div className="flex items-center justify-between pt-2 border-t">
              <div className="flex items-center gap-1">
                <User className="h-3 w-3 text-ui-fg-subtle" />
                <Text size="small">
                  {displayUser(history.changed_by_user_id)}
                </Text>
              </div>
              <Text size="small">
                {formatCostChange(
                  history.previous_cost,
                  history.new_cost,
                  history.new_currency
                )}
              </Text>
            </div>
          </div>
        ))}
      </div>

      {/* Load More Button */}
      {hasMore && onLoadMore && (
        <div className="text-center pt-4">
          <Button variant="secondary" onClick={onLoadMore}>
            Load More
          </Button>
        </div>
      )}
    </div>
  );
};

export default CostHistoryTable;
