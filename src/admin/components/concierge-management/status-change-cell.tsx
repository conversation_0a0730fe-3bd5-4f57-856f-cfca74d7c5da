import React, { useState, useEffect } from "react";
import {
  Badge,
  Text,
  toast,
  Popover,
  Button,
  Select,
} from "@camped-ai/ui";
import { Loader2 } from "lucide-react";
import { CircularProgress } from "@mui/material";

interface StatusChangeCellProps {
  value: string;
  onSave: (status: string) => Promise<void>;
  isLoading?: boolean;
  className?: string;
  allowedStatuses?: string[];
}

// Status options for concierge order items in booking screen
// Excludes 'order_placed' as per requirements
const BOOKING_STATUS_OPTIONS = [
  { value: "under_review", label: "Under Review" },
  { value: "client_confirmed", label: "Client Confirmed" },
  { value: "cancelled", label: "Cancelled" },
];

const StatusChangeCell: React.FC<StatusChangeCellProps> = ({
  value,
  onSave,
  isLoading = false,
  className = "",
  allowedStatuses = BOOKING_STATUS_OPTIONS.map(s => s.value),
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [editValue, setEditValue] = useState(value);
  const [isSaving, setIsSaving] = useState(false);

  useEffect(() => {
    setEditValue(value);
  }, [value]);

  const getStatusBadgeVariant = (status: string) => {
    switch (status) {
      case "under_review":
        return "orange";
      case "client_confirmed":
        return "green";
      case "order_placed":
        return "blue";
      case "cancelled":
        return "red";
      case "completed":
        return "purple";
      default:
        return "grey";
    }
  };

  const getStatusLabel = (status: string) => {
    const option = BOOKING_STATUS_OPTIONS.find(opt => opt.value === status);
    if (option) {
      return option.label;
    }
    // Handle statuses not in the editable options (like "order_placed")
    switch (status) {
      case "order_placed":
        return "Order Placed";
      case "completed":
        return "Completed";
      default:
        return status.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
    }
  };

  const handleSave = async () => {
    if (editValue === value) {
      setIsOpen(false);
      return;
    }

    setIsSaving(true);
    try {
      await onSave(editValue);
      setIsOpen(false);
      toast.success("Success", {
        description: "Status updated successfully!",
      });
    } catch (error) {
      console.error("Error updating status:", error);
      toast.error("Error", {
        description: "Failed to update status",
      });
      // Reset to original value on error
      setEditValue(value);
    } finally {
      setIsSaving(false);
    }
  };

  const handleCancel = () => {
    setEditValue(value);
    setIsOpen(false);
  };

  // Filter options based on allowed statuses
  const filteredOptions = BOOKING_STATUS_OPTIONS.filter(option =>
    allowedStatuses.includes(option.value)
  );

  // Check if the status is "order_placed" or "completed" to disable editing
  const isStatusLocked = value === "order_placed" || value === "completed";

  // If status is locked, render a non-interactive badge
  if (isStatusLocked) {
    return (
      <div className={`rounded px-2 py-1 ${className}`}>
        <div className="flex items-center gap-2">
          <Badge variant={getStatusBadgeVariant(value) as any}>
            {getStatusLabel(value)}
          </Badge>
          {isLoading && <CircularProgress size={12} />}
        </div>
        <div className="text-xs text-ui-fg-subtle mt-1">
          Status cannot be changed
        </div>
      </div>
    );
  }

  return (
    <Popover open={isOpen} onOpenChange={setIsOpen}>
      <Popover.Trigger asChild>
        <div
          className={`cursor-pointer hover:bg-ui-bg-subtle rounded px-2 py-1 transition-colors border border-transparent hover:border-ui-border-base ${isLoading ? 'opacity-50' : ''} ${className}`}
          title={isLoading ? "Updating..." : "Click to change status"}
        >
          <div className="flex items-center gap-2">
            <Badge variant={getStatusBadgeVariant(value) as any}>
              {getStatusLabel(value)}
            </Badge>
            {isLoading && <CircularProgress size={12} />}
          </div>
          <div className="text-xs text-ui-fg-subtle mt-1">
            {isLoading ? "Updating..." : "Click to change"}
          </div>
        </div>
      </Popover.Trigger>
      <Popover.Content side="top" align="start" className="w-[280px] p-4">
        <div className="space-y-4">
          <div>
            <Text size="small" weight="plus" className="mb-3">
              Change Status
            </Text>
            
            <Select
              value={editValue}
              onValueChange={setEditValue}
              disabled={isSaving}
            >
              <Select.Trigger>
                <Select.Value placeholder="Select status" />
              </Select.Trigger>
              <Select.Content>
                {filteredOptions.map((option) => (
                  <Select.Item key={option.value} value={option.value}>
                    <div className="flex items-center gap-2">
                      <Badge variant={getStatusBadgeVariant(option.value) as any}>
                        {option.label}
                      </Badge>
                    </div>
                  </Select.Item>
                ))}
              </Select.Content>
            </Select>
          </div>

          <div className="flex items-center justify-end gap-2 pt-2 border-t">
            <Button
              variant="secondary"
              size="small"
              onClick={handleCancel}
              disabled={isSaving}
            >
              Cancel
            </Button>
            <Button
              variant="primary"
              size="small"
              onClick={handleSave}
              disabled={isSaving || editValue === value}
            >
              {isSaving ? <Loader2 className="h-3 w-3 animate-spin mr-1" /> : null}
              Save
            </Button>
          </div>
        </div>
      </Popover.Content>
    </Popover>
  );
};

export default StatusChangeCell;
