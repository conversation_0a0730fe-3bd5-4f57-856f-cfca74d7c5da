import React, { useState, useEffect, useRef } from "react";
import {
  Input,
  Textarea,
  Text,
  toast,
  Popover,
  Button,
} from "@camped-ai/ui";
import { Check, X, Loader2, FileText, Edit3 } from "lucide-react";
import { CircularProgress } from "@mui/material";

interface NotesEditCellProps {
  value: string | null;
  onSave: (notes: string) => Promise<void>;
  isLoading?: boolean;
  className?: string;
  placeholder?: string;
  maxLength?: number;
}

const NotesEditCell: React.FC<NotesEditCellProps> = ({
  value,
  onSave,
  isLoading = false,
  className = "",
  placeholder = "Add notes...",
  maxLength = 500,
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [editValue, setEditValue] = useState(value || "");
  const [isSaving, setIsSaving] = useState(false);
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  useEffect(() => {
    setEditValue(value || "");
  }, [value]);

  useEffect(() => {
    if (isOpen && textareaRef.current) {
      textareaRef.current.focus();
      // Set cursor to end of text
      const length = textareaRef.current.value.length;
      textareaRef.current.setSelectionRange(length, length);
    }
  }, [isOpen]);

  const handleSave = async () => {
    const trimmedValue = editValue.trim();
    
    if (trimmedValue === (value || "")) {
      setIsOpen(false);
      return;
    }

    setIsSaving(true);
    try {
      await onSave(trimmedValue);
      setIsOpen(false);
      toast.success("Success", {
        description: "Notes updated successfully!",
      });
    } catch (error) {
      console.error("Error updating notes:", error);
      toast.error("Error", {
        description: "Failed to update notes",
      });
      // Reset to original value on error
      setEditValue(value || "");
    } finally {
      setIsSaving(false);
    }
  };

  const handleCancel = () => {
    setEditValue(value || "");
    setIsOpen(false);
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Escape") {
      handleCancel();
    } else if (e.key === "Enter" && (e.ctrlKey || e.metaKey)) {
      handleSave();
    }
  };

  const displayValue = value?.trim() || "";
  const hasNotes = displayValue.length > 0;

  return (
    <Popover open={isOpen} onOpenChange={setIsOpen}>
      <Popover.Trigger asChild>
        <div
          className={`cursor-pointer hover:bg-ui-bg-subtle rounded px-2 py-1 transition-colors border border-transparent hover:border-ui-border-base ${isLoading ? 'opacity-50' : ''} ${className}`}
          title={isLoading ? "Updating..." : hasNotes ? displayValue : "Click to add notes"}
        >
          <div className="flex items-center gap-2">
            {hasNotes ? (
              <div className="flex items-center gap-2">
                <FileText className="h-4 w-4 text-ui-fg-muted" />
                <div className="flex-1 min-w-0">
                  <div className="text-sm text-ui-fg-base truncate max-w-[200px]">
                    {displayValue}
                  </div>
                </div>
              </div>
            ) : (
              <div className="flex items-center gap-2 text-ui-fg-muted">
                <Edit3 className="h-4 w-4" />
                <Text size="small" className="text-ui-fg-muted">
                  Add notes
                </Text>
              </div>
            )}
            {isLoading && <CircularProgress size={12} />}
          </div>
          <div className="text-xs text-ui-fg-subtle mt-1">
            {isLoading ? "Updating..." : "Click to edit"}
          </div>
        </div>
      </Popover.Trigger>
      <Popover.Content side="top" align="start" className="w-[400px] p-4">
        <div className="space-y-4">
          <div>
            <Text size="small" weight="plus" className="mb-3">
              Notes
            </Text>
            
            <Textarea
              ref={textareaRef}
              value={editValue}
              onChange={(e) => setEditValue(e.target.value)}
              onKeyDown={handleKeyDown}
              placeholder={placeholder}
              className="min-h-[100px] resize-none"
              disabled={isSaving}
              maxLength={maxLength}
            />
            
            <div className="flex justify-between items-center mt-2">
              <Text size="small" className="text-ui-fg-muted">
                Press Ctrl+Enter to save, Esc to cancel
              </Text>
              <Text size="small" className="text-ui-fg-muted">
                {editValue.length}/{maxLength}
              </Text>
            </div>
          </div>

          <div className="flex items-center justify-end gap-2 pt-2 border-t">
            <Button
              variant="secondary"
              size="small"
              onClick={handleCancel}
              disabled={isSaving}
            >
              Cancel
            </Button>
            <Button
              variant="primary"
              size="small"
              onClick={handleSave}
              disabled={isSaving}
            >
              {isSaving ? <Loader2 className="h-3 w-3 animate-spin mr-1" /> : null}
              Save
            </Button>
          </div>
        </div>
      </Popover.Content>
    </Popover>
  );
};

export default NotesEditCell;
