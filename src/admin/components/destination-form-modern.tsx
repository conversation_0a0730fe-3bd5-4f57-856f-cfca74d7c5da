import {
  Button,
  Input,
  Text,
  Label,
  Heading,
  Tooltip,
  InlineTip,
  FocusModal,
  Tabs,
} from "@camped-ai/ui";
import { useForm } from "react-hook-form";
import DestinationMediaSection from "./destination/destination-media-section";
import { MediaField } from "./hotel/media-item";
import { useState, useEffect, useRef, useCallback } from "react";
import { Info, Building } from "lucide-react";
import { DestinationFaqData } from "./destination-form";
import { TextareaField } from "./ai-enhanced-inputs";

import VisibilitySettings from "./visibility-settings";
import LanguageSelector from "./language-selector";
import { useProjectLanguages } from "../hooks/languages/useProjectLanguages";

import { useDestinationTranslations } from "../hooks/translations/useDestinationTranslations";
import { useTagTranslations } from "../hooks/translations/useTagTranslations";
import AITranslateButton from "./ai-translate-button";
import { CountrySelector } from "./common/country-selector";
import TranslatableTagInput from "./translatable-tag-input";
import { CurrencySelector } from "./common/currency-selector";
import { isValidUrl, normalizeUrl } from "../../utils/url-validation";

// Define which fields are translatable
const TRANSLATABLE_FIELDS = ["name", "description", "location"] as const;

// Define which fields are translatable arrays
const TRANSLATABLE_ARRAY_FIELDS = ["tags"] as const;

// Define which fields are translatable nested objects
const TRANSLATABLE_NESTED_OBJECT_FIELDS = ["faqs"] as const;

// Helper function to check if a field is translatable
const isFieldTranslatable = (fieldName: string): boolean => {
  return (
    TRANSLATABLE_FIELDS.includes(fieldName as any) ||
    TRANSLATABLE_ARRAY_FIELDS.includes(fieldName as any) ||
    TRANSLATABLE_NESTED_OBJECT_FIELDS.includes(fieldName as any)
  );
};

// Helper function to determine if a field should be enabled based on language selection
const isFieldEnabled = (
  fieldName: string,
  isBaseLanguage: boolean
): boolean => {
  // If it's the base language, all fields are enabled
  if (isBaseLanguage) return true;

  // For non-base languages, only translatable fields (including array and nested object fields) are enabled
  return isFieldTranslatable(fieldName);
};

export type DestinationFormData = {
  name: string;
  handle: string;
  description: string;
  is_active: boolean;
  is_featured: boolean;
  country: string;
  location: string | null;
  tags: string[] | null;
  website?: string | null;
  ai_content?: string | null;
  media?: MediaField[];
  image_ids?: string[];
  thumbnail_image_id?: string;
  faqs?: DestinationFaqData[];
  id?: string;
  // New fields for currency/margin enhancement
  internal_web_link?: string | null;
  external_web_link?: string | null;
  currency?: string | null;
  margin?: number | null;
  translationData?: {
    language: string;
    translations:
      | Record<string, string>
      | Record<string, Record<string, string>>;
    saveFunction: (destinationId: string) => Promise<void>;
  };
};

type DestinationFormProps = {
  formData: DestinationFormData;
  setFormData: (data: DestinationFormData) => void;
  onSubmit: (updatedData?: DestinationFormData) => Promise<boolean>;
  isEdit?: boolean;
  closeModal: () => void;
  destinationId?: string;
};

const DestinationFormModern = ({
  formData,
  setFormData,
  onSubmit,
  isEdit,
  closeModal,
  destinationId,
}: DestinationFormProps) => {
  const form = useForm<DestinationFormData>({
    defaultValues: formData,
  });

  // Reset form when formData changes (e.g., when creating a new destination after submitting one)
  useEffect(() => {
    const currentFormMedia = form.getValues("media") || [];
    console.log("🔄 Form reset triggered with formData:", {
      mediaCount: formData.media?.length || 0,
      hasMedia: !!(formData.media && formData.media.length > 0),
      formDataKeys: Object.keys(formData),
      currentFormMediaCount: currentFormMedia.length,
      willOverwriteMedia:
        currentFormMedia.length > 0 && (formData.media?.length || 0) === 0,
    });

    // Only reset if the form data is significantly different to avoid unnecessary resets
    const currentFormValues = form.getValues();
    const shouldReset =
      JSON.stringify(currentFormValues) !== JSON.stringify(formData);

    console.log("🔄 Form reset evaluation:", {
      shouldReset,
      currentFormMediaCount: currentFormValues.media?.length || 0,
      newFormDataMediaCount: formData.media?.length || 0,
      formDataChanged: shouldReset,
      willLoseMedia:
        (currentFormValues.media?.length || 0) > (formData.media?.length || 0),
    });

    if (shouldReset) {
      console.log("🔄 Performing form reset - preserving current media data");

      // Preserve current media data during reset to prevent loss
      const currentMedia = currentFormValues.media || [];
      const newFormData = { ...formData };

      // If current form has more media than the new formData, preserve the current media
      if (currentMedia.length > (formData.media?.length || 0)) {
        console.log("🔄 Preserving current media during reset:", {
          preservingCount: currentMedia.length,
          incomingCount: formData.media?.length || 0,
        });
        newFormData.media = currentMedia;
      }

      // Reset the form with the preserved media data
      form.reset(newFormData);
    } else {
      console.log("🔄 Skipping form reset - data is the same");
    }

    // Reset tags state and sync with formData
    let newTags: string[] = [];
    if (!formData.tags) {
      newTags = [];
    } else if (Array.isArray(formData.tags)) {
      newTags = formData.tags;
    } else if (typeof formData.tags === "string") {
      try {
        const parsed = JSON.parse(formData.tags);
        newTags = Array.isArray(parsed) ? parsed : [];
      } catch (e) {
        newTags = (formData.tags as string)
          .split(",")
          .map((tag: string) => tag.trim())
          .filter((tag: string) => tag);
      }
    }
    setTags(newTags);
  }, [formData, form]);

  // Use refs to maintain stable references and prevent unnecessary re-renders
  const formDataRef = useRef(formData);
  const setFormDataRef = useRef(setFormData);
  const lastSyncedMediaRef = useRef<string>("");
  const syncTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Update refs when props change, but don't trigger re-renders
  formDataRef.current = formData;
  setFormDataRef.current = setFormData;

  // Helper function to safely update formData while preserving current media
  const safeSetFormData = useCallback(
    (updates: Partial<DestinationFormData>) => {
      const currentFormMedia = form.getValues("media") || [];
      const updatedData = {
        ...formDataRef.current,
        ...updates,
        // Always preserve the current form's media data unless explicitly overriding
        media: updates.media !== undefined ? updates.media : currentFormMedia,
      };

      console.log("🔄 Safe formData update:", {
        updates: Object.keys(updates),
        preservedMediaCount: updatedData.media?.length || 0,
        explicitMediaUpdate: updates.media !== undefined,
      });

      setFormDataRef.current(updatedData);
    },
    [form]
  );

  // Watch for changes in the form's media field and sync back to parent formData
  // This prevents media data loss during tab switches
  useEffect(() => {
    const subscription = form.watch((value, { name, type }) => {
      if (type === "change" && name === "media") {
        const currentMedia = (value.media || []).filter(
          (item): item is MediaField => item !== undefined
        );

        // Create a lightweight comparison key using field_ids and file names
        const createMediaKey = (media: MediaField[]) =>
          media
            .map((m) => `${m.field_id}:${m.file?.name || "no-file"}`)
            .join("|");

        const currentMediaKey = createMediaKey(currentMedia);

        // Only sync if media has actually changed and it's different from last sync
        if (currentMediaKey !== lastSyncedMediaRef.current) {
          const latestFormData = formDataRef.current;
          const parentMediaKey = createMediaKey(latestFormData.media || []);

          // Only update if parent data is actually different
          if (currentMediaKey !== parentMediaKey) {
            console.log("🔄 Syncing media data back to parent formData:", {
              currentMediaCount: currentMedia.length,
              previousMediaCount: latestFormData.media?.length || 0,
              currentMediaDetails: currentMedia.map((m, idx) => ({
                index: idx,
                field_id: m.field_id,
                fileName: m.file?.name,
                hasFile: !!m.file,
                hasUrl: !!m.url,
              })),
              parentMediaDetails: (latestFormData.media || []).map(
                (m, idx) => ({
                  index: idx,
                  field_id: m.field_id,
                  fileName: m.file?.name,
                  hasFile: !!m.file,
                  hasUrl: !!m.url,
                })
              ),
              currentMediaKey,
              parentMediaKey,
            });

            // Clear any pending sync
            if (syncTimeoutRef.current) {
              clearTimeout(syncTimeoutRef.current);
            }

            // Debounce the sync to prevent race conditions
            syncTimeoutRef.current = setTimeout(() => {
              setFormDataRef.current({
                ...formDataRef.current,
                media: currentMedia,
              });

              // Update the last synced reference
              lastSyncedMediaRef.current = currentMediaKey;
            }, 50); // Small delay to batch rapid changes
          }
        }
      }
    });

    return () => {
      subscription.unsubscribe();
      // Clean up any pending sync timeout
      if (syncTimeoutRef.current) {
        clearTimeout(syncTimeoutRef.current);
      }
    };
  }, [form]); // Only depend on form - all other dependencies are refs

  const [activeTab, setActiveTab] = useState("basics");
  const [selectedLanguage, setSelectedLanguage] = useState("en");

  // Force media sync when switching to media tab
  useEffect(() => {
    if (activeTab === "media") {
      console.log("📸 Media tab activated - forcing form media sync");
      const currentFormMedia = form.getValues("media") || [];
      const parentMedia = formDataRef.current.media || [];

      console.log("📸 Media sync check:", {
        formMediaCount: currentFormMedia.length,
        parentMediaCount: parentMedia.length,
        formMediaIds: currentFormMedia.map((m) => m?.field_id),
        parentMediaIds: parentMedia.map((m) => m?.field_id),
      });

      // If parent has media but form doesn't, sync from parent to form
      if (parentMedia.length > 0 && currentFormMedia.length === 0) {
        console.log("📸 Syncing media from parent to form");
        form.setValue("media", parentMedia);
      }
      // If form has media but parent doesn't, sync from form to parent
      else if (currentFormMedia.length > 0 && parentMedia.length === 0) {
        console.log("📸 Syncing media from form to parent");
        setFormDataRef.current({
          ...formDataRef.current,
          media: currentFormMedia,
        });
      }
    }
  }, [activeTab, form]); // Removed setFormData dependency - using ref instead

  // Loading states for form submission and translation saving
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSavingTranslations, setIsSavingTranslations] = useState(false);

  // State to track translated values for ALL languages (not just current)
  const [allTranslatedValues, setAllTranslatedValues] = useState<
    Record<string, Record<string, string>>
  >({});

  // Track which languages have user input to prevent overwrites
  const userInputLanguages = useRef<Set<string>>(new Set());

  // Get translated values for the current language
  const translatedValues = allTranslatedValues[selectedLanguage] || {};

  // Get available languages to determine base language
  const { languages: tolgeeLanguages } = useProjectLanguages();

  // Determine if current language is base language
  const isBaseLanguage =
    selectedLanguage === "en" ||
    tolgeeLanguages.find((lang) => lang.tag === selectedLanguage)?.base ===
      true;

  // Debug: Log state changes (reduced logging to prevent spam)
  useEffect(() => {
    console.log(
      "🔄 Language changed to:",
      selectedLanguage,
      "isBase:",
      isBaseLanguage
    );
    console.log("📊 Translation summary:", {
      totalLanguages: Object.keys(allTranslatedValues).length,
      currentLanguageFieldCount: Object.keys(translatedValues).length,
    });
  }, [selectedLanguage, isBaseLanguage]);

  // Translation management
  const {
    saveTranslations,
    loadTranslations,
    getTranslationKeys,
    translations,
    saveArrayTranslations,
    saveNestedObjectTranslations,
    nestedObjectTranslations,
  } = useDestinationTranslations(destinationId || "new");

  // Track which languages we're currently loading to prevent duplicate API calls
  const loadingLanguagesRef = useRef<Set<string>>(new Set());

  // Load translations when language changes - FIXED TO PREVENT INFINITE LOOPS
  useEffect(() => {
    const loadTranslationsForLanguage = async () => {
      if (!isBaseLanguage && destinationId && destinationId !== "new") {
        // Check if we're already loading this language
        if (loadingLanguagesRef.current.has(selectedLanguage)) {
          console.log(
            `⏳ Already loading translations for ${selectedLanguage}, skipping...`
          );
          return;
        }

        // Check if we already have translations for this language
        const hasExistingTranslations =
          translations[selectedLanguage] &&
          Object.keys(translations[selectedLanguage]).length > 0;

        if (hasExistingTranslations) {
          console.log(
            `✅ Translations already exist for ${selectedLanguage}, skipping load`
          );
          return;
        }

        console.log(
          `Loading translations for language: ${selectedLanguage}, destination: ${destinationId}`
        );
        loadingLanguagesRef.current.add(selectedLanguage); // Mark as loading

        try {
          await loadTranslations(selectedLanguage);
          console.log(`✅ loadTranslations completed for ${selectedLanguage}`);
        } catch (error) {
          console.error(
            `❌ Error loading translations for ${selectedLanguage}:`,
            error
          );
        } finally {
          loadingLanguagesRef.current.delete(selectedLanguage); // Remove from loading set
        }
      } else if (!isBaseLanguage && destinationId === "new") {
        // For new destinations, initialize with base language values ONLY if no translations exist yet
        const hasUserInput = userInputLanguages.current.has(selectedLanguage);
        const existingTranslations = allTranslatedValues[selectedLanguage];
        const hasExistingTranslations =
          existingTranslations && Object.keys(existingTranslations).length > 0;

        // Only initialize if we have NO existing translations AND no user input
        if (!hasExistingTranslations && !hasUserInput) {
          console.log(
            `🆕 First time switching to ${selectedLanguage} - initializing with base values`
          );
          const initialTranslations: Record<string, string> = {};
          TRANSLATABLE_FIELDS.forEach((fieldName) => {
            const baseValue = formData[fieldName as keyof typeof formData];
            if (baseValue && typeof baseValue === "string") {
              initialTranslations[fieldName] = baseValue;
            }
          });

          if (Object.keys(initialTranslations).length > 0) {
            console.log(
              `🔧 Setting initial translations for ${selectedLanguage}:`,
              initialTranslations
            );
            setAllTranslatedValues((prev) => ({
              ...prev,
              [selectedLanguage]: initialTranslations,
            }));
          }
        } else if (hasExistingTranslations) {
          console.log(
            `🔄 Returning to ${selectedLanguage} - preserving existing translations:`,
            existingTranslations
          );
        } else {
          console.log(
            `✅ ${selectedLanguage} translations already exist - keeping user input`
          );
        }
      } else {
        console.log(
          "Switched to base language - keeping translated values for later use"
        );
      }
    };

    loadTranslationsForLanguage();
  }, [selectedLanguage, isBaseLanguage, destinationId]); // Minimal dependencies to prevent infinite loop

  // Get translation keys once
  const translationKeys = getTranslationKeys();

  // Update translated values when translations change in the hook - ONLY if no user input exists
  useEffect(() => {
    if (!isBaseLanguage && translations[selectedLanguage]) {
      const currentTranslations = translations[selectedLanguage];
      const newTranslatedValues: Record<string, string> = {};

      // Extract only the values for our translatable fields
      TRANSLATABLE_FIELDS.forEach((fieldName) => {
        const key = translationKeys[fieldName];
        if (key && currentTranslations[key]) {
          newTranslatedValues[fieldName] = currentTranslations[key];
          console.log(
            `Found API translation for ${fieldName}: ${currentTranslations[key]}`
          );
        }
      });

      // Only update if we don't already have user input for this language
      const hasUserInput = userInputLanguages.current.has(selectedLanguage);
      const existingTranslations = allTranslatedValues[selectedLanguage] || {};
      const hasExistingUserTranslations =
        Object.keys(existingTranslations).length > 0;

      // Only load API translations if we have NO user input AND NO existing translations
      if (
        !hasUserInput &&
        !hasExistingUserTranslations &&
        Object.keys(newTranslatedValues).length > 0
      ) {
        console.log(
          `📥 Loading API translations for ${selectedLanguage} (no user input or existing translations):`,
          newTranslatedValues
        );
        setAllTranslatedValues((prev) => ({
          ...prev,
          [selectedLanguage]: newTranslatedValues,
        }));
      } else if (hasUserInput || hasExistingUserTranslations) {
        console.log(
          `🚫 Skipping API translations for ${selectedLanguage} - user input or existing translations exist:`,
          { hasUserInput, hasExistingUserTranslations, existingTranslations }
        );
      }
    }
    // Note: We don't clear values for base language anymore - they persist
  }, [translations, selectedLanguage, isBaseLanguage]);

  // Helper function to get field value based on selected language
  const getFieldValue = (fieldName: string, baseValue: string): string => {
    if (isBaseLanguage || !isFieldTranslatable(fieldName)) {
      return baseValue;
    }

    // For non-base languages, get the most up-to-date translated value
    // Use the current state directly to avoid stale closure issues
    const currentLanguageTranslations =
      allTranslatedValues[selectedLanguage] || {};
    const translatedValue = currentLanguageTranslations[fieldName];
    const finalValue =
      translatedValue !== undefined ? translatedValue : baseValue;

    console.log(
      `getFieldValue(${fieldName}): base="${baseValue}", translated="${translatedValue}", final="${finalValue}", lang="${selectedLanguage}"`
    );
    return finalValue;
  };

  // Helper function to get FAQ values based on selected language
  const getCurrentFaqs = () => {
    if (isBaseLanguage || !isFieldTranslatable("faqs")) {
      console.log(
        `📋 getCurrentFaqs: Using base FAQs for ${selectedLanguage}:`,
        formData.faqs
      );
      return formData.faqs || [];
    }

    // For non-base languages, use translated FAQs if available
    const result =
      translatedFaqs.length > 0 ? translatedFaqs : formData.faqs || [];
    console.log(
      `📋 getCurrentFaqs: For ${selectedLanguage}, returning:`,
      result
    );
    console.log(
      `📋 getCurrentFaqs: translatedFaqs.length=${
        translatedFaqs.length
      }, formData.faqs.length=${(formData.faqs || []).length}`
    );

    // Debug: Check the structure of the first FAQ
    if (result.length > 0) {
      console.log(`📋 getCurrentFaqs: First FAQ structure:`, result[0]);
      console.log(
        `📋 getCurrentFaqs: First FAQ question:`,
        result[0]?.question
      );
      console.log(`📋 getCurrentFaqs: First FAQ answer:`, result[0]?.answer);
    }

    return result;
  };

  // Helper function to handle FAQ changes
  const handleFaqChange = (
    index: number,
    field: "question" | "answer",
    value: string
  ) => {
    if (isBaseLanguage) {
      // Update base form data
      const updatedFaqs = [...(formData.faqs || [])];
      updatedFaqs[index] = { ...updatedFaqs[index], [field]: value };
      setFormData({ ...formData, faqs: updatedFaqs });
    } else {
      // Update translated FAQs
      const currentFaqs = getCurrentFaqs();
      const updatedFaqs = [...currentFaqs];
      updatedFaqs[index] = { ...updatedFaqs[index], [field]: value };
      setTranslatedFaqs(updatedFaqs);
      faqTranslationsRef.current[selectedLanguage] = updatedFaqs;
    }
  };

  // Helper function to handle field changes
  const handleFieldChange = (fieldName: string, value: string) => {
    console.log(
      `🔄 handleFieldChange called: ${fieldName} = "${value}" (lang: ${selectedLanguage}, isBase: ${isBaseLanguage})`
    );

    if (isBaseLanguage) {
      // Update the base form data
      console.log(`📝 Updating base form data for ${fieldName}`);
      const updated = { ...formData, [fieldName]: value };
      console.log(`📝 Base form data updated:`, updated);
      setFormData(updated);
    } else if (isFieldTranslatable(fieldName)) {
      // Update the translated values for the current language in multi-language state
      console.log(
        `🌐 Updating translation for ${fieldName} in ${selectedLanguage}`
      );

      // Mark this language as having user input
      userInputLanguages.current.add(selectedLanguage);
      console.log(`👤 Marked ${selectedLanguage} as having user input`);

      setAllTranslatedValues((prev) => {
        const updated = {
          ...prev,
          [selectedLanguage]: {
            ...prev[selectedLanguage],
            [fieldName]: value,
          },
        };
        console.log(`🌐 All translated values updated:`, updated);
        console.log(
          `🎯 Current language (${selectedLanguage}) now has:`,
          updated[selectedLanguage]
        );
        return updated;
      });
    } else {
      console.log(
        `⚠️ Field ${fieldName} is not translatable or language conditions not met`
      );
    }
  };

  const [tags, setTags] = useState<string[]>(() => {
    if (!formData.tags) return [];
    if (Array.isArray(formData.tags)) return formData.tags;
    if (typeof formData.tags === "string") {
      try {
        // Try to parse if it's a JSON string
        const parsed = JSON.parse(formData.tags);
        return Array.isArray(parsed) ? parsed : [];
      } catch (e) {
        // If not a valid JSON, split by comma
        return (formData.tags as string)
          .split(",")
          .map((tag: string) => tag.trim())
          .filter((tag: string) => tag);
      }
    }
    return [];
  });

  // Tag translation management
  const {
    translatedTags,
    setTranslatedTags,
    isBaseLanguage: tagIsBaseLanguage,
    hasUnsavedChanges: tagHasUnsavedChanges,
    getAllLanguageTranslations: getAllTagTranslations,
  } = useTagTranslations(destinationId || "new", selectedLanguage, tags);

  // FAQ translation management
  const baseFaqs = (formData.faqs || []).map((faq) => ({
    question: faq.question || "",
    answer: faq.answer || "",
  }));

  // Create a simplified FAQ translation management without separate hook
  const [translatedFaqs, setTranslatedFaqs] = useState<
    Array<{ question: string; answer: string }>
  >([]);
  const faqTranslationsRef = useRef<
    Record<string, Array<{ question: string; answer: string }>>
  >({});

  // Handle FAQ translations when language changes - SIMPLIFIED TO PREVENT INFINITE LOOP
  useEffect(() => {
    console.log(
      `❓ FAQ effect triggered for language: ${selectedLanguage}, isBase: ${isBaseLanguage}`
    );

    if (!isBaseLanguage) {
      // Check if we have local translations for this language
      const localTranslations = faqTranslationsRef.current[selectedLanguage];
      console.log(
        `💾 Local FAQ translations for ${selectedLanguage}:`,
        localTranslations?.length || 0,
        "items"
      );

      if (localTranslations && localTranslations.length > 0) {
        console.log(`🔄 Using local FAQ translations for ${selectedLanguage}`);
        setTranslatedFaqs(localTranslations);
      } else {
        // Initialize with base language values if no translations exist
        console.log(
          `🆕 No local FAQ translations found for ${selectedLanguage}, using base values`
        );
        setTranslatedFaqs(baseFaqs);
        if (baseFaqs.length > 0) {
          faqTranslationsRef.current[selectedLanguage] = baseFaqs;
        }
      }
    } else {
      // For base language, clear translated FAQs
      console.log(`🏠 Base language selected, clearing translated FAQs`);
      setTranslatedFaqs([]);
    }
  }, [selectedLanguage, isBaseLanguage]); // Only depend on language changes

  // Separate effect to handle API translations when they're loaded
  useEffect(() => {
    if (!isBaseLanguage && nestedObjectTranslations[selectedLanguage]?.faqs) {
      const apiTranslations = nestedObjectTranslations[selectedLanguage].faqs;
      const currentLocal = faqTranslationsRef.current[selectedLanguage];

      // Only update if API translations are different from what we have locally
      const shouldUpdate =
        !currentLocal ||
        currentLocal.length === 0 ||
        JSON.stringify(currentLocal) === JSON.stringify(baseFaqs) || // If we only have base values
        JSON.stringify(currentLocal) !== JSON.stringify(apiTranslations);

      if (shouldUpdate && apiTranslations.length > 0) {
        console.log(
          `🔄 Updating FAQ translations from API for ${selectedLanguage}:`,
          apiTranslations.length,
          "items"
        );
        setTranslatedFaqs(apiTranslations);
        faqTranslationsRef.current[selectedLanguage] = apiTranslations;
      }
    }
  }, [nestedObjectTranslations]); // Only depend on API data changes

  // Custom setter for translated FAQs that marks as unsaved and stores locally
  const setTranslatedFaqsWithTracking = useCallback(
    (newFaqs: Array<{ question: string; answer: string }>) => {
      console.log(
        `📝 Setting translated FAQs for ${selectedLanguage}:`,
        newFaqs
      );
      setTranslatedFaqs(newFaqs);
      faqTranslationsRef.current[selectedLanguage] = newFaqs;
    },
    [selectedLanguage]
  );

  // Helper function to get all FAQ translations
  const getAllFaqTranslations = useCallback(() => {
    const result = { ...faqTranslationsRef.current };
    console.log("📋 getAllFaqTranslations called, returning:", result);
    return result;
  }, []);

  // Debug function for browser console
  useEffect(() => {
    (window as any).debugDestinationForm = () => {
      console.log("🔍 Destination Form Debug Info:");
      console.log("📝 Form data:", formData);
      console.log("🌐 Selected language:", selectedLanguage);
      console.log("🏠 Is base language:", isBaseLanguage);
      console.log("📋 Translated values:", translatedValues);
      console.log("🗂️ All translated values:", allTranslatedValues);
      console.log("🏷️ All tag translations:", getAllTagTranslations());
      console.log("❓ All FAQ translations:", getAllFaqTranslations());
      console.log("❓ Current translated FAQs:", translatedFaqs);
      console.log("❓ Current FAQs:", getCurrentFaqs());
      console.log("🔧 FAQ hook state check:");
      console.log("  - translatedFaqs:", translatedFaqs);
      console.log("  - baseFaqs:", formData.faqs);
      console.log("  - isBaseLanguage:", isBaseLanguage);
    };

    // Also add a function to manually load FAQ translations
    (window as any).loadFaqTranslations = async (language?: string) => {
      const lang = language || selectedLanguage;
      console.log(`🔄 Manually loading FAQ translations for ${lang}...`);
      try {
        await loadTranslations(lang);
        console.log(`✅ Manual load complete for ${lang}`);

        // Force check FAQ state after loading
        setTimeout(() => {
          console.log(`\n🔍 ===== MANUAL LOAD VERIFICATION =====`);
          console.log(
            `📊 nestedObjectTranslations after manual load:`,
            nestedObjectTranslations
          );
          console.log(
            `❓ FAQ data for ${lang}:`,
            nestedObjectTranslations[lang]?.faqs
          );
          console.log(`📋 Current translatedFaqs state:`, translatedFaqs);
          console.log(
            `📋 faqTranslationsRef.current:`,
            faqTranslationsRef.current
          );
          console.log(`🔍 ===== MANUAL LOAD VERIFICATION END =====\n`);
        }, 1000);
      } catch (error) {
        console.error(`❌ Manual load failed for ${lang}:`, error);
      }
    };

    // Add a function to manually trigger FAQ effect
    (window as any).triggerFaqEffect = () => {
      console.log(
        `🔄 Manually triggering FAQ effect for ${selectedLanguage}...`
      );
      console.log(
        `📊 Current nestedObjectTranslations:`,
        nestedObjectTranslations
      );
      console.log(
        `❓ Current FAQ data:`,
        nestedObjectTranslations[selectedLanguage]?.faqs
      );

      // Manually trigger the FAQ loading logic
      if (!isBaseLanguage && nestedObjectTranslations[selectedLanguage]?.faqs) {
        const apiTranslations = nestedObjectTranslations[selectedLanguage].faqs;
        console.log(
          `✅ Found FAQ translations, setting them:`,
          apiTranslations
        );
        setTranslatedFaqs(apiTranslations);
        faqTranslationsRef.current[selectedLanguage] = apiTranslations;
        console.log(`✅ FAQ translations set manually`);
      } else {
        console.log(`❌ No FAQ translations found or is base language`);
      }
    };
  }, []); // Empty dependency array - debug functions don't need to update

  // Handle form submission
  const handleSubmit = async () => {
    // Set loading state
    setIsSubmitting(true);

    try {
      // Get the form values including media
      const formValues = form.getValues();

      console.log("Form values from useForm:", formValues);
      console.log("Current formData state:", formData);

      // Use the tags from our state (they are the source of truth)
      const finalTags = tags.length > 0 ? tags : null;

      // Make sure we're getting the media field from the form
      const mediaFromForm = formValues.media || [];

      // Debug media data structure
      console.log("🔍 Media debug info:", {
        mediaFromFormCount: mediaFromForm.length,
        mediaFromForm: mediaFromForm.map((m) => ({
          hasFile: !!m?.file,
          fileName: m?.file?.name,
          fileSize: m?.file?.size,
          fileType: m?.file?.type,
          url: m?.url?.substring(0, 50) + "...",
          isThumbnail: m?.isThumbnail,
          field_id: m?.field_id,
        })),
        formDataMediaCount: formData.media?.length || 0,
      });

      // Create the updated form data, prioritizing the current state values
      // for fields that are managed by our custom components
      const updatedFormData = {
        ...formValues, // Base values from the form
        ...formData, // Override with current state values
        country: formData.country, // Ensure custom select values are used
        media: mediaFromForm, // Explicitly include the media field
        tags: finalTags,
        is_featured: formValues.is_featured || false,
        faqs: formData.faqs || [], // Explicitly preserve FAQ data
        ai_content: formData.ai_content, // Explicitly include AI content
      };

      // For new destinations, include translation data for ALL languages that have translations
      console.log("🔍 Form submission check:", {
        isEdit,
        isBaseLanguage,
        selectedLanguage,
        translatedValuesCount: Object.keys(translatedValues).length,
        translatedValues,
        allTranslatedValues,
        destinationId,
      });

      // Check if we have any translations for ANY language (including FAQs and tags)
      const allLanguagesWithTranslations = Object.keys(
        allTranslatedValues
      ).filter((lang) => {
        const langTranslations = allTranslatedValues[lang] || {};
        const hasTranslations = Object.keys(langTranslations).length > 0;
        console.log(`🔍 Language ${lang}:`, {
          langTranslations,
          hasTranslations,
        });
        return hasTranslations;
      });

      // Also check for FAQ and tag translations
      const allTagTranslations = getAllTagTranslations();
      const allFaqTranslations = getAllFaqTranslations();

      const languagesWithTagTranslations = Object.keys(
        allTagTranslations
      ).filter(
        (lang) =>
          allTagTranslations[lang] && allTagTranslations[lang].length > 0
      );

      const languagesWithFaqTranslations = Object.keys(
        allFaqTranslations
      ).filter(
        (lang) =>
          allFaqTranslations[lang] && allFaqTranslations[lang].length > 0
      );

      // Combine all languages that have any kind of translations
      const allLanguagesWithAnyTranslations = new Set([
        ...allLanguagesWithTranslations,
        ...languagesWithTagTranslations,
        ...languagesWithFaqTranslations,
      ]);

      const hasAnyTranslations = allLanguagesWithAnyTranslations.size > 0;
      const isNewDestination = !isEdit || destinationId === "new";

      console.log("🔍 Translation detection:", {
        allLanguagesWithTranslations,
        languagesWithTagTranslations,
        languagesWithFaqTranslations,
        allLanguagesWithAnyTranslations: Array.from(
          allLanguagesWithAnyTranslations
        ),
        hasAnyTranslations,
      });

      console.log("🎯 Translation conditions:", {
        hasAnyTranslations,
        isNewDestination,
        allLanguagesWithTranslations,
        allLanguagesWithAnyTranslations: Array.from(
          allLanguagesWithAnyTranslations
        ),
        totalLanguages: allLanguagesWithAnyTranslations.size,
        allTranslatedValuesKeys: Object.keys(allTranslatedValues),
        detailedCheck: Object.keys(allTranslatedValues).map((lang) => ({
          language: lang,
          translations: allTranslatedValues[lang],
          count: Object.keys(allTranslatedValues[lang] || {}).length,
        })),
      });

      if (isNewDestination && hasAnyTranslations) {
        // Prepare translations for ALL languages that have translation data
        console.log("🔄 Preparing translations for all languages...");
        const allTranslationsToSave: Record<
          string,
          Record<string, string>
        > = {};

        Array.from(allLanguagesWithAnyTranslations).forEach((languageCode) => {
          console.log(`🌐 Processing language: ${languageCode}`);
          const languageTranslations = allTranslatedValues[languageCode] || {};
          const translationsForLanguage: Record<string, string> = {};

          TRANSLATABLE_FIELDS.forEach((fieldName) => {
            const translatedValue = languageTranslations[fieldName];
            const formValue =
              updatedFormData[fieldName as keyof typeof updatedFormData];
            // For non-base languages, prefer translated value; for base language, use form value
            const value =
              translatedValue || (languageCode === "en" ? formValue : null);

            console.log(`📝 ${languageCode}.${fieldName}:`, {
              translatedValue,
              formValue,
              finalValue: value,
            });

            if (value && typeof value === "string") {
              // Create placeholder key that will be replaced with actual destination ID
              const placeholderKey = `destination.PLACEHOLDER_ID.${fieldName}`;
              translationsForLanguage[placeholderKey] = value;
              console.log(
                `✅ Added ${languageCode} translation: ${placeholderKey} = ${value}`
              );
            } else {
              console.log(`⚠️ No value for ${languageCode}.${fieldName}`);
            }
          });

          if (Object.keys(translationsForLanguage).length > 0) {
            allTranslationsToSave[languageCode] = translationsForLanguage;
            console.log(
              `📦 ${languageCode} translations prepared:`,
              translationsForLanguage
            );
          }
        });

        console.log("🌍 All translations to save:", allTranslationsToSave);

        if (Object.keys(allTranslationsToSave).length > 0) {
          console.log(
            "✅ Adding multi-language translation data to form submission:",
            {
              languages: Object.keys(allTranslationsToSave),
              allTranslations: allTranslationsToSave,
            }
          );

          updatedFormData.translationData = {
            language: "multi", // Indicates multiple languages
            translations: allTranslationsToSave,
            saveFunction: async (destinationId: string) => {
              console.log(
                "🚀 Executing multi-language translation save function for destination:",
                destinationId
              );
              console.log(
                "🌍 Languages to process:",
                Object.keys(allTranslationsToSave)
              );

              // Get all tag translations before processing
              const allTagTranslations = getAllTagTranslations();
              console.log(
                "🏷️ All tag translations to save:",
                allTagTranslations
              );

              // Get all FAQ translations before processing
              const allFaqTranslations = getAllFaqTranslations();
              console.log(
                "❓ All FAQ translations to save:",
                allFaqTranslations
              );

              // Log tag translations setup for debugging
              console.log("🔍 Tag translations setup:", {
                destinationId,
                selectedLanguage,
                translatedTags,
                allTagTranslations,
              });

              // Process each language separately
              for (const [languageCode, languageTranslations] of Object.entries(
                allTranslationsToSave
              )) {
                console.log(`💾 Processing ${languageCode} translations...`);
                console.log(
                  `📝 Raw translations for ${languageCode}:`,
                  languageTranslations
                );

                // Replace placeholder with actual destination ID for this language
                const actualTranslations: Record<string, string> = {};
                Object.entries(languageTranslations).forEach(([key, value]) => {
                  const actualKey = key.replace(
                    "PLACEHOLDER_ID",
                    destinationId
                  );
                  actualTranslations[actualKey] = value;
                });

                console.log(
                  `🔄 Final translations for ${languageCode}:`,
                  actualTranslations
                );

                try {
                  console.log(`🚀 Making API call for ${languageCode}...`);

                  // Save regular translations
                  if (Object.keys(actualTranslations).length > 0) {
                    await saveTranslations(languageCode, actualTranslations);
                    console.log(
                      `✅ Successfully saved ${languageCode} translations`
                    );
                  }

                  // Save tag translations for this language if they exist
                  const tagsForLanguage = allTagTranslations[languageCode];
                  if (tagsForLanguage && tagsForLanguage.length > 0) {
                    console.log(
                      `🔍 DEBUGGING: About to call saveArrayTranslations for ${languageCode}:`,
                      {
                        language: languageCode,
                        fieldName: "tags",
                        arrayValues: tagsForLanguage,
                        destinationId: destinationId,
                      }
                    );
                    await saveArrayTranslations(
                      languageCode,
                      "tags",
                      tagsForLanguage
                    );
                    console.log(
                      `✅ Successfully saved ${languageCode} tag translations:`,
                      tagsForLanguage
                    );
                  } else {
                    console.log(
                      `ℹ️ No tag translations found for ${languageCode}`
                    );
                  }

                  // Save FAQ translations for this language if they exist
                  const faqsForLanguage = allFaqTranslations[languageCode];
                  if (faqsForLanguage && faqsForLanguage.length > 0) {
                    console.log(
                      `🔍 DEBUGGING: About to call saveNestedObjectTranslations for ${languageCode}:`,
                      {
                        language: languageCode,
                        fieldName: "faqs",
                        objectValues: faqsForLanguage,
                        destinationId: destinationId,
                      }
                    );
                    await saveNestedObjectTranslations(
                      languageCode,
                      "faqs",
                      faqsForLanguage
                    );
                    console.log(
                      `✅ Successfully saved ${languageCode} FAQ translations:`,
                      faqsForLanguage
                    );
                  } else {
                    console.log(
                      `ℹ️ No FAQ translations found for ${languageCode}`
                    );
                  }
                } catch (error) {
                  console.error(
                    `❌ Error saving ${languageCode} translations:`,
                    error
                  );
                  // Continue with other languages even if one fails
                }
              }

              // Also save tag translations for any languages that have tags but no regular translations
              for (const [languageCode, tags] of Object.entries(
                allTagTranslations
              )) {
                if (!allTranslationsToSave[languageCode] && tags.length > 0) {
                  console.log(
                    `🏷️ Saving standalone tag translations for ${languageCode}:`,
                    tags
                  );
                  try {
                    await saveArrayTranslations(languageCode, "tags", tags);
                    console.log(
                      `✅ Successfully saved standalone ${languageCode} tag translations`
                    );
                  } catch (error) {
                    console.error(
                      `❌ Error saving standalone ${languageCode} tag translations:`,
                      error
                    );
                  }
                }
              }

              // Also save FAQ translations for any languages that have FAQs but no regular translations
              for (const [languageCode, faqs] of Object.entries(
                allFaqTranslations
              )) {
                const typedFaqs = faqs as Array<{
                  question: string;
                  answer: string;
                }>;
                if (
                  !allTranslationsToSave[languageCode] &&
                  typedFaqs.length > 0
                ) {
                  console.log(
                    `❓ Saving standalone FAQ translations for ${languageCode}:`,
                    typedFaqs
                  );
                  try {
                    await saveNestedObjectTranslations(
                      languageCode,
                      "faqs",
                      typedFaqs
                    );
                    console.log(
                      `✅ Successfully saved standalone ${languageCode} FAQ translations`
                    );
                  } catch (error) {
                    console.error(
                      `❌ Error saving standalone ${languageCode} FAQ translations:`,
                      error
                    );
                  }
                }
              }

              console.log("🎉 All language translations processing completed");
            },
          };
        } else {
          console.log(
            "❌ No translations to save - allTranslationsToSave is empty"
          );
        }
      } else {
        console.log("🚫 Translation conditions not met:", {
          isNewDestination,
          hasAnyTranslations,
          reason: !isNewDestination
            ? "Not a new destination"
            : "No translations found",
        });

        // FALLBACK: Try single-language approach for current language
        if (
          isNewDestination &&
          !isBaseLanguage &&
          (Object.keys(translatedValues).length > 0 ||
            translatedTags.length > 0 ||
            translatedFaqs.length > 0)
        ) {
          console.log(
            "🔄 Fallback: Using single-language approach for current language"
          );
          const translationKeys = getTranslationKeys();
          const translationsToSave: Record<string, string> = {};

          TRANSLATABLE_FIELDS.forEach((fieldName) => {
            const value =
              translatedValues[fieldName] ||
              updatedFormData[fieldName as keyof typeof updatedFormData];
            if (value && typeof value === "string") {
              const key = translationKeys[fieldName];
              if (key) {
                const placeholderKey = key.replace(
                  /destination\..*?\./,
                  "destination.PLACEHOLDER_ID."
                );
                translationsToSave[placeholderKey] = value;
                console.log(
                  `✅ Fallback added translation: ${placeholderKey} = ${value}`
                );
              }
            }
          });

          if (
            Object.keys(translationsToSave).length > 0 ||
            translatedTags.length > 0 ||
            translatedFaqs.length > 0
          ) {
            console.log("✅ Fallback: Adding single-language translation data");
            updatedFormData.translationData = {
              language: selectedLanguage,
              translations: translationsToSave,
              saveFunction: async (destinationId: string) => {
                console.log(
                  "🚀 Fallback: Executing single-language save function"
                );

                // Save regular translations
                if (Object.keys(translationsToSave).length > 0) {
                  const actualTranslations: Record<string, string> = {};
                  Object.entries(translationsToSave).forEach(([key, value]) => {
                    const actualKey = key.replace(
                      "PLACEHOLDER_ID",
                      destinationId
                    );
                    actualTranslations[actualKey] = value;
                  });

                  console.log(
                    "💾 Fallback: Saving translations:",
                    actualTranslations
                  );
                  await saveTranslations(selectedLanguage, actualTranslations);
                }

                // Save tag translations
                if (translatedTags.length > 0) {
                  console.log(
                    "💾 Fallback: Saving tag translations:",
                    translatedTags
                  );
                  await saveArrayTranslations(
                    selectedLanguage,
                    "tags",
                    translatedTags
                  );
                }

                // Save FAQ translations
                if (translatedFaqs.length > 0) {
                  console.log(
                    "💾 Fallback: Saving FAQ translations:",
                    translatedFaqs
                  );
                  await saveNestedObjectTranslations(
                    selectedLanguage,
                    "faqs",
                    translatedFaqs
                  );
                }
              },
            };
          }
        }
      }

      console.log("FAQ data being submitted:", formData.faqs);
      console.log("Updated form data before submit:", updatedFormData);

      // Debug tag translations before submission
      console.log("🏷️ Tag translations before submission:", {
        selectedLanguage,
        translatedTags,
        allTagTranslations: getAllTagTranslations(),
        isNewDestination,
      });

      // Debug FAQ translations before submission
      console.log("❓ FAQ translations before submission:", {
        selectedLanguage,
        translatedFaqs,
        allFaqTranslations: getAllFaqTranslations(),
        isNewDestination,
        isBaseLanguage,
        currentFaqs: getCurrentFaqs(),
      });

      // For existing destinations, save translations for ALL languages that have translated content
      // This should happen regardless of which language is currently selected
      if (destinationId && destinationId !== "new") {
        console.log(
          "🔄 Processing translations for existing destination:",
          destinationId
        );

        // Get all languages that have any kind of translations
        const allTagTranslations = getAllTagTranslations();
        const allFaqTranslations = getAllFaqTranslations();

        // Combine all languages that have translations
        const allLanguagesWithTranslations = new Set([
          ...Object.keys(allTranslatedValues).filter((lang) => {
            const langTranslations = allTranslatedValues[lang] || {};
            return Object.keys(langTranslations).length > 0;
          }),
          ...Object.keys(allTagTranslations).filter(
            (lang) =>
              allTagTranslations[lang] && allTagTranslations[lang].length > 0
          ),
          ...Object.keys(allFaqTranslations).filter(
            (lang) =>
              allFaqTranslations[lang] && allFaqTranslations[lang].length > 0
          ),
        ]);

        console.log(
          "🌍 Languages with translations for existing destination:",
          Array.from(allLanguagesWithTranslations)
        );

        // Set translation saving loading state if we have translations to save
        if (allLanguagesWithTranslations.size > 0) {
          setIsSavingTranslations(true);
        }

        // Save translations for each language that has translated content
        for (const languageCode of allLanguagesWithTranslations) {
          if (languageCode === "en") continue; // Skip base language

          console.log(
            `💾 Processing ${languageCode} translations for existing destination...`
          );

          try {
            // Get translation keys for this destination
            const translationKeys = getTranslationKeys();

            // Prepare regular field translations
            const translationsToSave: Record<string, string> = {};
            const languageTranslations =
              allTranslatedValues[languageCode] || {};

            TRANSLATABLE_FIELDS.forEach((fieldName) => {
              const value = languageTranslations[fieldName];
              if (value && typeof value === "string") {
                const key = translationKeys[fieldName];
                if (key) {
                  translationsToSave[key] = value;
                  console.log(
                    `✅ Added ${languageCode} translation: ${key} = ${value}`
                  );
                }
              }
            });

            // Save regular field translations
            if (Object.keys(translationsToSave).length > 0) {
              await saveTranslations(languageCode, translationsToSave);
              console.log(
                `✅ Successfully saved ${languageCode} field translations`
              );
            }

            // Save tag translations for this language
            const tagsForLanguage = allTagTranslations[languageCode];
            if (tagsForLanguage && tagsForLanguage.length > 0) {
              console.log(
                `🏷️ Saving ${languageCode} tag translations:`,
                tagsForLanguage
              );
              await saveArrayTranslations(
                languageCode,
                "tags",
                tagsForLanguage
              );
              console.log(
                `✅ Successfully saved ${languageCode} tag translations`
              );
            }

            // Save FAQ translations for this language
            const faqsForLanguage = allFaqTranslations[languageCode];
            if (faqsForLanguage && faqsForLanguage.length > 0) {
              console.log(
                `❓ Saving ${languageCode} FAQ translations:`,
                faqsForLanguage
              );
              await saveNestedObjectTranslations(
                languageCode,
                "faqs",
                faqsForLanguage
              );
              console.log(
                `✅ Successfully saved ${languageCode} FAQ translations`
              );
            }
          } catch (error) {
            console.error(
              `❌ Error saving ${languageCode} translations:`,
              error
            );
            // Continue with other languages even if one fails
          }
        }

        // Clear translation saving loading state
        setIsSavingTranslations(false);
        console.log("🎉 Completed saving translations for all languages");
      }

      // Update the state
      setFormData(updatedFormData);

      // Pass the updated form data directly to onSubmit
      const success = await onSubmit(updatedFormData);
      if (success) {
        closeModal();
      }
    } catch (error) {
      console.error("Error submitting destination form:", error);
      // You can add toast notification here if needed
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <>
      <FocusModal.Header className=" flex justify-between items-center w-full">
        <div className="flex justify-between items-center w-full">
          <Heading level="h2" className="text-xl font-semibold">
            {isEdit ? "Edit Destination" : "Add New Destination"}
          </Heading>
          <LanguageSelector
            selectedLanguage={selectedLanguage}
            onLanguageChange={setSelectedLanguage}
          />
        </div>
      </FocusModal.Header>

      <div className="flex flex-col overflow-y-auto flex-1 px-6 pb-4 w-full ">
        <div className="w-full">
          <Tabs
            value={activeTab}
            onValueChange={(newTab) => {
              const currentFormMedia = form.getValues("media") || [];
              console.log("🔄 Tab switching:", {
                from: activeTab,
                to: newTab,
                formMediaCount: currentFormMedia.length,
                parentFormDataMediaCount: formData.media?.length || 0,
                formMediaDetails: currentFormMedia.map((m) => ({
                  hasFile: !!m?.file,
                  hasFieldId: !!m?.field_id,
                  fileName: m?.file?.name,
                })),
              });
              setActiveTab(newTab);
            }}
            className="w-full mt-5"
          >
            <Tabs.List className="grid grid-cols-4 mb-6">
              <Tabs.Trigger value="basics" className="flex items-center gap-2">
                <Building size={18} />
                <span>Basic Information</span>
              </Tabs.Trigger>
              <Tabs.Trigger value="details" className="flex items-center gap-2">
                <Info size={18} />
                <span>Additional Details</span>
              </Tabs.Trigger>
              <Tabs.Trigger value="media" className="flex items-center gap-2">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="18"
                  height="18"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                >
                  <rect x="3" y="3" width="18" height="18" rx="2" ry="2" />
                  <circle cx="8.5" cy="8.5" r="1.5" />
                  <polyline points="21 15 16 10 5 21" />
                </svg>
                <span>Images</span>
              </Tabs.Trigger>
              <Tabs.Trigger value="faqs" className="flex items-center gap-2">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="18"
                  height="18"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                >
                  <circle cx="12" cy="12" r="10" />
                  <path d="M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3" />
                  <path d="M12 17h.01" />
                </svg>
                <span>FAQs</span>
              </Tabs.Trigger>
            </Tabs.List>

            {/* Translation Info Tip */}
            {!isBaseLanguage && (
              <div className="mb-6">
                <InlineTip label="Translation Mode">
                  You are editing in{" "}
                  {tolgeeLanguages.find((lang) => lang.tag === selectedLanguage)
                    ?.name || selectedLanguage}{" "}
                  language. Only translatable fields (name, description,
                  location, tags, FAQs) can be edited. Other fields show base
                  language values and are read-only.
                  {destinationId &&
                    destinationId !== "new" &&
                    (() => {
                      const keys = getTranslationKeys();
                      console.log("Generated translation keys:", keys);
                      return null;
                    })()}
                </InlineTip>
              </div>
            )}

            <Tabs.Content value="basics" className="mt-0">
              <div className="space-y-6">
                <div className="bg-card pt-4 p-6 rounded-lg shadow-sm border border-border">
                  <h3 className="text-lg font-medium mb-4 text-card-foreground">
                    Destination Information
                  </h3>

                  <div className="space-y-4">
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <div className="flex items-center justify-between mb-1">
                          <Label htmlFor="name" className="font-medium">
                            Destination Name{" "}
                            <span className="text-destructive">*</span>
                          </Label>
                          {/* AI Translate button for name field - Only show for non-base languages */}
                          {!isBaseLanguage && formData.name && (
                            <AITranslateButton
                              sourceText={formData.name}
                              targetLanguage={selectedLanguage}
                              fieldType="name"
                              onTranslate={(translatedText) => {
                                console.log(
                                  `🤖 AI translated name: "${translatedText}"`
                                );
                                handleFieldChange("name", translatedText);
                              }}
                              context={{
                                entityType: "destination",
                                entityName: formData.name,
                                location: formData.location || formData.country,
                              }}
                              size="small"
                              variant="transparent"
                              showText={false}
                            />
                          )}
                        </div>
                        <Input
                          id="name"
                          key={`name-${selectedLanguage}`}
                          value={getFieldValue("name", formData.name)}
                          onChange={(e) => {
                            const value = e.target.value;
                            console.log(
                              `🎯 Name input onChange: "${value}" (lang: ${selectedLanguage})`
                            );
                            if (isBaseLanguage) {
                              safeSetFormData({
                                name: value,
                                handle: value
                                  .toLowerCase()
                                  .replace(/\s+/g, "-"),
                              });
                            } else {
                              // For non-base languages, only update name (not handle)
                              handleFieldChange("name", value);
                            }
                          }}
                          placeholder="e.g. Swiss Alps"
                          className="w-full h-9 text-sm"
                          disabled={!isFieldEnabled("name", isBaseLanguage)}
                        />
                        <Text className="text-xs text-muted-foreground mt-1">
                          The name of the destination as it will appear to users
                        </Text>
                      </div>

                      <div>
                        <Label
                          htmlFor="handle"
                          className="mb-1 font-medium flex items-center gap-1"
                        >
                          URL Handle <span className="text-destructive">*</span>
                          <Tooltip content="This will be used in the URL for this destination (e.g. /destinations/swiss-alps)">
                            <Info size={12} className="text-muted-foreground" />
                          </Tooltip>
                        </Label>
                        <Input
                          id="handle"
                          value={formData.handle}
                          onChange={(e) =>
                            safeSetFormData({ handle: e.target.value })
                          }
                          placeholder="e.g. swiss-alps"
                          className="w-full h-9 text-sm"
                          disabled={!isFieldEnabled("handle", isBaseLanguage)}
                        />
                      </div>
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <Label
                          htmlFor="country"
                          className="block mb-1 font-medium"
                        >
                          Country <span className="text-destructive">*</span>
                        </Label>
                        <CountrySelector
                          value={formData.country}
                          onChange={(value) => {
                            console.log("Country changed to:", value);
                            const selectedCountry = value || "";
                            safeSetFormData({
                              country: selectedCountry,
                            });
                            // Also update the form values
                            form.setValue("country", selectedCountry);
                          }}
                          placeholder="Search and select a country"
                          className="w-full"
                          disabled={!isFieldEnabled("country", isBaseLanguage)}
                          allowClear={true}
                        />
                      </div>

                      <div>
                        <div className="flex items-center justify-between mb-1">
                          <div className="flex items-center gap-1">
                            <Label htmlFor="location" className="font-medium">
                              Specific Location
                            </Label>
                            <Tooltip content="More specific location within the country (e.g. city, region)">
                              <Info
                                size={12}
                                className="text-muted-foreground"
                              />
                            </Tooltip>
                          </div>
                          {/* AI Translate button for location field - Only show for non-base languages */}
                          {!isBaseLanguage && formData.location && (
                            <AITranslateButton
                              sourceText={formData.location}
                              targetLanguage={selectedLanguage}
                              fieldType="content"
                              onTranslate={(translatedText) => {
                                console.log(
                                  `🤖 AI translated location: "${translatedText}"`
                                );
                                handleFieldChange("location", translatedText);
                              }}
                              context={{
                                entityType: "destination",
                                entityName: formData.name,
                                location: formData.country,
                              }}
                              size="small"
                              variant="transparent"
                              showText={false}
                            />
                          )}
                        </div>
                        <div className="relative">
                          <Input
                            id="location"
                            key={`location-${selectedLanguage}`}
                            value={getFieldValue(
                              "location",
                              formData.location || ""
                            )}
                            onChange={(e) => {
                              const value = e.target.value;
                              console.log(
                                `🎯 Location input onChange: "${value}" (lang: ${selectedLanguage})`
                              );
                              handleFieldChange("location", e.target.value);
                            }}
                            placeholder="e.g. Zermatt, Valais"
                            className="w-full h-9 text-sm"
                            disabled={
                              !isFieldEnabled("location", isBaseLanguage)
                            }
                          />
                        </div>
                      </div>
                    </div>
                    <div>
                      <div className="flex items-center justify-between mb-1">
                        <Label className="font-medium">Description</Label>
                        {/* AI Translate button for description field - Only show for non-base languages */}
                        {!isBaseLanguage && formData.description && (
                          <AITranslateButton
                            sourceText={formData.description}
                            targetLanguage={selectedLanguage}
                            fieldType="description"
                            onTranslate={(translatedText) => {
                              console.log(
                                `🤖 AI translated description: "${translatedText}"`
                              );
                              handleFieldChange("description", translatedText);
                            }}
                            context={{
                              entityType: "destination",
                              entityName: formData.name,
                              location: formData.location || formData.country,
                            }}
                            size="small"
                            variant="transparent"
                            showText={false}
                          />
                        )}
                      </div>
                      <TextareaField
                        id="description"
                        key={`description-${selectedLanguage}`}
                        value={getFieldValue(
                          "description",
                          formData.description
                        )}
                        onChange={(value) => {
                          console.log(
                            `🎯 Description input onChange: "${value}" (lang: ${selectedLanguage})`
                          );
                          handleFieldChange("description", value);
                        }}
                        placeholder="Describe this destination..."
                        rows={5}
                        contentType="description"
                        context={{
                          name: formData.name,
                          type: "destination",
                          country: formData.country,
                          location: formData.location,
                        }}
                        helpText="Provide a detailed description of the destination to help users understand what makes it special"
                        disabled={
                          !isFieldEnabled("description", isBaseLanguage)
                        }
                      />
                    </div>
                  </div>
                </div>

                <VisibilitySettings
                  title="Visibility Settings"
                  options={[
                    {
                      id: "is_active",
                      label: "Active",
                      description:
                        "When active, the destination will be visible to users",
                      checked: formData.is_active,
                      onChange: (checked) =>
                        safeSetFormData({ is_active: checked }),
                    },
                    {
                      id: "is_featured",
                      label: "Featured",
                      description:
                        "Featured destinations will be highlighted and shown prominently to users",
                      checked: formData.is_featured,
                      onChange: (checked) =>
                        safeSetFormData({ is_featured: checked }),
                    },
                  ]}
                />
              </div>
            </Tabs.Content>

            <Tabs.Content value="details" className="mt-0">
              <div className="space-y-6">
                <div className="bg-card p-6 rounded-lg shadow-sm border border-border">
                  <h3 className="text-lg font-medium mb-4 text-card-foreground">
                    Additional Information
                  </h3>

                  <div className="space-y-4">

                    <TranslatableTagInput
                      label="Tags"
                      baseTags={tags}
                      translatedTags={translatedTags}
                      onBaseTagsChange={(newTags) => {
                        setTags(newTags);
                        safeSetFormData({ tags: newTags });
                      }}
                      onTranslatedTagsChange={setTranslatedTags}
                      isBaseLanguage={tagIsBaseLanguage}
                      disabled={!isFieldEnabled("tags", isBaseLanguage)}
                      placeholder="Type a tag and press Enter"
                      helpText="Add tags to categorize this destination (e.g. mountains, beach, family-friendly)"
                      currentLanguage={selectedLanguage}
                      hasUnsavedChanges={tagHasUnsavedChanges}
                    />

                    {/* Web Links Section */}
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <Label htmlFor="internal_web_link" className="mb-2 block">
                          Internal Web Link
                        </Label>
                        <Input
                          id="internal_web_link"
                          type="url"
                          value={formData.internal_web_link || ""}
                          onChange={(e) => {
                            const value = e.target.value;
                            safeSetFormData({ internal_web_link: value });
                          }}
                          onBlur={(e) => {
                            const value = e.target.value;
                            if (value && !isValidUrl(value)) {
                              // Try to normalize the URL
                              const normalized = normalizeUrl(value);
                              if (isValidUrl(normalized)) {
                                safeSetFormData({ internal_web_link: normalized });
                              }
                            }
                          }}
                          placeholder="https://example.com"
                          className="w-full"
                          disabled={!isBaseLanguage}
                        />
                        <Text className="text-xs text-muted-foreground mt-1">
                          Internal website or page link
                        </Text>
                      </div>

                      <div>
                        <Label htmlFor="external_web_link" className="mb-2 block">
                          External Web Link
                        </Label>
                        <Input
                          id="external_web_link"
                          type="url"
                          value={formData.external_web_link || ""}
                          onChange={(e) => {
                            const value = e.target.value;
                            safeSetFormData({ external_web_link: value });
                          }}
                          onBlur={(e) => {
                            const value = e.target.value;
                            if (value && !isValidUrl(value)) {
                              // Try to normalize the URL
                              const normalized = normalizeUrl(value);
                              if (isValidUrl(normalized)) {
                                safeSetFormData({ external_web_link: normalized });
                              }
                            }
                          }}
                          placeholder="https://external-site.com"
                          className="w-full"
                          disabled={!isBaseLanguage}
                        />
                        <Text className="text-xs text-muted-foreground mt-1">
                          External website or resource link
                        </Text>
                      </div>
                    </div>

                    {/* Currency and Margin Section */}
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <Label htmlFor="currency" className="mb-2 block">
                          Currency
                        </Label>
                        <CurrencySelector
                          value={formData.currency || ""}
                          onChange={(currencyCode) => {
                            safeSetFormData({ currency: currencyCode });
                          }}
                          placeholder="Select currency"
                          disabled={!isBaseLanguage}
                          showLabel={false}
                        />
                        <Text className="text-xs text-muted-foreground mt-1">
                          Default currency for this destination
                        </Text>
                      </div>

                      <div>
                        <Label htmlFor="margin" className="mb-2 block">
                          Margin (%)
                        </Label>
                        <Input
                          id="margin"
                          type="number"
                          min="0"
                          max="100"
                          step="0.01"
                          value={formData.margin || ""}
                          onChange={(e) => {
                            const value = e.target.value;
                            safeSetFormData({
                              margin: value === "" ? null : parseFloat(value)
                            });
                          }}
                          placeholder="0.00"
                          className="w-full"
                          disabled={!isBaseLanguage}
                        />
                        <Text className="text-xs text-muted-foreground mt-1">
                          Default margin percentage for this destination
                        </Text>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </Tabs.Content>

            <Tabs.Content value="media" className="mt-0">
              <div className="space-y-6">
                <div className="bg-card p-6 rounded-lg shadow-sm border border-border">
                  <h3 className="text-lg font-medium mb-4 text-card-foreground">
                    Destination Images
                  </h3>
                  <Text className="text-sm text-muted-foreground mb-4">
                    Upload images that showcase this destination. The first
                    image or the one marked as thumbnail will be used as the
                    main image.
                  </Text>

                  <DestinationMediaSection
                    form={form}
                    destinationId={destinationId}
                  />
                </div>
              </div>
            </Tabs.Content>

            <Tabs.Content value="faqs" className="mt-0">
              <div className="space-y-6">
                <div className="bg-card p-6 rounded-lg shadow-sm border border-border">
                  <div className="flex justify-between items-center mb-4">
                    <h3 className="text-lg font-medium text-card-foreground">
                      Frequently Asked Questions
                    </h3>
                    <Button
                      variant="secondary"
                      size="small"
                      onClick={() => {
                        const newFaq = { question: "", answer: "" };
                        if (isBaseLanguage) {
                          setFormData({
                            ...formData,
                            faqs: [...(formData.faqs || []), newFaq],
                          });
                        } else {
                          const currentFaqs = getCurrentFaqs();
                          setTranslatedFaqs([...currentFaqs, newFaq]);
                        }
                      }}
                      disabled={!isFieldEnabled("faqs", isBaseLanguage)}
                    >
                      Add FAQ
                    </Button>
                  </div>
                  <Text className="text-sm text-muted-foreground mb-4">
                    Add frequently asked questions to help users understand more
                    about this destination.
                  </Text>

                  {(() => {
                    const currentFaqs = getCurrentFaqs();
                    console.log(
                      `🎯 FAQ Rendering: currentFaqs for ${selectedLanguage}:`,
                      currentFaqs
                    );
                    return currentFaqs && currentFaqs.length > 0 ? (
                      <div className="space-y-4">
                        {currentFaqs.map((faq, index) => {
                          console.log(`🎯 FAQ Rendering: FAQ ${index}:`, faq);
                          console.log(
                            `🎯 FAQ Rendering: FAQ ${index} question:`,
                            faq?.question
                          );
                          console.log(
                            `🎯 FAQ Rendering: FAQ ${index} answer:`,
                            faq?.answer
                          );
                          return (
                            <div
                              key={index}
                              className="border border-border rounded-lg p-4 space-y-3 bg-muted"
                            >
                              <div className="flex justify-between items-start">
                                <Text className="text-sm font-medium text-foreground">
                                  FAQ {index + 1}
                                </Text>
                                <Button
                                  variant="transparent"
                                  size="small"
                                  onClick={() => {
                                    if (isBaseLanguage) {
                                      const updatedFaqs =
                                        formData.faqs?.filter(
                                          (_, i) => i !== index
                                        ) || [];
                                      setFormData({
                                        ...formData,
                                        faqs: updatedFaqs,
                                      });
                                    } else {
                                      const currentFaqs = getCurrentFaqs();
                                      const updatedFaqs = currentFaqs.filter(
                                        (_, i) => i !== index
                                      );
                                      setTranslatedFaqs(updatedFaqs);
                                    }
                                  }}
                                  className="text-destructive hover:text-destructive/80"
                                  disabled={
                                    !isFieldEnabled("faqs", isBaseLanguage)
                                  }
                                >
                                  Remove
                                </Button>
                              </div>
                              <div>
                                <div className="flex items-center justify-between">
                                  <Label className="text-xs text-muted-foreground font-medium">
                                    Question
                                  </Label>
                                  {/* AI Translate button for FAQ question - Only show for non-base languages */}
                                  {!isBaseLanguage &&
                                    formData.faqs &&
                                    formData.faqs[index]?.question && (
                                      <AITranslateButton
                                        sourceText={
                                          formData.faqs[index].question
                                        }
                                        targetLanguage={selectedLanguage}
                                        fieldType="content"
                                        onTranslate={(translatedText) => {
                                          console.log(
                                            `🤖 AI translated FAQ question ${index}: "${translatedText}"`
                                          );
                                          handleFaqChange(
                                            index,
                                            "question",
                                            translatedText
                                          );
                                        }}
                                        context={{
                                          entityType: "destination",
                                          entityName: formData.name,
                                          location:
                                            formData.location ||
                                            formData.country,
                                        }}
                                        size="small"
                                        variant="transparent"
                                        showText={false}
                                      />
                                    )}
                                </div>
                                <Input
                                  value={faq.question || ""}
                                  onChange={(e) => {
                                    handleFaqChange(
                                      index,
                                      "question",
                                      e.target.value
                                    );
                                  }}
                                  placeholder="Enter question"
                                  className="mt-1 h-9 text-sm"
                                  disabled={
                                    !isFieldEnabled("faqs", isBaseLanguage)
                                  }
                                />
                              </div>
                              <div>
                                <div className="flex items-center justify-between">
                                  <Label className="text-xs text-muted-foreground font-medium">
                                    Answer
                                  </Label>
                                  {/* AI Translate button for FAQ answer - Only show for non-base languages */}
                                  {!isBaseLanguage &&
                                    formData.faqs &&
                                    formData.faqs[index]?.answer && (
                                      <AITranslateButton
                                        sourceText={formData.faqs[index].answer}
                                        targetLanguage={selectedLanguage}
                                        fieldType="description"
                                        onTranslate={(translatedText) => {
                                          console.log(
                                            `🤖 AI translated FAQ answer ${index}: "${translatedText}"`
                                          );
                                          handleFaqChange(
                                            index,
                                            "answer",
                                            translatedText
                                          );
                                        }}
                                        context={{
                                          entityType: "destination",
                                          entityName: formData.name,
                                          location:
                                            formData.location ||
                                            formData.country,
                                        }}
                                        size="small"
                                        variant="transparent"
                                        showText={false}
                                      />
                                    )}
                                </div>
                                <TextareaField
                                  value={faq.answer || ""}
                                  onChange={(value) => {
                                    handleFaqChange(index, "answer", value);
                                  }}
                                  placeholder="Enter answer"
                                  rows={3}
                                  contentType="description"
                                  context={{
                                    question: faq.question,
                                    destination: formData.name,
                                    country: formData.country,
                                    location: formData.location,
                                  }}
                                  className="mt-1"
                                  disabled={
                                    !isFieldEnabled("faqs", isBaseLanguage)
                                  }
                                />
                              </div>
                            </div>
                          );
                        })}
                      </div>
                    ) : (
                      <div className="text-center py-8 text-muted-foreground bg-muted rounded-lg">
                        <svg
                          className="mx-auto h-12 w-12 text-muted-foreground mb-4"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                          />
                        </svg>
                        <Text>
                          No FAQs added yet. Click "Add FAQ" to get started.
                        </Text>
                      </div>
                    );
                  })()}
                </div>
              </div>
            </Tabs.Content>
          </Tabs>
        </div>
      </div>

      <div className="flex justify-end gap-2 p-4 border-t border-border bg-card">
        <Button variant="secondary" onClick={closeModal}>
          Cancel
        </Button>
        <Button
          variant="primary"
          onClick={handleSubmit}
          disabled={!formData.name || !formData.handle || isSubmitting}
          isLoading={isSubmitting}
        >
          {isSubmitting
            ? isSavingTranslations
              ? "Saving Translations..."
              : isEdit
              ? "Updating..."
              : "Creating..."
            : isEdit
            ? "Update Destination"
            : "Create Destination"}
        </Button>
      </div>
    </>
  );
};

export default DestinationFormModern;
