import React, { useState, useEffect, useMemo } from "react";
import { Container, Heading, Text, Badge, Button, Toaster, toast } from "@camped-ai/ui";
import { 
  Bed, 
  Hotel, 
  ArrowRight, 
  ArrowLeft, 
  ArrowUp, 
  ArrowDown, 
  Link as LinkIcon, 
  CheckCircle, 
  XCircle, 
  Alert<PERSON><PERSON>gle, 
  Clock,
  Maximize,
  Minimize
} from "lucide-react";

interface Room {
  id: string;
  name: string;
  room_number: string;
  status: string;
  floor: string;
  notes?: string;
  is_active: boolean;
  left_room?: string;
  opposite_room?: string;
  connected_room?: string;
  right_room?: string;
  room_config_id: string;
  hotel_id: string;
}

interface FloorPlanVisualizationProps {
  rooms: Room[];
  hotelId: string;
  onRoomClick?: (roomId: string) => void;
  className?: string;
}

interface RoomNode {
  room: Room;
  x: number;
  y: number;
  placed: boolean;
}

const FloorPlanVisualization: React.FC<FloorPlanVisualizationProps> = ({
  rooms,
  hotelId,
  onRoomClick,
  className = "",
}) => {
  const [selectedFloor, setSelectedFloor] = useState<string>("");
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [roomNodes, setRoomNodes] = useState<RoomNode[]>([]);
  const [gridSize, setGridSize] = useState({ width: 5, height: 5 });
  const [hoveredRoom, setHoveredRoom] = useState<string | null>(null);

  // Get unique floors from rooms
  const floors = useMemo(() => {
    const uniqueFloors = [...new Set(rooms.map(room => room.floor))].filter(Boolean);
    return uniqueFloors.sort((a, b) => {
      // Try to sort numerically if possible
      const numA = parseInt(a);
      const numB = parseInt(b);
      if (!isNaN(numA) && !isNaN(numB)) {
        return numA - numB;
      }
      // Fall back to string comparison
      return a.localeCompare(b);
    });
  }, [rooms]);

  // Set default selected floor if not already set
  useEffect(() => {
    if (floors.length > 0 && !selectedFloor) {
      setSelectedFloor(floors[0]);
    }
  }, [floors, selectedFloor]);

  // Filter rooms by selected floor
  const floorRooms = useMemo(() => {
    return rooms.filter(room => room.floor === selectedFloor);
  }, [rooms, selectedFloor]);

  // Calculate room positions based on relationships
  useEffect(() => {
    if (floorRooms.length === 0) return;

    // Initialize room nodes with default positions
    const nodes: RoomNode[] = floorRooms.map(room => ({
      room,
      x: 0,
      y: 0,
      placed: false,
    }));

    // Find a room to start with (preferably one with relationships)
    let startNode = nodes.find(node => 
      node.room.left_room || node.room.right_room || 
      node.room.opposite_room || node.room.connected_room
    ) || nodes[0];
    
    startNode.x = Math.floor(gridSize.width / 2);
    startNode.y = Math.floor(gridSize.height / 2);
    startNode.placed = true;

    // Helper function to find a node by room ID
    const findNodeByRoomId = (roomId: string) => {
      return nodes.find(node => node.room.id === roomId);
    };

    // Helper function to find a node by room number
    const findNodeByRoomNumber = (roomNumber: string) => {
      return nodes.find(node => node.room.room_number === roomNumber);
    };

    // Place rooms based on relationships
    let placedCount = 1;
    let iterations = 0;
    const maxIterations = nodes.length * 2; // Prevent infinite loops

    while (placedCount < nodes.length && iterations < maxIterations) {
      iterations++;
      let newPlaced = false;

      for (const node of nodes) {
        if (node.placed) continue;

        // Check if this node has a relationship with any placed node
        for (const placedNode of nodes.filter(n => n.placed)) {
          // Check left relationship
          if (placedNode.room.left_room === node.room.id || placedNode.room.left_room === node.room.room_number) {
            node.x = placedNode.x - 1;
            node.y = placedNode.y;
            node.placed = true;
            newPlaced = true;
            placedCount++;
            break;
          }
          // Check right relationship
          if (placedNode.room.right_room === node.room.id || placedNode.room.right_room === node.room.room_number) {
            node.x = placedNode.x + 1;
            node.y = placedNode.y;
            node.placed = true;
            newPlaced = true;
            placedCount++;
            break;
          }
          // Check opposite relationship
          if (placedNode.room.opposite_room === node.room.id || placedNode.room.opposite_room === node.room.room_number) {
            node.x = placedNode.x;
            node.y = placedNode.y + 1;
            node.placed = true;
            newPlaced = true;
            placedCount++;
            break;
          }
          // Check connected relationship
          if (placedNode.room.connected_room === node.room.id || placedNode.room.connected_room === node.room.room_number) {
            // Place connected rooms diagonally if possible
            node.x = placedNode.x + 1;
            node.y = placedNode.y + 1;
            node.placed = true;
            newPlaced = true;
            placedCount++;
            break;
          }

          // Check reverse relationships
          if (node.room.left_room === placedNode.room.id || node.room.left_room === placedNode.room.room_number) {
            node.x = placedNode.x + 1;
            node.y = placedNode.y;
            node.placed = true;
            newPlaced = true;
            placedCount++;
            break;
          }
          if (node.room.right_room === placedNode.room.id || node.room.right_room === placedNode.room.room_number) {
            node.x = placedNode.x - 1;
            node.y = placedNode.y;
            node.placed = true;
            newPlaced = true;
            placedCount++;
            break;
          }
          if (node.room.opposite_room === placedNode.room.id || node.room.opposite_room === placedNode.room.room_number) {
            node.x = placedNode.x;
            node.y = placedNode.y - 1;
            node.placed = true;
            newPlaced = true;
            placedCount++;
            break;
          }
          if (node.room.connected_room === placedNode.room.id || node.room.connected_room === placedNode.room.room_number) {
            node.x = placedNode.x - 1;
            node.y = placedNode.y - 1;
            node.placed = true;
            newPlaced = true;
            placedCount++;
            break;
          }
        }
      }

      // If no new nodes were placed in this iteration, place remaining nodes in available spaces
      if (!newPlaced && placedCount < nodes.length) {
        // Find unplaced node
        const unplacedNode = nodes.find(node => !node.placed);
        if (unplacedNode) {
          // Find an empty spot near the center
          let placed = false;
          const centerX = Math.floor(gridSize.width / 2);
          const centerY = Math.floor(gridSize.height / 2);
          
          // Spiral outward from center to find empty spot
          for (let radius = 1; radius < Math.max(gridSize.width, gridSize.height) && !placed; radius++) {
            for (let dx = -radius; dx <= radius && !placed; dx++) {
              for (let dy = -radius; dy <= radius && !placed; dy++) {
                // Only check points on the perimeter of the square
                if (Math.abs(dx) === radius || Math.abs(dy) === radius) {
                  const x = centerX + dx;
                  const y = centerY + dy;
                  
                  // Check if position is empty
                  if (!nodes.some(n => n.placed && n.x === x && n.y === y)) {
                    unplacedNode.x = x;
                    unplacedNode.y = y;
                    unplacedNode.placed = true;
                    placedCount++;
                    placed = true;
                    break;
                  }
                }
              }
            }
          }
        }
      }
    }

    // Normalize coordinates to ensure all are positive
    const minX = Math.min(...nodes.map(node => node.x));
    const minY = Math.min(...nodes.map(node => node.y));
    
    nodes.forEach(node => {
      node.x -= minX - 1;
      node.y -= minY - 1;
    });

    // Update grid size based on node positions
    const maxX = Math.max(...nodes.map(node => node.x)) + 1;
    const maxY = Math.max(...nodes.map(node => node.y)) + 1;
    
    setGridSize({
      width: Math.max(5, maxX + 1),
      height: Math.max(5, maxY + 1)
    });
    
    setRoomNodes(nodes);
  }, [floorRooms]);

  // Get status color for room
  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'available':
        return 'bg-green-100 border-green-300';
      case 'occupied':
      case 'booked':
        return 'bg-blue-100 border-blue-300';
      case 'maintenance':
        return 'bg-orange-100 border-orange-300';
      case 'cleaning':
        return 'bg-yellow-100 border-yellow-300';
      default:
        return 'bg-gray-100 border-gray-300';
    }
  };

  // Get status icon for room
  const getStatusIcon = (status: string) => {
    switch (status.toLowerCase()) {
      case 'available':
        return <CheckCircle className="w-4 h-4 text-green-600" />;
      case 'occupied':
      case 'booked':
        return <Bed className="w-4 h-4 text-blue-600" />;
      case 'maintenance':
        return <AlertTriangle className="w-4 h-4 text-orange-600" />;
      case 'cleaning':
        return <Clock className="w-4 h-4 text-yellow-600" />;
      case 'on_hold':
        return <Clock className="w-4 h-4 text-yellow-500" />;
      default:
        return <XCircle className="w-4 h-4 text-gray-600" />;
    }
  };

  // Toggle fullscreen mode
  const toggleFullscreen = () => {
    setIsFullscreen(!isFullscreen);
  };

  // Render connection lines between rooms
  const renderConnectionLines = () => {
    const lines: JSX.Element[] = [];
    
    roomNodes.forEach(node => {
      const { room, x, y } = node;
      
      // Helper to find node position by room ID or room number
      const findNodePosition = (roomIdOrNumber: string | undefined) => {
        if (!roomIdOrNumber) return null;
        
        const targetNode = roomNodes.find(n => 
          n.room.id === roomIdOrNumber || n.room.room_number === roomIdOrNumber
        );
        
        return targetNode ? { x: targetNode.x, y: targetNode.y } : null;
      };
      
      // Left room connection
      const leftPos = findNodePosition(room.left_room);
      if (leftPos) {
        lines.push(
          <line 
            key={`${room.id}-left`}
            x1={x * 120 + 60}
            y1={y * 120 + 60}
            x2={leftPos.x * 120 + 60}
            y2={leftPos.y * 120 + 60}
            stroke={hoveredRoom === room.id ? "#4f46e5" : "#9ca3af"}
            strokeWidth={hoveredRoom === room.id ? 3 : 2}
            strokeDasharray={hoveredRoom === room.id ? "none" : "5,5"}
          />
        );
      }
      
      // Right room connection
      const rightPos = findNodePosition(room.right_room);
      if (rightPos) {
        lines.push(
          <line 
            key={`${room.id}-right`}
            x1={x * 120 + 60}
            y1={y * 120 + 60}
            x2={rightPos.x * 120 + 60}
            y2={rightPos.y * 120 + 60}
            stroke={hoveredRoom === room.id ? "#4f46e5" : "#9ca3af"}
            strokeWidth={hoveredRoom === room.id ? 3 : 2}
            strokeDasharray={hoveredRoom === room.id ? "none" : "5,5"}
          />
        );
      }
      
      // Opposite room connection
      const oppositePos = findNodePosition(room.opposite_room);
      if (oppositePos) {
        lines.push(
          <line 
            key={`${room.id}-opposite`}
            x1={x * 120 + 60}
            y1={y * 120 + 60}
            x2={oppositePos.x * 120 + 60}
            y2={oppositePos.y * 120 + 60}
            stroke={hoveredRoom === room.id ? "#4f46e5" : "#9ca3af"}
            strokeWidth={hoveredRoom === room.id ? 3 : 2}
            strokeDasharray={hoveredRoom === room.id ? "none" : "5,5"}
          />
        );
      }
      
      // Connected room connection
      const connectedPos = findNodePosition(room.connected_room);
      if (connectedPos) {
        lines.push(
          <line 
            key={`${room.id}-connected`}
            x1={x * 120 + 60}
            y1={y * 120 + 60}
            x2={connectedPos.x * 120 + 60}
            y2={connectedPos.y * 120 + 60}
            stroke={hoveredRoom === room.id ? "#4f46e5" : "#d946ef"}
            strokeWidth={hoveredRoom === room.id ? 3 : 2}
          />
        );
      }
    });
    
    return lines;
  };

  return (
    <Container 
      className={`p-6 border border-gray-200 rounded-lg bg-white shadow-sm ${className} ${isFullscreen ? 'fixed inset-0 z-50 m-0 rounded-none overflow-auto' : ''}`}
    >
      <Toaster />
      <div className="flex justify-between items-center mb-4">
        <div>
          <Heading level="h2" className="text-xl flex items-center gap-2">
            <Hotel className="w-5 h-5" />
            Floor Plan Visualization
          </Heading>
          <Text className="text-gray-500">
            {selectedFloor ? `Floor ${selectedFloor}` : 'Select a floor'}
          </Text>
        </div>
        <div className="flex items-center gap-2">
          <div className="flex border border-gray-200 rounded-md overflow-hidden">
            {floors.map(floor => (
              <button
                key={floor}
                className={`px-3 py-1.5 text-sm font-medium ${selectedFloor === floor ? 'bg-indigo-600 text-white' : 'bg-white text-gray-700 hover:bg-gray-50'}`}
                onClick={() => setSelectedFloor(floor)}
              >
                {floor}
              </button>
            ))}
          </div>
          <Button
            variant="secondary"
            size="small"
            onClick={toggleFullscreen}
          >
            {isFullscreen ? <Minimize className="w-4 h-4" /> : <Maximize className="w-4 h-4" />}
          </Button>
        </div>
      </div>

      {floorRooms.length === 0 ? (
        <div className="bg-gray-50 p-8 rounded-lg text-center">
          <Text className="text-gray-500">No rooms found for this floor</Text>
        </div>
      ) : (
        <div className="relative overflow-auto border border-gray-200 rounded-lg bg-gray-50 p-4">
          <div className="flex items-center gap-2 mb-4">
            <div className="flex items-center gap-1">
              <div className="w-3 h-3 bg-green-100 border border-green-300 rounded-full"></div>
              <Text className="text-xs">Available</Text>
            </div>
            <div className="flex items-center gap-1">
              <div className="w-3 h-3 bg-blue-100 border border-blue-300 rounded-full"></div>
              <Text className="text-xs">Occupied</Text>
            </div>
            <div className="flex items-center gap-1">
              <div className="w-3 h-3 bg-orange-100 border border-orange-300 rounded-full"></div>
              <Text className="text-xs">Maintenance</Text>
            </div>
            <div className="flex items-center gap-1">
              <div className="w-3 h-3 bg-yellow-100 border border-yellow-300 rounded-full"></div>
              <Text className="text-xs">Cleaning</Text>
            </div>
            <div className="flex items-center gap-1 ml-4">
              <div className="w-4 h-0.5 bg-d946ef"></div>
              <Text className="text-xs">Connected</Text>
            </div>
            <div className="flex items-center gap-1">
              <div className="w-4 h-0.5 bg-gray-400 border-dashed border-gray-400"></div>
              <Text className="text-xs">Adjacent</Text>
            </div>
          </div>

          <div 
            className="relative"
            style={{
              width: `${gridSize.width * 120}px`,
              height: `${gridSize.height * 120}px`,
              minWidth: '600px',
              minHeight: '600px'
            }}
          >
            {/* Connection lines */}
            <svg 
              className="absolute inset-0 w-full h-full pointer-events-none"
              style={{ zIndex: 1 }}
            >
              {renderConnectionLines()}
            </svg>

            {/* Room nodes */}
            {roomNodes.map(node => (
              <div
                key={node.room.id}
                className={`absolute rounded-lg border-2 ${getStatusColor(node.room.status)} p-3 w-100 h-100 shadow-sm transition-all duration-200 cursor-pointer ${hoveredRoom === node.room.id ? 'ring-2 ring-indigo-500 shadow-md scale-105 z-10' : ''}`}
                style={{
                  width: '100px',
                  height: '100px',
                  left: `${node.x * 120 + 10}px`,
                  top: `${node.y * 120 + 10}px`,
                }}
                onClick={() => onRoomClick && onRoomClick(node.room.id)}
                onMouseEnter={() => setHoveredRoom(node.room.id)}
                onMouseLeave={() => setHoveredRoom(null)}
              >
                <div className="flex justify-between items-start">
                  <Text className="font-semibold">{node.room.room_number}</Text>
                  {getStatusIcon(node.room.status)}
                </div>
                <Text className="text-xs truncate">{node.room.name}</Text>
                
                {/* Relationship indicators */}
                <div className="absolute inset-0 pointer-events-none">
                  {node.room.left_room && (
                    <div className="absolute left-0 top-1/2 transform -translate-y-1/2 -translate-x-1/2">
                      <ArrowLeft className="w-4 h-4 text-gray-500" />
                    </div>
                  )}
                  {node.room.right_room && (
                    <div className="absolute right-0 top-1/2 transform -translate-y-1/2 translate-x-1/2">
                      <ArrowRight className="w-4 h-4 text-gray-500" />
                    </div>
                  )}
                  {node.room.opposite_room && (
                    <div className="absolute top-0 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
                      <ArrowUp className="w-4 h-4 text-gray-500" />
                    </div>
                  )}
                  {node.room.connected_room && (
                    <div className="absolute bottom-0 right-0 p-1">
                      <LinkIcon className="w-3 h-3 text-purple-500" />
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </Container>
  );
};

export default FloorPlanVisualization;
