import React, { useState, useEffect } from "react";
import { Button, Text } from "@camped-ai/ui";
import { Upload, Image } from "lucide-react";

// Simulates a heavy media upload component
const MediaUploadComponent = () => {
  const [images, setImages] = useState<string[]>([]);
  const [loading, setLoading] = useState(true);

  // Simulate heavy media processing initialization
  useEffect(() => {
    const timer = setTimeout(() => {
      setLoading(false);
    }, 800);

    return () => clearTimeout(timer);
  }, []);

  const handleFileUpload = () => {
    // Simulate file upload
    const newImage = `image-${Date.now()}.jpg`;
    setImages(prev => [...prev, newImage]);
  };

  if (loading) {
    return (
      <div className="space-y-4">
        <Text>Initializing media upload system...</Text>
        <div className="animate-pulse">
          <div className="h-32 bg-gray-200 rounded"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <Text className="text-sm text-muted-foreground">
        This component simulates heavy media processing that would impact performance
        if loaded immediately.
      </Text>
      
      <div className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center">
        <Upload className="mx-auto h-12 w-12 text-gray-400 mb-4" />
        <Text className="text-gray-600 mb-4">
          Drag and drop images here, or click to select
        </Text>
        <Button onClick={handleFileUpload} className="flex items-center gap-2">
          <Upload size={16} />
          Upload Images
        </Button>
      </div>

      {images.length > 0 && (
        <div className="space-y-2">
          <Text className="font-medium">Uploaded Images:</Text>
          <div className="grid grid-cols-3 gap-2">
            {images.map((image, index) => (
              <div key={index} className="flex items-center gap-2 p-2 bg-gray-50 rounded">
                <Image size={16} className="text-gray-500" />
                <Text className="text-sm truncate">{image}</Text>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default MediaUploadComponent;
