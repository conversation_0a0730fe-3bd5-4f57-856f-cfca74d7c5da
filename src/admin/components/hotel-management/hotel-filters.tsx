import React from "react";
import {
  <PERSON><PERSON>,
  Dropdown<PERSON>enu,
  Badge,
  Text,
  Container,
} from "@camped-ai/ui";
import {
  Filter,
  MapPin,
  Star,
  Sparkles,
  X,
} from "lucide-react";

interface Destination {
  id: string;
  name: string;
}

interface HotelFiltersProps {
  // Filter values
  filterDestination?: string;
  filterStars: number[];
  filterFeatured: boolean | null;
  filterStatus: boolean | null;

  // Data
  destinations: Destination[];
  totalCount: number;

  // Callbacks
  onDestinationChange: (value: string | undefined) => void;
  onStarsChange: (stars: number[]) => void;
  onFeaturedChange: (featured: boolean | null) => void;
  onStatusChange: (status: boolean | null) => void;
  onClearAll: () => void;

  // UI state
  className?: string;
}

export const HotelFilters: React.FC<HotelFiltersProps> = ({
  filterDestination,
  filterStars,
  filterFeatured,
  filterStatus,
  destinations,
  totalCount,
  onDestinationChange,
  onStarsChange,
  onFeaturedChange,
  onStatusChange,
  onClearAll,
  className = "",
}) => {
  const toggleStarFilter = (star: number) => {
    if (filterStars.includes(star)) {
      onStarsChange(filterStars.filter((s) => s !== star));
    } else {
      onStarsChange([...filterStars, star]);
    }
  };

  const hasActiveFilters = filterDestination || filterStars.length > 0 || filterFeatured !== null || filterStatus !== null;

  return (
    <Container className={`border rounded-lg p-4 space-y-4 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <Filter className="h-4 w-4 text-ui-fg-muted" />
          <Text className="font-medium">Filter Hotels</Text>
        </div>
        {hasActiveFilters && (
          <Button
            variant="secondary"
            size="small"
            onClick={onClearAll}
          >
            <X className="h-4 w-4 mr-1" />
            Clear All
          </Button>
        )}
      </div>

      {/* Filter Controls */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        {/* Destination Filter */}
        <div className="space-y-2">
          <div className="flex items-center gap-2">
            <MapPin className="h-4 w-4 text-ui-fg-muted" />
            <Text className="text-sm font-medium">Destination</Text>
          </div>
          <DropdownMenu>
            <DropdownMenu.Trigger asChild>
              <Button variant="secondary" size="small" className="w-full justify-between">
                {filterDestination 
                  ? destinations.find(d => d.id === filterDestination)?.name || "Unknown"
                  : "All Destinations"
                }
                <Filter className="h-4 w-4" />
              </Button>
            </DropdownMenu.Trigger>
            <DropdownMenu.Content className="w-56">
              <DropdownMenu.Item
                onClick={() => onDestinationChange(undefined)}
                className={!filterDestination ? "bg-ui-bg-subtle" : ""}
              >
                All Destinations
              </DropdownMenu.Item>
              <DropdownMenu.Separator />
              {destinations.map((destination) => (
                <DropdownMenu.Item
                  key={destination.id}
                  onClick={() => onDestinationChange(destination.id)}
                  className={filterDestination === destination.id ? "bg-ui-bg-subtle" : ""}
                >
                  {destination.name}
                </DropdownMenu.Item>
              ))}
            </DropdownMenu.Content>
          </DropdownMenu>
        </div>

        {/* Star Rating Filter */}
        <div className="space-y-2">
          <div className="flex items-center gap-2">
            <Star className="h-4 w-4 text-ui-fg-muted" />
            <Text className="text-sm font-medium">Star Rating</Text>
          </div>
          <DropdownMenu>
            <DropdownMenu.Trigger asChild>
              <Button variant="secondary" size="small" className="w-full justify-between">
                {filterStars.length > 0 
                  ? `${filterStars.length} selected`
                  : "All Ratings"
                }
                <Filter className="h-4 w-4" />
              </Button>
            </DropdownMenu.Trigger>
            <DropdownMenu.Content className="w-56">
              <DropdownMenu.Item
                onClick={() => onStarsChange([])}
                className={filterStars.length === 0 ? "bg-ui-bg-subtle" : ""}
              >
                All Ratings
              </DropdownMenu.Item>
              <DropdownMenu.Separator />
              {[1, 2, 3, 4, 5].map((star) => (
                <DropdownMenu.Item
                  key={star}
                  onSelect={(e) => e.preventDefault()}
                  className="flex items-center gap-2"
                >
                  <input
                    type="checkbox"
                    checked={filterStars.includes(star)}
                    onChange={() => toggleStarFilter(star)}
                    className="rounded border-ui-border-base"
                  />
                  <div className="flex items-center gap-1">
                    <span>{star}</span>
                    <Star className="h-3 w-3 fill-current text-yellow-400" />
                  </div>
                </DropdownMenu.Item>
              ))}
            </DropdownMenu.Content>
          </DropdownMenu>
        </div>

        {/* Featured Status Filter */}
        <div className="space-y-2">
          <div className="flex items-center gap-2">
            <Sparkles className="h-4 w-4 text-ui-fg-muted" />
            <Text className="text-sm font-medium">Featured Status</Text>
          </div>
          <DropdownMenu>
            <DropdownMenu.Trigger asChild>
              <Button variant="secondary" size="small" className="w-full justify-between">
                {filterFeatured === true 
                  ? "Featured Only"
                  : filterFeatured === false 
                    ? "Not Featured"
                    : "All Hotels"
                }
                <Filter className="h-4 w-4" />
              </Button>
            </DropdownMenu.Trigger>
            <DropdownMenu.Content className="w-56">
              <DropdownMenu.Item
                onClick={() => onFeaturedChange(null)}
                className={filterFeatured === null ? "bg-ui-bg-subtle" : ""}
              >
                All Hotels
              </DropdownMenu.Item>
              <DropdownMenu.Separator />
              <DropdownMenu.Item
                onClick={() => onFeaturedChange(true)}
                className={filterFeatured === true ? "bg-ui-bg-subtle" : ""}
              >
                <Sparkles className="h-4 w-4 mr-2" />
                Featured Only
              </DropdownMenu.Item>
              <DropdownMenu.Item
                onClick={() => onFeaturedChange(false)}
                className={filterFeatured === false ? "bg-ui-bg-subtle" : ""}
              >
                Not Featured
              </DropdownMenu.Item>
            </DropdownMenu.Content>
          </DropdownMenu>
        </div>

        {/* Status Filter */}
        <div className="space-y-2">
          <div className="flex items-center gap-2">
            <Filter className="h-4 w-4 text-ui-fg-muted" />
            <Text className="text-sm font-medium">Status</Text>
          </div>
          <DropdownMenu>
            <DropdownMenu.Trigger asChild>
              <Button variant="secondary" size="small" className="w-full justify-between">
                {filterStatus === true ? "Active" :
                 filterStatus === false ? "Inactive" : "All Status"}
                <Filter className="h-4 w-4" />
              </Button>
            </DropdownMenu.Trigger>
            <DropdownMenu.Content className="w-56">
              <DropdownMenu.Item
                onClick={() => onStatusChange(null)}
                className={filterStatus === null ? "bg-ui-bg-subtle" : ""}
              >
                All Status
              </DropdownMenu.Item>
              <DropdownMenu.Separator />
              <DropdownMenu.Item
                onClick={() => onStatusChange(true)}
                className={filterStatus === true ? "bg-ui-bg-subtle" : ""}
              >
                <Filter className="h-4 w-4 mr-2 text-green-500" />
                Active
              </DropdownMenu.Item>
              <DropdownMenu.Item
                onClick={() => onStatusChange(false)}
                className={filterStatus === false ? "bg-ui-bg-subtle" : ""}
              >
                <Filter className="h-4 w-4 mr-2 text-red-500" />
                Inactive
              </DropdownMenu.Item>
            </DropdownMenu.Content>
          </DropdownMenu>
        </div>
      </div>

      {/* Active Filters Summary */}
      {hasActiveFilters && (
        <div className="flex flex-wrap items-center gap-2 pt-2 border-t border-ui-border-base">
          <Text className="text-xs text-ui-fg-muted">Active filters:</Text>
          
          {filterDestination && (
            <Badge size="small" className="flex items-center gap-1">
              <MapPin className="h-3 w-3" />
              {destinations.find(d => d.id === filterDestination)?.name}
              <button
                onClick={() => onDestinationChange(undefined)}
                className="ml-1 hover:text-ui-fg-error"
              >
                <X className="h-3 w-3" />
              </button>
            </Badge>
          )}
          
          {filterStars.length > 0 && (
            <Badge size="small" className="flex items-center gap-1">
              <Star className="h-3 w-3" />
              {filterStars.sort().join(", ")} star{filterStars.length > 1 ? "s" : ""}
              <button
                onClick={() => onStarsChange([])}
                className="ml-1 hover:text-ui-fg-error"
              >
                <X className="h-3 w-3" />
              </button>
            </Badge>
          )}
          
          {filterFeatured !== null && (
            <Badge size="small" className="flex items-center gap-1">
              <Sparkles className="h-3 w-3" />
              {filterFeatured ? "Featured" : "Not Featured"}
              <button
                onClick={() => onFeaturedChange(null)}
                className="ml-1 hover:text-ui-fg-error"
              >
                <X className="h-3 w-3" />
              </button>
            </Badge>
          )}

          {filterStatus !== null && (
            <Badge size="small" className="flex items-center gap-1">
              <Filter className="h-3 w-3" />
              {filterStatus ? "Active" : "Inactive"}
              <button
                onClick={() => onStatusChange(null)}
                className="ml-1 hover:text-ui-fg-error"
              >
                <X className="h-3 w-3" />
              </button>
            </Badge>
          )}
        </div>
      )}

      {/* Results Count */}
      {hasActiveFilters && (
        <div className="pt-2 border-t border-ui-border-base">
          <Text className="text-sm text-ui-fg-muted">
            <span className="font-medium">{totalCount}</span> hotels match your filters
          </Text>
        </div>
      )}
    </Container>
  );
};
