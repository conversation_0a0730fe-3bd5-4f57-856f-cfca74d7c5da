import React from "react";
import { <PERSON><PERSON>, <PERSON><PERSON>, Text } from "@camped-ai/ui";
import { MapPin, Settings } from "lucide-react";
import { Rating } from "@mui/material";
import OptimizedImage from "../shared/OptimizedImage";
import ImageSkeleton from "../shared/ImageSkeleton";

interface Hotel {
  id: string;
  name: string;
  destination_name?: string;
  location?: string;
  description?: string;
  image_url?: string;
  star?: number;
  is_internal?: boolean;
  is_featured?: boolean;
  status?: string;
}

interface HotelListItemProps {
  hotel: Hotel;
  onClick: () => void;
  priority?: boolean;
}

const HotelListItem: React.FC<HotelListItemProps> = ({
  hotel,
  onClick,
  priority = false,
}) => {
  const getStatusBadge = (status?: string) => {
    if (!status) return null;

    const statusConfig = {
      active: { label: "Active", color: "green" as const },
      inactive: { label: "Inactive", color: "grey" as const },
      featured: { label: "Featured", color: "purple" as const },
    };

    const config =
      statusConfig[status.toLowerCase() as keyof typeof statusConfig];
    if (!config) return null;

    return (
      <Badge color={config.color} className="text-xs">
        {config.label}
      </Badge>
    );
  };

  // Show loading skeleton if there's an image URL, otherwise show gradient
  const customPlaceholder = hotel.image_url ? (
    <ImageSkeleton className="absolute inset-0" variant="list" />
  ) : (
    <div className="absolute inset-0 bg-gradient-to-br from-blue-400 via-purple-500 to-pink-500" />
  );

  return (
    <div
      className="bg-card rounded-lg border shadow-sm hover:shadow-md transition-all duration-200 cursor-pointer group p-4"
      onClick={onClick}
    >
      <div className="flex flex-col md:flex-row gap-4">
        {/* Image */}
        <div className="w-full md:w-64 lg:w-80 h-40 md:h-52 bg-muted rounded-lg overflow-hidden flex-shrink-0 relative">
          <OptimizedImage
            src={hotel.image_url}
            alt={hotel.name}
            className="w-full h-full"
            placeholder={customPlaceholder}
            priority={priority}
          />

          {/* Rating overlay on image */}
          <div className="absolute top-3 right-3">
            {hotel.star && hotel.star > 0 ? (
              <div className="flex items-center gap-1 bg-black/60 backdrop-blur-sm rounded-md px-2 py-1">
                <Rating
                  value={hotel.star}
                  readOnly
                  size="small"
                  sx={{
                    "& .MuiRating-iconFilled": {
                      color: "#facc15", // yellow-400
                    },
                    "& .MuiRating-iconEmpty": {
                      color: "#d1d5db", // gray-300
                    },
                  }}
                />
                <span className="text-xs text-white font-medium">
                  {hotel.star}
                </span>
              </div>
            ) : (
              <div className="bg-black/60 backdrop-blur-sm rounded-md px-2 py-1">
                <Text className="text-xs text-white">No Rating</Text>
              </div>
            )}
          </div>

          {/* Hover overlay */}
          <div className="absolute inset-0 bg-black/20 opacity-0 group-hover:opacity-100 transition-opacity duration-200" />
        </div>

        {/* Content */}
        <div className="flex-1 flex flex-col justify-between min-w-0">
          {/* Header */}
          <div className="space-y-3">
            {/* Title and badges */}
            <div className="flex flex-col sm:flex-row sm:items-start sm:justify-between gap-2">
              <h3 className="font-semibold text-xl text-foreground group-hover:text-primary transition-colors duration-200 line-clamp-2">
                {hotel.name}
              </h3>

              <div className="flex flex-wrap gap-2 flex-shrink-0">
                {hotel.is_featured && (
                  <Badge color="purple" className="text-xs">
                    Featured
                  </Badge>
                )}
                {hotel.is_internal && (
                  <Badge color="grey" className="text-xs">
                    Internal
                  </Badge>
                )}
                {getStatusBadge(hotel.status)}
              </div>
            </div>

            {/* Description */}
            {hotel.description && (
              <div className="text-sm text-muted-foreground line-clamp-2">
                {hotel.description}
              </div>
            )}

            {/* Location and Destination */}
            <div className="text-sm text-muted-foreground space-y-1">
              {hotel.location && (
                <div className="flex items-center gap-2">
                  <MapPin className="w-4 h-4 flex-shrink-0" />
                  <span className="line-clamp-1">{hotel.location}</span>
                </div>
              )}
              {hotel.destination_name && (
                <div className="flex items-center gap-2">
                  <span className="w-4 h-4 flex-shrink-0 text-center">📍</span>
                  <span className="line-clamp-1">{hotel.destination_name}</span>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default HotelListItem;
