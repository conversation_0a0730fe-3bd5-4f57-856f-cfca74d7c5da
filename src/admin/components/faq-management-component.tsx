import React, { useState, useEffect } from "react";
import { Button, Input, Label, Text } from "@camped-ai/ui";
import { Plus, Trash2 } from "lucide-react";

interface FAQ {
  id: number;
  question: string;
  answer: string;
}

// Simulates a heavy FAQ management component with complex state
const FAQManagementComponent = () => {
  const [faqs, setFaqs] = useState<FAQ[]>([]);
  const [loading, setLoading] = useState(true);

  // Simulate heavy FAQ data processing
  useEffect(() => {
    const timer = setTimeout(() => {
      setFaqs([
        { id: 1, question: "Sample Question 1", answer: "Sample Answer 1" },
        { id: 2, question: "Sample Question 2", answer: "Sample Answer 2" },
      ]);
      setLoading(false);
    }, 1200);

    return () => clearTimeout(timer);
  }, []);

  const addFAQ = () => {
    const newFAQ: FAQ = {
      id: Date.now(),
      question: "",
      answer: "",
    };
    setFaqs(prev => [...prev, newFAQ]);
  };

  const updateFAQ = (id: number, field: keyof FAQ, value: string) => {
    setFaqs(prev => prev.map(faq => 
      faq.id === id ? { ...faq, [field]: value } : faq
    ));
  };

  const removeFAQ = (id: number) => {
    setFaqs(prev => prev.filter(faq => faq.id !== id));
  };

  if (loading) {
    return (
      <div className="space-y-4">
        <Text>Loading FAQ management system...</Text>
        <div className="animate-pulse space-y-4">
          {[1, 2].map((i) => (
            <div key={i} className="space-y-2">
              <div className="h-4 bg-gray-200 rounded w-3/4"></div>
              <div className="h-20 bg-gray-200 rounded"></div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <Text className="text-sm text-muted-foreground">
          This component simulates complex FAQ management with translation support
          that would slow down initial rendering.
        </Text>
        <Button onClick={addFAQ} size="small" className="flex items-center gap-2">
          <Plus size={16} />
          Add FAQ
        </Button>
      </div>

      {faqs.length === 0 ? (
        <div className="text-center py-8 text-muted-foreground bg-muted rounded-lg">
          <Text>No FAQs added yet. Click "Add FAQ" to get started.</Text>
        </div>
      ) : (
        <div className="space-y-4">
          {faqs.map((faq, index) => (
            <div key={faq.id} className="p-4 border border-border rounded-lg space-y-3">
              <div className="flex justify-between items-start">
                <Text className="text-sm font-medium">FAQ {index + 1}</Text>
                <Button
                  variant="transparent"
                  size="small"
                  onClick={() => removeFAQ(faq.id)}
                  className="text-destructive hover:text-destructive/80"
                >
                  <Trash2 size={16} />
                </Button>
              </div>
              
              <div>
                <Label className="text-xs text-muted-foreground font-medium">
                  Question
                </Label>
                <Input
                  value={faq.question}
                  onChange={(e) => updateFAQ(faq.id, "question", e.target.value)}
                  placeholder="Enter question"
                  className="mt-1"
                />
              </div>
              
              <div>
                <Label className="text-xs text-muted-foreground font-medium">
                  Answer
                </Label>
                <Input
                  value={faq.answer}
                  onChange={(e) => updateFAQ(faq.id, "answer", e.target.value)}
                  placeholder="Enter answer"
                  className="mt-1"
                />
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default FAQManagementComponent;
