import React from "react";
import {
  Table,
  Checkbox,
  Text,
  Heading,
  Tooltip,
} from "@camped-ai/ui";
import { Info } from "lucide-react";
import { ScreenPermission } from "../../../modules/rbac/types";
// Removed unused imports

// Define the permission types for the table columns
export enum PermissionType {
  VIEW = "VIEW",
  CREATE = "CREATE", 
  EDIT = "EDIT",
  DELETE = "DELETE",
  BULK_OPERATIONS = "BULK_OPERATIONS",
}

// Module configuration for the permission matrix
interface ModuleConfig {
  id: string;
  name: string;
  description: string;
  permissions: {
    [PermissionType.VIEW]?: ScreenPermission;
    [PermissionType.CREATE]?: ScreenPermission;
    [PermissionType.EDIT]?: ScreenPermission;
    [PermissionType.DELETE]?: ScreenPermission;
    [PermissionType.BULK_OPERATIONS]?: ScreenPermission;
  };
  restrictions?: {
    [key in PermissionType]?: boolean; // true = disabled/restricted
  };
}

// Define the module permission matrix according to requirements (11 modules)
const MODULE_PERMISSION_MATRIX: ModuleConfig[] = [
  {
    id: "destination_management",
    name: "Destination Management",
    description: "Manage travel destinations and location data",
    permissions: {
      [PermissionType.VIEW]: ScreenPermission.DESTINATIONS_VIEW,
      [PermissionType.CREATE]: ScreenPermission.DESTINATIONS_CREATE,
      [PermissionType.EDIT]: ScreenPermission.DESTINATIONS_EDIT,
      [PermissionType.DELETE]: ScreenPermission.DESTINATIONS_DELETE,
      [PermissionType.BULK_OPERATIONS]: ScreenPermission.DESTINATIONS_BULK_OPERATIONS,
    },
  },
  {
    id: "hotel_management",
    name: "Hotel Management",
    description: "VIEW: hotel listings and cancellation policies; CREATE: new hotel properties; EDIT: hotel information, settings, and cancellation policies; DELETE: hotel properties; BULK OPS: bulk operations on hotels",
    permissions: {
      [PermissionType.VIEW]: ScreenPermission.HOTEL_MANAGEMENT_VIEW, // View hotel listings and cancellation policies
      [PermissionType.CREATE]: ScreenPermission.HOTEL_MANAGEMENT_CREATE, // Create new hotel properties
      [PermissionType.EDIT]: ScreenPermission.HOTEL_MANAGEMENT_EDIT, // Modify hotel information, settings, and cancellation policies
      [PermissionType.DELETE]: ScreenPermission.HOTEL_MANAGEMENT_DELETE, // Remove hotel properties
      [PermissionType.BULK_OPERATIONS]: ScreenPermission.HOTEL_MANAGEMENT_BULK_OPERATIONS, // Perform bulk operations on hotels
    },
  },
  {
    id: "room_management",
    name: "Room Management",
    description: "VIEW: room listings, configurations, and availability; CREATE: new room configurations; EDIT: room configurations and availability; DELETE: room configurations; BULK OPS: bulk import room configurations and availability",
    permissions: {
      [PermissionType.VIEW]: ScreenPermission.ROOMS_VIEW, // View room listings, configurations, and availability
      [PermissionType.CREATE]: ScreenPermission.ROOMS_CREATE, // Create new room configurations
      [PermissionType.EDIT]: ScreenPermission.ROOMS_EDIT, // Modify room configurations and availability
      [PermissionType.DELETE]: ScreenPermission.ROOMS_DELETE, // Remove room configurations
      [PermissionType.BULK_OPERATIONS]: ScreenPermission.ROOMS_BULK_OPERATIONS, // Bulk import room configurations and bulk availability imports
    },
  },
  {
    id: "room_price_management",
    name: "Room Price Management",
    description: "View room rates and pricing configurations (view-only access)",
    permissions: {
      [PermissionType.VIEW]: ScreenPermission.PRICING_VIEW,
    },
    restrictions: {
      [PermissionType.CREATE]: true,
      [PermissionType.EDIT]: true,
      [PermissionType.DELETE]: true,
      [PermissionType.BULK_OPERATIONS]: true,
    },
  },
  {
    id: "booking_management",
    name: "Booking Management",
    description: "Manage guest bookings, reservations, and cancellations",
    permissions: {
      [PermissionType.VIEW]: ScreenPermission.BOOKINGS_VIEW,
      [PermissionType.CREATE]: ScreenPermission.BOOKINGS_CREATE,
      [PermissionType.EDIT]: ScreenPermission.BOOKINGS_EDIT, // Includes cancel functionality
      [PermissionType.DELETE]: ScreenPermission.BOOKINGS_DELETE,
      [PermissionType.BULK_OPERATIONS]: ScreenPermission.BOOKINGS_BULK_OPERATIONS,
    },
  },
  {
    id: "supplier_management",
    name: "Supplier Management",
    description: "Manage all supplier operations: suppliers, products & services, offerings, categories, unit types, and exchange rates",
    permissions: {
      [PermissionType.VIEW]: ScreenPermission.SUPPLIER_MANAGEMENT_VIEW,
      [PermissionType.CREATE]: ScreenPermission.SUPPLIER_MANAGEMENT_CREATE,
      [PermissionType.EDIT]: ScreenPermission.SUPPLIER_MANAGEMENT_EDIT,
      [PermissionType.DELETE]: ScreenPermission.SUPPLIER_MANAGEMENT_DELETE,
      [PermissionType.BULK_OPERATIONS]: ScreenPermission.SUPPLIER_MANAGEMENT_BULK_OPERATIONS,
    },
  },
  {
    id: "exchange_rate_management",
    name: "Exchange Rate Management",
    description: "Manage currency exchange rates for international transactions",
    permissions: {
      [PermissionType.VIEW]: ScreenPermission.EXCHANGE_RATE_MANAGEMENT_VIEW,
      [PermissionType.CREATE]: ScreenPermission.EXCHANGE_RATE_MANAGEMENT_CREATE,
      [PermissionType.EDIT]: ScreenPermission.EXCHANGE_RATE_MANAGEMENT_EDIT,
      [PermissionType.DELETE]: ScreenPermission.EXCHANGE_RATE_MANAGEMENT_DELETE,
      [PermissionType.BULK_OPERATIONS]: ScreenPermission.EXCHANGE_RATE_MANAGEMENT_BULK_OPERATIONS,
    },
  },
  {
    id: "supplier_cost_management",
    name: "Supplier Cost Management",
    description: "Manage supplier pricing, cost structures, and financial agreements",
    permissions: {
      [PermissionType.VIEW]: ScreenPermission.SUPPLIER_COST_VIEW,
      [PermissionType.CREATE]: ScreenPermission.SUPPLIER_COST_CREATE,
      [PermissionType.EDIT]: ScreenPermission.SUPPLIER_COST_EDIT,
      [PermissionType.DELETE]: ScreenPermission.SUPPLIER_COST_DELETE,
      [PermissionType.BULK_OPERATIONS]: ScreenPermission.SUPPLIER_COST_BULK_OPERATIONS,
    },
  },
  {
    id: "supplier_order_management",
    name: "Supplier Order Management",
    description: "Manage supplier orders, purchase requests, order fulfillment, and on-request items",
    permissions: {
      [PermissionType.VIEW]: ScreenPermission.SUPPLIER_ORDERS_VIEW, // Covers: Orders + On Request viewing
      [PermissionType.CREATE]: ScreenPermission.SUPPLIER_ORDERS_CREATE, // Covers: Create orders within "on request" context
      [PermissionType.EDIT]: ScreenPermission.SUPPLIER_ORDERS_EDIT, // Covers: Edit existing orders only
      // No DELETE or BULK_OPERATIONS permissions per requirements
    },
    restrictions: {
      [PermissionType.DELETE]: true, // No delete functionality for Supplier Order Management
      [PermissionType.BULK_OPERATIONS]: true, // No bulk operations for Supplier Order Management
    },
  },
  {
    id: "concierge_management",
    name: "Concierge Management",
    description: "Manage concierge operations, tasks, itineraries, and configuration",
    permissions: {
      [PermissionType.VIEW]: ScreenPermission.CONCIERGE_MANAGEMENT_VIEW,
      [PermissionType.CREATE]: ScreenPermission.CONCIERGE_MANAGEMENT_CREATE,
      [PermissionType.EDIT]: ScreenPermission.CONCIERGE_MANAGEMENT_EDIT,
      [PermissionType.DELETE]: ScreenPermission.CONCIERGE_MANAGEMENT_DELETE,
      [PermissionType.BULK_OPERATIONS]: ScreenPermission.CONCIERGE_MANAGEMENT_BULK_OPERATIONS,
    },
  },
  {
    id: "user_management",
    name: "User Management",
    description: "Manage user accounts, profiles, and access control",
    permissions: {
      [PermissionType.VIEW]: ScreenPermission.USER_MANAGEMENT_VIEW,
      [PermissionType.CREATE]: ScreenPermission.USER_MANAGEMENT_CREATE,
      [PermissionType.EDIT]: ScreenPermission.USER_MANAGEMENT_EDIT,
    },
    restrictions: {
      [PermissionType.DELETE]: true, // No delete for User Management
      [PermissionType.BULK_OPERATIONS]: true, // No bulk operations for User Management
    },
  },
  {
    id: "role_management",
    name: "Role Management",
    description: "Manage roles, permissions, and access control policies",
    permissions: {
      [PermissionType.VIEW]: ScreenPermission.ROLE_MANAGEMENT_VIEW,
      [PermissionType.CREATE]: ScreenPermission.ROLE_MANAGEMENT_CREATE,
      [PermissionType.EDIT]: ScreenPermission.ROLE_MANAGEMENT_EDIT,
      [PermissionType.DELETE]: ScreenPermission.ROLE_MANAGEMENT_DELETE,
    },
    restrictions: {
      [PermissionType.BULK_OPERATIONS]: true, // No bulk operations for Role Management
    },
  },
  {
    id: "reports",
    name: "Reports",
    description: "View analytics, generate reports, and access business intelligence data",
    permissions: {
      [PermissionType.VIEW]: ScreenPermission.REPORTS_VIEW,
      [PermissionType.CREATE]: ScreenPermission.REPORTS_CREATE,
      [PermissionType.EDIT]: ScreenPermission.REPORTS_EDIT,
      [PermissionType.DELETE]: ScreenPermission.REPORTS_DELETE,
      [PermissionType.BULK_OPERATIONS]: ScreenPermission.REPORTS_BULK_OPERATIONS,
    },
  },
];

interface PermissionTableProps {
  selectedPermissions: ScreenPermission[];
  onPermissionToggle: (permission: ScreenPermission) => void;
  disabled?: boolean;
  loading?: boolean;
}

export const PermissionTable: React.FC<PermissionTableProps> = ({
  selectedPermissions,
  onPermissionToggle,
  disabled = false,
  loading = false,
}) => {
  const permissionTypes = [
    PermissionType.VIEW,
    PermissionType.CREATE,
    PermissionType.EDIT,
    PermissionType.DELETE,
    PermissionType.BULK_OPERATIONS,
  ];

  const isPermissionSelected = (permission?: ScreenPermission): boolean => {
    return permission ? selectedPermissions.includes(permission) : false;
  };

  const isPermissionRestricted = (
    module: ModuleConfig,
    permissionType: PermissionType
  ): boolean => {
    return module.restrictions?.[permissionType] === true;
  };

  const handlePermissionChange = (permission?: ScreenPermission) => {
    if (permission && !disabled && !loading) {
      onPermissionToggle(permission);
    }
  };

  // Helper function to get module-level permission counts
  const getModulePermissionCounts = (module: ModuleConfig) => {
    const availablePermissions = Object.values(module.permissions).filter(Boolean);
    const selectedCount = availablePermissions.filter(permission =>
      permission && selectedPermissions.includes(permission)
    ).length;
    const totalCount = availablePermissions.length;

    return { selectedCount, totalCount };
  };

  // Helper function to determine if a module has all permissions selected
  const isModuleFullySelected = (module: ModuleConfig): boolean => {
    const { selectedCount, totalCount } = getModulePermissionCounts(module);
    return selectedCount === totalCount && totalCount > 0;
  };

  // Helper function to determine if a module has some permissions selected
  const isModulePartiallySelected = (module: ModuleConfig): boolean => {
    const { selectedCount, totalCount } = getModulePermissionCounts(module);
    return selectedCount > 0 && selectedCount < totalCount;
  };

  // Helper function to handle module-level selection
  const handleModuleToggle = (module: ModuleConfig) => {
    if (disabled || loading) return;

    const isFullySelected = isModuleFullySelected(module);
    const availablePermissions = Object.values(module.permissions).filter(Boolean) as ScreenPermission[];

    if (isFullySelected) {
      // Deselect all permissions in this module
      availablePermissions.forEach(permission => {
        if (selectedPermissions.includes(permission)) {
          onPermissionToggle(permission);
        }
      });
    } else {
      // Select all available permissions in this module
      availablePermissions.forEach(permission => {
        if (!selectedPermissions.includes(permission)) {
          onPermissionToggle(permission);
        }
      });
    }
  };

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <Heading level="h3" className="text-lg font-semibold">
          Module Permissions
        </Heading>
      </div>

      {/* Desktop Table */}
      <div className="border border-gray-200 rounded-lg overflow-hidden">
        <Table>
          <Table.Header>
            <Table.Row className="bg-gray-50">
              <Table.HeaderCell className="font-semibold text-left w-1/3">
                Module Name
              </Table.HeaderCell>
              {permissionTypes.map((type) => (
                <Table.HeaderCell
                  key={type}
                  className="font-semibold text-center min-w-[100px]"
                >
                  {type.replace("_", " ")}
                </Table.HeaderCell>
              ))}
            </Table.Row>
          </Table.Header>
          <Table.Body>
            {MODULE_PERMISSION_MATRIX.map((module) => (
              <Table.Row key={module.id} className="hover:bg-gray-50">
                <Table.Cell className="font-medium">
                  <div className="flex items-start space-x-3">
                    <div className="space-y-1 flex-1">
                      <div className="flex items-center space-x-2">
                        <Text className="font-medium">{module.name}</Text>
                      </div>
                    </div>
                  </div>
                </Table.Cell>
                {permissionTypes.map((type) => {
                  const permission = module.permissions[type];
                  const isRestricted = isPermissionRestricted(module, type);
                  const isSelected = isPermissionSelected(permission);
                  const isDisabled = disabled || loading || isRestricted || !permission;

                  return (
                    <Table.Cell key={type} className="text-center">
                      {permission ? (
                        <div className="flex items-center justify-center">
                          <Checkbox
                            checked={isSelected}
                            onCheckedChange={() => handlePermissionChange(permission)}
                            disabled={isDisabled}
                            className={isRestricted ? "opacity-50" : ""}
                          />
                          {isRestricted && (
                            <Tooltip content="This permission is restricted for this module">
                              <Info className="h-3 w-3 text-gray-400 ml-1" />
                            </Tooltip>
                          )}
                        </div>
                      ) : (
                        <span className="text-gray-300">—</span>
                      )}
                    </Table.Cell>
                  );
                })}
              </Table.Row>
            ))}
          </Table.Body>
        </Table>
      </div>

    </div>
  );
};
