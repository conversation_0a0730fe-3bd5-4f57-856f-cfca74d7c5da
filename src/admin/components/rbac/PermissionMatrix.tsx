import React, { useState, useEffect } from "react";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>, Badge, Text, Heading, Checkbox, Table } from "@camped-ai/ui";
import { Shield, Eye, Edit, Trash2, Plus } from "lucide-react";
import { useRbac, CustomRole, ScreenPermission, PermissionGroup } from "../../hooks/use-rbac";

interface PermissionMatrixProps {
  roles?: CustomRole[];
  onRoleUpdate?: (roleId: string, permissions: ScreenPermission[]) => void;
  readOnly?: boolean;
}

// Hierarchy mapping for permissions (only 'view' chain)
const PERMISSION_HIERARCHY: Record<string, string | null> = {
  [ScreenPermission.ROOMS_VIEW]: ScreenPermission.HOTEL_MANAGEMENT_VIEW,
  [ScreenPermission.HOTEL_MANAGEMENT_VIEW]: ScreenPermission.DESTINATIONS_VIEW,
  [ScreenPermission.DESTINATIONS_VIEW]: null,
};

// Helper to get all parent permissions for a given permission
function getAllParents(permission: ScreenPermission): ScreenPermission[] {
  const parents: ScreenPermission[] = [];
  let current = PERMISSION_HIERARCHY[permission];
  while (current) {
    parents.push(current as ScreenPermission);
    current = PERMISSION_HIERARCHY[current];
  }
  return parents;
}

// Helper to get all children for a given permission
function getAllChildren(permission: ScreenPermission): ScreenPermission[] {
  const children: ScreenPermission[] = [];
  Object.entries(PERMISSION_HIERARCHY).forEach(([child, parent]) => {
    if (parent === permission) {
      children.push(child as ScreenPermission);
      // Recursively add grandchildren
      children.push(...getAllChildren(child as ScreenPermission));
    }
  });
  return children;
}

export const PermissionMatrix: React.FC<PermissionMatrixProps> = ({
  roles: propRoles,
  onRoleUpdate,
  readOnly = false,
}) => {
  const { getRoles, getPermissions, updateRole } = useRbac();
  
  const [roles, setRoles] = useState<CustomRole[]>([]);
  const [permissionGroups, setPermissionGroups] = useState<PermissionGroup[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [compactView, setCompactView] = useState(false);
  const [selectedGroup, setSelectedGroup] = useState<string>("all");

  useEffect(() => {
    loadData();
  }, []);

  useEffect(() => {
    if (propRoles) {
      setRoles(propRoles);
    }
  }, [propRoles]);

  const loadData = async () => {
    try {
      setLoading(true);
      const [rolesResponse, permissionsResponse] = await Promise.all([
        propRoles ? Promise.resolve({ roles: propRoles }) : getRoles(),
        getPermissions(),
      ]);

      if (!propRoles) {
        setRoles(rolesResponse.roles || []);
      }
      setPermissionGroups(permissionsResponse.groups || []);
    } catch (err) {
      setError("Failed to load data");
    } finally {
      setLoading(false);
    }
  };

  const handlePermissionToggle = async (roleId: string, permission: ScreenPermission) => {
    if (readOnly) return;

    // Normalize permission to string for mapping
    const permKey = typeof permission === 'string' ? permission : String(permission);

    const role = roles.find(r => r.id === roleId);
    if (!role || role.is_system_role) return;

    const hasPermission = role.permissions.includes(permission);

    // Debug: Show what is being toggled and what parents are found
    const parents = getAllParents(permKey as ScreenPermission);
    console.log('Toggled permission:', permission, 'Type:', typeof permission);
    console.log('Permission key used for mapping:', permKey);
    console.log('Parents found:', parents);

    let newPermissions: ScreenPermission[];

    if (hasPermission) {
      // Deselect: remove this permission and all its children
      const children = getAllChildren(permKey as ScreenPermission);
      newPermissions = role.permissions.filter(
        p => p !== permission && !children.includes(p)
      );
    } else {
      // Select: add this permission and all its parents
      newPermissions = Array.from(new Set([
        ...role.permissions,
        permission,
        ...parents,
      ]));
    }

    // Debug: Show the new permissions array
    console.log('New permissions:', newPermissions);

    try {
      if (onRoleUpdate) {
        onRoleUpdate(roleId, newPermissions);
      } else {
        await updateRole(roleId, { permissions: newPermissions });
        // Update local state
        setRoles(prev => prev.map(r =>
          r.id === roleId ? { ...r, permissions: newPermissions } : r
        ));
      }
    } catch (err) {
      setError("Failed to update role permissions");
    }
  };

  const getFilteredPermissions = () => {
    if (selectedGroup === "all") {
      return permissionGroups.flatMap(group => group.permissions);
    }
    const group = permissionGroups.find(g => g.id === selectedGroup);
    return group ? group.permissions : [];
  };

  const getPermissionDisplayName = (permission: ScreenPermission): string => {
    return permission
      .split(":")
      .map(part => part.split("_").map(word => 
        word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()
      ).join(" "))
      .join(" - ");
  };

  const getPermissionGroup = (permission: ScreenPermission): string => {
    const group = permissionGroups.find(g => g.permissions.includes(permission));
    return group ? group.name : "Other";
  };

  if (loading) {
    return (
      <Container className="p-4">
        <Typography>Loading permission matrix...</Typography>
      </Container>
    );
  }

  if (error) {
    return (
      <Container className="p-4">
        <Typography color="error">{error}</Typography>
      </Container>
    );
  }

  const filteredPermissions = getFilteredPermissions();

  return (
    <Container className="p-4">
      <Box sx={{ display: "flex", flexDirection: "column", gap: 3 }}>
        {/* Header */}
        <Box display="flex" justifyContent="space-between" alignItems="center">
          <Box>
            <Typography variant="h5" component="h2" gutterBottom>
              Permission Matrix
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Visual overview of role permissions
            </Typography>
          </Box>
          <Box display="flex" gap={2} alignItems="center">
            <FormControlLabel
              control={
                <Switch
                  checked={compactView}
                  onChange={(e) => setCompactView(e.target.checked)}
                />
              }
              label="Compact View"
            />
          </Box>
        </Box>

        {/* Group Filter */}
        <Box display="flex" gap={1} flexWrap="wrap">
          <Chip
            label="All Permissions"
            onClick={() => setSelectedGroup("all")}
            color={selectedGroup === "all" ? "primary" : "default"}
            variant={selectedGroup === "all" ? "filled" : "outlined"}
          />
          {permissionGroups.map((group) => (
            <Chip
              key={group.id}
              label={group.name}
              onClick={() => setSelectedGroup(group.id)}
              color={selectedGroup === group.id ? "primary" : "default"}
              variant={selectedGroup === group.id ? "filled" : "outlined"}
            />
          ))}
        </Box>

        {/* Matrix Table */}
        <TableContainer component={Paper} sx={{ maxHeight: 600 }}>
          <Table stickyHeader size={compactView ? "small" : "medium"}>
            <TableHead>
              <TableRow>
                <TableCell sx={{ minWidth: 200, fontWeight: "bold" }}>
                  Permission
                </TableCell>
                {!compactView && (
                  <TableCell sx={{ minWidth: 120, fontWeight: "bold" }}>
                    Group
                  </TableCell>
                )}
                {roles.map((role) => (
                  <TableCell 
                    key={role.id} 
                    align="center" 
                    sx={{ minWidth: 120, fontWeight: "bold" }}
                  >
                    <Box display="flex" flexDirection="column" alignItems="center" gap={1}>
                      <Typography variant="body2" fontWeight="bold">
                        {role.name}
                      </Typography>
                      {role.is_system_role && (
                        <Badge color="blue" size="small">
                          System
                        </Badge>
                      )}
                      {!role.is_active && (
                        <Badge color="red" size="small">
                          Inactive
                        </Badge>
                      )}
                    </Box>
                  </TableCell>
                ))}
              </TableRow>
            </TableHead>
            <TableBody>
              {filteredPermissions.map((permission) => (
                <TableRow key={permission} hover>
                  <TableCell>
                    <Typography variant="body2">
                      {compactView 
                        ? permission.split(":")[1]?.replace(/_/g, " ") || permission
                        : getPermissionDisplayName(permission)
                      }
                    </Typography>
                  </TableCell>
                  {!compactView && (
                    <TableCell>
                      <Chip 
                        label={getPermissionGroup(permission)} 
                        size="small" 
                        variant="outlined"
                      />
                    </TableCell>
                  )}
                  {roles.map((role) => {
                    const hasPermission = role.permissions.includes(permission);
                    const isSystemRole = role.is_system_role;
                    
                    return (
                      <TableCell key={role.id} align="center">
                        <Checkbox
                          checked={
                            // Checked if explicitly selected, or if any child is selected (for parent in view mode)
                            hasPermission ||
                            (!readOnly && getAllChildren(permission).some(child => role.permissions.includes(child)))
                          }
                          onChange={() => handlePermissionToggle(role.id, permission)}
                          disabled={
                            readOnly || isSystemRole || !role.is_active ||
                            // Disable parent if only enabled by child (not explicitly selected)
                            (!hasPermission && getAllChildren(permission).some(child => role.permissions.includes(child)))
                          }
                          size="small"
                          color="primary"
                        />
                      </TableCell>
                    );
                  })}
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>

        {/* Summary */}
        <Box sx={{ mt: 2 }}>
          <Typography variant="h6" gutterBottom>
            Summary
          </Typography>
          <Box display="flex" gap={2} flexWrap="wrap">
            {roles.map((role) => {
              const permissionCount = role.permissions.filter(p => 
                filteredPermissions.includes(p)
              ).length;
              const totalPermissions = filteredPermissions.length;
              const percentage = totalPermissions > 0 
                ? Math.round((permissionCount / totalPermissions) * 100) 
                : 0;

              return (
                <Box key={role.id} sx={{ minWidth: 150 }}>
                  <Typography variant="body2" fontWeight="bold">
                    {role.name}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    {permissionCount}/{totalPermissions} permissions ({percentage}%)
                  </Typography>
                </Box>
              );
            })}
          </Box>
        </Box>

        {/* Legend */}
        <Box sx={{ mt: 2, p: 2, bgcolor: "grey.50", borderRadius: 1 }}>
          <Typography variant="body2" fontWeight="bold" gutterBottom>
            Legend:
          </Typography>
          <Box display="flex" gap={3} flexWrap="wrap">
            <Box display="flex" alignItems="center" gap={1}>
              <Checkbox checked size="small" disabled />
              <Typography variant="body2">Has Permission</Typography>
            </Box>
            <Box display="flex" alignItems="center" gap={1}>
              <Checkbox size="small" disabled />
              <Typography variant="body2">No Permission</Typography>
            </Box>
            <Box display="flex" alignItems="center" gap={1}>
              <Badge color="blue" size="small">System</Badge>
              <Typography variant="body2">System Role (Read-only)</Typography>
            </Box>
            <Box display="flex" alignItems="center" gap={1}>
              <Badge color="red" size="small">Inactive</Badge>
              <Typography variant="body2">Inactive Role</Typography>
            </Box>
          </Box>
        </Box>
      </Box>
    </Container>
  );
};
