import React, { useState, useEffect } from "react";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, Text, Heading, Prompt } from "@camped-ai/ui";
import { Plus, Edit, Trash2, Users, Shield, Eye } from "lucide-react";
import { useRbac, CustomRole } from "../../hooks/use-rbac";

interface RoleManagementProps {
  onRoleSelect?: (role: CustomRole) => void;
}

export const RoleManagement: React.FC<RoleManagementProps> = ({ onRoleSelect }) => {
  const { isAdmin, getRoles, deleteRole } = useRbac();
  const [roles, setRoles] = useState<CustomRole[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [roleToDelete, setRoleToDelete] = useState<CustomRole | null>(null);
  const [deleting, setDeleting] = useState(false);

  // Load roles on component mount
  useEffect(() => {
    loadRoles();
  }, []);

  const loadRoles = async () => {
    try {
      console.log("RoleManagement: Starting to load roles");
      setLoading(true);
      setError(null);

      console.log("RoleManagement: Calling getRoles()");
      const response = await getRoles();
      console.log("RoleManagement: getRoles response:", response);

      const rolesArray = response.roles || [];
      console.log("RoleManagement: Setting roles array:", rolesArray);
      setRoles(rolesArray);
    } catch (err) {
      console.error("RoleManagement: Error loading roles:", err);
      setError(err instanceof Error ? err.message : "Failed to load roles");
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteRole = async () => {
    if (!roleToDelete) return;

    try {
      setDeleting(true);
      await deleteRole(roleToDelete.id);
      await loadRoles(); // Refresh the list
      setDeleteDialogOpen(false);
      setRoleToDelete(null);
    } catch (err) {
      setError(err instanceof Error ? err.message : "Failed to delete role");
    } finally {
      setDeleting(false);
    }
  };

  const openDeleteDialog = (role: CustomRole) => {
    setRoleToDelete(role);
    setDeleteDialogOpen(true);
  };

  const closeDeleteDialog = () => {
    setDeleteDialogOpen(false);
    setRoleToDelete(null);
  };

  // Only admins can manage roles
  if (!isAdmin()) {
    return (
      <Container className="p-4">
        <Alert variant="warning">
          You don't have permission to manage roles. Contact an administrator.
        </Alert>
      </Container>
    );
  }

  if (loading) {
    return (
      <Container className="p-4">
        <div className="flex items-center gap-2">
          <Text>Loading roles...</Text>
        </div>
      </Container>
    );
  }

  return (
    <Container className="p-4">
      <div className="space-y-6">
        {/* Header */}
        <div className="flex justify-between items-center">
          <div>
            <Heading level="h1" className="text-2xl font-bold mb-2">
              Role Management
            </Heading>
            <Text className="text-gray-600">
              Create and manage custom roles with specific permissions
            </Text>
          </div>
          <Button
            onClick={() => {
              // TODO: Open create role dialog
              console.log("Create new role");
            }}
          >
            <Plus size={16} className="mr-2" />
            Create Role
          </Button>
        </div>

        {/* Error Alert */}
        {error && (
          <Alert variant="error">
            {error}
          </Alert>
        )}

        {/* Roles Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {roles.map((role) => (
            <div
              key={role.id}
              className={`border border-gray-200 rounded-lg p-6 h-full ${
                onRoleSelect ? 'cursor-pointer hover:shadow-md transition-shadow' : ''
              }`}
              onClick={() => onRoleSelect?.(role)}
            >
              <div className="space-y-4">
                {/* Role Header */}
                <div className="flex justify-between items-start">
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-2">
                      <Shield size={16} />
                      <Heading level="h3" className="text-lg font-semibold">
                        {role.name}
                      </Heading>
                      {role.is_system_role && (
                        <Badge>
                          System
                        </Badge>
                      )}
                      {!role.is_active && (
                        <Badge>
                          Inactive
                        </Badge>
                      )}
                    </div>
                    <Text className="text-gray-600 text-sm mb-3">
                      {role.description}
                    </Text>
                  </div>
                </div>

                {/* Permissions Count */}
                <div className="flex items-center gap-2">
                  <Eye size={14} className="text-gray-500" />
                  <Text className="text-sm text-gray-600">
                    {role.permissions.length} permissions
                  </Text>
                </div>

                {/* Actions */}
                <div className="flex justify-between items-center mt-4">
                  <div className="flex gap-2">
                    <Button
                      size="small"
                      variant="secondary"
                      onClick={(e) => {
                        e.stopPropagation();
                        // TODO: Open edit role dialog
                        console.log("Edit role:", role.id);
                      }}
                      disabled={role.is_system_role}
                    >
                      <Edit size={14} className="mr-1" />
                      Edit
                    </Button>
                    <Button
                      size="small"
                      variant="danger"
                      onClick={(e) => {
                        e.stopPropagation();
                        openDeleteDialog(role);
                      }}
                      disabled={role.is_system_role}
                    >
                      <Trash2 size={14} className="mr-1" />
                      Delete
                    </Button>
                  </div>
                  <Button
                    size="small"
                    onClick={(e) => {
                      e.stopPropagation();
                      // TODO: Show users with this role
                      console.log("View users with role:", role.id);
                    }}
                  >
                    <Users size={14} className="mr-1" />
                    Users
                  </Button>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Empty State */}
        {roles.length === 0 && !loading && (
          <div className="text-center py-12 border-2 border-dashed border-gray-300 rounded-lg">
            <Shield size={48} className="opacity-30 mx-auto mb-4" />
            <Heading level="h3" className="text-lg font-semibold mb-2">
              No roles found
            </Heading>
            <Text className="text-gray-600 mb-6">
              Create your first custom role to get started
            </Text>
            <Button
              onClick={() => {
                // TODO: Open create role dialog
                console.log("Create first role");
              }}
            >
              <Plus size={16} className="mr-2" />
              Create Role
            </Button>
          </div>
        )}

        {/* Delete Confirmation Dialog */}
        <Prompt
          open={deleteDialogOpen}
          onOpenChange={setDeleteDialogOpen}
        >
          <Prompt.Content>
            <Prompt.Header>
              <Prompt.Title>Delete Role</Prompt.Title>
              <Prompt.Description>
                Are you sure you want to delete the role "{roleToDelete?.name}"?
                This action cannot be undone.
                {roleToDelete?.is_system_role && (
                  <div className="mt-2">
                    <Alert variant="error">
                      System roles cannot be deleted.
                    </Alert>
                  </div>
                )}
              </Prompt.Description>
            </Prompt.Header>
            <Prompt.Footer>
              <Prompt.Cancel onClick={closeDeleteDialog} disabled={deleting}>
                Cancel
              </Prompt.Cancel>
              <Prompt.Action onClick={handleDeleteRole} disabled={deleting || roleToDelete?.is_system_role}>
                {deleting ? "Deleting..." : "Delete"}
              </Prompt.Action>
            </Prompt.Footer>
          </Prompt.Content>
        </Prompt>
      </div>
    </Container>
  );
};
