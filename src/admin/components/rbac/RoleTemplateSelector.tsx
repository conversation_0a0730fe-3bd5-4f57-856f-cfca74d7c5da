import React, { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  Alert,
  FocusModal,
  Input,
  Text,
  Heading,
  Label,
  Badge
} from "@camped-ai/ui";
import { FileText, Users, Shield, ChevronRight } from "lucide-react";
import { useRbac, RoleTemplate, ScreenPermission } from "../../hooks/use-rbac";

interface RoleTemplateSelectorProps {
  open: boolean;
  onClose: () => void;
  onTemplateSelect?: (template: RoleTemplate, customName?: string) => void;
  onSuccess?: () => void;
}

export const RoleTemplateSelector: React.FC<RoleTemplateSelectorProps> = ({
  open,
  onClose,
  onTemplateSelect,
  onSuccess,
}) => {
  const { getRoleTemplates, createRole } = useRbac();
  
  const [templates, setTemplates] = useState<RoleTemplate[]>([]);
  const [selectedTemplate, setSelectedTemplate] = useState<RoleTemplate | null>(null);
  const [customName, setCustomName] = useState("");
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [step, setStep] = useState<"select" | "customize">("select");

  useEffect(() => {
    if (open) {
      loadTemplates();
      resetState();
    }
  }, [open]);

  const loadTemplates = async () => {
    try {
      setLoading(true);
      const response = await getRoleTemplates();
      setTemplates(response.templates || []);
    } catch (err) {
      setError("Failed to load role templates");
    } finally {
      setLoading(false);
    }
  };

  const resetState = () => {
    setSelectedTemplate(null);
    setCustomName("");
    setStep("select");
    setError(null);
  };

  const handleTemplateSelect = (template: RoleTemplate) => {
    setSelectedTemplate(template);
    setCustomName(template.name);
    setStep("customize");
  };

  const handleCreateFromTemplate = async () => {
    if (!selectedTemplate || !customName.trim()) {
      setError("Please provide a role name");
      return;
    }

    try {
      setLoading(true);
      setError(null);

      if (onTemplateSelect) {
        onTemplateSelect(selectedTemplate, customName.trim());
      } else {
        await createRole({
          name: customName.trim(),
          description: selectedTemplate.description,
          permissions: selectedTemplate.permissions,
        });
        onSuccess?.();
      }
      
      onClose();
    } catch (err) {
      setError(err instanceof Error ? err.message : "Failed to create role");
    } finally {
      setLoading(false);
    }
  };

  const getPermissionDisplayName = (permission: ScreenPermission): string => {
    return permission
      .split(":")
      .map(part => part.split("_").map(word => 
        word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()
      ).join(" "))
      .join(" - ");
  };

  return (
    <FocusModal open={open} onOpenChange={onClose}>
      <FocusModal.Content className="max-w-4xl mx-auto max-h-[90vh] overflow-hidden">
        <FocusModal.Header>
          <div className="flex items-center gap-2">
            <FileText size={20} />
            <FocusModal.Title>
              {step === "select" ? "Choose Role Template" : "Customize Role"}
            </FocusModal.Title>
          </div>
        </FocusModal.Header>

        <FocusModal.Body className="overflow-y-auto space-y-6 p-6">
          <div className="space-y-6">
            {/* Error Alert */}
            {error && (
              <Alert variant="error">
                {error}
              </Alert>
            )}

            {step === "select" && (
              <>
                <Text className="text-gray-600">
                  Choose a pre-configured role template to get started quickly.
                  You can customize the permissions in the next step.
                </Text>

                {loading ? (
                  <Text>Loading templates...</Text>
                ) : (
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {templates.map((template) => (
                      <div
                        key={template.id}
                        className="border border-gray-200 rounded-lg p-4 cursor-pointer hover:shadow-md transition-shadow"
                        onClick={() => handleTemplateSelect(template)}
                      >
                        <div className="space-y-3">
                          {/* Template Header */}
                          <div className="flex justify-between items-start">
                            <div className="flex-1">
                              <div className="flex items-center gap-2 mb-2">
                                <Shield size={16} />
                                <Heading level="h3" className="text-lg font-semibold">
                                  {template.name}
                                </Heading>
                              </div>
                              <Text className="text-gray-600 text-sm mb-3">
                                {template.description}
                              </Text>
                            </div>
                            <ChevronRight size={16} className="text-gray-400" />
                          </div>

                          {/* Suggested For */}
                          <div>
                            <Text className="font-medium text-sm mb-2">
                              Suggested for:
                            </Text>
                            <div className="flex gap-2 flex-wrap">
                              {template.suggested_for.map((suggestion, index) => (
                                <Badge
                                  key={index}
                                >
                                  {suggestion}
                                </Badge>
                              ))}
                            </div>
                          </div>

                          {/* Permissions Count */}
                          <div className="flex items-center gap-2">
                            <Users size={14} className="text-gray-500" />
                            <Text className="text-sm text-gray-600">
                              {template.permissions.length} permissions included
                            </Text>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </>
            )}

          {step === "customize" && selectedTemplate && (
            <>
              <div className="flex items-center gap-2">
                <Button
                  variant="secondary"
                  size="small"
                  onClick={() => setStep("select")}
                >
                  ← Back to Templates
                </Button>
              </div>

              <div className="space-y-4">
                <div>
                  <Heading level="h3" className="text-lg font-semibold mb-2">
                    Customize Role: {selectedTemplate.name}
                  </Heading>
                  <Text className="text-gray-600 mb-4">
                    {selectedTemplate.description}
                  </Text>
                </div>

                <div>
                  <Label htmlFor="custom-role-name" className="block mb-2">
                    Role Name <span className="text-red-500">*</span>
                  </Label>
                  <Input
                    id="custom-role-name"
                    value={customName}
                    onChange={(e) => setCustomName(e.target.value)}
                    placeholder="Enter a custom name for this role"
                    className="w-full"
                  />
              </div>

              {/* Permissions Preview */}
              <div className="space-y-3">
                <div>
                  <Heading level="h3" className="text-base font-semibold mb-2">
                    Included Permissions ({selectedTemplate.permissions.length})
                  </Heading>
                  <Text className="text-gray-600 text-sm mb-3">
                    This template includes the following permissions. You can modify these after creating the role.
                  </Text>
                </div>

                <div className="max-h-72 overflow-auto border border-gray-200 rounded-lg p-4">
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-2">
                    {selectedTemplate.permissions.map((permission) => (
                      <div key={permission} className="flex items-center gap-2">
                        <Text className="text-sm">
                          • {getPermissionDisplayName(permission)}
                        </Text>
                      </div>
                    ))}
                  </div>
                </div>
              </div>

              {/* Suggested For */}
              <div className="space-y-2">
                <Text className="font-medium text-sm">
                  This template is suggested for:
                </Text>
                <div className="flex gap-2 flex-wrap">
                  {selectedTemplate.suggested_for.map((suggestion, index) => (
                    <Badge
                      key={index}
                    >
                      {suggestion}
                    </Badge>
                  ))}
                </div>
              </div>
            </div>
            </>
          )}
          </div>
        </FocusModal.Body>

        <FocusModal.Footer className="flex justify-end space-x-3 p-6 border-t border-gray-200">
          <Button variant="secondary" onClick={onClose} disabled={loading}>
            Cancel
          </Button>
          {step === "customize" && (
            <Button
              onClick={handleCreateFromTemplate}
              disabled={loading || !customName.trim()}
            >
              {loading ? "Creating..." : "Create Role"}
            </Button>
          )}
        </FocusModal.Footer>
      </FocusModal.Content>
    </FocusModal>
  );
};
