import React, { useState, useEffect } from "react";
import {
  <PERSON>ton,
  Alert,
  FocusModal,
  Input,
  Textarea,
  Checkbox,
  Text,
  Heading,
  Label,
  Badge,
  Tooltip
} from "@camped-ai/ui";
import { ChevronDown, Shield, Users, ChevronRight, Info } from "lucide-react";
import { useRbac, CustomRole, ScreenPermission, PermissionGroup } from "../../hooks/use-rbac";
import {
  PERMISSION_GROUPS,
  getPermissionMetadata,
  applyPermissionHierarchy,
  getAllParents,
  getAllChildren,
  PERMISSION_HIERARCHY,
  PERMISSION_METADATA
} from "../../../modules/rbac/permissions";

interface RoleDialogProps {
  open: boolean;
  onClose: () => void;
  role?: CustomRole | null; // null for create, CustomRole for edit
  onSuccess?: () => void;
}

export const RoleDialog: React.FC<RoleDialogProps> = ({
  open,
  onClose,
  role,
  onSuccess,
}) => {
  const { createRole, updateRole, getPermissions, getRoleTemplates } = useRbac();
  
  // Form state
  const [name, setName] = useState("");
  const [description, setDescription] = useState("");
  const [selectedPermissions, setSelectedPermissions] = useState<ScreenPermission[]>([]);
  const [isActive, setIsActive] = useState(true);
  
  // UI state
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [permissionGroups, setPermissionGroups] = useState<PermissionGroup[]>([]);
  const [expandedGroups, setExpandedGroups] = useState<string[]>([]);

  const isEditMode = !!role;

  // Helper function to get permission display name
  const getPermissionDisplayName = (permission: ScreenPermission): string => {
    const metadata = getPermissionMetadata(permission);
    return metadata?.name || permission;
  };

  // Helper function to get dependency tooltip text
  const getDependencyTooltip = (permission: ScreenPermission): string | null => {
    const parents = getAllParents(permission);
    if (parents.length === 0) return null;

    const parentNames = parents.map(p => PERMISSION_METADATA[p]?.name || p);
    if (parentNames.length === 1) {
      return `This permission requires "${parentNames[0]}" to be selected first.`;
    } else {
      return `This permission requires the following permissions: ${parentNames.join(', ')}.`;
    }
  };

  // Load permissions and initialize form
  useEffect(() => {
    if (open) {
      loadPermissions();
      initializeForm();
    }
  }, [open, role]);

  const loadPermissions = async () => {
    try {
      const response = await getPermissions();
      setPermissionGroups(response.groups || []);
      // Expand first group by default
      if (response.groups?.length > 0) {
        setExpandedGroups([response.groups[0].id]);
      }
    } catch (err) {
      setError("Failed to load permissions");
    }
  };

  const initializeForm = () => {
    if (role) {
      // Edit mode - populate with existing role data
      setName(role.name);
      setDescription(role.description);
      setSelectedPermissions(role.permissions);
      setIsActive(role.is_active);
    } else {
      // Create mode - reset form
      setName("");
      setDescription("");
      setSelectedPermissions([]);
      setIsActive(true);
    }
    setError(null);
  };

  const handlePermissionToggle = (permission: ScreenPermission) => {
    setSelectedPermissions(prev => {
      const isCurrentlySelected = prev.includes(permission);
      const isSelecting = !isCurrentlySelected;

      // Apply hierarchical permission logic
      return applyPermissionHierarchy(prev, permission, isSelecting);
    });
  };

  const handleGroupToggle = (groupId: string, groupPermissions: ScreenPermission[]) => {
    const allSelected = groupPermissions.every(p => selectedPermissions.includes(p));

    setSelectedPermissions(prev => {
      let newPermissions = [...prev];

      if (allSelected) {
        // Deselect all permissions in this group (with hierarchy)
        groupPermissions.forEach(permission => {
          newPermissions = applyPermissionHierarchy(newPermissions, permission, false);
        });
      } else {
        // Select all permissions in this group (with hierarchy)
        groupPermissions.forEach(permission => {
          if (!newPermissions.includes(permission)) {
            newPermissions = applyPermissionHierarchy(newPermissions, permission, true);
          }
        });
      }

      return newPermissions;
    });
  };

  const handleAccordionToggle = (groupId: string) => {
    setExpandedGroups(prev => {
      if (prev.includes(groupId)) {
        return prev.filter(id => id !== groupId);
      } else {
        return [...prev, groupId];
      }
    });
  };

  const handleSubmit = async () => {
    if (!name.trim()) {
      setError("Role name is required");
      return;
    }

    if (selectedPermissions.length === 0) {
      setError("At least one permission must be selected");
      return;
    }

    try {
      setLoading(true);
      setError(null);

      if (isEditMode && role) {
        await updateRole(role.id, {
          name: name.trim(),
          description: description.trim(),
          permissions: selectedPermissions,
          is_active: isActive,
        });
      } else {
        await createRole({
          name: name.trim(),
          description: description.trim(),
          permissions: selectedPermissions,
        });
      }

      onSuccess?.();
      onClose();
    } catch (err) {
      setError(err instanceof Error ? err.message : "Failed to save role");
    } finally {
      setLoading(false);
    }
  };

  const getPermissionDisplayName = (permission: ScreenPermission): string => {
    // Convert enum to readable name
    return permission
      .split(":")
      .map(part => part.split("_").map(word => 
        word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()
      ).join(" "))
      .join(" - ");
  };

  return (
    <FocusModal open={open} onOpenChange={onClose}>
      <FocusModal.Content className="max-w-4xl mx-auto max-h-[90vh] overflow-hidden">
        <FocusModal.Header>
          <div className="flex items-center gap-2">
            <Shield size={20} />
            <FocusModal.Title>
              {isEditMode ? `Edit Role: ${role?.name}` : "Create New Role"}
            </FocusModal.Title>
          </div>
        </FocusModal.Header>

        <FocusModal.Body className="overflow-y-auto space-y-6 p-6">
          {/* Error Alert */}
          {error && (
            <Alert variant="error" onClose={() => setError(null)}>
              {error}
            </Alert>
          )}

          {/* Basic Information */}
          <div className="space-y-4">
            <Heading level="h3" className="text-lg font-semibold">
              Basic Information
            </Heading>

            <div className="space-y-4">
              <div>
                <Label htmlFor="role-name" className="block mb-2">
                  Role Name <span className="text-red-500">*</span>
                </Label>
                <Input
                  id="role-name"
                  value={name}
                  onChange={(e) => setName(e.target.value)}
                  disabled={loading || (isEditMode && role?.is_system_role)}
                  placeholder="e.g., Front Desk Manager"
                  className="w-full"
                />
              </div>

              <div>
                <Label htmlFor="role-description" className="block mb-2">
                  Description
                </Label>
                <Textarea
                  id="role-description"
                  value={description}
                  onChange={(e) => setDescription(e.target.value)}
                  disabled={loading || (isEditMode && role?.is_system_role)}
                  placeholder="Describe what this role can do..."
                  rows={3}
                  className="w-full"
                />
              </div>

              {isEditMode && (
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="role-active"
                    checked={isActive}
                    onChange={(e) => setIsActive(e.target.checked)}
                    disabled={loading || role?.is_system_role}
                  />
                  <Label htmlFor="role-active">
                    Active
                  </Label>
                </div>
              )}
            </div>
          </div>

          {/* Permissions */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <Heading level="h3" className="text-lg font-semibold">
                Permissions
              </Heading>
              <Badge variant="secondary">
                {selectedPermissions.length} selected
              </Badge>
            </div>

            <div className="space-y-3">
              {/* Reorder permission groups: Destinations, Hotel Management, Room Management, then the rest */}
              {(() => {
                const groupOrder = ["destinations", "hotel_management", "rooms"];
                const orderedGroups = [
                  ...groupOrder
                    .map(id => permissionGroups.find(g => g.id === id))
                    .filter(Boolean),
                  ...permissionGroups.filter(g => !groupOrder.includes(g.id)),
                ];
                return orderedGroups.map((group) => {
                  if (!group) return null; // Type guard for undefined
                  const groupPermissions = group.permissions;
                  const selectedInGroup = groupPermissions.filter(p => selectedPermissions.includes(p)).length;
                  const allSelected = selectedInGroup === groupPermissions.length;
                  const someSelected = selectedInGroup > 0 && selectedInGroup < groupPermissions.length;
                  const isExpanded = expandedGroups.includes(group.id);

                  return (
                    <div key={group.id} className="border border-gray-200 rounded-lg">
                      <button
                        type="button"
                        onClick={() => handleAccordionToggle(group.id)}
                        className="w-full flex items-center justify-between p-4 text-left hover:bg-gray-50 transition-colors"
                      >
                        <div className="flex items-center space-x-3">
                          <Checkbox
                            checked={allSelected}
                            onChange={(e) => {
                              e.stopPropagation();
                              handleGroupToggle(group.id, groupPermissions);
                            }}
                            disabled={loading || (isEditMode && role?.is_system_role)}
                            className="mr-2"
                          />
                          <Text className="font-medium">{group.name}</Text>
                          <Badge
                            variant={allSelected ? "primary" : someSelected ? "secondary" : "outline"}
                          >
                            {selectedInGroup}/{groupPermissions.length}
                          </Badge>
                        </div>
                        <ChevronRight
                          size={16}
                          className={`transition-transform ${isExpanded ? 'rotate-90' : ''}`}
                        />
                      </button>

                      {isExpanded && (
                        <div className="border-t border-gray-200 p-4">
                          {group.description && (
                            <Text className="text-sm text-gray-600 mb-3">
                              {group.description}
                            </Text>
                          )}
                          <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
                            {groupPermissions.map((permission) => {
                              const isSelected = selectedPermissions.includes(permission);
                              const parents = getAllParents(permission);
                              const children = getAllChildren(permission);
                              const hasParents = parents.length > 0;
                              const hasChildren = children.length > 0;

                              return (
                                <div key={permission} className="flex flex-col space-y-1">
                                  <div className="flex items-center space-x-2">
                                    <Checkbox
                                      id={`permission-${permission}`}
                                      checked={isSelected}
                                      onChange={() => handlePermissionToggle(permission)}
                                      disabled={loading || (isEditMode && role?.is_system_role)}
                                    />
                                    <Label
                                      htmlFor={`permission-${permission}`}
                                      className="text-sm cursor-pointer flex-1"
                                    >
                                      {getPermissionDisplayName(permission)}
                                    </Label>
                                    {/* Info button for hierarchical permissions */}
                                    {hasParents && (
                                      <Tooltip content={getDependencyTooltip(permission)}>
                                        <Info className="h-4 w-4 text-blue-500 hover:text-blue-700 cursor-help" />
                                      </Tooltip>
                                    )}
                                  </div>

                                  {/* Show hierarchy information */}
                                  {(hasParents || hasChildren) && (
                                    <div className="ml-6 text-xs text-gray-500">
                                      {hasParents && (
                                        <div>
                                          <span className="font-medium">Requires:</span>{" "}
                                          {parents.map(parent => getPermissionDisplayName(parent)).join(", ")}
                                        </div>
                                      )}
                                      {hasChildren && (
                                        <div>
                                          <span className="font-medium">Enables:</span>{" "}
                                          {children.map(child => getPermissionDisplayName(child)).join(", ")}
                                        </div>
                                      )}
                                    </div>
                                  )}
                                </div>
                              );
                            })}
                          </div>
                        </div>
                      )}
                    </div>
                  );
                });
              })()}
            </div>
          </div>
        </FocusModal.Body>

        <FocusModal.Footer className="flex justify-end space-x-3 p-6 border-t border-gray-200">
          <Button variant="secondary" onClick={onClose} disabled={loading}>
            Cancel
          </Button>
          <Button
            onClick={handleSubmit}
            disabled={loading || !name.trim() || selectedPermissions.length === 0 || (isEditMode && role?.is_system_role)}
          >
            {loading ? "Saving..." : isEditMode ? "Update Role" : "Create Role"}
          </Button>
        </FocusModal.Footer>
      </FocusModal.Content>
    </FocusModal>
  );
};
