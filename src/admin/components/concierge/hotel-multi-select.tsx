import React from "react";
import { Text } from "@camped-ai/ui";
import { Building2, Loader2 } from "lucide-react";
import { useHotels } from "../../hooks/supplier-products-services/use-hotels";
import { MultiSelect } from "../common/MultiSelect";

interface Hotel {
  id: string;
  name: string;
  is_active: boolean;
}

interface HotelMultiSelectProps {
  selectedHotelIds: string[];
  onHotelsChange: (hotelIds: string[]) => void;
  placeholder?: string;
  className?: string;
  disabled?: boolean;
  showActiveOnly?: boolean;
}

export const HotelMultiSelect: React.FC<HotelMultiSelectProps> = ({
  selectedHotelIds,
  onHotelsChange,
  placeholder = "Select hotels...",
  className = "",
  disabled = false,
  showActiveOnly = true,
}) => {
  const { data: hotelsData, isLoading, error } = useHotels({
    limit: 100,
    is_active: showActiveOnly ? true : undefined,
  });

  const hotels = hotelsData?.hotels || [];

  if (error) {
    return (
      <div className={`space-y-2 ${className}`}>
        <div className="flex items-center space-x-2 p-3 border border-red-200 rounded-md bg-red-50">
          <Building2 className="h-4 w-4 text-red-500" />
          <Text className="text-sm text-red-600">
            Failed to load hotels. Please try again.
          </Text>
        </div>
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className={`space-y-2 ${className}`}>
        <div className="flex items-center space-x-2 p-3 border border-gray-200 rounded-md bg-gray-50">
          <Loader2 className="h-4 w-4 animate-spin text-gray-400" />
          <Text className="text-sm text-gray-500">Loading hotels...</Text>
        </div>
      </div>
    );
  }

  const hotelOptions = hotels.map((hotel: Hotel) => ({
    value: hotel.id,
    label: hotel.name + (!hotel.is_active ? " (Inactive)" : ""),
  }));

  return (
    <div className={`space-y-2 ${className}`}>
      <MultiSelect
        options={hotelOptions}
        selectedValues={selectedHotelIds}
        onChange={onHotelsChange}
        placeholder={placeholder}
        disabled={disabled}
        showSelectAll={true}
        showSelectedTags={true}
      />
      {hotels.length > 0 && (
        <Text className="text-xs text-gray-500">
          {hotels.length} hotel{hotels.length !== 1 ? 's' : ''} available
        </Text>
      )}
    </div>
  );
};
