import React, { useState, useMemo } from "react";
import {
  DndContext,
  DragEndEvent,
  DragOverEvent,
  DragOverlay,
  DragStartEvent,
  PointerSensor,
  KeyboardSensor,
  useSensor,
  useSensors,
  closestCorners,
} from "@dnd-kit/core";
import { sortableKeyboardCoordinates } from "@dnd-kit/sortable";
import { toast } from "@camped-ai/ui";
import { TaskScreenData } from "../../routes/concierge-management/tasks/loader";
import { TaskFilterControls } from "./task-filter-controls";
import { TaskSortControls } from "./task-sort-controls";
import KanbanColumn from "./kanban-column";
import KanbanTaskCard from "./kanban-task-card";
// Import EmptyState and TaskCardSkeleton from task-cards-view since they're defined there
// We'll create local versions to avoid circular dependencies

// Local EmptyState component
const EmptyState = ({ title, message }: { title?: string; message?: string }) => (
  <div className="flex flex-col items-center justify-center py-12 px-4">
    <div className="text-center space-y-3">
      <div className="w-12 h-12 mx-auto bg-ui-bg-subtle rounded-full flex items-center justify-center">
        <span className="text-ui-fg-subtle text-xl">📋</span>
      </div>
      <div>
        <span className="text-ui-fg-base font-medium text-sm">
          {title || "No tasks found"}
        </span>
        <div className="text-ui-fg-muted text-sm mt-1">
          {message || "Get started by creating your first task"}
        </div>
      </div>
    </div>
  </div>
);

// Local TaskCardSkeleton component
const TaskCardSkeleton = () => (
  <div className="p-3 border border-ui-border-base rounded-lg space-y-3 bg-white">
    <div className="flex items-start justify-between">
      <div className="flex-1 space-y-2">
        <div className="flex items-center gap-2">
          <div className="h-4 w-4 bg-gray-200 rounded animate-pulse" />
          <div className="h-4 w-32 bg-gray-200 rounded animate-pulse" />
        </div>
        <div className="h-3 w-20 bg-gray-200 rounded animate-pulse" />
      </div>
      <div className="h-8 w-8 bg-gray-200 rounded animate-pulse" />
    </div>

    <div className="h-4 w-full bg-gray-200 rounded animate-pulse" />
    <div className="h-4 w-3/4 bg-gray-200 rounded animate-pulse" />

    <div className="flex items-center gap-2">
      <div className="h-5 w-16 bg-gray-200 rounded animate-pulse" />
      <div className="h-5 w-12 bg-gray-200 rounded animate-pulse" />
    </div>

    <div className="space-y-2">
      <div className="flex items-center gap-2">
        <div className="h-3 w-3 bg-gray-200 rounded animate-pulse" />
        <div className="h-3 w-24 bg-gray-200 rounded animate-pulse" />
      </div>
    </div>
  </div>
);

// Task status order for columns
const COLUMN_ORDER = ["pending", "in_progress", "review", "completed", "cancelled"] as const;

interface TaskKanbanViewProps {
  tasks: TaskScreenData[];
  isLoading?: boolean;
  totalCount?: number;
  onEdit?: (task: TaskScreenData) => void;
  onDelete?: (task: TaskScreenData) => void;
  onCopyId?: (task: TaskScreenData) => void;
  onFiltersChange?: (filters: any) => void;
  onSortChange?: (sort: any) => void;
  onStatusChange?: (taskId: string, newStatus: string) => Promise<void>;
  hasEditPermission?: boolean;
  hasDeletePermission?: boolean;
  noRecordsTitle?: string;
  noRecordsMessage?: string;
}

export const TaskKanbanView: React.FC<TaskKanbanViewProps> = ({
  tasks,
  isLoading = false,
  totalCount,
  onEdit,
  onDelete,
  onCopyId,
  onFiltersChange,
  onSortChange,
  onStatusChange,
  hasEditPermission = false,
  hasDeletePermission = false,
  noRecordsTitle = "No tasks found",
  noRecordsMessage = "There are no tasks to display.",
}) => {
  const [activeTask, setActiveTask] = useState<TaskScreenData | null>(null);
  const [isUpdating, setIsUpdating] = useState<string | null>(null);

  // Optimistic updates state - tracks tasks that have been moved but not yet confirmed by API
  const [optimisticUpdates, setOptimisticUpdates] = useState<Record<string, string>>({});

  // Configure drag sensors
  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 8,
      },
    }),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  // Group tasks by status with optimistic updates
  const tasksByStatus = useMemo(() => {
    const grouped = tasks.reduce((acc, task) => {
      // Use optimistic status if available, otherwise use actual status
      const status = optimisticUpdates[task.id] || task.status;
      if (!acc[status]) {
        acc[status] = [];
      }
      acc[status].push(task);
      return acc;
    }, {} as Record<string, TaskScreenData[]>);

    // Ensure all columns exist even if empty
    COLUMN_ORDER.forEach(status => {
      if (!grouped[status]) {
        grouped[status] = [];
      }
    });

    return grouped;
  }, [tasks, optimisticUpdates]);

  const handleDragStart = (event: DragStartEvent) => {
    const { active } = event;
    const task = active.data.current?.task;
    if (task) {
      setActiveTask(task);
    }
  };

  const handleDragEnd = async (event: DragEndEvent) => {
    const { active, over } = event;
    setActiveTask(null);

    if (!over) return;

    const taskId = active.id as string;
    const task = active.data.current?.task as TaskScreenData;
    const newStatus = over.id as string;
    const originalStatus = task.status;

    // Don't update if status hasn't changed
    if (originalStatus === newStatus) return;

    // Don't allow dropping on invalid targets
    if (!COLUMN_ORDER.includes(newStatus as any)) return;

    // OPTIMISTIC UPDATE: Immediately update the UI
    setOptimisticUpdates(prev => ({
      ...prev,
      [taskId]: newStatus
    }));

    try {
      setIsUpdating(taskId);

      if (onStatusChange) {
        // The parent component handles all cache updates now
        await onStatusChange(taskId, newStatus);

        // SUCCESS: Remove local optimistic update since cache is now updated
        setOptimisticUpdates(prev => {
          const updated = { ...prev };
          delete updated[taskId];
          return updated;
        });

        toast.success(`Task moved to ${newStatus.replace('_', ' ')}`);
      } else {
        // Fallback: make API call directly (should rarely happen)
        const response = await fetch(`/admin/concierge-management/tasks/${taskId}`, {
          method: "PUT",
          headers: {
            "Content-Type": "application/json",
          },
          credentials: "include",
          body: JSON.stringify({
            status: newStatus,
          }),
        });

        if (!response.ok) {
          throw new Error("Failed to update task status");
        }

        // SUCCESS: Remove optimistic update
        setOptimisticUpdates(prev => {
          const updated = { ...prev };
          delete updated[taskId];
          return updated;
        });

        toast.success(`Task moved to ${newStatus.replace('_', ' ')}`);

        // Trigger a refresh if no callback provided
        window.location.reload();
      }
    } catch (error) {
      console.error("Error updating task status:", error);

      // FAILURE: Revert local optimistic update with smooth transition
      // The parent component will handle cache rollback
      setOptimisticUpdates(prev => {
        const updated = { ...prev };
        delete updated[taskId];
        return updated;
      });

      // Provide more specific error messages
      let errorMessage = "Failed to move task";
      if (error instanceof Error) {
        if (error.message.includes("network") || error.message.includes("fetch")) {
          errorMessage = "Network error - please check your connection and try again";
        } else if (error.message.includes("permission") || error.message.includes("unauthorized")) {
          errorMessage = "You don't have permission to move this task";
        } else if (error.message.includes("not found") && !error.message.includes("cache")) {
          // Only show "not found" error if it's from the server, not from cache lookup
          errorMessage = "Task not found - it may have been deleted";
        } else if (error.message.includes("cache")) {
          // For cache-related errors, show a generic message
          errorMessage = "Failed to move task - please try again";
        } else {
          errorMessage = `Failed to move task: ${error.message}`;
        }
      }

      toast.error(errorMessage);

      // Optional: Suggest retry for network errors
      if (error instanceof Error && (error.message.includes("network") || error.message.includes("fetch"))) {
        setTimeout(() => {
          toast.info("You can try moving the task again", {
            duration: 3000,
          });
        }, 2000);
      }
    } finally {
      setIsUpdating(null);
    }
  };

  const handleDragOver = (event: DragOverEvent) => {
    // Handle drag over logic if needed
  };

  if (isLoading) {
    return (
      <div className="divide-y">
        {/* Filter and Sort Controls */}
        <div className="px-6 py-4 space-y-4">
          {onFiltersChange && (
            <TaskFilterControls onFiltersChange={onFiltersChange} />
          )}
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              {onSortChange && (
                <TaskSortControls onSortChange={onSortChange} />
              )}
            </div>
          </div>
        </div>

        {/* Loading Kanban */}
        <div className="p-6">
          <div className="flex gap-4 overflow-x-auto">
            {COLUMN_ORDER.map((status) => (
              <div key={status} className="flex flex-col min-w-[280px]">
                <div className="p-4 bg-gray-50 rounded-t-lg border">
                  <div className="h-4 bg-gray-200 rounded animate-pulse"></div>
                </div>
                <div className="p-3 border-l border-r border-b rounded-b-lg min-h-[400px] bg-gray-50">
                  <div className="space-y-3">
                    {Array.from({ length: 2 }).map((_, index) => (
                      <TaskCardSkeleton key={index} />
                    ))}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  if (tasks.length === 0) {
    return (
      <div className="divide-y">
        {/* Filter and Sort Controls */}
        <div className="px-6 py-4 space-y-4">
          {onFiltersChange && (
            <TaskFilterControls onFiltersChange={onFiltersChange} />
          )}
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              {onSortChange && (
                <TaskSortControls onSortChange={onSortChange} />
              )}
            </div>
          </div>
        </div>

        {/* Empty State */}
        <div className="p-6">
          <EmptyState title={noRecordsTitle} message={noRecordsMessage} />
        </div>
      </div>
    );
  }

  return (
    <div className="divide-y">
      {/* Filter and Sort Controls */}
      <div className="px-6 py-4 space-y-4">
        {onFiltersChange && (
          <TaskFilterControls onFiltersChange={onFiltersChange} />
        )}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            {onSortChange && (
              <TaskSortControls onSortChange={onSortChange} />
            )}
          </div>
        </div>
      </div>

      {/* Kanban Board */}
      <div className="p-6">
        <DndContext
          sensors={sensors}
          collisionDetection={closestCorners}
          onDragStart={handleDragStart}
          onDragOver={handleDragOver}
          onDragEnd={handleDragEnd}
        >
          <div className="flex gap-4 overflow-x-auto pb-4">
            {COLUMN_ORDER.map((status) => (
              <KanbanColumn
                key={status}
                status={status}
                tasks={tasksByStatus[status] || []}
                onEdit={onEdit}
                onDelete={onDelete}
                onCopyId={onCopyId}
                hasEditPermission={hasEditPermission}
                hasDeletePermission={hasDeletePermission}
                updatingTaskId={isUpdating}
              />
            ))}
          </div>

          {/* Drag Overlay */}
          <DragOverlay>
            {activeTask ? (
              <KanbanTaskCard
                task={activeTask}
                onEdit={onEdit}
                onDelete={onDelete}
                onCopyId={onCopyId}
                hasEditPermission={hasEditPermission}
                hasDeletePermission={hasDeletePermission}
                isDragging={true}
              />
            ) : null}
          </DragOverlay>
        </DndContext>
      </div>
    </div>
  );
};

export default TaskKanbanView;
