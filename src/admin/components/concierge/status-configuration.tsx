import React, { useState, useEffect } from "react";
import {
  Container,
  Heading,
  Text,
  Button,
  Input,
  Select,
  Badge,
  Table,
  Toaster,
  toast,
  FocusModal,
} from "@camped-ai/ui";
import {
  Plus,
  Edit,
  Trash2,
  Save,
  X,
  Palette,
} from "lucide-react";
import { useRbac } from "../../hooks/use-rbac";

// Types
interface Status {
  id: string;
  name: string;
  color: "grey" | "blue" | "green" | "orange" | "red" | "purple";
  description?: string;
  category: "task" | "itinerary" | "booking" | "general";
  is_active: boolean;
  sort_order: number;
  created_at: string;
  updated_at: string;
}

const StatusConfiguration: React.FC = () => {
  const [statuses, setStatuses] = useState<Status[]>([]);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [editingStatus, setEditingStatus] = useState<Status | null>(null);
  const [formData, setFormData] = useState({
    name: "",
    color: "grey" as Status["color"],
    description: "",
    category: "general" as Status["category"],
    is_active: true,
  });
  const [isLoading, setIsLoading] = useState(false);
  const { hasPermission } = useRbac();

  // Mock data for demonstration
  useEffect(() => {
    const mockStatuses: Status[] = [
      {
        id: "1",
        name: "To Do",
        color: "grey",
        description: "Task is pending and not yet started",
        category: "task",
        is_active: true,
        sort_order: 1,
        created_at: "2024-01-01T00:00:00Z",
        updated_at: "2024-01-01T00:00:00Z",
      },
      {
        id: "2",
        name: "In Progress",
        color: "blue",
        description: "Task is currently being worked on",
        category: "task",
        is_active: true,
        sort_order: 2,
        created_at: "2024-01-01T00:00:00Z",
        updated_at: "2024-01-01T00:00:00Z",
      },
      {
        id: "3",
        name: "Review",
        color: "orange",
        description: "Task is completed and awaiting review",
        category: "task",
        is_active: true,
        sort_order: 3,
        created_at: "2024-01-01T00:00:00Z",
        updated_at: "2024-01-01T00:00:00Z",
      },
      {
        id: "4",
        name: "Done",
        color: "green",
        description: "Task is completed and approved",
        category: "task",
        is_active: true,
        sort_order: 4,
        created_at: "2024-01-01T00:00:00Z",
        updated_at: "2024-01-01T00:00:00Z",
      },
      {
        id: "5",
        name: "Draft",
        color: "grey",
        description: "Itinerary is in draft state",
        category: "itinerary",
        is_active: true,
        sort_order: 1,
        created_at: "2024-01-01T00:00:00Z",
        updated_at: "2024-01-01T00:00:00Z",
      },
      {
        id: "6",
        name: "Confirmed",
        color: "green",
        description: "Itinerary is confirmed with guest",
        category: "itinerary",
        is_active: true,
        sort_order: 2,
        created_at: "2024-01-01T00:00:00Z",
        updated_at: "2024-01-01T00:00:00Z",
      },
    ];
    setStatuses(mockStatuses);
  }, []);

  const handleOpenModal = (status?: Status) => {
    if (status) {
      setEditingStatus(status);
      setFormData({
        name: status.name,
        color: status.color,
        description: status.description || "",
        category: status.category,
        is_active: status.is_active,
      });
    } else {
      setEditingStatus(null);
      setFormData({
        name: "",
        color: "grey",
        description: "",
        category: "general",
        is_active: true,
      });
    }
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
    setEditingStatus(null);
    setFormData({
      name: "",
      color: "grey",
      description: "",
      category: "general",
      is_active: true,
    });
  };

  const handleSave = () => {
    if (!formData.name.trim()) {
      toast.error("Status name is required");
      return;
    }

    // Mock save operation
    const newStatus: Status = {
      id: editingStatus?.id || Date.now().toString(),
      name: formData.name,
      color: formData.color,
      description: formData.description,
      category: formData.category,
      is_active: formData.is_active,
      sort_order: editingStatus?.sort_order || statuses.length + 1,
      created_at: editingStatus?.created_at || new Date().toISOString(),
      updated_at: new Date().toISOString(),
    };

    if (editingStatus) {
      setStatuses(statuses.map(s => s.id === editingStatus.id ? newStatus : s));
      toast.success("Status updated successfully");
    } else {
      setStatuses([...statuses, newStatus]);
      toast.success("Status created successfully");
    }

    handleCloseModal();
  };

  const handleDelete = (statusId: string) => {
    if (confirm("Are you sure you want to delete this status?")) {
      setStatuses(statuses.filter(s => s.id !== statusId));
      toast.success("Status deleted successfully");
    }
  };

  const getStatusBadge = (status: Status) => {
    return <Badge color={status.color}>{status.name}</Badge>;
  };

  const getCategoryLabel = (category: Status["category"]) => {
    const labels = {
      task: "Task",
      itinerary: "Itinerary",
      booking: "Booking",
      general: "General",
    };
    return labels[category];
  };

  const colorOptions = [
    { value: "grey", label: "Grey", preview: "bg-gray-500" },
    { value: "blue", label: "Blue", preview: "bg-blue-500" },
    { value: "green", label: "Green", preview: "bg-green-500" },
    { value: "orange", label: "Orange", preview: "bg-orange-500" },
    { value: "red", label: "Red", preview: "bg-red-500" },
    { value: "purple", label: "Purple", preview: "bg-purple-500" },
  ];

  return (
    <div className="space-y-6">
      <Toaster />
      
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <Heading level="h1" className="text-2xl font-bold">
            Status Configuration
          </Heading>
          <Text className="text-muted-foreground">
            Manage statuses for tasks, itineraries, and other entities
          </Text>
        </div>
        {hasPermission("concierge_config:manage") && (
          <Button onClick={() => handleOpenModal()}>
            <Plus className="h-4 w-4 mr-2" />
            New Status
          </Button>
        )}
      </div>

      {/* Statuses Table */}
      <div className="bg-card rounded-md shadow-sm border">
        <Table>
          <Table.Header>
            <Table.Row>
              <Table.HeaderCell>Status</Table.HeaderCell>
              <Table.HeaderCell>Category</Table.HeaderCell>
              <Table.HeaderCell>Description</Table.HeaderCell>
              <Table.HeaderCell>Active</Table.HeaderCell>
              <Table.HeaderCell>Sort Order</Table.HeaderCell>
              <Table.HeaderCell>Actions</Table.HeaderCell>
            </Table.Row>
          </Table.Header>
          <Table.Body>
            {statuses
              .sort((a, b) => a.category.localeCompare(b.category) || a.sort_order - b.sort_order)
              .map((status) => (
                <Table.Row key={status.id}>
                  <Table.Cell>{getStatusBadge(status)}</Table.Cell>
                  <Table.Cell>{getCategoryLabel(status.category)}</Table.Cell>
                  <Table.Cell>
                    <Text className="text-sm">
                      {status.description || "No description"}
                    </Text>
                  </Table.Cell>
                  <Table.Cell>
                    <Badge color={status.is_active ? "green" : "grey"}>
                      {status.is_active ? "Active" : "Inactive"}
                    </Badge>
                  </Table.Cell>
                  <Table.Cell>{status.sort_order}</Table.Cell>
                  <Table.Cell>
                    <div className="flex gap-2">
                      {hasPermission("concierge_config:manage") && (
                        <>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleOpenModal(status)}
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleDelete(status.id)}
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </>
                      )}
                    </div>
                  </Table.Cell>
                </Table.Row>
              ))}
          </Table.Body>
        </Table>
      </div>

      {/* Create/Edit Modal */}
      <FocusModal open={isModalOpen} onOpenChange={setIsModalOpen}>
        <FocusModal.Content>
          <FocusModal.Header>
            <Heading level="h2">
              {editingStatus ? "Edit Status" : "Create New Status"}
            </Heading>
          </FocusModal.Header>
          <FocusModal.Body className="space-y-4">
            <div>
              <label className="block text-sm font-medium mb-2">Name</label>
              <Input
                value={formData.name}
                onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                placeholder="Enter status name"
              />
            </div>

            <div>
              <label className="block text-sm font-medium mb-2">Category</label>
              <Select
                value={formData.category}
                onValueChange={(value) => setFormData({ ...formData, category: value as Status["category"] })}
              >
                <Select.Trigger>
                  <Select.Value />
                </Select.Trigger>
                <Select.Content>
                  <Select.Item value="task">Task</Select.Item>
                  <Select.Item value="itinerary">Itinerary</Select.Item>
                  <Select.Item value="booking">Booking</Select.Item>
                  <Select.Item value="general">General</Select.Item>
                </Select.Content>
              </Select>
            </div>

            <div>
              <label className="block text-sm font-medium mb-2">Color</label>
              <Select
                value={formData.color}
                onValueChange={(value) => setFormData({ ...formData, color: value as Status["color"] })}
              >
                <Select.Trigger>
                  <Select.Value />
                </Select.Trigger>
                <Select.Content>
                  {colorOptions.map((option) => (
                    <Select.Item key={option.value} value={option.value}>
                      <div className="flex items-center gap-2">
                        <div className={`w-3 h-3 rounded-full ${option.preview}`} />
                        {option.label}
                      </div>
                    </Select.Item>
                  ))}
                </Select.Content>
              </Select>
            </div>

            <div>
              <label className="block text-sm font-medium mb-2">Description</label>
              <Input
                value={formData.description}
                onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                placeholder="Enter status description (optional)"
              />
            </div>

            <div className="flex items-center gap-2">
              <input
                type="checkbox"
                id="is_active"
                checked={formData.is_active}
                onChange={(e) => setFormData({ ...formData, is_active: e.target.checked })}
                className="rounded"
              />
              <label htmlFor="is_active" className="text-sm font-medium">
                Active
              </label>
            </div>
          </FocusModal.Body>
          <FocusModal.Footer>
            <div className="flex gap-2">
              <Button variant="secondary" onClick={handleCloseModal}>
                <X className="h-4 w-4 mr-2" />
                Cancel
              </Button>
              <Button onClick={handleSave}>
                <Save className="h-4 w-4 mr-2" />
                {editingStatus ? "Update" : "Create"}
              </Button>
            </div>
          </FocusModal.Footer>
        </FocusModal.Content>
      </FocusModal>
    </div>
  );
};

export default StatusConfiguration;
