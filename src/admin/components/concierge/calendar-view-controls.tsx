import React from "react";
import { But<PERSON> } from "@camped-ai/ui";
import { CalendarViewType } from "./calendar-view";

interface CalendarViewControlsProps {
  viewType: CalendarViewType;
  onViewTypeChange: (viewType: CalendarViewType) => void;
}

export const CalendarViewControls: React.FC<CalendarViewControlsProps> = ({
  viewType,
  onViewTypeChange,
}) => {
  return (
    <div className="flex border border-ui-border-base rounded-md overflow-hidden shadow-sm">
      <Button
        variant="transparent"
        size="small"
        onClick={() => onViewTypeChange("month")}
        className={`rounded-none border-0 ${
          viewType === "month"
            ? "bg-ui-bg-interactive text-white"
            : "hover:bg-ui-bg-base-hover"
        }`}
      >
        Month
      </Button>
      <Button
        variant="transparent"
        size="small"
        onClick={() => onViewTypeChange("week")}
        className={`rounded-none border-0 ${
          viewType === "week"
            ? "bg-ui-bg-interactive text-white"
            : "hover:bg-ui-bg-base-hover"
        }`}
      >
        Week
      </Button>
      <Button
        variant="transparent"
        size="small"
        onClick={() => onViewTypeChange("day")}
        className={`rounded-none border-0 ${
          viewType === "day"
            ? "bg-ui-bg-interactive text-white"
            : "hover:bg-ui-bg-base-hover"
        }`}
      >
        Day
      </Button>
    </div>
  );
};
