import React from "react";
import { Text, Container } from "@camped-ai/ui";
import { TaskCard } from "./task-card";
import { TaskFilterControls, FilterState } from "./task-filter-controls";
import { TaskSortControls, SortState } from "./task-sort-controls";
import { TaskPaginationControls } from "./task-pagination-controls";
import { Skeleton } from "../../../components/common/skeleton";
import type { TaskScreenData } from "../../routes/concierge-management/tasks/loader";

interface TaskCardsViewProps {
  tasks: TaskScreenData[];
  isLoading?: boolean;
  totalCount: number;
  currentPage: number;
  pageSize: number;
  onEdit?: (task: TaskScreenData) => void;
  onDelete?: (task: TaskScreenData) => void;
  onCopyId?: (taskId: string) => void;
  onFiltersChange?: (filters: FilterState) => void;
  onSortChange?: (sort: SortState) => void;
  onPageChange?: (page: number) => void;
  onPageSizeChange?: (pageSize: number) => void;
  hasEditPermission?: boolean;
  hasDeletePermission?: boolean;
  noRecordsTitle?: string;
  noRecordsMessage?: string;
}

const TaskCardSkeleton = () => (
  <div className="p-4 border border-ui-border-base rounded-lg space-y-3">
    <div className="flex items-start justify-between">
      <div className="flex-1 space-y-2">
        <div className="flex items-center gap-2">
          <Skeleton className="h-4 w-4" />
          <Skeleton className="h-4 w-32" />
        </div>
        <Skeleton className="h-3 w-20" />
      </div>
      <Skeleton className="h-8 w-8" />
    </div>
    
    <Skeleton className="h-4 w-full" />
    <Skeleton className="h-4 w-3/4" />
    
    <div className="flex items-center gap-2">
      <Skeleton className="h-5 w-16" />
      <Skeleton className="h-5 w-12" />
      <Skeleton className="h-5 w-14" />
    </div>
    
    <div className="space-y-2">
      <div className="flex items-center gap-2">
        <Skeleton className="h-3 w-3" />
        <Skeleton className="h-3 w-24" />
      </div>
      <div className="flex items-center gap-2">
        <Skeleton className="h-3 w-3" />
        <Skeleton className="h-3 w-20" />
      </div>
    </div>
    
    <div className="pt-2 border-t border-ui-border-base">
      <Skeleton className="h-3 w-28" />
    </div>
  </div>
);

const EmptyState = ({ title, message }: { title?: string; message?: string }) => (
  <div className="flex flex-col items-center justify-center py-12 px-4">
    <div className="text-center space-y-3">
      <div className="w-12 h-12 mx-auto bg-ui-bg-subtle rounded-full flex items-center justify-center">
        <Text className="text-ui-fg-subtle text-xl">📋</Text>
      </div>
      <div>
        <Text size="small" weight="plus" className="text-ui-fg-base">
          {title || "No tasks found"}
        </Text>
        <Text size="small" className="text-ui-fg-muted mt-1">
          {message || "Get started by creating your first task"}
        </Text>
      </div>
    </div>
  </div>
);

export const TaskCardsView: React.FC<TaskCardsViewProps> = ({
  tasks,
  isLoading = false,
  totalCount,
  currentPage,
  pageSize,
  onEdit,
  onDelete,
  onCopyId,
  onFiltersChange,
  onSortChange,
  onPageChange,
  onPageSizeChange,
  hasEditPermission = false,
  hasDeletePermission = false,
  noRecordsTitle,
  noRecordsMessage,
}) => {
  return (
    <div className="divide-y">
      {/* Filter and Sort Controls */}
      <div className="px-6 py-4 space-y-4">
        {/* Filter Controls */}
        {onFiltersChange && (
          <TaskFilterControls onFiltersChange={onFiltersChange} />
        )}

        {/* Sort Controls */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            {onSortChange && (
              <TaskSortControls onSortChange={onSortChange} />
            )}
          </div>
        </div>
      </div>

      {/* Cards Content */}
      <div className="p-6">
        {isLoading ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
            {Array.from({ length: pageSize || 12 }).map((_, index) => (
              <TaskCardSkeleton key={index} />
            ))}
          </div>
        ) : tasks.length === 0 ? (
          <EmptyState title={noRecordsTitle} message={noRecordsMessage} />
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
            {tasks.map((task) => (
              <TaskCard
                key={task.id}
                task={task}
                onEdit={onEdit}
                onDelete={onDelete}
                onCopyId={onCopyId}
                hasEditPermission={hasEditPermission}
                hasDeletePermission={hasDeletePermission}
              />
            ))}
          </div>
        )}
      </div>

      {/* Pagination Controls */}
      {!isLoading && tasks.length > 0 && onPageChange && onPageSizeChange && (
        <div className="px-6 py-4 border-t border-ui-border-base">
          <TaskPaginationControls
            currentPage={currentPage}
            totalCount={totalCount}
            pageSize={pageSize}
            onPageChange={onPageChange}
            onPageSizeChange={onPageSizeChange}
          />
        </div>
      )}
    </div>
  );
};
