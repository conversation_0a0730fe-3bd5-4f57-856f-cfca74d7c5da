import React from "react";
import { useUserLookup } from "../../hooks/use-user-display";

interface UserDisplayNameProps {
  userId: string | null | undefined;
  className?: string;
  showUnassignedStyle?: boolean;
}

export const UserDisplayName: React.FC<UserDisplayNameProps> = ({
  userId,
  className = "",
  showUnassignedStyle = true,
}) => {
  const { getUserDisplayNameById, isLoading } = useUserLookup();

  if (isLoading) {
    return <span className={className}>Loading...</span>;
  }

  const displayName = getUserDisplayNameById(userId);
  const isUnassigned = !userId || displayName === "Unassigned";

  return (
    <span 
      className={`${className} ${
        showUnassignedStyle && isUnassigned ? "text-ui-fg-subtle" : ""
      }`}
    >
      {displayName}
    </span>
  );
};

export default UserDisplayName;
