import React from "react";
import { <PERSON><PERSON>, Tooltip } from "@camped-ai/ui";
import { Table, Columns, Calendar } from "lucide-react";

export type ViewMode = "list" | "kanban" | "calendar";

interface ViewToggleProps {
  viewMode: ViewMode;
  onViewModeChange: (mode: ViewMode) => void;
  className?: string;
}

export const ViewToggle: React.FC<ViewToggleProps> = ({
  viewMode,
  onViewModeChange,
  className = "",
}) => {
  const getViewIcon = (view: ViewMode) => {
    switch (view) {
      case "list":
        return <Table className="w-4 h-4" />;
      case "kanban":
        return <Columns className="w-4 h-4" />;
      case "calendar":
        return <Calendar className="w-4 h-4" />;
      default:
        return <Table className="w-4 h-4" />;
    }
  };

  const getViewLabel = (view: ViewMode) => {
    switch (view) {
      case "list":
        return "Table View";
      case "kanban":
        return "Kanban View";
      case "calendar":
        return "Calendar View";
      default:
        return "Table View";
    }
  };

  const views: ViewMode[] = ["list", "kanban", "calendar"];

  return (
    <div className={`inline-flex bg-ui-bg-subtle rounded-lg p-1 ${className}`}>
      {views.map((view) => (
        <Tooltip key={view} content={getViewLabel(view)}>
          <Button
            variant="transparent"
            size="small"
            onClick={() => onViewModeChange(view)}
            className={`px-2 py-1.5 rounded-md transition-all duration-200 ${
              viewMode === view
                ? "bg-[#165DFB] shadow-sm text-white font-medium hover:text-white hover:bg-[#336DFD]"
                : "text-ui-black hover:text-white hover:bg-[#336DFD]"
            }`}
          >
            <div className="flex items-center gap-2">
              {getViewIcon(view)}
            </div>
          </Button>
        </Tooltip>
      ))}
    </div>
  );
};
