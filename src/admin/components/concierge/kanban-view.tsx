import React, { useState, useMemo } from "react";
import {
  DndContext,
  DragEndEvent,
  DragOverlay,
  DragStartEvent,
  PointerSensor,
  KeyboardSensor,
  useSensor,
  useSensors,
  closestCorners,
} from "@dnd-kit/core";
import { sortableKeyboardCoordinates } from "@dnd-kit/sortable";
import { Heading } from "@camped-ai/ui";
import { BookingScreenData } from "../../routes/concierge-management/bookings/loader";
import { KanbanColumn } from "./kanban-column";
import { KanbanCard } from "./kanban-card";
import { KanbanGroupControls } from "./kanban-group-controls";
import {
  getGroupByOptions,
  getSubGroupByOptions,
  groupBookings,
  groupBookingsWithSubGroup,
  sortGroups,
  canUpdateField,
  getUpdatePayload,
} from "./kanban-utils";

export interface KanbanViewProps {
  bookings: BookingScreenData[];
  onBookingUpdate: (
    bookingId: string,
    updates: Partial<BookingScreenData>
  ) => Promise<void>;
  onBookingClick: (booking: BookingScreenData) => void;
  isLoading?: boolean;
}

export const KanbanView: React.FC<KanbanViewProps> = ({
  bookings,
  onBookingUpdate,
  onBookingClick,
  isLoading = false,
}) => {
  const [activeId, setActiveId] = useState<string | null>(null);
  const [groupBy, setGroupBy] =
    useState<keyof BookingScreenData>("assigned_to");
  const [subGroupBy, setSubGroupBy] = useState<
    keyof BookingScreenData | "none"
  >("none");

  // Get grouping options
  const groupByOptions = useMemo(() => getGroupByOptions(), []);
  const subGroupByOptions = useMemo(
    () => getSubGroupByOptions(groupBy),
    [groupBy]
  );

  // Sensors for drag and drop
  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 8,
      },
    }),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  // Group bookings by the selected field
  const groupedBookings = useMemo(() => {
    if (subGroupBy !== "none") {
      return groupBookingsWithSubGroup(bookings, groupBy, subGroupBy);
    }
    return groupBookings(bookings, groupBy);
  }, [bookings, groupBy, subGroupBy]);

  // Sort groups for better display
  const sortedGroups = useMemo(() => {
    if (subGroupBy !== "none") {
      const result: Record<string, [string, BookingScreenData[]][]> = {};
      Object.entries(
        groupedBookings as Record<string, Record<string, BookingScreenData[]>>
      ).forEach(([groupKey, subGroups]) => {
        result[groupKey] = sortGroups(subGroups, subGroupBy);
      });
      return result;
    }
    return sortGroups(
      groupedBookings as Record<string, BookingScreenData[]>,
      groupBy
    );
  }, [groupedBookings, groupBy, subGroupBy]);

  const handleDragStart = (event: DragStartEvent) => {
    setActiveId(event.active.id as string);
  };

  const handleDragEnd = async (event: DragEndEvent) => {
    const { active, over } = event;
    setActiveId(null);

    if (!over || active.id === over.id) {
      return;
    }

    // Extract booking ID and target group from drag event
    const bookingId = active.id as string;
    const targetGroup = over.id as string;

    // Find the booking being moved
    const booking = bookings.find((b) => b.id === bookingId);
    if (!booking) return;

    // Check if the field can be updated via drag and drop
    if (!canUpdateField(groupBy)) {
      console.warn(`Cannot update field "${groupBy}" via drag and drop`);
      return;
    }

    // Get update payload
    const updates = getUpdatePayload(groupBy, targetGroup);

    // Only update if there are actual changes
    if (Object.keys(updates).length > 0) {
      try {
        await onBookingUpdate(bookingId, updates);
      } catch (error) {
        console.error("Failed to update booking:", error);
      }
    }
  };

  // Get the active booking for drag overlay
  const activeBooking = activeId
    ? bookings.find((b) => b.id === activeId)
    : null;

  if (isLoading) {
    return (
      <div className="p-6">
        <div className="animate-pulse space-y-4">
          <div className="h-8 bg-gray-200 rounded w-1/4"></div>
          <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-4 gap-4">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="h-64 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <>
      {/* Controls */}
      <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4 px-6 py-3">
        <Heading level="h3">Kanban Board</Heading>
        <KanbanGroupControls
          groupBy={groupBy}
          subGroupBy={subGroupBy}
          groupByOptions={groupByOptions}
          subGroupByOptions={subGroupByOptions}
          onGroupByChange={setGroupBy}
          onSubGroupByChange={setSubGroupBy}
        />
      </div>
      {/* Kanban Board */}
      <DndContext
        sensors={sensors}
        collisionDetection={closestCorners}
        onDragStart={handleDragStart}
        onDragEnd={handleDragEnd}
      >
        <div className="flex gap-4 md:gap-4 overflow-x-auto h-[640px] px-6 py-3">
          {subGroupBy === "none"
            ? // Simple grouping
              (sortedGroups as [string, BookingScreenData[]][]).map(
                ([groupKey, groupBookings]) => (
                  <KanbanColumn
                    key={groupKey}
                    id={groupKey}
                    title={groupKey}
                    bookings={groupBookings}
                    onBookingClick={onBookingClick}
                  />
                )
              )
            : // Sub-grouping
              Object.entries(
                sortedGroups as Record<string, [string, BookingScreenData[]][]>
              ).map(([groupKey, subGroupEntries]) => (
                <div key={groupKey} className="flex flex-col gap-4 min-w-0">
                  <Heading
                    level="h4"
                    className="text-sm font-medium text-gray-600 px-2"
                  >
                    {groupKey}
                  </Heading>
                  <div className="flex gap-4">
                    {subGroupEntries.map(([subGroupKey, subGroupBookings]) => (
                      <KanbanColumn
                        key={`${groupKey}-${subGroupKey}`}
                        id={`${groupKey}-${subGroupKey}`}
                        title={subGroupKey}
                        bookings={subGroupBookings}
                        onBookingClick={onBookingClick}
                        isSubGroup
                      />
                    ))}
                  </div>
                </div>
              ))}
        </div>

        <DragOverlay>
          {activeBooking ? (
            <KanbanCard booking={activeBooking} onClick={() => {}} isDragging />
          ) : null}
        </DragOverlay>
      </DndContext>
    </>
  );
};
