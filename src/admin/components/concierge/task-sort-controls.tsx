import React from "react";
import { useSearchParams } from "react-router-dom";
import {
  Button,
  DropdownMenu,
} from "@camped-ai/ui";
import {
  ArrowUpDown,
  ArrowUp,
  ArrowDown,
  ChevronDown,
} from "lucide-react";

export interface SortState {
  sortBy: string;
  sortOrder: "asc" | "desc";
}

interface TaskSortControlsProps {
  onSortChange: (sort: SortState) => void;
  className?: string;
}

const sortOptions = [
  { value: "created_at", label: "Created Date" },
  { value: "due_date", label: "Due Date" },
  { value: "title", label: "Title" },
  { value: "status", label: "Status" },
  { value: "priority", label: "Priority" },
  { value: "updated_at", label: "Last Updated" },
];

export const TaskSortControls: React.FC<TaskSortControlsProps> = ({
  onSortChange,
  className = "",
}) => {
  const [searchParams, setSearchParams] = useSearchParams();
  
  const currentSortBy = searchParams.get("order") || "created_at";
  const currentSortOrder = (searchParams.get("sort_order") || "desc") as "asc" | "desc";

  const handleSortChange = (sortBy: string, sortOrder: "asc" | "desc") => {
    const newSearchParams = new URLSearchParams(searchParams);
    newSearchParams.set("order", sortBy);
    newSearchParams.set("sort_order", sortOrder);
    
    // Reset to page 1 when sort changes
    newSearchParams.delete("page");
    
    setSearchParams(newSearchParams);
    onSortChange({ sortBy, sortOrder });
  };

  const toggleSortOrder = () => {
    const newOrder = currentSortOrder === "asc" ? "desc" : "asc";
    handleSortChange(currentSortBy, newOrder);
  };

  const getCurrentSortLabel = () => {
    const option = sortOptions.find(opt => opt.value === currentSortBy);
    return option?.label || "Created Date";
  };

  const getSortIcon = () => {
    if (currentSortOrder === "asc") {
      return <ArrowUp className="h-4 w-4" />;
    } else {
      return <ArrowDown className="h-4 w-4" />;
    }
  };

  return (
    <div className={`flex items-center gap-2 ${className}`}>
      {/* Sort Field Dropdown */}
      <DropdownMenu>
        <DropdownMenu.Trigger asChild>
          <Button variant="secondary" size="small">
            <ArrowUpDown className="h-4 w-4 mr-2" />
            Sort by: {getCurrentSortLabel()}
            <ChevronDown className="h-4 w-4 ml-1" />
          </Button>
        </DropdownMenu.Trigger>
        <DropdownMenu.Content>
          {sortOptions.map((option) => (
            <DropdownMenu.Item
              key={option.value}
              onClick={() => handleSortChange(option.value, currentSortOrder)}
              className={currentSortBy === option.value ? "bg-ui-bg-subtle" : ""}
            >
              {option.label}
              {currentSortBy === option.value && (
                <span className="ml-auto">
                  {getSortIcon()}
                </span>
              )}
            </DropdownMenu.Item>
          ))}
        </DropdownMenu.Content>
      </DropdownMenu>

      {/* Sort Direction Toggle */}
      <Button
        variant="secondary"
        size="small"
        onClick={toggleSortOrder}
        title={`Sort ${currentSortOrder === "asc" ? "descending" : "ascending"}`}
      >
        {getSortIcon()}
      </Button>
    </div>
  );
};
