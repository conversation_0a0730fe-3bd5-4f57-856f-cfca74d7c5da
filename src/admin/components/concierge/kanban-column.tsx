import React from "react";
import { useDroppable } from "@dnd-kit/core";
import { SortableContext, verticalListSortingStrategy } from "@dnd-kit/sortable";
import { Heading, Text, Badge } from "@camped-ai/ui";
import {
  Clock,
  Pause,
  AlertTriangle,
  CheckCircle,
  XCircle,
  User,
  Calendar,
  MapPin,
  Users,
  Folder,
} from "lucide-react";
import { BookingScreenData } from "../../routes/concierge-management/bookings/loader";
import { KanbanCard } from "./kanban-card";

// Get appropriate icon for different group types
const getGroupIcon = (title: string, isSubGroup?: boolean) => {
  const lowerTitle = title.toLowerCase();

  // Status-based icons
  if (lowerTitle.includes('pending') || lowerTitle.includes('not started')) return Clock;
  if (lowerTitle.includes('progress') || lowerTitle.includes('active')) return Pause;
  if (lowerTitle.includes('review') || lowerTitle.includes('waiting')) return AlertTriangle;
  if (lowerTitle.includes('completed') || lowerTitle.includes('done')) return CheckCircle;
  if (lowerTitle.includes('cancelled') || lowerTitle.includes('canceled')) return XCircle;

  // Assignment-based icons
  if (lowerTitle.includes('unassigned') || lowerTitle === 'unassigned') return User;

  // Date-based icons
  if (lowerTitle.includes('date') || /\d{4}/.test(title)) return Calendar;

  // Hotel-based icons
  if (lowerTitle.includes('hotel') || lowerTitle.includes('property')) return MapPin;

  // Customer-based icons
  if (lowerTitle.includes('customer') || lowerTitle.includes('guest')) return Users;

  // Default icon
  return isSubGroup ? Folder : Calendar;
};

// Get appropriate color for different group types
const getGroupColor = (title: string) => {
  const lowerTitle = title.toLowerCase();

  // Status-based colors
  if (lowerTitle.includes('pending') || lowerTitle.includes('not started')) return 'orange';
  if (lowerTitle.includes('progress') || lowerTitle.includes('active')) return 'blue';
  if (lowerTitle.includes('review') || lowerTitle.includes('waiting')) return 'purple';
  if (lowerTitle.includes('completed') || lowerTitle.includes('done')) return 'green';
  if (lowerTitle.includes('cancelled') || lowerTitle.includes('canceled')) return 'red';

  // Default color
  return 'gray';
};

// Get background and border colors
const getGroupStyles = (title: string) => {
  const color = getGroupColor(title);

  const colorMap = {
    orange: { bgColor: 'bg-orange-50', borderColor: 'border-orange-200' },
    blue: { bgColor: 'bg-blue-50', borderColor: 'border-blue-200' },
    purple: { bgColor: 'bg-purple-50', borderColor: 'border-purple-200' },
    green: { bgColor: 'bg-green-50', borderColor: 'border-green-200' },
    red: { bgColor: 'bg-red-50', borderColor: 'border-red-200' },
    gray: { bgColor: 'bg-gray-50', borderColor: 'border-gray-200' },
  };

  return colorMap[color as keyof typeof colorMap] || colorMap.gray;
};

interface KanbanColumnProps {
  id: string;
  title: string;
  bookings: BookingScreenData[];
  onBookingClick: (booking: BookingScreenData) => void;
  isSubGroup?: boolean;
}

export const KanbanColumn: React.FC<KanbanColumnProps> = ({
  id,
  title,
  bookings,
  onBookingClick,
  isSubGroup = false,
}) => {
  const IconComponent = getGroupIcon(title, isSubGroup);
  const color = getGroupColor(title);
  const { bgColor, borderColor } = getGroupStyles(title);

  const { setNodeRef, isOver } = useDroppable({
    id: id,
    data: {
      type: "column",
      id,
    },
  });

  const bookingIds = bookings.map(booking => booking.id);

  return (
    <div className="flex flex-col h-full min-w-[280px] max-w-[320px]">
      {/* Column Header */}
      <div className={`
        flex items-center justify-between p-4 rounded-t-lg border-t border-l border-r
        ${bgColor} ${borderColor}
      `}>
        <div className="flex items-center gap-2">
          <IconComponent className="h-4 w-4" />
          <Heading level="h3" className="text-sm font-medium">
            {title}
          </Heading>
        </div>
        <Badge color={color} size="small">
          {bookings.length}
        </Badge>
      </div>

      {/* Column Content */}
      <div
        ref={setNodeRef}
        className={`
          flex-1 p-3 border-l border-r border-b rounded-b-lg min-h-[400px]
          ${bgColor} ${borderColor}
          ${isOver ? 'bg-opacity-80 ring-2 ring-blue-300' : ''}
          transition-all duration-200
        `}
      >
        <SortableContext items={bookingIds} strategy={verticalListSortingStrategy}>
          <div className="space-y-3">
            {bookings.length === 0 ? (
              <div className="flex flex-col items-center justify-center py-8 text-center">
                <IconComponent className="h-8 w-8 text-ui-fg-muted mb-2" />
                <Text className="text-sm text-ui-fg-muted">
                  No {title.toLowerCase()} bookings
                </Text>
              </div>
            ) : (
              bookings.map((booking) => (
                <KanbanCard
                  key={booking.id}
                  booking={booking}
                  onClick={() => onBookingClick(booking)}
                />
              ))
            )}
          </div>
        </SortableContext>
      </div>
    </div>
  );
};