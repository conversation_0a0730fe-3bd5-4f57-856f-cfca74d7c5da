import React from "react";
import { useSortable } from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";
import { Badge, Text, IconButton } from "@camped-ai/ui";
import {
  Calendar,
  User,
  MoreHorizontal,
  AlertTriangle,
  Clock,
  CheckCircle,
  XCircle,
  Pause,
  Loader2
} from "lucide-react";
import { format } from "date-fns";
import { useNavigate } from "react-router-dom";
import { TaskScreenData } from "../../routes/concierge-management/tasks/loader";

// Status and priority color mappings
const statusColors = {
  pending: "orange",
  in_progress: "blue", 
  review: "purple",
  completed: "green",
  cancelled: "red",
} as const;

const priorityColors = {
  low: "grey",
  medium: "blue",
  high: "orange", 
  urgent: "red",
} as const;

const statusIcons = {
  pending: Clock,
  in_progress: Pause,
  review: AlertTriangle,
  completed: CheckCircle,
  cancelled: XCircle,
} as const;

interface KanbanTaskCardProps {
  task: TaskScreenData;
  onEdit?: (task: TaskScreenData) => void;
  onDelete?: (task: TaskScreenData) => void;
  onCopyId?: (task: TaskScreenData) => void;
  hasEditPermission?: boolean;
  hasDeletePermission?: boolean;
  isDragging?: boolean;
  isUpdating?: boolean;
}

export const KanbanTaskCard: React.FC<KanbanTaskCardProps> = ({
  task,
  onEdit,
  onDelete,
  onCopyId,
  hasEditPermission = false,
  hasDeletePermission = false,
  isDragging = false,
  isUpdating = false,
}) => {
  const navigate = useNavigate();
  
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging: isSortableDragging,
  } = useSortable({
    id: task.id,
    data: {
      type: "task",
      task,
    },
  });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
  };

  const getStatusBadgeVariant = (status: string) => {
    return statusColors[status as keyof typeof statusColors] || "grey";
  };

  const getPriorityBadgeVariant = (priority: string) => {
    return priorityColors[priority as keyof typeof priorityColors] || "grey";
  };

  const getPriorityIcon = (priority: string) => {
    switch (priority) {
      case "urgent":
      case "high":
        return <AlertTriangle className="h-3 w-3" />;
      default:
        return null;
    }
  };

  const getStatusIcon = (status: string) => {
    const IconComponent = statusIcons[status as keyof typeof statusIcons];
    return IconComponent ? <IconComponent className="h-3 w-3" /> : null;
  };

  const handleCardClick = (e: React.MouseEvent) => {
    // Don't navigate if clicking on action buttons
    if ((e.target as HTMLElement).closest('button')) {
      return;
    }
    navigate(`/concierge-management/tasks/${task.id}`);
  };

  const handleEdit = (e: React.MouseEvent) => {
    e.stopPropagation();
    onEdit?.(task);
  };

  const handleDelete = (e: React.MouseEvent) => {
    e.stopPropagation();
    onDelete?.(task);
  };

  const handleCopyId = (e: React.MouseEvent) => {
    e.stopPropagation();
    onCopyId?.(task);
  };

  return (
    <div
      ref={setNodeRef}
      style={style}
      {...attributes}
      {...listeners}
      className={`
        bg-white border border-ui-border-base rounded-lg p-3 shadow-sm
        hover:shadow-md transition-all duration-200 cursor-pointer
        ${isDragging || isSortableDragging ? 'opacity-50 rotate-2 scale-105' : ''}
        ${isDragging || isSortableDragging ? 'z-50' : ''}
        ${isUpdating ? 'opacity-75 pointer-events-none' : ''}
      `}
      onClick={handleCardClick}
    >
      {/* Header with priority and actions */}
      <div className="flex items-start justify-between mb-2">
        <div className="flex items-center gap-1">
          {getPriorityIcon(task.priority)}
          <Badge color={getPriorityBadgeVariant(task.priority)} size="small">
            {task.priority}
          </Badge>
          {isUpdating && (
            <Loader2 className="h-3 w-3 animate-spin text-ui-fg-muted" />
          )}
        </div>
        
        {(hasEditPermission || hasDeletePermission) && (
          <IconButton
            size="small"
            variant="transparent"
            onClick={handleEdit}
            className="opacity-0 group-hover:opacity-100 transition-opacity"
          >
            <MoreHorizontal className="h-3 w-3" />
          </IconButton>
        )}
      </div>

      {/* Task title */}
      <Text className="font-medium text-sm mb-2 line-clamp-2">
        {task.title}
      </Text>

      {/* Task description */}
      {task.description && (
        <Text className="text-xs text-ui-fg-subtle mb-3 line-clamp-2">
          {task.description}
        </Text>
      )}

      {/* Status badge */}
      <div className="flex items-center gap-1 mb-3">
        {getStatusIcon(task.status)}
        <Badge color={getStatusBadgeVariant(task.status)} size="small">
          {task.status.replace('_', ' ')}
        </Badge>
      </div>

      {/* Footer with metadata */}
      <div className="flex items-center justify-between text-xs text-ui-fg-subtle">
        {/* Due date */}
        {task.due_date && (
          <div className="flex items-center gap-1">
            <Calendar className="h-3 w-3" />
            <span>{format(new Date(task.due_date), "MMM dd")}</span>
          </div>
        )}

        {/* Assigned to */}
        {task.assigned_to && (
          <div className="flex items-center gap-1">
            <User className="h-3 w-3" />
            <span className="truncate max-w-[60px]">{task.assigned_to}</span>
          </div>
        )}

        {/* Created date fallback */}
        {!task.due_date && !task.assigned_to && (
          <div className="flex items-center gap-1">
            <Calendar className="h-3 w-3" />
            <span>{format(new Date(task.created_at), "MMM dd")}</span>
          </div>
        )}
      </div>
    </div>
  );
};

export default KanbanTaskCard;
