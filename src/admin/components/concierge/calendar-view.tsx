import React, { useState, useMemo } from "react";
import { Head<PERSON>, But<PERSON>, Text } from "@camped-ai/ui";
import { ChevronLeft, ChevronRight, Calendar as CalendarIcon } from "lucide-react";
import {
  format,
  startOfMonth,
  endOfMonth,
  startOfWeek,
  endOfWeek,
  addDays,
  addMonths,
  subMonths,
  addWeeks,
  subWeeks,
  isSameMonth,
  isSameDay,
  isWithinInterval,
  parseISO,
} from "date-fns";
import { BookingScreenData } from "../../routes/concierge-management/bookings/loader";
import { CalendarEvent } from "./calendar-event";
import { CalendarBookingSpan } from "./calendar-booking-span";
import { CalendarViewControls } from "./calendar-view-controls";

export type CalendarViewType = "month" | "week" | "day";

interface CalendarViewProps {
  bookings: BookingScreenData[];
  onBookingClick: (booking: BookingScreenData) => void;
  isLoading?: boolean;
}

export const CalendarView: React.FC<CalendarViewProps> = ({
  bookings,
  onBookingClick,
  isLoading = false,
}) => {
  const [currentDate, setCurrentDate] = useState(new Date());
  const [viewType, setViewType] = useState<CalendarViewType>("month");

  // Navigation functions
  const navigatePrevious = () => {
    if (viewType === "month") {
      setCurrentDate(subMonths(currentDate, 1));
    } else if (viewType === "week") {
      setCurrentDate(subWeeks(currentDate, 1));
    } else {
      setCurrentDate(addDays(currentDate, -1));
    }
  };

  const navigateNext = () => {
    if (viewType === "month") {
      setCurrentDate(addMonths(currentDate, 1));
    } else if (viewType === "week") {
      setCurrentDate(addWeeks(currentDate, 1));
    } else {
      setCurrentDate(addDays(currentDate, 1));
    }
  };

  const navigateToday = () => {
    setCurrentDate(new Date());
  };

  // Get calendar events from bookings
  const calendarEvents = useMemo(() => {
    return bookings
      .filter(booking => booking.check_in_date && booking.check_out_date)
      .map(booking => ({
        id: booking.id,
        title: `${booking.customer_first_name} ${booking.customer_last_name}`,
        subtitle: booking.hotel_name || "Hotel TBD",
        startDate: parseISO(booking.check_in_date!),
        endDate: parseISO(booking.check_out_date!),
        booking,
        status: booking.status,
      }));
  }, [bookings]);

  // Get events for a specific date
  const getEventsForDate = (date: Date) => {
    return calendarEvents.filter(event =>
      isWithinInterval(date, { start: event.startDate, end: event.endDate }) ||
      isSameDay(date, event.startDate) ||
      isSameDay(date, event.endDate)
    );
  };

  // Group overlapping bookings for proper stacking
  const getOverlappingGroups = (events: typeof calendarEvents, dateRange: { start: Date; end: Date }) => {
    const visibleEvents = events.filter(event =>
      event.startDate <= dateRange.end && event.endDate >= dateRange.start
    );

    const groups: Array<Array<typeof calendarEvents[0]>> = [];

    visibleEvents.forEach(event => {
      let addedToGroup = false;

      // Try to add to existing group
      for (const group of groups) {
        const hasOverlap = group.some(groupEvent =>
          event.startDate < groupEvent.endDate && event.endDate > groupEvent.startDate
        );

        if (hasOverlap) {
          group.push(event);
          addedToGroup = true;
          break;
        }
      }

      // Create new group if no overlap found
      if (!addedToGroup) {
        groups.push([event]);
      }
    });

    return groups;
  };

  // Get date range for current view
  const getDateRange = () => {
    if (viewType === "month") {
      const monthStart = startOfMonth(currentDate);
      const monthEnd = endOfMonth(currentDate);
      return {
        start: startOfWeek(monthStart),
        end: endOfWeek(monthEnd),
      };
    } else if (viewType === "week") {
      return {
        start: startOfWeek(currentDate),
        end: endOfWeek(currentDate),
      };
    } else {
      return {
        start: currentDate,
        end: currentDate,
      };
    }
  };

  const dateRange = getDateRange();

  // Generate calendar days
  const calendarDays = useMemo(() => {
    const days = [];
    let day = dateRange.start;

    while (day <= dateRange.end) {
      days.push(day);
      day = addDays(day, 1);
    }

    return days;
  }, [dateRange]);

  // Format header title
  const getHeaderTitle = () => {
    if (viewType === "month") {
      return format(currentDate, "MMMM yyyy");
    } else if (viewType === "week") {
      const weekStart = startOfWeek(currentDate);
      const weekEnd = endOfWeek(currentDate);
      return `${format(weekStart, "MMM d")} - ${format(weekEnd, "MMM d, yyyy")}`;
    } else {
      return format(currentDate, "EEEE, MMMM d, yyyy");
    }
  };

  if (isLoading) {
    return (
      <div className="p-6">
        <div className="animate-pulse space-y-4">
          <div className="h-8 bg-gray-200 rounded w-1/3"></div>
          <div className="h-96 bg-gray-200 rounded"></div>
        </div>
      </div>
    );
  }

  return (
    <>
      {/* Header */}
      <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4 px-6 py-3">
        <div className="flex flex-col sm:flex-row items-start sm:items-center gap-4">
          <Heading level="h3" className="text-lg sm:text-xl">{getHeaderTitle()}</Heading>
          <div className="flex items-center gap-2">
            <Button
              variant="secondary"
              size="small"
              onClick={navigatePrevious}
            >
              <ChevronLeft className="w-4 h-4" />
            </Button>
            <Button
              variant="secondary"
              size="small"
              onClick={navigateToday}
            >
              Today
            </Button>
            <Button
              variant="secondary"
              size="small"
              onClick={navigateNext}
            >
              <ChevronRight className="w-4 h-4" />
            </Button>
          </div>
        </div>

        <CalendarViewControls
          viewType={viewType}
          onViewTypeChange={setViewType}
        />
      </div>

      {/* Calendar Grid */}
      <div className="flex-1 overflow-auto">
        {viewType === "month" && (
          <div className="relative">
            {/* Calendar Grid */}
            <div className="grid grid-cols-7 gap-px bg-gray-200 rounded-lg overflow-hidden">
              {/* Day headers */}
              {["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"].map(day => (
                <div key={day} className="bg-gray-50 p-3 text-center">
                  <Text className="text-sm font-medium text-gray-600">{day}</Text>
                </div>
              ))}

              {/* Calendar days */}
              {calendarDays.map(day => {
                const isCurrentMonth = isSameMonth(day, currentDate);
                const isToday = isSameDay(day, new Date());

                return (
                  <div
                    key={day.toISOString()}
                    className={`bg-white p-2 min-h-[120px] relative ${
                      !isCurrentMonth ? "bg-gray-50 text-gray-400" : ""
                    } ${isToday ? "bg-blue-50" : ""}`}
                  >
                    <div className="flex items-center justify-between mb-2">
                      <Text className={`text-sm ${isToday ? "font-bold text-blue-600" : ""}`}>
                        {format(day, "d")}
                      </Text>
                    </div>
                    {/* Space reserved for booking spans */}
                    <div className="h-20"></div>
                  </div>
                );
              })}
            </div>

            {/* Booking Spans Overlay */}
            <div className="absolute inset-0 pointer-events-none" style={{ top: '60px' }}>
              <div className="grid grid-cols-7 gap-px h-full">
                {/* Render booking spans */}
                {(() => {
                  const overlappingGroups = getOverlappingGroups(calendarEvents, dateRange);

                  return overlappingGroups.map((group, groupIndex) =>
                    group.map((event, eventIndex) => (
                      <div key={event.id} className="pointer-events-auto">
                        <CalendarBookingSpan
                          booking={event.booking}
                          startDate={event.startDate}
                          endDate={event.endDate}
                          onClick={() => onBookingClick(event.booking)}
                          spanIndex={eventIndex}
                          totalSpans={group.length}
                          gridStartDay={dateRange.start}
                          gridEndDay={dateRange.end}
                          daysInWeek={7}
                        />
                      </div>
                    ))
                  );
                })()}
              </div>
            </div>
          </div>
        )}

        {viewType === "week" && (
          <div className="grid grid-cols-7 gap-px bg-gray-200 rounded-lg overflow-hidden">
            {/* Day headers */}
            {calendarDays.map(day => (
              <div key={day.toISOString()} className="bg-gray-50 p-3 text-center">
                <Text className="text-sm font-medium text-gray-600">
                  {format(day, "EEE")}
                </Text>
                <Text className="text-lg font-bold">
                  {format(day, "d")}
                </Text>
              </div>
            ))}

            {/* Week view content */}
            {calendarDays.map(day => {
              const dayEvents = getEventsForDate(day);
              const isToday = isSameDay(day, new Date());

              return (
                <div
                  key={day.toISOString()}
                  className={`bg-white p-3 min-h-[400px] ${
                    isToday ? "bg-blue-50" : ""
                  }`}
                >
                  <div className="space-y-2">
                    {dayEvents.map(event => (
                      <CalendarEvent
                        key={event.id}
                        event={event}
                        onClick={() => onBookingClick(event.booking)}
                      />
                    ))}
                  </div>
                </div>
              );
            })}
          </div>
        )}

        {viewType === "day" && (
          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <div className="space-y-4">
              {getEventsForDate(currentDate).map(event => (
                <CalendarEvent
                  key={event.id}
                  event={event}
                  onClick={() => onBookingClick(event.booking)}
                  detailed
                />
              ))}
              {getEventsForDate(currentDate).length === 0 && (
                <div className="text-center py-12">
                  <CalendarIcon className="w-12 h-12 text-gray-300 mx-auto mb-4" />
                  <Text className="text-gray-500">No bookings for this day</Text>
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    </>
  );
};
