import React from "react";
import { Text, Badge, Tooltip } from "@camped-ai/ui";
import { MapPin, DollarSign, User } from "lucide-react";
import { format } from "date-fns";
import { BookingScreenData } from "../../routes/concierge-management/bookings/loader";
import { UserDisplayName } from "./user-display-name";

interface CalendarEventData {
  id: string;
  title: string;
  subtitle: string;
  startDate: Date;
  endDate: Date;
  booking: BookingScreenData;
  status: string;
}

interface CalendarEventProps {
  event: CalendarEventData;
  onClick: () => void;
  compact?: boolean;
  detailed?: boolean;
}

export const CalendarEvent: React.FC<CalendarEventProps> = ({
  event,
  onClick,
  compact = false,
  detailed = false,
}) => {
  // Get status color
  const getStatusColor = (status: string) => {
    const statusColors: Record<string, string> = {
      pending: "orange",
      confirmed: "blue",
      in_progress: "blue",
      completed: "green",
      cancelled: "red",
      on_hold: "yellow",
    };
    return statusColors[status?.toLowerCase()] || "gray";
  };

  // Format currency
  const formatCurrency = (amount: number | undefined, currencyCode: string = "USD") => {
    if (!amount) return "N/A";
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: currencyCode,
    }).format(amount);
  };

  // Calculate duration in nights
  const calculateNights = () => {
    const diffTime = event.endDate.getTime() - event.startDate.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays;
  };

  const statusColor = getStatusColor(event.status);
  const nights = calculateNights();

  if (compact) {
    return (
      <Tooltip
        content={
          <div className="p-2 space-y-1">
            <Text className="font-medium text-sm">{event.title}</Text>
            <Text className="text-xs text-gray-600">{event.subtitle}</Text>
            <Text className="text-xs">
              {format(event.startDate, "MMM d")} - {format(event.endDate, "MMM d")} ({nights} nights)
            </Text>
            {event.booking.order_total && (
              <Text className="text-xs">
                {formatCurrency(event.booking.order_total, event.booking.order_currency_code)}
              </Text>
            )}
            <Badge size="small" color={statusColor} className="text-xs">
              {event.status}
            </Badge>
          </div>
        }
      >
        <div
          onClick={onClick}
          className={`p-1 rounded text-xs cursor-pointer hover:opacity-80 transition-opacity ${
            statusColor === "blue" ? "bg-blue-100 text-blue-800" :
            statusColor === "green" ? "bg-green-100 text-green-800" :
            statusColor === "orange" ? "bg-orange-100 text-orange-800" :
            statusColor === "red" ? "bg-red-100 text-red-800" :
            statusColor === "yellow" ? "bg-yellow-100 text-yellow-800" :
            "bg-gray-100 text-gray-800"
          }`}
        >
          <Text className="truncate text-xs font-medium">
            {event.title}
          </Text>
        </div>
      </Tooltip>
    );
  }

  if (detailed) {
    return (
      <div
        onClick={onClick}
        className="bg-white border border-gray-200 rounded-lg p-4 cursor-pointer hover:shadow-md transition-shadow"
      >
        <div className="flex items-start justify-between mb-3">
          <div className="flex-1">
            <Text className="font-medium">{event.title}</Text>
            <Text className="text-sm text-gray-600">
              Booking #{event.booking.order?.display_id || event.booking.order_id}
            </Text>
          </div>
          <Badge size="small" color={statusColor}>
            {event.status}
          </Badge>
        </div>

        <div className="space-y-2">
          {event.subtitle && (
            <div className="flex items-center gap-2">
              <MapPin className="w-4 h-4 text-gray-400" />
              <Text className="text-sm text-gray-600">{event.subtitle}</Text>
            </div>
          )}

          <div className="flex items-center gap-2">
            <Text className="text-sm text-gray-600">
              {format(event.startDate, "MMM d, yyyy")} - {format(event.endDate, "MMM d, yyyy")} ({nights} nights)
            </Text>
          </div>

          {event.booking.order_total && (
            <div className="flex items-center gap-2">
              <DollarSign className="w-4 h-4 text-gray-400" />
              <Text className="text-sm text-gray-600">
                {formatCurrency(event.booking.order_total, event.booking.order_currency_code)}
              </Text>
            </div>
          )}

          {event.booking.assigned_to && (
            <div className="flex items-center gap-2">
              <User className="w-4 h-4 text-gray-400" />
              <Text className="text-sm text-gray-600">
                <UserDisplayName userId={event.booking.assigned_to} />
              </Text>
            </div>
          )}

          {event.booking.concierge_order_items && event.booking.concierge_order_items.length > 0 && (
            <div className="pt-2 border-t border-gray-100">
              <Badge size="small" color="blue" className="text-xs">
                {event.booking.concierge_order_items.length} add-on{event.booking.concierge_order_items.length !== 1 ? 's' : ''}
              </Badge>
            </div>
          )}
        </div>
      </div>
    );
  }

  // Default view
  return (
    <div
      onClick={onClick}
      className={`p-2 rounded cursor-pointer hover:opacity-80 transition-opacity ${
        statusColor === "blue" ? "bg-blue-100 text-blue-800" :
        statusColor === "green" ? "bg-green-100 text-green-800" :
        statusColor === "orange" ? "bg-orange-100 text-orange-800" :
        statusColor === "red" ? "bg-red-100 text-red-800" :
        statusColor === "yellow" ? "bg-yellow-100 text-yellow-800" :
        "bg-gray-100 text-gray-800"
      }`}
    >
      <div className="flex items-center justify-between mb-1">
        <Text className="text-sm font-medium truncate">{event.title}</Text>
        <Badge size="small" color={statusColor} className="text-xs ml-2">
          {event.status}
        </Badge>
      </div>
      
      <Text className="text-xs text-gray-600 truncate">{event.subtitle}</Text>
      
      <Text className="text-xs mt-1">
        {format(event.startDate, "MMM d")} - {format(event.endDate, "MMM d")} ({nights}n)
      </Text>

      {event.booking.order_total && (
        <Text className="text-xs font-medium mt-1">
          {formatCurrency(event.booking.order_total, event.booking.order_currency_code)}
        </Text>
      )}
    </div>
  );
};
