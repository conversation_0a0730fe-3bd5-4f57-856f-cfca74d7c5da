import React from "react";
import { <PERSON><PERSON>, <PERSON><PERSON>, Text } from "@camped-ai/ui";
import { X } from "lucide-react";
import { BookingFilterState } from "./booking-filter-controls";

interface FilterSummaryProps {
  filters: BookingFilterState;
  onRemoveFilter: (filterKey: keyof BookingFilterState, value?: string) => void;
  onClearAll: () => void;
  className?: string;
}

export const FilterSummary: React.FC<FilterSummaryProps> = ({
  filters,
  onRemoveFilter,
  onClearAll,
  className = "",
}) => {
  const getActiveFilters = () => {
    const activeFilters: Array<{
      key: keyof BookingFilterState;
      label: string;
      value?: string;
      removable: boolean;
    }> = [];

    // Search filter
    if (filters.search) {
      activeFilters.push({
        key: "search",
        label: `Search: "${filters.search}"`,
        removable: true,
      });
    }

    // Customer name filter
    if (filters.customer_name) {
      activeFilters.push({
        key: "customer_name",
        label: `Customer: "${filters.customer_name}"`,
        removable: true,
      });
    }

    // Hotel filters
    if (filters.hotel_id && filters.hotel_id.length > 0) {
      filters.hotel_id.forEach(hotelId => {
        activeFilters.push({
          key: "hotel_id",
          label: `Hotel: ${hotelId}`,
          value: hotelId,
          removable: true,
        });
      });
    }

    // Assignee filters
    if (filters.assigned_to && filters.assigned_to.length > 0) {
      filters.assigned_to.forEach(assigneeId => {
        const displayName = assigneeId === "unassigned" ? "Unassigned" : assigneeId;
        activeFilters.push({
          key: "assigned_to",
          label: `Assigned: ${displayName}`,
          value: assigneeId,
          removable: true,
        });
      });
    }

    // Status filters
    if (filters.status && filters.status.length > 0) {
      filters.status.forEach(status => {
        const displayStatus = status.replace(/_/g, " ").replace(/\b\w/g, l => l.toUpperCase());
        activeFilters.push({
          key: "status",
          label: `Status: ${displayStatus}`,
          value: status,
          removable: true,
        });
      });
    }

    // Date range filters
    if (filters.check_in_date_gte || filters.check_in_date_lte) {
      let dateLabel = "Check-in: ";
      if (filters.check_in_date_gte && filters.check_in_date_lte) {
        dateLabel += `${filters.check_in_date_gte} to ${filters.check_in_date_lte}`;
      } else if (filters.check_in_date_gte) {
        dateLabel += `from ${filters.check_in_date_gte}`;
      } else if (filters.check_in_date_lte) {
        dateLabel += `until ${filters.check_in_date_lte}`;
      }
      
      activeFilters.push({
        key: "check_in_date_gte",
        label: dateLabel,
        removable: true,
      });
    }

    if (filters.check_out_date_gte || filters.check_out_date_lte) {
      let dateLabel = "Check-out: ";
      if (filters.check_out_date_gte && filters.check_out_date_lte) {
        dateLabel += `${filters.check_out_date_gte} to ${filters.check_out_date_lte}`;
      } else if (filters.check_out_date_gte) {
        dateLabel += `from ${filters.check_out_date_gte}`;
      } else if (filters.check_out_date_lte) {
        dateLabel += `until ${filters.check_out_date_lte}`;
      }
      
      activeFilters.push({
        key: "check_out_date_gte",
        label: dateLabel,
        removable: true,
      });
    }

    return activeFilters;
  };

  const activeFilters = getActiveFilters();

  if (activeFilters.length === 0) {
    return null;
  }

  return (
    <div className={`flex flex-wrap items-center gap-2 p-3 bg-blue-50 border-b border-blue-200 ${className}`}>
      <Text className="text-sm font-medium text-blue-800 mr-2">
        Active Filters ({activeFilters.length}):
      </Text>
      
      {activeFilters.map((filter, index) => (
        <Badge
          key={`${filter.key}-${filter.value || index}`}
          size="small"
          color="blue"
          className="flex items-center gap-1 px-2 py-1"
        >
          <Text className="text-xs">{filter.label}</Text>
          {filter.removable && (
            <button
              onClick={() => onRemoveFilter(filter.key, filter.value)}
              className="ml-1 hover:bg-blue-200 rounded-full p-0.5 transition-colors"
              aria-label={`Remove ${filter.label} filter`}
            >
              <X className="h-3 w-3" />
            </button>
          )}
        </Badge>
      ))}

      <Button
        variant="secondary"
        size="small"
        onClick={onClearAll}
        className="ml-2 text-xs"
      >
        Clear All
      </Button>
    </div>
  );
};
