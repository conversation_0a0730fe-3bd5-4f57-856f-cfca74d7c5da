import React, { useState } from "react";
import {
  Button,
  Input,
  Text,
  Badge,
} from "@camped-ai/ui";
import { Loader2, Search, Grid, List, Calendar, Package } from "lucide-react";
import { format as formatDateFns, parseISO } from "date-fns";


interface EventFormProps {
  initialData?: any;
  onSubmit: (data: any) => void;
  onCancel: () => void;
}

interface Category {
  id: string;
  name: string;
}

interface SupplierOffering {
  id: string;
  product_service_name: string;
  product_service_custom_fields?: Record<string, any>;
  offering_custom_fields?: Record<string, any>;
  supplier_id?: string;
  active_from?: string;
  active_to?: string;
  availability_notes?: string;
  gross_price?: number;
  net_cost?: number;
  selling_price?: number;
  currency?: string;
  status?: "active" | "inactive";
}

const EventForm: React.FC<EventFormProps> = ({ initialData, onSubmit, onCancel }) => {
  // Form state
  const [selectedCategory, setSelectedCategory] = useState<Category | null>(null);
  const [fromDate, setFromDate] = useState("");
  const [toDate, setToDate] = useState("");
  const [startTime, setStartTime] = useState("09:00");
  const [endTime, setEndTime] = useState("17:00");

  // Categories and offerings state
  const [categories, setCategories] = useState<Category[]>([]);
  const [offerings, setOfferings] = useState<SupplierOffering[]>([]);
  const [filteredOfferings, setFilteredOfferings] = useState<SupplierOffering[]>([]);
  const [selectedOffering, setSelectedOffering] = useState<SupplierOffering | null>(null);

  // UI state
  const [loadingCategories, setLoadingCategories] = useState(false);
  const [loadingOfferings, setLoadingOfferings] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [viewMode, setViewMode] = useState<"cards" | "table">("cards");
  const [error, setError] = useState<string | null>(null);

  // Fetch categories when component mounts
  React.useEffect(() => {
    fetchCategories();
  }, []);

  const fetchCategories = async () => {
    try {
      setLoadingCategories(true);
      setError(null);

      const response = await fetch('/admin/supplier-management/products-services/categories?is_active=true&limit=50', {
        method: 'GET',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch categories: ${response.statusText}`);
      }

      const data = await response.json();
      console.log('Categories fetched:', data);

      if (data.categories && Array.isArray(data.categories)) {
        setCategories(data.categories);
      } else {
        console.warn('Invalid categories data format:', data);
        setCategories([]);
      }
    } catch (err) {
      console.error('Error fetching categories:', err);
      setError(err instanceof Error ? err.message : 'Failed to load categories');
      setCategories([]);
    } finally {
      setLoadingCategories(false);
    }
  };

  // Fetch offerings when category and dates are selected
  const fetchOfferings = async () => {
    if (!selectedCategory || !fromDate || !toDate) {
      return; // Don't fetch if required fields are missing
    }

    try {
      setLoadingOfferings(true);
      setError(null);

      const params = new URLSearchParams({
        status: 'active',
        limit: '1000',
        offset: '0',
        category_id: selectedCategory.id,
      });

      const response = await fetch(`/admin/supplier-management/supplier-offerings?${params.toString()}`, {
        method: 'GET',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch offerings: ${response.statusText}`);
      }

      const data = await response.json();
      console.log('Filtered offerings fetched:', data);

      if (data && data.supplier_offerings && Array.isArray(data.supplier_offerings)) {
        console.log('✅ Offerings loaded successfully:', data.supplier_offerings.length);
        setOfferings(data.supplier_offerings);
        setFilteredOfferings(data.supplier_offerings);
      } else {
        console.warn('Invalid offerings data format:', data);
        setOfferings([]);
        setFilteredOfferings([]);
      }
    } catch (err) {
      console.error('Error fetching offerings:', err);
      setError(err instanceof Error ? err.message : 'Failed to load offerings');
      setOfferings([]);
      setFilteredOfferings([]);
    } finally {
      setLoadingOfferings(false);
    }
  };

  // Filter offerings based on search term
  React.useEffect(() => {
    try {
      if (!Array.isArray(offerings)) {
        setFilteredOfferings([]);
        return;
      }

      if (!searchTerm.trim()) {
        setFilteredOfferings(offerings);
        return;
      }

      const term = searchTerm.toLowerCase();
      const filtered = offerings.filter(offering => {
        if (!offering || typeof offering !== 'object') return false;

        try {
          return (
            (offering.product_service_name && offering.product_service_name.toLowerCase().includes(term)) ||
            (offering.availability_notes && offering.availability_notes.toLowerCase().includes(term)) ||
            (offering.product_service_custom_fields && JSON.stringify(offering.product_service_custom_fields).toLowerCase().includes(term)) ||
            (offering.offering_custom_fields && JSON.stringify(offering.offering_custom_fields).toLowerCase().includes(term))
          );
        } catch (e) {
          console.warn('Error filtering offering:', e);
          return false;
        }
      });
      setFilteredOfferings(filtered);
    } catch (error) {
      console.error('Error in search filter:', error);
      setFilteredOfferings([]);
    }
  }, [searchTerm, offerings]);

  // Trigger API call when category and dates are selected
  React.useEffect(() => {
    if (selectedCategory && fromDate && toDate) {
      fetchOfferings();
    } else {
      setOfferings([]);
      setFilteredOfferings([]);
    }
  }, [selectedCategory, fromDate, toDate]);

  const handleSubmit = () => {
    if (!selectedOffering) {
      setError('Please select an offering');
      return;
    }

    const offeringFormData = {
      category: selectedCategory?.name || "Service",
      type: "Service",
      title: selectedOffering.product_service_name || "",
      notes: selectedOffering.availability_notes || "",
      start_time: startTime,
      end_time: endTime,
      duration: calculateDuration(startTime, endTime),
      timezone: "",
      price: (() => {
        const price = selectedOffering.selling_price || selectedOffering.gross_price || selectedOffering.net_cost;
        return price ? Number(price) : null;
      })(),
      currency: selectedOffering.currency || "CHF",
      media: [],

      // New API fields at top level
      supplier_offering_id: selectedOffering.id,
      supplier_id: selectedOffering.supplier_id,
      category_id: selectedCategory?.id,
      from_date: fromDate,
      to_date: toDate,

      // Keep details for additional data
      details: {
        product_service_custom_fields: selectedOffering.product_service_custom_fields,
        offering_custom_fields: selectedOffering.offering_custom_fields,
      },
    };

    console.log('🚀 Submitting event data:', offeringFormData);
    onSubmit(offeringFormData);
  };

  const calculateDuration = (start: string, end: string) => {
    const startTime = new Date(`2000-01-01T${start}`);
    const endTime = new Date(`2000-01-01T${end}`);
    const diffMs = endTime.getTime() - startTime.getTime();
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffMinutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));

    if (diffMinutes === 0) {
      return `${diffHours} hours`;
    }
    return `${diffHours}h ${diffMinutes}m`;
  };

  const formatPrice = (offering: SupplierOffering) => {
    try {
      if (!offering || typeof offering !== 'object') return 'Price on request';

      const price = offering.selling_price || offering.gross_price || offering.net_cost;
      if (!price || typeof price !== 'number') return 'Price on request';

      const currency = offering.currency || 'CHF';
      return `${currency} ${price.toFixed(2)}`;
    } catch (error) {
      console.warn('Error formatting price:', error);
      return 'Price on request';
    }
  };

  const formatDateRange = (offering: SupplierOffering) => {
    try {
      if (!offering || typeof offering !== 'object') return 'Available';

      if (offering.active_from && offering.active_to) {
        return `${offering.active_from} - ${offering.active_to}`;
      }
      return 'Available';
    } catch (error) {
      console.warn('Error formatting date range:', error);
      return 'Available';
    }
  };

  // Helper to format date/time
  const formatDateTime = (dateString?: string) => {
    if (!dateString) return '—';
    try {
      return formatDateFns(parseISO(dateString), 'MMM dd, yyyy HH:mm');
    } catch {
      return dateString;
    }
  };

  // If editing existing event, show a simple message
  if (initialData) {
    return (
      <div className="p-6 text-center">
        <Text className="text-gray-600">
          Editing existing events is not supported in the new flow yet.
        </Text>
        <Button onClick={onCancel} className="mt-4">
          Cancel
        </Button>
      </div>
    );
  }

  // Show loading state while categories are being fetched initially
  if (loadingCategories && categories.length === 0) {
    return (
      <div className="p-6 text-center">
        <Loader2 className="h-8 w-8 animate-spin text-blue-600 mx-auto mb-4" />
        <Text className="text-gray-600">
          Loading event form...
        </Text>
      </div>
    );
  }

  return (
    <div className="bg-white p-6 space-y-6">
      {/* Header */}
      <div className="text-center">
        <h3 className="text-lg font-semibold text-gray-900 mb-2">
          Create Event from Supplier Offerings
        </h3>
        <Text className="text-gray-600">
          Select category, dates, and choose from available offerings
        </Text>
      </div>

      {/* Error Display */}
      {error && (
        <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
          <Text className="text-red-600">{error}</Text>
        </div>
      )}

      {/* Form Fields */}
      {/* Category Selection Row */}
      <div className="mb-4">
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Category *
        </label>
        {loadingCategories && categories.length === 0 ? (
          <div className="flex items-center justify-center py-2">
            <Loader2 className="h-4 w-4 animate-spin" />
            <Text className="ml-2 text-sm">Loading categories...</Text>
          </div>
        ) : categories.length === 0 ? (
          <div className="p-2 text-center text-red-600 text-sm">
            Failed to load categories. Please refresh the page.
          </div>
        ) : (
          <select
            value={selectedCategory?.id || ""}
            onChange={(e) => {
              const category = categories.find(c => c.id === e.target.value);
              setSelectedCategory(category || null);
            }}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            disabled={loadingCategories}
          >
            <option value="">Select category</option>
            {categories.map((category) => (
              <option key={category.id} value={category.id}>
                {category.name}
              </option>
            ))}
          </select>
        )}
      </div>

      {/* Other Fields Row */}
      <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-3 gap-4">
        {/* From Date */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            From Date *
          </label>
          <Input
            type="date"
            value={fromDate}
            onChange={(e) => setFromDate(e.target.value)}
            min={new Date().toISOString().split('T')[0]}
          />
        </div>

        {/* To Date */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            To Date *
          </label>
          <Input
            type="date"
            value={toDate}
            onChange={(e) => setToDate(e.target.value)}
            min={fromDate || new Date().toISOString().split('T')[0]}
          />
        </div>

        {/* Time Range */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Time
          </label>
          <div className="flex gap-2">
            <Input
              type="time"
              value={startTime}
              onChange={(e) => setStartTime(e.target.value)}
              className="w-38"
            />
            <Input
              type="time"
              value={endTime}
              onChange={(e) => setEndTime(e.target.value)}
              className="w-38"
            />
          </div>
        </div>
      </div>

      {/* Offerings Section */}
      {selectedCategory && fromDate && toDate && (
        <div className="space-y-4">
          {/* Search and View Toggle */}
          <div className="flex gap-4 items-center">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                placeholder="Search offerings..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>

            {/* View Toggle */}
            <div className="flex border border-gray-200 rounded-lg overflow-hidden">
              <button
                type="button"
                onClick={() => setViewMode("cards")}
                className={`px-3 py-2 text-sm font-medium transition-colors ${
                  viewMode === "cards"
                    ? 'bg-blue-600 text-white'
                    : 'bg-white text-gray-600 hover:bg-gray-50'
                }`}
              >
                <Grid className="h-4 w-4" />
              </button>
              <button
                type="button"
                onClick={() => setViewMode("table")}
                className={`px-3 py-2 text-sm font-medium transition-colors ${
                  viewMode === "table"
                    ? 'bg-blue-600 text-white'
                    : 'bg-white text-gray-600 hover:bg-gray-50'
                }`}
              >
                <List className="h-4 w-4" />
              </button>
            </div>
          </div>

          {/* Loading State */}
          {loadingOfferings && (
            <div className="flex items-center justify-center py-12">
              <Loader2 className="h-8 w-8 animate-spin text-blue-600" />
              <Text className="ml-3">Loading offerings...</Text>
            </div>
          )}

          {/* Offerings Display */}
          {!loadingOfferings && Array.isArray(filteredOfferings) && filteredOfferings.length > 0 && (
            <>
              <div className="flex items-center justify-between">
                <Text className="text-sm text-gray-600">
                  {filteredOfferings.length} offering{filteredOfferings.length !== 1 ? 's' : ''} found
                </Text>
              </div>

              {viewMode === "cards" ? (
                /* Cards View */
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 max-h-96 overflow-y-auto">
                  {filteredOfferings && filteredOfferings.map((offering) => {
                    if (!offering || !offering.id) return null;

                    return (
                      <div
                        key={offering.id}
                        className={`relative p-4 rounded-lg border-2 transition-all cursor-pointer ${
                          selectedOffering?.id === offering.id
                            ? 'border-blue-600 bg-blue-50 shadow-lg'
                            : 'border-gray-200 bg-white hover:border-gray-300 hover:shadow-md'
                        }`}
                        onClick={() => setSelectedOffering(offering)}
                      >
                        {/* Selection indicator */}
                        {selectedOffering?.id === offering.id && (
                          <div className="absolute top-2 right-2 w-6 h-6 bg-blue-600 rounded-full flex items-center justify-center">
                            <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                              <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                            </svg>
                          </div>
                        )}

                        <div className="space-y-3 h-full flex flex-col">
                          {/* Name at the top */}
                          <h4 className="font-semibold text-gray-900 text-sm leading-tight">
                            {offering.product_service_name || 'Unnamed Service'}
                          </h4>
                          {/* Price immediately below name, right-aligned */}
                          <div className="text-right">
                            <div className="font-bold text-green-600 text-sm">
                              {formatPrice(offering)}
                            </div>
                          </div>

                          {(() => {
                            try {
                              if (!offering || !offering.product_service_custom_fields || typeof offering.product_service_custom_fields !== 'object') {
                                return null;
                              }

                              return (
                                <div className="space-y-1">
                                  {Object.entries(offering.product_service_custom_fields).map(([key, value]) => {
                                    try {
                                      if (key === 'destinations_names' && Array.isArray(value)) {
                                        return (
                                          <div key={key} className="flex items-center gap-2 text-xs">
                                            <span className="font-medium text-gray-600 capitalize">
                                              Destinations:
                                            </span>
                                            <div className="flex flex-wrap gap-1">
                                              {value.slice(0, 2).map((dest: string, idx: number) => (
                                                <Badge key={idx} className="text-xs bg-purple-100 text-purple-700">
                                                  {dest || 'Unknown'}
                                                </Badge>
                                              ))}
                                              {value.length > 2 && (
                                                <Badge className="text-xs bg-gray-100 text-gray-600">
                                                  +{value.length - 2} more
                                                </Badge>
                                              )}
                                            </div>
                                          </div>
                                        );
                                      }
                                      if (typeof value === 'string' || typeof value === 'number') {
                                        return (
                                          <div key={key} className="flex items-center gap-2 text-xs">
                                            <span className="font-medium text-gray-600 capitalize">
                                              {key.replace(/_/g, ' ')}:
                                            </span>
                                            <span className="text-gray-800">{value}</span>
                                          </div>
                                        );
                                      }
                                      return null;
                                    } catch (fieldError) {
                                      console.warn('Error rendering custom field:', key, fieldError);
                                      return null;
                                    }
                                  })}
                                </div>
                              );
                            } catch (error) {
                              console.warn('Error rendering custom fields:', error);
                              return null;
                            }
                          })()}

                          <hr className="w-full my-2 border-gray-200" />
                          
                          <div className="flex flex-col text-xs text-gray-700">
                            <div>
                              <span className="font-medium">From: </span>
                              {offering.active_from ? offering.active_from : '—'}
                            </div>
                            <div>
                              <span className="font-medium">To: </span>
                              {offering.active_to ? offering.active_to : '—'}
                              
                            </div>
                            {/* Show blue text with full availability_notes if standard offering */}
                            {typeof offering.availability_notes === 'string' &&
                              offering.availability_notes.toLowerCase().includes('standard offering') && (
                                <div className="mt-1 font-semibold text-blue-600">{offering.availability_notes}</div>
                              )}
                          </div>
                        </div>
                      </div>
                    );
                  })}
                </div>
              ) : (
                /* Table View */
                <div className="max-h-96 overflow-y-auto border border-gray-200 rounded-lg">
                  <table className="w-full">
                    <thead className="bg-gray-50 sticky top-0">
                      <tr>
                        <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Offering
                        </th>
                        <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Details
                        </th>
                        <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Price
                        </th>
                        <th className="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Select
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {filteredOfferings && filteredOfferings.map((offering) => {
                        if (!offering || !offering.id) return null;

                        return (
                          <tr
                            key={offering.id}
                            className={`cursor-pointer transition-colors ${
                              selectedOffering?.id === offering.id
                                ? 'bg-blue-50 border-l-4 border-l-blue-600'
                                : 'hover:bg-gray-50'
                            }`}
                            onClick={() => setSelectedOffering(offering)}
                          >
                          <td className="px-4 py-4">
                            <div className="text-sm font-medium text-gray-900">
                              {offering.product_service_name}
                            </div>
                          </td>
                          <td className="px-4 py-4">
                            <div className="space-y-1">
                              {offering.product_service_custom_fields?.age_range && (
                                <Badge className="text-xs bg-purple-100 text-purple-700">
                                  {offering.product_service_custom_fields.age_range}
                                </Badge>
                              )}
                              {offering.product_service_custom_fields?.no_of_days && (
                                <Badge className="text-xs bg-green-100 text-green-700">
                                  {offering.product_service_custom_fields.no_of_days} days
                                </Badge>
                              )}
                            </div>
                          </td>
                          <td className="px-4 py-4">
                            <div className="text-sm font-semibold text-green-600">
                              {formatPrice(offering)}
                            </div>
                          </td>
                          <td className="px-4 py-4 text-center">
                            {selectedOffering?.id === offering.id ? (
                              <div className="w-5 h-5 bg-blue-600 rounded-full flex items-center justify-center mx-auto">
                                <svg className="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                                  <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                                </svg>
                              </div>
                            ) : (
                              <div className="w-5 h-5 border-2 border-gray-300 rounded-full mx-auto"></div>
                            )}
                          </td>
                        </tr>
                        );
                      })}
                    </tbody>
                  </table>
                </div>
              )}
            </>
          )}

          {/* No Results */}
          {!loadingOfferings && Array.isArray(filteredOfferings) && filteredOfferings.length === 0 && Array.isArray(offerings) && offerings.length > 0 && (
            <div className="text-center py-8">
              <Text className="text-gray-500">No offerings match your search criteria</Text>
            </div>
          )}

          {/* No Offerings */}
          {!loadingOfferings && Array.isArray(offerings) && offerings.length === 0 && selectedCategory && fromDate && toDate && (
            <div className="text-center py-8">
              <Text className="text-gray-500">No offerings found for the selected category and dates</Text>
            </div>
          )}
        </div>
      )}

      {/* Action Buttons */}
      <div className="flex justify-end gap-3 pt-6 border-t border-gray-200">
        <Button
          type="button"
          variant="secondary"
          onClick={onCancel}
        >
          Cancel
        </Button>
        <Button
          type="button"
          onClick={handleSubmit}
          disabled={!selectedOffering}
          className="bg-blue-600 hover:bg-blue-700 text-white disabled:opacity-50"
        >
          Create Event
        </Button>
      </div>
    </div>
  );
};

export default EventForm;

