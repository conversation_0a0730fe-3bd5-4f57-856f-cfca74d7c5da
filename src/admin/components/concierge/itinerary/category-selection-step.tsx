import React, { useState, useEffect } from "react";
import { <PERSON><PERSON>, Text, Badge } from "@camped-ai/ui";
import { Loader2, Tag } from "lucide-react";

interface Category {
  id: string;
  name: string;
  description?: string;
  category_type: "Product" | "Service" | "Both";
  icon?: string;
  is_active: boolean;
  product_count?: number;
  service_count?: number;
  total_count?: number;
}

interface CategorySelectionStepProps {
  selectedCategory?: Category;
  onCategorySelect: (category: Category) => void;
  onNext: () => void;
  onCancel: () => void;
}

const CategorySelectionStep: React.FC<CategorySelectionStepProps> = ({
  selectedCategory,
  onCategorySelect,
  onNext,
  onCancel,
}) => {
  const [categories, setCategories] = useState<Category[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Fetch categories from API
  useEffect(() => {
    const fetchCategories = async () => {
      try {
        setLoading(true);
        setError(null);

        const response = await fetch('/admin/supplier-management/products-services/categories?is_active=true&limit=50', {
          method: 'GET',
          credentials: 'include',
          headers: {
            'Content-Type': 'application/json',
          },
        });

        if (!response.ok) {
          throw new Error(`Failed to fetch categories: ${response.statusText}`);
        }

        const data = await response.json();
        console.log('Categories fetched:', data);

        if (data.categories && Array.isArray(data.categories)) {
          setCategories(data.categories);
        } else {
          console.warn('Invalid categories data format:', data);
          setCategories([]); // Set empty array instead of throwing error
        }
      } catch (err) {
        console.error('Error fetching categories:', err);
        setError(err instanceof Error ? err.message : 'Failed to load categories');
        setCategories([]); // Ensure categories is always an array
      } finally {
        setLoading(false);
      }
    };

    // Add a small delay to ensure component is mounted
    const timer = setTimeout(fetchCategories, 100);
    return () => clearTimeout(timer);
  }, []);

  // Handle category selection
  const handleCategoryClick = (category: Category) => {
    onCategorySelect(category);
  };

  // Get category icon or default
  const getCategoryIcon = (category: Category) => {
    if (category.icon) {
      return category.icon;
    }
    
    // Default icons based on category name
    const name = category.name.toLowerCase();
    if (name.includes('activity') || name.includes('tour')) return '🎯';
    if (name.includes('lodging') || name.includes('hotel')) return '🏨';
    if (name.includes('flight') || name.includes('air')) return '✈️';
    if (name.includes('transport') || name.includes('transfer')) return '🚗';
    if (name.includes('cruise') || name.includes('ship')) return '🚢';
    if (name.includes('food') || name.includes('dining')) return '🍽️';
    if (name.includes('spa') || name.includes('wellness')) return '💆';
    return '📋'; // Default icon
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="text-center py-12">
          <Loader2 className="h-8 w-8 animate-spin mx-auto text-blue-600" />
          <Text className="mt-4 text-gray-600">Loading categories...</Text>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="space-y-6">
        <div className="text-center py-12">
          <div className="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center mx-auto mb-4">
            <Tag className="h-6 w-6 text-red-600" />
          </div>
          <Text className="text-red-600 mb-4">{error}</Text>
          <Button 
            onClick={() => window.location.reload()} 
            variant="secondary"
          >
            Retry
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="text-center">
        <h3 className="text-lg font-semibold text-gray-900 mb-2">
          Select Event Category
        </h3>
        <Text className="text-gray-600">
          Choose a category to see available offerings for your event
        </Text>
      </div>

      {/* Categories Grid */}
      <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
        {categories.map((category) => (
          <button
            key={category.id}
            type="button"
            onClick={() => handleCategoryClick(category)}
            className={`p-4 rounded-lg border-2 transition-all hover:shadow-md ${
              selectedCategory?.id === category.id
                ? 'border-blue-600 bg-blue-50 shadow-md'
                : 'border-gray-200 bg-white hover:border-gray-300'
            }`}
          >
            <div className="text-center space-y-2">
              <div className="text-3xl mb-2">
                {getCategoryIcon(category)}
              </div>
              <div className="font-medium text-gray-900">
                {category.name}
              </div>
              {category.description && (
                <div className="text-xs text-gray-500 line-clamp-2">
                  {category.description}
                </div>
              )}
              <div className="flex justify-center gap-1">
                <Badge className="text-xs bg-gray-100 text-gray-600">
                  {category.total_count || 0} offerings
                </Badge>
              </div>
            </div>
          </button>
        ))}
      </div>

      {/* No categories message */}
      {categories.length === 0 && (
        <div className="text-center py-12">
          <div className="w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center mx-auto mb-4">
            <Tag className="h-6 w-6 text-gray-400" />
          </div>
          <Text className="text-gray-600">
            No active categories found. Please contact your administrator.
          </Text>
        </div>
      )}

      {/* Actions */}
      <div className="flex justify-between items-center pt-6 border-t border-gray-200">
        <Button type="button" variant="secondary" onClick={onCancel}>
          Cancel
        </Button>
        <Button
          type="button"
          onClick={onNext}
          disabled={!selectedCategory}
          className="bg-blue-600 hover:bg-blue-700 text-white"
        >
          Next: Set Date & Time
        </Button>
      </div>
    </div>
  );
};

export default CategorySelectionStep;
