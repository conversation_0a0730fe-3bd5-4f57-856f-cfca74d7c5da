import React, { useState } from "react";
import {
  Con<PERSON><PERSON>,
  <PERSON>,
  Button,
  FocusModal,
} from "@camped-ai/ui";
import { Plus } from "lucide-react";
import EventForm from "./event-form";
import EventTemplates, { EventTemplate } from "./event-templates";

/**
 * Test component to verify the enhanced event form works correctly
 * This component can be used to test the Select components and overall functionality
 */

const EventFormTest: React.FC = () => {
  const [isTemplateModalOpen, setIsTemplateModalOpen] = useState(false);
  const [isEventFormOpen, setIsEventFormOpen] = useState(false);
  const [selectedTemplate, setSelectedTemplate] = useState<EventTemplate | null>(null);

  const handleSelectTemplate = (template: EventTemplate) => {
    setSelectedTemplate(template);
    setIsTemplateModalOpen(false);
    setIsEventFormOpen(true);
  };

  const handleCreateBlank = () => {
    setSelectedTemplate(null);
    setIsTemplateModalOpen(false);
    setIsEventFormOpen(true);
  };

  const handleEventSubmit = (eventData: any) => {
    console.log("Event submitted:", eventData);
    alert("Event created successfully! Check console for details.");
    setIsEventFormOpen(false);
    setSelectedTemplate(null);
  };

  const handleEventCancel = () => {
    setIsEventFormOpen(false);
    setSelectedTemplate(null);
  };

  return (
    <Container className="max-w-4xl mx-auto p-6">
      <div className="text-center mb-8">
        <Heading level="h1" className="text-2xl font-bold text-gray-900 mb-4">
          Event Form Test
        </Heading>
        <p className="text-gray-600 mb-6">
          Test the enhanced event creation form with templates and validation
        </p>
        
        <div className="flex justify-center gap-4">
          <Button onClick={() => setIsTemplateModalOpen(true)} className="bg-blue-600 hover:bg-blue-700">
            <Plus className="h-4 w-4 mr-2" />
            Test with Templates
          </Button>
          <Button variant="secondary" onClick={() => setIsEventFormOpen(true)}>
            Test Blank Form
          </Button>
        </div>
      </div>

      {/* Event Templates Modal */}
      <FocusModal open={isTemplateModalOpen} onOpenChange={setIsTemplateModalOpen}>
        <FocusModal.Content className="max-w-4xl">
          <FocusModal.Header>
            <FocusModal.Title>Choose Event Template</FocusModal.Title>
          </FocusModal.Header>
          <div className="p-6">
            <EventTemplates
              onSelectTemplate={handleSelectTemplate}
              onCreateBlank={handleCreateBlank}
            />
          </div>
        </FocusModal.Content>
      </FocusModal>

      {/* Event Form Modal */}
      <FocusModal open={isEventFormOpen} onOpenChange={setIsEventFormOpen}>
        <FocusModal.Content className="max-w-2xl">
          <FocusModal.Header>
            <FocusModal.Title>
              {selectedTemplate ? `Create ${selectedTemplate.name}` : "Create New Event"}
            </FocusModal.Title>
          </FocusModal.Header>
          <EventForm
            initialData={selectedTemplate?.defaultData}
            onSubmit={handleEventSubmit}
            onCancel={handleEventCancel}
          />
        </FocusModal.Content>
      </FocusModal>

      {/* Instructions */}
      <div className="mt-8 bg-gray-50 rounded-lg p-6">
        <Heading level="h3" className="text-lg font-semibold mb-3">
          Test Instructions
        </Heading>
        <div className="space-y-2 text-sm text-gray-600">
          <p>• Click "Test with Templates" to see the template selection interface</p>
          <p>• Click "Test Blank Form" to test the form without templates</p>
          <p>• Try different event categories to see dynamic fields</p>
          <p>• Test form validation by leaving required fields empty</p>
          <p>• Try the time picker with automatic calculations</p>
          <p>• Test file upload (drag and drop)</p>
          <p>• Check browser console for submitted form data</p>
        </div>
      </div>
    </Container>
  );
};

export default EventFormTest;
