import React, { useState } from "react";
import {
  <PERSON><PERSON>,
  Text,
  Heading,
  FocusModal,
  DropdownMenu,
  IconButton,
  toast,
} from "@camped-ai/ui";
import { Plus, MoreHorizontal, Edit, Trash, Clock, MapPin, DollarSign } from "lucide-react";
import { format } from "date-fns";
import EventForm from "./event-form";

interface EventPanelProps {
  day: {
    id: string;
    date: string;
    title?: string;
    events: Array<{
      id: string;
      category: string;
      type?: string;
      title: string;
      notes?: string;
      start_time?: string;
      end_time?: string;
      duration?: string;
      timezone?: string;
      details?: Record<string, any>;
      price?: number;
      currency?: string;
      media?: string[];
    }>;
  };
  onEventUpdate: () => void;
}

const EventPanel: React.FC<EventPanelProps> = ({ day, onEventUpdate }) => {
  const [isAddFormOpen, setIsAddFormOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [editingEvent, setEditingEvent] = useState<any>(null);

  // Get category emoji
  const getCategoryEmoji = (category: string) => {
    const emojis: Record<string, string> = {
      Flight: "✈️",
      Lodging: "🛏️",
      Activity: "🎯",
      Cruise: "🚢",
      Transport: "🚗",
      Info: "ℹ️",
    };
    return emojis[category] || "📍";
  };

  // Get category color
  const getCategoryColor = (category: string) => {
    const colors: Record<string, string> = {
      Flight: "bg-blue-100 text-blue-800",
      Lodging: "bg-purple-100 text-purple-800",
      Activity: "bg-green-100 text-green-800",
      Cruise: "bg-cyan-100 text-cyan-800",
      Transport: "bg-orange-100 text-orange-800",
      Info: "bg-gray-100 text-gray-800",
    };
    return colors[category] || "bg-gray-100 text-gray-800";
  };

  // Handle add event
  const handleAddEvent = async (eventData: any) => {
    try {
      console.log({eventData})
      const response = await fetch(`/admin/concierge-management/itineraries/days/${day.id}/events`, {
        method: "POST",
        credentials: "include",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(eventData),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || "Failed to add event");
      }

      toast.success("Event added successfully");
      setIsAddFormOpen(false);

      // Add a small delay to ensure the database transaction is complete
      setTimeout(() => {
        onEventUpdate();
      }, 100);
    } catch (error) {
      console.error("Error adding event:", error);
      toast.error("Failed to add event");
    }
  };

  // Handle edit event
  const handleEditEvent = (event: any) => {
    setEditingEvent(event);
    setIsEditModalOpen(true);
  };

  // Handle update event
  const handleUpdateEvent = async (eventData: any) => {
    if (!editingEvent) return;

    try {
      console.log({eventData})
      const response = await fetch(`/admin/concierge-management/itineraries/events/${editingEvent.id}`, {
        method: "PATCH",
        credentials: "include",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(eventData),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || "Failed to update event");
      }

      toast.success("Event updated successfully");
      setIsEditModalOpen(false);
      setEditingEvent(null);

      // Add a small delay to ensure the database transaction is complete
      setTimeout(() => {
        onEventUpdate();
      }, 100);
    } catch (error) {
      console.error("Error updating event:", error);
      toast.error("Failed to update event");
    }
  };

  // Handle delete event
  const handleDeleteEvent = async (eventId: string) => {
    if (!confirm("Are you sure you want to delete this event?")) {
      return;
    }

    try {
      const response = await fetch(`/admin/concierge-management/itineraries/events/${eventId}`, {
        method: "DELETE",
        credentials: "include",
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || "Failed to delete event");
      }

      console.log("Event deleted successfully");
      toast.success("Event deleted successfully");

      // Add a small delay to ensure the database transaction is complete
      setTimeout(() => {
        onEventUpdate();
      }, 100);
    } catch (error) {
      console.error("Error deleting event:", error);
      toast.error("Failed to delete event");
    }
  };

  return (
    <>

      {/* Edit Event Modal */}
      <FocusModal open={isEditModalOpen} onOpenChange={setIsEditModalOpen}>
        <FocusModal.Content className="max-w-2xl">
          <FocusModal.Header>
            <FocusModal.Title>Edit Event</FocusModal.Title>
          </FocusModal.Header>
          <EventForm
            initialData={editingEvent}
            onSubmit={handleUpdateEvent}
            onCancel={() => setIsEditModalOpen(false)}
          />
        </FocusModal.Content>
      </FocusModal>

      {/* Panel Content */}
      <div className="p-6">
        {/* Day Header */}
        <div className="flex items-center justify-between mb-6">
          <div>
            <Heading level="h2" className="text-xl font-semibold">
              {day.title || format(new Date(day.date), "EEEE, MMMM d")}
            </Heading>
            <Text className="text-gray-500">
              {format(new Date(day.date), "EEEE, MMMM d, yyyy")}
            </Text>
          </div>
          <Button onClick={() => setIsAddFormOpen(!isAddFormOpen)}>
            <Plus className="h-4 w-4 mr-2" />
            {isAddFormOpen ? "Cancel" : "Add Event"}
          </Button>
        </div>

        {/* Inline Event Form */}
        {isAddFormOpen && (
          <div className="mb-6 bg-gray-50 rounded-lg border border-gray-200 overflow-hidden">
            <EventForm
              onSubmit={handleAddEvent}
              onCancel={() => setIsAddFormOpen(false)}
            />
          </div>
        )}

        {/* Events List */}
        <div className="space-y-4">
          {day.events.length === 0 ? (
            <div className="text-center py-12">
              <Text className="text-gray-500 mb-4">
                No events scheduled for this day
              </Text>
              <Button variant="secondary" onClick={() => setIsAddFormOpen(true)}>
                <Plus className="h-4 w-4 mr-2" />
                Add First Event
              </Button>
            </div>
          ) : (
            day.events.map((event) => (
              <div
                key={event.id}
                className="bg-white border border-gray-200 rounded-lg p-4 hover:shadow-sm transition-shadow"
              >
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center gap-3 mb-2">
                      <span className="text-lg">{getCategoryEmoji(event.category)}</span>
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${getCategoryColor(event.category)}`}>
                        {event.category}
                      </span>
                      {event.type && (
                        <span className="text-xs text-gray-500">
                          {event.type}
                        </span>
                      )}
                    </div>
                    
                    <Heading level="h3" className="text-lg font-medium mb-2">
                      {event.title}
                    </Heading>
                    
                    {event.notes && (
                      <Text className="text-gray-600 mb-3">
                        {event.notes}
                      </Text>
                    )}
                    
                    <div className="flex items-center gap-4 text-sm text-gray-500">
                      {event.start_time && (
                        <div className="flex items-center gap-1">
                          <Clock className="h-4 w-4" />
                          <span>
                            {event.start_time}
                            {event.end_time && ` - ${event.end_time}`}
                          </span>
                        </div>
                      )}
                      
                      {event.details?.location && (
                        <div className="flex items-center gap-1">
                          <MapPin className="h-4 w-4" />
                          <span>{event.details.location}</span>
                        </div>
                      )}
                      
                      {event.price && (
                        <div className="flex items-center gap-1">
                          <DollarSign className="h-4 w-4" />
                          <span>
                            {event.currency || "USD"} {event.price.toLocaleString()}
                          </span>
                        </div>
                      )}
                    </div>
                  </div>
                  
                  <DropdownMenu>
                    <DropdownMenu.Trigger asChild>
                      <IconButton size="small">
                        <MoreHorizontal className="h-4 w-4" />
                      </IconButton>
                    </DropdownMenu.Trigger>
                    <DropdownMenu.Content>
                      <DropdownMenu.Item onClick={() => handleEditEvent(event)}>
                        <Edit className="h-4 w-4 mr-2" />
                        Edit
                      </DropdownMenu.Item>
                      <DropdownMenu.Item
                        onClick={() => handleDeleteEvent(event.id)}
                        className="text-red-600"
                      >
                        <Trash className="h-4 w-4 mr-2" />
                        Delete
                      </DropdownMenu.Item>
                    </DropdownMenu.Content>
                  </DropdownMenu>
                </div>
              </div>
            ))
          )}
        </div>
      </div>
    </>
  );
};

export default EventPanel;
