import React, { useState, useRef, useCallback } from "react";
import {
  But<PERSON>,
  Text,
  toast,
  Badge,
} from "@camped-ai/ui";
import { Upload, X, Image, FileText, Video, Download, Eye, CloudUpload } from "lucide-react";

interface MediaUploadProps {
  value: string[];
  onChange: (files: string[]) => void;
  accept?: string;
  maxFiles?: number;
  label?: string;
}

const MediaUpload: React.FC<MediaUploadProps> = ({
  value = [],
  onChange,
  accept = "image/*,video/*,.pdf,.doc,.docx",
  maxFiles = 5,
  label = "Upload Files",
}) => {
  const [isUploading, setIsUploading] = useState(false);
  const [isDragOver, setIsDragOver] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Get file icon and type based on URL
  const getFileInfo = (url: string) => {
    const extension = url.split('.').pop()?.toLowerCase();
    const fileName = url.split('/').pop() || 'Unknown file';

    if (['jpg', 'jpeg', 'png', 'gif', 'webp'].includes(extension || '')) {
      return {
        icon: <Image className="h-4 w-4" />,
        type: 'image',
        color: 'text-green-600',
        bgColor: 'bg-green-100',
        fileName
      };
    } else if (['mp4', 'mov', 'avi', 'mkv'].includes(extension || '')) {
      return {
        icon: <Video className="h-4 w-4" />,
        type: 'video',
        color: 'text-blue-600',
        bgColor: 'bg-blue-100',
        fileName
      };
    } else {
      return {
        icon: <FileText className="h-4 w-4" />,
        type: 'document',
        color: 'text-gray-600',
        bgColor: 'bg-gray-100',
        fileName
      };
    }
  };

  // Handle drag and drop
  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(true);
  }, []);

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
  }, []);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);

    const files = Array.from(e.dataTransfer.files);
    if (files.length > 0) {
      handleFileUpload(files);
    }
  }, [value, maxFiles, onChange]);

  // Unified file upload handler
  const handleFileUpload = async (files: File[]) => {
    if (files.length === 0) return;

    if (value.length + files.length > maxFiles) {
      toast.error(`Maximum ${maxFiles} files allowed`);
      return;
    }

    setIsUploading(true);

    try {
      // Simulate file upload - in a real implementation, you would upload to a file storage service
      const uploadPromises = files.map(async (file) => {
        // Validate file size (max 10MB)
        if (file.size > 10 * 1024 * 1024) {
          throw new Error(`File ${file.name} is too large (max 10MB)`);
        }

        // Simulate upload delay
        await new Promise(resolve => setTimeout(resolve, 1000));

        // Return a mock URL - in real implementation, this would be the uploaded file URL
        return `https://example.com/uploads/${Date.now()}-${file.name}`;
      });

      const uploadedUrls = await Promise.all(uploadPromises);
      onChange([...value, ...uploadedUrls]);

      toast.success(`${files.length} file(s) uploaded successfully`);
    } catch (error) {
      console.error("Upload error:", error);
      toast.error(error instanceof Error ? error.message : "Failed to upload files");
    } finally {
      setIsUploading(false);
      // Reset file input
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    }
  };

  // Handle file selection from input
  const handleFileSelect = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(event.target.files || []);
    handleFileUpload(files);
  };

  // Handle file removal
  const handleRemoveFile = (indexToRemove: number) => {
    const newFiles = value.filter((_, index) => index !== indexToRemove);
    onChange(newFiles);
  };

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <label className="block text-sm font-medium text-gray-700">{label}</label>
        <Badge variant="secondary" className="text-xs">
          {value.length}/{maxFiles} files
        </Badge>
      </div>

      {/* Drag and Drop Area */}
      <div
        className={`
          relative border-2 border-dashed rounded-lg p-6 text-center transition-colors
          ${isDragOver
            ? 'border-blue-400 bg-blue-50'
            : 'border-gray-300 hover:border-gray-400'
          }
          ${value.length >= maxFiles ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}
        `}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
        onClick={() => value.length < maxFiles && fileInputRef.current?.click()}
      >
        <div className="space-y-3">
          <div className="flex justify-center">
            <CloudUpload className={`h-8 w-8 ${isDragOver ? 'text-blue-500' : 'text-gray-400'}`} />
          </div>

          <div>
            <Text className="text-sm font-medium text-gray-900">
              {isDragOver ? 'Drop files here' : 'Drag and drop files here'}
            </Text>
            <Text className="text-xs text-gray-500 mt-1">
              or click to browse
            </Text>
          </div>

          {!isUploading && value.length < maxFiles && (
            <Button
              type="button"
              variant="secondary"
              size="small"
              className="mt-2"
              onClick={(e) => {
                e.stopPropagation();
                fileInputRef.current?.click();
              }}
            >
              <Upload className="h-4 w-4 mr-2" />
              Choose Files
            </Button>
          )}

          {isUploading && (
            <div className="flex items-center justify-center gap-2">
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
              <Text className="text-sm text-blue-600">Uploading...</Text>
            </div>
          )}
        </div>
      </div>

      {/* Hidden File Input */}
      <input
        ref={fileInputRef}
        type="file"
        multiple
        accept={accept}
        onChange={handleFileSelect}
        className="hidden"
      />

      {/* File List */}
      {value.length > 0 && (
        <div className="space-y-3">
          <Text className="text-sm font-medium text-gray-700">Uploaded Files</Text>
          <div className="grid grid-cols-1 gap-2">
            {value.map((url, index) => {
              const fileInfo = getFileInfo(url);
              return (
                <div
                  key={index}
                  className="flex items-center justify-between p-3 bg-white border border-gray-200 rounded-lg hover:shadow-sm transition-shadow"
                >
                  <div className="flex items-center gap-3 flex-1 min-w-0">
                    <div className={`p-2 rounded-full ${fileInfo.bgColor}`}>
                      <span className={fileInfo.color}>
                        {fileInfo.icon}
                      </span>
                    </div>

                    <div className="flex-1 min-w-0">
                      <Text className="text-sm font-medium text-gray-900 truncate">
                        {fileInfo.fileName}
                      </Text>
                      <Text className="text-xs text-gray-500 capitalize">
                        {fileInfo.type}
                      </Text>
                    </div>
                  </div>

                  <div className="flex items-center gap-1">
                    {fileInfo.type === 'image' && (
                      <Button
                        type="button"
                        variant="ghost"
                        size="small"
                        onClick={() => window.open(url, '_blank')}
                        className="text-gray-400 hover:text-gray-600"
                      >
                        <Eye className="h-4 w-4" />
                      </Button>
                    )}

                    <Button
                      type="button"
                      variant="ghost"
                      size="small"
                      onClick={() => window.open(url, '_blank')}
                      className="text-gray-400 hover:text-gray-600"
                    >
                      <Download className="h-4 w-4" />
                    </Button>

                    <Button
                      type="button"
                      variant="ghost"
                      size="small"
                      onClick={() => handleRemoveFile(index)}
                      className="text-red-400 hover:text-red-600"
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      )}

      {/* Upload Guidelines */}
      <div className="bg-gray-50 rounded-lg p-3">
        <Text className="text-xs text-gray-600 leading-relaxed">
          <strong>Supported formats:</strong> Images (JPG, PNG, GIF), Videos (MP4, MOV), Documents (PDF, DOC, DOCX)
          <br />
          <strong>File size limit:</strong> 10MB per file
          <br />
          <strong>Tips:</strong> Drag and drop multiple files at once for faster uploads
        </Text>
      </div>
    </div>
  );
};

export default MediaUpload;
