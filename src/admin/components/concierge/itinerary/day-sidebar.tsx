import React, { useState } from "react";
import {
  But<PERSON>,
  Text,
  FocusModal,
  Input,
  DatePicker,
  toast,
  DropdownMenu,
  IconButton,
} from "@camped-ai/ui";
import { Plus, Calendar, MoreHorizontal, Edit, Trash, GripVertical } from "lucide-react";
import { format } from "date-fns";

interface DaySidebarProps {
  itinerary: {
    id: string;
    days: Array<{
      id: string;
      date: string;
      title?: string;
      sort_order: number;
      events: Array<{
        category: string;
      }>;
    }>;
  };
  selectedDayId: string | null;
  onDaySelect: (dayId: string) => void;
  onDayUpdate: () => void;
}

interface DayFormData {
  date: Date;
  title: string;
}

const DaySidebar: React.FC<DaySidebarProps> = ({
  itinerary,
  selectedDayId,
  onDaySelect,
  onDayUpdate,
}) => {
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [editingDay, setEditingDay] = useState<any>(null);
  const [editFormData, setEditFormData] = useState<DayFormData>({
    date: new Date(),
    title: "",
  });

  // Get emoji indicators for day based on event categories
  const getDayEmojis = (events: Array<{ category: string }>) => {
    const categoryEmojis: Record<string, string> = {
      Flight: "✈️",
      Lodging: "🛏️",
      Activity: "🎯",
      Cruise: "🚢",
      Transport: "🚗",
      Info: "ℹ️",
    };

    const uniqueCategories = [...new Set(events.map(e => e.category))];
    return uniqueCategories.map(cat => categoryEmojis[cat] || "📍").join(" ");
  };

  // Handle automatic day creation
  const handleAddDay = async () => {
    try {
      // Calculate the next day number and date
      const sortedDays = [...itinerary.days].sort((a, b) => a.sort_order - b.sort_order);
      const nextDayNumber = sortedDays.length + 1;
      const defaultTitle = `Day ${nextDayNumber}`;

      // Calculate next date based on the last day
      let nextDate = new Date();
      if (sortedDays.length > 0) {
        const lastDay = sortedDays[sortedDays.length - 1];
        nextDate = new Date(lastDay.date);
        nextDate.setDate(nextDate.getDate() + 1);
      }

      console.log("Creating new day:", { title: defaultTitle, date: nextDate });

      const response = await fetch(`/admin/concierge-management/itineraries/${itinerary.id}/days`, {
        method: "POST",
        credentials: "include",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          date: nextDate.toISOString(),
          title: defaultTitle,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || "Failed to add day");
      }

      const result = await response.json();
      console.log("Day created successfully:", result);

      toast.success(`${defaultTitle} added successfully`);

      // Add a small delay to ensure the database transaction is complete
      setTimeout(() => {
        onDayUpdate();
      }, 100);
    } catch (error) {
      console.error("Error adding day:", error);
      toast.error("Failed to add day");
    }
  };

  // Handle edit day
  const handleEditDay = (day: any) => {
    setEditingDay(day);
    setEditFormData({
      date: new Date(day.date),
      title: day.title || "",
    });
    setIsEditModalOpen(true);
  };

  // Handle update day
  const handleUpdateDay = async () => {
    if (!editingDay) return;

    try {
      const response = await fetch(`/admin/concierge-management/itineraries/days/${editingDay.id}`, {
        method: "PATCH",
        credentials: "include",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          date: editFormData.date.toISOString(),
          title: editFormData.title || null,
        }),
      });

      if (!response.ok) {
        throw new Error("Failed to update day");
      }

      toast.success("Day updated successfully");
      setIsEditModalOpen(false);
      setEditingDay(null);
      onDayUpdate();
    } catch (error) {
      console.error("Error updating day:", error);
      toast.error("Failed to update day");
    }
  };

  // Handle delete day
  const handleDeleteDay = async (dayId: string) => {
    if (!confirm("Are you sure you want to delete this day and all its events?")) {
      return;
    }

    try {
      const response = await fetch(`/admin/concierge-management/itineraries/days/${dayId}`, {
        method: "DELETE",
        credentials: "include",
      });

      if (!response.ok) {
        throw new Error("Failed to delete day");
      }

      toast.success("Day deleted successfully");
      onDayUpdate();
    } catch (error) {
      console.error("Error deleting day:", error);
      toast.error("Failed to delete day");
    }
  };

  return (
    <>


      {/* Edit Day Modal */}
      <FocusModal open={isEditModalOpen} onOpenChange={setIsEditModalOpen}>
        <FocusModal.Content>
          <FocusModal.Header>
            <FocusModal.Title>Edit Day</FocusModal.Title>
          </FocusModal.Header>
          <div className="p-6 space-y-4">
            <div>
              <label className="block text-sm font-medium mb-2">Date</label>
              <DatePicker
                value={editFormData.date}
                onChange={(date) => setEditFormData({ ...editFormData, date: date || new Date() })}
              />
            </div>
            <div>
              <label className="block text-sm font-medium mb-2">Title (Optional)</label>
              <Input
                placeholder="e.g., Welcome to Los Angeles"
                value={editFormData.title}
                onChange={(e) => setEditFormData({ ...editFormData, title: e.target.value })}
              />
            </div>
            <div className="flex justify-end gap-3 pt-4">
              <Button variant="secondary" onClick={() => setIsEditModalOpen(false)}>
                Cancel
              </Button>
              <Button onClick={handleUpdateDay}>
                Update Day
              </Button>
            </div>
          </div>
        </FocusModal.Content>
      </FocusModal>

      {/* Sidebar Content */}
      <div className="p-4">
        {/* Header */}
        <div className="flex items-center justify-between mb-4">
          <Text className="font-medium text-gray-900">Information & Documents</Text>
        </div>

        {/* Add New Day Button */}
        <Button
          variant="secondary"
          size="small"
          className="w-full mb-6"
          onClick={handleAddDay}
        >
          <Plus className="h-4 w-4 mr-2" />
          New Day
        </Button>

        {/* Days List */}
        <div className="space-y-2">
          {itinerary.days
            .sort((a, b) => a.sort_order - b.sort_order)
            .map((day) => (
              <div
                key={day.id}
                className={`p-3 rounded-lg border cursor-pointer transition-colors ${
                  selectedDayId === day.id
                    ? "bg-blue-50 border-blue-200"
                    : "bg-white border-gray-200 hover:bg-gray-50"
                }`}
                onClick={() => onDaySelect(day.id)}
              >
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-1">
                      <Calendar className="h-4 w-4 text-gray-400" />
                      <Text className="text-sm font-medium">
                        {format(new Date(day.date), "MMM d")}
                      </Text>
                    </div>
                    <Text className="text-sm font-medium text-gray-900">
                      {day.title || "Untitled Day"}
                    </Text>
                    <div className="flex items-center gap-1 mt-1">
                      <Text className="text-xs">
                        {getDayEmojis(day.events)}
                      </Text>
                    </div>
                  </div>
                  
                  <DropdownMenu>
                    <DropdownMenu.Trigger asChild>
                      <IconButton variant="ghost" size="small">
                        <MoreHorizontal className="h-4 w-4" />
                      </IconButton>
                    </DropdownMenu.Trigger>
                    <DropdownMenu.Content>
                      <DropdownMenu.Item onClick={() => handleEditDay(day)}>
                        <Edit className="h-4 w-4 mr-2" />
                        Edit
                      </DropdownMenu.Item>
                      <DropdownMenu.Item
                        onClick={() => handleDeleteDay(day.id)}
                        className="text-red-600"
                      >
                        <Trash className="h-4 w-4 mr-2" />
                        Delete
                      </DropdownMenu.Item>
                    </DropdownMenu.Content>
                  </DropdownMenu>
                </div>
              </div>
            ))}
        </div>
      </div>
    </>
  );
};

export default DaySidebar;
