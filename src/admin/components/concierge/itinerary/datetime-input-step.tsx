import React, { useState, useEffect } from "react";
import { Button, Input, Text } from "@camped-ai/ui";
import { Calendar, Clock, AlertCircle } from "lucide-react";
import { format, parseISO, isValid, isBefore, isAfter } from "date-fns";

interface DateTimeData {
  validFrom: string; // ISO date string (YYYY-MM-DD)
  validTo: string;   // ISO date string (YYYY-MM-DD)
  startTime: string; // HH:MM format
  endTime: string;   // HH:MM format
  duration: string;  // Human readable duration
}

interface DateTimeInputStepProps {
  selectedCategory: any;
  dateTimeData: DateTimeData;
  onDateTimeChange: (data: DateTimeData) => void;
  onNext: () => void;
  onBack: () => void;
  onCancel: () => void;
}

const DateTimeInputStep: React.FC<DateTimeInputStepProps> = ({
  selectedCategory,
  dateTimeData,
  onDateTimeChange,
  onNext,
  onBack,
  onCancel,
}) => {
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [calculatedDuration, setCalculatedDuration] = useState<string>("");

  // Calculate duration when start and end times change
  useEffect(() => {
    if (dateTimeData.startTime && dateTimeData.endTime) {
      const start = new Date(`2000-01-01T${dateTimeData.startTime}`);
      const end = new Date(`2000-01-01T${dateTimeData.endTime}`);
      
      if (end > start) {
        const diffMs = end.getTime() - start.getTime();
        const diffHours = diffMs / (1000 * 60 * 60);
        
        let durationText = "";
        if (diffHours < 1) {
          const diffMinutes = Math.round(diffMs / (1000 * 60));
          durationText = `${diffMinutes} minutes`;
        } else if (diffHours === 1) {
          durationText = "1 hour";
        } else if (diffHours < 24) {
          const hours = Math.floor(diffHours);
          const minutes = Math.round((diffHours - hours) * 60);
          if (minutes === 0) {
            durationText = `${hours} hours`;
          } else {
            durationText = `${hours}h ${minutes}m`;
          }
        }
        
        setCalculatedDuration(durationText);
        
        // Auto-update duration if it's different
        if (durationText !== dateTimeData.duration) {
          onDateTimeChange({
            ...dateTimeData,
            duration: durationText
          });
        }
      }
    }
  }, [dateTimeData.startTime, dateTimeData.endTime]);

  // Validation function
  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    // Validate dates
    if (!dateTimeData.validFrom) {
      newErrors.validFrom = "Start date is required";
    } else if (!isValid(parseISO(dateTimeData.validFrom))) {
      newErrors.validFrom = "Invalid start date format";
    }

    if (!dateTimeData.validTo) {
      newErrors.validTo = "End date is required";
    } else if (!isValid(parseISO(dateTimeData.validTo))) {
      newErrors.validTo = "Invalid end date format";
    }

    // Validate date range
    if (dateTimeData.validFrom && dateTimeData.validTo) {
      const startDate = parseISO(dateTimeData.validFrom);
      const endDate = parseISO(dateTimeData.validTo);
      
      if (isValid(startDate) && isValid(endDate)) {
        if (isAfter(startDate, endDate)) {
          newErrors.validTo = "End date must be after start date";
        }
        
        // Check if dates are not too far in the past
        const today = new Date();
        today.setHours(0, 0, 0, 0);
        
        if (isBefore(endDate, today)) {
          newErrors.validTo = "End date cannot be in the past";
        }
      }
    }

    // Validate times if provided
    if (dateTimeData.startTime && dateTimeData.endTime) {
      const start = new Date(`2000-01-01T${dateTimeData.startTime}`);
      const end = new Date(`2000-01-01T${dateTimeData.endTime}`);

      if (end <= start) {
        newErrors.endTime = "End time must be after start time";
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Handle form field updates
  const updateField = (field: keyof DateTimeData, value: string) => {
    const updatedData = { ...dateTimeData, [field]: value };
    onDateTimeChange(updatedData);

    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[field];
        return newErrors;
      });
    }
  };

  // Handle next button click
  const handleNext = () => {
    if (validateForm()) {
      onNext();
    }
  };

  // Get today's date in YYYY-MM-DD format for min date
  const today = format(new Date(), 'yyyy-MM-dd');

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="text-center">
        <h3 className="text-lg font-semibold text-gray-900 mb-2">
          Set Date & Time
        </h3>
        <Text className="text-gray-600">
          Specify the date range and time for your {selectedCategory?.name} event
        </Text>
      </div>

      {/* Date Range Section */}
      <div className="bg-blue-50 rounded-lg p-6 space-y-4">
        <div className="flex items-center gap-2 mb-4">
          <Calendar className="h-5 w-5 text-blue-600" />
          <Text className="font-medium text-gray-900">Event Date Range</Text>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Valid From Date *
            </label>
            <Input
              type="date"
              value={dateTimeData.validFrom}
              min={today}
              onChange={(e) => updateField("validFrom", e.target.value)}
              className={errors.validFrom ? 'border-red-500' : ''}
            />
            {errors.validFrom && (
              <div className="flex items-center gap-1 mt-1">
                <AlertCircle className="h-4 w-4 text-red-500" />
                <Text className="text-xs text-red-600">{errors.validFrom}</Text>
              </div>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Valid To Date *
            </label>
            <Input
              type="date"
              value={dateTimeData.validTo}
              min={dateTimeData.validFrom || today}
              onChange={(e) => updateField("validTo", e.target.value)}
              className={errors.validTo ? 'border-red-500' : ''}
            />
            {errors.validTo && (
              <div className="flex items-center gap-1 mt-1">
                <AlertCircle className="h-4 w-4 text-red-500" />
                <Text className="text-xs text-red-600">{errors.validTo}</Text>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Time Section */}
      <div className="bg-green-50 rounded-lg p-6 space-y-4">
        <div className="flex items-center gap-2 mb-4">
          <Clock className="h-5 w-5 text-green-600" />
          <Text className="font-medium text-gray-900">Event Time (Optional)</Text>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Start Time
            </label>
            <Input
              type="time"
              value={dateTimeData.startTime}
              onChange={(e) => updateField("startTime", e.target.value)}
              className={errors.startTime ? 'border-red-500' : ''}
            />
            {errors.startTime && (
              <div className="flex items-center gap-1 mt-1">
                <AlertCircle className="h-4 w-4 text-red-500" />
                <Text className="text-xs text-red-600">{errors.startTime}</Text>
              </div>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              End Time
            </label>
            <Input
              type="time"
              value={dateTimeData.endTime}
              onChange={(e) => updateField("endTime", e.target.value)}
              className={errors.endTime ? 'border-red-500' : ''}
            />
            {errors.endTime && (
              <div className="flex items-center gap-1 mt-1">
                <AlertCircle className="h-4 w-4 text-red-500" />
                <Text className="text-xs text-red-600">{errors.endTime}</Text>
              </div>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Duration
            </label>
            <Input
              placeholder="e.g., 2 hours"
              value={dateTimeData.duration}
              onChange={(e) => updateField("duration", e.target.value)}
              className="bg-gray-50"
            />
            {calculatedDuration && calculatedDuration !== dateTimeData.duration && (
              <Text className="text-xs text-gray-500 mt-1">
                Calculated: {calculatedDuration}
              </Text>
            )}
          </div>
        </div>
      </div>

      {/* Actions */}
      <div className="flex justify-between items-center pt-6 border-t border-gray-200">
        <div className="flex gap-3">
          <Button type="button" variant="secondary" onClick={onCancel}>
            Cancel
          </Button>
          <Button type="button" variant="secondary" onClick={onBack}>
            Back
          </Button>
        </div>
        <Button
          type="button"
          onClick={handleNext}
          className="bg-blue-600 hover:bg-blue-700 text-white"
        >
          Next: Select Offering
        </Button>
      </div>
    </div>
  );
};

export default DateTimeInputStep;
