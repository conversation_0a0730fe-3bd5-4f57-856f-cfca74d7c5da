import React, { useState } from "react";
import {
  Container,
  Heading,
  Text,
  Button,
} from "@camped-ai/ui";
import { Calendar } from "lucide-react";
import EventPanel from "./event-panel";

/**
 * Test component for the inline event form functionality
 * This demonstrates the new inline event creation without modals
 */

const InlineEventTest: React.FC = () => {
  const [events, setEvents] = useState<any[]>([]);

  // Mock day data for testing
  const mockDay = {
    id: "day_test_123",
    date: "2024-07-22",
    title: "Day 1 - Departure",
    events: events,
  };

  const handleEventUpdate = () => {
    // In a real implementation, this would refetch the events
    console.log("Event updated - would refetch data here");
  };

  return (
    <Container className="max-w-6xl mx-auto p-6">
      <div className="text-center mb-8">
        <Heading level="h1" className="text-2xl font-bold text-gray-900 mb-4">
          Inline Event Form Test
        </Heading>
        <Text className="text-gray-600 mb-6">
          Test the new inline event creation form (no modals)
        </Text>
      </div>

      {/* Mock Itinerary Day */}
      <div className="bg-white rounded-lg border border-gray-200 shadow-sm">
        <div className="p-4 border-b border-gray-200 bg-gray-50">
          <div className="flex items-center gap-2">
            <Calendar className="h-5 w-5 text-blue-600" />
            <Heading level="h2" className="text-lg font-semibold">
              Test Day - July 22, 2024
            </Heading>
          </div>
          <Text className="text-sm text-gray-600 mt-1">
            Click "Add Event" to see the inline form
          </Text>
        </div>

        {/* Event Panel with Inline Form */}
        <EventPanel
          day={mockDay}
          onEventUpdate={handleEventUpdate}
        />
      </div>

      {/* Instructions */}
      <div className="mt-8 bg-blue-50 rounded-lg p-6">
        <Heading level="h3" className="text-lg font-semibold mb-3 text-blue-900">
          Test Instructions
        </Heading>
        <div className="space-y-2 text-sm text-blue-800">
          <Text>• Click "Add Event" to see the inline form appear</Text>
          <Text>• The form should appear directly below the day header (no modal)</Text>
          <Text>• Try filling out the form and submitting (will show console log)</Text>
          <Text>• Click "Cancel" or "Add Event" again to hide the form</Text>
          <Text>• The form should be compact and well-organized for inline use</Text>
        </div>
      </div>

      {/* Changes Summary */}
      <div className="mt-6 bg-green-50 rounded-lg p-6">
        <Heading level="h3" className="text-lg font-semibold mb-3 text-green-900">
          Changes Made
        </Heading>
        <div className="space-y-2 text-sm text-green-800">
          <Text>✅ Removed event template modal</Text>
          <Text>✅ Removed event creation modal</Text>
          <Text>✅ Added inline event form that appears below day header</Text>
          <Text>✅ Simplified "Add Event" button (no template selection)</Text>
          <Text>✅ Made form more compact for inline display</Text>
          <Text>✅ Fixed API error handling for missing itinerary data</Text>
        </div>
      </div>
    </Container>
  );
};

export default InlineEventTest;
