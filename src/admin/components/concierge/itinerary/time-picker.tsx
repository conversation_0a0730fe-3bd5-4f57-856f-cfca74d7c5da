import React, { useState, useEffect } from "react";
import {
  Button,
  Input,
  Text,
  Badge,
} from "@camped-ai/ui";
import { Clock, Calculator } from "lucide-react";

interface TimePickerProps {
  startTime: string;
  endTime: string;
  duration: string;
  timezone: string;
  onStartTimeChange: (time: string) => void;
  onEndTimeChange: (time: string) => void;
  onDurationChange: (duration: string) => void;
  onTimezoneChange: (timezone: string) => void;
}



const TimePicker: React.FC<TimePickerProps> = ({
  startTime,
  endTime,
  duration,
  onStartTimeChange,
  onEndTimeChange,
  onDurationChange,
}) => {
  const [calculatedDuration, setCalculatedDuration] = useState<string>("");
  const [calculatedEndTime, setCalculatedEndTime] = useState<string>("");

  // Calculate duration when start and end times change
  useEffect(() => {
    if (startTime && endTime) {
      const start = new Date(`2000-01-01T${startTime}`);
      const end = new Date(`2000-01-01T${endTime}`);
      
      if (end > start) {
        const diffMs = end.getTime() - start.getTime();
        const diffHours = diffMs / (1000 * 60 * 60);
        
        if (diffHours < 1) {
          const diffMinutes = Math.round(diffMs / (1000 * 60));
          setCalculatedDuration(`${diffMinutes} minutes`);
        } else if (diffHours === 1) {
          setCalculatedDuration("1 hour");
        } else if (diffHours < 24) {
          const hours = Math.floor(diffHours);
          const minutes = Math.round((diffHours - hours) * 60);
          if (minutes === 0) {
            setCalculatedDuration(`${hours} hours`);
          } else {
            setCalculatedDuration(`${hours}h ${minutes}m`);
          }
        }
      }
    }
  }, [startTime, endTime]);

  // Calculate end time when start time and duration change
  useEffect(() => {
    if (startTime && duration) {
      const start = new Date(`2000-01-01T${startTime}`);
      const durationMatch = duration.match(/(\d+(?:\.\d+)?)\s*(hour|hours|h|minute|minutes|min|m)/i);
      
      if (durationMatch) {
        const value = parseFloat(durationMatch[1]);
        const unit = durationMatch[2].toLowerCase();
        
        let minutesToAdd = 0;
        if (unit.startsWith('h') || unit.startsWith('hour')) {
          minutesToAdd = value * 60;
        } else if (unit.startsWith('m') || unit.startsWith('min')) {
          minutesToAdd = value;
        }
        
        const end = new Date(start.getTime() + minutesToAdd * 60 * 1000);
        const endTimeString = end.toTimeString().slice(0, 5);
        setCalculatedEndTime(endTimeString);
      }
    }
  }, [startTime, duration]);

  const handleDurationPreset = (presetDuration: string) => {
    onDurationChange(presetDuration);
  };

  const handleCalculatedDurationApply = () => {
    if (calculatedDuration) {
      onDurationChange(calculatedDuration);
    }
  };

  const handleCalculatedEndTimeApply = () => {
    if (calculatedEndTime) {
      onEndTimeChange(calculatedEndTime);
    }
  };

  return (
    <div className="bg-blue-50 rounded-lg p-4 space-y-4">
      <div className="flex items-center gap-2 mb-3">
        <Clock className="h-4 w-4 text-blue-600" />
        <Text className="text-sm font-medium text-gray-900">Timing & Schedule</Text>
      </div>
      
      {/* Time Inputs */}
      <div className="grid grid-cols-3 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">Start Time</label>
          <div className="space-y-2">
            <Input
              type="time"
              value={startTime}
              onChange={(e) => onStartTimeChange(e.target.value)}
              className="text-center"
            />
          </div>
        </div>
        
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">End Time</label>
          <div className="space-y-2">
            <Input
              type="time"
              value={endTime}
              onChange={(e) => onEndTimeChange(e.target.value)}
              className="text-center"
            />
            {calculatedEndTime && calculatedEndTime !== endTime && (
              <div className="flex items-center gap-1">
                <Badge className="text-xs">
                  Calc: {calculatedEndTime}
                </Badge>
                <Button
                  type="button"
                  size="small"
                  onClick={handleCalculatedEndTimeApply}
                  className="text-xs p-1"
                >
                  <Calculator className="h-3 w-3" />
                </Button>
              </div>
            )}
          </div>
        </div>
        
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">Duration</label>
          <div className="space-y-2">
            <Input
              placeholder="e.g., 2 hours"
              value={duration}
              onChange={(e) => onDurationChange(e.target.value)}
            />
            {calculatedDuration && calculatedDuration !== duration && (
              <div className="flex items-center gap-1">
                <Badge className="text-xs">
                  Calc: {calculatedDuration}
                </Badge>
                <Button
                  type="button"
                  size="small"
                  onClick={handleCalculatedDurationApply}
                  className="text-xs p-1"
                >
                  <Calculator className="h-3 w-3" />
                </Button>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default TimePicker;
