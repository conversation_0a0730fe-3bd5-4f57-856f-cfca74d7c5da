import React, { useState } from "react";
import {
  Container,
  <PERSON>ing,
  Text,
  But<PERSON>,
  FocusModal,
  Badge,
} from "@camped-ai/ui";
import { Plus, Calendar, Clock, MapPin, Users, Star } from "lucide-react";
import EventForm from "./event-form";
import EventTemplates, { EventTemplate } from "./event-templates";

/**
 * Enhanced Event Management Demo
 * 
 * This component demonstrates all the improvements made to the cruise itinerary
 * event management system, including:
 * 
 * 1. Enhanced UI/UX with better visual hierarchy
 * 2. Improved media upload with drag-and-drop
 * 3. Event templates for quick creation
 * 4. Advanced time management with calculations
 * 5. Comprehensive form validation
 */

const EnhancedEventDemo: React.FC = () => {
  const [isTemplateModalOpen, setIsTemplateModalOpen] = useState(false);
  const [isEventFormOpen, setIsEventFormOpen] = useState(false);
  const [selectedTemplate, setSelectedTemplate] = useState<EventTemplate | null>(null);
  const [events, setEvents] = useState<any[]>([]);

  const handleSelectTemplate = (template: EventTemplate) => {
    setSelectedTemplate(template);
    setIsTemplateModalOpen(false);
    setIsEventFormOpen(true);
  };

  const handleCreateBlank = () => {
    setSelectedTemplate(null);
    setIsTemplateModalOpen(false);
    setIsEventFormOpen(true);
  };

  const handleEventSubmit = (eventData: any) => {
    const newEvent = {
      id: Date.now().toString(),
      ...eventData,
      created_at: new Date().toISOString(),
    };
    
    setEvents(prev => [...prev, newEvent]);
    setIsEventFormOpen(false);
    setSelectedTemplate(null);
  };

  const handleEventCancel = () => {
    setIsEventFormOpen(false);
    setSelectedTemplate(null);
  };

  return (
    <Container className="max-w-6xl mx-auto p-6">
      {/* Header */}
      <div className="text-center mb-8">
        <Heading level="h1" className="text-3xl font-bold text-gray-900 mb-4">
          Enhanced Cruise Itinerary Event Management
        </Heading>
        <Text className="text-lg text-gray-600 mb-6">
          Experience the improved event creation and management system with advanced features
        </Text>
        
        <div className="flex justify-center gap-4 mb-8">
          <Button onClick={() => setIsTemplateModalOpen(true)} className="bg-blue-600 hover:bg-blue-700">
            <Plus className="h-4 w-4 mr-2" />
            Create Event from Template
          </Button>
          <Button variant="secondary" onClick={() => setIsEventFormOpen(true)}>
            Create Blank Event
          </Button>
        </div>
      </div>

      {/* Features Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
        <div className="bg-blue-50 rounded-lg p-6">
          <div className="flex items-center gap-3 mb-3">
            <Star className="h-6 w-6 text-blue-600" />
            <Heading level="h3" className="text-lg font-semibold">Enhanced UI/UX</Heading>
          </div>
          <Text className="text-sm text-gray-600">
            Improved visual hierarchy with color-coded sections, better spacing, and intuitive form layout
          </Text>
        </div>

        <div className="bg-green-50 rounded-lg p-6">
          <div className="flex items-center gap-3 mb-3">
            <Calendar className="h-6 w-6 text-green-600" />
            <Heading level="h3" className="text-lg font-semibold">Event Templates</Heading>
          </div>
          <Text className="text-sm text-gray-600">
            Pre-configured templates for common cruise activities like shore excursions, formal dinners, and deck parties
          </Text>
        </div>

        <div className="bg-purple-50 rounded-lg p-6">
          <div className="flex items-center gap-3 mb-3">
            <Clock className="h-6 w-6 text-purple-600" />
            <Heading level="h3" className="text-lg font-semibold">Smart Time Management</Heading>
          </div>
          <Text className="text-sm text-gray-600">
            Automatic duration calculations, timezone support, and time adjustment controls
          </Text>
        </div>

        <div className="bg-orange-50 rounded-lg p-6">
          <div className="flex items-center gap-3 mb-3">
            <MapPin className="h-6 w-6 text-orange-600" />
            <Heading level="h3" className="text-lg font-semibold">Enhanced Media Upload</Heading>
          </div>
          <Text className="text-sm text-gray-600">
            Drag-and-drop file uploads with preview capabilities and better file management
          </Text>
        </div>

        <div className="bg-red-50 rounded-lg p-6">
          <div className="flex items-center gap-3 mb-3">
            <Users className="h-6 w-6 text-red-600" />
            <Heading level="h3" className="text-lg font-semibold">Form Validation</Heading>
          </div>
          <Text className="text-sm text-gray-600">
            Real-time validation with helpful error messages and smart field validation
          </Text>
        </div>

        <div className="bg-indigo-50 rounded-lg p-6">
          <div className="flex items-center gap-3 mb-3">
            <Star className="h-6 w-6 text-indigo-600" />
            <Heading level="h3" className="text-lg font-semibold">Category-Specific Fields</Heading>
          </div>
          <Text className="text-sm text-gray-600">
            Dynamic form fields that adapt based on event category with relevant input options
          </Text>
        </div>
      </div>

      {/* Events List */}
      {events.length > 0 && (
        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <Heading level="h2" className="text-xl font-semibold mb-4">
            Created Events ({events.length})
          </Heading>
          <div className="space-y-3">
            {events.map((event) => (
              <div key={event.id} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                <div className="flex items-center gap-3">
                  <span className="text-2xl">
                    {event.category === "Flight" && "✈️"}
                    {event.category === "Lodging" && "🛏️"}
                    {event.category === "Activity" && "🎯"}
                    {event.category === "Cruise" && "🚢"}
                    {event.category === "Transport" && "🚗"}
                    {event.category === "Info" && "ℹ️"}
                  </span>
                  <div>
                    <Text className="font-medium text-gray-900">{event.title}</Text>
                    <div className="flex items-center gap-4 text-sm text-gray-500">
                      <Badge variant="secondary">{event.category}</Badge>
                      {event.duration && (
                        <div className="flex items-center gap-1">
                          <Clock className="h-3 w-3" />
                          <span>{event.duration}</span>
                        </div>
                      )}
                      {event.start_time && (
                        <div className="flex items-center gap-1">
                          <Calendar className="h-3 w-3" />
                          <span>{event.start_time}</span>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Event Templates Modal */}
      <FocusModal open={isTemplateModalOpen} onOpenChange={setIsTemplateModalOpen}>
        <FocusModal.Content className="max-w-4xl">
          <FocusModal.Header>
            <FocusModal.Title>Choose Event Template</FocusModal.Title>
          </FocusModal.Header>
          <div className="p-6">
            <EventTemplates
              onSelectTemplate={handleSelectTemplate}
              onCreateBlank={handleCreateBlank}
            />
          </div>
        </FocusModal.Content>
      </FocusModal>

      {/* Event Form Modal */}
      <FocusModal open={isEventFormOpen} onOpenChange={setIsEventFormOpen}>
        <FocusModal.Content className="max-w-2xl">
          <FocusModal.Header>
            <FocusModal.Title>
              {selectedTemplate ? `Create ${selectedTemplate.name}` : "Create New Event"}
            </FocusModal.Title>
          </FocusModal.Header>
          <EventForm
            initialData={selectedTemplate?.defaultData}
            onSubmit={handleEventSubmit}
            onCancel={handleEventCancel}
          />
        </FocusModal.Content>
      </FocusModal>

      {/* Instructions */}
      <div className="mt-8 bg-gray-50 rounded-lg p-6">
        <Heading level="h3" className="text-lg font-semibold mb-3">
          Try the Enhanced Features
        </Heading>
        <div className="space-y-2 text-sm text-gray-600">
          <Text>• Click "Create Event from Template" to see pre-configured cruise event templates</Text>
          <Text>• Try the drag-and-drop file upload in the media section</Text>
          <Text>• Use the time picker with automatic duration calculations</Text>
          <Text>• Experience real-time form validation with helpful error messages</Text>
          <Text>• Notice how form fields adapt based on the selected event category</Text>
        </div>
      </div>
    </Container>
  );
};

export default EnhancedEventDemo;
