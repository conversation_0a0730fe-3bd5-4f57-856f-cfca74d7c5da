import React, { useState, useEffect } from "react";
import { Button, Text, Badge, Input } from "@camped-ai/ui";
import { Loader2, Package, Search, AlertCircle, Calendar, DollarSign, Grid, List } from "lucide-react";
import { format, parseISO, isWithinInterval } from "date-fns";

interface SupplierOffering {
  id: string;
  product_service_name: string;
  product_service_custom_fields?: Record<string, any>;
  offering_custom_fields?: Record<string, any>;
  supplier_id?: string;
  active_from?: string;
  active_to?: string;
  availability_notes?: string;
  gross_price?: number;
  net_cost?: number;
  selling_price?: number;
  currency?: string;
  status?: "active" | "inactive";
}

interface OfferingSelectionStepProps {
  selectedCategory?: any; // Made optional since we're not using it
  dateTimeData?: any; // Made optional since we're not using it
  selectedOffering?: SupplierOffering;
  onOfferingSelect: (offering: SupplierOffering) => void;
  onNext: () => void;
  onBack?: () => void; // Made optional since we're removing it
  onCancel: () => void;
}

const OfferingSelectionStep: React.FC<OfferingSelectionStepProps> = ({
  selectedCategory,
  dateTimeData,
  selectedOffering,
  onOfferingSelect,
  onNext,
  onBack,
  onCancel,
}) => {
  const [offerings, setOfferings] = useState<SupplierOffering[]>([]);
  const [filteredOfferings, setFilteredOfferings] = useState<SupplierOffering[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState("");
  const [viewMode, setViewMode] = useState<"cards" | "table">("cards");

  // Fetch offerings from API
  useEffect(() => {
    const fetchOfferings = async () => {
      try {
        setLoading(true);
        setError(null);

        // Build query parameters - fetch ALL active offerings
        const params = new URLSearchParams({
          status: 'active',
          limit: '1000', // Increased limit to get all offerings
          offset: '0',
        });

        // Don't filter by category - get all offerings
        // if (selectedCategory?.id) {
        //   params.append('category_id', selectedCategory.id);
        // }

        const response = await fetch(`/admin/supplier-management/supplier-offerings?${params.toString()}`, {
          method: 'GET',
          credentials: 'include',
          headers: {
            'Content-Type': 'application/json',
          },
        });

        if (!response.ok) {
          throw new Error(`Failed to fetch offerings: ${response.statusText}`);
        }

        const data = await response.json();
        console.log('Offerings fetched:', data);

        if (data.supplier_offerings && Array.isArray(data.supplier_offerings)) {
          setOfferings(data.supplier_offerings);
        } else {
          console.warn('Invalid offerings data format:', data);
          setOfferings([]); // Set empty array instead of throwing error
        }
      } catch (err) {
        console.error('Error fetching offerings:', err);
        setError(err instanceof Error ? err.message : 'Failed to load offerings');
        setOfferings([]); // Ensure offerings is always an array
      } finally {
        setLoading(false);
      }
    };

    // Fetch all offerings on component mount
    const timer = setTimeout(fetchOfferings, 100);
    return () => clearTimeout(timer);
  }, []); // Empty dependency array - only run once on mount

  // Filter offerings based on date range and search term
  useEffect(() => {
    let filtered = [...offerings];

    // Filter by date range if provided
    if (dateTimeData.validFrom && dateTimeData.validTo) {
      const eventStart = parseISO(dateTimeData.validFrom);
      const eventEnd = parseISO(dateTimeData.validTo);

      filtered = filtered.filter(offering => {
        // If offering has no date restrictions, it's always available
        if (!offering.active_from && !offering.active_to) {
          return true;
        }

        // Check if offering is active during the event period
        const offeringStart = offering.active_from ? parseISO(offering.active_from) : new Date(0);
        const offeringEnd = offering.active_to ? parseISO(offering.active_to) : new Date(2099, 11, 31);

        // Check if there's any overlap between offering period and event period
        return (
          isWithinInterval(eventStart, { start: offeringStart, end: offeringEnd }) ||
          isWithinInterval(eventEnd, { start: offeringStart, end: offeringEnd }) ||
          isWithinInterval(offeringStart, { start: eventStart, end: eventEnd }) ||
          isWithinInterval(offeringEnd, { start: eventStart, end: eventEnd })
        );
      });
    }

    // Filter by search term
    if (searchTerm.trim()) {
      const term = searchTerm.toLowerCase();
      filtered = filtered.filter(offering =>
        offering.product_service_name.toLowerCase().includes(term) ||
        offering.availability_notes?.toLowerCase().includes(term) ||
        JSON.stringify(offering.product_service_custom_fields || {}).toLowerCase().includes(term) ||
        JSON.stringify(offering.offering_custom_fields || {}).toLowerCase().includes(term)
      );
    }

    setFilteredOfferings(filtered);
  }, [offerings, dateTimeData, searchTerm]);

  // Handle offering selection
  const handleOfferingClick = (offering: SupplierOffering) => {
    onOfferingSelect(offering);
  };

  // Format price display
  const formatPrice = (offering: SupplierOffering) => {
    const price = offering.selling_price || offering.gross_price || offering.net_cost;
    const currency = offering.currency || 'CHF';
    
    if (price) {
      return `${currency} ${price.toFixed(2)}`;
    }
    return 'Price on request';
  };

  // Format date range
  const formatDateRange = (offering: SupplierOffering) => {
    if (!offering.active_from && !offering.active_to) {
      return 'Always available';
    }
    
    const from = offering.active_from ? format(parseISO(offering.active_from), 'MMM dd, yyyy') : 'Open';
    const to = offering.active_to ? format(parseISO(offering.active_to), 'MMM dd, yyyy') : 'Open';
    
    return `${from} - ${to}`;
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="text-center py-12">
          <Loader2 className="h-8 w-8 animate-spin mx-auto text-blue-600" />
          <Text className="mt-4 text-gray-600">Loading offerings...</Text>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="space-y-6">
        <div className="text-center py-12">
          <div className="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center mx-auto mb-4">
            <AlertCircle className="h-6 w-6 text-red-600" />
          </div>
          <Text className="text-red-600 mb-4">{error}</Text>
          <Button onClick={() => window.location.reload()} variant="secondary">
            Retry
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="text-center">
        <h3 className="text-lg font-semibold text-gray-900 mb-2">
          Select Event from Supplier Offerings
        </h3>
        <Text className="text-gray-600">
          Choose from all available supplier offerings to create your event
        </Text>
      </div>

      {/* Search and View Toggle */}
      <div className="flex gap-4 items-center">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          <Input
            placeholder="Search offerings..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>

        {/* View Toggle */}
        <div className="flex border border-gray-200 rounded-lg overflow-hidden">
          <button
            type="button"
            onClick={() => setViewMode("cards")}
            className={`px-3 py-2 text-sm font-medium transition-colors ${
              viewMode === "cards"
                ? 'bg-blue-600 text-white'
                : 'bg-white text-gray-600 hover:bg-gray-50'
            }`}
          >
            <Grid className="h-4 w-4" />
          </button>
          <button
            type="button"
            onClick={() => setViewMode("table")}
            className={`px-3 py-2 text-sm font-medium transition-colors ${
              viewMode === "table"
                ? 'bg-blue-600 text-white'
                : 'bg-white text-gray-600 hover:bg-gray-50'
            }`}
          >
            <List className="h-4 w-4" />
          </button>
        </div>
      </div>

      {/* Results count */}
      <div className="flex justify-between items-center">
        <Text className="text-sm text-gray-600">
          {filteredOfferings.length} offering{filteredOfferings.length !== 1 ? 's' : ''} found
        </Text>
        {dateTimeData.validFrom && dateTimeData.validTo && (
          <Badge className="text-xs bg-blue-100 text-blue-600">
            Filtered for {format(parseISO(dateTimeData.validFrom), 'MMM dd')} - {format(parseISO(dateTimeData.validTo), 'MMM dd')}
          </Badge>
        )}
      </div>

      {/* Offerings Display */}
      {viewMode === "cards" ? (
        /* Cards View */
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 max-h-96 overflow-y-auto">
        {filteredOfferings.map((offering) => (
          <div
            key={offering.id}
            className={`relative p-4 rounded-lg border-2 transition-all cursor-pointer ${
              selectedOffering?.id === offering.id
                ? 'border-blue-600 bg-blue-50 shadow-lg'
                : 'border-gray-200 bg-white hover:border-gray-300 hover:shadow-md'
            }`}
            onClick={() => handleOfferingClick(offering)}
          >
            {/* Selection indicator */}
            {selectedOffering?.id === offering.id && (
              <div className="absolute top-2 right-2 w-6 h-6 bg-blue-600 rounded-full flex items-center justify-center">
                <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                </svg>
              </div>
            )}

            <div className="space-y-3">
              {/* Title and Price */}
              <div className="flex justify-between items-start pr-8">
                <div className="flex-1">
                  <h4 className="font-semibold text-gray-900 text-sm leading-tight">
                    {offering.product_service_name}
                  </h4>
                </div>
                <div className="text-right ml-2">
                  <div className="font-bold text-green-600 text-sm">
                    {formatPrice(offering)}
                  </div>
                </div>
              </div>

              {/* Custom Fields */}
              {offering.product_service_custom_fields && (
                <div className="space-y-1">
                  {Object.entries(offering.product_service_custom_fields).map(([key, value]) => {
                    if (key === 'destinations_names' && Array.isArray(value)) {
                      return (
                        <div key={key} className="flex items-center gap-2 text-xs">
                          <span className="font-medium text-gray-600 capitalize">
                            {key.replace(/_/g, ' ')}:
                          </span>
                          <div className="flex flex-wrap gap-1">
                            {value.map((dest: string, idx: number) => (
                              <Badge key={idx} className="text-xs bg-purple-100 text-purple-700">
                                {dest}
                              </Badge>
                            ))}
                          </div>
                        </div>
                      );
                    }
                    if (key === 'addons_names' && Array.isArray(value)) {
                      return (
                        <div key={key} className="flex items-center gap-2 text-xs">
                          <span className="font-medium text-gray-600 capitalize">
                            Add-ons:
                          </span>
                          <div className="flex flex-wrap gap-1">
                            {value.map((addon: string, idx: number) => (
                              <Badge key={idx} className="text-xs bg-green-100 text-green-700">
                                {addon}
                              </Badge>
                            ))}
                          </div>
                        </div>
                      );
                    }
                    if (typeof value === 'string' || typeof value === 'number') {
                      return (
                        <div key={key} className="flex items-center gap-2 text-xs">
                          <span className="font-medium text-gray-600 capitalize">
                            {key.replace(/_/g, ' ')}:
                          </span>
                          <span className="text-gray-800">{value}</span>
                        </div>
                      );
                    }
                    return null;
                  })}
                </div>
              )}

              {/* Offering Custom Fields */}
              {offering.offering_custom_fields?.destinations_names && (
                <div className="flex items-center gap-2 text-xs">
                  <span className="font-medium text-blue-600">Available in:</span>
                  <div className="flex flex-wrap gap-1">
                    {offering.offering_custom_fields.destinations_names.map((dest: string, idx: number) => (
                      <Badge key={idx} className="text-xs bg-blue-100 text-blue-700">
                        {dest}
                      </Badge>
                    ))}
                  </div>
                </div>
              )}

              {/* Availability */}
              <div className="flex items-center justify-between text-xs text-gray-500 pt-2 border-t border-gray-100">
                <div className="flex items-center gap-1">
                  <Calendar className="h-3 w-3" />
                  {formatDateRange(offering)}
                </div>
                {offering.availability_notes && (
                  <div className="flex items-center gap-1">
                    <Package className="h-3 w-3" />
                    <span className="truncate max-w-20">{offering.availability_notes}</span>
                  </div>
                )}
              </div>
            </div>
          </div>
        ))}
        </div>
      ) : (
        /* Table View */
        <div className="max-h-96 overflow-y-auto border border-gray-200 rounded-lg">
          <table className="w-full">
            <thead className="bg-gray-50 sticky top-0">
              <tr>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Offering
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Details
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Price
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Availability
                </th>
                <th className="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Select
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {filteredOfferings.map((offering) => (
                <tr
                  key={offering.id}
                  className={`cursor-pointer transition-colors ${
                    selectedOffering?.id === offering.id
                      ? 'bg-blue-50 border-l-4 border-l-blue-600'
                      : 'hover:bg-gray-50'
                  }`}
                  onClick={() => handleOfferingClick(offering)}
                >
                  <td className="px-4 py-4">
                    <div className="text-sm font-medium text-gray-900">
                      {offering.product_service_name}
                    </div>
                  </td>
                  <td className="px-4 py-4">
                    <div className="space-y-1">
                      {offering.product_service_custom_fields?.age_range && (
                        <Badge className="text-xs bg-purple-100 text-purple-700">
                          {offering.product_service_custom_fields.age_range}
                        </Badge>
                      )}
                      {offering.product_service_custom_fields?.no_of_days && (
                        <Badge className="text-xs bg-green-100 text-green-700">
                          {offering.product_service_custom_fields.no_of_days} days
                        </Badge>
                      )}
                      {offering.offering_custom_fields?.destinations_names && (
                        <div className="flex flex-wrap gap-1 mt-1">
                          {offering.offering_custom_fields.destinations_names.slice(0, 2).map((dest: string, idx: number) => (
                            <Badge key={idx} className="text-xs bg-blue-100 text-blue-700">
                              {dest}
                            </Badge>
                          ))}
                          {offering.offering_custom_fields.destinations_names.length > 2 && (
                            <Badge className="text-xs bg-gray-100 text-gray-600">
                              +{offering.offering_custom_fields.destinations_names.length - 2} more
                            </Badge>
                          )}
                        </div>
                      )}
                    </div>
                  </td>
                  <td className="px-4 py-4">
                    <div className="text-sm font-semibold text-green-600">
                      {formatPrice(offering)}
                    </div>
                  </td>
                  <td className="px-4 py-4">
                    <div className="text-xs text-gray-500">
                      {formatDateRange(offering)}
                    </div>
                  </td>
                  <td className="px-4 py-4 text-center">
                    {selectedOffering?.id === offering.id ? (
                      <div className="w-5 h-5 bg-blue-600 rounded-full flex items-center justify-center mx-auto">
                        <svg className="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                        </svg>
                      </div>
                    ) : (
                      <div className="w-5 h-5 border-2 border-gray-300 rounded-full mx-auto"></div>
                    )}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}

      {/* No offerings message */}
      {filteredOfferings.length === 0 && !loading && (
        <div className="text-center py-12">
          <div className="w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center mx-auto mb-4">
            <Package className="h-6 w-6 text-gray-400" />
          </div>
          <Text className="text-gray-600 mb-2">
            No offerings found for the selected criteria
          </Text>
          <Text className="text-sm text-gray-500">
            Try adjusting your date range or search terms
          </Text>
        </div>
      )}

      {/* Actions */}
      <div className="flex justify-between items-center pt-6 border-t border-gray-200">
        <div className="flex gap-3">
          <Button type="button" variant="secondary" onClick={onCancel}>
            Cancel
          </Button>
          <Button type="button" variant="secondary" onClick={onBack}>
            Back
          </Button>
        </div>
        <Button
          type="button"
          onClick={onNext}
          disabled={!selectedOffering}
          className="bg-blue-600 hover:bg-blue-700 text-white"
        >
          Next: Review & Save
        </Button>
      </div>
    </div>
  );
};

export default OfferingSelectionStep;
