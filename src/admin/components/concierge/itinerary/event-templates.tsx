import React from "react";
import {
  <PERSON><PERSON>,
  Text,
  Heading,
  Badge,
} from "@camped-ai/ui";
import { Plus, Clock, MapPin, Users, DollarSign } from "lucide-react";

interface EventTemplate {
  id: string;
  name: string;
  category: string;
  type: string;
  icon: string;
  description: string;
  defaultData: {
    title: string;
    notes?: string;
    duration?: string;
    details?: Record<string, any>;
    price?: number;
    currency?: string;
  };
  color: string;
  bgColor: string;
}

interface EventTemplatesProps {
  onSelectTemplate: (template: EventTemplate) => void;
  onCreateBlank: () => void;
}

const CRUISE_EVENT_TEMPLATES: EventTemplate[] = [
  {
    id: "embarkation",
    name: "Embarkation",
    category: "Cruise",
    type: "Embarkation",
    icon: "🚢",
    description: "Board the cruise ship",
    defaultData: {
      title: "Cruise Ship Embarkation",
      notes: "Check-in and board the cruise ship. Please arrive 2 hours before departure.",
      duration: "2 hours",
      details: {
        location: "Cruise Terminal",
        meeting_point: "Terminal Entrance"
      }
    },
    color: "text-blue-700",
    bgColor: "bg-blue-100"
  },
  {
    id: "shore-excursion",
    name: "Shore Excursion",
    category: "Activity",
    type: "Tour",
    icon: "🏝️",
    description: "Guided tour at port destination",
    defaultData: {
      title: "Shore Excursion Tour",
      notes: "Guided tour of local attractions and landmarks.",
      duration: "4 hours",
      details: {
        difficulty: "Easy",
        max_capacity: "30"
      },
      price: 89,
      currency: "USD"
    },
    color: "text-green-700",
    bgColor: "bg-green-100"
  },
  {
    id: "formal-dinner",
    name: "Formal Dinner",
    category: "Activity",
    type: "Food/Drink",
    icon: "🍽️",
    description: "Elegant dining experience",
    defaultData: {
      title: "Formal Dinner Night",
      notes: "Dress code: Formal attire required. Reservations recommended.",
      duration: "2 hours",
      details: {
        location: "Main Dining Room",
        price_type: "included"
      }
    },
    color: "text-purple-700",
    bgColor: "bg-purple-100"
  },
  {
    id: "spa-treatment",
    name: "Spa & Wellness",
    category: "Activity",
    type: "Entertainment",
    icon: "🧘",
    description: "Relaxation and wellness activities",
    defaultData: {
      title: "Spa Treatment",
      notes: "Rejuvenating spa experience. Advance booking required.",
      duration: "90 minutes",
      details: {
        location: "Spa Deck",
        age_restrictions: "18+"
      },
      price: 150,
      currency: "USD"
    },
    color: "text-pink-700",
    bgColor: "bg-pink-100"
  },
  {
    id: "deck-party",
    name: "Deck Party",
    category: "Activity",
    type: "Entertainment",
    icon: "🎉",
    description: "Fun deck activities and entertainment",
    defaultData: {
      title: "Pool Deck Party",
      notes: "Live music, dancing, and poolside fun for all ages.",
      duration: "3 hours",
      details: {
        location: "Pool Deck",
        deck_location: "Deck 11",
        age_restrictions: "All ages"
      }
    },
    color: "text-orange-700",
    bgColor: "bg-orange-100"
  },
  {
    id: "port-arrival",
    name: "Port Arrival",
    category: "Cruise",
    type: "Port",
    icon: "⚓",
    description: "Arrival at destination port",
    defaultData: {
      title: "Port Arrival",
      notes: "Ship arrives at destination port. Disembarkation begins at 8:00 AM.",
      details: {
        port_name: "Port of Call",
        deck_location: "Gangway - Deck 3"
      }
    },
    color: "text-teal-700",
    bgColor: "bg-teal-100"
  }
];

const EventTemplates: React.FC<EventTemplatesProps> = ({ onSelectTemplate, onCreateBlank }) => {
  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="text-center">
        <Heading level="h3" className="text-lg font-semibold text-gray-900 mb-2">
          Choose Event Type
        </Heading>
        <Text className="text-sm text-gray-600">
          Select a template to get started quickly, or create a blank event
        </Text>
      </div>

      {/* Create Blank Event */}
      <div className="border-2 border-dashed border-gray-300 rounded-lg p-4 hover:border-gray-400 transition-colors">
        <Button
          variant="ghost"
          onClick={onCreateBlank}
          className="w-full h-auto p-4 flex flex-col items-center gap-3 hover:bg-gray-50"
        >
          <div className="w-12 h-12 rounded-full bg-gray-100 flex items-center justify-center">
            <Plus className="h-6 w-6 text-gray-600" />
          </div>
          <div className="text-center">
            <Text className="font-medium text-gray-900">Create Blank Event</Text>
            <Text className="text-sm text-gray-500">Start from scratch</Text>
          </div>
        </Button>
      </div>

      {/* Template Grid */}
      <div>
        <Text className="text-sm font-medium text-gray-700 mb-3">Popular Templates</Text>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
          {CRUISE_EVENT_TEMPLATES.map((template) => (
            <div
              key={template.id}
              className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow cursor-pointer"
              onClick={() => onSelectTemplate(template)}
            >
              <div className="flex items-start gap-3">
                <div className={`w-10 h-10 rounded-lg ${template.bgColor} flex items-center justify-center`}>
                  <span className="text-lg">{template.icon}</span>
                </div>
                
                <div className="flex-1 min-w-0">
                  <div className="flex items-center gap-2 mb-1">
                    <Text className="font-medium text-gray-900">{template.name}</Text>
                    <Badge variant="secondary" className="text-xs">
                      {template.category}
                    </Badge>
                  </div>
                  
                  <Text className="text-sm text-gray-600 mb-2">
                    {template.description}
                  </Text>
                  
                  <div className="flex items-center gap-4 text-xs text-gray-500">
                    {template.defaultData.duration && (
                      <div className="flex items-center gap-1">
                        <Clock className="h-3 w-3" />
                        <span>{template.defaultData.duration}</span>
                      </div>
                    )}
                    
                    {template.defaultData.price && (
                      <div className="flex items-center gap-1">
                        <DollarSign className="h-3 w-3" />
                        <span>${template.defaultData.price}</span>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Quick Actions */}
      <div className="border-t border-gray-200 pt-4">
        <Text className="text-sm font-medium text-gray-700 mb-3">Quick Actions</Text>
        <div className="flex flex-wrap gap-2">
          <Button
            variant="secondary"
            size="small"
            onClick={() => onSelectTemplate(CRUISE_EVENT_TEMPLATES.find(t => t.id === "embarkation")!)}
          >
            🚢 Embarkation
          </Button>
          <Button
            variant="secondary"
            size="small"
            onClick={() => onSelectTemplate(CRUISE_EVENT_TEMPLATES.find(t => t.id === "shore-excursion")!)}
          >
            🏝️ Shore Tour
          </Button>
          <Button
            variant="secondary"
            size="small"
            onClick={() => onSelectTemplate(CRUISE_EVENT_TEMPLATES.find(t => t.id === "formal-dinner")!)}
          >
            🍽️ Dinner
          </Button>
          <Button
            variant="secondary"
            size="small"
            onClick={() => onSelectTemplate(CRUISE_EVENT_TEMPLATES.find(t => t.id === "deck-party")!)}
          >
            🎉 Deck Party
          </Button>
        </div>
      </div>
    </div>
  );
};

export default EventTemplates;
export type { EventTemplate };
