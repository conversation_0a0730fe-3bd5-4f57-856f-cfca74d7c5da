import React, { useState, useEffect } from "react";
import {
  Container,
  Heading,
  Text,
  Button,
  Input,
  Select,
  Badge,
  Table,
  Toaster,
  toast,
  FocusModal,
  Textarea,
} from "@camped-ai/ui";
import {
  Plus,
  Edit,
  Trash2,
  Save,
  X,
  Mail,
  Eye,
  Copy,
} from "lucide-react";
import { useRbac } from "../../hooks/use-rbac";

// Types
interface EmailTemplate {
  id: string;
  name: string;
  subject: string;
  body: string;
  type: "booking_confirmation" | "itinerary_update" | "task_assignment" | "welcome" | "custom";
  is_active: boolean;
  variables: string[];
  created_at: string;
  updated_at: string;
}

const EmailTemplateManagement: React.FC = () => {
  const [templates, setTemplates] = useState<EmailTemplate[]>([]);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [editingTemplate, setEditingTemplate] = useState<EmailTemplate | null>(null);
  const [formData, setFormData] = useState({
    name: "",
    subject: "",
    body: "",
    type: "custom" as EmailTemplate["type"],
    is_active: true,
  });
  const [isLoading, setIsLoading] = useState(false);
  const { hasPermission } = useRbac();

  // Mock data for demonstration
  useEffect(() => {
    const mockTemplates: EmailTemplate[] = [
      {
        id: "1",
        name: "Booking Confirmation",
        subject: "Your booking confirmation - {{booking_id}}",
        body: `Dear {{guest_name}},

Thank you for your booking with us. Here are your booking details:

Booking ID: {{booking_id}}
Check-in: {{check_in_date}}
Check-out: {{check_out_date}}
Room Type: {{room_type}}

We look forward to welcoming you!

Best regards,
Concierge Team`,
        type: "booking_confirmation",
        is_active: true,
        variables: ["guest_name", "booking_id", "check_in_date", "check_out_date", "room_type"],
        created_at: "2024-01-01T00:00:00Z",
        updated_at: "2024-01-01T00:00:00Z",
      },
      {
        id: "2",
        name: "Itinerary Update",
        subject: "Your itinerary has been updated",
        body: `Dear {{guest_name}},

Your itinerary for {{date}} has been updated. Please review the changes:

{{itinerary_details}}

If you have any questions, please don't hesitate to contact us.

Best regards,
Concierge Team`,
        type: "itinerary_update",
        is_active: true,
        variables: ["guest_name", "date", "itinerary_details"],
        created_at: "2024-01-01T00:00:00Z",
        updated_at: "2024-01-01T00:00:00Z",
      },
      {
        id: "3",
        name: "Task Assignment",
        subject: "New task assigned: {{task_title}}",
        body: `Hello {{assignee_name}},

You have been assigned a new task:

Task: {{task_title}}
Description: {{task_description}}
Due Date: {{due_date}}
Priority: {{priority}}

Please log in to the system to view more details.

Best regards,
Management Team`,
        type: "task_assignment",
        is_active: true,
        variables: ["assignee_name", "task_title", "task_description", "due_date", "priority"],
        created_at: "2024-01-01T00:00:00Z",
        updated_at: "2024-01-01T00:00:00Z",
      },
    ];
    setTemplates(mockTemplates);
  }, []);

  const handleOpenModal = (template?: EmailTemplate) => {
    if (template) {
      setEditingTemplate(template);
      setFormData({
        name: template.name,
        subject: template.subject,
        body: template.body,
        type: template.type,
        is_active: template.is_active,
      });
    } else {
      setEditingTemplate(null);
      setFormData({
        name: "",
        subject: "",
        body: "",
        type: "custom",
        is_active: true,
      });
    }
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
    setEditingTemplate(null);
    setFormData({
      name: "",
      subject: "",
      body: "",
      type: "custom",
      is_active: true,
    });
  };

  const handleSave = () => {
    if (!formData.name.trim() || !formData.subject.trim() || !formData.body.trim()) {
      toast.error("Name, subject, and body are required");
      return;
    }

    // Extract variables from template
    const variableRegex = /\{\{([^}]+)\}\}/g;
    const variables: string[] = [];
    let match;
    const content = formData.subject + " " + formData.body;
    while ((match = variableRegex.exec(content)) !== null) {
      if (!variables.includes(match[1])) {
        variables.push(match[1]);
      }
    }

    // Mock save operation
    const newTemplate: EmailTemplate = {
      id: editingTemplate?.id || Date.now().toString(),
      name: formData.name,
      subject: formData.subject,
      body: formData.body,
      type: formData.type,
      is_active: formData.is_active,
      variables,
      created_at: editingTemplate?.created_at || new Date().toISOString(),
      updated_at: new Date().toISOString(),
    };

    if (editingTemplate) {
      setTemplates(templates.map(t => t.id === editingTemplate.id ? newTemplate : t));
      toast.success("Template updated successfully");
    } else {
      setTemplates([...templates, newTemplate]);
      toast.success("Template created successfully");
    }

    handleCloseModal();
  };

  const handleDelete = (templateId: string) => {
    if (confirm("Are you sure you want to delete this template?")) {
      setTemplates(templates.filter(t => t.id !== templateId));
      toast.success("Template deleted successfully");
    }
  };

  const handleDuplicate = (template: EmailTemplate) => {
    const duplicatedTemplate: EmailTemplate = {
      ...template,
      id: Date.now().toString(),
      name: `${template.name} (Copy)`,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    };
    setTemplates([...templates, duplicatedTemplate]);
    toast.success("Template duplicated successfully");
  };

  const getTypeBadge = (type: EmailTemplate["type"]) => {
    const typeConfig = {
      booking_confirmation: { color: "blue" as const, label: "Booking Confirmation" },
      itinerary_update: { color: "green" as const, label: "Itinerary Update" },
      task_assignment: { color: "orange" as const, label: "Task Assignment" },
      welcome: { color: "purple" as const, label: "Welcome" },
      custom: { color: "grey" as const, label: "Custom" },
    };
    
    const config = typeConfig[type];
    return <Badge color={config.color}>{config.label}</Badge>;
  };

  const typeOptions = [
    { value: "booking_confirmation", label: "Booking Confirmation" },
    { value: "itinerary_update", label: "Itinerary Update" },
    { value: "task_assignment", label: "Task Assignment" },
    { value: "welcome", label: "Welcome" },
    { value: "custom", label: "Custom" },
  ];

  return (
    <div className="space-y-6">
      <Toaster />
      
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <Heading level="h1" className="text-2xl font-bold">
            Email Template Management
          </Heading>
          <Text className="text-muted-foreground">
            Manage email templates for notifications and communications
          </Text>
        </div>
        {hasPermission("concierge_config:manage") && (
          <Button onClick={() => handleOpenModal()}>
            <Plus className="h-4 w-4 mr-2" />
            New Template
          </Button>
        )}
      </div>

      {/* Templates Table */}
      <div className="bg-card rounded-md shadow-sm border">
        <Table>
          <Table.Header>
            <Table.Row>
              <Table.HeaderCell>Name</Table.HeaderCell>
              <Table.HeaderCell>Type</Table.HeaderCell>
              <Table.HeaderCell>Subject</Table.HeaderCell>
              <Table.HeaderCell>Variables</Table.HeaderCell>
              <Table.HeaderCell>Status</Table.HeaderCell>
              <Table.HeaderCell>Actions</Table.HeaderCell>
            </Table.Row>
          </Table.Header>
          <Table.Body>
            {templates.map((template) => (
              <Table.Row key={template.id}>
                <Table.Cell>
                  <div className="font-medium">{template.name}</div>
                </Table.Cell>
                <Table.Cell>{getTypeBadge(template.type)}</Table.Cell>
                <Table.Cell>
                  <Text className="text-sm truncate max-w-xs">
                    {template.subject}
                  </Text>
                </Table.Cell>
                <Table.Cell>
                  <div className="flex flex-wrap gap-1">
                    {template.variables.slice(0, 3).map((variable) => (
                      <Badge key={variable} color="grey" className="text-xs">
                        {variable}
                      </Badge>
                    ))}
                    {template.variables.length > 3 && (
                      <Badge color="grey" className="text-xs">
                        +{template.variables.length - 3}
                      </Badge>
                    )}
                  </div>
                </Table.Cell>
                <Table.Cell>
                  <Badge color={template.is_active ? "green" : "grey"}>
                    {template.is_active ? "Active" : "Inactive"}
                  </Badge>
                </Table.Cell>
                <Table.Cell>
                  <div className="flex gap-2">
                    <Button variant="ghost" size="sm" title="Preview">
                      <Eye className="h-4 w-4" />
                    </Button>
                    {hasPermission("concierge_config:manage") && (
                      <>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleDuplicate(template)}
                          title="Duplicate"
                        >
                          <Copy className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleOpenModal(template)}
                          title="Edit"
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleDelete(template.id)}
                          title="Delete"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </>
                    )}
                  </div>
                </Table.Cell>
              </Table.Row>
            ))}
          </Table.Body>
        </Table>
      </div>

      {/* Create/Edit Modal */}
      <FocusModal open={isModalOpen} onOpenChange={setIsModalOpen}>
        <FocusModal.Content className="max-w-4xl">
          <FocusModal.Header>
            <Heading level="h2">
              {editingTemplate ? "Edit Template" : "Create New Template"}
            </Heading>
          </FocusModal.Header>
          <FocusModal.Body className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium mb-2">Name</label>
                <Input
                  value={formData.name}
                  onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                  placeholder="Enter template name"
                />
              </div>

              <div>
                <label className="block text-sm font-medium mb-2">Type</label>
                <Select
                  value={formData.type}
                  onValueChange={(value) => setFormData({ ...formData, type: value as EmailTemplate["type"] })}
                >
                  <Select.Trigger>
                    <Select.Value />
                  </Select.Trigger>
                  <Select.Content>
                    {typeOptions.map((option) => (
                      <Select.Item key={option.value} value={option.value}>
                        {option.label}
                      </Select.Item>
                    ))}
                  </Select.Content>
                </Select>
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium mb-2">Subject</label>
              <Input
                value={formData.subject}
                onChange={(e) => setFormData({ ...formData, subject: e.target.value })}
                placeholder="Enter email subject (use {{variable}} for dynamic content)"
              />
            </div>

            <div>
              <label className="block text-sm font-medium mb-2">Body</label>
              <Textarea
                value={formData.body}
                onChange={(e) => setFormData({ ...formData, body: e.target.value })}
                placeholder="Enter email body (use {{variable}} for dynamic content)"
                rows={12}
                className="font-mono text-sm"
              />
            </div>

            <div className="flex items-center gap-2">
              <input
                type="checkbox"
                id="is_active"
                checked={formData.is_active}
                onChange={(e) => setFormData({ ...formData, is_active: e.target.checked })}
                className="rounded"
              />
              <label htmlFor="is_active" className="text-sm font-medium">
                Active
              </label>
            </div>

            <div className="bg-muted p-4 rounded-lg">
              <Text className="text-sm font-medium mb-2">Available Variables:</Text>
              <Text className="text-xs text-muted-foreground">
                Use double curly braces to insert variables: {`{{variable_name}}`}
                <br />
                Common variables: guest_name, booking_id, check_in_date, check_out_date, room_type, date, task_title, assignee_name
              </Text>
            </div>
          </FocusModal.Body>
          <FocusModal.Footer>
            <div className="flex gap-2">
              <Button variant="secondary" onClick={handleCloseModal}>
                <X className="h-4 w-4 mr-2" />
                Cancel
              </Button>
              <Button onClick={handleSave}>
                <Save className="h-4 w-4 mr-2" />
                {editingTemplate ? "Update" : "Create"}
              </Button>
            </div>
          </FocusModal.Footer>
        </FocusModal.Content>
      </FocusModal>
    </div>
  );
};

export default EmailTemplateManagement;
