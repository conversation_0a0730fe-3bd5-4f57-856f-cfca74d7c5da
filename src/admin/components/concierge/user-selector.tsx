import React, { useState } from "react";
import { Combobox } from "../common/combobox";
import { useQuery } from "@tanstack/react-query";
import { User } from "lucide-react";
import { sdk } from "../../lib/sdk";

interface User {
  id: string;
  email: string;
  first_name?: string;
  last_name?: string;
  created_at: string;
  updated_at: string;
  metadata?: any;
}

interface UserOption {
  value: string;
  label: string;
  email?: string;
}

interface UserSelectorProps {
  value?: string;
  onChange?: (value?: string) => void;
  placeholder?: string;
  disabled?: boolean;
  className?: string;
  error?: string;
}

// Helper function to get user display name (same logic as roles page)
const getUserDisplayName = (user: User): string => {
  if (user.first_name && user.last_name) {
    return `${user.first_name} ${user.last_name}`;
  }
  return user.email;
};

// API function to fetch users using the same pattern as roles page
const fetchUsers = async (): Promise<UserOption[]> => {
  try {
    // Use the same SDK method as the roles page
    const usersResponse = await sdk.admin.user.list();
    const users = (usersResponse.users || []) as User[];

    // Transform users into options using the same display logic as roles page
    return users.map((user: User) => ({
      value: user.id,
      label: getUserDisplayName(user),
      email: user.email,
    }));
  } catch (error) {
    console.error("Error fetching users:", error);
    // Return empty array if API fails - no fallback users
    return [];
  }
};

export const UserSelector: React.FC<UserSelectorProps> = ({
  value,
  onChange,
  placeholder = "Select a user...",
  disabled = false,
  className = "",
  error,
}) => {
  const [searchValue, setSearchValue] = useState("");

  // Fetch users using the same query pattern as other components
  const { data: users = [], isLoading, error: fetchError } = useQuery({
    queryKey: ["users", "selector"],
    queryFn: fetchUsers,
    staleTime: 5 * 60 * 1000, // Cache for 5 minutes
    gcTime: 10 * 60 * 1000, // Keep in cache for 10 minutes (updated from deprecated cacheTime)
  });

  // Filter users based on search
  const filteredUsers = users.filter((user) =>
    user.label.toLowerCase().includes(searchValue.toLowerCase()) ||
    (user.email && user.email.toLowerCase().includes(searchValue.toLowerCase()))
  );

  // Add "Unassigned" option at the top
  const options = [
    { value: "", label: "Unassigned" },
    ...filteredUsers,
  ];

  return (
    <Combobox
      value={value}
      onChange={onChange}
      searchValue={searchValue}
      onSearchValueChange={setSearchValue}
      options={options}
      placeholder={placeholder}
      disabled={disabled || isLoading}
      className={className}
      noResultsPlaceholder={
        <div className="flex items-center gap-x-2 rounded-[4px] px-2 py-1.5">
          <User className="h-4 w-4 text-ui-fg-subtle" />
          <span className="text-ui-fg-subtle text-sm">
            {fetchError
              ? "Failed to load users"
              : searchValue
                ? `No users found matching "${searchValue}"`
                : "No users available"
            }
          </span>
        </div>
      }
    />
  );
};

export default UserSelector;
