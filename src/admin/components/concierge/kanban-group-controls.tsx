import React from "react";
import { But<PERSON>, DropdownMenu, Text } from "@camped-ai/ui";
import { Settings, ChevronDown } from "lucide-react";
import { BookingScreenData } from "../../routes/concierge-management/bookings/loader";
import { GroupByOption } from "./kanban-view";

interface KanbanGroupControlsProps {
  groupBy: keyof BookingScreenData;
  subGroupBy: keyof BookingScreenData | "none";
  groupByOptions: GroupByOption[];
  subGroupByOptions: GroupByOption[];
  onGroupByChange: (groupBy: keyof BookingScreenData) => void;
  onSubGroupByChange: (subGroupBy: keyof BookingScreenData | "none") => void;
}

export const KanbanGroupControls: React.FC<KanbanGroupControlsProps> = ({
  groupBy,
  subGroupBy,
  groupByOptions,
  subGroupByOptions,
  onGroupByChange,
  onSubGroupByChange,
}) => {
  const currentGroupByLabel = groupByOptions.find(option => option.key === groupBy)?.label || "Status";
  const currentSubGroupByLabel = subGroupByOptions.find(option => option.key === subGroupBy)?.label || "None";

  return (
    <div className="flex flex-col sm:flex-row items-start sm:items-center gap-3">
      {/* Group By Dropdown */}
      <div className="flex items-center gap-2">
        <Text className="text-sm text-gray-600 whitespace-nowrap">Group by:</Text>
        <DropdownMenu>
          <DropdownMenu.Trigger asChild>
            <Button variant="secondary" size="small" className="min-w-[120px] justify-between">
              {currentGroupByLabel}
              <ChevronDown className="w-4 h-4" />
            </Button>
          </DropdownMenu.Trigger>
          <DropdownMenu.Content align="start" className="min-w-[150px]">
            {groupByOptions.map((option) => (
              <DropdownMenu.Item
                key={option.key}
                onClick={() => onGroupByChange(option.key as keyof BookingScreenData)}
                className={groupBy === option.key ? "bg-blue-50 text-blue-700" : ""}
              >
                {option.label}
              </DropdownMenu.Item>
            ))}
          </DropdownMenu.Content>
        </DropdownMenu>
      </div>

      {/* Sub-Group By Dropdown */}
      <div className="flex items-center gap-2">
        <Text className="text-sm text-gray-600 whitespace-nowrap">Sub-group by:</Text>
        <DropdownMenu>
          <DropdownMenu.Trigger asChild>
            <Button variant="secondary" size="small" className="min-w-[120px] justify-between">
              {currentSubGroupByLabel}
              <ChevronDown className="w-4 h-4" />
            </Button>
          </DropdownMenu.Trigger>
          <DropdownMenu.Content align="start" className="min-w-[150px]">
            {subGroupByOptions.map((option) => (
              <DropdownMenu.Item
                key={option.key}
                onClick={() => onSubGroupByChange(option.key)}
                className={subGroupBy === option.key ? "bg-blue-50 text-blue-700" : ""}
              >
                {option.label}
              </DropdownMenu.Item>
            ))}
          </DropdownMenu.Content>
        </DropdownMenu>
      </div>
    </div>
  );
};
