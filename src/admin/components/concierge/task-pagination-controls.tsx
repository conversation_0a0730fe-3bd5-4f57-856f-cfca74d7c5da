import React from "react";
import { useSearchParams } from "react-router-dom";
import {
  Button,
  DropdownMenu,
  Text,
} from "@camped-ai/ui";
import {
  ChevronLeft,
  ChevronRight,
  ChevronsLeft,
  ChevronsRight,
  ChevronDown,
} from "lucide-react";

interface TaskPaginationControlsProps {
  currentPage: number;
  totalCount: number;
  pageSize: number;
  onPageChange: (page: number) => void;
  onPageSizeChange: (pageSize: number) => void;
  className?: string;
}

const pageSizeOptions = [12, 24, 48, 96];

export const TaskPaginationControls: React.FC<TaskPaginationControlsProps> = ({
  currentPage,
  totalCount,
  pageSize,
  onPageChange,
  onPageSizeChange,
  className = "",
}) => {
  const [searchParams, setSearchParams] = useSearchParams();
  
  const totalPages = Math.ceil(totalCount / pageSize);
  const startItem = (currentPage - 1) * pageSize + 1;
  const endItem = Math.min(currentPage * pageSize, totalCount);

  const handlePageChange = (page: number) => {
    if (page >= 1 && page <= totalPages) {
      const newSearchParams = new URLSearchParams(searchParams);
      if (page === 1) {
        newSearchParams.delete("page");
      } else {
        newSearchParams.set("page", page.toString());
      }
      setSearchParams(newSearchParams);
      onPageChange(page);
    }
  };

  const handlePageSizeChange = (newPageSize: number) => {
    const newSearchParams = new URLSearchParams(searchParams);
    newSearchParams.set("limit", newPageSize.toString());
    newSearchParams.delete("page"); // Reset to page 1
    setSearchParams(newSearchParams);
    onPageSizeChange(newPageSize);
  };

  const getVisiblePageNumbers = () => {
    const delta = 2; // Number of pages to show on each side of current page
    const range = [];
    const rangeWithDots = [];

    for (
      let i = Math.max(2, currentPage - delta);
      i <= Math.min(totalPages - 1, currentPage + delta);
      i++
    ) {
      range.push(i);
    }

    if (currentPage - delta > 2) {
      rangeWithDots.push(1, "...");
    } else {
      rangeWithDots.push(1);
    }

    rangeWithDots.push(...range);

    if (currentPage + delta < totalPages - 1) {
      rangeWithDots.push("...", totalPages);
    } else if (totalPages > 1) {
      rangeWithDots.push(totalPages);
    }

    return rangeWithDots;
  };

  if (totalCount === 0) {
    return null;
  }

  return (
    <div className={`flex items-center justify-between ${className}`}>
      {/* Results Info */}
      <div className="flex items-center gap-4">
        <Text size="small" className="text-ui-fg-muted">
          Showing {startItem} to {endItem} of {totalCount} results
        </Text>
        
        {/* Page Size Selector */}
        <div className="flex items-center gap-2">
          <Text size="small" className="text-ui-fg-muted">
            Show:
          </Text>
          <DropdownMenu>
            <DropdownMenu.Trigger asChild>
              <Button variant="secondary" size="small">
                {pageSize}
                <ChevronDown className="h-4 w-4 ml-1" />
              </Button>
            </DropdownMenu.Trigger>
            <DropdownMenu.Content>
              {pageSizeOptions.map((size) => (
                <DropdownMenu.Item
                  key={size}
                  onClick={() => handlePageSizeChange(size)}
                  className={pageSize === size ? "bg-ui-bg-subtle" : ""}
                >
                  {size} per page
                </DropdownMenu.Item>
              ))}
            </DropdownMenu.Content>
          </DropdownMenu>
        </div>
      </div>

      {/* Pagination Controls */}
      {totalPages > 1 && (
        <div className="flex items-center gap-1">
          {/* First Page */}
          <Button
            variant="secondary"
            size="small"
            onClick={() => handlePageChange(1)}
            disabled={currentPage === 1}
            title="First page"
          >
            <ChevronsLeft className="h-4 w-4" />
          </Button>

          {/* Previous Page */}
          <Button
            variant="secondary"
            size="small"
            onClick={() => handlePageChange(currentPage - 1)}
            disabled={currentPage === 1}
            title="Previous page"
          >
            <ChevronLeft className="h-4 w-4" />
          </Button>

          {/* Page Numbers */}
          <div className="flex items-center gap-1">
            {getVisiblePageNumbers().map((page, index) => (
              <React.Fragment key={index}>
                {page === "..." ? (
                  <span className="px-2 py-1 text-ui-fg-muted">...</span>
                ) : (
                  <Button
                    variant={currentPage === page ? "primary" : "secondary"}
                    size="small"
                    onClick={() => handlePageChange(page as number)}
                    className="min-w-[32px]"
                  >
                    {page}
                  </Button>
                )}
              </React.Fragment>
            ))}
          </div>

          {/* Next Page */}
          <Button
            variant="secondary"
            size="small"
            onClick={() => handlePageChange(currentPage + 1)}
            disabled={currentPage === totalPages}
            title="Next page"
          >
            <ChevronRight className="h-4 w-4" />
          </Button>

          {/* Last Page */}
          <Button
            variant="secondary"
            size="small"
            onClick={() => handlePageChange(totalPages)}
            disabled={currentPage === totalPages}
            title="Last page"
          >
            <ChevronsRight className="h-4 w-4" />
          </Button>
        </div>
      )}
    </div>
  );
};
