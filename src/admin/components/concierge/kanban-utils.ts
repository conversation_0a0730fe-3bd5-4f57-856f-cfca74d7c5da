import { BookingScreenData } from "../../routes/concierge-management/bookings/loader";
import { format } from "date-fns";

export interface GroupByOption {
  key: keyof BookingScreenData | "none";
  label: string;
}

// Available grouping options based on booking data fields
export const getGroupByOptions = (): GroupByOption[] => [
  { key: "status", label: "Status" },
  { key: "assigned_to", label: "Assigned To" },
  { key: "order_status", label: "Order Status" },
  { key: "hotel_name", label: "Hotel" },
  { key: "customer_first_name", label: "Customer" },
  { key: "check_in_date", label: "Check-in Date" },
  { key: "created_at", label: "Created Date" },
];

export const getSubGroupByOptions = (excludeKey?: keyof BookingScreenData): GroupByOption[] => [
  { key: "none", label: "None" },
  ...getGroupByOptions().filter(option => option.key !== excludeKey),
];

// Format group key for display
export const formatGroupKey = (
  value: any,
  groupBy: keyof BookingScreenData
): string => {
  if (!value) return "Unassigned";

  switch (groupBy) {
    case "check_in_date":
    case "created_at":
      try {
        return format(new Date(value), "MMM d, yyyy");
      } catch {
        return "Invalid Date";
      }
    
    case "customer_first_name":
      // For customer grouping, we might want to combine first and last name
      return String(value);
    
    case "assigned_to":
      return value === null ? "Unassigned" : String(value);
    
    case "status":
    case "order_status":
      return String(value).replace(/_/g, " ").replace(/\b\w/g, l => l.toUpperCase());
    
    default:
      return String(value);
  }
};

// Group bookings by a specific field
export const groupBookings = (
  bookings: BookingScreenData[],
  groupBy: keyof BookingScreenData
): Record<string, BookingScreenData[]> => {
  const groups: Record<string, BookingScreenData[]> = {};

  bookings.forEach((booking) => {
    const value = booking[groupBy];
    const groupKey = formatGroupKey(value, groupBy);

    if (!groups[groupKey]) {
      groups[groupKey] = [];
    }
    groups[groupKey].push(booking);
  });

  return groups;
};

// Group bookings with sub-grouping
export const groupBookingsWithSubGroup = (
  bookings: BookingScreenData[],
  groupBy: keyof BookingScreenData,
  subGroupBy: keyof BookingScreenData
): Record<string, Record<string, BookingScreenData[]>> => {
  const result: Record<string, Record<string, BookingScreenData[]>> = {};

  // First group by main field
  const mainGroups = groupBookings(bookings, groupBy);

  // Then sub-group each main group
  Object.entries(mainGroups).forEach(([groupKey, groupBookingsArray]) => {
    result[groupKey] = groupBookings(groupBookingsArray, subGroupBy);
  });

  return result;
};

// Get default status columns for kanban
export const getDefaultStatusColumns = (): string[] => [
  "pending",
  "confirmed", 
  "in_progress",
  "completed",
  "cancelled",
  "on_hold",
];

// Sort groups for better display order
export const sortGroups = (
  groups: Record<string, any>,
  groupBy: keyof BookingScreenData
): [string, any][] => {
  const entries = Object.entries(groups);

  switch (groupBy) {
    case "status":
      // Sort by predefined status order
      const statusOrder = getDefaultStatusColumns();
      return entries.sort(([a], [b]) => {
        const aIndex = statusOrder.indexOf(a.toLowerCase());
        const bIndex = statusOrder.indexOf(b.toLowerCase());
        
        if (aIndex === -1 && bIndex === -1) return a.localeCompare(b);
        if (aIndex === -1) return 1;
        if (bIndex === -1) return -1;
        
        return aIndex - bIndex;
      });

    case "check_in_date":
    case "created_at":
      // Sort by date
      return entries.sort(([a], [b]) => {
        if (a === "Unassigned") return 1;
        if (b === "Unassigned") return -1;
        
        try {
          const dateA = new Date(a);
          const dateB = new Date(b);
          return dateA.getTime() - dateB.getTime();
        } catch {
          return a.localeCompare(b);
        }
      });

    case "assigned_to":
      // Sort with "Unassigned" last
      return entries.sort(([a], [b]) => {
        if (a === "Unassigned") return 1;
        if (b === "Unassigned") return -1;
        return a.localeCompare(b);
      });

    default:
      // Default alphabetical sort
      return entries.sort(([a], [b]) => a.localeCompare(b));
  }
};

// Validate if a field can be used for drag-and-drop updates
export const canUpdateField = (groupBy: keyof BookingScreenData): boolean => {
  const updatableFields: (keyof BookingScreenData)[] = [
    "status",
    "assigned_to",
    "order_status",
  ];
  
  return updatableFields.includes(groupBy);
};

// Get update payload for drag-and-drop
export const getUpdatePayload = (
  groupBy: keyof BookingScreenData,
  targetGroup: string
): Partial<BookingScreenData> => {
  const updates: Partial<BookingScreenData> = {};

  switch (groupBy) {
    case "status":
      updates.status = targetGroup.toLowerCase().replace(/ /g, "_");
      break;
    
    case "assigned_to":
      updates.assigned_to = targetGroup === "Unassigned" ? null : targetGroup;
      break;
    
    case "order_status":
      updates.order_status = targetGroup.toLowerCase().replace(/ /g, "_");
      break;
  }

  return updates;
};
