import React from "react";
import { useNavigate } from "react-router-dom";
import { format } from "date-fns";
import {
  <PERSON><PERSON>,
  <PERSON>ton,
  Text,
  DropdownMenu,
  Container,
} from "@camped-ai/ui";
import { 
  Edit, 
  Eye, 
  MoreHorizontal, 
  Trash, 
  Calendar,
  Clock,
  User,
  AlertTriangle,
} from "lucide-react";
import { UserDisplayName } from "./user-display-name";
import type { TaskScreenData } from "../../routes/concierge-management/tasks/loader";

// Status badge colors
const statusColors = {
  pending: "orange",
  in_progress: "blue",
  review: "orange",
  completed: "green",
  cancelled: "grey",
} as const;

const priorityColors = {
  low: "grey",
  medium: "blue",
  high: "orange",
  urgent: "red",
} as const;

interface TaskCardProps {
  task: TaskScreenData;
  onEdit?: (task: TaskScreenData) => void;
  onDelete?: (task: TaskScreenData) => void;
  onCopyId?: (taskId: string) => void;
  hasEditPermission?: boolean;
  hasDeletePermission?: boolean;
}

export const TaskCard: React.FC<TaskCardProps> = ({
  task,
  onEdit,
  onDelete,
  onCopyId,
  hasEditPermission = false,
  hasDeletePermission = false,
}) => {
  const navigate = useNavigate();

  const getStatusBadgeVariant = (status: string) => {
    return statusColors[status as keyof typeof statusColors] || "grey";
  };

  const getPriorityBadgeVariant = (priority: string) => {
    return priorityColors[priority as keyof typeof priorityColors] || "grey";
  };

  const getPriorityIcon = (priority: string) => {
    switch (priority) {
      case "urgent":
        return <AlertTriangle className="h-3 w-3" />;
      case "high":
        return <AlertTriangle className="h-3 w-3" />;
      default:
        return null;
    }
  };

  const handleCardClick = () => {
    navigate(`/concierge-management/tasks/${task.id}`);
  };

  const handleCopyId = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (onCopyId) {
      onCopyId(task.id);
    }
  };

  return (
    <Container className="p-4 hover:shadow-md transition-shadow cursor-pointer group border border-ui-border-base rounded-lg bg-ui-bg-base">
      <div onClick={handleCardClick} className="space-y-3">
        {/* Header with title and actions */}
        <div className="flex items-start justify-between">
          <div className="flex-1 min-w-0">
            <div className="flex items-center gap-2 mb-1">
              <Calendar className="h-4 w-4 text-ui-fg-subtle flex-shrink-0" />
              <Text className="font-medium truncate" weight="plus">
                {task.title}
              </Text>
            </div>
            <button
              onClick={handleCopyId}
              className="text-xs text-ui-fg-muted hover:text-ui-fg-subtle transition-colors"
            >
              ID: {task.id.slice(0, 8)}...
            </button>
          </div>
          
          <div className="flex items-center gap-2 opacity-0 group-hover:opacity-100 transition-opacity">
            <DropdownMenu>
              <DropdownMenu.Trigger asChild>
                <Button
                  variant="secondary"
                  size="small"
                  onClick={(e) => e.stopPropagation()}
                >
                  <MoreHorizontal className="h-4 w-4" />
                </Button>
              </DropdownMenu.Trigger>
              <DropdownMenu.Content>
                <DropdownMenu.Item
                  onClick={(e) => {
                    e.stopPropagation();
                    navigate(`/concierge-management/tasks/${task.id}`);
                  }}
                >
                  <Eye className="h-4 w-4 mr-2" />
                  View
                </DropdownMenu.Item>
                {hasEditPermission && onEdit && (
                  <DropdownMenu.Item
                    onClick={(e) => {
                      e.stopPropagation();
                      onEdit(task);
                    }}
                  >
                    <Edit className="h-4 w-4 mr-2" />
                    Edit
                  </DropdownMenu.Item>
                )}
                {hasDeletePermission && onDelete && (
                  <DropdownMenu.Item
                    onClick={(e) => {
                      e.stopPropagation();
                      onDelete(task);
                    }}
                    className="text-red-600"
                  >
                    <Trash className="h-4 w-4 mr-2" />
                    Delete
                  </DropdownMenu.Item>
                )}
              </DropdownMenu.Content>
            </DropdownMenu>
          </div>
        </div>

        {/* Description */}
        {task.description && (
          <div className="text-sm text-ui-fg-subtle line-clamp-2">
            {task.description}
          </div>
        )}

        {/* Status and Priority badges */}
        <div className="flex items-center gap-2 flex-wrap">
          <Badge color={getStatusBadgeVariant(task.status)} size="xsmall">
            {task.status.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}
          </Badge>
          <div className="flex items-center gap-1">
            {getPriorityIcon(task.priority)}
            <Badge color={getPriorityBadgeVariant(task.priority)} size="xsmall">
              {task.priority.charAt(0).toUpperCase() + task.priority.slice(1)}
            </Badge>
          </div>
          {task.entity_type && (
            <Badge variant="outline" size="xsmall">
              {task.entity_type.charAt(0).toUpperCase() + task.entity_type.slice(1)}
            </Badge>
          )}
        </div>

        {/* Assigned user and due date */}
        <div className="space-y-2 text-sm">
          <div className="flex items-center gap-2">
            <User className="h-3 w-3 text-ui-fg-subtle" />
            <UserDisplayName userId={task.assigned_to} />
          </div>
          
          <div className="flex items-center gap-2">
            <Clock className="h-3 w-3 text-ui-fg-subtle" />
            <Text size="small" className={task.due_date ? "" : "text-ui-fg-subtle"}>
              {task.due_date
                ? format(new Date(task.due_date), "MMM dd, yyyy")
                : "No due date"}
            </Text>
          </div>
        </div>

        {/* Created date */}
        <div className="pt-2 border-t border-ui-border-base">
          <Text size="xsmall" className="text-ui-fg-muted">
            Created {format(new Date(task.created_at), "MMM dd, yyyy")}
          </Text>
        </div>
      </div>
    </Container>
  );
};
