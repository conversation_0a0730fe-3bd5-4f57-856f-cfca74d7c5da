import React, { useEffect } from "react";
import { use<PERSON><PERSON>, Controller } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import {
  Button,
  Input,
  Textarea,
  Select,
  Label,
  FocusModal,
  Heading,
  Text,
  toast,
} from "@camped-ai/ui";
import { useTranslation } from "react-i18next";
import {
  useCreateConciergeTask,
  useCreateBookingTask,
  useUpdateConciergeTask,
  ConciergeTask,
} from "../../hooks/api/concierge-tasks";
import { UserSelector } from "./user-selector";
import "../../styles/task-modal.css";

// Form validation schema
const taskFormSchema = z.object({
  title: z
    .string()
    .min(1, "Title is required")
    .max(255, "Title must be less than 255 characters")
    .trim(),
  description: z
    .string()
    .max(1000, "Description must be less than 1000 characters")
    .optional()
    .or(z.literal("")),
  status: z
    .enum(["pending", "in_progress", "review", "completed", "cancelled"], {
      errorMap: () => ({ message: "Please select a valid status" }),
    })
    .default("pending"),
  priority: z
    .enum(["low", "medium", "high", "urgent"], {
      errorMap: () => ({ message: "Please select a valid priority" }),
    })
    .default("medium"),
  entity_type: z.string().optional().or(z.literal("")).or(z.literal("general")),
  entity_id: z.string().optional().or(z.literal("")),
  assigned_to: z
    .string()
    .max(100, "Assigned to field must be less than 100 characters")
    .optional()
    .or(z.literal("")),
  due_date: z
    .string()
    .optional()
    .or(z.literal(""))
    .refine((date) => {
      if (!date) return true; // Optional field
      const parsedDate = new Date(date);
      return !isNaN(parsedDate.getTime());
    }, "Please enter a valid date"),
});

type TaskFormData = z.infer<typeof taskFormSchema>;

interface TaskFormModalProps {
  isOpen?: boolean;
  onClose: () => void;
  task?: ConciergeTask; // For editing existing tasks
  bookingId?: string; // For creating tasks linked to a booking
  entityType?: string; // For creating tasks linked to other entities
  entityId?: string;
  bookingDisplayId?: string; // For showing booking context
  inline?: boolean; // If true, render as inline form instead of modal
  defaultAssignedTo?: string; // Default value for assigned_to when creating
}

export const TaskFormModal: React.FC<TaskFormModalProps> = ({
  isOpen,
  onClose,
  task,
  bookingId,
  entityType,
  entityId,
  bookingDisplayId,
  inline = false,
  defaultAssignedTo = "",
}) => {
  const { t } = useTranslation();
  const isEditing = !!task;
  const isBookingContext = !!bookingId;

  // Choose the appropriate mutation based on context
  const createTaskMutation = useCreateConciergeTask();
  const createBookingTaskMutation = useCreateBookingTask(bookingId || "");
  const updateTaskMutation = useUpdateConciergeTask();

  const {
    register,
    handleSubmit,
    reset,
    setValue,
    watch,
    control,
    formState: { errors, isSubmitting },
  } = useForm<TaskFormData>({
    resolver: zodResolver(taskFormSchema),
    defaultValues: {
      title: "",
      description: "",
      status: "pending",
      priority: "medium",
      entity_type: "general",
      entity_id: "",
      assigned_to: "",
      due_date: "",
    },
  });

  // Initialize form when modal opens
  useEffect(() => {
    if (isOpen || inline) {
      if (isEditing && task) {
        // Populate form fields when editing a task
        setValue("title", task.title || "");
        setValue("description", task.description || "");
        setValue("status", task.status || "pending");
        setValue("priority", task.priority || "medium");
        setValue("entity_type", task.entity_type || "general");
        setValue("entity_id", task.entity_id || "");
        setValue("assigned_to", task.assigned_to || "");
        setValue(
          "due_date",
          task.due_date
            ? new Date(task.due_date).toISOString().split("T")[0]
            : ""
        );
      } else {
        // Reset to default values for new task
        reset({
          title: "",
          description: "",
          status: "pending",
          priority: "medium",
          entity_type: isBookingContext ? "booking" : entityType || "general",
          entity_id: isBookingContext ? bookingId || "" : entityId || "",
          assigned_to: defaultAssignedTo || "",
          due_date: "",
        });
      }
    }
  }, [
    isOpen,
    inline,
    isEditing,
    task,
    isBookingContext,
    bookingId,
    entityType,
    entityId,
    setValue,
    reset,
    defaultAssignedTo,
  ]);

  const onSubmit = async (data: TaskFormData) => {
    try {
      const taskData = {
        ...data,
        due_date: data.due_date
          ? new Date(data.due_date).toISOString()
          : undefined,
        // Ensure entity fields are set for booking context
        entity_type:
          isBookingContext && !isEditing
            ? "booking"
            : data.entity_type === "general"
            ? ""
            : data.entity_type,
        entity_id: isBookingContext && !isEditing ? bookingId : data.entity_id,
      };

      if (isEditing && task) {
        // Update existing task
        await updateTaskMutation.mutateAsync({
          taskId: task.id,
          data: taskData,
        });
        // Success toast is handled by the mutation hook
      } else if (bookingId) {
        // Create task for booking
        await createBookingTaskMutation.mutateAsync(taskData);
        // Success toast is handled by the mutation hook
      } else {
        // Create general task
        await createTaskMutation.mutateAsync(taskData);
        // Success toast is handled by the mutation hook
      }

      onClose();
    } catch (error) {
      console.error("Error saving task:", error);
      toast.error("Failed to save task. Please try again.");
    }
  };

  const handleClose = () => {
    reset();
    onClose();
  };

  // Form content component to avoid duplication
  const FormContent = () => (
    <form
      id="task-form"
      onSubmit={handleSubmit(onSubmit)}
      className={
        inline ? "bg-white rounded-lg shadow-sm border border-border p-6" : ""
      }
    >
      <div className="space-y-6">
        {/* Header for inline mode */}
        {inline && (
          <div className="mb-6">
            <Heading
              level="h3"
              className="text-lg font-semibold text-foreground"
            >
              {task ? "Edit Task" : "Create New Task"}
            </Heading>
          </div>
        )}

        {/* Title */}
        <div className="space-y-2">
          <Label
            htmlFor="title"
            className="text-sm font-medium text-foreground"
          >
            Title <span className="text-destructive">*</span>
          </Label>
          <Input
            id="title"
            {...register("title")}
            placeholder="Enter task title"
            className={`w-full ${
              errors.title ? "border-destructive focus:border-destructive" : ""
            }`}
            disabled={isSubmitting}
          />
          {errors.title && (
            <Text className="text-xs text-destructive">
              {errors.title.message}
            </Text>
          )}
        </div>

        {/* Description */}
        <div className="space-y-2">
          <Label
            htmlFor="description"
            className="text-sm font-medium text-foreground"
          >
            Description
          </Label>
          <Textarea
            id="description"
            {...register("description")}
            placeholder="Enter task description (optional)"
            rows={3}
            className={`w-full ${
              errors.description
                ? "border-destructive focus:border-destructive"
                : ""
            }`}
            disabled={isSubmitting}
          />
          {errors.description && (
            <Text className="text-xs text-destructive">
              {errors.description.message}
            </Text>
          )}
        </div>

        {/* Status and Priority Row */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label
              htmlFor="status"
              className="text-sm font-medium text-foreground"
            >
              Status
            </Label>
            <Controller
              name="status"
              control={control}
              render={({ field }) => (
                <Select
                  value={field.value}
                  onValueChange={field.onChange}
                  disabled={isSubmitting}
                >
                  <Select.Trigger
                    className={`w-full ${
                      errors.status
                        ? "border-destructive focus:border-destructive"
                        : ""
                    }`}
                  >
                    <Select.Value placeholder="Select status" />
                  </Select.Trigger>
                  <Select.Content>
                    <Select.Item value="pending">Pending</Select.Item>
                    <Select.Item value="in_progress">In Progress</Select.Item>
                    <Select.Item value="review">Review</Select.Item>
                    <Select.Item value="completed">Completed</Select.Item>
                    <Select.Item value="cancelled">Cancelled</Select.Item>
                  </Select.Content>
                </Select>
              )}
            />
            {errors.status && (
              <Text className="text-xs text-destructive">
                {errors.status.message}
              </Text>
            )}
          </div>

          <div className="space-y-2">
            <Label
              htmlFor="priority"
              className="text-sm font-medium text-foreground"
            >
              Priority
            </Label>
            <Controller
              name="priority"
              control={control}
              render={({ field }) => (
                <Select
                  value={field.value}
                  onValueChange={field.onChange}
                  disabled={isSubmitting}
                >
                  <Select.Trigger
                    className={`w-full ${
                      errors.priority
                        ? "border-destructive focus:border-destructive"
                        : ""
                    }`}
                  >
                    <Select.Value placeholder="Select priority" />
                  </Select.Trigger>
                  <Select.Content>
                    <Select.Item value="low">Low</Select.Item>
                    <Select.Item value="medium">Medium</Select.Item>
                    <Select.Item value="high">High</Select.Item>
                    <Select.Item value="urgent">Urgent</Select.Item>
                  </Select.Content>
                </Select>
              )}
            />
            {errors.priority && (
              <Text className="text-xs text-destructive">
                {errors.priority.message}
              </Text>
            )}
          </div>
        </div>

        {/* Entity Information (hidden when in booking context or editing) */}
        {!isEditing && !isBookingContext && (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label
                htmlFor="entity_type"
                className="text-sm font-medium text-foreground"
              >
                Entity Type
              </Label>
              <Controller
                name="entity_type"
                control={control}
                render={({ field }) => (
                  <Select
                    value={field.value}
                    onValueChange={field.onChange}
                    disabled={isSubmitting}
                  >
                    <Select.Trigger
                      className={`w-full ${
                        errors.entity_type
                          ? "border-destructive focus:border-destructive"
                          : ""
                      }`}
                    >
                      <Select.Value placeholder="Select entity type" />
                    </Select.Trigger>
                    <Select.Content>
                      <Select.Item value="general">General Task</Select.Item>
                      <Select.Item value="booking">Booking</Select.Item>
                      <Select.Item value="deal">Deal</Select.Item>
                      <Select.Item value="guest">Guest</Select.Item>
                      <Select.Item value="itinerary">Itinerary</Select.Item>
                    </Select.Content>
                  </Select>
                )}
              />
              {errors.entity_type && (
                <Text className="text-xs text-destructive">
                  {errors.entity_type.message}
                </Text>
              )}
            </div>

            <div className="space-y-2">
              <Label
                htmlFor="entity_id"
                className="text-sm font-medium text-foreground"
              >
                Entity ID
              </Label>
              <Input
                id="entity_id"
                {...register("entity_id")}
                placeholder="Enter entity ID (optional)"
                className={`w-full ${
                  errors.entity_id
                    ? "border-destructive focus:border-destructive"
                    : ""
                }`}
                disabled={isSubmitting}
              />
              {errors.entity_id && (
                <Text className="text-xs text-destructive">
                  {errors.entity_id.message}
                </Text>
              )}
            </div>
          </div>
        )}

        {/* Assignment and Due Date Row */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label
              htmlFor="assigned_to"
              className="text-sm font-medium text-foreground"
            >
              Assigned To
            </Label>
            <UserSelector
              value={watch("assigned_to")}
              onChange={(value) => setValue("assigned_to", value || "")}
              placeholder="Select a user (optional)"
              disabled={isSubmitting}
              className={
                errors.assigned_to
                  ? "border-destructive focus:border-destructive"
                  : ""
              }
            />
            {errors.assigned_to && (
              <Text className="text-xs text-destructive">
                {errors.assigned_to.message}
              </Text>
            )}
          </div>

          <div className="space-y-2">
            <Label
              htmlFor="due_date"
              className="text-sm font-medium text-foreground"
            >
              Due Date
            </Label>
            <Input
              id="due_date"
              type="date"
              {...register("due_date")}
              className={`w-full ${
                errors.due_date
                  ? "border-destructive focus:border-destructive"
                  : ""
              }`}
              disabled={isSubmitting}
            />
            {errors.due_date && (
              <Text className="text-xs text-destructive">
                {errors.due_date.message}
              </Text>
            )}
          </div>
        </div>
      </div>

      {/* Inline Form Actions - Only show for inline mode */}
      {inline && (
        <div className="flex justify-end gap-2 pt-4 mt-6 border-t border-border">
          <Button
            type="button"
            variant="secondary"
            onClick={handleClose}
            disabled={isSubmitting}
            size="small"
          >
            Cancel
          </Button>
          <Button
            type="submit"
            disabled={isSubmitting}
            isLoading={isSubmitting}
            size="small"
          >
            {isEditing ? "Update Task" : "Create Task"}
          </Button>
        </div>
      )}
    </form>
  );

  if (inline) {
    return <FormContent />;
  }

  // Default: modal rendering
  return (
    <FocusModal open={isOpen} onOpenChange={handleClose}>
      <FocusModal.Content className="shadow-lg flex flex-col">
        <FocusModal.Header className="px-6 py-4 border-b border-border bg-background/95 backdrop-blur-sm text-left">
          <Heading level="h2" className="text-lg font-semibold text-foreground">
            {isEditing ? "Edit Task" : "Create New Task"}
          </Heading>
        </FocusModal.Header>
        <FocusModal.Body className="flex-1 overflow-y-auto px-6 py-6">
          <FormContent />
        </FocusModal.Body>
        <FocusModal.Footer className="flex justify-end gap-3 px-6 py-4 border-t border-border bg-background/95 backdrop-blur-sm">
          <Button
            type="button"
            variant="secondary"
            onClick={handleClose}
            disabled={isSubmitting}
            size="base"
          >
            Cancel
          </Button>
          <Button
            type="submit"
            form="task-form"
            disabled={isSubmitting}
            isLoading={isSubmitting}
            size="base"
          >
            {isEditing ? "Update Task" : "Create Task"}
          </Button>
        </FocusModal.Footer>
      </FocusModal.Content>
    </FocusModal>
  );
};
