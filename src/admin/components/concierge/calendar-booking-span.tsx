import React from "react";
import { Text, Badge, Tooltip } from "@camped-ai/ui";
import { MapPin, DollarSign, User } from "lucide-react";
import { format, differenceInDays, isSameDay } from "date-fns";
import { BookingScreenData } from "../../routes/concierge-management/bookings/loader";
import { UserDisplayName } from "./user-display-name";

interface CalendarBookingSpanProps {
  booking: BookingScreenData;
  startDate: Date;
  endDate: Date;
  onClick: () => void;
  spanIndex: number; // For stacking overlapping bookings
  totalSpans: number; // Total number of overlapping bookings
  gridStartDay: Date; // First day of the calendar grid
  gridEndDay: Date; // Last day of the calendar grid
  daysInWeek: number; // Number of days in a week (7)
}

export const CalendarBookingSpan: React.FC<CalendarBookingSpanProps> = ({
  booking,
  startDate,
  endDate,
  onClick,
  spanIndex,
  totalSpans,
  gridStartDay,
  gridEndDay,
  daysInWeek,
}) => {
  // Calculate the actual span within the visible calendar grid
  const actualStartDate = startDate < gridStartDay ? gridStartDay : startDate;
  const actualEndDate = endDate > gridEndDay ? gridEndDay : endDate;
  
  // Calculate grid position
  const startDayIndex = differenceInDays(actualStartDate, gridStartDay);
  const endDayIndex = differenceInDays(actualEndDate, gridStartDay);
  const spanDays = endDayIndex - startDayIndex + 1;

  // Calculate grid column and row positions
  const startColumn = (startDayIndex % daysInWeek) + 1;
  const startRow = Math.floor(startDayIndex / daysInWeek) + 1;
  
  // Handle multi-week spans
  const isMultiWeek = Math.floor(startDayIndex / daysInWeek) !== Math.floor(endDayIndex / daysInWeek);
  
  // Get status color
  const getStatusColor = (status: string) => {
    const statusColors: Record<string, string> = {
      pending: "orange",
      confirmed: "blue",
      in_progress: "blue",
      completed: "green",
      cancelled: "red",
      on_hold: "yellow",
    };
    return statusColors[status?.toLowerCase()] || "gray";
  };

  // Format currency
  const formatCurrency = (amount: number | undefined, currencyCode: string = "USD") => {
    if (!amount) return "N/A";
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: currencyCode,
    }).format(amount);
  };

  // Calculate duration in nights
  const calculateNights = () => {
    const diffTime = endDate.getTime() - startDate.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays;
  };

  const statusColor = getStatusColor(booking.status);
  const nights = calculateNights();

  // Calculate height and position for stacking
  const spanHeight = 24; // Height of each span in pixels
  const spanGap = 2; // Gap between stacked spans
  const topOffset = spanIndex * (spanHeight + spanGap);

  // Determine if this is a single day booking
  const isSingleDay = isSameDay(startDate, endDate);

  // Create tooltip content
  const tooltipContent = (
    <div className="p-2 space-y-1 max-w-xs">
      <Text className="font-medium text-sm">
        {booking.customer_first_name} {booking.customer_last_name}
      </Text>
      <Text className="text-xs text-gray-600">{booking.hotel_name}</Text>
      <Text className="text-xs">
        {format(startDate, "MMM d")} - {format(endDate, "MMM d")} ({nights} nights)
      </Text>
      {booking.order_total && (
        <Text className="text-xs">
          {formatCurrency(booking.order_total, booking.order_currency_code)}
        </Text>
      )}
      <Badge size="small" color={statusColor} className="text-xs">
        {booking.status}
      </Badge>
      {booking.assigned_to && (
        <Text className="text-xs">
          Assigned: <UserDisplayName userId={booking.assigned_to} />
        </Text>
      )}
    </div>
  );

  if (isMultiWeek) {
    // For multi-week spans, we need to render multiple segments
    const segments = [];
    let currentWeekStart = startDayIndex;
    
    while (currentWeekStart <= endDayIndex) {
      const currentWeekEnd = Math.min(
        currentWeekStart + (daysInWeek - (currentWeekStart % daysInWeek)) - 1,
        endDayIndex
      );
      
      const segmentStartColumn = (currentWeekStart % daysInWeek) + 1;
      const segmentEndColumn = (currentWeekEnd % daysInWeek) + 1;
      const segmentRow = Math.floor(currentWeekStart / daysInWeek) + 1;
      const segmentSpan = segmentEndColumn - segmentStartColumn + 1;
      
      segments.push(
        <Tooltip key={`${booking.id}-${currentWeekStart}`} content={tooltipContent}>
          <div
            onClick={onClick}
            className={`absolute cursor-pointer rounded px-2 py-1 text-xs font-medium transition-opacity hover:opacity-80 ${
              statusColor === "blue" ? "bg-blue-500 text-white" :
              statusColor === "green" ? "bg-green-500 text-white" :
              statusColor === "orange" ? "bg-orange-500 text-white" :
              statusColor === "red" ? "bg-red-500 text-white" :
              statusColor === "yellow" ? "bg-yellow-500 text-black" :
              "bg-gray-500 text-white"
            }`}
            style={{
              gridColumn: `${segmentStartColumn} / span ${segmentSpan}`,
              gridRow: segmentRow,
              top: `${topOffset}px`,
              height: `${spanHeight}px`,
              zIndex: 10 + spanIndex,
              lineHeight: `${spanHeight}px`,
            }}
          >
            <Text className="truncate text-xs font-medium">
              {booking.customer_first_name} {booking.customer_last_name}
            </Text>
          </div>
        </Tooltip>
      );
      
      currentWeekStart = currentWeekEnd + 1;
    }
    
    return <>{segments}</>;
  }

  // Single week span
  return (
    <Tooltip content={tooltipContent}>
      <div
        onClick={onClick}
        className={`absolute cursor-pointer rounded px-2 py-1 text-xs font-medium transition-opacity hover:opacity-80 ${
          statusColor === "blue" ? "bg-blue-500 text-white" :
          statusColor === "green" ? "bg-green-500 text-white" :
          statusColor === "orange" ? "bg-orange-500 text-white" :
          statusColor === "red" ? "bg-red-500 text-white" :
          statusColor === "yellow" ? "bg-yellow-500 text-black" :
          "bg-gray-500 text-white"
        } ${isSingleDay ? "border-l-4 border-l-white" : ""}`}
        style={{
          gridColumn: `${startColumn} / span ${spanDays}`,
          gridRow: startRow,
          top: `${topOffset}px`,
          height: `${spanHeight}px`,
          zIndex: 10 + spanIndex,
          lineHeight: `${spanHeight}px`,
        }}
      >
        <div className="flex items-center justify-between">
          <Text className="truncate text-xs font-medium">
            {booking.customer_first_name} {booking.customer_last_name}
          </Text>
          {booking.hotel_name && (
            <Text className="text-xs opacity-75 ml-2 truncate">
              {booking.hotel_name}
            </Text>
          )}
        </div>
      </div>
    </Tooltip>
  );
};
