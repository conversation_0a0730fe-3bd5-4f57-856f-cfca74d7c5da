import React, { useState, useEffect } from "react";
import {
  Container,
  Heading,
  Text,
  Button,
  Input,
  Select,
  Badge,
  Table,
  Toaster,
  toast,
} from "@camped-ai/ui";
import {
  Plus,
  Search,
  Filter,
  Calendar,
  MapPin,
  Clock,
  Edit,
  Trash2,
  Eye,
} from "lucide-react";
import { useRbac } from "../../hooks/use-rbac";

// Types
interface Itinerary {
  id: string;
  booking_id: string;
  title?: string;
  status: "DRAFT" | "FINALIZED";
  created_by?: string;
  created_at: string;
  updated_at: string;
  days: Array<{
    id: string;
    date: string;
    title?: string;
    events: Array<{
      id: string;
      category: string;
      title: string;
    }>;
  }>;
}

const ItineraryManagement: React.FC = () => {
  const [itineraries, setItineraries] = useState<Itinerary[]>([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState<string>("all");
  const [isLoading, setIsLoading] = useState(false);
  const { hasPermission } = useRbac();

  // Mock data for demonstration
  useEffect(() => {
    const mockItineraries: Itinerary[] = [
      {
        id: "1",
        guest_name: "<PERSON>",
        guest_email: "<EMAIL>",
        booking_id: "BK001",
        date: "2024-01-15",
        status: "confirmed",
        items: [
          {
            id: "1",
            title: "Airport Pickup",
            description: "Luxury car service from JFK",
            type: "transport",
            start_time: "14:00",
            end_time: "15:30",
            location: "JFK Airport",
            notes: "Flight AA123 - Terminal 4",
          },
          {
            id: "2",
            title: "Hotel Check-in",
            type: "accommodation",
            start_time: "16:00",
            location: "Hotel Lobby",
          },
          {
            id: "3",
            title: "Welcome Dinner",
            description: "Reservation at Le Bernardin",
            type: "dining",
            start_time: "19:30",
            end_time: "22:00",
            location: "Le Bernardin Restaurant",
          },
        ],
        created_at: "2024-01-10T10:00:00Z",
        updated_at: "2024-01-12T14:30:00Z",
      },
      {
        id: "2",
        guest_name: "Sarah Johnson",
        guest_email: "<EMAIL>",
        booking_id: "BK002",
        date: "2024-01-16",
        status: "draft",
        items: [
          {
            id: "4",
            title: "City Tour",
            description: "Private guided tour of Manhattan",
            type: "activity",
            start_time: "10:00",
            end_time: "16:00",
            location: "Hotel Lobby (Pickup)",
          },
          {
            id: "5",
            title: "Business Meeting",
            type: "meeting",
            start_time: "17:00",
            end_time: "18:30",
            location: "Conference Room A",
          },
        ],
        created_at: "2024-01-11T09:15:00Z",
        updated_at: "2024-01-11T09:15:00Z",
      },
    ];
    setItineraries(mockItineraries);
  }, []);

  const filteredItineraries = itineraries.filter((itinerary) => {
    const matchesSearch = 
      itinerary.guest_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      itinerary.booking_id?.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === "all" || itinerary.status === statusFilter;
    
    return matchesSearch && matchesStatus;
  });

  const getStatusBadge = (status: Itinerary["status"]) => {
    const statusConfig = {
      draft: { color: "grey" as const, label: "Draft" },
      confirmed: { color: "green" as const, label: "Confirmed" },
      in_progress: { color: "blue" as const, label: "In Progress" },
      completed: { color: "purple" as const, label: "Completed" },
    };
    
    const config = statusConfig[status];
    return <Badge color={config.color}>{config.label}</Badge>;
  };

  const getTypeIcon = (type: ItineraryItem["type"]) => {
    const icons = {
      activity: <MapPin className="h-4 w-4" />,
      dining: <Calendar className="h-4 w-4" />,
      transport: <Clock className="h-4 w-4" />,
      accommodation: <MapPin className="h-4 w-4" />,
      meeting: <Calendar className="h-4 w-4" />,
    };
    return icons[type];
  };

  const getTypeBadge = (type: ItineraryItem["type"]) => {
    const typeConfig = {
      activity: { color: "blue" as const, label: "Activity" },
      dining: { color: "orange" as const, label: "Dining" },
      transport: { color: "green" as const, label: "Transport" },
      accommodation: { color: "purple" as const, label: "Accommodation" },
      meeting: { color: "grey" as const, label: "Meeting" },
    };
    
    const config = typeConfig[type];
    return <Badge color={config.color}>{config.label}</Badge>;
  };

  return (
    <div className="space-y-6">
      <Toaster />
      
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <Heading level="h1" className="text-2xl font-bold">
            Itinerary Management
          </Heading>
          <Text className="text-muted-foreground">
            Manage guest itineraries and schedules
          </Text>
        </div>
        {hasPermission("concierge_management:create") && (
          <Button>
            <Plus className="h-4 w-4 mr-2" />
            New Itinerary
          </Button>
        )}
      </div>

      {/* Filters */}
      <div className="flex gap-4 flex-wrap">
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search by guest name or booking ID..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10 w-80"
          />
        </div>
        <Select value={statusFilter} onValueChange={setStatusFilter}>
          <Select.Trigger className="w-40">
            <Select.Value placeholder="Status" />
          </Select.Trigger>
          <Select.Content>
            <Select.Item value="all">All Status</Select.Item>
            <Select.Item value="draft">Draft</Select.Item>
            <Select.Item value="confirmed">Confirmed</Select.Item>
            <Select.Item value="in_progress">In Progress</Select.Item>
            <Select.Item value="completed">Completed</Select.Item>
          </Select.Content>
        </Select>
      </div>

      {/* Itineraries Table */}
      <div className="bg-card rounded-md shadow-sm border">
        <Table>
          <Table.Header>
            <Table.Row>
              <Table.HeaderCell>Guest</Table.HeaderCell>
              <Table.HeaderCell>Date</Table.HeaderCell>
              <Table.HeaderCell>Status</Table.HeaderCell>
              <Table.HeaderCell>Items</Table.HeaderCell>
              <Table.HeaderCell>Last Updated</Table.HeaderCell>
              <Table.HeaderCell>Actions</Table.HeaderCell>
            </Table.Row>
          </Table.Header>
          <Table.Body>
            {filteredItineraries.map((itinerary) => (
              <Table.Row key={itinerary.id}>
                <Table.Cell>
                  <div>
                    <div className="font-medium">{itinerary.guest_name}</div>
                    {itinerary.guest_email && (
                      <div className="text-sm text-muted-foreground">
                        {itinerary.guest_email}
                      </div>
                    )}
                    {itinerary.booking_id && (
                      <div className="text-sm text-muted-foreground">
                        Booking: {itinerary.booking_id}
                      </div>
                    )}
                  </div>
                </Table.Cell>
                <Table.Cell>
                  {new Date(itinerary.date).toLocaleDateString()}
                </Table.Cell>
                <Table.Cell>{getStatusBadge(itinerary.status)}</Table.Cell>
                <Table.Cell>
                  <div className="space-y-1">
                    {itinerary.items.slice(0, 2).map((item) => (
                      <div key={item.id} className="flex items-center gap-2 text-sm">
                        {getTypeIcon(item.type)}
                        <span>{item.title}</span>
                        <span className="text-muted-foreground">
                          {item.start_time}
                        </span>
                      </div>
                    ))}
                    {itinerary.items.length > 2 && (
                      <div className="text-sm text-muted-foreground">
                        +{itinerary.items.length - 2} more items
                      </div>
                    )}
                  </div>
                </Table.Cell>
                <Table.Cell>
                  {new Date(itinerary.updated_at).toLocaleDateString()}
                </Table.Cell>
                <Table.Cell>
                  <div className="flex gap-2">
                    <Button variant="ghost" size="sm" title="View Details">
                      <Eye className="h-4 w-4" />
                    </Button>
                    {hasPermission("concierge_management:edit") && (
                      <Button variant="ghost" size="sm" title="Edit">
                        <Edit className="h-4 w-4" />
                      </Button>
                    )}
                    {hasPermission("concierge_management:delete") && (
                      <Button variant="ghost" size="sm" title="Delete">
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    )}
                  </div>
                </Table.Cell>
              </Table.Row>
            ))}
          </Table.Body>
        </Table>
      </div>

      {/* Summary Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div className="bg-card p-4 rounded-lg border">
          <div className="text-2xl font-bold">
            {itineraries.length}
          </div>
          <div className="text-sm text-muted-foreground">Total Itineraries</div>
        </div>
        <div className="bg-card p-4 rounded-lg border">
          <div className="text-2xl font-bold">
            {itineraries.filter(i => i.status === "confirmed").length}
          </div>
          <div className="text-sm text-muted-foreground">Confirmed</div>
        </div>
        <div className="bg-card p-4 rounded-lg border">
          <div className="text-2xl font-bold">
            {itineraries.filter(i => i.status === "in_progress").length}
          </div>
          <div className="text-sm text-muted-foreground">In Progress</div>
        </div>
        <div className="bg-card p-4 rounded-lg border">
          <div className="text-2xl font-bold">
            {itineraries.reduce((acc, i) => acc + i.items.length, 0)}
          </div>
          <div className="text-sm text-muted-foreground">Total Items</div>
        </div>
      </div>
    </div>
  );
};

export default ItineraryManagement;
