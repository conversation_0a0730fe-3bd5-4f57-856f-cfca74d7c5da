import React, { useState, useEffect } from "react";
import { useSearchParams } from "react-router-dom";
import {
  Button,
  DropdownMenu,
  Input,
  Badge,
  Text,
  Checkbox,
} from "@camped-ai/ui";
import {
  Filter,
  Search,
  X,
  ChevronDown,
  Calendar,
  User,
  AlertTriangle,
  CheckCircle,
} from "lucide-react";
import { UserSelector } from "./user-selector";

export interface FilterState {
  search: string;
  status: string[];
  priority: string[];
  assigned_to: string[];
  entity_type: string[];
  due_date_gte?: string;
  due_date_lte?: string;
  created_at_gte?: string;
  created_at_lte?: string;
}

interface TaskFilterControlsProps {
  onFiltersChange: (filters: FilterState) => void;
  className?: string;
}

const statusOptions = [
  { value: "pending", label: "Pending", color: "orange" },
  { value: "in_progress", label: "In Progress", color: "blue" },
  { value: "review", label: "Review", color: "orange" },
  { value: "completed", label: "Completed", color: "green" },
  { value: "cancelled", label: "Cancelled", color: "grey" },
];

const priorityOptions = [
  { value: "low", label: "Low", color: "grey" },
  { value: "medium", label: "Medium", color: "blue" },
  { value: "high", label: "High", color: "orange" },
  { value: "urgent", label: "Urgent", color: "red" },
];

const entityTypeOptions = [
  { value: "booking", label: "Booking" },
  { value: "deal", label: "Deal" },
  { value: "guest", label: "Guest" },
  { value: "itinerary", label: "Itinerary" },
];

export const TaskFilterControls: React.FC<TaskFilterControlsProps> = ({
  onFiltersChange,
  className = "",
}) => {
  const [searchParams, setSearchParams] = useSearchParams();
  const [searchValue, setSearchValue] = useState("");
  const [filters, setFilters] = useState<FilterState>({
    search: "",
    status: [],
    priority: [],
    assigned_to: [],
    entity_type: [],
  });

  // Initialize filters from URL params
  useEffect(() => {
    const initialFilters: FilterState = {
      search: searchParams.get("q") || "",
      status: searchParams.getAll("status"),
      priority: searchParams.getAll("priority"),
      assigned_to: searchParams.getAll("assigned_to"),
      entity_type: searchParams.getAll("entity_type"),
      due_date_gte: searchParams.get("due_date_gte") || undefined,
      due_date_lte: searchParams.get("due_date_lte") || undefined,
      created_at_gte: searchParams.get("created_at_gte") || undefined,
      created_at_lte: searchParams.get("created_at_lte") || undefined,
    };
    
    setFilters(initialFilters);
    setSearchValue(initialFilters.search);
    onFiltersChange(initialFilters);
  }, [searchParams, onFiltersChange]);

  const updateFilters = (newFilters: Partial<FilterState>) => {
    const updatedFilters = { ...filters, ...newFilters };
    setFilters(updatedFilters);
    
    // Update URL params
    const newSearchParams = new URLSearchParams(searchParams);
    
    // Clear existing filter params
    ["q", "status", "priority", "assigned_to", "entity_type", "due_date_gte", "due_date_lte", "created_at_gte", "created_at_lte"].forEach(key => {
      newSearchParams.delete(key);
    });
    
    // Add new filter params
    if (updatedFilters.search) newSearchParams.set("q", updatedFilters.search);
    updatedFilters.status.forEach(s => newSearchParams.append("status", s));
    updatedFilters.priority.forEach(p => newSearchParams.append("priority", p));
    updatedFilters.assigned_to.forEach(a => newSearchParams.append("assigned_to", a));
    updatedFilters.entity_type.forEach(e => newSearchParams.append("entity_type", e));
    if (updatedFilters.due_date_gte) newSearchParams.set("due_date_gte", updatedFilters.due_date_gte);
    if (updatedFilters.due_date_lte) newSearchParams.set("due_date_lte", updatedFilters.due_date_lte);
    if (updatedFilters.created_at_gte) newSearchParams.set("created_at_gte", updatedFilters.created_at_gte);
    if (updatedFilters.created_at_lte) newSearchParams.set("created_at_lte", updatedFilters.created_at_lte);
    
    // Reset to page 1 when filters change
    newSearchParams.delete("page");
    
    setSearchParams(newSearchParams);
    onFiltersChange(updatedFilters);
  };

  const handleSearchChange = (value: string) => {
    setSearchValue(value);
    updateFilters({ search: value });
  };

  const toggleArrayFilter = (key: keyof FilterState, value: string) => {
    const currentArray = filters[key] as string[];
    const newArray = currentArray.includes(value)
      ? currentArray.filter(item => item !== value)
      : [...currentArray, value];
    
    updateFilters({ [key]: newArray });
  };

  const clearAllFilters = () => {
    const clearedFilters: FilterState = {
      search: "",
      status: [],
      priority: [],
      assigned_to: [],
      entity_type: [],
    };
    setFilters(clearedFilters);
    setSearchValue("");
    setSearchParams(new URLSearchParams());
    onFiltersChange(clearedFilters);
  };

  const getActiveFilterCount = () => {
    return (
      (filters.search ? 1 : 0) +
      filters.status.length +
      filters.priority.length +
      filters.assigned_to.length +
      filters.entity_type.length +
      (filters.due_date_gte ? 1 : 0) +
      (filters.due_date_lte ? 1 : 0) +
      (filters.created_at_gte ? 1 : 0) +
      (filters.created_at_lte ? 1 : 0)
    );
  };

  const activeFilterCount = getActiveFilterCount();

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Search and Filter Toggle Row */}
      <div className="flex items-center gap-3">
        {/* Search Input */}
        <div className="relative flex-1 max-w-md">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-ui-fg-muted" />
          <Input
            placeholder="Search tasks..."
            value={searchValue}
            onChange={(e) => handleSearchChange(e.target.value)}
            className="pl-2"
          />
        </div>

        {/* Filter Dropdowns */}
        <div className="flex items-center gap-2">
          {/* Status Filter */}
          <DropdownMenu>
            <DropdownMenu.Trigger asChild>
              <Button variant="secondary" size="small">
                <CheckCircle className="h-4 w-4 mr-2" />
                Status
                {filters.status.length > 0 && (
                  <Badge size="xsmall" className="ml-2">
                    {filters.status.length}
                  </Badge>
                )}
                <ChevronDown className="h-4 w-4 ml-1" />
              </Button>
            </DropdownMenu.Trigger>
            <DropdownMenu.Content>
              {statusOptions.map((option) => (
                <DropdownMenu.Item
                  key={option.value}
                  onSelect={(e) => e.preventDefault()}
                  className="flex items-center gap-2"
                >
                  <Checkbox
                    checked={filters.status.includes(option.value)}
                    onCheckedChange={() => toggleArrayFilter("status", option.value)}
                  />
                  <Badge color={option.color as any} size="xsmall">
                    {option.label}
                  </Badge>
                </DropdownMenu.Item>
              ))}
            </DropdownMenu.Content>
          </DropdownMenu>

          {/* Priority Filter */}
          <DropdownMenu>
            <DropdownMenu.Trigger asChild>
              <Button variant="secondary" size="small">
                <AlertTriangle className="h-4 w-4 mr-2" />
                Priority
                {filters.priority.length > 0 && (
                  <Badge size="xsmall" className="ml-2">
                    {filters.priority.length}
                  </Badge>
                )}
                <ChevronDown className="h-4 w-4 ml-1" />
              </Button>
            </DropdownMenu.Trigger>
            <DropdownMenu.Content>
              {priorityOptions.map((option) => (
                <DropdownMenu.Item
                  key={option.value}
                  onSelect={(e) => e.preventDefault()}
                  className="flex items-center gap-2"
                >
                  <Checkbox
                    checked={filters.priority.includes(option.value)}
                    onCheckedChange={() => toggleArrayFilter("priority", option.value)}
                  />
                  <Badge color={option.color as any} size="xsmall">
                    {option.label}
                  </Badge>
                </DropdownMenu.Item>
              ))}
            </DropdownMenu.Content>
          </DropdownMenu>

          {/* Entity Type Filter */}
          <DropdownMenu>
            <DropdownMenu.Trigger asChild>
              <Button variant="secondary" size="small">
                <Filter className="h-4 w-4 mr-2" />
                Type
                {filters.entity_type.length > 0 && (
                  <Badge size="xsmall" className="ml-2">
                    {filters.entity_type.length}
                  </Badge>
                )}
                <ChevronDown className="h-4 w-4 ml-1" />
              </Button>
            </DropdownMenu.Trigger>
            <DropdownMenu.Content>
              {entityTypeOptions.map((option) => (
                <DropdownMenu.Item
                  key={option.value}
                  onSelect={(e) => e.preventDefault()}
                  className="flex items-center gap-2"
                >
                  <Checkbox
                    checked={filters.entity_type.includes(option.value)}
                    onCheckedChange={() => toggleArrayFilter("entity_type", option.value)}
                  />
                  {option.label}
                </DropdownMenu.Item>
              ))}
            </DropdownMenu.Content>
          </DropdownMenu>

          {/* Clear Filters */}
          {activeFilterCount > 0 && (
            <Button
              variant="secondary"
              size="small"
              onClick={clearAllFilters}
            >
              <X className="h-4 w-4 mr-2" />
              Clear ({activeFilterCount})
            </Button>
          )}
        </div>
      </div>

      {/* Active Filters Display */}
      {activeFilterCount > 0 && (
        <div className="flex flex-wrap gap-2">
          {filters.search && (
            <Badge variant="outline" className="flex items-center gap-1">
              Search: "{filters.search}"
              <X
                className="h-3 w-3 cursor-pointer"
                onClick={() => handleSearchChange("")}
              />
            </Badge>
          )}
          {filters.status.map((status) => (
            <Badge key={status} variant="outline" className="flex items-center gap-1">
              Status: {statusOptions.find(s => s.value === status)?.label}
              <X
                className="h-3 w-3 cursor-pointer"
                onClick={() => toggleArrayFilter("status", status)}
              />
            </Badge>
          ))}
          {filters.priority.map((priority) => (
            <Badge key={priority} variant="outline" className="flex items-center gap-1">
              Priority: {priorityOptions.find(p => p.value === priority)?.label}
              <X
                className="h-3 w-3 cursor-pointer"
                onClick={() => toggleArrayFilter("priority", priority)}
              />
            </Badge>
          ))}
          {filters.entity_type.map((type) => (
            <Badge key={type} variant="outline" className="flex items-center gap-1">
              Type: {entityTypeOptions.find(e => e.value === type)?.label}
              <X
                className="h-3 w-3 cursor-pointer"
                onClick={() => toggleArrayFilter("entity_type", type)}
              />
            </Badge>
          ))}
        </div>
      )}
    </div>
  );
};
