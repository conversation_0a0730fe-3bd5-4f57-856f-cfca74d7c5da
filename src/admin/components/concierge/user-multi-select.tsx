import React, { useState } from "react";
import { Text } from "@camped-ai/ui";
import { User, Loader2 } from "lucide-react";
import { useQuery } from "@tanstack/react-query";
import { sdk } from "../../lib/sdk";
import { MultiSelect } from "../common/MultiSelect";

interface User {
  id: string;
  email: string;
  first_name?: string;
  last_name?: string;
  created_at: string;
  updated_at: string;
  metadata?: any;
}

interface UserOption {
  value: string;
  label: string;
  email?: string;
}

interface UserMultiSelectProps {
  selectedUserIds: string[];
  onUsersChange: (userIds: string[]) => void;
  placeholder?: string;
  className?: string;
  disabled?: boolean;
  includeUnassigned?: boolean;
}

// Helper function to get user display name
const getUserDisplayName = (user: User): string => {
  return user.first_name && user.last_name
    ? `${user.first_name} ${user.last_name}`
    : user.email;
};

// API function to fetch users
const fetchUsers = async (): Promise<UserOption[]> => {
  try {
    const usersResponse = await sdk.admin.user.list();
    const users = (usersResponse.users || []) as User[];

    return users.map((user: User) => ({
      value: user.id,
      label: getUserDisplayName(user),
      email: user.email,
    }));
  } catch (error) {
    console.error("Error fetching users:", error);
    return [];
  }
};

export const UserMultiSelect: React.FC<UserMultiSelectProps> = ({
  selectedUserIds,
  onUsersChange,
  placeholder = "Select assignees...",
  className = "",
  disabled = false,
  includeUnassigned = true,
}) => {
  const [searchValue, setSearchValue] = useState("");

  // Fetch users
  const { data: users = [], isLoading, error } = useQuery({
    queryKey: ["users", "multi-selector"],
    queryFn: fetchUsers,
    staleTime: 5 * 60 * 1000, // Cache for 5 minutes
    gcTime: 10 * 60 * 1000, // Keep in cache for 10 minutes
  });

  if (error) {
    return (
      <div className={`space-y-2 ${className}`}>
        <div className="flex items-center space-x-2 p-3 border border-red-200 rounded-md bg-red-50">
          <User className="h-4 w-4 text-red-500" />
          <Text className="text-sm text-red-600">
            Failed to load users. Please try again.
          </Text>
        </div>
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className={`space-y-2 ${className}`}>
        <div className="flex items-center space-x-2 p-3 border border-gray-200 rounded-md bg-gray-50">
          <Loader2 className="h-4 w-4 animate-spin text-gray-400" />
          <Text className="text-sm text-gray-500">Loading users...</Text>
        </div>
      </div>
    );
  }

  // Filter users based on search
  const filteredUsers = users.filter((user) =>
    user.label.toLowerCase().includes(searchValue.toLowerCase()) ||
    (user.email && user.email.toLowerCase().includes(searchValue.toLowerCase()))
  );

  // Prepare options
  const options = includeUnassigned 
    ? [{ value: "unassigned", label: "Unassigned" }, ...filteredUsers]
    : filteredUsers;

  return (
    <div className={`space-y-2 ${className}`}>
      <MultiSelect
        options={options}
        selectedValues={selectedUserIds}
        onChange={onUsersChange}
        placeholder={placeholder}
        disabled={disabled || isLoading}
        showSelectAll={true}
        showSelectedTags={true}
      />
      {users.length > 0 && (
        <Text className="text-xs text-gray-500">
          {users.length} user{users.length !== 1 ? 's' : ''} available
        </Text>
      )}
    </div>
  );
};
