import React from "react";
import { Select, Label, Text } from "@camped-ai/ui";
import { Building2, Loader2 } from "lucide-react";
import { useHotels } from "../../hooks/supplier-products-services/use-hotels";

interface Hotel {
  id: string;
  name: string;
  is_active: boolean;
}

interface HotelSelectorProps {
  selectedHotelId: string | null;
  onHotelChange: (hotelId: string) => void;
  label?: string;
  placeholder?: string;
  className?: string;
  disabled?: boolean;
  showActiveOnly?: boolean;
}

const HotelSelector: React.FC<HotelSelectorProps> = ({
  selectedHotelId,
  onHotelChange,
  label = "Select Hotel",
  placeholder = "Choose a hotel...",
  className = "",
  disabled = false,
  showActiveOnly = true,
}) => {
  const { data: hotelsData, isLoading, error } = useHotels({
    limit: 100,
    is_active: showActiveOnly ? true : undefined,
  });

  const hotels = hotelsData?.hotels || [];

  if (error) {
    return (
      <div className={`space-y-2 ${className}`}>
        {label && (
          <Label className="text-sm font-medium text-gray-700">{label}</Label>
        )}
        <div className="flex items-center space-x-2 p-3 border border-red-200 rounded-md bg-red-50">
          <Building2 className="h-4 w-4 text-red-500" />
          <Text className="text-sm text-red-600">
            Failed to load hotels. Please try again.
          </Text>
        </div>
      </div>
    );
  }

  return (
    <div className={`space-y-2 ${className}`}>
      {label && (
        <Label className="text-sm font-medium text-gray-700">{label}</Label>
      )}
      <Select
        value={selectedHotelId || ""}
        onValueChange={onHotelChange}
        disabled={disabled || isLoading}
      >
        <Select.Trigger className="w-full">
          <div className="flex items-center space-x-2">
            {isLoading && (
              <Loader2 className="h-4 w-4 animate-spin text-gray-400" />
            )}
            <Select.Value placeholder={isLoading ? "Loading hotels..." : placeholder} />
          </div>
        </Select.Trigger>
        <Select.Content>
          {isLoading ? (
            <div className="flex items-center justify-center p-4">
              <Loader2 className="h-4 w-4 animate-spin text-gray-400" />
              <Text className="ml-2 text-sm text-gray-500">Loading hotels...</Text>
            </div>
          ) : hotels.length === 0 ? (
            <div className="flex items-center justify-center p-4">
              <Building2 className="h-4 w-4 text-gray-400" />
              <Text className="ml-2 text-sm text-gray-500">No hotels found</Text>
            </div>
          ) : (
            hotels.map((hotel: Hotel) => (
              <Select.Item key={hotel.id} value={hotel.id}>
                <div className="flex items-center space-x-2">
                  <Building2 className="h-4 w-4 text-gray-400" />
                  <span className="truncate">{hotel.name}</span>
                  {!hotel.is_active && (
                    <span className="text-xs text-gray-400 bg-gray-100 px-1 rounded">
                      Inactive
                    </span>
                  )}
                </div>
              </Select.Item>
            ))
          )}
        </Select.Content>
      </Select>
      {hotels.length > 0 && (
        <Text className="text-xs text-gray-500">
          {hotels.length} hotel{hotels.length !== 1 ? 's' : ''} available
        </Text>
      )}
    </div>
  );
};

export default HotelSelector;
