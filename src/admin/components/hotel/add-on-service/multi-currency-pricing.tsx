import { useState, useEffect } from "react";
import {
  Input,
  Button,
  Select,
  Label,
  Text,
  Alert,
  Container,
} from "@camped-ai/ui";
import { useFormContext } from "react-hook-form";
import { Plus, X, Globe, AlertCircle } from "lucide-react";
import { useAdminCurrencies } from "../../../hooks/use-admin-currencies";
import { getCurrencySymbol } from "../../../utils/currency-utils";

interface CurrencyPrice {
  adult_price?: number;
  child_price?: number;
  package_price?: number;
  per_day_adult_price?: number;
  per_day_child_price?: number;
}

interface MultiCurrencyPricingProps {
  pricingType: "per_person" | "package" | "usage_based";
  defaultCurrency?: string;
  initialPrices?: { [currencyCode: string]: CurrencyPrice };
  onPricesChange?: (prices: { [currencyCode: string]: CurrencyPrice }) => void;
}

export default function MultiCurrencyPricing({
  pricingType,
  defaultCurrency: propDefaultCurrency,
  initialPrices = {},
  onPricesChange,
}: MultiCurrencyPricingProps) {
  const { control } = useFormContext();
  const [currencies, setCurrencies] = useState<string[]>([]);
  const [prices, setPrices] = useState<{
    [currencyCode: string]: CurrencyPrice;
  }>(initialPrices);

  // Get store currencies and default currency
  const {
    currencyOptions,
    isLoading: currenciesLoading,
    isError: currenciesError,
    defaultCurrency: storeDefaultCurrency,
  } = useAdminCurrencies();

  // Use store default currency or prop default currency
  const defaultCurrency =
    propDefaultCurrency || storeDefaultCurrency?.currency_code;

  // Initialize with default currency if no prices exist
  useEffect(() => {
    if (Object.keys(prices).length === 0 && defaultCurrency) {
      const newPrices = { [defaultCurrency]: {} };
      setPrices(newPrices);
      setCurrencies([defaultCurrency]);
      onPricesChange?.(newPrices);
    } else {
      setCurrencies(Object.keys(prices));
    }
  }, [defaultCurrency, initialPrices]);

  // Add a new currency
  const addCurrency = (currencyCode: string) => {
    if (!currencies.includes(currencyCode)) {
      const newCurrencies = [...currencies, currencyCode];
      const newPrices = { ...prices, [currencyCode]: {} };

      setCurrencies(newCurrencies);
      setPrices(newPrices);
      onPricesChange?.(newPrices);
    }
  };

  // Remove a currency
  const removeCurrency = (currencyCode: string) => {
    if (currencies.length > 1) {
      // Keep at least one currency
      const newCurrencies = currencies.filter((c) => c !== currencyCode);
      const newPrices = { ...prices };
      delete newPrices[currencyCode];

      setCurrencies(newCurrencies);
      setPrices(newPrices);
      onPricesChange?.(newPrices);
    }
  };

  // Update price for a specific currency
  const updatePrice = (
    currencyCode: string,
    priceType: keyof CurrencyPrice,
    value: number | undefined
  ) => {
    const newPrices = {
      ...prices,
      [currencyCode]: {
        ...prices[currencyCode],
        [priceType]: value,
      },
    };

    setPrices(newPrices);
    onPricesChange?.(newPrices);
  };

  // Get available currencies for dropdown (exclude already selected)
  const availableCurrencies = currencyOptions.filter(
    (option) => !currencies.includes(option.value)
  );

  // Render price inputs based on pricing type
  const renderPriceInputs = (currencyCode: string) => {
    const currencyPrices = prices[currencyCode] || {};

    switch (pricingType) {
      case "package":
        return (
          <div className="space-y-3">
            <div>
              <Label htmlFor={`package_price_${currencyCode}`}>
                Package Price ({currencyCode})
              </Label>
              <div className="relative">
                <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 text-sm">
                  {getCurrencySymbol(currencyCode)}
                </span>
                <Input
                  id={`package_price_${currencyCode}`}
                  type="number"
                  step="0.01"
                  min="0"
                  placeholder="0.00"
                  className="pl-12"
                  value={currencyPrices.package_price || ""}
                  onChange={(e) =>
                    updatePrice(
                      currencyCode,
                      "package_price",
                      e.target.value ? parseFloat(e.target.value) : undefined
                    )
                  }
                />
              </div>
            </div>
          </div>
        );

      case "usage_based":
        return (
          <div className="space-y-3">
            <div>
              <Label htmlFor={`per_day_adult_price_${currencyCode}`}>
                Per Day Adult Price ({currencyCode})
              </Label>
              <div className="relative">
                <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 text-sm">
                  {getCurrencySymbol(currencyCode)}
                </span>
                <Input
                  id={`per_day_adult_price_${currencyCode}`}
                  type="number"
                  step="0.01"
                  min="0"
                  placeholder="0.00"
                  className="pl-12"
                  value={currencyPrices.per_day_adult_price || ""}
                  onChange={(e) =>
                    updatePrice(
                      currencyCode,
                      "per_day_adult_price",
                      e.target.value ? parseFloat(e.target.value) : undefined
                    )
                  }
                />
              </div>
            </div>
            <div>
              <Label htmlFor={`per_day_child_price_${currencyCode}`}>
                Per Day Child Price ({currencyCode})
              </Label>
              <div className="relative">
                <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 text-sm">
                  {getCurrencySymbol(currencyCode)}
                </span>
                <Input
                  id={`per_day_child_price_${currencyCode}`}
                  type="number"
                  step="0.01"
                  min="0"
                  placeholder="0.00"
                  className="pl-12"
                  value={currencyPrices.per_day_child_price || ""}
                  onChange={(e) =>
                    updatePrice(
                      currencyCode,
                      "per_day_child_price",
                      e.target.value ? parseFloat(e.target.value) : undefined
                    )
                  }
                />
              </div>
            </div>
          </div>
        );

      default: // per_person
        return (
          <div className="space-y-3">
            <div>
              <Label htmlFor={`adult_price_${currencyCode}`}>
                Adult Price ({currencyCode})
              </Label>
              <div className="relative">
                <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 text-sm">
                  {getCurrencySymbol(currencyCode)}
                </span>
                <Input
                  id={`adult_price_${currencyCode}`}
                  type="number"
                  step="0.01"
                  min="0"
                  placeholder="0.00"
                  className="pl-12"
                  value={currencyPrices.adult_price || ""}
                  onChange={(e) =>
                    updatePrice(
                      currencyCode,
                      "adult_price",
                      e.target.value ? parseFloat(e.target.value) : undefined
                    )
                  }
                />
              </div>
            </div>
            <div>
              <Label htmlFor={`child_price_${currencyCode}`}>
                Child Price ({currencyCode})
              </Label>
              <div className="relative">
                <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 text-sm">
                  {getCurrencySymbol(currencyCode)}
                </span>
                <Input
                  id={`child_price_${currencyCode}`}
                  type="number"
                  step="0.01"
                  min="0"
                  placeholder="0.00"
                  className="pl-12"
                  value={currencyPrices.child_price || ""}
                  onChange={(e) =>
                    updatePrice(
                      currencyCode,
                      "child_price",
                      e.target.value ? parseFloat(e.target.value) : undefined
                    )
                  }
                />
              </div>
            </div>
          </div>
        );
    }
  };

  // Show loading state
  if (currenciesLoading) {
    return (
      <Container className="border rounded-lg p-6 bg-white shadow-sm">
        <div className="mb-4">
          <Text className="flex items-center gap-2 text-lg font-semibold">
            <Globe className="h-5 w-5" />
            Multi-Currency Pricing
          </Text>
        </div>
        <div className="flex items-center justify-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          <span className="ml-2 text-gray-600">Loading currencies...</span>
        </div>
      </Container>
    );
  }

  // Show error state
  if (currenciesError) {
    return (
      <Container className="border rounded-lg p-6 bg-white shadow-sm">
        <div className="mb-4">
          <Text className="flex items-center gap-2 text-lg font-semibold">
            <Globe className="h-5 w-5" />
            Multi-Currency Pricing
          </Text>
        </div>
        <Alert variant="error" className="flex items-center gap-2">
          <AlertCircle className="h-4 w-4" />
          <Text>
            Failed to load store currencies. Please check your store
            configuration or contact support.
          </Text>
        </Alert>
      </Container>
    );
  }

  // Show warning if no currencies configured
  if (currencyOptions.length === 0) {
    return (
      <Container className="border rounded-lg p-6 bg-white shadow-sm">
        <div className="mb-4">
          <Text className="flex items-center gap-2 text-lg font-semibold">
            <Globe className="h-5 w-5" />
            Multi-Currency Pricing
          </Text>
        </div>
        <Alert variant="warning" className="flex items-center gap-2">
          <AlertCircle className="h-4 w-4" />
          <Text>
            No currencies are configured in your store. Please configure
            currencies in the admin panel before setting up multi-currency
            pricing.
          </Text>
        </Alert>
      </Container>
    );
  }

  // If there's only one currency, show simplified single currency pricing
  if (currencyOptions.length === 1) {
    const singleCurrency = currencyOptions[0].value;

    // Ensure the single currency is added to the currencies list
    if (!currencies.includes(singleCurrency)) {
      setCurrencies([singleCurrency]);
      if (!prices[singleCurrency]) {
        setPrices((prev) => ({ ...prev, [singleCurrency]: {} }));
      }
    }

    return (
      <Container className="border rounded-lg p-6 bg-white shadow-sm">
        <div className="mb-4">
          <Text className="flex items-center gap-2 text-lg font-semibold">
            <Globe className="h-5 w-5" />
            Pricing ({currencyOptions[0].label})
          </Text>
          <Text className="text-sm text-gray-600 mt-1">
            Set prices for your add-on service.
          </Text>
        </div>
        <div className="space-y-4">
          {/* Single Currency Pricing Card */}
          <div className="border border-l-4 border-l-blue-500 rounded-lg p-4 bg-gray-50">
            <div className="pb-3">
              <Text className="text-lg flex items-center gap-2 font-semibold">
                <span className="font-mono bg-gray-100 px-2 py-1 rounded text-sm">
                  {singleCurrency}
                </span>
                <span className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded">
                  Default
                </span>
              </Text>
            </div>
            <div>{renderPriceInputs(singleCurrency)}</div>
          </div>

          {/* Hidden inputs to store the data in the form */}
          <input
            type="hidden"
            {...control.register("prices")}
            value={JSON.stringify(prices)}
          />
          <input
            type="hidden"
            {...control.register("default_currency")}
            value={singleCurrency}
          />
        </div>
      </Container>
    );
  }

  return (
    <Container className="border rounded-lg p-6 bg-white shadow-sm">
      <div className="mb-4">
        <Text className="flex items-center gap-2 text-lg font-semibold">
          <Globe className="h-5 w-5" />
          Multi-Currency Pricing
        </Text>
        <Text className="text-sm text-gray-600 mt-1">
          Set prices in multiple currencies. At least one currency is required.
          {currencyOptions.length > 0 && (
            <span className="block mt-1 text-xs text-blue-600">
              {currencyOptions.length} currencies available in your store
              configuration.
            </span>
          )}
        </Text>
      </div>
      <div className="space-y-4">
        {/* Currency Tabs/Cards */}
        {currencies.map((currencyCode) => (
          <div
            key={currencyCode}
            className="border border-l-4 border-l-blue-500 rounded-lg p-4 bg-gray-50"
          >
            <div className="pb-3">
              <div className="flex items-center justify-between">
                <Text className="text-lg flex items-center gap-2 font-semibold">
                  <span className="font-mono bg-gray-100 px-2 py-1 rounded text-sm">
                    {currencyCode}
                  </span>
                  {currencyCode === defaultCurrency && (
                    <span className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded">
                      Default
                    </span>
                  )}
                </Text>
                {currencies.length > 1 && (
                  <Button
                    type="button"
                    variant="secondary"
                    size="small"
                    onClick={() => removeCurrency(currencyCode)}
                    className="text-red-600 hover:text-red-800"
                  >
                    <X className="h-4 w-4" />
                  </Button>
                )}
              </div>
            </div>
            <div>{renderPriceInputs(currencyCode)}</div>
          </div>
        ))}

        {/* Add Currency Button */}
        {availableCurrencies.length > 0 && (
          <div className="flex items-center gap-2">
            <Select
              onValueChange={(value) => {
                if (value) {
                  addCurrency(value);
                }
              }}
            >
              <Select.Trigger className="w-48">
                <Select.Value placeholder="Add currency..." />
              </Select.Trigger>
              <Select.Content>
                {availableCurrencies.map((currency) => (
                  <Select.Item key={currency.value} value={currency.value}>
                    {currency.label}
                  </Select.Item>
                ))}
              </Select.Content>
            </Select>
            <Button
              type="button"
              variant="secondary"
              size="small"
              className="flex items-center gap-2"
            >
              <Plus className="h-4 w-4" />
              Add Currency
            </Button>
          </div>
        )}

        {/* Hidden inputs to store the data in the form */}
        <input
          type="hidden"
          {...control.register("prices")}
          value={JSON.stringify(prices)}
        />
        <input
          type="hidden"
          {...control.register("default_currency")}
          value={currencies[0] || defaultCurrency}
        />
      </div>
    </Container>
  );
}
