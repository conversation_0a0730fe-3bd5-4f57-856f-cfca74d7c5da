import React from "react";
import { MultiSelect } from "../../common/MultiSelect";
import { RoomInventoryStatus } from "../types/booking";
import { getStatusDisplayName } from "./statusUtils";

interface RoomStatusFilterProps {
  selectedStatuses: RoomInventoryStatus[];
  onStatusesChange: (statuses: RoomInventoryStatus[]) => void;
}

const RoomStatusFilter: React.FC<RoomStatusFilterProps> = ({
  selectedStatuses,
  onStatusesChange,
}) => {
  // Define the main room statuses as requested
  const mainStatuses: RoomInventoryStatus[] = [
    RoomInventoryStatus.AVAILABLE,
    RoomInventoryStatus.MAINTENANCE,
    RoomInventoryStatus.RESERVED,
    RoomInventoryStatus.UNAVAILABLE,
    RoomInventoryStatus.ON_DEMAND, // This maps to "on_request" in display
    RoomInventoryStatus.ON_HOLD,
    RoomInventoryStatus.BOOKED,
  ];

  const options = mainStatuses.map((status) => ({
    value: status,
    label: getStatusDisplayName(status),
  }));

  const handleChange = (values: string[]) => {
    // Convert string values back to RoomInventoryStatus enum values
    const statusValues = values as RoomInventoryStatus[];
    onStatusesChange(statusValues);
  };

  return (
    <div className="flex flex-col">
      <label className="text-xs text-muted-foreground mb-1">Room Status</label>
      <MultiSelect
        options={options}
        selectedValues={selectedStatuses}
        onChange={handleChange}
        placeholder="All Statuses"
        className="min-w-[200px]"
        showSelectAll={true}
        showSelectedTags={false}
      />
    </div>
  );
};

export default RoomStatusFilter;
