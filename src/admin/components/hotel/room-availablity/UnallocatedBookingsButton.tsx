import React from "react";
import { But<PERSON> } from "@camped-ai/ui";
import { Calendar } from "lucide-react";

interface UnallocatedBookingsButtonProps {
  count: number;
  onClick: () => void;
}

const UnallocatedBookingsButton: React.FC<UnallocatedBookingsButtonProps> = ({
  count,
  onClick,
}) => {
  return (
    <div className="fixed bottom-6 right-6 z-50">
      <Button
        onClick={onClick}
        className="rounded-full h-14 w-14 shadow-lg hover:shadow-xl transition-all duration-200"
        size="large"
      >
        <div className="flex flex-col items-center">
          <Calendar className="h-5 w-5" />
          {count > 0 && (
            <span className="text-xs font-bold">
              {count}
            </span>
          )}
        </div>
      </Button>
    </div>
  );
};

export default UnallocatedBookingsButton;
