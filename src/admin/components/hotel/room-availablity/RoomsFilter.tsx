import React from "react";
import { MultiSelect } from "../../common/MultiSelect";
import { Room } from "../types";

interface RoomConfig {
  id: string;
  name: string;
}

interface RoomsFilterProps {
  selectedRooms: string[];
  onRoomsChange: (rooms: string[]) => void;
  rooms: Room[];
  selectedRoomTypes?: string[]; // Filter rooms by selected room types
  roomTypeConfigs?: RoomConfig[]; // For mapping room type IDs to names
}

const RoomsFilter: React.FC<RoomsFilterProps> = ({
  selectedRooms,
  onRoomsChange,
  rooms,
  selectedRoomTypes = [],
  roomTypeConfigs = [],
}) => {
  // Filter rooms based on selected room types (if any)
  const filteredRooms = selectedRoomTypes.length > 0
    ? rooms.filter((room) => selectedRoomTypes.includes(room.room_config_id))
    : rooms;

  // Create options for the multi-select dropdown
  const options = filteredRooms.map((room) => {
    // Create a display label with room number and room type
    const roomNumber = room.metadata?.room_number || room.room_number || room.title;
    const roomType = room.config_name;
    const floor = room.metadata?.floor ? ` (Floor ${room.metadata.floor})` : '';
    
    return {
      value: room.id,
      label: `${roomNumber} - ${roomType}${floor}`,
    };
  });

  // Sort options by room number/title for better UX
  options.sort((a, b) => {
    const aRoom = filteredRooms.find(r => r.id === a.value);
    const bRoom = filteredRooms.find(r => r.id === b.value);
    
    const aNumber = aRoom?.metadata?.room_number || aRoom?.room_number || aRoom?.title || '';
    const bNumber = bRoom?.metadata?.room_number || bRoom?.room_number || bRoom?.title || '';
    
    // Try to sort numerically if possible, otherwise alphabetically
    const aNum = parseInt(aNumber);
    const bNum = parseInt(bNumber);
    
    if (!isNaN(aNum) && !isNaN(bNum)) {
      return aNum - bNum;
    }
    
    return aNumber.localeCompare(bNumber);
  });

  const handleChange = (values: string[]) => {
    onRoomsChange(values);
  };

  // Create placeholder text based on filtering state
  const getPlaceholder = () => {
    if (selectedRoomTypes.length > 0) {
      const roomTypeNames = selectedRoomTypes.map(id => {
        const config = roomTypeConfigs.find(c => c.id === id);
        return config?.name || id;
      }).join(', ');
      return `All Rooms (${roomTypeNames})`;
    }
    return "All Rooms";
  };

  return (
    <div className="flex flex-col">
      <label className="text-xs text-muted-foreground mb-1">
        Rooms {filteredRooms.length !== rooms.length && `(${filteredRooms.length} of ${rooms.length})`}
      </label>
      <MultiSelect
        options={options}
        selectedValues={selectedRooms}
        onChange={handleChange}
        placeholder={getPlaceholder()}
        className="min-w-[280px]"
        showSelectAll={true}
        showSelectedTags={false}
        disabled={filteredRooms.length === 0}
      />
      {filteredRooms.length === 0 && selectedRoomTypes.length > 0 && (
        <div className="text-xs text-muted-foreground mt-1">
          No rooms available for selected room types
        </div>
      )}
    </div>
  );
};

export default RoomsFilter;
