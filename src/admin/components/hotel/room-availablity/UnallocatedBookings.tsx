import React from "react";
import { But<PERSON> } from "@camped-ai/ui";
import { Drawer } from "@camped-ai/ui";
import {
  Calendar,
  Clock,
  MapPin,
  User,
  ExternalLink,
  Bed,
  Users,
} from "lucide-react";

interface UnallocatedBookingsProps {
  unallocatedBookings: any[];
  isLoading: boolean;
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  onAllocateRoom: (booking: any) => void;
}

const UnallocatedBookings: React.FC<UnallocatedBookingsProps> = ({
  unallocatedBookings,
  isLoading,
  isOpen,
  onOpenChange,
  onAllocateRoom,
}) => {
  return (
    <Drawer open={isOpen} onOpenChange={onOpenChange}>
      <Drawer.Content>
        <Drawer.Header>
          <Drawer.Title>Unallocated Bookings</Drawer.Title>
          <Drawer.Description>
            Bookings that haven't been assigned to specific rooms yet
          </Drawer.Description>
        </Drawer.Header>
        <Drawer.Body className="p-6">
          {isLoading ? (
            <div className="flex flex-col items-center justify-center py-12">
              <div className="animate-spin rounded-full h-10 w-10 border-t-2 border-b-2 border-primary mb-4"></div>
              <span className="text-muted-foreground font-medium">
                Loading unallocated bookings...
              </span>
            </div>
          ) : unallocatedBookings.length === 0 ? (
            <div className="flex flex-col items-center justify-center py-12 text-center">
              <div className="bg-accent/20 rounded-full p-4 mb-4">
                <Calendar className="w-12 h-12 text-muted-foreground" />
              </div>
              <h3 className="text-xl font-semibold text-foreground mb-2">
                All bookings allocated
              </h3>
              <p className="text-muted-foreground max-w-sm">
                Great! All bookings have been successfully assigned to specific
                rooms.
              </p>
            </div>
          ) : (
            <div className="space-y-4">
              {unallocatedBookings.map((booking, index) => {
                // Extract booking ID - for unallocated bookings, the order ID is in the 'id' field
                // Also try to extract from notes as fallback
                let bookingId = booking.id || null;
                if (!bookingId && booking.notes) {
                  const match = booking.notes.match(/\b(order_[a-z0-9]+)/i);
                  bookingId = match ? match[1] : null;
                }

                // Extract room config ID from notes
                let roomConfigId = null;
                if (booking.notes) {
                  const match = booking.notes.match(/room_config_id:([^,\s]+)/);
                  roomConfigId = match ? match[1] : null;
                }

                // Extract room type from notes
                let roomType = null;
                if (booking.notes) {
                  const match = booking.notes.match(/room_type:([^,\s]+)/);
                  roomType = match ? match[1] : null;
                }

                // Format dates for better display
                const checkInDate =
                  booking.check_in_date || booking.metadata?.check_in_date;
                const checkOutDate =
                  booking.check_out_date || booking.metadata?.check_out_date;
                const formatDate = (dateStr: string) => {
                  if (!dateStr) return "N/A";
                  return new Date(dateStr).toLocaleDateString("en-GB", {
                    day: "2-digit",
                    month: "2-digit",
                    year: "numeric",
                  });
                };

                // Calculate nights
                const calculateNights = () => {
                  if (!checkInDate || !checkOutDate) return null;
                  const start = new Date(checkInDate);
                  const end = new Date(checkOutDate);
                  const diffTime = Math.abs(end.getTime() - start.getTime());
                  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
                  return diffDays;
                };

                const nights = calculateNights();
                const guestName =
                  booking.variant_title ||
                  booking.metadata?.guest_name ||
                  "Guest";

                return (
                  <div
                    key={`${booking.id}-${index}`}
                    className="unallocated-booking-card border border-border rounded-xl p-5 bg-card hover:bg-accent/30 transition-all duration-200 shadow-sm hover:shadow-md"
                  >
                    {/* Header with guest name and status */}
                    <div className="flex justify-between items-start mb-4">
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-2">
                          <User className="w-5 h-5 text-primary" />
                          <h4 className="font-semibold text-lg text-foreground">
                            {guestName}
                          </h4>
                        </div>
                        {bookingId && (
                          <div className="flex items-center gap-2">
                            <span className="text-sm text-muted-foreground">
                              Order:
                            </span>
                            <a
                              href={`/app/hotel-management/bookings/${bookingId}`}
                              target="_blank"
                              rel="noopener noreferrer"
                              className="inline-flex items-center gap-1 text-sm text-primary hover:text-primary/80 hover:underline font-medium"
                            >
                              {bookingId}
                              <ExternalLink className="w-3 h-3" />
                            </a>
                          </div>
                        )}
                      </div>

                      {/* Status badge */}
                      <div className="flex items-center gap-1 px-3 py-1 bg-orange-100 text-orange-800 rounded-full text-xs font-medium">
                        <Clock className="w-3 h-3" />
                        <span>Unallocated</span>
                      </div>
                    </div>

                    {/* Date information */}
                    <div className="bg-accent/20 rounded-lg p-3 mb-4">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <Calendar className="w-4 h-4 text-muted-foreground" />
                          <span className="text-sm font-medium text-foreground">
                            {formatDate(checkInDate)} -{" "}
                            {formatDate(checkOutDate)}
                          </span>
                        </div>
                        {nights && (
                          <div className="flex items-center gap-1 text-sm text-muted-foreground">
                            <Bed className="w-4 h-4" />
                            <span>
                              {nights} night{nights !== 1 ? "s" : ""}
                            </span>
                          </div>
                        )}
                      </div>
                    </div>

                    {/* Room information */}
                    {(roomConfigId || roomType) && (
                      <div className="border-t border-border pt-3 mb-4">
                        <div className="flex items-center gap-2 mb-2">
                          <Bed className="w-4 h-4 text-muted-foreground" />
                          <span className="text-sm font-medium text-foreground">
                            Room Requirements
                          </span>
                        </div>
                        <div className="grid grid-cols-1 gap-2">
                          {roomConfigId && (
                            <div className="flex items-center gap-2 text-sm">
                              <MapPin className="w-3 h-3 text-muted-foreground" />
                              <span className="text-muted-foreground">
                                Config:
                              </span>
                              <span className="font-medium text-foreground">
                                {roomConfigId}
                              </span>
                            </div>
                          )}
                          {roomType && (
                            <div className="flex items-center gap-2 text-sm">
                              <Users className="w-3 h-3 text-muted-foreground" />
                              <span className="text-muted-foreground">
                                Type:
                              </span>
                              <span className="font-medium text-foreground">
                                {roomType}
                              </span>
                            </div>
                          )}
                        </div>
                      </div>
                    )}

                    {/* Action button */}
                    <div className="flex justify-end pt-2">
                      <Button
                        size="small"
                        variant="primary"
                        onClick={() => onAllocateRoom(booking)}
                        className="allocate-button px-6 py-2 font-medium"
                      >
                        <Bed className="w-4 h-4 mr-2" />
                        Assign Room
                      </Button>
                    </div>
                  </div>
                );
              })}
            </div>
          )}
        </Drawer.Body>
        <Drawer.Footer className="flex justify-end gap-2 p-6 border-t">
          <Button variant="secondary" onClick={() => onOpenChange(false)}>
            Close
          </Button>
        </Drawer.Footer>
      </Drawer.Content>
    </Drawer>
  );
};

export default UnallocatedBookings;
