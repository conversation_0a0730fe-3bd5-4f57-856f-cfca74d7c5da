import React from "react";

// Test helper component to verify booking order_id logic
export const BookingTestHelper: React.FC = () => {
  // Test booking objects
  const bookingWithOrderId = {
    id: "variant_01JWDTHMJS68Q4GQGKKZBJRTQK-2025-07-18-2025-07-19-booked-3",
    room_id: "variant_01JWDTHMJS68Q4GQGKKZBJRTQK",
    guestName: "Guest JASH",
    checkIn: "2025-07-18T00:00:00.000Z",
    checkOut: "2025-07-19T00:00:00.000Z",
    status: "booked",
    notes: "Allocated to order order_01K0EG22W4DKB9KRFZ2FVEJASH",
    order_id: "order_01K0EG22W4DKB9KRFZ2FVEJASH",
    confirmationNumber: "2FVEJASH"
  };

  const bookingWithoutOrderId = {
    id: "variant_01JWDTHMJS68Q4GQGKKZBJRTQK-2025-07-18-2025-07-19-booked-4",
    room_id: "variant_01JWDTHMJS68Q4GQGKKZBJRTQK",
    guestName: "Guest TEST",
    checkIn: "2025-07-18T00:00:00.000Z",
    checkOut: "2025-07-19T00:00:00.000Z",
    status: "booked",
    notes: "Test booking without order_id",
    // No order_id field
    confirmationNumber: "TESTCONF"
  };

  const testBookingLogic = (booking: any, label: string) => {
    console.log(`\n=== ${label} ===`);
    console.log("Booking object:", booking);
    console.log("Has order_id:", !!booking.order_id);
    console.log("order_id value:", booking.order_id);
    console.log("Should show View Booking Details button:", !!booking.order_id);
    console.log("=================\n");
  };

  React.useEffect(() => {
    testBookingLogic(bookingWithOrderId, "Booking WITH order_id");
    testBookingLogic(bookingWithoutOrderId, "Booking WITHOUT order_id");
  }, []);

  return (
    <div className="p-4 bg-card border rounded-lg">
      <h3 className="text-lg font-semibold mb-4">Booking Test Helper</h3>
      <p className="text-sm text-muted-foreground mb-4">
        Check the browser console to see the test results for booking logic.
      </p>
      
      <div className="space-y-4">
        <div className="p-3 bg-green-50 border border-green-200 rounded">
          <h4 className="font-medium text-green-800">Booking WITH order_id:</h4>
          <p className="text-sm text-green-700">
            order_id: {bookingWithOrderId.order_id}
          </p>
          <p className="text-sm text-green-700">
            Should show "View Booking Details": ✅ YES
          </p>
        </div>
        
        <div className="p-3 bg-red-50 border border-red-200 rounded">
          <h4 className="font-medium text-red-800">Booking WITHOUT order_id:</h4>
          <p className="text-sm text-red-700">
            order_id: {bookingWithoutOrderId.order_id || "undefined"}
          </p>
          <p className="text-sm text-red-700">
            Should show "View Booking Details": ❌ NO (disabled)
          </p>
        </div>
      </div>
    </div>
  );
};

export default BookingTestHelper;
