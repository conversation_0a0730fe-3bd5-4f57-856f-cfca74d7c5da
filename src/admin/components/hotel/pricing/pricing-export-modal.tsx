import React, { useState, useEffect } from "react";
import { FocusModal, Heading, Button, Label, toast } from "@camped-ai/ui";
import { Download, X } from "lucide-react";
import { MultiSelect } from "../../common/MultiSelect";

// Types for the modal props (matching comprehensive pricing table types)
interface RoomConfig {
  id: string;
  title: string;
  handle?: string;
  description?: string;
}

interface MealPlan {
  id: string;
  name: string;
  is_default?: boolean;
  metadata?: Record<string, any>;
}

interface SeasonalPeriod {
  id: string;
  name: string;
  start_date: string;
  end_date: string;
}

interface OccupancyConfig {
  id: string;
  name: string;
  is_default?: boolean;
}

interface PricingExportModalProps {
  open: boolean;
  onClose: () => void;
  hotelId: string;
  currentCurrency: string;
  roomConfigs: RoomConfig[];
  occupancyConfigs: OccupancyConfig[];
  mealPlans: MealPlan[];
  seasonalPeriods?: SeasonalPeriod[];
}

// Available columns for export - ordered to match import template exactly
// This ensures exported files can be directly imported back
const AVAILABLE_COLUMNS = {
  // Required columns for import (in exact template order)
  room_config_name: "Room Config Name",
  occupancy_name: "Occupancy Name",
  meal_plan_name: "Meal Plan Name",
  currency_code: "Currency Code",

  // Default cost/margin columns (in exact template order)
  default_gross_cost: "Default Gross Cost",
  default_fixed_margin: "Default Fixed Margin",
  default_margin_percentage: "Default Margin %",
  default_total: "Default Total",

  // Weekday price columns (in exact template order)
  monday_price: "Monday Price",
  tuesday_price: "Tuesday Price",
  wednesday_price: "Wednesday Price",
  thursday_price: "Thursday Price",
  friday_price: "Friday Price",
  saturday_price: "Saturday Price",
  sunday_price: "Sunday Price",

  // Weekday cost columns (in exact template order)
  monday_gross_cost: "Monday Gross Cost",
  tuesday_gross_cost: "Tuesday Gross Cost",
  wednesday_gross_cost: "Wednesday Gross Cost",
  thursday_gross_cost: "Thursday Gross Cost",
  friday_gross_cost: "Friday Gross Cost",
  saturday_gross_cost: "Saturday Gross Cost",
  sunday_gross_cost: "Sunday Gross Cost",

  // Weekday fixed margin columns (in exact template order)
  monday_fixed_margin: "Monday Fixed Margin",
  tuesday_fixed_margin: "Tuesday Fixed Margin",
  wednesday_fixed_margin: "Wednesday Fixed Margin",
  thursday_fixed_margin: "Thursday Fixed Margin",
  friday_fixed_margin: "Friday Fixed Margin",
  saturday_fixed_margin: "Saturday Fixed Margin",
  sunday_fixed_margin: "Sunday Fixed Margin",

  // Weekday margin percentage columns (in exact template order)
  monday_margin_percentage: "Monday Margin %",
  tuesday_margin_percentage: "Tuesday Margin %",
  wednesday_margin_percentage: "Wednesday Margin %",
  thursday_margin_percentage: "Thursday Margin %",
  friday_margin_percentage: "Friday Margin %",
  saturday_margin_percentage: "Saturday Margin %",
  sunday_margin_percentage: "Sunday Margin %",

  // Additional export-only columns (user-friendly, no IDs)
  hotel_name: "Hotel Name",
  max_occupancy: "Max Occupancy",
  max_cots: "Max Cots",
  occupancy_adults: "Adults",
  occupancy_children: "Children",
  occupancy_infants: "Infants",
  meal_plan_type: "Meal Plan Type",
  pricing_type: "Pricing Type",
  seasonal_period: "Seasonal Period",
  seasonal_start_date: "Season Start Date",
  seasonal_end_date: "Season End Date",
};

interface ExportOptions {
  format: "csv" | "xlsx";
  selectedSeasonIds: string[];
  selectedRoomConfigIds: string[];
  selectedOccupancyConfigIds: string[];
  selectedMealPlanIds: string[];
  selectedColumns: Record<string, boolean>;
}

const PricingExportModal: React.FC<PricingExportModalProps> = ({
  open,
  onClose,
  hotelId,
  currentCurrency,
  roomConfigs,
  occupancyConfigs,
  mealPlans,
  seasonalPeriods = [],
}) => {
  // Export options state
  const [exportOptions, setExportOptions] = useState<ExportOptions>({
    format: "xlsx",
    selectedSeasonIds: [], // Nothing selected by default
    selectedRoomConfigIds: [], // Nothing selected by default
    selectedOccupancyConfigIds: [], // Nothing selected by default
    selectedMealPlanIds: [], // Nothing selected by default
    selectedColumns: {
      // Core identification columns - selected by default (essential for understanding data)
      seasonal_period: true, // Important to know which season/period
      room_config_name: true, // Room type
      occupancy_name: true, // Occupancy type
      meal_plan_name: true, // Meal plan
      currency_code: true, // Currency

      // Weekday Prices - selected by default (core pricing data)
      monday_price: true,
      tuesday_price: true,
      wednesday_price: true,
      thursday_price: true,
      friday_price: true,
      saturday_price: true,
      sunday_price: true,

      // Default cost/margin columns - not selected by default (optional)
      default_gross_cost: false,
      default_fixed_margin: false,
      default_margin_percentage: false,
      default_total: false,

      // Weekday cost columns - not selected by default (optional)
      monday_gross_cost: false,
      tuesday_gross_cost: false,
      wednesday_gross_cost: false,
      thursday_gross_cost: false,
      friday_gross_cost: false,
      saturday_gross_cost: false,
      sunday_gross_cost: false,

      // Weekday fixed margin columns - not selected by default (optional)
      monday_fixed_margin: false,
      tuesday_fixed_margin: false,
      wednesday_fixed_margin: false,
      thursday_fixed_margin: false,
      friday_fixed_margin: false,
      saturday_fixed_margin: false,
      sunday_fixed_margin: false,

      // Weekday margin percentage columns - not selected by default (optional)
      monday_margin_percentage: false,
      tuesday_margin_percentage: false,
      wednesday_margin_percentage: false,
      thursday_margin_percentage: false,
      friday_margin_percentage: false,
      saturday_margin_percentage: false,
      sunday_margin_percentage: false,

      // Additional export-only fields - not selected by default
      hotel_name: false,
      max_occupancy: false,
      max_cots: false,
      occupancy_adults: false,
      occupancy_children: false,
      occupancy_infants: false,
      meal_plan_type: false,
      pricing_type: false,
      seasonal_start_date: false,
      seasonal_end_date: false,
    },
  });

  const [isExporting, setIsExporting] = useState(false);

  // Update default selections when props change
  useEffect(() => {
    setExportOptions((prev) => ({
      ...prev,
      selectedSeasonIds: [],
      selectedRoomConfigIds: [],
      selectedOccupancyConfigIds: [],
      selectedMealPlanIds: [],
    }));
  }, [roomConfigs, occupancyConfigs, mealPlans, seasonalPeriods]);

  // Handle export
  const handleExport = async () => {
    if (!hotelId) {
      toast.error("Hotel ID is required for export");
      return;
    }

    // Validation is now handled by the column selection check below

    setIsExporting(true);

    try {
      // Validate that at least one column is selected
      const selectedColumnKeys = Object.entries(exportOptions.selectedColumns)
        .filter(([_, selected]) => selected)
        .map(([column]) => column);

      if (selectedColumnKeys.length === 0) {
        toast.error("Please select at least one column to export");
        return;
      }

      // Determine if seasonal pricing should be included based on selected columns
      const includeSeasonalPricing = selectedColumnKeys.some(
        (col) => col.includes("seasonal") || col === "pricing_type"
      );

      // Determine if cost/margin data should be included based on selected columns
      const includeCostMarginData = selectedColumnKeys.some(
        (col) => col.includes("cost") || col.includes("margin")
      );

      // Build query parameters
      const queryParams = new URLSearchParams({
        format: exportOptions.format,
        currency: currentCurrency,
        include_seasonal: includeSeasonalPricing.toString(),
        include_cost_margin: includeCostMarginData.toString(),
      });

      // Add selected columns to the query parameters
      queryParams.append("columns", selectedColumnKeys.join(","));

      // Add room config IDs if any are selected
      if (exportOptions.selectedRoomConfigIds.length > 0) {
        queryParams.append(
          "room_config_ids",
          exportOptions.selectedRoomConfigIds.join(",")
        );
      }

      // Add occupancy config IDs if any are selected
      if (exportOptions.selectedOccupancyConfigIds.length > 0) {
        queryParams.append(
          "occupancy_config_ids",
          exportOptions.selectedOccupancyConfigIds.join(",")
        );
      }

      // Add meal plan IDs if any are selected
      if (exportOptions.selectedMealPlanIds.length > 0) {
        queryParams.append(
          "meal_plan_ids",
          exportOptions.selectedMealPlanIds.join(",")
        );
      }

      // Add seasonal period IDs if any are selected
      if (exportOptions.selectedSeasonIds.length > 0) {
        queryParams.append(
          "season_ids",
          exportOptions.selectedSeasonIds.join(",")
        );
      }

      // Make API call to export endpoint
      const response = await fetch(
        `/admin/hotel-management/hotels/${hotelId}/pricing/export?${queryParams.toString()}`,
        {
          method: "GET",
          headers: {
            "Content-Type": "application/json",
          },
        }
      );

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(
          errorData.message || `HTTP ${response.status}: ${response.statusText}`
        );
      }

      // Get filename from response headers
      const contentDisposition = response.headers.get("Content-Disposition");
      const filename = contentDisposition
        ? contentDisposition.split("filename=")[1]?.replace(/"/g, "")
        : `hotel_pricing_export_${currentCurrency}_${
            new Date().toISOString().split("T")[0]
          }.${exportOptions.format}`;

      // Create blob and trigger download
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement("a");
      link.href = url;
      link.download = filename;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);

      toast.success(`Pricing data exported successfully as ${filename}`);
      onClose();
    } catch (error) {
      console.error("Export error:", error);
      toast.error(
        error instanceof Error
          ? error.message
          : "An unexpected error occurred during export"
      );
    } finally {
      setIsExporting(false);
    }
  };

  // Helper functions
  const handleSelectAll = () => {
    const allSelected = Object.values(exportOptions.selectedColumns).every(
      (v) => v
    );
    const newColumns = { ...exportOptions.selectedColumns };
    Object.keys(newColumns).forEach((key) => {
      newColumns[key] = !allSelected;
    });
    setExportOptions((prev) => ({
      ...prev,
      selectedColumns: newColumns,
    }));
  };

  const handleFieldChange = (field: string, checked: boolean) => {
    setExportOptions((prev) => ({
      ...prev,
      selectedColumns: {
        ...prev.selectedColumns,
        [field]: checked,
      },
    }));
  };

  const selectedFieldsCount = Object.values(
    exportOptions.selectedColumns
  ).filter(Boolean).length;
  const totalFieldsCount = Object.keys(exportOptions.selectedColumns).length;

  return (
    <FocusModal open={open} onOpenChange={onClose}>
      <FocusModal.Content className="">
        <FocusModal.Header>
          <div className="flex items-center justify-between w-full">
            <div className="flex items-center gap-3">
              <Heading level="h2" className="text-xl font-semibold">
                Export Pricing Data
              </Heading>
            </div>

            {/* Step indicators */}
            <div className="flex items-center gap-4">
              <div className="flex items-center gap-2 px-3 py-1 rounded-full text-sm bg-blue-100 text-blue-800 border border-blue-200">
                <span className="w-5 h-5 rounded-full flex items-center justify-center text-xs font-medium bg-blue-600 text-white">
                  1
                </span>
                Configure
              </div>
              <div className="flex items-center gap-2 px-3 py-1 rounded-full text-sm bg-gray-100 text-gray-600">
                <span className="w-5 h-5 rounded-full flex items-center justify-center text-xs font-medium bg-gray-400 text-white">
                  2
                </span>
                Export
              </div>
            </div>
          </div>
        </FocusModal.Header>

        <FocusModal.Body className="flex flex-col flex-grow overflow-hidden">
          <div className="flex-grow overflow-y-auto p-6 pb-0">
            <div className="flex flex-col gap-6">
              {/* Fields Selection Section - Full Width Top Row */}
              <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 shadow-sm">
                <div className="flex-shrink-0 p-4 border-b border-gray-200 dark:border-gray-700">
                  <div className="flex justify-between items-center">
                    <Heading
                      level="h3"
                      className="text-lg font-semibold text-gray-900 dark:text-gray-100"
                    >
                      Select Fields to Export
                    </Heading>
                    <Button
                      variant="secondary"
                      size="small"
                      onClick={handleSelectAll}
                      className="bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600 border-gray-300 dark:border-gray-600"
                    >
                      {Object.values(exportOptions.selectedColumns).every(
                        (v) => v
                      )
                        ? "Deselect All"
                        : "Select All"}
                    </Button>
                  </div>
                </div>

                <div className="p-4">
                  <div className="grid grid-cols-6 gap-3">
                    {Object.entries(AVAILABLE_COLUMNS).map(
                      ([columnKey, columnLabel]) => (
                        <div
                          key={columnKey}
                          className="flex items-center gap-2 p-3 rounded-md hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors border border-gray-100 dark:border-gray-600 h-12 min-w-0"
                        >
                          <input
                            type="checkbox"
                            id={`field-${columnKey}`}
                            checked={
                              exportOptions.selectedColumns[columnKey] || false
                            }
                            onChange={(e) =>
                              handleFieldChange(columnKey, e.target.checked)
                            }
                            className="h-4 w-4 flex-shrink-0 text-blue-600 dark:text-blue-400 focus:ring-blue-500 dark:focus:ring-blue-400 rounded border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700"
                          />
                          <Label
                            htmlFor={`field-${columnKey}`}
                            className="cursor-pointer text-gray-700 dark:text-gray-300 text-xs font-medium truncate min-w-0 flex-1"
                            title={columnLabel}
                          >
                            {columnLabel}
                          </Label>
                        </div>
                      )
                    )}
                  </div>
                </div>
              </div>

              {/* Bottom Row - Filters, Format, and Summary */}
              <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6">
                {/* Filters Section */}
                <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 shadow-sm">
                  <div className="p-4 border-b border-gray-200 dark:border-gray-700">
                    <Heading
                      level="h3"
                      className="text-base font-semibold text-gray-900 dark:text-gray-100"
                    >
                      Filters
                    </Heading>
                  </div>

                  <div className="p-4 space-y-4">
                    <div>
                      <Label
                        htmlFor="seasons"
                        className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"
                      >
                        Seasons
                      </Label>
                      <MultiSelect
                        options={[
                          { value: "base", label: "Base Pricing" },
                          ...seasonalPeriods.map((season) => ({
                            value: season.id,
                            label: season.name,
                          })),
                        ]}
                        selectedValues={exportOptions.selectedSeasonIds}
                        onChange={(selectedValues) =>
                          setExportOptions((prev) => ({
                            ...prev,
                            selectedSeasonIds: selectedValues,
                          }))
                        }
                        placeholder="All seasons (optional)"
                        showSelectAll={true}
                      />
                    </div>

                    <div>
                      <Label
                        htmlFor="room_types"
                        className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"
                      >
                        Room Types
                      </Label>
                      <MultiSelect
                        options={roomConfigs.map((rc) => ({
                          value: rc.id,
                          label: rc.title,
                        }))}
                        selectedValues={exportOptions.selectedRoomConfigIds}
                        onChange={(selectedValues) =>
                          setExportOptions((prev) => ({
                            ...prev,
                            selectedRoomConfigIds: selectedValues,
                          }))
                        }
                        placeholder="All room types (optional)"
                        showSelectAll={true}
                      />
                    </div>

                    <div>
                      <Label
                        htmlFor="occupancy_types"
                        className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"
                      >
                        Occupancy Types
                      </Label>
                      <MultiSelect
                        options={occupancyConfigs.map((oc) => ({
                          value: oc.id,
                          label: oc.name,
                        }))}
                        selectedValues={
                          exportOptions.selectedOccupancyConfigIds
                        }
                        onChange={(selectedValues) =>
                          setExportOptions((prev) => ({
                            ...prev,
                            selectedOccupancyConfigIds: selectedValues,
                          }))
                        }
                        placeholder="All occupancy types (optional)"
                        showSelectAll={true}
                      />
                    </div>

                    <div>
                      <Label
                        htmlFor="meal_plans"
                        className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"
                      >
                        Meal Plans
                      </Label>
                      <MultiSelect
                        options={mealPlans.map((mp) => ({
                          value: mp.id,
                          label: mp.name,
                        }))}
                        selectedValues={exportOptions.selectedMealPlanIds}
                        onChange={(selectedValues) =>
                          setExportOptions((prev) => ({
                            ...prev,
                            selectedMealPlanIds: selectedValues,
                          }))
                        }
                        placeholder="All meal plans (optional)"
                        showSelectAll={true}
                      />
                    </div>
                  </div>
                </div>

                {/* Format Section */}
                <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 shadow-sm">
                  <div className="p-4 border-b border-gray-200 dark:border-gray-700">
                    <Heading
                      level="h3"
                      className="text-base font-semibold text-gray-900 dark:text-gray-100"
                    >
                      Export Format
                    </Heading>
                  </div>

                  <div className="p-4 space-y-3">
                    <div className="flex items-center gap-3 p-3 rounded-md hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors border border-gray-100 dark:border-gray-600">
                      <input
                        type="radio"
                        id="format-xlsx"
                        name="fileFormat"
                        value="xlsx"
                        checked={exportOptions.format === "xlsx"}
                        onChange={() =>
                          setExportOptions((prev) => ({
                            ...prev,
                            format: "xlsx",
                          }))
                        }
                        className="h-4 w-4 text-blue-600 dark:text-blue-400 focus:ring-blue-500 dark:focus:ring-blue-400 border-gray-300 dark:border-gray-600"
                      />
                      <div className="flex-grow">
                        <Label
                          htmlFor="format-xlsx"
                          className="cursor-pointer text-gray-900 dark:text-gray-100 font-medium"
                        >
                          Excel (.xlsx)
                        </Label>
                        <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
                          Recommended for data analysis
                        </p>
                      </div>
                    </div>

                    <div className="flex items-center gap-3 p-3 rounded-md hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors border border-gray-100 dark:border-gray-600">
                      <input
                        type="radio"
                        id="format-csv"
                        name="fileFormat"
                        value="csv"
                        checked={exportOptions.format === "csv"}
                        onChange={() =>
                          setExportOptions((prev) => ({
                            ...prev,
                            format: "csv",
                          }))
                        }
                        className="h-4 w-4 text-blue-600 dark:text-blue-400 focus:ring-blue-500 dark:focus:ring-blue-400 border-gray-300 dark:border-gray-600"
                      />
                      <div className="flex-grow">
                        <Label
                          htmlFor="format-csv"
                          className="cursor-pointer text-gray-900 dark:text-gray-100 font-medium"
                        >
                          CSV (.csv)
                        </Label>
                        <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
                          Compatible with most applications
                        </p>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Export Summary Section */}
                <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 shadow-sm">
                  <div className="p-4 border-b border-gray-200 dark:border-gray-700">
                    <Heading
                      level="h3"
                      className="text-base font-semibold text-gray-900 dark:text-gray-100"
                    >
                      Export Summary
                    </Heading>
                  </div>

                  <div className="p-4 space-y-4">
                    {/* Selected Fields Summary */}
                    <div
                      className={`rounded-lg p-3 border ${
                        selectedFieldsCount === 0
                          ? "bg-red-50 dark:bg-red-900/20 border-red-200 dark:border-red-800"
                          : "bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-800"
                      }`}
                    >
                      <div className="flex items-center justify-between mb-2">
                        <h4
                          className={`text-sm font-semibold ${
                            selectedFieldsCount === 0
                              ? "text-red-900 dark:text-red-100"
                              : "text-blue-900 dark:text-blue-100"
                          }`}
                        >
                          Selected Fields
                        </h4>
                        <span
                          className={`text-xs font-medium px-2 py-1 rounded-full ${
                            selectedFieldsCount === 0
                              ? "bg-red-100 dark:bg-red-800 text-red-800 dark:text-red-200"
                              : "bg-blue-100 dark:bg-blue-800 text-blue-800 dark:text-blue-200"
                          }`}
                        >
                          {selectedFieldsCount} of {totalFieldsCount}
                        </span>
                      </div>
                      <div
                        className={`text-xs max-h-16 overflow-y-auto ${
                          selectedFieldsCount === 0
                            ? "text-red-700 dark:text-red-300"
                            : "text-blue-700 dark:text-blue-300"
                        }`}
                      >
                        {selectedFieldsCount === 0 ? (
                          <div className="text-center font-medium">
                            ⚠️ No fields selected for export
                          </div>
                        ) : (
                          <>
                            {Object.entries(exportOptions.selectedColumns)
                              .filter(([_, selected]) => selected)
                              .slice(0, 4)
                              .map(([field]) => (
                                <div key={field} className="truncate">
                                  •{" "}
                                  {
                                    AVAILABLE_COLUMNS[
                                      field as keyof typeof AVAILABLE_COLUMNS
                                    ]
                                  }
                                </div>
                              ))}
                            {selectedFieldsCount > 4 && (
                              <div className="text-blue-600 dark:text-blue-400 font-medium">
                                +{selectedFieldsCount - 4} more...
                              </div>
                            )}
                          </>
                        )}
                      </div>
                    </div>

                    {/* Filter & Export Details Combined */}
                    <div className="grid grid-cols-2 gap-3">
                      <div className="bg-green-50 dark:bg-green-900/20 rounded-lg p-3 border border-green-200 dark:border-green-800">
                        <h4 className="text-sm font-semibold text-green-900 dark:text-green-100 mb-2">
                          Filters
                        </h4>
                        <div className="space-y-1 text-xs text-green-700 dark:text-green-300">
                          <div>
                            <span className="font-medium">Seasons: </span>
                            {exportOptions.selectedSeasonIds.length === 0
                              ? "All"
                              : `${exportOptions.selectedSeasonIds.length} selected`}
                          </div>
                          <div>
                            <span className="font-medium">Rooms: </span>
                            <span className="truncate">
                              {exportOptions.selectedRoomConfigIds.length === 0
                                ? "All"
                                : `${exportOptions.selectedRoomConfigIds.length} selected`}
                            </span>
                          </div>
                          <div>
                            <span className="font-medium">Occupancy: </span>
                            <span className="truncate">
                              {exportOptions.selectedOccupancyConfigIds
                                .length === 0
                                ? "All"
                                : `${exportOptions.selectedOccupancyConfigIds.length} selected`}
                            </span>
                          </div>
                          <div>
                            <span className="font-medium">Meals: </span>
                            <span className="truncate">
                              {exportOptions.selectedMealPlanIds.length === 0
                                ? "All"
                                : `${exportOptions.selectedMealPlanIds.length} selected`}
                            </span>
                          </div>
                        </div>
                      </div>

                      <div className="bg-purple-50 dark:bg-purple-900/20 rounded-lg p-3 border border-purple-200 dark:border-purple-800">
                        <h4 className="text-sm font-semibold text-purple-900 dark:text-purple-100 mb-2">
                          Export Details
                        </h4>
                        <div className="space-y-1 text-xs text-purple-700 dark:text-purple-300">
                          <div>
                            <span className="font-medium">Format: </span>
                            <span className="uppercase">
                              {exportOptions.format}
                            </span>
                          </div>
                          <div>
                            <span className="font-medium">Date: </span>
                            {new Date().toLocaleDateString()}
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Instructions */}
                    <div className="bg-gray-50 dark:bg-gray-800/50 rounded-lg p-3 border border-gray-200 dark:border-gray-700">
                      <div className="text-sm text-gray-600 dark:text-gray-400 text-center">
                        Click "Export Data" to download the file to your
                        downloads folder
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Footer */}
          <div className="flex-shrink-0 py-6 px-8 bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700">
            <div className="flex items-center justify-between">
              <div className="text-sm text-gray-500 dark:text-gray-400">
                {selectedFieldsCount > 0
                  ? `Ready to export ${selectedFieldsCount} fields`
                  : "Please select at least one field to export"}
              </div>
              <div className="flex gap-4">
                <Button
                  variant="secondary"
                  onClick={onClose}
                  className="px-6 py-3 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600 border-gray-300 dark:border-gray-600 font-medium"
                >
                  Cancel
                </Button>
                <Button
                  variant="primary"
                  onClick={handleExport}
                  disabled={selectedFieldsCount === 0 || isExporting}
                  className={`flex items-center gap-3 px-6 py-3 shadow-lg font-medium ${
                    selectedFieldsCount === 0 || isExporting
                      ? "bg-gray-400 dark:bg-gray-600 text-gray-200 dark:text-gray-400 cursor-not-allowed"
                      : "bg-blue-600 dark:bg-blue-500 hover:bg-blue-700 dark:hover:bg-blue-600 text-white"
                  }`}
                >
                  {isExporting ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent"></div>
                      <span>Exporting...</span>
                    </>
                  ) : (
                    <>
                      <Download className="w-5 h-5" />
                      Export Data
                    </>
                  )}
                </Button>
              </div>
            </div>
          </div>
        </FocusModal.Body>
      </FocusModal.Content>
    </FocusModal>
  );
};

export default PricingExportModal;
