import React, { useState, useEffect, useMemo } from "react";
import {
  Container,
  Heading,
  Text,
  Button,
  toast,
  Toaster,
  Tabs,
  IconButton,
} from "@camped-ai/ui";
import { ArrowLeft } from "lucide-react";
import ComprehensivePricingTable from "./comprehensive-pricing-table.tsx";
import PricingCalendarView from "./pricing-calendar-view";
import { useAdminHotelComprehensivePricing } from "../../../hooks/hotel/use-admin-hotel-comprehensive-pricing.ts";
import { usePricingCalendar } from "../../../hooks/hotel/use-pricing-calendar";
import OccupancyConfigsManager from "./occupancy-configs-manager.tsx";
import MealPlansManager from "./meal-plans-manager.tsx";
import SeasonsManager from "./seasons-manager.tsx";
import HotelPricingTableSkeleton from "../../shared/hotel-pricing-table-skeleton.tsx";
import {
  type BasePriceRule,
  type SeasonalPriceRule,
  type PricingContext,
} from "../../../utils/pricing-calendar-utils";

type RoomConfig = {
  id: string;
  title: string;
  handle?: string;
  description?: string;
};

type SeasonalPeriod = {
  id: string;
  name: string;
  start_date: string;
  end_date: string;
};

type HotelPricingManagerProps = {
  hotelId: string;
  roomConfigs?: RoomConfig[]; // Optional - will use data from API
  onBack?: () => void;
  hotelName?: string; // Optional - will use data from API
  canEdit?: boolean;
  canCreate?: boolean;
  canDelete?: boolean;
  hideBackButton?: boolean;
};

const HotelPricingManager: React.FC<HotelPricingManagerProps> = ({
  hotelId,
  roomConfigs: propRoomConfigs,
  hotelName,
  onBack,
  canEdit = false,
  canCreate = false,
  canDelete = false,
  hideBackButton = false,
}) => {
  const [activeTab, setActiveTab] = useState("calendar");
  const [currentCurrency, setCurrentCurrency] = useState("GBP"); // Default to GBP

  // Reset tab if it was set to a removed tab
  useEffect(() => {
    if (activeTab === "channels" || activeTab === "unified") {
      setActiveTab("pricing");
    }
  }, [activeTab]);

  // Use the comprehensive pricing hook with currency parameter
  console.log(`[HOTEL PRICING MANAGER] Using currency: ${currentCurrency} for hotel: ${hotelId}`);
  const {
    data: comprehensiveData,
    isLoading,
    isError,
    refetch,
  } = useAdminHotelComprehensivePricing(hotelId, currentCurrency);

  // Use the pricing calendar hook for date-specific pricing updates
  const pricingCalendar = usePricingCalendar(hotelId);

  // Handle tab changes with data refetching
  const handleTabChange = (newTab: string) => {
    console.log(`[HOTEL PRICING MANAGER] Tab changed from ${activeTab} to ${newTab} - refetching data`);
    setActiveTab(newTab);

    // Refetch comprehensive pricing data when switching tabs to ensure latest data
    refetch();
  };

  // Memoize room configs to prevent unnecessary re-renders
  const roomConfigs = useMemo(() => {
    return comprehensiveData?.roomConfigs || propRoomConfigs || [];
  }, [comprehensiveData?.roomConfigs, propRoomConfigs]);

  // Memoize other data to prevent unnecessary re-renders
  const occupancyConfigs = useMemo(() => {
    return comprehensiveData?.occupancyConfigs || [];
  }, [comprehensiveData?.occupancyConfigs]);

  const mealPlans = useMemo(() => {
    return comprehensiveData?.mealPlans || [];
  }, [comprehensiveData?.mealPlans]);

  const pricingData = useMemo(() => {
    return comprehensiveData?.pricingData || {};
  }, [comprehensiveData?.pricingData]);

  // Get hotel name from API data if not provided as prop
  const displayHotelName =
    hotelName || comprehensiveData?.hotel?.name || "Hotel";

  const [seasonalPeriods, setSeasonalPeriods] = useState<SeasonalPeriod[]>([]);

  // Update seasonal periods when comprehensive data changes
  useEffect(() => {
    if (comprehensiveData?.seasonalPeriods) {
      setSeasonalPeriods(comprehensiveData.seasonalPeriods);
    }
  }, [comprehensiveData?.seasonalPeriods]);

  const handleSavePricing = async (data: any) => {
    // If the data contains seasonal periods, update the state
    if (data && data.seasonal_periods) {
      setSeasonalPeriods(data.seasonal_periods);
    }

    // Refetch the data from the server to ensure UI shows the latest saved values
    try {
      await refetch();
      toast.success("Success", {
        description: "Pricing saved successfully",
      });
    } catch (error) {
      console.error("Error refreshing pricing data:", error);
      // Still show success since the save operation itself succeeded
      toast.success("Success", {
        description: "Pricing saved successfully",
      });
    }
  };

  // Handle calendar price updates
  const handleCalendarPriceUpdate = async (
    date: Date,
    price: number,
    context: PricingContext,
    costMarginData?: {
      cost: number;
      fixedMargin: number;
      marginPercentage: number;
    }
  ) => {
    try {
      // Transform comprehensive data to the format expected by pricing calendar utils
      const basePriceRules: BasePriceRule[] = [];
      const seasonalRules: SeasonalPriceRule[] = [];

      // Extract base price rules from room pricing data
      comprehensiveData?.room_pricing_data?.forEach((roomData: any) => {
        roomData.weekday_rules?.forEach((rule: any) => {
          basePriceRules.push({
            id: rule.id,
            room_config_id: roomData.room_config_id,
            occupancy_type_id: rule.occupancy_type_id,
            meal_plan_id: rule.meal_plan_id,
            weekday_prices: rule.weekday_prices,
            currency_code: rule.currency_code || currentCurrency,
          });
        });

        // Extract seasonal rules
        roomData.seasonal_prices?.forEach((seasonalPrice: any) => {
          seasonalPrice.weekday_rules?.forEach((rule: any) => {
            seasonalRules.push({
              id: rule.id,
              base_price_rule_id: rule.base_price_rule_id || rule.id,
              start_date: seasonalPrice.start_date,
              end_date: seasonalPrice.end_date,
              amount: price,
              currency_code: rule.currency_code || currentCurrency,
              priority: rule.priority || 100,
              name: seasonalPrice.name,
            });
          });
        });
      });

      await pricingCalendar.updateDatePrice(
        date,
        price,
        context,
        basePriceRules,
        seasonalRules,
        currentCurrency,
        costMarginData
      );
    } catch (error) {
      console.error("Error updating calendar price:", error);
      throw error;
    }
  };

  if (isLoading) {
    return <HotelPricingTableSkeleton />;
  }

  return (
    <>
      <Toaster />
      {!hideBackButton && (
        <div className="">
          <div className="flex items-center gap-4 mb-2">
            <IconButton onClick={onBack}>
              <ArrowLeft className="w-4 h-4" />
            </IconButton>
            <Heading level="h1">{displayHotelName}</Heading>
          </div>
        </div>
      )}

      {roomConfigs.length === 0 ? (
        <div className="bg-muted p-8 rounded-lg border border-border text-center">
          <Text className="text-muted-foreground mb-4">
            No room configurations found. Please add room configurations first.
          </Text>
          {canCreate && (
            <Button
              variant="primary"
              onClick={() =>
                (window.location.href = `/hotel-management/hotels/${hotelId}/room-configs/new`)
              }
            >
              Add Room Configuration
            </Button>
          )}
        </div>
      ) : (
        <Tabs value={activeTab} onValueChange={handleTabChange} className="w-full">
          <Tabs.List className=" mb-4">
            <Tabs.Trigger value="calendar" className="px-4 py-2">
              Calendar View
            </Tabs.Trigger>
            <Tabs.Trigger value="pricing" className="px-4 py-2">
              Pricing Table
            </Tabs.Trigger>
        
            <Tabs.Trigger value="seasons" className="px-4 py-2">
              Seasons
            </Tabs.Trigger>
            <Tabs.Trigger value="occupancy" className="px-4 py-2">
              Occupancy Types
            </Tabs.Trigger>
            <Tabs.Trigger value="mealplans" className="px-4 py-2">
              Meal Plans
            </Tabs.Trigger>
            {/* <Tabs.Trigger value="channel-overrides" className="px-4 py-2">Channel Overrides</Tabs.Trigger> */}
          </Tabs.List>

          <Tabs.Content value="occupancy">
            <OccupancyConfigsManager
              hotelId={hotelId}
              canEdit={canEdit}
              canCreate={canCreate}
              canDelete={canDelete}
            />
          </Tabs.Content>

          <Tabs.Content value="mealplans">
            <MealPlansManager
              hotelId={hotelId}
              canEdit={canEdit}
              canCreate={canCreate}
              canDelete={canDelete}
            />
          </Tabs.Content>

          <Tabs.Content value="seasons">
            <SeasonsManager
              hotelId={hotelId}
              seasonalPeriods={seasonalPeriods}
              setSeasonalPeriods={setSeasonalPeriods}
              canEdit={canEdit}
              canCreate={canCreate}
              canDelete={canDelete}
            />
          </Tabs.Content>

          <Tabs.Content value="pricing">
            <ComprehensivePricingTable
              hotelId={hotelId}
              roomConfigs={roomConfigs}
              occupancyConfigs={occupancyConfigs}
              mealPlans={mealPlans}
              seasonalPeriods={seasonalPeriods}
              setSeasonalPeriods={setSeasonalPeriods}
              initialPrices={pricingData}
              roomPricingData={comprehensiveData?.room_pricing_data || []}
              onSave={handleSavePricing}
              canEdit={canEdit}
              canCreate={canCreate}
              canDelete={canDelete}
              hideBackButton={hideBackButton}
              onRefetch={refetch}
            />
          </Tabs.Content>

          <Tabs.Content value="calendar">
            <PricingCalendarView
              hotelId={hotelId}
              roomConfigs={roomConfigs}
              occupancyConfigs={occupancyConfigs}
              mealPlans={mealPlans}
              basePriceRules={(() => {
                const rules: BasePriceRule[] = [];
                comprehensiveData?.room_pricing_data?.forEach((roomData: any) => {
                  roomData.weekday_rules?.forEach((rule: any) => {
                    // Extract weekday-specific cost and margin data
                    const weekdayCosts: any = {};
                    const weekdayFixedMargins: any = {};
                    const weekdayMarginPercentages: any = {};

                    if (rule.weekday_values) {
                      ['mon', 'tue', 'wed', 'thu', 'fri', 'sat', 'sun'].forEach(day => {
                        weekdayCosts[day] = rule.weekday_values[day]?.gross_cost || null;
                        weekdayFixedMargins[day] = rule.weekday_values[day]?.fixed_margin || null;
                        weekdayMarginPercentages[day] = rule.weekday_values[day]?.margin_percentage || null;
                      });
                    }

                    // Ensure weekday_prices has valid numbers
                    const safeWeekdayPrices = {
                      mon: Number(rule.weekday_prices?.mon) || 0,
                      tue: Number(rule.weekday_prices?.tue) || 0,
                      wed: Number(rule.weekday_prices?.wed) || 0,
                      thu: Number(rule.weekday_prices?.thu) || 0,
                      fri: Number(rule.weekday_prices?.fri) || 0,
                      sat: Number(rule.weekday_prices?.sat) || 0,
                      sun: Number(rule.weekday_prices?.sun) || 0,
                    };

                    rules.push({
                      id: rule.id,
                      room_config_id: roomData.room_config_id,
                      occupancy_type_id: rule.occupancy_type_id,
                      meal_plan_id: rule.meal_plan_id,
                      weekday_prices: safeWeekdayPrices,
                      currency_code: rule.currency_code || currentCurrency,
                      // Add cost and margin data
                      default_gross_cost: rule.default_values?.gross_cost || null,
                      default_fixed_margin: rule.default_values?.fixed_margin || null,
                      default_margin_percentage: rule.default_values?.margin_percentage || null,
                      weekday_costs: weekdayCosts,
                      weekday_fixed_margins: weekdayFixedMargins,
                      weekday_margin_percentages: weekdayMarginPercentages,
                    });
                  });
                });
                return rules;
              })()}
              seasonalRules={(() => {
                const rules: SeasonalPriceRule[] = [];
                comprehensiveData?.room_pricing_data?.forEach((roomData: any) => {
                  roomData.seasonal_prices?.forEach((seasonalPrice: any) => {
                    seasonalPrice.weekday_rules?.forEach((rule: any) => {
                      rules.push({
                        id: rule.id,
                        base_price_rule_id: rule.base_price_rule_id || rule.id,
                        start_date: seasonalPrice.start_date,
                        end_date: seasonalPrice.end_date,
                        amount: Number(rule.weekday_prices?.mon) || 0, // This is simplified
                        currency_code: rule.currency_code || currentCurrency,
                        priority: rule.priority || 100,
                        name: seasonalPrice.name,
                      });
                    });
                  });
                });
                return rules;
              })()}
              currencyCode={currentCurrency}
              onPriceUpdate={handleCalendarPriceUpdate}
              canEdit={canEdit}
               onRefetch={refetch}
            />
          </Tabs.Content>

          {/* <Tabs.Content value="channel-overrides">
            <SalesChannelPriceOverrides
              hotelId={hotelId}
              roomConfigs={roomConfigs}
              occupancyConfigs={occupancyConfigs}
              mealPlans={mealPlans}
              seasonalPeriods={seasonalPeriods}
              onSave={handleSavePricing}
            />
          </Tabs.Content> */}
        </Tabs>
      )}
    </>
  );
};

export default HotelPricingManager;
