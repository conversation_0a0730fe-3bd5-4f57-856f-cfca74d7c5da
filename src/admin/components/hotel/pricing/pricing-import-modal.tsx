import React, { useState, useRef, useEffect } from "react";
import { Button, FocusModal, Text, Heading, toast, Input, Label, Select } from "@camped-ai/ui";
import {
  Upload,
  Download,
  AlertCircle,
  CheckCircle,
  FileIcon,
  X,
  Calendar,
} from "lucide-react";
import ImportPreviewTable from "./import-preview-table";
import { useHotelPricingImport } from "../../../hooks/hotel-management/use-hotel-pricing-import";
import { MultiSelect } from "../../common/MultiSelect";
import { useHotels } from "../../../hooks/supplier-products-services/use-hotels";

export type ImportValidationError = {
  row: number;
  field: string;
  message: string;
  value?: any;
};

export type ImportResult = {
  success: boolean;
  message: string;
  imported: number;
  errors: ImportValidationError[];
};

type PricingImportModalProps = {
  open: boolean;
  onClose: () => void;
  hotelId: string;
  currentCurrency: string;
  onImportComplete?: () => void;
};

const PricingImportModal: React.FC<PricingImportModalProps> = ({
  open,
  onClose,
  hotelId,
  currentCurrency,
  onImportComplete,
}) => {
  const [importFile, setImportFile] = useState<File | null>(null);
  const [importData, setImportData] = useState<any[]>([]);
  const [importErrors, setImportErrors] = useState<ImportValidationError[]>([]);
  const [isUploading, setIsUploading] = useState(false);
  const [importSuccess, setImportSuccess] = useState(false);
  const [importedCount, setImportedCount] = useState(0);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Export template state
  const [fromDate, setFromDate] = useState<string>("");
  const [toDate, setToDate] = useState<string>("");
  const [selectedHotels, setSelectedHotels] = useState<string[]>([]);
  const [isDownloadingTemplate, setIsDownloadingTemplate] = useState(false);

  // Use the React Query hook for proper cache invalidation
  const { importPricingData, isImporting, importError, importResult } =
    useHotelPricingImport(hotelId);

  // Fetch hotels for multi-select
  const { data: hotelsData, isLoading: isLoadingHotels } = useHotels({ is_active: true });

  // Reset modal state when opening
  useEffect(() => {
    if (open) {
      resetImportModal();
    }
  }, [open]);

  const resetImportModal = () => {
    setImportFile(null);
    setImportData([]);
    setImportErrors([]);
    setIsUploading(false);
    setImportSuccess(false);
    setImportedCount(0);
  };

  const handleFileSelect = async (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    const file = event.target.files?.[0];
    if (file) {
      setImportFile(file);
      setImportData([]);
      setImportErrors([]);
      setImportSuccess(false);

      // Validate file type
      const validTypes = [
        "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
        "application/vnd.ms-excel",
        "text/csv",
        "application/csv",
      ];

      if (
        !validTypes.includes(file.type) &&
        !file.name.endsWith(".xlsx") &&
        !file.name.endsWith(".csv")
      ) {
        toast.error("Please upload an Excel (.xlsx) or CSV (.csv) file");
        return;
      }

      // Validate file size (10MB limit)
      if (file.size > 10 * 1024 * 1024) {
        toast.error("File size must be less than 10MB");
        return;
      }

      // Auto-parse file
      await parseFile(file);
    }
  };

  const parseFile = async (file: File) => {
    setIsUploading(true);
    try {
      const formData = new FormData();
      formData.append("file", file);

      const response = await fetch(
        `/admin/hotel-management/hotels/${hotelId}/pricing/parse`,
        {
          method: "POST",
          body: formData,
        }
      );

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(
          errorData.message || `Failed to parse file: ${response.statusText}`
        );
      }

      const result = await response.json();

      if (!result.success) {
        throw new Error(result.message || "Failed to parse file");
      }

      if (!result.pricing_data || result.pricing_data.length === 0) {
        throw new Error("No valid pricing data found in the file");
      }

      setImportData(result.pricing_data);
      setImportErrors(result.errors || []);

      toast.success(`Parsed ${result.pricing_data.length} pricing records`);
    } catch (error) {
      toast.error(
        error instanceof Error ? error.message : "Failed to parse file"
      );
      setImportErrors([
        {
          row: 0,
          field: "file",
          message:
            error instanceof Error ? error.message : "Failed to parse file",
          value: file.name,
        },
      ]);
    } finally {
      setIsUploading(false);
    }
  };

  const handleImport = async () => {
    if (importData.length === 0) return;

    try {
      // Use the React Query hook which handles cache invalidation
      await importPricingData({
        pricingData: importData,
        currency: currentCurrency,
      });

      // Handle success
      setImportSuccess(true);
      setImportedCount(importData.length);
      onImportComplete?.();

      // The hook will show success toast and invalidate cache automatically
    } catch (error) {
      // The hook will show error toast automatically
      setImportErrors([
        {
          row: 0,
          field: "general",
          message: error instanceof Error ? error.message : "Import failed",
          value: null,
        },
      ]);
    }
  };

  const downloadTemplate = async () => {
    // Validate required fields for bulk template
    if (!fromDate || !toDate) {
      toast.error("Please select both From Date and To Date");
      return;
    }

    if (selectedHotels.length === 0) {
      toast.error("Please select at least one hotel");
      return;
    }

    setIsDownloadingTemplate(true);
    try {
      // Use bulk template API if multiple hotels or date range is specified
      const hotelIds = selectedHotels.includes(hotelId)
        ? selectedHotels.join(',')
        : [hotelId, ...selectedHotels].join(',');

      const response = await fetch(
        `/admin/hotel-management/pricing/bulk-template?currency=${currentCurrency}&from_date=${fromDate}&to_date=${toDate}&hotel_ids=${hotelIds}`
      );

      if (!response.ok) {
        throw new Error("Failed to download template");
      }

      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement("a");
      a.href = url;
      a.download = `hotel_pricing_bulk_template_${currentCurrency}_${fromDate}_to_${toDate}.xlsx`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);

      toast.success("Template downloaded successfully");
    } catch (error) {
      console.error("Template download error:", error);
      toast.error("Failed to download template");
    } finally {
      setIsDownloadingTemplate(false);
    }
  };

  return (
    <FocusModal open={open} onOpenChange={onClose}>
      <FocusModal.Content>
        <FocusModal.Header>Import Pricing Data</FocusModal.Header>

        <FocusModal.Body className="overflow-y-auto">
          {!importSuccess && (
            <>
              {/* Step 1: Download Template */}
              <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 shadow-sm">
                <div className="flex items-start gap-3 p-6">
                  <div className="flex-shrink-0 w-8 h-8 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center">
                    <span className="text-blue-600 dark:text-blue-400 font-semibold text-sm">
                      1
                    </span>
                  </div>
                  <div className="flex-1">
                    <Heading
                      level="h3"
                      className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2"
                    >
                      Export Template
                    </Heading>
                    <Text className="text-gray-600 dark:text-gray-400 mb-4">
                      Generate an Excel template with all possible combinations of Date Range × Hotel × Room Type × Occupancy Type × Meal Plan.
                      Select your date range and hotels to create a comprehensive template for bulk pricing updates.
                    </Text>

                    {/* Date Range Selection */}
                    <div className="grid grid-cols-2 gap-4 mb-4">
                      <div>
                        <Label htmlFor="from-date" className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                          From Date
                        </Label>
                        <Input
                          id="from-date"
                          type="date"
                          value={fromDate}
                          onChange={(e) => setFromDate(e.target.value)}
                          className="w-full"
                          required
                        />
                      </div>
                      <div>
                        <Label htmlFor="to-date" className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                          To Date
                        </Label>
                        <Input
                          id="to-date"
                          type="date"
                          value={toDate}
                          onChange={(e) => setToDate(e.target.value)}
                          className="w-full"
                          required
                        />
                      </div>
                    </div>

                    {/* Hotel Selection */}
                    <div className="mb-4">
                      <Label className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                        Select Hotels
                      </Label>
                      <MultiSelect
                        options={hotelsData?.hotels?.map(hotel => ({
                          value: hotel.id,
                          label: hotel.name
                        })) || []}
                        selectedValues={selectedHotels}
                        onChange={setSelectedHotels}
                        placeholder="Select hotels..."
                        disabled={isLoadingHotels}
                      />
                      <Text className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                        Current hotel ({hotelId}) will be automatically included
                      </Text>
                    </div>

                    <div className="bg-blue-50 dark:bg-blue-900/20 p-3 rounded-md border border-blue-200 dark:border-blue-700 mb-4">
                      <Text className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                        Template will include:
                      </Text>
                      <ul className="text-sm text-gray-600 dark:text-gray-400 space-y-1">
                        <li>• <strong>Date From/To:</strong> Pre-filled with selected date range</li>
                        <li>• <strong>Hotel Name:</strong> Pre-filled with selected hotels</li>
                        <li>• <strong>Room Type:</strong> All room types for each hotel</li>
                        <li>• <strong>Occupancy Type:</strong> All occupancy types for each hotel</li>
                        <li>• <strong>Meal Plan:</strong> All meal plans for each hotel</li>
                        <li>• <strong>Cost:</strong> Empty (values in pence/cents, no decimals)</li>
                        <li>• <strong>Fixed Margin:</strong> Empty (user input required)</li>
                        <li>• <strong>Margin Percentage:</strong> Empty (2 decimal places)</li>
                      </ul>
                    </div>

                    <Button
                      variant="secondary"
                      onClick={downloadTemplate}
                      className="flex items-center gap-2"
                      disabled={isDownloadingTemplate || !fromDate || !toDate || selectedHotels.length === 0}
                    >
                      {isDownloadingTemplate ? (
                        <>
                          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-current"></div>
                          Generating Template...
                        </>
                      ) : (
                        <>
                          <Download className="w-4 h-4" />
                          Download Template ({currentCurrency})
                        </>
                      )}
                    </Button>
                  </div>
                </div>
              </div>

              {/* Step 2: Upload File */}
              <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 shadow-sm">
                <div className="flex items-start gap-3 p-6">
                  <div className="flex-shrink-0 w-8 h-8 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center">
                    <span className="text-green-600 dark:text-green-400 font-semibold text-sm">
                      2
                    </span>
                  </div>
                  <div className="flex-1">
                    <Heading
                      level="h3"
                      className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2"
                    >
                      Import Data
                    </Heading>
                    <Text className="text-gray-600 dark:text-gray-400 mb-4">
                      Upload the completed Excel file with your pricing data.
                      Fill in the Cost, Fixed Margin, and Margin Percentage columns in the template.
                      Either Fixed Margin OR Margin Percentage should be provided (not both).
                    </Text>

                    {!importFile ? (
                      <div className="border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg p-6 text-center hover:border-gray-400 dark:hover:border-gray-500 transition-colors">
                        <FileIcon className="w-12 h-12 text-gray-400 dark:text-gray-500 mx-auto mb-4" />
                        <Text className="text-gray-600 dark:text-gray-400 mb-4">
                          Drag and drop your file here, or click to browse
                        </Text>
                        <Button
                          variant="secondary"
                          onClick={() => fileInputRef.current?.click()}
                          className="flex items-center gap-2"
                        >
                          <Upload className="w-4 h-4" />
                          Choose File
                        </Button>
                        <input
                          ref={fileInputRef}
                          type="file"
                          accept=".xlsx,.xls,.csv"
                          onChange={handleFileSelect}
                          className="hidden"
                        />
                        <Text className="text-xs text-gray-500 dark:text-gray-400 mt-2">
                          Supports Excel (.xlsx) and CSV (.csv) files up to 10MB
                        </Text>
                      </div>
                    ) : (
                      <div className="space-y-4">
                        <div className="flex items-center gap-3 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                          <FileIcon className="w-5 h-5 text-blue-600 dark:text-blue-400" />
                          <div className="flex-1">
                            <Text className="font-medium text-gray-900 dark:text-gray-100">
                              {importFile.name}
                            </Text>
                            <Text className="text-sm text-gray-500 dark:text-gray-400">
                              {(importFile.size / 1024 / 1024).toFixed(2)} MB
                            </Text>
                          </div>
                          <Button
                            variant="secondary"
                            size="small"
                            onClick={() => {
                              setImportFile(null);
                              setImportData([]);
                              setImportErrors([]);
                              if (fileInputRef.current) {
                                fileInputRef.current.value = "";
                              }
                            }}
                          >
                            <X className="w-4 h-4" />
                          </Button>
                        </div>

                        {isUploading && (
                          <div className="flex items-center gap-2 text-blue-600 dark:text-blue-400">
                            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600 dark:border-blue-400"></div>
                            <Text className="text-sm">Parsing file...</Text>
                          </div>
                        )}

                        {importData.length > 0 && (
                          <ImportPreviewTable
                            importData={importData}
                            currentCurrency={currentCurrency}
                            maxHeight="400px"
                            validationErrors={importErrors}
                          />
                        )}

                        {importErrors.length > 0 && (
                          <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-700 rounded-lg p-4">
                            <div className="flex items-center gap-2 mb-2">
                              <AlertCircle className="w-5 h-5 text-red-600 dark:text-red-400" />
                              <Text className="font-medium text-red-800 dark:text-red-200">
                                Validation Errors ({importErrors.length})
                              </Text>
                            </div>
                            <div className="space-y-2 max-h-40 overflow-y-auto">
                              {importErrors.slice(0, 10).map((error, index) => (
                                <div
                                  key={index}
                                  className="text-sm text-red-700 dark:text-red-300"
                                >
                                  <strong>
                                    Row {error.row + 1}, {error.field}:
                                  </strong>{" "}
                                  {error.message}
                                </div>
                              ))}
                              {importErrors.length > 10 && (
                                <Text className="text-sm text-red-600 dark:text-red-400">
                                  ... and {importErrors.length - 10} more errors
                                </Text>
                              )}
                            </div>
                          </div>
                        )}
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </>
          )}

          {/* Success State */}
          {importSuccess && (
            <div className="text-center py-8">
              <CheckCircle className="w-16 h-16 text-green-600 dark:text-green-400 mx-auto mb-4" />
              <Heading
                level="h3"
                className="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-2"
              >
                Import Completed Successfully!
              </Heading>
              <Text className="text-gray-600 dark:text-gray-400">
                Successfully imported {importedCount} pricing records.
              </Text>
            </div>
          )}
        </FocusModal.Body>

        {/* Success Footer */}
        {importSuccess && (
          <FocusModal.Footer>
            <div className="flex justify-end">
              <Button variant="primary" onClick={onClose}>
                Close
              </Button>
            </div>
          </FocusModal.Footer>
        )}

        {/* Import Button Footer */}
        {!importSuccess &&
          importData.length > 0 &&
          importErrors.length === 0 && (
            <FocusModal.Footer>
              <div className="flex justify-end gap-3">
                <Button variant="secondary" onClick={onClose}>
                  Cancel
                </Button>
                <Button
                  variant="primary"
                  onClick={handleImport}
                  disabled={isImporting}
                  className="flex items-center gap-2"
                >
                  {isImporting ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                      Importing...
                    </>
                  ) : (
                    <>
                      <Upload className="w-4 h-4" />
                      Import {importData.length} Records
                    </>
                  )}
                </Button>
              </div>
            </FocusModal.Footer>
          )}

        {/* Default Footer - when no data to import or there are errors */}
        {!importSuccess &&
          (importData.length === 0 || importErrors.length > 0) && (
            <FocusModal.Footer>
              <div className="flex justify-end">
                <Button variant="secondary" onClick={onClose}>
                  Close
                </Button>
              </div>
            </FocusModal.Footer>
          )}
      </FocusModal.Content>
    </FocusModal>
  );
};

export default PricingImportModal;
