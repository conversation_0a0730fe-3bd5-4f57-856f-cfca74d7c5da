import React, { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  Container,
  Heading,
  Text,
  toast,
  Toaster,
  Input,
  Label,
  Drawer,
  Select,
} from "@camped-ai/ui";
import { Save, Loader2, Calendar, Calculator, Copy, DollarSign, Percent, TrendingUp } from "lucide-react";
// Removed useAdminHotelPricing import - using direct fetch to avoid duplicate API calls
import { useAdminCurrencies } from "../../../hooks/use-admin-currencies";
import { CurrencySelector } from "../../common/currency-selector";
// Import cost-margin calculator utilities
import {
  calculateTotalFromCostMargin,
} from "../../../../modules/hotel-management/hotel-pricing/utils/cost-margin-calculator";

import { format } from "date-fns";
import BulkPriceUpdateModal from "./bulk-price-update-modal";
import HotelPricingTableSkeleton from "../../shared/hotel-pricing-table-skeleton";

// Helper function to generate a unique ID
const generateId = () => {
  return (
    Math.random().toString(36).substring(2, 15) +
    Math.random().toString(36).substring(2, 15)
  );
};

type RoomConfig = {
  id: string;
  title: string;
  handle?: string;
  description?: string;
};

type OccupancyConfig = {
  id: string;
  name: string;
  is_default?: boolean;
};

type MealPlan = {
  id: string;
  name: string;
  is_default?: boolean;
  metadata?: Record<string, any> & {
    applicable_occupancy_types?: string[] | null;
  };
};

type SeasonalPeriod = {
  id: string;
  name: string;
  start_date: string;
  end_date: string;
};

type PricingRow = {
  id: string;
  roomConfigId: string;
  occupancyTypeId: string;
  mealPlanId: string | null; // null for extra beds and cots
  seasonalPeriodId?: string; // null/undefined for base pricing
  prices: {
    mon: number;
    tue: number;
    wed: number;
    thu: number;
    fri: number;
    sat: number;
    sun: number;
  };
  // New cost and margin fields
  defaultValues: {
    grossCost: number;
    fixedMargin: number;
    marginPercentage: number;
    total: number;
  };
  modified: boolean;
};

type RoomPricingData = {
  room_config_id: string;
  room_config: RoomConfig;
  weekday_rules: any[];
  seasonal_prices: any[];
};

type ComprehensivePricingTableProps = {
  hotelId: string;
  roomConfigs: RoomConfig[];
  occupancyConfigs: OccupancyConfig[];
  mealPlans: MealPlan[];
  seasonalPeriods: SeasonalPeriod[];
  setSeasonalPeriods: React.Dispatch<React.SetStateAction<SeasonalPeriod[]>>;
  initialPrices?: Record<string, any>; // Legacy support
  roomPricingData?: RoomPricingData[]; // New comprehensive API data
  onSave?: (data: any) => void | Promise<void>;
  canEdit?: boolean;
  canCreate?: boolean;
  canDelete?: boolean;
};

const ComprehensivePricingTable: React.FC<ComprehensivePricingTableProps> = ({
  hotelId: _hotelId, // Prefix with underscore to indicate intentionally unused
  roomConfigs,
  occupancyConfigs,
  mealPlans,
  seasonalPeriods,
  setSeasonalPeriods,
  initialPrices = {},
  roomPricingData = [],
  onSave,
  canEdit = false,
  canCreate = false,
  canDelete = false,
}) => {
  const [pricingRows, setPricingRows] = useState<PricingRow[]>([]);
  const [currencyCode, setCurrencyCode] = useState("");
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [selectedRoomConfig, setSelectedRoomConfig] = useState<string>("");
  const [selectedOccupancyConfig, setSelectedOccupancyConfig] =
    useState<string>("");
  const [selectedMealPlan, setSelectedMealPlan] = useState<string>("");
  const [selectedSeasonFilter, setSelectedSeasonFilter] =
    useState<string>("all");
  const [showAllDays] = useState(true); // Always show all days for now

  const {
    currencies,
    defaultCurrency,
    isLoading: isLoadingCurrencies,
  } = useAdminCurrencies();

  // Get current currency object for proper formatting
  const currentCurrency =
    currencies.find((c: any) => c.currency_code === currencyCode) ||
    defaultCurrency;

  // Helper function to format price display
  const formatPrice = (amount: number | null | undefined): string => {
    if (!amount) return "";

    // The amount is already in display units (dollars) from the data loading
    // Return as string without forcing decimal places
    return amount.toString();
  };

  // Helper function to get currency symbol
  const getCurrencySymbol = (): string => {
    return currentCurrency?.symbol || currencyCode;
  };

  // Helper function to parse user input (display format) to component state format
  const parsePrice = (displayValue: string): number => {
    // Parse the display value and return it as-is for component state
    // The component state stores values in display format (dollars)
    return parseFloat(displayValue) || 0;
  };

  // Helper function to get available meal plans for an occupancy type
  const getAvailableMealPlans = (occupancyTypeId: string) => {
    const occupancy = occupancyConfigs.find((oc) => oc.id === occupancyTypeId);
    if (!occupancy) return mealPlans;

    // Logic for special accommodations (Extra Bed and Baby Cot)
    const isExtraBed =
      (occupancy as any).type === "EXTRA_BED" ||
      occupancy.name?.toLowerCase().includes("extra bed");
    const isCot =
      (occupancy as any).type === "COT" ||
      occupancy.name?.toLowerCase().includes("cot") ||
      occupancy.name?.toLowerCase().includes("baby cot");

    if (isExtraBed || isCot) {
      // Find the "No Meals" meal plan instead of returning a fake N/A option
      const noMealsPlan = mealPlans.find(
        (mp) =>
          mp.name?.toLowerCase().includes("no meals") ||
          mp.name?.toLowerCase().includes("none") ||
          (mp as any).type === "none"
      );

      if (noMealsPlan) {
        return [noMealsPlan];
      }

      // Fallback to N/A if no "No Meals" plan exists
      return [{ id: null, name: "No Meals" }];
    }

    // Filter meal plans based on their applicable_occupancy_types metadata
    const availableMealPlans = mealPlans.filter((mealPlan) => {
      const applicableTypes = mealPlan.metadata?.applicable_occupancy_types;

      // If no applicable types specified, meal plan is available for all occupancy types
      if (!applicableTypes || applicableTypes.length === 0) {
        return true;
      }

      // Check if this occupancy type is in the applicable types
      return applicableTypes.includes(occupancyTypeId);
    });

    // If no meal plans are available, find "No Meals" plan as fallback
    if (availableMealPlans.length === 0) {
      const noMealsPlan = mealPlans.find(
        (mp) =>
          mp.name?.toLowerCase().includes("no meals") ||
          mp.name?.toLowerCase().includes("none") ||
          (mp as any).type === "none"
      );

      if (noMealsPlan) {
        return [noMealsPlan];
      }

      return [{ id: null, name: "No Meals" }];
    }

    return availableMealPlans;
  };

  // State for seasonal period modal
  const [isSeasonalModalOpen, setIsSeasonalModalOpen] = useState(false);
  const [newSeasonName, setNewSeasonName] = useState("");

  // State for bulk price update modal
  const [isBulkUpdateModalOpen, setIsBulkUpdateModalOpen] = useState(false);

  // Initialize dates with noon time to avoid timezone issues
  const today = new Date();
  today.setHours(12, 0, 0, 0);

  const nextWeek = new Date(today);
  nextWeek.setDate(today.getDate() + 7);

  const [newSeasonStartDate, setNewSeasonStartDate] = useState<Date>(today);
  const [newSeasonEndDate, setNewSeasonEndDate] = useState<Date>(nextWeek);
  const [isAddingSeasonalPeriod, setIsAddingSeasonalPeriod] = useState(false);

  // Removed savePricing hook to avoid duplicate API calls - using direct fetch instead

  const weekdays = [
    { id: "mon", name: "Monday" },
    { id: "tue", name: "Tuesday" },
    { id: "wed", name: "Wednesday" },
    { id: "thu", name: "Thursday" },
    { id: "fri", name: "Friday" },
    { id: "sat", name: "Saturday" },
    { id: "sun", name: "Sunday" },
  ];

  // Set default currency when currencies are loaded
  useEffect(() => {
    if (defaultCurrency && !currencyCode) {
      setCurrencyCode(defaultCurrency.currency_code);
    }
  }, [defaultCurrency, currencyCode]);

  // Initialize data when components load AND currency is set
  useEffect(() => {
    if (!isLoadingCurrencies && currencyCode) {
      initializeData();
    }
  }, [
    initialPrices,
    roomPricingData,
    roomConfigs,
    occupancyConfigs,
    mealPlans,
    seasonalPeriods,
    isLoadingCurrencies,
    currencyCode, // Add currencyCode as dependency
  ]);

  const initializeData = (targetCurrency?: string) => {
    const activeCurrency = targetCurrency || currencyCode;

    // Don't initialize if no currency is set
    if (!activeCurrency) {
      return;
    }

    setIsLoading(true);

    const newRows: PricingRow[] = [];

    // For each room config, create pricing rows for all combinations
    roomConfigs.forEach((room) => {
      // Get pricing data for this room - try new structure first, then fall back to legacy
      let currencyFilteredPrices: any = {
        weekday_rules: [],
        seasonal_prices: [],
      };

      // Method 1: Try new comprehensive API structure (roomPricingData)
      if (roomPricingData && roomPricingData.length > 0) {
        const roomData = roomPricingData.find(
          (data) => data.room_config_id === room.id
        );
        if (roomData) {
          // Filter weekday rules by currency
          const weekdayRules = roomData.weekday_rules.filter(
            (rule) =>
              rule.currency_code === activeCurrency || !rule.currency_code
          );

          // Filter seasonal prices by currency
          const seasonalPrices = roomData.seasonal_prices.filter(
            (price) =>
              price.currency_code === activeCurrency || !price.currency_code
          );

          currencyFilteredPrices = {
            weekday_rules: weekdayRules,
            seasonal_prices: seasonalPrices,
          };
        }
      }

      // Method 2: Fall back to legacy initialPrices structure
      if (
        currencyFilteredPrices.weekday_rules.length === 0 &&
        initialPrices[room.id]
      ) {
        const roomPrices = initialPrices[room.id] || {};

        if (roomPrices[activeCurrency]) {
          // Multi-currency structure - look for currency-specific data
          console.log(
            `Room ${room.id} - Found ${activeCurrency} data in legacy structure:`,
            roomPrices[activeCurrency]
          );
          currencyFilteredPrices = roomPrices[activeCurrency];
        } else if (roomPrices.currency_code === activeCurrency) {
          // Legacy single currency structure - direct match
          console.log(
            `Room ${room.id} - Using legacy single currency data for ${activeCurrency}`
          );
          currencyFilteredPrices = roomPrices;
        }
      }

      // Create base pricing rows (no seasonal period)
      occupancyConfigs.forEach((occupancy) => {
        // Get available meal plans for this occupancy type
        const mealPlansToProcess = getAvailableMealPlans(occupancy.id);

        mealPlansToProcess.forEach((mealPlan) => {
          // Check if we have a price for this combination
          const existingRule = currencyFilteredPrices.weekday_rules?.find(
            (rule: any) =>
              rule.occupancy_type_id === occupancy.id &&
              (mealPlan.id === null
                ? !rule.meal_plan_id || rule.meal_plan_id === null
                : rule.meal_plan_id === mealPlan.id)
          );

          // Create pricing row with weekday prices
          newRows.push({
            id: existingRule?.id || generateId(),
            roomConfigId: room.id,
            occupancyTypeId: occupancy.id,
            mealPlanId: mealPlan.id,
            seasonalPeriodId: undefined, // Base pricing
            prices: {
              mon: existingRule
                ? (existingRule.weekday_prices?.mon || 0) / 100
                : 0,
              tue: existingRule
                ? (existingRule.weekday_prices?.tue || 0) / 100
                : 0,
              wed: existingRule
                ? (existingRule.weekday_prices?.wed || 0) / 100
                : 0,
              thu: existingRule
                ? (existingRule.weekday_prices?.thu || 0) / 100
                : 0,
              fri: existingRule
                ? (existingRule.weekday_prices?.fri || 0) / 100
                : 0,
              sat: existingRule
                ? (existingRule.weekday_prices?.sat || 0) / 100
                : 0,
              sun: existingRule
                ? (existingRule.weekday_prices?.sun || 0) / 100
                : 0,
            },
            // Initialize default cost and margin values
            defaultValues: {
              grossCost: existingRule?.default_values?.gross_cost ? (existingRule.default_values.gross_cost / 100) : 0,
              fixedMargin: existingRule?.default_values?.fixed_margin ? (existingRule.default_values.fixed_margin / 100) : 0,
              marginPercentage: existingRule?.default_values?.margin_percentage || 0,
              total: existingRule?.default_values?.total ? (existingRule.default_values.total / 100) : 0,
            },
            modified: false,
          });

          // Create seasonal pricing rows
          seasonalPeriods.forEach((season) => {
            // Check if we have seasonal prices for this combination
            const seasonalPrices = currencyFilteredPrices.seasonal_prices || [];
            const existingSeasonalPrice = seasonalPrices.find(
              (sp: any) => sp.id === season.id || sp.name === season.name
            );

            const existingSeasonalRule =
              existingSeasonalPrice?.weekday_rules?.find(
                (rule: any) =>
                  rule.occupancy_type_id === occupancy.id &&
                  (mealPlan.id === null
                    ? !rule.meal_plan_id || rule.meal_plan_id === null
                    : rule.meal_plan_id === mealPlan.id)
              );

            // Create pricing row with weekday prices for this season
            newRows.push({
              id: existingSeasonalRule?.id || generateId(),
              roomConfigId: room.id,
              occupancyTypeId: occupancy.id,
              mealPlanId: mealPlan.id,
              seasonalPeriodId: season.id,
              prices: {
                mon: existingSeasonalRule
                  ? (existingSeasonalRule.weekday_prices?.mon || 0) / 100
                  : 0,
                tue: existingSeasonalRule
                  ? (existingSeasonalRule.weekday_prices?.tue || 0) / 100
                  : 0,
                wed: existingSeasonalRule
                  ? (existingSeasonalRule.weekday_prices?.wed || 0) / 100
                  : 0,
                thu: existingSeasonalRule
                  ? (existingSeasonalRule.weekday_prices?.thu || 0) / 100
                  : 0,
                fri: existingSeasonalRule
                  ? (existingSeasonalRule.weekday_prices?.fri || 0) / 100
                  : 0,
                sat: existingSeasonalRule
                  ? (existingSeasonalRule.weekday_prices?.sat || 0) / 100
                  : 0,
                sun: existingSeasonalRule
                  ? (existingSeasonalRule.weekday_prices?.sun || 0) / 100
                  : 0,
              },
              // Initialize default cost and margin values for seasonal pricing
              defaultValues: {
                grossCost: 0,
                fixedMargin: 0,
                marginPercentage: 0,
                total: 0,
              },
              modified: false,
            });
          });
        });
      });
    });

    setPricingRows(newRows);
    setIsLoading(false);
  };

  const handlePriceChange = (
    roomConfigId: string,
    occupancyTypeId: string,
    mealPlanId: string | null,
    seasonalPeriodId: string | undefined,
    day: string,
    value: number
  ) => {
    setPricingRows((prev) =>
      prev.map((row) =>
        row.roomConfigId === roomConfigId &&
        row.occupancyTypeId === occupancyTypeId &&
        row.mealPlanId === mealPlanId &&
        row.seasonalPeriodId === seasonalPeriodId
          ? {
              ...row,
              prices: { ...row.prices, [day]: value },
              modified: true,
            }
          : row
      )
    );
  };

  const handleCurrencyChange = (newCurrencyCode: string) => {
    console.log(
      `Currency change requested: ${currencyCode} → ${newCurrencyCode}`
    );
    setCurrencyCode(newCurrencyCode);
    // Re-initialize data with the new currency to filter pricing data
    // Pass the new currency directly to avoid state timing issues
    initializeData(newCurrencyCode);
  };

  const handleCopyBaseToSeasonal = (
    roomConfigId: string,
    occupancyTypeId: string,
    mealPlanId: string | null
  ) => {
    // Find the base pricing row
    const basePricingRow = pricingRows.find(
      (row) =>
        row.roomConfigId === roomConfigId &&
        row.occupancyTypeId === occupancyTypeId &&
        row.mealPlanId === mealPlanId &&
        !row.seasonalPeriodId
    );

    if (!basePricingRow) return;

    // Update all seasonal pricing rows for this combination
    setPricingRows((prev) =>
      prev.map((row) =>
        row.roomConfigId === roomConfigId &&
        row.occupancyTypeId === occupancyTypeId &&
        row.mealPlanId === mealPlanId &&
        row.seasonalPeriodId // Only seasonal rows
          ? {
              ...row,
              prices: { ...basePricingRow.prices },
              modified: true,
            }
          : row
      )
    );

    toast.success("Success", {
      description: "Base prices copied to all seasonal periods",
    });
  };

  const handleCopyToAllDays = (
    roomConfigId: string,
    occupancyTypeId: string,
    mealPlanId: string | null,
    seasonalPeriodId: string | undefined,
    sourceDay: string
  ) => {
    // Find the pricing row
    const pricingRow = pricingRows.find(
      (row) =>
        row.roomConfigId === roomConfigId &&
        row.occupancyTypeId === occupancyTypeId &&
        row.mealPlanId === mealPlanId &&
        row.seasonalPeriodId === seasonalPeriodId
    );

    if (!pricingRow) return;

    // Get the price from the source day
    const sourcePrice =
      pricingRow.prices[sourceDay as keyof typeof pricingRow.prices];

    // Create new prices with the same value for all days
    const newPrices = {
      mon: sourcePrice,
      tue: sourcePrice,
      wed: sourcePrice,
      thu: sourcePrice,
      fri: sourcePrice,
      sat: sourcePrice,
      sun: sourcePrice,
    };

    // Update the pricing row
    setPricingRows((prev) =>
      prev.map((row) =>
        row.roomConfigId === roomConfigId &&
        row.occupancyTypeId === occupancyTypeId &&
        row.mealPlanId === mealPlanId &&
        row.seasonalPeriodId === seasonalPeriodId
          ? {
              ...row,
              prices: newPrices,
              modified: true,
            }
          : row
      )
    );

    toast.success("Success", {
      description: `${sourceDay.toUpperCase()} price copied to all days`,
    });
  };

  const handleBulkPriceUpdate = (updatedRows: PricingRow[]) => {
    setPricingRows(updatedRows);
    toast.success("Success", {
      description: "Bulk price update applied successfully",
    });
  };

  // Handle changes to default cost and margin values
  const handleDefaultValueChange = (
    roomConfigId: string,
    occupancyTypeId: string,
    mealPlanId: string | null,
    seasonalPeriodId: string | undefined,
    field: 'grossCost' | 'fixedMargin' | 'marginPercentage',
    value: number
  ) => {
    setPricingRows((prev) =>
      prev.map((row) => {
        if (
          row.roomConfigId === roomConfigId &&
          row.occupancyTypeId === occupancyTypeId &&
          row.mealPlanId === mealPlanId &&
          row.seasonalPeriodId === seasonalPeriodId
        ) {
          const updatedDefaultValues = {
            ...row.defaultValues,
            [field]: value,
          };

          // Calculate the new total using the cost-margin calculator
          // Convert display format (CHF) back to cents for calculation
          const totalInCents = calculateTotalFromCostMargin({
            gross_cost: Math.round(updatedDefaultValues.grossCost * 100), // Convert CHF to cents
            fixed_margin: Math.round(updatedDefaultValues.fixedMargin * 100), // Convert CHF to cents
            margin_percentage: updatedDefaultValues.marginPercentage, // Percentage stays as-is
          });

          // Convert result back to display format (CHF)
          const total = totalInCents ? (totalInCents / 100) : 0;

          return {
            ...row,
            defaultValues: {
              ...updatedDefaultValues,
              total,
            },
            modified: true,
          };
        }
        return row;
      })
    );
  };

  // Handle applying default values to all days of the week (LOCAL CALCULATION ONLY)
  const handleApplyToAllDays = (
    roomConfigId: string,
    occupancyTypeId: string,
    mealPlanId: string | null,
    seasonalPeriodId: string | undefined
  ) => {
    // Find the pricing row
    const pricingRow = pricingRows.find(
      (row) =>
        row.roomConfigId === roomConfigId &&
        row.occupancyTypeId === occupancyTypeId &&
        row.mealPlanId === mealPlanId &&
        row.seasonalPeriodId === seasonalPeriodId
    );

    if (!pricingRow) {
      toast.error("Error", {
        description: "Pricing row not found",
      });
      return;
    }

    // Check if we have valid cost/margin data to calculate from
    if (!pricingRow.defaultValues.grossCost || pricingRow.defaultValues.grossCost <= 0) {
      toast.error("Error", {
        description: "Please set a valid gross cost value first",
      });
      return;
    }

    // Calculate the total price using the cost-margin calculator
    const totalInCents = calculateTotalFromCostMargin({
      gross_cost: Math.round(pricingRow.defaultValues.grossCost * 100), // Convert CHF to cents
      fixed_margin: Math.round(pricingRow.defaultValues.fixedMargin * 100), // Convert CHF to cents
      margin_percentage: pricingRow.defaultValues.marginPercentage, // Percentage stays as-is
    });

    if (!totalInCents) {
      toast.error("Error", {
        description: "Unable to calculate total price from cost and margin values",
      });
      return;
    }

    // Convert result back to display format (CHF)
    const calculatedPrice = totalInCents / 100;

    console.log(`🔄 Applying calculated price ${calculatedPrice} CHF to all weekdays for pricing row (LOCAL ONLY)`);

    // Update the local state to populate all weekday fields with the calculated total
    setPricingRows((prev) =>
      prev.map((row) => {
        if (
          row.roomConfigId === roomConfigId &&
          row.occupancyTypeId === occupancyTypeId &&
          row.mealPlanId === mealPlanId &&
          row.seasonalPeriodId === seasonalPeriodId
        ) {
          return {
            ...row,
            weekdayPrices: {
              mon: calculatedPrice,
              tue: calculatedPrice,
              wed: calculatedPrice,
              thu: calculatedPrice,
              fri: calculatedPrice,
              sat: calculatedPrice,
              sun: calculatedPrice,
            },
            modified: true,
          };
        }
        return row;
      })
    );

    // Show success message
    toast.success("Applied cost and margin values to all days for this pricing rule", {
      description: `Set all weekday prices to ${calculatedPrice.toFixed(2)} CHF`,
    });
  };

  const handleAddSeasonalPeriod = () => {
    // Validate inputs
    if (!newSeasonName.trim()) {
      toast.error("Error", {
        description: "Please enter a season name",
      });
      return;
    }

    // Make sure we have valid dates
    let startDate = newSeasonStartDate;
    let endDate = newSeasonEndDate;

    if (!startDate) {
      startDate = new Date();
    }

    if (!endDate) {
      endDate = new Date();
      // Set to 7 days after start date by default
      endDate.setDate(startDate.getDate() + 7);
    }

    if (startDate > endDate) {
      toast.error("Error", {
        description: "End date must be after start date",
      });
      return;
    }

    setIsAddingSeasonalPeriod(true);

    try {
      // Format dates safely
      const formattedStartDate = format(startDate, "yyyy-MM-dd");
      const formattedEndDate = format(endDate, "yyyy-MM-dd");

      // Check if season already exists
      const existingSeasonalPeriod = seasonalPeriods.find(
        (period) => period.name.toLowerCase() === newSeasonName.trim().toLowerCase()
      );

      if (existingSeasonalPeriod) {
        toast.error("Error", {
          description: "A season with this name already exists",
        });
        return;
      }

      // Create new seasonal period
      const newSeasonalPeriod: SeasonalPeriod = {
        id: generateId(),
        name: newSeasonName.trim(),
        start_date: formattedStartDate,
        end_date: formattedEndDate,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      };

      // Add to seasonal periods list
      setSeasonalPeriods((prev) => [...prev, newSeasonalPeriod]);

      // Create new pricing rows for this seasonal period
      const newRows: PricingRow[] = [];

      roomConfigs.forEach((roomConfig) => {
        occupancyConfigs.forEach((occupancyConfig) => {
          // Add row without meal plan
          newRows.push({
            id: generateId(),
            roomConfigId: roomConfig.id,
            occupancyTypeId: occupancyConfig.id,
            mealPlanId: null,
            seasonalPeriodId: newSeasonalPeriod.id,
            weekdayPrices: {
              mon: 0,
              tue: 0,
              wed: 0,
              thu: 0,
              fri: 0,
              sat: 0,
              sun: 0,
            },
            defaultValues: {
              grossCost: 0,
              fixedMargin: 0,
              marginPercentage: 0,
              total: 0,
            },
            modified: false,
          });

          // Add rows for each meal plan
          mealPlans.forEach((mealPlan) => {
            newRows.push({
              id: generateId(),
              roomConfigId: roomConfig.id,
              occupancyTypeId: occupancyConfig.id,
              mealPlanId: mealPlan.id,
              seasonalPeriodId: newSeasonalPeriod.id,
              weekdayPrices: {
                mon: 0,
                tue: 0,
                wed: 0,
                thu: 0,
                fri: 0,
                sat: 0,
                sun: 0,
              },
              defaultValues: {
                grossCost: 0,
                fixedMargin: 0,
                marginPercentage: 0,
                total: 0,
              },
              modified: false,
            });
          });
        });
      });

      // Update pricing rows state
      setPricingRows((prev) => [...prev, ...newRows]);

      // Close the modal and reset form
      setIsSeasonalModalOpen(false);
      setNewSeasonName("");
      setNewSeasonStartDate(new Date());
      setNewSeasonEndDate(new Date());

      toast.success("Success", {
        description: "Seasonal period added successfully",
      });
    } catch (error) {
      console.error("Error adding seasonal period:", error);
      toast.error("Error", {
        description:
          "Failed to add seasonal period: " +
          (error instanceof Error ? error.message : String(error)),
      });
    } finally {
      setIsAddingSeasonalPeriod(false);
    }
  };

          console.log(`� Found associated base pricing rule: ${associatedBasePriceRule.id}`);

          // First, update the base price rule's cost/margin data
          const basePriceResponse = await fetch(
            `/admin/hotel-management/room-configs/${roomConfigId}/pricing/bulk-populate`,
            {
              method: "POST",
              headers: {
                "Content-Type": "application/json",
              },
              body: JSON.stringify({
                rule_ids: [associatedBasePriceRule.id],
                apply_gross_cost: true,
                apply_fixed_margin: true,
                apply_margin_percentage: true,
                // Use the current default values from the seasonal row
                override_defaults: {
                  gross_cost: Math.round(pricingRow.defaultValues.grossCost * 100), // Convert CHF to cents
                  fixed_margin: Math.round(pricingRow.defaultValues.fixedMargin * 100), // Convert CHF to cents
                  margin_percentage: pricingRow.defaultValues.marginPercentage, // Percentage stays as-is
                },
              }),
            }
          );

          if (!basePriceResponse.ok) {
            const errorData = await basePriceResponse.json();
            throw new Error(errorData.message || "Failed to save cost and margin data to base pricing rule");
          }

          const basePriceResult = await basePriceResponse.json();
          console.log(`✅ Base pricing rule updated for seasonal rule:`, basePriceResult);

          // Now update the seasonal rule's weekday prices
          const calculatedPrice = pricingRow.defaultValues.total;
          const weekdayPricesUpdate = {
            mon: calculatedPrice,
            tue: calculatedPrice,
            wed: calculatedPrice,
            thu: calculatedPrice,
            fri: calculatedPrice,
            sat: calculatedPrice,
            sun: calculatedPrice,
          };

          const seasonalUpdateResponse = await fetch(
            `/admin/hotel-management/seasonal-pricing/${pricingRow.id}/update-weekday-prices`,
            {
              method: "PUT",
              headers: {
                "Content-Type": "application/json",
              },
              body: JSON.stringify({
                weekday_prices: weekdayPricesUpdate,
              }),
            }
          );

          if (!seasonalUpdateResponse.ok) {
            const errorData = await seasonalUpdateResponse.json();
            throw new Error(errorData.message || "Failed to update seasonal rule weekday prices");
          }

          const seasonalUpdateResult = await seasonalUpdateResponse.json();
          console.log(`✅ Seasonal rule weekday prices updated:`, seasonalUpdateResult);
        }
      } else {
        console.log(`⚠️ New rule detected (not yet saved): ${pricingRow.id} - applying locally only`);
        toast.info("Note", {
          description: "This is a new pricing rule. Values applied locally - remember to save the pricing table to persist changes.",
        });
      }

      // Update the local state to populate all weekday fields with the calculated total
      const calculatedPrice = pricingRow.defaultValues.total;

      setPricingRows((prev) =>
        prev.map((row) => {
          if (
            row.roomConfigId === roomConfigId &&
            row.occupancyTypeId === occupancyTypeId &&
            row.mealPlanId === mealPlanId &&
            row.seasonalPeriodId === seasonalPeriodId
          ) {
            return {
              ...row,
              prices: {
                mon: calculatedPrice,
                tue: calculatedPrice,
                wed: calculatedPrice,
                thu: calculatedPrice,
                fri: calculatedPrice,
                sat: calculatedPrice,
                sun: calculatedPrice,
              },
              modified: true,
            };
          }
          return row;
        })
      );

      toast.success("Success", {
        description: `Applied cost and margin values to all days for this pricing rule`,
      });

    } catch (error) {
      console.error("❌ Error applying cost and margin values:", error);
      toast.error("Error", {
        description: error instanceof Error ? error.message : "Failed to apply cost and margin values",
      });
    }
  };

  const handleAddSeasonalPeriod = () => {
    // Validate inputs
    if (!newSeasonName.trim()) {
      toast.error("Error", {
        description: "Please enter a season name",
      });
      return;
    }

    // Make sure we have valid dates
    let startDate = newSeasonStartDate;
    let endDate = newSeasonEndDate;

    if (!startDate) {
      startDate = new Date();
    }

    if (!endDate) {
      endDate = new Date();
      // Set to 7 days after start date by default
      endDate.setDate(startDate.getDate() + 7);
    }

    if (startDate > endDate) {
      toast.error("Error", {
        description: "End date must be after start date",
      });
      return;
    }

    setIsAddingSeasonalPeriod(true);

    try {
      // Format dates safely
      const formatDate = (date: Date) => {
        try {
          return format(date, "yyyy-MM-dd");
        } catch (e) {
          console.error("Date formatting error:", e);
          // Fallback to ISO string and extract the date part
          return date.toISOString().split("T")[0];
        }
      };

      // Create a new seasonal period
      const newSeasonalPeriod: SeasonalPeriod = {
        id: generateId(),
        name: newSeasonName,
        start_date: formatDate(startDate),
        end_date: formatDate(endDate),
      };

      // Add the new seasonal period to the list
      setSeasonalPeriods((prevSeasons) => [...prevSeasons, newSeasonalPeriod]);

      // Create pricing rows for the new seasonal period
      const newRows: PricingRow[] = [];

      // For each room config, create pricing rows for all occupancy and meal plan combinations
      roomConfigs.forEach((room) => {
        occupancyConfigs.forEach((occupancy) => {
          // Get available meal plans for this occupancy type
          const mealPlansToProcess = getAvailableMealPlans(occupancy.id);

          mealPlansToProcess.forEach((mealPlan) => {
            // Find the base pricing row for this combination
            const basePricingRow = pricingRows.find(
              (row) =>
                row.roomConfigId === room.id &&
                row.occupancyTypeId === occupancy.id &&
                row.mealPlanId === mealPlan.id &&
                !row.seasonalPeriodId
            );

            // Default prices if no base pricing row is found
            const defaultPrices = {
              mon: 0,
              tue: 0,
              wed: 0,
              thu: 0,
              fri: 0,
              sat: 0,
              sun: 0,
            };

            // Create a new pricing row for this combination with the new seasonal period
            newRows.push({
              id: generateId(),
              roomConfigId: room.id,
              occupancyTypeId: occupancy.id,
              mealPlanId: mealPlan.id,
              seasonalPeriodId: newSeasonalPeriod.id,
              prices: basePricingRow
                ? { ...basePricingRow.prices }
                : defaultPrices,
              // Copy default values from base pricing row if available
              defaultValues: basePricingRow
                ? { ...basePricingRow.defaultValues }
                : {
                    grossCost: 0,
                    fixedMargin: 0,
                    marginPercentage: 0,
                    total: 0,
                  },
              modified: true, // Mark as modified so it gets saved
            });
          });
        });
      });

      // Update pricing rows state
      setPricingRows((prev) => [...prev, ...newRows]);

      // Close the modal and reset form
      setIsSeasonalModalOpen(false);
      setNewSeasonName("");
      setNewSeasonStartDate(new Date());
      setNewSeasonEndDate(new Date());

      toast.success("Success", {
        description: "Seasonal period added successfully",
      });
    } catch (error) {
      console.error("Error adding seasonal period:", error);
      toast.error("Error", {
        description:
          "Failed to add seasonal period: " +
          (error instanceof Error ? error.message : String(error)),
      });
    } finally {
      setIsAddingSeasonalPeriod(false);
    }
  };

  const handleSaveAll = async () => {
    setIsSaving(true);

    try {
      console.log("=== SAVE ALL DEBUG START ===");

      // Group pricing rows by room config and seasonal period
      const roomConfigPrices: Record<string, any> = {};

      // Only initialize room config prices for rooms that have modified data
      // Don't create empty pricing records for all rooms

      // Process base pricing rows
      const basePricingRows = pricingRows.filter(
        (row) => !row.seasonalPeriodId
      );
      basePricingRows.forEach((row) => {
        // Only include rows that have been modified
        if (row.modified) {
          console.log(
            `Saving modified base pricing for room ${row.roomConfigId}, currency ${currencyCode}`
          );

          // Initialize room config pricing if not exists
          if (!roomConfigPrices[row.roomConfigId]) {
            roomConfigPrices[row.roomConfigId] = {
              currency_code: currencyCode,
              weekday_rules: [],
              seasonal_prices: [],
            };
          }

          // Create cost/margin data for each day (convert from display format to smallest units)
          const costMarginData = {
            mon: {
              gross_cost: Math.round(
                row.defaultValues.grossCost *
                  Math.pow(10, currentCurrency?.decimal_digits || 2)
              ),
              fixed_margin: Math.round(
                row.defaultValues.fixedMargin *
                  Math.pow(10, currentCurrency?.decimal_digits || 2)
              ),
              margin_percentage: row.defaultValues.marginPercentage,
            },
            tue: {
              gross_cost: Math.round(
                row.defaultValues.grossCost *
                  Math.pow(10, currentCurrency?.decimal_digits || 2)
              ),
              fixed_margin: Math.round(
                row.defaultValues.fixedMargin *
                  Math.pow(10, currentCurrency?.decimal_digits || 2)
              ),
              margin_percentage: row.defaultValues.marginPercentage,
            },
            wed: {
              gross_cost: Math.round(
                row.defaultValues.grossCost *
                  Math.pow(10, currentCurrency?.decimal_digits || 2)
              ),
              fixed_margin: Math.round(
                row.defaultValues.fixedMargin *
                  Math.pow(10, currentCurrency?.decimal_digits || 2)
              ),
              margin_percentage: row.defaultValues.marginPercentage,
            },
            thu: {
              gross_cost: Math.round(
                row.defaultValues.grossCost *
                  Math.pow(10, currentCurrency?.decimal_digits || 2)
              ),
              fixed_margin: Math.round(
                row.defaultValues.fixedMargin *
                  Math.pow(10, currentCurrency?.decimal_digits || 2)
              ),
              margin_percentage: row.defaultValues.marginPercentage,
            },
            fri: {
              gross_cost: Math.round(
                row.defaultValues.grossCost *
                  Math.pow(10, currentCurrency?.decimal_digits || 2)
              ),
              fixed_margin: Math.round(
                row.defaultValues.fixedMargin *
                  Math.pow(10, currentCurrency?.decimal_digits || 2)
              ),
              margin_percentage: row.defaultValues.marginPercentage,
            },
            sat: {
              gross_cost: Math.round(
                row.defaultValues.grossCost *
                  Math.pow(10, currentCurrency?.decimal_digits || 2)
              ),
              fixed_margin: Math.round(
                row.defaultValues.fixedMargin *
                  Math.pow(10, currentCurrency?.decimal_digits || 2)
              ),
              margin_percentage: row.defaultValues.marginPercentage,
            },
            sun: {
              gross_cost: Math.round(
                row.defaultValues.grossCost *
                  Math.pow(10, currentCurrency?.decimal_digits || 2)
              ),
              fixed_margin: Math.round(
                row.defaultValues.fixedMargin *
                  Math.pow(10, currentCurrency?.decimal_digits || 2)
              ),
              margin_percentage: row.defaultValues.marginPercentage,
            },
          };

          // Create default values (convert from display format to smallest units)
          const defaultValues = {
            gross_cost: Math.round(
              row.defaultValues.grossCost *
                Math.pow(10, currentCurrency?.decimal_digits || 2)
            ),
            fixed_margin: Math.round(
              row.defaultValues.fixedMargin *
                Math.pow(10, currentCurrency?.decimal_digits || 2)
            ),
            margin_percentage: row.defaultValues.marginPercentage,
            total: Math.round(
              row.defaultValues.total *
                Math.pow(10, currentCurrency?.decimal_digits || 2)
            ),
          };

          console.log(`[FRONTEND] Sending cost_margin_data for room ${row.roomConfigId}:`, {
            original: row.defaultValues,
            converted: costMarginData
          });

          // FIXED: Store default_values at room config level, not in each weekday_rule
          if (!roomConfigPrices[row.roomConfigId].default_values) {
            roomConfigPrices[row.roomConfigId].default_values = defaultValues;
          }

          roomConfigPrices[row.roomConfigId].weekday_rules.push({
            occupancy_type_id: row.occupancyTypeId,
            meal_plan_id: row.mealPlanId, // This will be null for extra beds
            cost_margin_data: costMarginData, // FIXED: Send cost/margin data instead of weekday_prices
          });
        }
      });

      // Process seasonal pricing rows - UPDATE existing seasonal pricing rules
      const seasonalPricingRows = pricingRows.filter(
        (row) => row.seasonalPeriodId && row.modified
      );

      console.log(`=== SEASONAL PRICING: ${seasonalPricingRows.length} seasonal pricing rules to update ===`);

      // Update each seasonal pricing rule individually
      const seasonalPricingPromises = seasonalPricingRows.map(async (row) => {
        console.log(`🌟 SEASONAL API CALL: Updating seasonal pricing rule for room ${row.roomConfigId}, occupancy ${row.occupancyTypeId}, meal plan ${row.mealPlanId}`);

        // Convert weekday prices from display format (CHF) to smallest units (cents)
        const weekdayPricesInCents = {
          mon: Math.round(row.prices.mon * Math.pow(10, currentCurrency?.decimal_digits || 2)),
          tue: Math.round(row.prices.tue * Math.pow(10, currentCurrency?.decimal_digits || 2)),
          wed: Math.round(row.prices.wed * Math.pow(10, currentCurrency?.decimal_digits || 2)),
          thu: Math.round(row.prices.thu * Math.pow(10, currentCurrency?.decimal_digits || 2)),
          fri: Math.round(row.prices.fri * Math.pow(10, currentCurrency?.decimal_digits || 2)),
          sat: Math.round(row.prices.sat * Math.pow(10, currentCurrency?.decimal_digits || 2)),
          sun: Math.round(row.prices.sun * Math.pow(10, currentCurrency?.decimal_digits || 2)),
        };

        console.log(`Updating seasonal rule ${row.id} with weekday prices:`, weekdayPricesInCents);

        const response = await fetch(
          `/admin/hotel-management/seasonal-pricing/${row.id}/update-weekday-prices`,
          {
            method: "PUT",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify({
              weekday_prices: weekdayPricesInCents,
            }),
          }
        );

        if (!response.ok) {
          const errorText = await response.text();
          console.error(`Failed to update seasonal pricing rule ${row.id}: ${errorText}`);
          throw new Error(`Failed to update seasonal pricing rule: ${errorText}`);
        }

        const result = await response.json();
        console.log(`✅ Successfully updated seasonal pricing rule ${row.id}`);
        return result;
      });

      // Wait for all seasonal pricing rules to be updated
      const seasonalResults = await Promise.all(seasonalPricingPromises);

      // Save base pricing for each room config
      // FIXED: Only save room configs that actually have base pricing changes
      // Now that seasonal pricing is handled separately, roomConfigPrices should
      // only contain room configs with actual base pricing modifications
      console.log(`=== BASE PRICING: ${Object.keys(roomConfigPrices).length} room configs to process ===`);
      const savePromises = Object.entries(roomConfigPrices).map(
        async ([roomConfigId, data]) => {
          // Only save if there are weekday rules (base pricing)
          if (data.weekday_rules.length > 0) {
            console.log(`💰 BASE API CALL: Saving base pricing for room config ${roomConfigId} with ${data.weekday_rules.length} rules`);

            // FIXED: Use direct fetch instead of savePricing hook to avoid duplicate seasonal processing
            // The savePricing hook automatically processes seasonal_prices which we handle separately
            const response = await fetch(
              `/admin/hotel-management/room-configs/${roomConfigId}/weekday-pricing/bulk`,
              {
                method: "POST",
                headers: {
                  "Content-Type": "application/json",
                },
                body: JSON.stringify({
                  currency_code: data.currency_code,
                  weekday_rules: data.weekday_rules,
                }),
              }
            );

            if (!response.ok) {
              const errorText = await response.text();
              console.error(`Failed to save base pricing for room ${roomConfigId}: ${errorText}`);
              throw new Error(`Failed to save base pricing: ${errorText}`);
            }

            return await response.json();
          } else {
            console.log(`⏭️  SKIPPING: Room config ${roomConfigId} has no base pricing changes`);
          }
          return null;
        }
      );

      const baseResults = await Promise.all(savePromises);
      const results = [...baseResults, ...seasonalResults];

      console.log(`=== SAVE ALL DEBUG END: ${seasonalResults.length} seasonal + ${baseResults.filter(r => r !== null).length} base = ${results.filter(r => r !== null).length} total API calls ===`);

      // Call onSave callback if provided and wait for it to complete
      if (onSave) {
        await onSave({
          results,
          seasonal_periods: seasonalPeriods,
        });
      }

      // Reset modified flags AFTER the data has been refreshed
      setPricingRows((prev) =>
        prev.map((row) => ({ ...row, modified: false }))
      );

      // Don't show success toast here - let the parent component handle it
      // The parent component will show the success message after refetching data
    } catch (error) {
      console.error("Error saving pricing:", error);
      toast.error("Error", {
        description: "Failed to save pricing",
      });
    } finally {
      setIsSaving(false);
    }
  };

  // Filter pricing rows based on selected filters
  const filteredPricingRows = pricingRows.filter((row) => {
    // Filter by room config if selected
    if (selectedRoomConfig && row.roomConfigId !== selectedRoomConfig) {
      return false;
    }

    // Filter by occupancy config if selected
    if (
      selectedOccupancyConfig &&
      row.occupancyTypeId !== selectedOccupancyConfig
    ) {
      return false;
    }

    // Filter by meal plan if selected
    if (selectedMealPlan && row.mealPlanId !== selectedMealPlan) {
      return false;
    }

    // Filter by season based on selected season filter
    if (selectedSeasonFilter === "base" && row.seasonalPeriodId) {
      return false; // Hide seasonal rows when showing base only
    }
    if (selectedSeasonFilter !== "all" && selectedSeasonFilter !== "base") {
      // Show only specific season
      if (row.seasonalPeriodId !== selectedSeasonFilter) {
        return false;
      }
    }

    return true;
  });

  if (isLoading) {
    return <HotelPricingTableSkeleton />;
  }

  // Reusable select component using @camped-ai/ui Select
  const FilterSelect = ({
    id,
    label,
    value,
    onChange,
    options,
    placeholder = "All",
  }: {
    id: string;
    label: string;
    value: string;
    onChange: (value: string) => void;
    options: Array<{ id: string; name?: string; title?: string }>;
    placeholder?: string;
  }) => (
    <div>
      <Label htmlFor={id} className="mb-1 block text-sm font-medium">
        {label}
      </Label>
      <Select
        value={value || "all"}
        onValueChange={(val) => onChange(val === "all" ? "" : val)}
      >
        <Select.Trigger id={id} className="w-full">
          <Select.Value placeholder={placeholder} />
        </Select.Trigger>
        <Select.Content>
          <Select.Item value="all">{placeholder}</Select.Item>
          {options.map((option) => (
            <Select.Item key={option.id} value={option.id}>
              {option.title || option.name}
            </Select.Item>
          ))}
        </Select.Content>
      </Select>
    </div>
  );

  console.log({ canCreate });

  return (
    <Container>
      <Toaster />

      {/* Compact Header with Controls */}
      <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4 mb-6">
        <div>
          <Heading level="h2" className="mb-1">
            Hotel Pricing Management
          </Heading>
          <Text className=" text-sm">
            Manage base rates, seasonal pricing, and special offers for your
            hotel rooms
          </Text>
        </div>

        <div className="flex flex-wrap items-center gap-2">
          {currencies.length > 1 && (
            <CurrencySelector
              value={currencyCode}
              onChange={handleCurrencyChange}
              label="Currency"
              id="currency"
            />
          )}

          {canEdit && (
            <Button
              variant="secondary"
              onClick={() => setIsBulkUpdateModalOpen(true)}
              className="flex items-center gap-2 h-9 text-sm"
              title="Update multiple prices at once"
            >
              <Calculator className="w-4 h-4" />
              Bulk Update
            </Button>
          )}

          {canCreate && (
            <Button
              variant="primary"
              onClick={() => setIsSeasonalModalOpen(true)}
              className="flex items-center gap-2 h-9 text-sm"
            >
              <Calendar className="w-4 h-4" />
              Add Season
            </Button>
          )}

          {canEdit && (
            <Button
              variant="primary"
              onClick={handleSaveAll}
              className="flex items-center gap-2 h-9 text-sm"
              disabled={isSaving}
            >
              {isSaving ? (
                <>
                  <Loader2 className="w-4 h-4 animate-spin" />
                  Saving...
                </>
              ) : (
                <>
                  <Save className="w-4 h-4" />
                  Save All
                </>
              )}
            </Button>
          )}
        </div>
      </div>

      {/* Seasonal Period Modal */}
      <Drawer
        open={isSeasonalModalOpen}
        onOpenChange={(open) => {
          setIsSeasonalModalOpen(open);
          if (!open) {
            // Reset form when closing
            setNewSeasonName("");

            const resetToday = new Date();
            resetToday.setHours(12, 0, 0, 0);

            const resetNextWeek = new Date(resetToday);
            resetNextWeek.setDate(resetToday.getDate() + 7);

            setNewSeasonStartDate(resetToday);
            setNewSeasonEndDate(resetNextWeek);
          }
        }}
      >
        <Drawer.Content className="flex flex-col">
          <Drawer.Header className="flex-shrink-0">
            <Heading level="h2" className="text-l font-semibold">
              Add Seasonal Period
            </Heading>
          </Drawer.Header>
          <Drawer.Body className="flex-1 overflow-y-auto">
            <div className=" space-y-6">
              <div>
                <Label htmlFor="seasonName" className="mb-1 block">
                  Season Name
                </Label>
                <Input
                  id="seasonName"
                  value={newSeasonName}
                  onChange={(e) => setNewSeasonName(e.target.value)}
                  placeholder="e.g., Summer 2023"
                  className="w-full"
                  required
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="startDate" className="mb-1 block">
                    Start Date
                  </Label>
                  <div className="relative">
                    <Input
                      id="startDate"
                      type="date"
                      value={
                        newSeasonStartDate
                          ? format(newSeasonStartDate, "yyyy-MM-dd")
                          : ""
                      }
                      onChange={(e) => {
                        if (e.target.value) {
                          // Create date at noon to avoid timezone issues
                          const date = new Date(e.target.value + "T12:00:00");
                          setNewSeasonStartDate(date);
                        } else {
                          setNewSeasonStartDate(new Date());
                        }
                      }}
                      className="w-full"
                      required
                    />
                  </div>
                </div>

                <div>
                  <Label htmlFor="endDate" className="mb-1 block">
                    End Date
                  </Label>
                  <div className="relative">
                    <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                      <Calendar className="w-4 h-4 text-muted-foreground" />
                    </div>
                    <Input
                      id="endDate"
                      type="date"
                      value={
                        newSeasonEndDate
                          ? format(newSeasonEndDate, "yyyy-MM-dd")
                          : ""
                      }
                      onChange={(e) => {
                        if (e.target.value) {
                          // Create date at noon to avoid timezone issues
                          const date = new Date(e.target.value + "T12:00:00");
                          setNewSeasonEndDate(date);
                        } else {
                          // Default to 7 days after start date
                          const date = new Date(newSeasonStartDate);
                          date.setDate(date.getDate() + 7);
                          setNewSeasonEndDate(date);
                        }
                      }}
                      className="w-full"
                      required
                    />
                  </div>
                </div>
              </div>

              <div className="bg-blue-50 dark:bg-blue-950/30 p-4 rounded-md border border-blue-200 dark:border-blue-800">
                <Text className="text-blue-800 dark:text-blue-200">
                  <strong>Note:</strong> Adding a new seasonal period will
                  create pricing rows for all room types, occupancy types, and
                  meal plans. Base prices will be copied to the new seasonal
                  period by default.
                </Text>
              </div>
            </div>
          </Drawer.Body>

          {/* Fixed Footer with CTAs */}
          <div className="flex-shrink-0 border-t border-border bg-card p-4">
            <div className="flex justify-end gap-4">
              <Button
                variant="secondary"
                onClick={() => setIsSeasonalModalOpen(false)}
                disabled={isAddingSeasonalPeriod}
                className="flex-1 sm:flex-none"
              >
                Cancel
              </Button>
              <Button
                variant="primary"
                onClick={handleAddSeasonalPeriod}
                disabled={isAddingSeasonalPeriod}
                className="flex-1 sm:flex-none"
              >
                {isAddingSeasonalPeriod ? (
                  <>
                    <Loader2 className="w-4 h-4 animate-spin mr-2" />
                    Adding...
                  </>
                ) : (
                  "Add Season"
                )}
              </Button>
            </div>
          </div>
        </Drawer.Content>
      </Drawer>

      {/* Bulk Price Update Modal */}
      <BulkPriceUpdateModal
        isOpen={isBulkUpdateModalOpen}
        onClose={() => setIsBulkUpdateModalOpen(false)}
        roomConfigs={roomConfigs}
        occupancyConfigs={occupancyConfigs}
        mealPlans={mealPlans}
        seasonalPeriods={seasonalPeriods}
        currencyCode={currencyCode}
        pricingRows={pricingRows}
        onApplyUpdate={handleBulkPriceUpdate}
        onCurrencyChange={handleCurrencyChange}
      />

      {/* Compact Filters */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-3 mb-6 p-4 bg-muted/50 rounded-lg border border-border">
        {/* Seasons Filter using @camped-ai/ui Select */}
        <div>
          <Label
            htmlFor="showAllSeasons"
            className="mb-1 block text-sm font-medium"
          >
            Season
          </Label>
          <Select
            value={selectedSeasonFilter}
            onValueChange={setSelectedSeasonFilter}
          >
            <Select.Trigger id="showAllSeasons" className="w-full">
              <Select.Value placeholder="All Seasons" />
            </Select.Trigger>
            <Select.Content>
              <Select.Item value="all">All Seasons</Select.Item>
              <Select.Item value="base">Base Pricing Only</Select.Item>
              {seasonalPeriods.map((season) => (
                <Select.Item key={season.id} value={season.id}>
                  {season.name}
                </Select.Item>
              ))}
            </Select.Content>
          </Select>
        </div>

        <FilterSelect
          id="roomConfig"
          label="Room Type"
          value={selectedRoomConfig}
          onChange={setSelectedRoomConfig}
          options={roomConfigs}
          placeholder="All Room Types"
        />

        <FilterSelect
          id="occupancyConfig"
          label="Occupancy Type"
          value={selectedOccupancyConfig}
          onChange={setSelectedOccupancyConfig}
          options={occupancyConfigs}
          placeholder="All Occupancy Types"
        />

        <FilterSelect
          id="mealPlan"
          label="Meal Plan"
          value={selectedMealPlan}
          onChange={setSelectedMealPlan}
          options={mealPlans}
          placeholder="All Meal Plans"
        />
      </div>

      <div className="overflow-x-auto rounded-lg border border-border shadow-sm">
        <table className="min-w-full divide-y divide-border table-fixed">
          <thead className="bg-muted/50">
            <tr>
              <th className="px-3 py-2 text-left text-xs font-medium text-muted-foreground uppercase tracking-wide w-32">
                Season
              </th>
              <th className="px-3 py-2 text-left text-xs font-medium text-muted-foreground uppercase tracking-wide w-32">
                Room Type
              </th>
              <th className="px-3 py-2 text-left text-xs font-medium text-muted-foreground uppercase tracking-wide w-20">
                Occupancy
              </th>
              <th className="px-3 py-2 text-left text-xs font-medium text-muted-foreground uppercase tracking-wide w-28">
                Meal Plan
              </th>

              {/* Cost & Margin Management Columns */}
              <th className="px-3 py-2 text-center text-xs font-medium text-muted-foreground uppercase tracking-wide w-24">
                <div className="flex flex-col items-center gap-0.5">
                  <span className="font-semibold">Gross Cost</span>
                  <span className="text-xs text-muted-foreground/70 font-normal">
                    {getCurrencySymbol()}
                  </span>
                </div>
              </th>
              <th className="px-3 py-2 text-center text-xs font-medium text-muted-foreground uppercase tracking-wide w-24">
                <div className="flex flex-col items-center gap-0.5">
                  <span className="font-semibold">Fixed Margin</span>
                  <span className="text-xs text-muted-foreground/70 font-normal">
                    {getCurrencySymbol()}
                  </span>
                </div>
              </th>
              <th className="px-3 py-2 text-center text-xs font-medium text-muted-foreground uppercase tracking-wide w-20">
                <div className="flex flex-col items-center gap-0.5">
                  <span className="font-semibold">Margin %</span>
                </div>
              </th>
              <th className="px-3 py-2 text-center text-xs font-medium text-muted-foreground uppercase tracking-wide w-24">
                <div className="flex flex-col items-center gap-0.5">
                  <span className="font-semibold">Total</span>
                  <span className="text-xs text-muted-foreground/70 font-normal">
                    {getCurrencySymbol()}
                  </span>
                </div>
              </th>
              <th className="px-3 py-2 text-center text-xs font-medium text-muted-foreground uppercase tracking-wide w-20">
                Actions
              </th>

              {showAllDays ? (
                <>
                  {weekdays.map((day) => (
                    <th
                      key={day.id}
                      className="px-2 py-2 text-center text-xs font-medium text-muted-foreground uppercase tracking-wide"
                    >
                      <div className="flex flex-col items-center gap-0.5">
                        <span className="font-semibold">
                          {day.name.slice(0, 3)}
                        </span>
                        <span className="text-xs text-muted-foreground/70 font-normal">
                          {getCurrencySymbol()}
                        </span>
                      </div>
                    </th>
                  ))}
                </>
              ) : (
                <th className="px-3 py-2 text-center text-xs font-medium text-muted-foreground uppercase tracking-wide">
                  <div className="flex flex-col items-center gap-0.5">
                    <span className="font-semibold">Price</span>
                    <span className="text-xs text-muted-foreground/70 font-normal">
                      {getCurrencySymbol()}
                    </span>
                  </div>
                </th>
              )}
              {/* <th className="px-2 py-2 text-center text-xs font-medium text-gray-500 uppercase tracking-wide">
                Actions
              </th> */}
            </tr>
          </thead>
          <tbody className="bg-background divide-y divide-border">
            {filteredPricingRows.map((row, index) => {
              // Find the room, occupancy, and meal plan objects
              const room = roomConfigs.find((r) => r.id === row.roomConfigId);
              const occupancy = occupancyConfigs.find(
                (o) => o.id === row.occupancyTypeId
              );
              const mealPlan = mealPlans.find((m) => m.id === row.mealPlanId);
              const seasonalPeriod = row.seasonalPeriodId
                ? seasonalPeriods.find((s) => s.id === row.seasonalPeriodId)
                : undefined;

              return (
                <tr
                  key={row.id}
                  className={index % 2 === 0 ? "bg-background" : "bg-muted/50"}
                >
                  <td className="px-3 py-3 whitespace-nowrap text-sm w-32">
                    {seasonalPeriod ? (
                      <div className="flex items-center gap-1">
                        <Calendar className="w-3 h-3 text-blue-600 dark:text-blue-400 flex-shrink-0" />
                        <div className="flex flex-col min-w-0">
                          <span
                            className="font-medium truncate text-xs"
                            title={seasonalPeriod.name}
                          >
                            {seasonalPeriod.name}
                          </span>
                          <span className="text-xs text-muted-foreground truncate">
                            {format(
                              new Date(seasonalPeriod.start_date),
                              "MMM d"
                            )}{" "}
                            -{" "}
                            {format(new Date(seasonalPeriod.end_date), "MMM d")}
                          </span>
                        </div>
                      </div>
                    ) : (
                      <span className="font-medium text-foreground text-xs">
                        Base Price
                      </span>
                    )}
                  </td>
                  <td className="px-3 py-3 whitespace-nowrap text-sm w-32">
                    <div
                      className="truncate text-xs"
                      title={room?.title || "Unknown Room"}
                    >
                      <span className="font-medium">
                        {room?.title || "Unknown Room"}
                      </span>
                    </div>
                  </td>
                  <td className="px-2 py-3 whitespace-nowrap text-sm w-20">
                    <div
                      className="truncate text-xs"
                      title={occupancy?.name || "Unknown Occupancy"}
                    >
                      {occupancy?.name || "Unknown Occupancy"}
                    </div>
                  </td>
                  <td className="px-3 py-3 whitespace-nowrap text-sm w-28">
                    <div
                      className="truncate"
                      title={
                        (occupancy as any)?.type === "EXTRA_BED" ||
                        occupancy?.name?.toLowerCase().includes("extra bed") ||
                        (occupancy as any)?.type === "COT" ||
                        occupancy?.name?.toLowerCase().includes("cot")
                          ? "-"
                          : mealPlan?.name || "Unknown Meal Plan"
                      }
                    >
                      {(occupancy as any)?.type === "EXTRA_BED" ||
                      occupancy?.name?.toLowerCase().includes("extra bed") ||
                      (occupancy as any)?.type === "COT" ||
                      occupancy?.name?.toLowerCase().includes("cot") ? (
                        <span className="text-muted-foreground">-</span>
                      ) : (
                        mealPlan?.name || "Unknown Meal Plan"
                      )}
                    </div>
                  </td>

                  {/* Cost & Margin Management Columns */}
                  {/* Gross Cost */}
                  <td className="px-2 py-3 whitespace-nowrap">
                    <div className="flex justify-center">
                      <Input
                        type="number"
                        className={`w-20 py-1 text-sm border rounded focus:border-primary focus:ring-1 focus:ring-primary transition-all text-center ${
                          row.modified
                            ? "border-primary bg-primary/10 dark:bg-primary/20"
                            : "border-input"
                        }`}
                        value={row.defaultValues.grossCost}
                        onChange={(e) =>
                          handleDefaultValueChange(
                            row.roomConfigId,
                            row.occupancyTypeId,
                            row.mealPlanId,
                            row.seasonalPeriodId,
                            'grossCost',
                            parseFloat(e.target.value) || 0
                          )
                        }
                        min="0"
                        step="0.01"
                        placeholder="0.00"
                      />
                    </div>
                  </td>

                  {/* Fixed Margin */}
                  <td className="px-2 py-3 whitespace-nowrap">
                    <div className="flex justify-center">
                      <Input
                        type="number"
                        className={`w-20 py-1 text-sm border rounded focus:border-primary focus:ring-1 focus:ring-primary transition-all text-center ${
                          row.modified
                            ? "border-primary bg-primary/10 dark:bg-primary/20"
                            : "border-input"
                        }`}
                        value={row.defaultValues.fixedMargin}
                        onChange={(e) =>
                          handleDefaultValueChange(
                            row.roomConfigId,
                            row.occupancyTypeId,
                            row.mealPlanId,
                            row.seasonalPeriodId,
                            'fixedMargin',
                            parseFloat(e.target.value) || 0
                          )
                        }
                        step="0.01"
                        placeholder="0.00"
                      />
                    </div>
                  </td>

                  {/* Margin Percentage */}
                  <td className="px-2 py-3 whitespace-nowrap">
                    <div className="flex justify-center">
                      <Input
                        type="number"
                        className={`w-16 py-1 text-sm border rounded focus:border-primary focus:ring-1 focus:ring-primary transition-all text-center ${
                          row.modified
                            ? "border-primary bg-primary/10 dark:bg-primary/20"
                            : "border-input"
                        }`}
                        value={row.defaultValues.marginPercentage}
                        onChange={(e) =>
                          handleDefaultValueChange(
                            row.roomConfigId,
                            row.occupancyTypeId,
                            row.mealPlanId,
                            row.seasonalPeriodId,
                            'marginPercentage',
                            parseFloat(e.target.value) || 0
                          )
                        }
                        min="0"
                        max="1000"
                        step="0.1"
                        placeholder="0"
                      />
                    </div>
                  </td>

                  {/* Calculated Total */}
                  <td className="px-2 py-3 whitespace-nowrap">
                    <div className="flex justify-center">
                      <div className={`w-20 py-1 px-2 text-sm rounded text-center font-medium ${
                        row.defaultValues.total > 0
                          ? "bg-green-50 text-green-700 border border-green-200"
                          : "bg-gray-50 text-gray-500 border border-gray-200"
                      }`}>
                        {row.defaultValues.total.toFixed(2)}
                      </div>
                    </div>
                  </td>

                  {/* Apply to All Days Action */}
                  <td className="px-2 py-3 whitespace-nowrap">
                    <div className="flex justify-center">
                      <Button
                        variant="secondary"
                        size="small"
                        onClick={() => handleApplyToAllDays(row.roomConfigId, row.occupancyTypeId, row.mealPlanId, row.seasonalPeriodId)}
                        disabled={!row.defaultValues.grossCost || row.defaultValues.grossCost <= 0}
                        className="flex items-center gap-1 text-xs px-2 py-1"
                        title="Apply default cost and margin values to all days of the week"
                      >
                        <Copy className="w-3 h-3" />
                        Apply
                      </Button>
                    </div>
                  </td>

                  {showAllDays ? (
                    <>
                      {weekdays.map((day) => (
                        <td
                          key={day.id}
                          className="px-2 py-3 whitespace-nowrap"
                        >
                          <div className="flex justify-center">
                            <div className="relative">
                              {/* <div className="absolute inset-y-0 left-0 flex items-center pl-2 pointer-events-none">
                                <span className="text-muted-foreground text-xs">
                                  {getCurrencySymbol()}
                                </span>
                              </div> */}
                              <Input
                                type="number"
                                className={`w-20 py-1 text-sm border rounded focus:border-primary focus:ring-1 focus:ring-primary transition-all text-center ${
                                  row.modified
                                    ? "border-primary bg-primary/10 dark:bg-primary/20"
                                    : "border-input"
                                }`}
                                value={formatPrice(
                                  row.prices[day.id as keyof typeof row.prices]
                                )}
                                onChange={(e) =>
                                  handlePriceChange(
                                    row.roomConfigId,
                                    row.occupancyTypeId,
                                    row.mealPlanId,
                                    row.seasonalPeriodId,
                                    day.id,
                                    parsePrice(e.target.value)
                                  )
                                }
                                min="0"
                                step="0.01"
                              />
                            </div>
                          </div>
                        </td>
                      ))}
                    </>
                  ) : (
                    <td className="px-3 py-3 whitespace-nowrap">
                      <div className="flex justify-center">
                        <div className="relative">
                          {/* <div className="absolute inset-y-0 left-0 left-0 flex items-center pl-2 pointer-events-none">
                            <span className="text-muted-foreground text-xs">
                              {getCurrencySymbol()}
                            </span>
                          </div> */}
                          <Input
                            type="number"
                            className={`w-24 pl-5 py-1 text-sm border rounded focus:border-primary focus:ring-1 focus:ring-primary transition-all text-center ${
                              row.modified
                                ? "border-primary bg-primary/10 dark:bg-primary/20"
                                : "border-input"
                            }`}
                            value={formatPrice(row.prices.mon)}
                            onChange={(e) => {
                              const value = parsePrice(e.target.value);
                              // First update Monday price
                              handlePriceChange(
                                row.roomConfigId,
                                row.occupancyTypeId,
                                row.mealPlanId,
                                row.seasonalPeriodId,
                                "mon",
                                value
                              );
                              // Then copy to all days
                              setTimeout(() => {
                                handleCopyToAllDays(
                                  row.roomConfigId,
                                  row.occupancyTypeId,
                                  row.mealPlanId,
                                  row.seasonalPeriodId,
                                  "mon"
                                );
                              }, 0);
                            }}
                            min="0"
                            step="0.01"
                          />
                        </div>
                      </div>
                    </td>
                  )}
                </tr>
              );
            })}
          </tbody>
        </table>
      </div>
    </Container>
  );
};

export default ComprehensivePricingTable;
