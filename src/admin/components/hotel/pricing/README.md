# Hotel Pricing Components

This directory contains two main pricing table components for different use cases:

## 1. ComprehensivePricingTable (Full Edit Mode)

**Location**: `comprehensive-pricing-table.tsx`

**Use Case**: Root-level pricing management page with full editing capabilities.

**URL**: `http://localhost:9000/app/hotel-management/pricing`

**Features**:
- ✅ Full editing capabilities (input fields, save buttons)
- ✅ Cost/margin management columns
- ✅ Bulk update functionality
- ✅ Seasonal period management
- ✅ All CRUD operations

**Usage Example**:
```tsx
<ComprehensivePricingTable
  hotelId={hotelId}
  roomConfigs={roomConfigs}
  occupancyConfigs={occupancyConfigs}
  mealPlans={mealPlans}
  seasonalPeriods={seasonalPeriods}
  setSeasonalPeriods={setSeasonalPeriods}
  roomPricingData={roomPricingData}
  onSave={handleSave}
  canEdit={true}
  canCreate={true}
  canDelete={true}
  hideBackButton={false}
  readOnlyMode={false} // Full edit mode
/>
```

## 2. ReadOnlyPricingTable (View-Only Mode)

**Location**: `read-only-pricing-table.tsx`

**Use Case**: Individual hotel pricing pages for viewing pricing data only.

**URL**: `http://localhost:9000/app/hotel-management/hotels/{hotelId}/pricing`

**Features**:
- ✅ Read-only display of pricing data
- ✅ Clean, simplified interface
- ✅ Shows only weekday price totals (Monday through Sunday)
- ❌ No cost/margin columns
- ❌ No editing capabilities
- ❌ No bulk update functionality
- ❌ No save buttons

**Usage Example**:
```tsx
<ReadOnlyPricingTable
  hotelId={hotelId}
  roomConfigs={roomConfigs}
  occupancyConfigs={occupancyConfigs}
  mealPlans={mealPlans}
  seasonalPeriods={seasonalPeriods}
  pricingRows={transformedPricingRows}
  currencyCode="CHF"
  isLoading={false}
/>
```

## 3. Alternative: ComprehensivePricingTable in Read-Only Mode

You can also use the comprehensive table in read-only mode by setting the `readOnlyMode` prop:

```tsx
<ComprehensivePricingTable
  hotelId={hotelId}
  roomConfigs={roomConfigs}
  occupancyConfigs={occupancyConfigs}
  mealPlans={mealPlans}
  seasonalPeriods={seasonalPeriods}
  setSeasonalPeriods={setSeasonalPeriods}
  roomPricingData={roomPricingData}
  canEdit={false}
  canCreate={false}
  canDelete={false}
  hideBackButton={true}
  readOnlyMode={true} // Enables read-only mode
/>
```

## Implementation Examples

### Root-Level Pricing Page (Full Edit)
```tsx
// src/admin/routes/hotel-management/pricing/page.tsx
import ComprehensivePricingTable from "../../../components/hotel/pricing/comprehensive-pricing-table";

// In component:
<ComprehensivePricingTable
  hotelId={selectedHotelId}
  roomConfigs={pricingData.room_configs}
  occupancyConfigs={pricingData.occupancy_configs}
  mealPlans={pricingData.meal_plans}
  seasonalPeriods={seasonalPeriods}
  setSeasonalPeriods={setSeasonalPeriods}
  roomPricingData={pricingData.room_pricing_data}
  onSave={handleSave}
  canEdit={canEdit}
  canCreate={canCreate}
  canDelete={canDelete}
  hideBackButton={true}
/>
```

### Individual Hotel Pricing Page (Read-Only)
```tsx
// src/admin/routes/hotel-management/hotels/[slug]/pricing/page.tsx
import ReadOnlyPricingTable from "../../../../../components/hotel/pricing/read-only-pricing-table";

// In component:
<ReadOnlyPricingTable
  hotelId={hotel.id}
  roomConfigs={comprehensiveData.roomConfigs || []}
  occupancyConfigs={comprehensiveData.occupancyConfigs || []}
  mealPlans={comprehensiveData.mealPlans || []}
  seasonalPeriods={comprehensiveData.seasonalPeriods || []}
  pricingRows={transformToPricingRows()}
  currencyCode="CHF"
  isLoading={false}
/>
```

## Data Transformation

When using `ReadOnlyPricingTable`, you need to transform the comprehensive pricing data into the expected format:

```tsx
const transformToPricingRows = () => {
  if (!comprehensiveData?.room_pricing_data) return [];

  const pricingRows: any[] = [];

  comprehensiveData.room_pricing_data.forEach((roomData: any) => {
    // Process base pricing (weekday rules)
    roomData.weekday_rules?.forEach((rule: any) => {
      pricingRows.push({
        id: `${roomData.room_config_id}-${rule.occupancy_type_id}-${rule.meal_plan_id || 'no-meal'}`,
        roomConfigId: roomData.room_config_id,
        occupancyTypeId: rule.occupancy_type_id,
        mealPlanId: rule.meal_plan_id,
        prices: {
          mon: rule.monday_price || 0,
          tue: rule.tuesday_price || 0,
          wed: rule.wednesday_price || 0,
          thu: rule.thursday_price || 0,
          fri: rule.friday_price || 0,
          sat: rule.saturday_price || 0,
          sun: rule.sunday_price || 0,
        },
      });
    });

    // Process seasonal pricing
    roomData.seasonal_prices?.forEach((seasonalPrice: any) => {
      seasonalPrice.weekday_rules?.forEach((rule: any) => {
        pricingRows.push({
          id: `${roomData.room_config_id}-${rule.occupancy_type_id}-${rule.meal_plan_id || 'no-meal'}-${seasonalPrice.seasonal_period_id}`,
          roomConfigId: roomData.room_config_id,
          occupancyTypeId: rule.occupancy_type_id,
          mealPlanId: rule.meal_plan_id,
          seasonalPeriodId: seasonalPrice.seasonal_period_id,
          prices: {
            mon: rule.monday_price || 0,
            tue: rule.tuesday_price || 0,
            wed: rule.wednesday_price || 0,
            thu: rule.thursday_price || 0,
            fri: rule.friday_price || 0,
            sat: rule.saturday_price || 0,
            sun: rule.sunday_price || 0,
          },
        });
      });
    });
  });

  return pricingRows;
};
```

## Key Differences

| Feature | ComprehensivePricingTable | ReadOnlyPricingTable |
|---------|---------------------------|----------------------|
| **Editing** | ✅ Full editing | ❌ Read-only |
| **Cost/Margin Columns** | ✅ Visible | ❌ Hidden |
| **Bulk Update** | ✅ Available | ❌ Not available |
| **Save Buttons** | ✅ Available | ❌ Not available |
| **Apply Buttons** | ✅ Available | ❌ Not available |
| **File Size** | ~3000 lines | ~300 lines |
| **Performance** | Heavier | Lighter |
| **Use Case** | Management/Admin | Viewing/Display |
