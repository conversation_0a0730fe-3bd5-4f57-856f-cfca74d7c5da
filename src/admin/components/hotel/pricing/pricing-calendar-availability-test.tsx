import React, { useState } from "react";
import { <PERSON><PERSON>, Container, Heading, Text, Select } from "@camped-ai/ui";
import { format, addDays, startOfMonth, endOfMonth } from "date-fns";
import { usePricingCalendarAvailability, isRoomConfigAvailableOnDate, getRoomConfigAvailabilityStatus } from "../../../hooks/hotel/use-pricing-calendar-availability";

/**
 * Test component to verify the pricing calendar availability integration
 * This component tests the availability checking logic for the pricing calendar
 */
const PricingCalendarAvailabilityTest: React.FC = () => {
  const [selectedHotelId, setSelectedHotelId] = useState("01K1SVK8C7GYSFTNKQQ5N1GHKP");
  const [selectedRoomConfigId, setSelectedRoomConfigId] = useState("");
  const [testDate, setTestDate] = useState(new Date());

  const currentMonth = startOfMonth(testDate);
  const endOfCurrentMonth = endOfMonth(testDate);

  // Fetch availability data
  const { data: availabilityData, isLoading, error } = usePricingCalendarAvailability({
    hotelId: selectedHotelId,
    startDate: currentMonth,
    endDate: endOfCurrentMonth,
    roomConfigId: selectedRoomConfigId || undefined,
    enabled: !!selectedHotelId,
  });

  // Test dates for the next 7 days
  const testDates = Array.from({ length: 7 }, (_, i) => addDays(new Date(), i));

  return (
    <Container className="py-6">
      <div className="max-w-4xl mx-auto">
        <Heading level="h1" className="text-2xl font-bold mb-6">
          Pricing Calendar Availability Test
        </Heading>

        {/* Test Configuration */}
        <div className="bg-muted/20 border border-border rounded-lg p-4 mb-6">
          <Heading level="h3" className="text-lg font-semibold mb-4">
            Test Configuration
          </Heading>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium mb-2">Hotel ID</label>
              <input
                type="text"
                value={selectedHotelId}
                onChange={(e) => setSelectedHotelId(e.target.value)}
                className="w-full p-2 border border-border rounded-md"
                placeholder="Enter hotel ID"
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium mb-2">Room Config ID (Optional)</label>
              <input
                type="text"
                value={selectedRoomConfigId}
                onChange={(e) => setSelectedRoomConfigId(e.target.value)}
                className="w-full p-2 border border-border rounded-md"
                placeholder="Enter room config ID for filtering"
              />
            </div>
          </div>
        </div>

        {/* API Status */}
        <div className="bg-muted/20 border border-border rounded-lg p-4 mb-6">
          <Heading level="h3" className="text-lg font-semibold mb-4">
            API Status
          </Heading>
          
          <div className="space-y-2">
            <div className="flex items-center gap-2">
              <span className="font-medium">Loading:</span>
              <span className={isLoading ? "text-yellow-600" : "text-green-600"}>
                {isLoading ? "Yes" : "No"}
              </span>
            </div>
            
            <div className="flex items-center gap-2">
              <span className="font-medium">Error:</span>
              <span className={error ? "text-red-600" : "text-green-600"}>
                {error ? error.message : "None"}
              </span>
            </div>
            
            <div className="flex items-center gap-2">
              <span className="font-medium">Data Loaded:</span>
              <span className={availabilityData ? "text-green-600" : "text-red-600"}>
                {availabilityData ? "Yes" : "No"}
              </span>
            </div>
            
            {availabilityData && (
              <>
                <div className="flex items-center gap-2">
                  <span className="font-medium">Availability Records:</span>
                  <span className="text-blue-600">
                    {availabilityData.availability.length}
                  </span>
                </div>
                
                <div className="flex items-center gap-2">
                  <span className="font-medium">Room Configs:</span>
                  <span className="text-blue-600">
                    {availabilityData.room_configs.length}
                  </span>
                </div>
              </>
            )}
          </div>
        </div>

        {/* Room Configs */}
        {availabilityData?.room_configs && availabilityData.room_configs.length > 0 && (
          <div className="bg-muted/20 border border-border rounded-lg p-4 mb-6">
            <Heading level="h3" className="text-lg font-semibold mb-4">
              Available Room Configurations
            </Heading>
            
            <div className="space-y-2">
              {availabilityData.room_configs.map((config) => (
                <div key={config.id} className="flex items-center justify-between p-2 bg-background rounded border">
                  <div>
                    <span className="font-medium">{config.title}</span>
                    <span className="text-sm text-muted-foreground ml-2">({config.id})</span>
                  </div>
                  <div className="text-sm text-muted-foreground">
                    {config.variants?.length || 0} rooms
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Availability Test Results */}
        {availabilityData && selectedRoomConfigId && (
          <div className="bg-muted/20 border border-border rounded-lg p-4 mb-6">
            <Heading level="h3" className="text-lg font-semibold mb-4">
              Availability Test Results (Next 7 Days)
            </Heading>
            
            <div className="space-y-3">
              {testDates.map((date) => {
                const isAvailable = isRoomConfigAvailableOnDate(
                  selectedRoomConfigId,
                  date,
                  availabilityData.availability
                );
                
                const status = getRoomConfigAvailabilityStatus(
                  selectedRoomConfigId,
                  date,
                  availabilityData.availability
                );

                return (
                  <div key={format(date, "yyyy-MM-dd")} className="flex items-center justify-between p-3 bg-background rounded border">
                    <div>
                      <span className="font-medium">{format(date, "MMM dd, yyyy")}</span>
                      <span className="text-sm text-muted-foreground ml-2">({format(date, "EEEE")})</span>
                    </div>
                    
                    <div className="flex items-center gap-4">
                      <div className={`px-2 py-1 rounded text-sm font-medium ${
                        isAvailable 
                          ? "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200"
                          : "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200"
                      }`}>
                        {isAvailable ? "Available" : "Not Available"}
                      </div>
                      
                      <div className="text-sm text-muted-foreground">
                        {status.availableRooms}/{status.totalRooms} rooms
                      </div>
                      
                      <div className={`px-2 py-1 rounded text-xs ${
                        status.status === "available" 
                          ? "bg-green-50 text-green-700 dark:bg-green-950 dark:text-green-300"
                          : status.status === "partially_available"
                          ? "bg-yellow-50 text-yellow-700 dark:bg-yellow-950 dark:text-yellow-300"
                          : "bg-red-50 text-red-700 dark:bg-red-950 dark:text-red-300"
                      }`}>
                        {status.status.replace("_", " ")}
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        )}

        {/* Raw Data */}
        {availabilityData && (
          <div className="bg-muted/20 border border-border rounded-lg p-4">
            <Heading level="h3" className="text-lg font-semibold mb-4">
              Raw Availability Data (First 10 Records)
            </Heading>
            
            <div className="overflow-x-auto">
              <table className="w-full text-sm">
                <thead>
                  <tr className="border-b">
                    <th className="text-left p-2">Room ID</th>
                    <th className="text-left p-2">Room Config ID</th>
                    <th className="text-left p-2">Date</th>
                    <th className="text-left p-2">Status</th>
                    <th className="text-left p-2">Available Qty</th>
                    <th className="text-left p-2">Is Available</th>
                  </tr>
                </thead>
                <tbody>
                  {availabilityData.availability.slice(0, 10).map((entry, index) => (
                    <tr key={index} className="border-b">
                      <td className="p-2 font-mono text-xs">{entry.room_id}</td>
                      <td className="p-2 font-mono text-xs">{entry.room_config_id}</td>
                      <td className="p-2">{entry.date}</td>
                      <td className="p-2">
                        <span className={`px-2 py-1 rounded text-xs ${
                          entry.status === "available" 
                            ? "bg-green-100 text-green-800"
                            : "bg-red-100 text-red-800"
                        }`}>
                          {entry.status}
                        </span>
                      </td>
                      <td className="p-2">{entry.available_quantity}</td>
                      <td className="p-2">
                        <span className={entry.is_available ? "text-green-600" : "text-red-600"}>
                          {entry.is_available ? "Yes" : "No"}
                        </span>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
              
              {availabilityData.availability.length > 10 && (
                <div className="text-center text-sm text-muted-foreground mt-2">
                  ... and {availabilityData.availability.length - 10} more records
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    </Container>
  );
};

export default PricingCalendarAvailabilityTest;
