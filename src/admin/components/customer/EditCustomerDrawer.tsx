import React, { useState, useEffect } from "react";
import {
  Drawer,
  Button,
  Input,
  Text,
  toast,
} from "@camped-ai/ui";
import { useF<PERSON>, Controller } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { Edit } from "lucide-react";
import { sdk } from "../../lib/sdk";

// Customer interface (matching the backend structure)
interface Customer {
  id: string;
  company_name: string | null;
  first_name: string | null;
  last_name: string | null;
  email: string;
  phone: string | null;
  metadata: any;
  has_account: boolean;
  created_by: string | null;
  created_at: string;
  updated_at: string;
  deleted_at: string | null;
}

// Form validation schema
const editCustomerSchema = z.object({
  first_name: z.string().min(1, "First name is required"),
  last_name: z.string().min(1, "Last name is required"),
  email: z.string().email("Invalid email address"),
  phone: z.string().optional(),
  company_name: z.string().optional(),
});

type EditCustomerFormData = z.infer<typeof editCustomerSchema>;

interface EditCustomerDrawerProps {
  isOpen: boolean;
  onClose: () => void;
  customer: Customer;
  onSuccess: () => void;
}

const EditCustomerDrawer: React.FC<EditCustomerDrawerProps> = ({
  isOpen,
  onClose,
  customer,
  onSuccess,
}) => {
  const [isSubmitting, setIsSubmitting] = useState(false);

  const {
    control,
    handleSubmit,
    reset,
    formState: { errors },
  } = useForm<EditCustomerFormData>({
    resolver: zodResolver(editCustomerSchema),
    defaultValues: {
      first_name: "",
      last_name: "",
      email: "",
      phone: "",
      company_name: "",
    },
  });

  // Update form values when customer data changes
  useEffect(() => {
    if (customer && isOpen) {
      reset({
        first_name: customer.first_name || "",
        last_name: customer.last_name || "",
        email: customer.email || "",
        phone: customer.phone || "",
        company_name: customer.company_name || "",
      });
    }
  }, [customer, isOpen, reset]);

  const handleClose = () => {
    reset();
    onClose();
  };

  const onSubmit = async (data: EditCustomerFormData) => {
    setIsSubmitting(true);
    try {
      await sdk.client.fetch(`/admin/all-customers/${customer.id}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: data,
      });

      toast.success("Customer updated successfully!");
      onSuccess();
      onClose();
    } catch (error) {
      console.error("Error updating customer:", error);
      toast.error(
        error instanceof Error ? error.message : "Failed to update customer"
      );
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Drawer open={isOpen} onOpenChange={handleClose}>
      <Drawer.Content>
        <Drawer.Header>
          <div className="flex items-center gap-3">
            <div>
              <Drawer.Title>Edit Customer</Drawer.Title>
              <Drawer.Description>
                Update customer information and contact details
              </Drawer.Description>
            </div>
          </div>
        </Drawer.Header>

        <Drawer.Body className="overflow-y-auto p-6">
          <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
            {/* First Name */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                First Name *
              </label>
              <Controller
                name="first_name"
                control={control}
                render={({ field }) => (
                  <Input
                    {...field}
                    placeholder="Enter first name"
                    className={errors.first_name ? "border-red-500" : ""}
                  />
                )}
              />
              {errors.first_name && (
                <Text size="small" className="text-red-600 mt-1">
                  {errors.first_name.message}
                </Text>
              )}
            </div>

            {/* Last Name */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Last Name *
              </label>
              <Controller
                name="last_name"
                control={control}
                render={({ field }) => (
                  <Input
                    {...field}
                    placeholder="Enter last name"
                    className={errors.last_name ? "border-red-500" : ""}
                  />
                )}
              />
              {errors.last_name && (
                <Text size="small" className="text-red-600 mt-1">
                  {errors.last_name.message}
                </Text>
              )}
            </div>

            {/* Email */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Email Address *
              </label>
              <Controller
                name="email"
                control={control}
                render={({ field }) => (
                  <Input
                    {...field}
                    type="email"
                    placeholder="Enter email address"
                    className={errors.email ? "border-red-500" : ""}
                  />
                )}
              />
              {errors.email && (
                <Text size="small" className="text-red-600 mt-1">
                  {errors.email.message}
                </Text>
              )}
            </div>

            {/* Phone */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Phone Number
              </label>
              <Controller
                name="phone"
                control={control}
                render={({ field }) => (
                  <Input
                    {...field}
                    type="tel"
                    placeholder="Enter phone number (optional)"
                    className={errors.phone ? "border-red-500" : ""}
                  />
                )}
              />
              {errors.phone && (
                <Text size="small" className="text-red-600 mt-1">
                  {errors.phone.message}
                </Text>
              )}
            </div>

            {/* Company Name */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Company Name
              </label>
              <Controller
                name="company_name"
                control={control}
                render={({ field }) => (
                  <Input
                    {...field}
                    placeholder="Enter company name (optional)"
                    className={errors.company_name ? "border-red-500" : ""}
                  />
                )}
              />
              {errors.company_name && (
                <Text size="small" className="text-red-600 mt-1">
                  {errors.company_name.message}
                </Text>
              )}
            </div>

          </form>
        </Drawer.Body>

        <Drawer.Footer className="flex justify-end gap-3 p-6 border-t">
          <Button
            type="button"
            variant="secondary"
            onClick={handleClose}
            disabled={isSubmitting}
          >
            Cancel
          </Button>
          <Button
            type="submit"
            variant="primary"
            disabled={isSubmitting}
            onClick={handleSubmit(onSubmit)}
          >
            {isSubmitting ? "Updating..." : "Update Customer"}
          </Button>
        </Drawer.Footer>
      </Drawer.Content>
    </Drawer>
  );
};

export default EditCustomerDrawer;
