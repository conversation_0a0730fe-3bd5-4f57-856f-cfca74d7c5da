import React, { useState } from "react";
import {
  Drawer,
  Button,
  Input,
  Text,
  toast,
} from "@camped-ai/ui";
import { useForm, Controller } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { UserPlus } from "lucide-react";
import { sdk } from "../../lib/sdk";

// Gender and Relationship enums (matching the backend)
enum Gender {
  MALE = "male",
  FEMALE = "female",
  OTHER = "other",
}

enum Relationship {
  SPOUSE = "spouse",
  CHILD = "child",
  SIBLING = "sibling",
  GRANDPARENT = "grandparent",
  FRIEND = "friend",
  PARENT = "parent",
  COLLEAGUE = "colleague",
  RELATIVE = "relative",
  PARENT_IN_LAW = "parent_in_law",
  OTHER = "other",
}

// Form validation schema
const familyMemberSchema = z.object({
  first_name: z.string().min(1, "First name is required"),
  last_name: z.string().min(1, "Last name is required"),
  gender: z.nativeEnum(Gender, { required_error: "Gender is required" }),
  date_of_birth: z.string().min(1, "Date of birth is required"),
  relationship: z.nativeEnum(Relationship).optional(),
});

type FamilyMemberFormData = z.infer<typeof familyMemberSchema>;

interface AddFamilyMemberDrawerProps {
  isOpen: boolean;
  onClose: () => void;
  customerId: string;
  onSuccess: () => void;
}

const AddFamilyMemberDrawer: React.FC<AddFamilyMemberDrawerProps> = ({
  isOpen,
  onClose,
  customerId,
  onSuccess,
}) => {
  const [isSubmitting, setIsSubmitting] = useState(false);

  const {
    control,
    handleSubmit,
    reset,
    formState: { errors },
  } = useForm<FamilyMemberFormData>({
    resolver: zodResolver(familyMemberSchema),
    defaultValues: {
      first_name: "",
      last_name: "",
      gender: undefined,
      date_of_birth: "",
      relationship: undefined,
    },
  });

  const handleClose = () => {
    reset();
    onClose();
  };

  const onSubmit = async (data: FamilyMemberFormData) => {
    setIsSubmitting(true);
    try {
      await sdk.client.fetch(`/admin/all-customers/${customerId}/travellers`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: data,
      });

      toast.success("Family member added successfully!");
      reset();
      onSuccess();
      onClose();
    } catch (error) {
      console.error("Error adding family member:", error);
      toast.error(
        error instanceof Error ? error.message : "Failed to add family member"
      );
    } finally {
      setIsSubmitting(false);
    }
  };

  // Gender options
  const genderOptions = [
    { value: Gender.MALE, label: "Male" },
    { value: Gender.FEMALE, label: "Female" },
    { value: Gender.OTHER, label: "Other" },
  ];

  // Relationship options
  const relationshipOptions = [
    { value: Relationship.SPOUSE, label: "Spouse" },
    { value: Relationship.CHILD, label: "Child" },
    { value: Relationship.PARENT, label: "Parent" },
    { value: Relationship.SIBLING, label: "Sibling" },
    { value: Relationship.GRANDPARENT, label: "Grandparent" },
    { value: Relationship.PARENT_IN_LAW, label: "Parent-in-law" },
    { value: Relationship.RELATIVE, label: "Relative" },
    { value: Relationship.FRIEND, label: "Friend" },
    { value: Relationship.COLLEAGUE, label: "Colleague" },
    { value: Relationship.OTHER, label: "Other" },
  ];

  return (
    <Drawer open={isOpen} onOpenChange={handleClose}>
      <Drawer.Content>
        <Drawer.Header>
          <div className="flex items-center gap-3">
            <UserPlus className="h-6 w-6 text-blue-600" />
            <div>
              <Drawer.Title>Add Family Member</Drawer.Title>
              <Drawer.Description>
                Add a new family member or traveling companion
              </Drawer.Description>
            </div>
          </div>
        </Drawer.Header>

        <Drawer.Body className="overflow-y-auto p-6">
          <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
            {/* First Name */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                First Name *
              </label>
              <Controller
                name="first_name"
                control={control}
                render={({ field }) => (
                  <Input
                    {...field}
                    placeholder="Enter first name"
                    className={errors.first_name ? "border-red-500" : ""}
                  />
                )}
              />
              {errors.first_name && (
                <Text size="small" className="text-red-600 mt-1">
                  {errors.first_name.message}
                </Text>
              )}
            </div>

            {/* Last Name */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Last Name *
              </label>
              <Controller
                name="last_name"
                control={control}
                render={({ field }) => (
                  <Input
                    {...field}
                    placeholder="Enter last name"
                    className={errors.last_name ? "border-red-500" : ""}
                  />
                )}
              />
              {errors.last_name && (
                <Text size="small" className="text-red-600 mt-1">
                  {errors.last_name.message}
                </Text>
              )}
            </div>

            {/* Gender */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Gender *
              </label>
              <Controller
                name="gender"
                control={control}
                render={({ field }) => (
                  <select
                    {...field}
                    className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                      errors.gender ? "border-red-500" : "border-gray-300"
                    }`}
                  >
                    <option value="">Select gender</option>
                    {genderOptions.map((option) => (
                      <option key={option.value} value={option.value}>
                        {option.label}
                      </option>
                    ))}
                  </select>
                )}
              />
              {errors.gender && (
                <Text size="small" className="text-red-600 mt-1">
                  {errors.gender.message}
                </Text>
              )}
            </div>

            {/* Date of Birth */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Date of Birth *
              </label>
              <Controller
                name="date_of_birth"
                control={control}
                render={({ field }) => (
                  <Input
                    {...field}
                    type="date"
                    className={errors.date_of_birth ? "border-red-500" : ""}
                  />
                )}
              />
              {errors.date_of_birth && (
                <Text size="small" className="text-red-600 mt-1">
                  {errors.date_of_birth.message}
                </Text>
              )}
            </div>

            {/* Relationship */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Relationship
              </label>
              <Controller
                name="relationship"
                control={control}
                render={({ field }) => (
                  <select
                    {...field}
                    className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                      errors.relationship ? "border-red-500" : "border-gray-300"
                    }`}
                  >
                    <option value="">Select relationship (optional)</option>
                    {relationshipOptions.map((option) => (
                      <option key={option.value} value={option.value}>
                        {option.label}
                      </option>
                    ))}
                  </select>
                )}
              />
              {errors.relationship && (
                <Text size="small" className="text-red-600 mt-1">
                  {errors.relationship.message}
                </Text>
              )}
            </div>

          </form>
        </Drawer.Body>

        <Drawer.Footer className="flex justify-end gap-3 p-6 border-t">
          <Button
            type="button"
            variant="secondary"
            onClick={handleClose}
            disabled={isSubmitting}
          >
            Cancel
          </Button>
          <Button
            type="submit"
            variant="primary"
            disabled={isSubmitting}
            onClick={handleSubmit(onSubmit)}
          >
            {isSubmitting ? "Adding..." : "Add Family Member"}
          </Button>
        </Drawer.Footer>
      </Drawer.Content>
    </Drawer>
  );
};

export default AddFamilyMemberDrawer;
