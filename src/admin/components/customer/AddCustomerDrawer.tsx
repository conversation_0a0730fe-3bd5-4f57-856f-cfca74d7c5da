import React, { useState } from "react";
import {
  Drawer,
  Button,
  Input,
  Text,
  toast,
} from "@camped-ai/ui";
import { useForm, Controller } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { UserPlus } from "lucide-react";
import { sdk } from "../../lib/sdk";

// Form validation schema
const addCustomerSchema = z.object({
  first_name: z.string().min(1, "First name is required"),
  last_name: z.string().min(1, "Last name is required"),
  email: z.string().email("Invalid email address"),
  phone: z.string().optional(),
  company_name: z.string().optional(),
});

type AddCustomerFormData = z.infer<typeof addCustomerSchema>;

interface AddCustomerDrawerProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
}

const AddCustomerDrawer: React.FC<AddCustomerDrawerProps> = ({
  isOpen,
  onClose,
  onSuccess,
}) => {
  const [isSubmitting, setIsSubmitting] = useState(false);

  const {
    control,
    handleSubmit,
    reset,
    formState: { errors },
  } = useForm<AddCustomerFormData>({
    resolver: zodResolver(addCustomerSchema),
    defaultValues: {
      first_name: "",
      last_name: "",
      email: "",
      phone: "",
      company_name: "",
    },
  });

  const handleClose = () => {
    reset();
    onClose();
  };

  const onSubmit = async (data: AddCustomerFormData) => {
    setIsSubmitting(true);
    try {
      await sdk.client.fetch("/admin/customers", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      });

      toast.success("Customer created successfully!");
      reset();
      onSuccess();
      onClose();
    } catch (error) {
      console.error("Error creating customer:", error);
      toast.error(
        error instanceof Error ? error.message : "Failed to create customer"
      );
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Drawer open={isOpen} onOpenChange={handleClose}>
      <Drawer.Content>
        <Drawer.Header>
          <div className="flex items-center gap-3">
            <UserPlus className="h-6 w-6 text-blue-600" />
            <div>
              <Drawer.Title>Add Customer</Drawer.Title>
              <Drawer.Description>
                Create a new customer account
              </Drawer.Description>
            </div>
          </div>
        </Drawer.Header>

        <Drawer.Body className="overflow-y-auto p-6">
          <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
            {/* First Name */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                First Name *
              </label>
              <Controller
                name="first_name"
                control={control}
                render={({ field }) => (
                  <Input
                    {...field}
                    placeholder="Enter first name"
                    className={errors.first_name ? "border-red-500" : ""}
                  />
                )}
              />
              {errors.first_name && (
                <Text size="small" className="text-red-600 mt-1">
                  {errors.first_name.message}
                </Text>
              )}
            </div>

            {/* Last Name */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Last Name *
              </label>
              <Controller
                name="last_name"
                control={control}
                render={({ field }) => (
                  <Input
                    {...field}
                    placeholder="Enter last name"
                    className={errors.last_name ? "border-red-500" : ""}
                  />
                )}
              />
              {errors.last_name && (
                <Text size="small" className="text-red-600 mt-1">
                  {errors.last_name.message}
                </Text>
              )}
            </div>

            {/* Email */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Email Address *
              </label>
              <Controller
                name="email"
                control={control}
                render={({ field }) => (
                  <Input
                    {...field}
                    type="email"
                    placeholder="Enter email address"
                    className={errors.email ? "border-red-500" : ""}
                  />
                )}
              />
              {errors.email && (
                <Text size="small" className="text-red-600 mt-1">
                  {errors.email.message}
                </Text>
              )}
            </div>

            {/* Phone */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Phone Number
              </label>
              <Controller
                name="phone"
                control={control}
                render={({ field }) => (
                  <Input
                    {...field}
                    type="tel"
                    placeholder="Enter phone number (optional)"
                    className={errors.phone ? "border-red-500" : ""}
                  />
                )}
              />
              {errors.phone && (
                <Text size="small" className="text-red-600 mt-1">
                  {errors.phone.message}
                </Text>
              )}
            </div>

            {/* Company Name */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Company Name
              </label>
              <Controller
                name="company_name"
                control={control}
                render={({ field }) => (
                  <Input
                    {...field}
                    placeholder="Enter company name (optional)"
                    className={errors.company_name ? "border-red-500" : ""}
                  />
                )}
              />
              {errors.company_name && (
                <Text size="small" className="text-red-600 mt-1">
                  {errors.company_name.message}
                </Text>
              )}
            </div>
          </form>
        </Drawer.Body>
        
        <Drawer.Footer className="flex justify-end gap-3 p-6 border-t">
          <Button
            type="button"
            variant="secondary"
            onClick={handleClose}
            disabled={isSubmitting}
          >
            Cancel
          </Button>
          <Button
            type="submit"
            variant="primary"
            disabled={isSubmitting}
            onClick={handleSubmit(onSubmit)}
          >
            {isSubmitting ? "Creating..." : "Create Customer"}
          </Button>
        </Drawer.Footer>
      </Drawer.Content>
    </Drawer>
  );
};

export default AddCustomerDrawer;
