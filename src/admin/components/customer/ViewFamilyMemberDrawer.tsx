import React, { useState, useEffect } from "react";
import {
  <PERSON>er,
  Button,
  Input,
  Text,
  Badge,
  Select,
  toast,
} from "@camped-ai/ui";
import { useForm, Controller } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { User, Edit, Calendar, Users } from "lucide-react";
import { sdk } from "../../lib/sdk";
import { CustomerTraveller } from "../../hooks/customer-management/use-customer-details";

// Gender and Relationship types (matching the backend)
type Gender = "male" | "female" | "other";
type Relationship = "spouse" | "child" | "sibling" | "grandparent" | "friend" | "parent" | "colleague" | "relative" | "parent_in_law" | "other";

// Form validation schema
const editFamilyMemberSchema = z.object({
  first_name: z.string().min(1, "First name is required"),
  last_name: z.string().min(1, "Last name is required"),
  gender: z.enum(["male", "female", "other"], { required_error: "Gender is required" }),
  date_of_birth: z.string().min(1, "Date of birth is required"),
  relationship: z.enum(["spouse", "child", "sibling", "grandparent", "friend", "parent", "colleague", "relative", "parent_in_law", "other"]).optional(),
});

type EditFamilyMemberFormData = z.infer<typeof editFamilyMemberSchema>;

interface ViewFamilyMemberDrawerProps {
  isOpen: boolean;
  onClose: () => void;
  familyMember: CustomerTraveller | null;
  onSuccess: () => void;
}

const ViewFamilyMemberDrawer: React.FC<ViewFamilyMemberDrawerProps> = ({
  isOpen,
  onClose,
  familyMember,
  onSuccess,
}) => {
  const [isEditing, setIsEditing] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const {
    control,
    handleSubmit,
    reset,
    formState: { errors },
  } = useForm<EditFamilyMemberFormData>({
    resolver: zodResolver(editFamilyMemberSchema),
    defaultValues: {
      first_name: "",
      last_name: "",
      gender: undefined,
      date_of_birth: "",
      relationship: undefined,
    },
  });

  // Update form values when family member data changes
  useEffect(() => {
    if (familyMember && isOpen) {
      reset({
        first_name: familyMember.first_name || "",
        last_name: familyMember.last_name || "",
        gender: familyMember.gender,
        date_of_birth: familyMember.date_of_birth ? formatDateForInput(familyMember.date_of_birth) : "",
        relationship: familyMember.relationship as Relationship,
      });
    }
  }, [familyMember, isOpen, reset]);

  const handleClose = () => {
    setIsEditing(false);
    reset();
    onClose();
  };

  const handleEdit = () => {
    setIsEditing(true);
  };

  const handleCancelEdit = () => {
    setIsEditing(false);
    if (familyMember) {
      reset({
        first_name: familyMember.first_name || "",
        last_name: familyMember.last_name || "",
        gender: familyMember.gender,
        date_of_birth: familyMember.date_of_birth ? formatDateForInput(familyMember.date_of_birth) : "",
        relationship: familyMember.relationship as Relationship,
      });
    }
  };

  const onSubmit = async (data: EditFamilyMemberFormData) => {
    if (!familyMember) return;

    setIsSubmitting(true);
    try {
      // Ensure date is in YYYY-MM-DD format (no time component)
      const formattedData = {
        ...data,
        date_of_birth: data.date_of_birth, // HTML date input already provides YYYY-MM-DD format
      };

      await sdk.client.fetch(
        `/admin/all-customers/${familyMember.customer_id}/travellers/${familyMember.id}`,
        {
          method: "PUT",
          headers: {
            "Content-Type": "application/json",
          },
          body: formattedData,
        }
      );

      toast.success("Family member updated successfully!");
      setIsEditing(false);
      onSuccess();
    } catch (error) {
      console.error("Error updating family member:", error);
      toast.error(
        error instanceof Error ? error.message : "Failed to update family member"
      );
    } finally {
      setIsSubmitting(false);
    }
  };

  // Helper functions
  const formatDateForInput = (dateString: string): string => {
    // Convert date to YYYY-MM-DD format for HTML date input
    const date = new Date(dateString);
    return date.toISOString().split('T')[0];
  };

  const calculateAge = (dateOfBirth: string): number => {
    const today = new Date();
    const birthDate = new Date(dateOfBirth);
    let age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();

    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
      age--;
    }

    return age;
  };

  const getGenderColor = (gender: string) => {
    switch (gender) {
      case "male":
        return "bg-blue-100 text-blue-800";
      case "female":
        return "bg-pink-100 text-pink-800";
      default:
        return "bg-purple-100 text-purple-800";
    }
  };

  const formatGender = (gender: Gender): string => {
    switch (gender) {
      case "male":
        return "Male";
      case "female":
        return "Female";
      case "other":
        return "Other";
      default:
        return "Not specified";
    }
  };

  const formatRelationship = (relationship?: Relationship): string => {
    if (!relationship) return "Not specified";

    switch (relationship) {
      case "spouse":
        return "Spouse";
      case "child":
        return "Child";
      case "sibling":
        return "Sibling";
      case "grandparent":
        return "Grandparent";
      case "friend":
        return "Friend";
      case "parent":
        return "Parent";
      case "colleague":
        return "Colleague";
      case "relative":
        return "Relative";
      case "parent_in_law":
        return "Parent-in-law";
      case "other":
        return "Other";
      default:
        return "Not specified";
    }
  };

  const formatDate = (dateString: string): string => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
    });
  };

  if (!familyMember) return null;

  return (
    <Drawer open={isOpen} onOpenChange={handleClose}>
      <Drawer.Content>
        <Drawer.Header>
          <div className="flex items-center gap-3">
            <div>
              <Drawer.Title>
                {isEditing ? "Edit Family Member" : "Family Member Details"}
              </Drawer.Title>
            </div>
          </div>
        </Drawer.Header>

        <Drawer.Body className="overflow-y-auto p-6">
          {!isEditing ? (
            // View Mode
            <div className="space-y-6">
              {/* Profile Header */}
              <div className="flex items-center space-x-4">
                <div className="h-16 w-16 rounded-full bg-gradient-to-br from-blue-500 to-purple-600 text-white font-bold flex items-center justify-center text-xl">
                  {familyMember.first_name.charAt(0)}
                  {familyMember.last_name.charAt(0)}
                </div>
                <div>
                  <Text size="large" className="text-gray-900 font-semibold">
                    {familyMember.first_name} {familyMember.last_name}
                  </Text>
                  <Text size="small" className="text-gray-600 mt-1">
                    {formatRelationship(familyMember.relationship as Relationship)} • {calculateAge(familyMember.date_of_birth)} years old
                  </Text>
                </div>
              </div>

              {/* Details Grid */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <Text size="small" className="text-gray-700 font-medium">
                    Gender
                  </Text>
                  <Badge className={`px-2 py-1 rounded-full text-xs ${getGenderColor(familyMember.gender)}`}>
                    {formatGender(familyMember.gender)}
                  </Badge>
                </div>

                <div className="space-y-2">
                  <Text size="small" className="text-gray-700 font-medium">
                    Date of Birth
                  </Text>
                  <div className="flex items-center gap-2">
                    <Calendar className="h-4 w-4 text-gray-500" />
                    <Text size="small">{formatDate(familyMember.date_of_birth)}</Text>
                  </div>
                </div>

                <div className="space-y-2">
                  <Text size="small" className="text-gray-700 font-medium">
                    Relationship
                  </Text>
                  <div className="flex items-center gap-2">
                    <Users className="h-4 w-4 text-gray-500" />
                    <Text size="small">{formatRelationship(familyMember.relationship as Relationship)}</Text>
                  </div>
                </div>

                <div className="space-y-2">
                  <Text size="small" className="text-gray-700 font-medium">
                    Age
                  </Text>
                  <Text size="base" className="text-gray-900 font-semibold">
                    {calculateAge(familyMember.date_of_birth)} years
                  </Text>
                </div>
              </div>
            </div>
          ) : (
            // Edit Mode
            <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
              {/* First Name */}
              <div className="space-y-2">
                <Text size="small" className="text-gray-700 font-medium">
                  First Name *
                </Text>
                <Controller
                  name="first_name"
                  control={control}
                  render={({ field }) => (
                    <Input
                      {...field}
                      placeholder="Enter first name"
                      className={errors.first_name ? "border-red-500 focus:border-red-500" : ""}
                    />
                  )}
                />
                {errors.first_name && (
                  <Text size="small" className="text-red-600">
                    {errors.first_name.message}
                  </Text>
                )}
              </div>

              {/* Last Name */}
              <div className="space-y-2">
                <Text size="small" className="text-gray-700 font-medium">
                  Last Name *
                </Text>
                <Controller
                  name="last_name"
                  control={control}
                  render={({ field }) => (
                    <Input
                      {...field}
                      placeholder="Enter last name"
                      className={errors.last_name ? "border-red-500 focus:border-red-500" : ""}
                    />
                  )}
                />
                {errors.last_name && (
                  <Text size="small" className="text-red-600">
                    {errors.last_name.message}
                  </Text>
                )}
              </div>

              {/* Gender */}
              <div className="space-y-2">
                <Text size="small" className="text-gray-700 font-medium">
                  Gender *
                </Text>
                <Controller
                  name="gender"
                  control={control}
                  render={({ field }) => (
                    <Select
                      value={field.value}
                      onValueChange={field.onChange}
                    >
                      <Select.Trigger className={errors.gender ? "border-red-500 focus:border-red-500" : ""}>
                        <Select.Value placeholder="Select gender" />
                      </Select.Trigger>
                      <Select.Content>
                        <Select.Item value="male">Male</Select.Item>
                        <Select.Item value="female">Female</Select.Item>
                        <Select.Item value="other">Other</Select.Item>
                      </Select.Content>
                    </Select>
                  )}
                />
                {errors.gender && (
                  <Text size="small" className="text-red-600">
                    {errors.gender.message}
                  </Text>
                )}
              </div>

              {/* Date of Birth */}
              <div className="space-y-2">
                <Text size="small" className="text-gray-700 font-medium">
                  Date of Birth *
                </Text>
                <Controller
                  name="date_of_birth"
                  control={control}
                  render={({ field }) => (
                    <Input
                      {...field}
                      type="date"
                      className={errors.date_of_birth ? "border-red-500 focus:border-red-500" : ""}
                    />
                  )}
                />
                {errors.date_of_birth && (
                  <Text size="small" className="text-red-600">
                    {errors.date_of_birth.message}
                  </Text>
                )}
              </div>

              {/* Relationship */}
              <div className="space-y-2">
                <Text size="small" className="text-gray-700 font-medium">
                  Relationship
                </Text>
                <Controller
                  name="relationship"
                  control={control}
                  render={({ field }) => (
                    <Select
                      value={field.value}
                      onValueChange={field.onChange}
                    >
                      <Select.Trigger className={errors.relationship ? "border-red-500 focus:border-red-500" : ""}>
                        <Select.Value placeholder="Select relationship" />
                      </Select.Trigger>
                      <Select.Content>
                        <Select.Item value="spouse">Spouse</Select.Item>
                        <Select.Item value="child">Child</Select.Item>
                        <Select.Item value="sibling">Sibling</Select.Item>
                        <Select.Item value="parent">Parent</Select.Item>
                        <Select.Item value="grandparent">Grandparent</Select.Item>
                        <Select.Item value="parent_in_law">Parent-in-law</Select.Item>
                        <Select.Item value="relative">Relative</Select.Item>
                        <Select.Item value="friend">Friend</Select.Item>
                        <Select.Item value="colleague">Colleague</Select.Item>
                        <Select.Item value="other">Other</Select.Item>
                      </Select.Content>
                    </Select>
                  )}
                />
                {errors.relationship && (
                  <Text size="small" className="text-red-600">
                    {errors.relationship.message}
                  </Text>
                )}
              </div>
            </form>
          )}
        </Drawer.Body>
        
        <Drawer.Footer className="flex justify-end gap-3 p-6 border-t">
          {!isEditing ? (
            <>
              <Button
                type="button"
                variant="secondary"
                onClick={handleClose}
              >
                Close
              </Button>
              <Button
                type="button"
                variant="primary"
                onClick={handleEdit}
              >
                <Edit className="mr-2 h-4 w-4" />
                Edit
              </Button>
            </>
          ) : (
            <>
              <Button
                type="button"
                variant="secondary"
                onClick={handleCancelEdit}
                disabled={isSubmitting}
              >
                Cancel
              </Button>
              <Button
                type="submit"
                variant="primary"
                disabled={isSubmitting}
                onClick={handleSubmit(onSubmit)}
              >
                {isSubmitting ? "Saving..." : "Save Changes"}
              </Button>
            </>
          )}
        </Drawer.Footer>
      </Drawer.Content>
    </Drawer>
  );
};

export default ViewFamilyMemberDrawer;
