import React, { useState, useEffect, useRef } from "react";
import { Text, Label, toast } from "@camped-ai/ui";
import {
  DndContext,
  DragEndEvent,
  DragOverlay,
  DragStartEvent,
  DropAnimation,
  KeyboardSensor,
  PointerSensor,
  UniqueIdentifier,
  useSensor,
  useSensors,
  closestCenter,
} from "@dnd-kit/core";
import {
  arrayMove,
  SortableContext,
  sortableKeyboardCoordinates,
} from "@dnd-kit/sortable";
import { UseFormReturn, useFieldArray } from "react-hook-form";
import { defaultDropAnimationSideEffects } from "@dnd-kit/core";

import MediaItem, { MediaField } from "../hotel/media-item";
import MediaGridItemOverlay from "../hotel/media-grid-item-overlay";

export type DestinationFormData = {
  name: string;
  handle: string;
  description: string;
  is_active: boolean;
  is_featured: boolean;
  country: string;
  location: string | null;
  tags: string[] | null;
  category_id?: string;
  media?: MediaField[];
  image_ids?: string[];
  thumbnail_image_id?: string;
  faqs?: Array<{
    id?: string;
    question: string;
    answer: string;
  }>;
  id?: string;
};

const dropAnimationConfig: DropAnimation = {
  sideEffects: defaultDropAnimationSideEffects({
    styles: {
      active: {
        opacity: "0.4",
      },
    },
  }),
};

// Helper function to validate image IDs
const isValidImageId = (id: string | undefined): boolean => {
  return !!(id && id.trim() !== "" && id !== "undefined" && id !== "null");
};

const DestinationMediaSection = ({
  form,
  destinationId,
}: {
  form: UseFormReturn<DestinationFormData>;
  destinationId?: string;
}) => {
  const { fields, append, remove } = useFieldArray({
    name: "media",
    control: form.control,
    keyName: "field_id",
  });

  const [activeId, setActiveId] = useState<UniqueIdentifier | null>(null);

  // Create a ref for the file input
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Watch for changes in the form's media field
  // This will help us detect when the form is reset
  useEffect(() => {
    // If the form's media field is empty but our fields array isn't,
    // it means the form was reset externally
    const formMedia = form.getValues("media") || [];
    if (formMedia.length === 0 && fields.length > 0) {
      // Clear our fields by removing all items
      fields.forEach((_, index) => remove(index));
    }
  }, [form, remove]);

  // Clear the file input when the form is reset
  useEffect(() => {
    // If the form's media field is empty, also clear the file input
    const formMedia = form.getValues("media") || [];
    if (formMedia.length === 0 && fileInputRef.current) {
      fileInputRef.current.value = "";
    }
  }, [form]);

  // Initialize field array with form data when component mounts or form data changes
  useEffect(() => {
    const formMedia = form.getValues("media") || [];

    // Only initialize if form has media but field array is empty
    if (formMedia.length > 0 && fields.length === 0) {
      // Replace all fields with form data
      formMedia.forEach((media) => {
        append(media);
      });
    }
  }, [form.formState.defaultValues?.media, append, fields.length]);

  // Watch for form reset and sync field array
  useEffect(() => {
    const subscription = form.watch((value, { name, type }) => {
      if (type === "change" && name === "media") {
        const formMedia = value.media || [];

        console.log("📸 Media section form watch triggered:", {
          formMediaCount: formMedia.length,
          fieldsCount: fields.length,
          formMediaItems: formMedia.map((m, idx) => ({
            index: idx,
            hasUrl: !!m?.url,
            hasFieldId: !!m?.field_id,
            hasFile: !!m?.file,
            fileName: m?.file?.name,
            field_id: m?.field_id,
            isThumbnail: m?.isThumbnail,
          })),
          fieldsItems: fields.map((f, idx) => ({
            index: idx,
            hasUrl: !!f?.url,
            hasFieldId: !!f?.field_id,
            hasFile: !!f?.file,
            fileName: f?.file?.name,
            field_id: f?.field_id,
            isThumbnail: f?.isThumbnail,
          })),
        });

        // If form media is different from field array, sync them
        // Compare by field_id since newly uploaded images don't have server id yet
        if (
          formMedia.length !== fields.length ||
          JSON.stringify(formMedia.map((m) => m?.field_id)) !==
            JSON.stringify(fields.map((f) => f.field_id))
        ) {
          console.log("📸 Syncing media fields with form data");

          // Clear existing fields
          while (fields.length > 0) {
            remove(0);
          }

          // Add new fields - relaxed condition to include items with field_id even if no url yet
          formMedia.forEach((media) => {
            if (media && media.field_id) {
              console.log("📸 Adding media field:", {
                hasUrl: !!media.url,
                hasFile: !!media.file,
                fileName: media.file?.name,
                field_id: media.field_id,
              });
              append({
                isThumbnail: media.isThumbnail || false,
                url: media.url,
                id: media.id,
                file: media.file,
                field_id: media.field_id,
              });
            } else {
              console.log("📸 Skipping media item (missing field_id):", media);
            }
          });
        }
      }
    });

    return () => subscription.unsubscribe();
  }, [form, fields, append, remove]);

  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 5, // 5px movement to start dragging
      },
    }),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  const handleDragStart = (event: DragStartEvent) => {
    setActiveId(event.active.id);
  };

  const handleDragEnd = async (event: DragEndEvent) => {
    const { active, over } = event;
    setActiveId(null);

    if (active.id !== over?.id) {
      const oldIndex = fields.findIndex(
        (field) => field.field_id === active.id
      );
      const newIndex = fields.findIndex((field) => field.field_id === over?.id);

      // Update local state immediately for better UX
      const reorderedFields = arrayMove(fields, oldIndex, newIndex);
      form.setValue("media", reorderedFields, {
        shouldDirty: true,
        shouldTouch: true,
      });

      // Update ranks on the server for existing images
      try {
        const updatePromises = reorderedFields.map(async (field, index) => {
          // Only update existing images (those with valid IDs)
          if (isValidImageId(field.id)) {
            const response = await fetch(
              `/admin/hotel-management/destinations/images/${field.id}/rank`,
              {
                method: "POST",
                credentials: "include",
                headers: {
                  "Content-Type": "application/json",
                },
                body: JSON.stringify({ rank: index }),
              }
            );

            if (!response.ok) {
              console.error(`Failed to update rank for image ${field.id}`);
            }
          }
        });

        await Promise.all(updatePromises);

        toast.success("Success", {
          description: "Image order updated successfully",
        });
      } catch (error) {
        console.error("Error updating image order:", error);
        toast.error("Error", {
          description: "Failed to update image order",
        });
      }
    }
  };

  const handleDragCancel = () => {
    setActiveId(null);
  };

  const getOnDelete = (index: number) => {
    return async () => {
      const mediaToDelete = fields[index];

      // If the image has a valid ID, attempt to delete from the server
      if (isValidImageId(mediaToDelete.id)) {
        try {
          const response = await fetch(
            `/admin/hotel-management/destinations/images/${mediaToDelete.id}`,
            {
              method: "DELETE",
              credentials: "include",
            }
          );

          if (!response.ok) {
            const errorText = await response.text();
            throw new Error(`Failed to delete image: ${errorText}`);
          }

          toast.success("Success", {
            description: "Image deleted successfully",
          });
        } catch (error) {
          console.error("Error deleting image:", error);
          toast.error("Error", {
            description: "Failed to delete image",
          });
          return;
        }
      }

      // Remove the image from the form
      remove(index);
    };
  };

  const getMakeThumbnail = (index: number) => {
    return async () => {
      const selectedField = fields[index];

      // Validate that we have a valid field with an ID
      if (!selectedField || !isValidImageId(selectedField.id)) {
        console.error(
          "Cannot set thumbnail: Invalid field or missing ID",
          selectedField
        );
        toast.error("Error", {
          description: "Cannot set thumbnail: Invalid image",
        });
        return;
      }

      // First, update the local form state to ensure only one thumbnail is selected
      const newFields = fields.map((field, i) => ({
        ...field,
        isThumbnail: i === index, // Only the selected item will be true, all others false
      }));

      form.setValue("media", newFields, {
        shouldDirty: true,
        shouldTouch: true,
      });

      // If this is an existing image (has a valid ID), call the API to set it as thumbnail
      if (isValidImageId(selectedField.id)) {
        try {
          // Use the passed destinationId or extract from URL as fallback
          let actualDestinationId = destinationId;

          // if (!actualDestinationId) {
          //   const currentUrl = window.location.pathname;
          //   const destinationIdMatch = currentUrl.match(/\/destinations\/([^\/]+)/);
          //   actualDestinationId = destinationIdMatch?.[1];
          // }

          if (actualDestinationId) {
            const response = await fetch(
              `/admin/hotel-management/destinations/${actualDestinationId}/thumbnail`,
              {
                method: "POST",
                credentials: "include",
                headers: {
                  "Content-Type": "application/json",
                },
                body: JSON.stringify({ image_id: selectedField.id }),
              }
            );

            if (!response.ok) {
              const errorText = await response.text();
              console.error("Failed to set thumbnail:", errorText);
              toast.error("Error", {
                description: "Failed to set thumbnail",
              });
              // Revert the local state if the API call fails
              const revertedFields = fields.map((field) => ({
                ...field,
                isThumbnail: field.isThumbnail, // Keep original thumbnail state
              }));
              form.setValue("media", revertedFields, {
                shouldDirty: true,
                shouldTouch: true,
              });
              return;
            }

            // Show success message
            toast.success("Success", {
              description: "Thumbnail updated successfully",
            });

            // No need to refresh data from server since we've already updated
            // the local state correctly and the server doesn't change the order
          }
        } catch (error) {
          console.error("Error setting thumbnail:", error);
          toast.error("Error", {
            description: "Failed to set thumbnail",
          });
          // Revert the local state if the API call fails
          const revertedFields = fields.map((field) => ({
            ...field,
            isThumbnail: field.isThumbnail, // Keep original thumbnail state
          }));
          form.setValue("media", revertedFields, {
            shouldDirty: true,
            shouldTouch: true,
          });
        }
      }
    };
  };

  const handleMediaUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (files) {
      Array.from(files).forEach((file) => {
        const reader = new FileReader();
        reader.onload = (e) => {
          append({
            url: e.target?.result as string,
            file,
            isThumbnail: fields.length === 0,
            field_id: crypto.randomUUID(),
          });
        };
        reader.readAsDataURL(file);
      });
    }
  };

  // Debug: Log current state when component renders
  console.log("📸 DestinationMediaSection render:", {
    fieldsCount: fields.length,
    formMediaCount: form.getValues("media")?.length || 0,
    fieldsDetails: fields.map((f) => ({
      hasFile: !!f.file,
      hasFieldId: !!f.field_id,
      fileName: f.file?.name,
    })),
  });

  return (
    <div id="media" className="flex flex-col gap-y-2">
        <input
          ref={fileInputRef}
          type="file"
          multiple
          accept="image/*"
          onChange={handleMediaUpload}
          className="hidden"
          id="media-upload"
        />
      <DndContext
        sensors={sensors}
        collisionDetection={closestCenter}
        onDragStart={handleDragStart}
        onDragEnd={handleDragEnd}
        onDragCancel={handleDragCancel}
      >
        {fields.length > 0 ? (
          <DragOverlay dropAnimation={dropAnimationConfig}>
            {activeId ? (
              <MediaGridItemOverlay
                field={
                  fields.find(
                    (field) => field.field_id === activeId
                  ) as MediaField
                }
              />
            ) : null}
          </DragOverlay>
        ) : null}
        <ul className="flex flex-col gap-y-2">
          <SortableContext items={fields.map((field) => field.field_id)}>
            {fields.map((field, index) => {
              const onDelete = getOnDelete(index);
              const onMakeThumbnail = getMakeThumbnail(index);

              return (
                <MediaItem
                  key={field.field_id}
                  field={field}
                  onDelete={onDelete}
                  onMakeThumbnail={onMakeThumbnail}
                />
              );
            })}
          </SortableContext>
        </ul>
      </DndContext>
    </div>
  );
};

export default DestinationMediaSection;
