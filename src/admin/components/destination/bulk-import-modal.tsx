import { useState } from "react";
import {
  <PERSON>ton,
  FocusModal,
  Text,
  Heading,
  Toaster,
  toast,
} from "@camped-ai/ui";
import {
  FileIcon,
  DownloadIcon,
  UploadIcon,
  CheckCircleIcon,
  XCircleIcon,
  AlertTriangleIcon,
} from "lucide-react";
import { useNavigate } from "react-router-dom";
import * as XLSX from "xlsx";

type BulkImportModalProps = {
  open: boolean;
  onClose: () => void;
};

const BulkImportModal = ({ open, onClose }: BulkImportModalProps) => {
  const [file, setFile] = useState<File | null>(null);
  const [isUploading, setIsUploading] = useState(false);
  const [uploadResult, setUploadResult] = useState<any>(null);
  const [filePreview, setFilePreview] = useState<any>(null);
  const [previewData, setPreviewData] = useState<{
    destinations: { headers: string[]; rows: any[] };
    faqs: { headers: string[]; rows: any[] };
  }>({
    destinations: { headers: [], rows: [] },
    faqs: { headers: [], rows: [] },
  });
  const [previewError, setPreviewError] = useState<string>("");
  const [isValidating, setIsValidating] = useState(false);
  const [validationErrors, setValidationErrors] = useState<string[]>([]);
  const [duplicateHandles, setDuplicateHandles] = useState<
    { handle: string; name: string; existingName: string }[]
  >([]);
  const navigate = useNavigate();

  const handleDownloadTemplate = async () => {
    try {
      window.open("/admin/destinations/template", "_blank");
    } catch (error) {
      console.error("Error downloading template:", error);
      toast.error("Error", {
        description: "Failed to download template",
      });
    }
  };

  // Helper function to generate handle from name (same logic as backend)
  const generateHandle = (name: string): string => {
    return name
      .toLowerCase()
      .replace(/[^a-z0-9\s-]/g, "") // Remove special characters
      .replace(/\s+/g, "-") // Replace spaces with hyphens
      .replace(/-+/g, "-") // Replace multiple hyphens with single
      .trim();
  };

  // Function to validate destinations for duplicate handles
  const validateDestinationsForDuplicates = async (
    destinations: any[]
  ): Promise<{
    isValid: boolean;
    duplicates: { handle: string; name: string; existingName: string }[];
    errors: string[];
  }> => {
    const duplicates: { handle: string; name: string; existingName: string }[] =
      [];
    const errors: string[] = [];

    try {
      // Generate handles for all destinations
      const destinationsWithHandles = destinations
        .map((dest, index) => {
          if (!dest.name) {
            errors.push(`Row ${index + 2}: Missing destination name`);
            return null;
          }

          const handle = dest.handle || generateHandle(dest.name);
          return {
            ...dest,
            handle,
            rowIndex: index + 2, // +2 because Excel rows start at 1 and we skip header
          };
        })
        .filter(Boolean);

      // Check for duplicates within the file itself
      const handleCounts = new Map<
        string,
        { name: string; rowIndex: number }[]
      >();
      destinationsWithHandles.forEach((dest) => {
        if (!handleCounts.has(dest.handle)) {
          handleCounts.set(dest.handle, []);
        }
        handleCounts
          .get(dest.handle)!
          .push({ name: dest.name, rowIndex: dest.rowIndex });
      });

      // Find internal duplicates
      handleCounts.forEach((destinations, handle) => {
        if (destinations.length > 1) {
          const names = destinations
            .map((d) => `"${d.name}" (row ${d.rowIndex})`)
            .join(", ");
          errors.push(
            `Duplicate handle "${handle}" found in file for destinations: ${names}`
          );
        }
      });

      // Check against existing destinations in database
      const uniqueHandles = Array.from(handleCounts.keys());
      if (uniqueHandles.length > 0) {
        const response = await fetch("/admin/destinations/validate-handles", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          credentials: "include", // Include cookies for authentication
          body: JSON.stringify({ handles: uniqueHandles }),
        });

        if (response.ok) {
          const result = await response.json();
          console.log("Validation API response:", result);
          if (result.duplicates && result.duplicates.length > 0) {
            result.duplicates.forEach((dup: any) => {
              const fileDestinations = handleCounts.get(dup.handle) || [];
              fileDestinations.forEach((fileDest) => {
                duplicates.push({
                  handle: dup.handle,
                  name: fileDest.name,
                  existingName: dup.existingName,
                });
              });
            });
          }
        } else {
          console.error(
            "Validation API failed:",
            response.status,
            response.statusText
          );
          const errorText = await response.text();
          console.error("Error response:", errorText);
          errors.push(
            "Failed to validate handles against existing destinations"
          );
        }
      }

      return {
        isValid: duplicates.length === 0 && errors.length === 0,
        duplicates,
        errors,
      };
    } catch (error) {
      console.error("Error validating destinations:", error);
      return {
        isValid: false,
        duplicates: [],
        errors: ["Failed to validate destinations. Please try again."],
      };
    }
  };

  // Async function to validate destinations and update state
  const validateDestinationsAsync = async (destinationsData: {
    headers: string[];
    rows: any[];
  }) => {
    console.log("Starting validation for destinations data:", destinationsData);
    setIsValidating(true);
    setValidationErrors([]);
    setDuplicateHandles([]);

    try {
      // Convert rows to destination objects
      const destinations = destinationsData.rows
        .map((row: any) => {
          const destination: any = {};
          destinationsData.headers.forEach((header: string, index: number) => {
            const cleanHeader = header.toLowerCase().trim();
            if (cleanHeader === "name" || cleanHeader === "destination name") {
              destination.name = row[index];
            } else if (cleanHeader === "handle") {
              destination.handle = row[index];
            }
          });
          return destination;
        })
        .filter((dest: any) => dest.name); // Only include destinations with names

      console.log("Parsed destinations for validation:", destinations);

      if (destinations.length > 0) {
        const validation = await validateDestinationsForDuplicates(
          destinations
        );
        console.log("Validation result:", validation);
        setValidationErrors(validation.errors);
        setDuplicateHandles(validation.duplicates);
      }
    } catch (error) {
      console.error("Error during validation:", error);
      setValidationErrors([
        "Failed to validate destinations. Please try again.",
      ]);
    } finally {
      setIsValidating(false);
    }
  };

  // Function to parse Excel/CSV files
  const parseExcelFile = (file: File) => {
    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const data = e.target?.result;
        if (!data) {
          setPreviewError("Could not read file data");
          return;
        }

        // Parse the file using XLSX
        const workbook = XLSX.read(data, { type: "array" });

        // Initialize preview data
        const newPreviewData = {
          destinations: { headers: [] as string[], rows: [] as any[] },
          faqs: { headers: [] as string[], rows: [] as any[] },
        };

        // Process Destinations sheet
        const destinationsSheetName =
          workbook.SheetNames.find(
            (name) =>
              name.toLowerCase() === "destinations" ||
              name.toLowerCase() === "sheet1"
          ) || workbook.SheetNames[0];

        if (destinationsSheetName) {
          const destinationsSheet = workbook.Sheets[destinationsSheetName];
          const destinationsData = XLSX.utils.sheet_to_json(destinationsSheet, {
            header: 1,
          });

          if (destinationsData.length > 0) {
            newPreviewData.destinations.headers =
              destinationsData[0] as string[];
            newPreviewData.destinations.rows = destinationsData.slice(
              1,
              6
            ) as any[];
          }
        }

        // Process FAQs sheet if it exists
        const faqsSheetName = workbook.SheetNames.find(
          (name) => name.toLowerCase() === "faqs"
        );

        if (faqsSheetName) {
          const faqsSheet = workbook.Sheets[faqsSheetName];
          const faqsData = XLSX.utils.sheet_to_json(faqsSheet, { header: 1 });

          if (faqsData.length > 0) {
            newPreviewData.faqs.headers = faqsData[0] as string[];
            newPreviewData.faqs.rows = faqsData.slice(1, 6) as any[];
          }
        }

        if (
          newPreviewData.destinations.headers.length === 0 &&
          newPreviewData.faqs.headers.length === 0
        ) {
          setPreviewError("No data found in file");
          return;
        }

        setPreviewData(newPreviewData);
        setFilePreview({
          name: file.name,
          size: (file.size / 1024).toFixed(2) + " KB",
          type: file.type,
          previewAvailable: true,
        });

        // Validate destinations for duplicate handles if we have destination data
        if (
          newPreviewData.destinations.headers.length > 0 &&
          newPreviewData.destinations.rows.length > 0
        ) {
          validateDestinationsAsync(newPreviewData.destinations);
        }
      } catch (error) {
        console.error("Error parsing file:", error);
        setPreviewError(
          "Could not parse file. Make sure it's a valid Excel or CSV file."
        );
        setFilePreview({
          name: file.name,
          size: (file.size / 1024).toFixed(2) + " KB",
          type: file.type,
          previewAvailable: false,
          error: "Could not generate preview",
        });
      }
    };
    reader.onerror = () => {
      setPreviewError("Error reading file");
    };
    reader.readAsArrayBuffer(file);
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      const selectedFile = e.target.files[0];
      setFile(selectedFile);
      setFilePreview(null); // Reset preview when file changes
      setPreviewData({
        destinations: { headers: [], rows: [] },
        faqs: { headers: [], rows: [] },
      });
      setPreviewError("");
      // Reset validation state
      setValidationErrors([]);
      setDuplicateHandles([]);
      setIsValidating(false);

      // Generate preview for Excel/CSV files
      if (
        selectedFile.name.endsWith(".xlsx") ||
        selectedFile.name.endsWith(".xls") ||
        selectedFile.name.endsWith(".csv")
      ) {
        parseExcelFile(selectedFile);
      }
    }
  };

  const handleUpload = async () => {
    if (!file) {
      toast.error("Error", {
        description: "Please select a file to upload",
      });
      return;
    }

    // Check for validation errors before proceeding
    if (validationErrors.length > 0 || duplicateHandles.length > 0) {
      toast.error("Validation Error", {
        description: "Please fix the validation errors before importing",
      });
      return;
    }

    setIsUploading(true);
    setUploadResult(null);

    try {
      const formData = new FormData();
      formData.append("file", file);

      // Use XMLHttpRequest to track upload progress
      const xhr = new XMLHttpRequest();

      // We're not tracking progress anymore, just using loading state

      // Create a promise to handle the response
      const uploadPromise = new Promise<any>((resolve, reject) => {
        xhr.onreadystatechange = () => {
          if (xhr.readyState === 4) {
            if (xhr.status >= 200 && xhr.status < 300) {
              try {
                const result = JSON.parse(xhr.responseText);
                resolve(result);
              } catch (error) {
                reject(new Error("Invalid response format"));
              }
            } else {
              try {
                const errorResponse = JSON.parse(xhr.responseText);
                reject(new Error(errorResponse.message || "Upload failed"));
              } catch (e) {
                reject(new Error(`Upload failed with status ${xhr.status}`));
              }
            }
          }
        };
      });

      // Open and send the request
      xhr.open("POST", "/admin/destinations/import", true);
      xhr.send(formData);

      // Wait for the upload to complete
      const result = await uploadPromise;

      // Process the result
      setUploadResult(result);

      const destinationsSuccess =
        result.results.destinations?.successful ||
        result.results.successful ||
        0;
      const destinationsFailed =
        result.results.destinations?.failed || result.results.failed || 0;
      const faqsSuccess = result.results.faqs?.successful || 0;
      const faqsFailed = result.results.faqs?.failed || 0;

      if (destinationsSuccess > 0 || faqsSuccess > 0) {
        let description = "";
        if (destinationsSuccess > 0) {
          description += `${destinationsSuccess} destinations imported successfully`;
        }
        if (faqsSuccess > 0) {
          if (description) description += ", ";
          description += `${faqsSuccess} FAQs imported successfully`;
        }

        toast.success("Success", {
          description: description,
        });

        // Note: We keep the modal open to show the results
        // The user can manually close it or upload another file
      }

      if (destinationsFailed > 0 || faqsFailed > 0) {
        let description = "";
        if (destinationsFailed > 0) {
          description += `${destinationsFailed} destinations failed to import`;
        }
        if (faqsFailed > 0) {
          if (description) description += ", ";
          description += `${faqsFailed} FAQs failed to import`;
        }

        toast.error("Warning", {
          description: description,
        });
      }
    } catch (error: any) {
      console.error("Error uploading file:", error);
      toast.error("Error", {
        description: error.message || "Failed to upload file",
      });
    } finally {
      setIsUploading(false);
    }
  };

  const resetModalState = () => {
    setUploadResult(null);
    setFile(null);
    setFilePreview(null);
    setPreviewData({
      destinations: { headers: [], rows: [] },
      faqs: { headers: [], rows: [] },
    });
    setPreviewError("");
    setValidationErrors([]);
    setDuplicateHandles([]);
    setIsValidating(false);
  };

  const handleViewDestinations = () => {
    resetModalState();
    onClose();
    navigate("/hotel-management/destinations");
  };

  return (
    <FocusModal
      open={open}
      onOpenChange={(isOpen) => {
        if (!isOpen) {
          resetModalState();
          onClose();
        }
      }}
    >
      <FocusModal.Content className="flex flex-col h-full">
        <FocusModal.Header className="flex-shrink-0">
          <div className="flex justify-between w-full items-center py-4">
            <Heading level="h2" className="text-xl font-semibold">
              Import Destinations & FAQs
            </Heading>
            {/* Progress Indicator */}
            {!uploadResult && (
              <div className="px-6">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-4">
                    {/* Step 1 */}
                    <div className="flex items-center">
                      <div className="w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-semibold">
                        1
                      </div>
                      <span className="ml-2 text-sm font-medium text-blue-600">
                        Download
                      </span>
                    </div>
                    <div className="w-8 h-0.5 bg-gray-300"></div>

                    {/* Step 2 */}
                    <div className="flex items-center">
                      <div
                        className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-semibold ${
                          file
                            ? "bg-green-600 text-white"
                            : "bg-gray-300 text-gray-600"
                        }`}
                      >
                        2
                      </div>
                      <span
                        className={`ml-2 text-sm font-medium ${
                          file ? "text-green-600" : "text-gray-500"
                        }`}
                      >
                        Upload
                      </span>
                    </div>
                    <div
                      className={`w-8 h-0.5 ${
                        file ? "bg-green-300" : "bg-gray-300"
                      }`}
                    ></div>

                    {/* Step 3 */}
                    <div className="flex items-center">
                      <div
                        className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-semibold ${
                          filePreview
                            ? "bg-purple-600 text-white"
                            : "bg-gray-300 text-gray-600"
                        }`}
                      >
                        3
                      </div>
                      <span
                        className={`ml-2 text-sm font-medium ${
                          filePreview ? "text-purple-600" : "text-gray-500"
                        }`}
                      >
                        Review
                      </span>
                    </div>
                    <div
                      className={`w-8 h-0.5 ${
                        filePreview ? "bg-purple-300" : "bg-gray-300"
                      }`}
                    ></div>

                    {/* Step 4 */}
                    <div className="flex items-center">
                      <div
                        className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-semibold ${
                          file && filePreview
                            ? "bg-orange-600 text-white"
                            : "bg-gray-300 text-gray-600"
                        }`}
                      >
                        4
                      </div>
                      <span
                        className={`ml-2 text-sm font-medium ${
                          file && filePreview
                            ? "text-orange-600"
                            : "text-gray-500"
                        }`}
                      >
                        Import
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        </FocusModal.Header>

        <FocusModal.Body className="flex flex-col flex-grow overflow-hidden">
          <div className="flex-grow overflow-y-auto p-6 space-y-6">
            <Toaster />

            {!uploadResult ? (
              <>
                <div className="bg-gradient-to-r from-blue-50 to-indigo-50 p-6 rounded-lg border border-blue-200 shadow-sm">
                  <div className="flex flex-col gap-4">
                    <div className="flex items-start gap-3">
                      <div className="flex-shrink-0 w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                        <span className="text-blue-600 font-semibold text-sm">
                          1
                        </span>
                      </div>
                      <div className="flex-1">
                        <Heading
                          level="h3"
                          className="text-lg font-medium text-gray-900 mb-2"
                        >
                          Download Import Template
                        </Heading>
                        <Text className="text-gray-600 mb-3">
                          Get the Excel template with pre-configured sheets for
                          destinations and FAQs. The template includes sample
                          data and validation rules to help you format your data
                          correctly.
                        </Text>
                        <div className="bg-white p-3 rounded-md border border-blue-200 mb-3">
                          <Text className="text-sm font-medium text-gray-700 mb-1">
                            Template includes:
                          </Text>
                          <ul className="text-sm text-gray-600 space-y-1">
                            <li>
                              • <strong>Destinations Sheet:</strong> Name,
                              Country, Location, Tags, etc. (Handle
                              auto-generated from name)
                            </li>
                            <li>
                              • <strong>FAQs Sheet:</strong> Questions and
                              answers linked to destinations
                            </li>
                            <li>
                              • <strong>Instructions Sheet:</strong> Detailed
                              field descriptions and requirements
                            </li>
                            <li>
                              • <strong>Auto-Handle Generation:</strong>{" "}
                              URL-friendly handles created automatically from
                              destination names
                            </li>
                          </ul>
                        </div>
                        <Button
                          variant="secondary"
                          onClick={handleDownloadTemplate}
                          className="flex items-center gap-2 bg-blue-600 text-white hover:bg-blue-700"
                        >
                          <DownloadIcon className="w-4 h-4" />
                          <span>Download Excel Template</span>
                        </Button>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="bg-white p-6 rounded-lg border border-gray-200 shadow-sm">
                  <div className="flex flex-col gap-4">
                    <div className="flex items-start gap-3">
                      <div className="flex-shrink-0 w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                        <span className="text-green-600 font-semibold text-sm">
                          2
                        </span>
                      </div>
                      <div className="flex-1">
                        <Heading
                          level="h3"
                          className="text-lg font-medium text-gray-900 mb-2"
                        >
                          Upload Your Data File
                        </Heading>
                        <Text className="text-gray-600 mb-4">
                          Upload the completed Excel file with your destination
                          and FAQ data.
                        </Text>
                      </div>
                    </div>

                    <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-gray-400 transition-colors">
                      <input
                        type="file"
                        accept=".xlsx,.xls,.csv"
                        onChange={handleFileChange}
                        className="hidden"
                        id="file-upload"
                      />
                      <label htmlFor="file-upload" className="cursor-pointer">
                        <div className="flex flex-col items-center gap-3">
                          <UploadIcon className="w-12 h-12 text-gray-400" />
                          <div>
                            <Text className="text-lg font-medium text-gray-700">
                              Click to upload or drag and drop
                            </Text>
                            <Text className="text-sm text-gray-500 mt-1">
                              Excel (.xlsx, .xls) and CSV files • Maximum 5MB
                            </Text>
                          </div>
                        </div>
                      </label>
                    </div>

                    {file && (
                      <div className="mt-4">
                        <div className="bg-green-50 p-4 rounded-lg border border-green-200">
                          <div className="flex items-center gap-3 mb-2">
                            <div className="flex-shrink-0 w-6 h-6 bg-green-100 rounded-full flex items-center justify-center">
                              <CheckCircleIcon className="w-4 h-4 text-green-600" />
                            </div>
                            <Text className="font-medium text-green-800">
                              File Selected Successfully
                            </Text>
                          </div>
                          <div className="ml-9">
                            <div className="flex items-center gap-2 mb-1">
                              <FileIcon className="w-4 h-4 text-green-600" />
                              <Text className="font-medium text-green-700">
                                {file.name}
                              </Text>
                            </div>
                            <Text className="text-green-600 text-sm">
                              Size: {(file.size / 1024).toFixed(2)} KB
                            </Text>
                          </div>
                        </div>
                      </div>
                    )}

                    {filePreview && (
                      <div className="mt-6">
                        <div className="bg-white p-6 rounded-lg border border-gray-200 shadow-sm">
                          <div className="flex items-start gap-3 mb-4">
                            <div className="flex-shrink-0 w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
                              <span className="text-purple-600 font-semibold text-sm">
                                3
                              </span>
                            </div>
                            <div className="flex-1">
                              <Heading
                                level="h3"
                                className="text-lg font-medium text-gray-900 mb-2"
                              >
                                Review Your Data
                              </Heading>
                              <Text className="text-gray-600 mb-4">
                                Preview your data before importing to ensure
                                everything looks correct.
                              </Text>
                            </div>
                          </div>

                          {filePreview.previewAvailable ? (
                            <div>
                              {/* Validation Status */}
                              {isValidating ? (
                                <div className="bg-yellow-50 p-4 rounded-lg border border-yellow-200 mb-4">
                                  <div className="flex items-center gap-2">
                                    <div className="w-4 h-4 border-2 border-yellow-600 border-t-transparent rounded-full animate-spin"></div>
                                    <Text className="text-yellow-800 font-medium">
                                      Validating destinations...
                                    </Text>
                                  </div>
                                </div>
                              ) : validationErrors.length > 0 ||
                                duplicateHandles.length > 0 ? (
                                <div className="bg-red-50 p-4 rounded-lg border border-red-200 mb-4">
                                  <div className="flex items-start gap-2 mb-2">
                                    <AlertTriangleIcon className="w-5 h-5 text-red-600 flex-shrink-0 mt-0.5" />
                                    <div className="flex-1">
                                      <Text className="text-red-800 font-medium mb-2">
                                        Validation Errors Found
                                      </Text>

                                      {/* General validation errors */}
                                      {validationErrors.length > 0 && (
                                        <div className="mb-3">
                                          <Text className="text-red-700 text-sm font-medium mb-1">
                                            Data Issues:
                                          </Text>
                                          <ul className="text-red-700 text-sm space-y-1">
                                            {validationErrors.map(
                                              (error, index) => (
                                                <li
                                                  key={index}
                                                  className="flex items-start gap-1"
                                                >
                                                  <span className="text-red-500 mt-1">
                                                    •
                                                  </span>
                                                  <span>{error}</span>
                                                </li>
                                              )
                                            )}
                                          </ul>
                                        </div>
                                      )}

                                      {/* Duplicate handle errors */}
                                      {duplicateHandles.length > 0 && (
                                        <div>
                                          <Text className="text-red-700 text-sm font-medium mb-1">
                                            Duplicate Destinations Found:
                                          </Text>
                                          <ul className="text-red-700 text-sm space-y-1">
                                            {duplicateHandles.map(
                                              (dup, index) => (
                                                <li
                                                  key={index}
                                                  className="flex items-start gap-1"
                                                >
                                                  <span className="text-red-500 mt-1">
                                                    •
                                                  </span>
                                                  <span>
                                                    "{dup.name}" conflicts with
                                                    existing destination "
                                                    {dup.existingName}" (handle:{" "}
                                                    {dup.handle})
                                                  </span>
                                                </li>
                                              )
                                            )}
                                          </ul>
                                        </div>
                                      )}

                                      <Text className="text-red-600 text-sm mt-2 font-medium">
                                        Please fix these issues before
                                        importing.
                                      </Text>
                                    </div>
                                  </div>
                                </div>
                              ) : (
                                <div className="bg-green-50 p-4 rounded-lg border border-green-200 mb-4">
                                  <div className="flex items-center gap-2">
                                    <CheckCircleIcon className="w-5 h-5 text-green-600" />
                                    <Text className="text-green-800 font-medium">
                                      Validation Passed
                                    </Text>
                                  </div>
                                  <Text className="text-green-700 text-sm mt-1">
                                    ✓ No duplicate handles found
                                    <br />
                                    ✓ Required columns present
                                    <br />✓ File format and size are valid
                                  </Text>
                                </div>
                              )}

                              {/* Destinations Preview */}
                              {previewData.destinations.headers.length > 0 && (
                                <div className="mb-6">
                                  <div className="flex items-center gap-2 mb-3">
                                    <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                                    <Text className="font-semibold text-gray-800">
                                      Destinations Sheet Preview
                                    </Text>
                                    <span className="bg-blue-100 text-blue-800 text-xs font-medium px-2 py-1 rounded-full">
                                      {previewData.destinations.rows.length}{" "}
                                      rows
                                    </span>
                                  </div>
                                  <div className="border border-gray-200 rounded-lg overflow-hidden shadow-sm">
                                    <div className="overflow-x-auto">
                                      <table className="min-w-full divide-y divide-gray-200">
                                        <thead className="bg-gradient-to-r from-blue-50 to-indigo-50">
                                          <tr>
                                            {previewData.destinations.headers.map(
                                              (
                                                header: string,
                                                index: number
                                              ) => (
                                                <th
                                                  key={index}
                                                  className="px-4 py-3 text-left text-xs font-semibold text-blue-800 uppercase tracking-wider"
                                                >
                                                  {header}
                                                </th>
                                              )
                                            )}
                                          </tr>
                                        </thead>
                                        <tbody className="bg-white divide-y divide-gray-100">
                                          {previewData.destinations.rows.map(
                                            (row: any, rowIndex: number) => (
                                              <tr
                                                key={rowIndex}
                                                className={
                                                  rowIndex % 2 === 0
                                                    ? "bg-white hover:bg-blue-25"
                                                    : "bg-blue-25 hover:bg-blue-50"
                                                }
                                              >
                                                {Array.from({
                                                  length:
                                                    previewData.destinations
                                                      .headers.length,
                                                }).map((_, colIndex) => (
                                                  <td
                                                    key={colIndex}
                                                    className="px-4 py-3 text-sm text-gray-700 font-medium"
                                                  >
                                                    {row[colIndex] !== undefined
                                                      ? String(row[colIndex])
                                                      : "-"}
                                                  </td>
                                                ))}
                                              </tr>
                                            )
                                          )}
                                        </tbody>
                                      </table>
                                    </div>
                                  </div>
                                  {previewData.destinations.rows.length > 0 && (
                                    <div className="mt-3 flex items-center gap-2">
                                      <div className="w-2 h-2 bg-blue-400 rounded-full"></div>
                                      <Text className="text-blue-600 text-sm font-medium">
                                        Showing first{" "}
                                        {previewData.destinations.rows.length}{" "}
                                        rows • Ready for import
                                      </Text>
                                    </div>
                                  )}
                                </div>
                              )}

                              {/* FAQs Preview */}
                              {previewData.faqs.headers.length > 0 && (
                                <div className="mb-6">
                                  <div className="flex items-center gap-2 mb-3">
                                    <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                                    <Text className="font-semibold text-gray-800">
                                      FAQs Sheet Preview
                                    </Text>
                                    <span className="bg-green-100 text-green-800 text-xs font-medium px-2 py-1 rounded-full">
                                      {previewData.faqs.rows.length} rows
                                    </span>
                                  </div>
                                  <div className="border border-gray-200 rounded-lg overflow-hidden shadow-sm">
                                    <div className="overflow-x-auto">
                                      <table className="min-w-full divide-y divide-gray-200">
                                        <thead className="bg-gradient-to-r from-green-50 to-emerald-50">
                                          <tr>
                                            {previewData.faqs.headers.map(
                                              (
                                                header: string,
                                                index: number
                                              ) => (
                                                <th
                                                  key={index}
                                                  className="px-4 py-3 text-left text-xs font-semibold text-green-800 uppercase tracking-wider"
                                                >
                                                  {header}
                                                </th>
                                              )
                                            )}
                                          </tr>
                                        </thead>
                                        <tbody className="bg-white divide-y divide-gray-100">
                                          {previewData.faqs.rows.map(
                                            (row: any, rowIndex: number) => (
                                              <tr
                                                key={rowIndex}
                                                className={
                                                  rowIndex % 2 === 0
                                                    ? "bg-white hover:bg-green-25"
                                                    : "bg-green-25 hover:bg-green-50"
                                                }
                                              >
                                                {Array.from({
                                                  length:
                                                    previewData.faqs.headers
                                                      .length,
                                                }).map((_, colIndex) => (
                                                  <td
                                                    key={colIndex}
                                                    className="px-4 py-3 text-sm text-gray-700 font-medium"
                                                  >
                                                    {row[colIndex] !== undefined
                                                      ? String(row[colIndex])
                                                      : "-"}
                                                  </td>
                                                ))}
                                              </tr>
                                            )
                                          )}
                                        </tbody>
                                      </table>
                                    </div>
                                  </div>
                                  {previewData.faqs.rows.length > 0 && (
                                    <div className="mt-3 flex items-center gap-2">
                                      <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                                      <Text className="text-green-600 text-sm font-medium">
                                        Showing first{" "}
                                        {previewData.faqs.rows.length} rows •
                                        Ready for import
                                      </Text>
                                    </div>
                                  )}
                                </div>
                              )}

                              {previewData.destinations.headers.length === 0 &&
                                previewData.faqs.headers.length === 0 &&
                                (previewError ? (
                                  <Text className="text-red-500">
                                    {previewError}
                                  </Text>
                                ) : (
                                  <Text className="text-gray-500">
                                    Loading preview...
                                  </Text>
                                ))}
                            </div>
                          ) : (
                            <Text className="text-gray-500">
                              {filePreview.error ||
                                "Preview not available for this file type."}
                            </Text>
                          )}
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </>
            ) : (
              <>
                {/* Success Header */}
                <div className="bg-gradient-to-r from-green-50 to-emerald-50 p-6 rounded-lg border border-green-200 shadow-sm">
                  <div className="flex items-center gap-4">
                    <div className="flex-shrink-0 w-12 h-12 bg-green-100 rounded-full flex items-center justify-center">
                      <CheckCircleIcon className="w-8 h-8 text-green-600" />
                    </div>
                    <div className="flex-1">
                      <Heading
                        level="h3"
                        className="text-xl font-semibold text-green-800 mb-1"
                      >
                        Import Completed Successfully!
                      </Heading>
                      <Text className="text-green-700">
                        Your destinations and FAQs have been imported and are
                        now available in the system.
                      </Text>
                    </div>
                  </div>
                </div>

                {/* Detailed Results */}
                <div className="bg-white p-6 rounded-lg border border-gray-200 shadow-sm">
                  <div className="flex flex-col gap-4">
                    <Heading
                      level="h3"
                      className="text-lg font-medium text-gray-800 mb-2"
                    >
                      Import Summary
                    </Heading>
                    <div className="flex flex-col gap-2">
                      {/* Destinations Results */}
                      <div className="flex items-center gap-2">
                        <CheckCircleIcon className="w-5 h-5 text-green-500" />
                        <Text>
                          Destinations imported:{" "}
                          {uploadResult.results.destinations?.successful ||
                            uploadResult.results.successful ||
                            0}
                        </Text>
                      </div>
                      {(uploadResult.results.destinations?.failed ||
                        uploadResult.results.failed ||
                        0) > 0 && (
                        <div className="flex items-center gap-2">
                          <XCircleIcon className="w-5 h-5 text-red-500" />
                          <Text>
                            Destinations failed:{" "}
                            {uploadResult.results.destinations?.failed ||
                              uploadResult.results.failed ||
                              0}
                          </Text>
                        </div>
                      )}

                      {/* FAQs Results */}
                      {uploadResult.results.faqs && (
                        <>
                          <div className="flex items-center gap-2">
                            <CheckCircleIcon className="w-5 h-5 text-green-500" />
                            <Text>
                              FAQs imported:{" "}
                              {uploadResult.results.faqs.successful || 0}
                            </Text>
                          </div>
                          {(uploadResult.results.faqs.failed || 0) > 0 && (
                            <div className="flex items-center gap-2">
                              <XCircleIcon className="w-5 h-5 text-red-500" />
                              <Text>
                                FAQs failed:{" "}
                                {uploadResult.results.faqs.failed || 0}
                              </Text>
                            </div>
                          )}
                        </>
                      )}

                      {/* Warnings */}
                      {uploadResult.results.warnings &&
                        uploadResult.results.warnings.length > 0 && (
                          <div className="mt-2">
                            <Text className="text-yellow-600 font-medium">
                              Warnings:
                            </Text>
                            {uploadResult.results.warnings.map(
                              (warning: string, index: number) => (
                                <Text
                                  key={index}
                                  className="text-yellow-600 text-sm ml-2"
                                >
                                  • {warning}
                                </Text>
                              )
                            )}
                          </div>
                        )}
                    </div>
                  </div>
                </div>

                {/* Display Destination Errors */}
                {((uploadResult.results.destinations?.errors &&
                  uploadResult.results.destinations.errors.length > 0) ||
                  (uploadResult.results.errors &&
                    uploadResult.results.errors.length > 0)) && (
                  <div className="bg-white p-6 rounded-lg border border-gray-200 shadow-sm">
                    <div className="flex flex-col gap-4">
                      <Heading level="h3" className="text-lg font-medium">
                        Destination Import Errors
                      </Heading>
                      <div className="flex flex-col gap-2">
                        {(
                          uploadResult.results.destinations?.errors ||
                          uploadResult.results.errors ||
                          []
                        ).map((error: any, index: number) => (
                          <div
                            key={index}
                            className="p-3 border border-gray-200 rounded-md"
                          >
                            <Text className="font-medium">Row {error.row}</Text>
                            <Text className="text-red-500">{error.error}</Text>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                )}

                {/* Display FAQ Errors */}
                {uploadResult.results.faqs?.errors &&
                  uploadResult.results.faqs.errors.length > 0 && (
                    <div className="bg-white p-6 rounded-lg border border-gray-200 shadow-sm">
                      <div className="flex flex-col gap-4">
                        <Heading level="h3" className="text-lg font-medium">
                          FAQ Import Errors
                        </Heading>
                        <div className="flex flex-col gap-2">
                          {uploadResult.results.faqs.errors.map(
                            (error: any, index: number) => (
                              <div
                                key={index}
                                className="p-3 border border-gray-200 rounded-md"
                              >
                                <Text className="font-medium">
                                  FAQ Row {error.row}
                                </Text>
                                <Text className="text-red-500">
                                  {error.error}
                                </Text>
                              </div>
                            )
                          )}
                        </div>
                      </div>
                    </div>
                  )}
              </>
            )}
          </div>

          <div className="flex-shrink-0 py-6 px-6 bg-gradient-to-r from-gray-50 to-gray-100 border-t border-gray-200">
            {!uploadResult ? (
              <div className="flex gap-3 justify-end">
                <Button
                  variant="secondary"
                  onClick={() => {
                    resetModalState();
                    onClose();
                  }}
                  className="px-6"
                >
                  Cancel
                </Button>
                <Button
                  variant="primary"
                  onClick={handleUpload}
                  disabled={
                    !file ||
                    isUploading ||
                    isValidating ||
                    validationErrors.length > 0 ||
                    duplicateHandles.length > 0
                  }
                  className={`flex items-center gap-2 px-6 py-2 ${
                    !file ||
                    isUploading ||
                    isValidating ||
                    validationErrors.length > 0 ||
                    duplicateHandles.length > 0
                      ? "bg-gray-400 cursor-not-allowed"
                      : "bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 shadow-lg"
                  }`}
                >
                  {isUploading ? (
                    <>
                      <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                      <span>Importing...</span>
                    </>
                  ) : (
                    <>
                      <UploadIcon className="w-4 h-4" />
                      <span>Import Data</span>
                    </>
                  )}
                </Button>
              </div>
            ) : (
              <div className="flex gap-3 justify-end">
                <Button
                  variant="secondary"
                  onClick={() => {
                    // Reset the upload result and file to allow re-upload
                    resetModalState();
                  }}
                  className="flex items-center gap-2 px-6"
                >
                  <UploadIcon className="w-4 h-4" />
                  Upload Another File
                </Button>
                <Button
                  variant="secondary"
                  onClick={() => {
                    resetModalState();
                    onClose();
                  }}
                  className="px-6"
                >
                  Close
                </Button>
                <Button
                  variant="primary"
                  onClick={handleViewDestinations}
                  className="flex items-center gap-2 px-6 bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 shadow-lg"
                >
                  <CheckCircleIcon className="w-4 h-4" />
                  View Destinations
                </Button>
              </div>
            )}
          </div>
        </FocusModal.Body>
      </FocusModal.Content>
    </FocusModal>
  );
};

export default BulkImportModal;
