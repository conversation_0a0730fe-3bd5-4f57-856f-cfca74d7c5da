import React from "react";
import { Tabs, Container, clx } from "@camped-ai/ui";
import { FileText, Hotel, HelpCircle, Settings, Sparkles, Image as ImageIcon } from "lucide-react";

export type TabId = "overview" | "hotels" | "faqs" | "bailey-ai" | "settings" | "media";

export interface Tab {
  id: TabId;
  label: string;
  icon: React.ComponentType<{ className?: string }>;
  count?: number;
}

interface DestinationTabsProps {
  activeTab: TabId;
  onTabChange: (tabId: TabId) => void;
  tabs?: Tab[];
  className?: string;
  sticky?: boolean;
}

const defaultTabs: Tab[] = [
  {
    id: "overview",
    label: "Overview",
    icon: FileText,
  },
  {
    id: "hotels",
    label: "Hotels",
    icon: Hotel,
  },
  {
    id: "faqs",
    label: "FAQs",
    icon: HelpCircle,
  },
  {
    id: "media",
    label: "Media",
    icon: ImageIcon,
  },
  {
    id: "bailey-ai",
    label: "Bailey AI",
    icon: Sparkles,
  },
  {
    id: "settings",
    label: "Settings",
    icon: Settings,
  },
];

const DestinationTabs: React.FC<DestinationTabsProps> = ({
  activeTab,
  onTabChange,
  tabs = defaultTabs,
  className,
  sticky = true,
}) => {
  return (
    <div className={clx("w-full bg-background", className)}>
      <Tabs value={activeTab} onValueChange={(v) => onTabChange(v as TabId)}>
        <Container>
          <Tabs.List className="grid w-full grid-cols-6">
            {tabs.map((tab) => {
              const Icon = tab.icon;
              return (
                <Tabs.Trigger
                  key={tab.id}
                  value={tab.id}
                  className="flex items-center gap-2"
                >
                  <Icon className="h-4 w-4" />
                  {tab.label}
                  {typeof tab.count === "number" ? (
                    <span className="ml-2 inline-flex items-center justify-center rounded-full bg-gray-200 px-2 py-0.5 text-xs text-gray-700">
                      {tab.count}
                    </span>
                  ) : null}
                </Tabs.Trigger>
              );
            })}
          </Tabs.List>
        </Container>
      </Tabs>
    </div>
  );
};

export default DestinationTabs;
