import React from "react";
import { clx } from "@camped-ai/ui";
import { TabId } from "./destination-tabs";

interface DestinationTabContentProps {
  activeTab: TabId;
  children: React.ReactNode;
  tabId: TabId;
  className?: string;
}

const DestinationTabContent: React.FC<DestinationTabContentProps> = ({
  activeTab,
  children,
  tabId,
  className,
}) => {
  const isActive = activeTab === tabId;

  if (!isActive) {
    return null;
  }

  return (
    <div
      role="tabpanel"
      id={`${tabId}-panel`}
      aria-labelledby={`${tabId}-tab`}
      className={clx(
        "w-full animate-in fade-in-0 duration-200",
        className
      )}
    >
      {children}
    </div>
  );
};

export default DestinationTabContent;
