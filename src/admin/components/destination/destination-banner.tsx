import React from "react";
import { <PERSON><PERSON>, <PERSON><PERSON>, Heading, Text } from "@camped-ai/ui";
import { MapPin, Globe, Image } from "lucide-react";
import { Buildings } from "@camped-ai/icons";
import { DestinationData } from "../../types";

interface DestinationBannerProps {
  destination: DestinationData | null;
  destinationImages: any[];
  activeHotelsCount: number;
  heroImage: string | null;
  onViewImages: () => void;
  getTranslatedContent: (field: string, content: string) => string;
}

const DestinationBanner: React.FC<DestinationBannerProps> = ({
  destination,
  destinationImages,
  activeHotelsCount,
  heroImage,
  onViewImages,
  getTranslatedContent,
}) => {
  return (
    <div className="w-full h-64 md:h-80 overflow-hidden rounded-lg rounded-b-none shadow-md flex flex-col justify-between p-4 md:p-6 relative bg-muted">
      {/* Hero background image */}
      {heroImage && (
        <>
          <img
            src={heroImage}
            alt="Hero background"
            className="absolute inset-0 w-full h-full object-cover"
          />
          {/* Dark overlay for better text readability */}
          <div className="absolute inset-0 bg-gradient-to-b from-black/70 to-black/30 dark:from-black/80 dark:to-black/40"></div>
        </>
      )}

      {/* Fallback background when no hero image */}
      {!heroImage && (
        <div className="absolute inset-0 bg-gradient-to-br from-blue-600 to-purple-700 dark:from-blue-700 dark:to-purple-800"></div>
      )}

      {/* Content overlay */}
      <div className="hero-content h-full flex flex-col justify-between relative">
        <div className="flex flex-col md:flex-row md:justify-between md:items-start gap-4">
          <div className="flex flex-col gap-2 min-w-0 flex-1">
            <div className="flex items-center gap-2 flex-wrap">
              <Badge
                color={destination?.is_active ? "green" : "grey"}
                className="text-xs font-medium px-3 py-1"
              >
                {destination?.is_active ? "Active" : "Inactive"}
              </Badge>
              {destination?.is_featured && (
                <Badge color="purple" className="text-xs font-medium px-3 py-1">
                  Featured
                </Badge>
              )}
            </div>
            <Heading
              level="h1"
              className="text-2xl md:text-4xl font-bold text-white drop-shadow-sm break-words"
            >
              {getTranslatedContent("name", destination?.name || "")}
            </Heading>
            <div className="flex flex-wrap items-center gap-2 md:gap-4 mt-2">
              {(getTranslatedContent("location", destination?.location || "") ||
                destination?.country) && (
                <div className="flex items-center gap-2 text-white/90 bg-black/20 dark:bg-white/20 px-3 py-1 rounded-full">
                  <MapPin className="w-4 h-4 flex-shrink-0" />
                  <Text className="text-white/90 text-sm">
                    {getTranslatedContent(
                      "location",
                      destination?.location || ""
                    ) ||
                      destination?.country ||
                      "Location not specified"}
                  </Text>
                </div>
              )}
            </div>
          </div>

          <div className="flex flex-wrap gap-2 md:gap-x-2 md:flex-nowrap">
            {/* View Images Button - Only show if destination has images */}
            {destinationImages && destinationImages.length > 0 && (
              <Button
                variant="secondary"
                size="small"
                className="bg-white/90 hover:bg-white dark:bg-gray-800/90 dark:hover:bg-gray-800 dark:text-white"
                onClick={onViewImages}
              >
                <Image className="w-4 h-4 md:mr-1" />
                <span className="hidden md:inline">
                  View Images ({destinationImages.length})
                </span>
                <span className="md:hidden">Images</span>
              </Button>
            )}
          </div>
        </div>

        <div className="flex flex-wrap gap-2 md:gap-4 mt-auto">
          <div className="bg-white/10 dark:bg-black/20 backdrop-blur-sm px-3 md:px-4 py-2 rounded-full flex items-center gap-2">
            <Globe className="w-4 h-4 text-white flex-shrink-0" />
            <Text className="text-white font-medium text-sm md:text-base">
              {destination?.country || "Country not specified"}
            </Text>
          </div>

          <div className="bg-white/10 dark:bg-black/20 backdrop-blur-sm px-3 md:px-4 py-2 rounded-full flex items-center gap-2">
            <Buildings className="w-4 h-4 text-white flex-shrink-0" />
            <Text className="text-white font-medium text-sm md:text-base">
              {activeHotelsCount} Active Hotels
            </Text>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DestinationBanner;
