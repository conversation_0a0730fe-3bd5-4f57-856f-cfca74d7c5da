import React from "react";
import { siteConfig } from "../../../config/site";
import { Helmet } from "react-helmet";
import { useEffect } from "react";

type Props = {
  suffix?: string;
  titleOverride?: string;
  faviconOverride?: string;
};

/**
 * AdminPageHelmet
 * Renders a consistent document title and favicon for admin pages.
 * Default title: `${siteConfig.title} | Concierge Bookings`
 * Default favicon: `siteConfig.favicon`
 *
 * You can override with:
 * - suffix: changes the text after the site title. Defaults to "Concierge Bookings".
 * - titleOverride: sets the entire title.
 * - faviconOverride: sets a different favicon URL for the page.
 */
const AdminPageHelmet: React.FC<Props> = ({
  suffix = "",
  titleOverride,
  faviconOverride,
}) => {
  const title = suffix ? `${siteConfig.title} | ${suffix}` : siteConfig.title;
  const favicon = faviconOverride ?? siteConfig.favicon;

  useEffect(() => {
    // Remove all existing favicon tags
    const existingIcons = document.querySelectorAll("link[rel='icon']");
    existingIcons.forEach((el) => el.parentNode?.removeChild(el));

    // Create and append your custom favicon
    const link = document.createElement("link");
    link.rel = "icon";
    link.type = "image/png";
    link.href =
      "https://www.powderbyrne.com/wp-content/uploads/2024/08/cropped-FavIcon-32x32.png";
    document.head.appendChild(link);
  }, []);

  return (
    <Helmet>
      <link
        rel="icon"
        type="image/png"
        href="https://www.powderbyrne.com/wp-content/uploads/2024/08/cropped-FavIcon-32x32.png"
      />
      <title>{title}</title>
      {favicon ? <link rel="icon" type="image/png" href={favicon} /> : null}
    </Helmet>
  );
};

export default AdminPageHelmet;
