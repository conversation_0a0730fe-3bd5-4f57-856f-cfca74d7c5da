import React, { useState, useEffect } from "react";
import { Button, FocusModal, Text, Input, Badge } from "@camped-ai/ui";
import { Calculator, Save, AlertCircle } from "lucide-react";
import { toast } from "@camped-ai/ui";

interface MarginManagementModalProps {
  isOpen: boolean;
  onClose: () => void;
  addOn: {
    id: string;
    title: string;
    pricing: {
      base_cost: number;
      base_currency: string;
      total_selling_price: number;
      calculated_margin: number;
    };
    supplier: {
      supplier_name: string;
    };
  };
  onSave: (addOnId: string, newMargin: number) => Promise<void>;
}

export const MarginManagementModal: React.FC<MarginManagementModalProps> = ({
  isOpen,
  onClose,
  addOn,
  onSave,
}) => {
  const [marginPercentage, setMarginPercentage] = useState<string>("");
  const [sellingPrice, setSellingPrice] = useState<string>("");
  const [isCalculatingFromMargin, setIsCalculatingFromMargin] = useState(true);
  const [isSaving, setIsSaving] = useState(false);

  // Initialize values when modal opens
  useEffect(() => {
    if (isOpen && addOn) {
      setMarginPercentage(addOn.pricing.calculated_margin.toFixed(2));
      setSellingPrice(addOn.pricing.total_selling_price.toFixed(2));
    }
  }, [isOpen, addOn]);

  // Calculate selling price from margin using formula: price = Net cost / (1 - margin/100)
  const calculateSellingPriceFromMargin = (margin: number) => {
    const baseCost = addOn.pricing.base_cost;
    if (margin >= 100) {
      return 0; // Invalid margin, return 0 to indicate error
    }
    return baseCost / (1 - margin / 100);
  };

  // Calculate margin from selling price using reverse formula: margin = (1 - cost/price) * 100
  const calculateMarginFromSellingPrice = (price: number) => {
    const baseCost = addOn.pricing.base_cost;
    if (baseCost === 0 || price === 0) return 0;
    return (1 - baseCost / price) * 100;
  };

  // Handle margin percentage change
  const handleMarginChange = (value: string) => {
    setMarginPercentage(value);
    setIsCalculatingFromMargin(true);
    
    const margin = parseFloat(value);
    if (!isNaN(margin)) {
      const newSellingPrice = calculateSellingPriceFromMargin(margin);
      setSellingPrice(newSellingPrice.toFixed(2));
    }
  };

  // Handle selling price change
  const handleSellingPriceChange = (value: string) => {
    setSellingPrice(value);
    setIsCalculatingFromMargin(false);
    
    const price = parseFloat(value);
    if (!isNaN(price)) {
      const newMargin = calculateMarginFromSellingPrice(price);
      setMarginPercentage(newMargin.toFixed(2));
    }
  };

  // Handle save
  const handleSave = async () => {
    try {
      setIsSaving(true);
      
      const margin = parseFloat(marginPercentage);
      if (isNaN(margin)) {
        toast.error("Please enter a valid margin percentage");
        return;
      }

      if (margin < 0) {
        toast.error("Margin percentage cannot be negative");
        return;
      }

      if (margin >= 100) {
        toast.error("Margin percentage must be less than 100%");
        return;
      }

      await onSave(addOn.id, margin);
      toast.success("Margin updated successfully");
      onClose();
    } catch (error) {
      toast.error("Failed to update margin");
      console.error("Error updating margin:", error);
    } finally {
      setIsSaving(false);
    }
  };

  // Calculate profit amount
  const profitAmount = () => {
    const price = parseFloat(sellingPrice);
    const baseCost = addOn.pricing.base_cost;
    if (isNaN(price)) return 0;
    return price - baseCost;
  };

  // Format currency
  const formatCurrency = (amount: number, currency: string = "CHF") => {
    return new Intl.NumberFormat("en-CH", {
      style: "currency",
      currency: currency,
    }).format(amount);
  };

  if (!addOn) return null;

  return (
    <FocusModal open={isOpen} onOpenChange={onClose}>
      <FocusModal.Content>
        <FocusModal.Header>
          <FocusModal.Title className="flex items-center gap-2">
            <Calculator size={20} />
            Manage Margin - {addOn.title}
          </FocusModal.Title>
          <FocusModal.Description>
            Adjust the margin percentage or selling price for this add-on.
            Changes will be applied immediately.
          </FocusModal.Description>
        </FocusModal.Header>

        <FocusModal.Body>
          <div className="flex flex-col gap-6">
            {/* Add-on Info */}
            <div className="p-4 bg-gray-50 rounded-lg">
              <Text className="font-medium mb-2">Add-on Information</Text>
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <Text className="text-gray-600">Supplier</Text>
                  <Text className="font-medium">{addOn.supplier.supplier_name}</Text>
                </div>
                <div>
                  <Text className="text-gray-600">Base Cost</Text>
                  <Text className="font-medium">
                    {formatCurrency(addOn.pricing.base_cost, addOn.pricing.base_currency)}
                  </Text>
                </div>
              </div>
            </div>

            {/* Margin Calculation */}
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium mb-2">
                  Margin Percentage (%)
                </label>
                <Input
                  type="number"
                  step="0.01"
                  min="0"
                  value={marginPercentage}
                  onChange={(e) => handleMarginChange(e.target.value)}
                  placeholder="Enter margin percentage"
                  className={isCalculatingFromMargin ? "ring-2 ring-blue-500" : ""}
                />
                {isCalculatingFromMargin && (
                  <Text className="text-xs text-blue-600 mt-1">
                    Calculating selling price from margin
                  </Text>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium mb-2">
                  Selling Price ({addOn.pricing.base_currency})
                </label>
                <Input
                  type="number"
                  step="0.01"
                  min="0"
                  value={sellingPrice}
                  onChange={(e) => handleSellingPriceChange(e.target.value)}
                  placeholder="Enter selling price"
                  className={!isCalculatingFromMargin ? "ring-2 ring-blue-500" : ""}
                />
                {!isCalculatingFromMargin && (
                  <Text className="text-xs text-blue-600 mt-1">
                    Calculating margin from selling price
                  </Text>
                )}
              </div>
            </div>

            {/* Calculation Summary */}
            <div className="p-4 bg-blue-50 rounded-lg">
              <Text className="font-medium mb-3">Calculation Summary</Text>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <Text className="text-gray-600">Base Cost:</Text>
                  <Text className="font-medium">
                    {formatCurrency(addOn.pricing.base_cost, addOn.pricing.base_currency)}
                  </Text>
                </div>
                <div className="flex justify-between">
                  <Text className="text-gray-600">Selling Price:</Text>
                  <Text className="font-medium">
                    {formatCurrency(parseFloat(sellingPrice) || 0, addOn.pricing.base_currency)}
                  </Text>
                </div>
                <div className="flex justify-between">
                  <Text className="text-gray-600">Profit Amount:</Text>
                  <Text className="font-medium text-green-600">
                    {formatCurrency(profitAmount(), addOn.pricing.base_currency)}
                  </Text>
                </div>
                <div className="flex justify-between border-t pt-2">
                  <Text className="font-medium">Margin Percentage:</Text>
                  <Badge variant="success">
                    {parseFloat(marginPercentage) || 0}%
                  </Badge>
                </div>
              </div>
            </div>

            {/* Validation Warning */}
            {parseFloat(marginPercentage) < 0 && (
              <div className="flex items-center gap-2 p-3 bg-red-50 rounded-lg">
                <AlertCircle size={16} className="text-red-600" />
                <Text className="text-red-800 text-sm">
                  Negative margin means selling below cost price
                </Text>
              </div>
            )}
          </div>
        </FocusModal.Body>

        <FocusModal.Footer>
          <Button
            variant="outline"
            onClick={onClose}
            disabled={isSaving}
          >
            Cancel
          </Button>
          <Button
            onClick={handleSave}
            disabled={isSaving || isNaN(parseFloat(marginPercentage))}
          >
            {isSaving ? (
              <>
                <Save size={16} className="animate-pulse" />
                Saving...
              </>
            ) : (
              <>
                <Save size={16} />
                Save Margin
              </>
            )}
          </Button>
        </FocusModal.Footer>
      </FocusModal.Content>
    </FocusModal>
  );
};
