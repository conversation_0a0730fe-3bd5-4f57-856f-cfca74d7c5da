import React from "react";
import { Badge } from "@camped-ai/ui";
import { getUserStatus } from "../../utils/userStatus";

interface UserStatusBadgeProps {
  user: any;
  showTooltip?: boolean;
}

/**
 * Badge component to display user active/inactive status
 */
const UserStatusBadge: React.FC<UserStatusBadgeProps> = ({
  user,
  showTooltip = false,
}) => {
  const { isActive, deactivatedAt, deactivationReason } = getUserStatus(user);

  const badge = isActive ? (
    <Badge className="text-xs px-2 py-1 rounded-full bg-green-100 text-green-800 border border-green-200 dark:bg-green-500/20 dark:text-green-300 dark:border-green-500/40">
      Active
    </Badge>
  ) : (
    <Badge className="text-xs px-2 py-1 rounded-full bg-red-100 text-red-800 border border-red-200 dark:bg-red-500/20 dark:text-red-300 dark:border-red-500/40">
      Inactive
    </Badge>
  );

  if (showTooltip && !isActive && deactivatedAt) {
    const deactivatedDate = new Date(deactivatedAt).toLocaleDateString();
    const tooltipText = deactivationReason
      ? `Deactivated on ${deactivatedDate}: ${deactivationReason}`
      : `Deactivated on ${deactivatedDate}`;

    return <div title={tooltipText}>{badge}</div>;
  }

  return badge;
};

export default UserStatusBadge;
