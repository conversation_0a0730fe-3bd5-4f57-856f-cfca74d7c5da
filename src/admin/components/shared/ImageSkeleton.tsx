import React from "react";
import { clx } from "@camped-ai/ui";

interface ImageSkeletonProps {
  className?: string;
  variant?: "card" | "list";
}

const ImageSkeleton: React.FC<ImageSkeletonProps> = ({
  className,
  variant = "card",
}) => {
  return (
    <div
      className={clx(
        "bg-gradient-to-br from-gray-100 via-gray-200 to-gray-300 dark:from-gray-800 dark:via-gray-700 dark:to-gray-600 animate-pulse",
        className
      )}
    />
  );
};

export default ImageSkeleton;
