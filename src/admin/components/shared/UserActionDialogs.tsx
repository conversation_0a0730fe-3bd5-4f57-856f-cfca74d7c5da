import React, { useState } from "react";
import { <PERSON>er, But<PERSON>, Label, Textarea } from "@camped-ai/ui";
import { getUserStatus } from "../../utils/userStatus";

interface DeactivateUserDialogProps {
  user: any;
  open: boolean;
  onClose: () => void;
  onConfirm: (reason?: string) => void;
  loading?: boolean;
}

/**
 * Dialog for confirming user deactivation
 */
export const DeactivateUserDialog: React.FC<DeactivateUserDialogProps> = ({
  user,
  open,
  onClose,
  onConfirm,
  loading = false,
}) => {
  const [reason, setReason] = useState("");

  const handleConfirm = () => {
    onConfirm(reason.trim() || undefined);
    setReason(""); // Reset for next time
  };

  const handleClose = () => {
    setReason(""); // Reset on close
    onClose();
  };

  return (
    <Drawer open={open} onOpenChange={handleClose}>
      <Drawer.Content>
        <Drawer.Header>
          <Drawer.Title>Deactivate User</Drawer.Title>
          <Drawer.Description>
            Are you sure you want to deactivate{" "}
            <strong>{user?.email || "this user"}</strong>?
            <br />
            <br />
            They will not be able to log in until reactivated. All their data
            and role assignments will be preserved.
          </Drawer.Description>
        </Drawer.Header>

        <Drawer.Body className="space-y-4 p-6">
          <div>
            <Label htmlFor="deactivation-reason">Reason (Optional)</Label>
            <Textarea
              id="deactivation-reason"
              value={reason}
              onChange={(e) => setReason(e.target.value)}
              placeholder="Enter reason for deactivation..."
              className="mt-1"
              rows={3}
            />
          </div>
        </Drawer.Body>

        <Drawer.Footer>
          <Button variant="secondary" onClick={handleClose} disabled={loading}>
            Cancel
          </Button>
          <Button variant="danger" onClick={handleConfirm} disabled={loading}>
            {loading ? "Deactivating..." : "Deactivate User"}
          </Button>
        </Drawer.Footer>
      </Drawer.Content>
    </Drawer>
  );
};

interface ActivateUserDialogProps {
  user: any;
  open: boolean;
  onClose: () => void;
  onConfirm: () => void;
  loading?: boolean;
}

/**
 * Dialog for confirming user activation
 */
export const ActivateUserDialog: React.FC<ActivateUserDialogProps> = ({
  user,
  open,
  onClose,
  onConfirm,
  loading = false,
}) => {
  const { deactivationReason, deactivatedAt } = getUserStatus(user);

  return (
    <Drawer open={open} onOpenChange={onClose}>
      <Drawer.Content>
        <Drawer.Header>
          <Drawer.Title>Activate User</Drawer.Title>
          <Drawer.Description>
            Are you sure you want to activate{" "}
            <strong>{user?.email || "this user"}</strong>?
            <br />
            <br />
            They will be able to log in and access the system according to their
            role permissions.
          </Drawer.Description>
        </Drawer.Header>

        <Drawer.Body className="p-6">
          {deactivationReason && (
            <div className="bg-muted p-3 rounded-md">
              <Label className="text-sm font-medium">
                Previous Deactivation Reason:
              </Label>
              <p className="text-sm text-muted-foreground mt-1">
                {deactivationReason}
              </p>
              {deactivatedAt && (
                <p className="text-xs text-muted-foreground mt-1">
                  Deactivated on: {new Date(deactivatedAt).toLocaleDateString()}
                </p>
              )}
            </div>
          )}
        </Drawer.Body>

        <Drawer.Footer>
          <Button variant="secondary" onClick={onClose} disabled={loading}>
            Cancel
          </Button>
          <Button variant="primary" onClick={onConfirm} disabled={loading}>
            {loading ? "Activating..." : "Activate User"}
          </Button>
        </Drawer.Footer>
      </Drawer.Content>
    </Drawer>
  );
};
