import React, { useState, useRef, useEffect, useCallback } from "react";
import { clx } from "@camped-ai/ui";
import { useImagePerformance } from "../../hooks/useImagePerformance";

interface OptimizedImageProps {
  src?: string | null;
  alt: string;
  className?: string;
  fallbackSrc?: string;
  placeholder?: React.ReactNode;
  priority?: boolean;
  onLoad?: () => void;
  onError?: () => void;
}

const OptimizedImage: React.FC<OptimizedImageProps> = ({
  src,
  alt,
  className,
  fallbackSrc,
  placeholder,
  priority = false,
  onLoad,
  onError,
}) => {
  const [imageState, setImageState] = useState<"loading" | "loaded" | "error">(
    "loading"
  );
  const [currentSrc, setCurrentSrc] = useState<string | null>(null);
  const [isInView, setIsInView] = useState(priority);
  const imgRef = useRef<HTMLImageElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const { startImageLoad, endImageLoad } = useImagePerformance();

  // Intersection Observer for lazy loading
  useEffect(() => {
    if (priority || !containerRef.current) return;

    const observer = new IntersectionObserver(
      (entries) => {
        const [entry] = entries;
        if (entry.isIntersecting) {
          setIsInView(true);
          observer.disconnect();
        }
      },
      {
        rootMargin: "50px", // Start loading 50px before entering viewport
        threshold: 0.1,
      }
    );

    observer.observe(containerRef.current);

    return () => observer.disconnect();
  }, [priority]);

  // Set image source when in view
  useEffect(() => {
    if (isInView) {
      if (src && src.trim() !== "") {
        setCurrentSrc(src);
        startImageLoad(src);
      } else if (fallbackSrc) {
        // If no valid src, use fallback immediately
        setCurrentSrc(fallbackSrc);
        startImageLoad(fallbackSrc);
      } else {
        // No src and no fallback, show error state
        setImageState("error");
      }
    }
  }, [isInView, src, fallbackSrc, startImageLoad]);

  const handleImageLoad = useCallback(() => {
    setImageState("loaded");
    if (currentSrc) {
      endImageLoad(currentSrc, true);
    }
    onLoad?.();
  }, [onLoad, currentSrc, endImageLoad]);

  const handleImageError = useCallback(() => {
    if (currentSrc) {
      endImageLoad(currentSrc, false);
    }

    // If the current source is not the fallback, try the fallback
    if (currentSrc !== fallbackSrc && fallbackSrc) {
      setCurrentSrc(fallbackSrc);
      setImageState("loading"); // Reset to loading state for fallback
      if (fallbackSrc) {
        startImageLoad(fallbackSrc);
      }
    } else {
      // If fallback also failed or no fallback, show error state
      setImageState("error");
      onError?.();
    }
  }, [currentSrc, fallbackSrc, onError, endImageLoad, startImageLoad]);

  // Default placeholder component - simple gradient
  const defaultPlaceholder = (
    <div className="absolute inset-0 bg-gradient-to-br from-gray-100 via-gray-200 to-gray-300 dark:from-gray-800 dark:via-gray-700 dark:to-gray-600 animate-pulse" />
  );

  // Error state placeholder - simple gradient
  const errorPlaceholder = (
    <div className="absolute inset-0 bg-gradient-to-br from-gray-200 via-gray-300 to-gray-400 dark:from-gray-700 dark:via-gray-800 dark:to-gray-900" />
  );

  return (
    <div
      ref={containerRef}
      className={clx("relative overflow-hidden", className)}
    >
      {/* Show placeholder while loading or if no image */}
      {(imageState === "loading" || (!currentSrc && placeholder)) &&
        placeholder}

      {/* Show default placeholder if loading and no custom placeholder */}
      {imageState === "loading" &&
        currentSrc &&
        !placeholder &&
        defaultPlaceholder}

      {/* Show error placeholder if image failed to load */}
      {imageState === "error" && currentSrc === fallbackSrc && errorPlaceholder}

      {/* Actual image */}
      {currentSrc && (
        <img
          ref={imgRef}
          src={currentSrc}
          alt={alt}
          className={clx(
            "w-full h-full object-cover transition-opacity duration-300",
            imageState === "loaded" ? "opacity-100" : "opacity-0"
          )}
          onLoad={handleImageLoad}
          onError={handleImageError}
          loading={priority ? "eager" : "lazy"}
          decoding="async"
        />
      )}
    </div>
  );
};

export default OptimizedImage;
