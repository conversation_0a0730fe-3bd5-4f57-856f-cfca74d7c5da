import { useEffect, useState } from "react";
import { Text, Badge } from "@camped-ai/ui";

const STATUS_OPTIONS = [
  { value: null, label: "All Statuses", key: "all" },
  { value: "featured", label: "Featured", key: "featured" },
  { value: "not-featured", label: "Not Featured", key: "not-featured" },
  { value: "active", label: "Active", key: "active" },
  { value: "inactive", label: "Inactive", key: "inactive" },
];

const STATUS_COLORS: Record<
  string,
  { lightBg: string; lightText: string; darkBg: string; darkText: string }
> = {
  all: {
    lightBg: "#F3F4F6",
    lightText: "#4B5563",
    darkBg: "#1F2937",
    darkText: "#D1D5DB",
  },
  featured: {
    lightBg: "#FFFBEB",
    lightText: "#92400E",
    darkBg: "#78350F",
    darkText: "#FFF7D6",
  },
  "not-featured": {
    lightBg: "#F3F4F6",
    lightText: "#4B5563",
    darkBg: "#1F2937",
    darkText: "#D1D5DB",
  },
  active: {
    lightBg: "#ECFDF5",
    lightText: "#065F46",
    darkBg: "#064E3B",
    darkText: "#A7F3D0",
  },
  inactive: {
    lightBg: "#FEF2F2",
    lightText: "#991B1B",
    darkBg: "#4B1F1F",
    darkText: "#FECACA",
  },
};

export function StatusFilterBadges({
  filters,
  updateStatusFilter,
}: {
  filters: { status: string | null };
  updateStatusFilter: (v: string | null) => void;
}) {
  const [isDark, setIsDark] = useState(false);

  useEffect(() => {
    setIsDark(document.documentElement.classList.contains("dark"));
  }, []);

  return (
    <div className="flex items-center gap-2">
      <Text className="text-sm text-ui-fg-subtle">Status:</Text>
      <div className="flex gap-2">
        {STATUS_OPTIONS.map(({ value, label, key }) => {
          const isSelected = filters.status === value;
          const { lightBg, lightText, darkBg, darkText } = STATUS_COLORS[key];
          const bg = isDark ? darkBg : lightBg;
          const text = isDark ? darkText : lightText;

          return (
            <button
              key={key}
              onClick={() => updateStatusFilter(value)}
              style={
                isSelected
                  ? {
                      backgroundColor: bg,
                      color: text,
                      borderColor: text,
                    }
                  : {}
              }
              className={`
                px-3 py-1 rounded-full text-sm border
                ${
                  isSelected
                    ? "" // all styling via inline style
                    : "bg-ui-bg-base text-ui-fg-subtle border-ui-border-base hover:bg-ui-bg-subtle"
                }
              `}
            >
              {label}
            </button>
          );
        })}
      </div>
    </div>
  );
}

/**
 * Individual status badge components for use in destination cards/tables
 *
 * Usage Examples:
 *
 * // Individual badges
 * <FeaturedStatusBadge isFeatured={destination.is_featured} size="small" />
 * <ActiveStatusBadge isActive={destination.is_active} size="default" />
 *
 * // Combined badges (recommended for most use cases)
 * <StatusBadges
 *   isActive={destination.is_active}
 *   isFeatured={destination.is_featured}
 *   size="small"
 *   showBoth={true}
 * />
 */
export function FeaturedStatusBadge({
  isFeatured,
  size = "default",
}: {
  isFeatured: boolean;
  size?: "small" | "default";
}) {
  const [isDark, setIsDark] = useState(false);

  useEffect(() => {
    setIsDark(document.documentElement.classList.contains("dark"));
  }, []);

  if (!isFeatured) return null;

  const colors = STATUS_COLORS.featured;
  const bg = isDark ? colors.darkBg : colors.lightBg;
  const text = isDark ? colors.darkText : colors.lightText;

  return (
    <Badge
      style={{
        backgroundColor: bg,
        color: text,
        borderColor: text,
      }}
      className={`
        border rounded-full font-medium
        ${size === "small" ? "px-2 py-0.5 text-xs" : "px-3 py-1 text-sm"}
      `}
    >
      Featured
    </Badge>
  );
}

export function ActiveStatusBadge({
  isActive,
  size = "default",
}: {
  isActive: boolean;
  size?: "small" | "default";
}) {
  const [isDark, setIsDark] = useState(false);

  useEffect(() => {
    setIsDark(document.documentElement.classList.contains("dark"));
  }, []);

  const statusKey = isActive ? "active" : "inactive";
  const colors = STATUS_COLORS[statusKey];
  const bg = isDark ? colors.darkBg : colors.lightBg;
  const text = isDark ? colors.darkText : colors.lightText;

  return (
    <Badge
      style={{
        backgroundColor: bg,
        color: text,
        borderColor: text,
      }}
      className={`
        border rounded-full font-medium
        ${size === "small" ? "px-2 py-0.5 text-xs" : "px-3 py-1 text-sm"}
      `}
    >
      {isActive ? "Active" : "Inactive"}
    </Badge>
  );
}

// Combined status badges component for showing both active and featured status
export function StatusBadges({
  isActive,
  isFeatured,
  size = "default",
  showBoth = true,
}: {
  isActive: boolean;
  isFeatured: boolean;
  size?: "small" | "default";
  showBoth?: boolean;
}) {
  return (
    <div className="flex gap-1">
      {showBoth && <ActiveStatusBadge isActive={isActive} size={size} />}
      <FeaturedStatusBadge isFeatured={isFeatured} size={size} />
    </div>
  );
}
