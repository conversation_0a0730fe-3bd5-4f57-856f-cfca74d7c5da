import React from "react";
import { Container } from "@camped-ai/ui";

/**
 * Reusable skeleton loading component for hotel pricing tables
 * Displays animated placeholder content with shimmer effect while data is loading
 * Matches the actual UI structure of HotelPricingManager with tabs and ComprehensivePricingTable
 */
const HotelPricingTableSkeleton: React.FC = () => {
  return (
    <Container>
      <div>
        <style>{`
          @keyframes shimmer {
            0% {
              background-position: -100% 0;
            }
            100% {
              background-position: 100% 0;
            }
          }
          .shimmer {
            background: linear-gradient(
              90deg,
              hsl(var(--muted)) 0%,
              hsl(var(--muted)) 40%,
              hsl(var(--muted-foreground) / 0.08) 50%,
              hsl(var(--muted)) 60%,
              hsl(var(--muted)) 100%
            );
            background-size: 200% 100%;
            animation: shimmer 3.5s ease-in-out infinite;
          }
        `}</style>
        {/* Main Header Section - matches HotelPricingManager header */}
        <div className="mb-6">
          <div className="flex items-center gap-4 mb-2">
            {/* Back button */}
            <div className="h-9 w-9 shimmer rounded-md"></div>
            {/* Hotel name */}
            <div className="h-8 shimmer rounded-md w-48"></div>
          </div>
        </div>

        {/* Tabs Section - matches HotelPricingManager tabs */}
        <div className="w-full mb-4">
          <div className="flex gap-1 mb-4">
            {/* Tab triggers */}
            <div className="h-10 shimmer rounded-md w-20 px-4 py-2"></div>
            <div className="h-10 shimmer opacity-50 rounded-md w-20 px-4 py-2"></div>
            <div className="h-10 shimmer opacity-50 rounded-md w-32 px-4 py-2"></div>
            <div className="h-10 shimmer opacity-50 rounded-md w-24 px-4 py-2"></div>
          </div>
        </div>

        {/* Pricing Tab Content Header - matches ComprehensivePricingTable header */}
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4 mb-6">
          <div>
            <div className="h-7 shimmer rounded-md mb-1 w-64"></div>
            <div className="h-4 shimmer rounded-md w-80"></div>
          </div>
          <div className="flex flex-wrap items-center gap-2">
            {/* Currency selector */}
            <div className="h-9 shimmer rounded-md w-24"></div>
            {/* Bulk Update button */}
            <div className="h-9 shimmer rounded-md w-28"></div>
            {/* Add Season button */}
            <div className="h-9 shimmer rounded-md w-28"></div>
            {/* Save All button */}
            <div className="h-9 shimmer rounded-md w-20"></div>
          </div>
        </div>

        {/* Filter Section - matches ComprehensivePricingTable filters */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-3 mb-6 p-4 bg-muted/50 rounded-lg border border-border">
          <div>
            <div className="h-4 shimmer rounded-md mb-1 w-16"></div>
            <div className="h-9 shimmer rounded-md border border-input"></div>
          </div>
          <div>
            <div className="h-4 shimmer rounded-md mb-1 w-20"></div>
            <div className="h-9 shimmer rounded-md border border-input"></div>
          </div>
          <div>
            <div className="h-4 shimmer rounded-md mb-1 w-24"></div>
            <div className="h-9 shimmer rounded-md border border-input"></div>
          </div>
          <div>
            <div className="h-4 shimmer rounded-md mb-1 w-16"></div>
            <div className="h-9 shimmer rounded-md border border-input"></div>
          </div>
        </div>

        {/* Table Section - matches ComprehensivePricingTable table structure */}
        <div className="overflow-x-auto rounded-lg border border-border shadow-sm">
          <table className="min-w-full divide-y divide-border table-fixed">
            {/* Table Header - matches actual column headers */}
            <thead className="bg-muted/50">
              <tr>
                <th className="px-3 py-2 text-left text-xs font-medium text-muted-foreground uppercase tracking-wide w-32">
                  <div className="h-3 shimmer rounded-md w-12"></div>
                </th>
                <th className="px-3 py-2 text-left text-xs font-medium text-muted-foreground uppercase tracking-wide w-32">
                  <div className="h-3 shimmer rounded-md w-16"></div>
                </th>
                <th className="px-3 py-2 text-left text-xs font-medium text-muted-foreground uppercase tracking-wide w-20">
                  <div className="h-3 shimmer rounded-md w-14"></div>
                </th>
                <th className="px-3 py-2 text-left text-xs font-medium text-muted-foreground uppercase tracking-wide w-28">
                  <div className="h-3 shimmer rounded-md w-16"></div>
                </th>
                {/* Weekday columns - matches actual weekday headers */}
                {["MON", "TUE", "WED", "THU", "FRI", "SAT", "SUN"].map(
                  (day) => (
                    <th
                      key={day}
                      className="px-2 py-2 text-center text-xs font-medium text-muted-foreground uppercase tracking-wide"
                    >
                      <div className="flex flex-col items-center gap-0.5">
                        <div className="h-3 shimmer rounded-md w-6"></div>
                        <div className="h-2 shimmer rounded-md w-4"></div>
                      </div>
                    </th>
                  )
                )}
              </tr>
            </thead>

            {/* Table Body - matches actual pricing row structure */}
            <tbody className="bg-background divide-y divide-border">
              {/* Base Price Row - matches actual base pricing structure */}
              <tr className="bg-background">
                <td className="px-3 py-3 whitespace-nowrap text-sm w-32">
                  <div className="h-3 shimmer rounded-md w-16"></div>
                </td>
                <td className="px-3 py-3 whitespace-nowrap text-sm w-32">
                  <div className="h-3 shimmer rounded-md w-24"></div>
                </td>
                <td className="px-2 py-3 whitespace-nowrap text-sm w-20">
                  <div className="h-3 shimmer rounded-md w-10"></div>
                </td>
                <td className="px-3 py-3 whitespace-nowrap text-sm w-28">
                  <div className="h-3 shimmer rounded-md w-20"></div>
                </td>
                {/* Price input skeletons - matches actual input styling */}
                {Array.from({ length: 7 }).map((_, index) => (
                  <td key={index} className="px-2 py-3 whitespace-nowrap">
                    <div className="flex justify-center">
                      <div className="h-8 shimmer rounded border w-20"></div>
                    </div>
                  </td>
                ))}
              </tr>

              {/* Seasonal Period Row - matches actual seasonal row with calendar icon */}
              <tr className="bg-muted/50">
                <td className="px-3 py-3 whitespace-nowrap text-sm w-32">
                  <div className="flex items-center gap-1">
                    <div className="flex flex-col min-w-0">
                      <div className="h-3 shimmer rounded-md w-16 mb-1"></div>
                      <div className="h-2 shimmer rounded-md w-12"></div>
                    </div>
                  </div>
                </td>
                <td className="px-3 py-3 whitespace-nowrap text-sm w-32">
                  <div className="h-3 shimmer rounded-md w-24"></div>
                </td>
                <td className="px-2 py-3 whitespace-nowrap text-sm w-20">
                  <div className="h-3 shimmer rounded-md w-10"></div>
                </td>
                <td className="px-3 py-3 whitespace-nowrap text-sm w-28">
                  <div className="h-3 shimmer rounded-md w-20"></div>
                </td>
                {/* Price input skeletons */}
                {Array.from({ length: 7 }).map((_, index) => (
                  <td key={index} className="px-2 py-3 whitespace-nowrap">
                    <div className="flex justify-center">
                      <div className="h-8 shimmer rounded border w-20"></div>
                    </div>
                  </td>
                ))}
              </tr>

              {/* Additional pricing rows - mix of base and seasonal */}
              {Array.from({ length: 6 }).map((_, rowIndex) => (
                <tr
                  key={rowIndex}
                  className={
                    rowIndex % 2 === 0 ? "bg-background" : "bg-muted/50"
                  }
                >
                  <td className="px-3 py-3 whitespace-nowrap text-sm w-32">
                    {rowIndex % 3 === 0 ? (
                      // Base price row
                      <div className="h-3 shimmer rounded-md w-16"></div>
                    ) : (
                      // Seasonal row with icon
                      <div className="flex items-center gap-1">
                        <div className="flex flex-col min-w-0">
                          <div className="h-3 shimmer rounded-md w-14 mb-1"></div>
                          <div className="h-2 shimmer rounded-md w-10"></div>
                        </div>
                      </div>
                    )}
                  </td>
                  <td className="px-3 py-3 whitespace-nowrap text-sm w-32">
                    <div className="h-3 shimmer rounded-md w-24"></div>
                  </td>
                  <td className="px-2 py-3 whitespace-nowrap text-sm w-20">
                    <div className="h-3 shimmer rounded-md w-10"></div>
                  </td>
                  <td className="px-3 py-3 whitespace-nowrap text-sm w-28">
                    <div className="h-3 shimmer rounded-md w-20"></div>
                  </td>
                  {/* Price input skeletons */}
                  {Array.from({ length: 7 }).map((_, index) => (
                    <td key={index} className="px-2 py-3 whitespace-nowrap">
                      <div className="flex justify-center">
                        <div className="h-8 shimmer rounded border w-20"></div>
                      </div>
                    </td>
                  ))}
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </Container>
  );
};

export default HotelPricingTableSkeleton;
