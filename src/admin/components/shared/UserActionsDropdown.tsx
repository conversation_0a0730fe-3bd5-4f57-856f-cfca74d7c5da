import React, { useState } from "react";
import { Dropdown<PERSON>enu, I<PERSON><PERSON><PERSON>on } from "@camped-ai/ui";
import { MoreH<PERSON>zontal, UserX, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON> } from "lucide-react";
import { useRbac } from "../../hooks/use-rbac";
import {
  useDeactivateUser,
  useActivateUser,
} from "../../hooks/useUserManagement";
import { isUserActive } from "../../utils/userStatus";
import { DeactivateUserDialog, ActivateUserDialog } from "./UserActionDialogs";

interface UserActionsDropdownProps {
  user: any;
  onViewDetails?: (user: any) => void;
  showViewDetails?: boolean;
}

/**
 * Dropdown menu with user management actions
 */
const UserActionsDropdown: React.FC<UserActionsDropdownProps> = ({
  user,
  onViewDetails,
  showViewDetails = true,
}) => {
  const { hasPermission, currentUser } = useRbac();
  const deactivateUser = useDeactivateUser();
  const activateUser = useActivateUser();

  const [showDeactivateDialog, setShowDeactivateDialog] = useState(false);
  const [showActivateDialog, setShowActivateDialog] = useState(false);

  const userIsActive = isUserActive(user);
  const canEditUsers = hasPermission("user_management:edit");
  const canActivateUsers = hasPermission("user_management:activate");
  const canDeactivateUsers = hasPermission("user_management:deactivate");
  const isCurrentUser = currentUser?.id === user?.id;

  // Don't show actions if user has no permissions
  if (
    !canEditUsers &&
    !canActivateUsers &&
    !canDeactivateUsers &&
    !showViewDetails
  ) {
    return null;
  }

  const handleDeactivate = (reason?: string) => {
    deactivateUser.mutate(
      { userId: user.id, reason },
      {
        onSuccess: () => {
          setShowDeactivateDialog(false);
        },
      }
    );
  };

  const handleActivate = () => {
    activateUser.mutate(user.id, {
      onSuccess: () => {
        setShowActivateDialog(false);
      },
    });
  };

  return (
    <>
      <DropdownMenu>
        <DropdownMenu.Trigger asChild>
          <IconButton size="small">
            <MoreHorizontal className="h-4 w-4" />
          </IconButton>
        </DropdownMenu.Trigger>
        <DropdownMenu.Content align="end">
          {showViewDetails && onViewDetails && (
            <DropdownMenu.Item onClick={() => onViewDetails(user)}>
              <Eye className="h-4 w-4 mr-2" />
              View Details
            </DropdownMenu.Item>
          )}

          {!isCurrentUser && (
            <>
              {userIsActive && canDeactivateUsers && (
                <DropdownMenu.Item
                  onClick={() => setShowDeactivateDialog(true)}
                >
                  <UserX className="h-4 w-4 mr-2" />
                  Deactivate User
                </DropdownMenu.Item>
              )}
              {!userIsActive && canActivateUsers && (
                <DropdownMenu.Item onClick={() => setShowActivateDialog(true)}>
                  <UserCheck className="h-4 w-4 mr-2" />
                  Activate User
                </DropdownMenu.Item>
              )}
            </>
          )}

          {isCurrentUser && (
            <DropdownMenu.Item disabled className="text-muted-foreground">
              <UserX className="h-4 w-4 mr-2" />
              Cannot modify own account
            </DropdownMenu.Item>
          )}
        </DropdownMenu.Content>
      </DropdownMenu>

      {/* Deactivation Dialog */}
      <DeactivateUserDialog
        user={user}
        open={showDeactivateDialog}
        onClose={() => setShowDeactivateDialog(false)}
        onConfirm={handleDeactivate}
        loading={deactivateUser.isPending}
      />

      {/* Activation Dialog */}
      <ActivateUserDialog
        user={user}
        open={showActivateDialog}
        onClose={() => setShowActivateDialog(false)}
        onConfirm={handleActivate}
        loading={activateUser.isPending}
      />
    </>
  );
};

export default UserActionsDropdown;
