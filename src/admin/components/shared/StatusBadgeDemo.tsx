import { Container, Heading, Text } from "@camped-ai/ui";
import { 
  FeaturedStatusBadge, 
  ActiveStatusBadge, 
  StatusBadges 
} from "./StatusFilterBadges";

/**
 * Demo component showing how to use the status badge components
 * This can be used as a reference or imported into other components
 */
export default function StatusBadgeDemo() {
  return (
    <Container className="space-y-8 p-6">
      <div>
        <Heading level="h2" className="text-xl mb-4">
          Status Badge Components Demo
        </Heading>
        <Text className="text-muted-foreground mb-6">
          Examples of how to use the status badge components for destinations
        </Text>
      </div>

      {/* Individual Badge Examples */}
      <div className="space-y-4">
        <Heading level="h3" className="text-lg">
          Individual Badge Components
        </Heading>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Featured Badge Examples */}
          <div className="space-y-3">
            <Text className="font-medium">Featured Status Badge</Text>
            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <FeaturedStatusBadge isFeatured={true} size="default" />
                <Text className="text-sm text-muted-foreground">Default size, featured</Text>
              </div>
              <div className="flex items-center gap-2">
                <FeaturedStatusBadge isFeatured={true} size="small" />
                <Text className="text-sm text-muted-foreground">Small size, featured</Text>
              </div>
              <div className="flex items-center gap-2">
                <FeaturedStatusBadge isFeatured={false} size="default" />
                <Text className="text-sm text-muted-foreground">Not featured (hidden)</Text>
              </div>
            </div>
          </div>

          {/* Active Badge Examples */}
          <div className="space-y-3">
            <Text className="font-medium">Active Status Badge</Text>
            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <ActiveStatusBadge isActive={true} size="default" />
                <Text className="text-sm text-muted-foreground">Default size, active</Text>
              </div>
              <div className="flex items-center gap-2">
                <ActiveStatusBadge isActive={false} size="default" />
                <Text className="text-sm text-muted-foreground">Default size, inactive</Text>
              </div>
              <div className="flex items-center gap-2">
                <ActiveStatusBadge isActive={true} size="small" />
                <Text className="text-sm text-muted-foreground">Small size, active</Text>
              </div>
              <div className="flex items-center gap-2">
                <ActiveStatusBadge isActive={false} size="small" />
                <Text className="text-sm text-muted-foreground">Small size, inactive</Text>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Combined Badge Examples */}
      <div className="space-y-4">
        <Heading level="h3" className="text-lg">
          Combined Status Badges
        </Heading>
        
        <div className="space-y-3">
          <div className="flex items-center gap-2">
            <StatusBadges isActive={true} isFeatured={true} size="default" />
            <Text className="text-sm text-muted-foreground">Active & Featured</Text>
          </div>
          <div className="flex items-center gap-2">
            <StatusBadges isActive={true} isFeatured={false} size="default" />
            <Text className="text-sm text-muted-foreground">Active only</Text>
          </div>
          <div className="flex items-center gap-2">
            <StatusBadges isActive={false} isFeatured={true} size="default" />
            <Text className="text-sm text-muted-foreground">Inactive but Featured</Text>
          </div>
          <div className="flex items-center gap-2">
            <StatusBadges isActive={false} isFeatured={false} size="default" />
            <Text className="text-sm text-muted-foreground">Inactive & Not Featured</Text>
          </div>
          <div className="flex items-center gap-2">
            <StatusBadges isActive={true} isFeatured={true} size="small" />
            <Text className="text-sm text-muted-foreground">Small size - Active & Featured</Text>
          </div>
          <div className="flex items-center gap-2">
            <StatusBadges isActive={false} isFeatured={true} size="small" showBoth={false} />
            <Text className="text-sm text-muted-foreground">Featured only (showBoth=false)</Text>
          </div>
        </div>
      </div>

      {/* Usage Examples */}
      <div className="space-y-4">
        <Heading level="h3" className="text-lg">
          Usage Examples
        </Heading>
        
        <div className="bg-muted/50 p-4 rounded-lg">
          <Text className="font-medium mb-2">Code Examples:</Text>
          <div className="space-y-2 text-sm font-mono bg-background p-3 rounded border">
            <div>{`// Individual badges`}</div>
            <div>{`<FeaturedStatusBadge isFeatured={destination.is_featured} size="small" />`}</div>
            <div>{`<ActiveStatusBadge isActive={destination.is_active} size="default" />`}</div>
            <div className="mt-2">{`// Combined badges`}</div>
            <div>{`<StatusBadges`}</div>
            <div>{`  isActive={destination.is_active}`}</div>
            <div>{`  isFeatured={destination.is_featured}`}</div>
            <div>{`  size="small"`}</div>
            <div>{`  showBoth={true}`}</div>
            <div>{`/>`}</div>
          </div>
        </div>
      </div>
    </Container>
  );
}
