import { Text, clx } from "@camped-ai/ui";
import { formatDateRange } from "../../lib/format-date-range";

type DateRangeDisplayProps = {
  startsAt?: Date | string | null;
  endsAt?: Date | string | null;
  showTime?: boolean;
};

export const DateRangeDisplay = ({
  startsAt,
  endsAt,
  showTime = false,
}: DateRangeDisplayProps) => {
  const startDate = startsAt ? new Date(startsAt) : null;
  const endDate = endsAt ? new Date(endsAt) : null;

  return (
    <div className="flex items-center gap-x-3 rounded-md px-3 py-1.5 shadow-elevation-card-rest">
      <Bar date={startDate} />
      <div>
        <Text weight="plus" size="small">
          Date Range
        </Text>
        <Text size="small" className="tabular-nums">
          {startDate && endDate
            ? formatDateRange(startDate, endDate)
            : "-"}
        </Text>
      </div>
    </div>
  );
};

const Bar = ({ date }: { date: Date | null }) => {
  const now = new Date();

  const isDateInFuture = date && date > now;

  return (
    <div
      className={clx("bg-ui-tag-neutral-icon h-8 w-1 rounded-full", {
        "bg-ui-tag-orange-icon": isDateInFuture,
      })}
    />
  );
};
