import React, { useState, useEffect } from "react";
import { Button, Input, Textarea, Heading, Text, toast } from "@camped-ai/ui";
import { Controller, useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { Search, Users } from "lucide-react";

const customerSchema = z.object({
  guest_name: z.string().min(1, "Guest name is required"),
  guest_email: z.string().email("Invalid email address"),
  guest_phone: z.string().optional(),
  special_requests: z.string().optional(),
  travelers: z.object({
    adults: z.array(z.object({ name: z.string() })),
    children: z.array(z.object({ name: z.string(), age: z.number() })),
    infants: z.array(z.object({ name: z.string(), age: z.number() })),
  }),
});

type CustomerFormData = z.infer<typeof customerSchema>;

interface CustomerInformationModalProps {
  open: boolean;
  onClose: () => void;
  onNext: (customerData: CustomerFormData) => void;
  onBack: () => void;
  availabilityData: any;
  initialFormData?: CustomerFormData | null;
}

const CustomerInformationModal: React.FC<CustomerInformationModalProps> = ({
  open,
  onClose,
  onNext,
  onBack,
  availabilityData,
  initialFormData,
}) => {
  const [customerSearchQuery, setCustomerSearchQuery] = useState("");
  const [searchResults, setSearchResults] = useState<any[]>([]);
  const [isSearching, setIsSearching] = useState(false);

  // Create default values, using persisted data if available
  const getDefaultValues = (): CustomerFormData => {
    const baseDefaults = {
      guest_name: "",
      guest_email: "",
      guest_phone: "",
      special_requests: "",
      travelers: {
        adults: Array.from({ length: availabilityData?.adults || 0 }, () => ({
          name: "",
        })),
        children: Array.from(
          { length: availabilityData?.children || 0 },
          (_, i) => ({
            name: "",
            age: availabilityData?.child_ages?.[i]?.age || 10,
          })
        ),
        infants: Array.from({ length: availabilityData?.infants || 0 }, () => ({
          name: "",
          age: 0,
        })),
      },
    };

    // If we have persisted data, merge it with defaults
    if (initialFormData) {
      return {
        ...baseDefaults,
        ...initialFormData,
        travelers: {
          ...baseDefaults.travelers,
          ...initialFormData.travelers,
        },
      };
    }

    return baseDefaults;
  };

  const form = useForm<CustomerFormData>({
    resolver: zodResolver(customerSchema),
    defaultValues: getDefaultValues(),
  });

  // Reset form values when initialFormData changes (when navigating back)
  useEffect(() => {
    const newValues = getDefaultValues();
    form.reset(newValues);
  }, [initialFormData, availabilityData]);

  // Search for customers
  const searchCustomers = async (query: string) => {
    if (!query.trim()) {
      setSearchResults([]);
      return;
    }

    try {
      setIsSearching(true);
      const response = await fetch(
        `/admin/customers?q=${encodeURIComponent(query)}&limit=5`
      );

      if (!response.ok) {
        throw new Error("Failed to search customers");
      }

      const data = await response.json();
      setSearchResults(data.customers || []);
    } catch (error) {
      console.error("Error searching customers:", error);
      toast.error("Error", {
        description: "Failed to search customers",
      });
      setSearchResults([]);
    } finally {
      setIsSearching(false);
    }
  };

  // Select a customer from search results
  const selectCustomer = (customer: any) => {
    form.setValue("guest_name", `${customer.first_name} ${customer.last_name}`);
    form.setValue("guest_email", customer.email);
    form.setValue("guest_phone", customer.phone || "");

    // Clear search results
    setSearchResults([]);
    setCustomerSearchQuery("");
  };

  const onSubmit = (data: CustomerFormData) => {
    onNext(data);
  };

  return (
    <div className="p-6">
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        {/* Customer Search Card */}
        <div className="border rounded-lg p-6">
          <div className="space-y-3">
            <label className="text-sm font-medium flex items-center">
              <Search className="w-4 h-4 mr-2" />
              Search Existing Customer
            </label>
            <div className="flex gap-2">
              <div className="flex-1">
                <Input
                  placeholder="Search by name or email"
                  value={customerSearchQuery}
                  onChange={(e) => setCustomerSearchQuery(e.target.value)}
                />
              </div>
              <Button
                type="button"
                variant="secondary"
                onClick={() => searchCustomers(customerSearchQuery)}
                disabled={isSearching}
              >
                {isSearching ? "Searching..." : "Search"}
              </Button>
            </div>
            <Text className="text-xs text-gray-500">
              Search for an existing customer or enter details below to create a
              new one
            </Text>

            {/* Search Results */}
            {searchResults.length > 0 && (
              <div className="mt-2 border rounded-md overflow-hidden">
                <div className=" px-4 py-2 text-sm font-medium">
                  Search Results
                </div>
                <div className="divide-y">
                  {searchResults.map((customer) => (
                    <div
                      key={customer.id}
                      className="p-3 cursor-pointer flex justify-between items-center"
                      onClick={() => selectCustomer(customer)}
                    >
                      <div>
                        <div className="font-medium">
                          {customer.first_name} {customer.last_name}
                        </div>
                        <div className="text-sm text-gray-500">
                          {customer.email}
                        </div>
                      </div>
                      <Button size="small" variant="secondary">
                        Select
                      </Button>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Customer Details Card */}
        <div className="border rounded-lg p-6">
          <div className="space-y-4">
            <Heading level="h3" className="text-lg font-medium">
              Primary Guest Information
            </Heading>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <label className="text-sm font-medium">Guest Name *</label>
                <Controller
                  control={form.control}
                  name="guest_name"
                  render={({ field }) => (
                    <>
                      <Input {...field} placeholder="John Doe" />
                      {form.formState.errors.guest_name && (
                        <p className="text-sm text-red-500">
                          {form.formState.errors.guest_name.message}
                        </p>
                      )}
                    </>
                  )}
                />
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium">Email *</label>
                <Controller
                  control={form.control}
                  name="guest_email"
                  render={({ field }) => (
                    <>
                      <Input {...field} placeholder="<EMAIL>" />
                      {form.formState.errors.guest_email && (
                        <p className="text-sm text-red-500">
                          {form.formState.errors.guest_email.message}
                        </p>
                      )}
                    </>
                  )}
                />
              </div>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">Phone</label>
              <Controller
                control={form.control}
                name="guest_phone"
                render={({ field }) => (
                  <Input {...field} placeholder="****** 456 7890" />
                )}
              />
            </div>
          </div>
        </div>

        {/* Travelers Information Card */}
        <div className="border rounded-lg p-6">
          <div className="space-y-4">
            <div className="flex items-center">
              <Users className="w-5 h-5 mr-2 text-gray-600" />
              <Heading level="h3" className="text-lg font-medium">
                Travelers Information
              </Heading>
            </div>
            <Text className="text-sm text-gray-500">
              Enter details for all travelers
            </Text>

            {/* Adults */}
            {availabilityData && availabilityData.adults > 0 && (
              <div className="space-y-3">
                <Heading level="h3" className="font-medium text-gray-700">
                  Adults ({availabilityData.adults})
                </Heading>
                {Array.from({ length: availabilityData.adults }).map(
                  (_, index) => (
                    <div
                      key={`adult-${index}`}
                      className="p-3 border rounded-md"
                    >
                      <label className="text-sm font-medium mb-2 block">
                        Adult {index + 1} - Full Name
                      </label>
                      <Controller
                        control={form.control}
                        name={`travelers.adults.${index}.name`}
                        render={({ field }) => (
                          <Input {...field} placeholder="Full Name" />
                        )}
                      />
                    </div>
                  )
                )}
              </div>
            )}

            {/* Children */}
            {availabilityData && availabilityData.children > 0 && (
              <div className="space-y-3">
                <Heading level="h3" className="font-medium text-gray-700">
                  Children ({availabilityData.children})
                </Heading>
                {Array.from({ length: availabilityData.children }).map(
                  (_, index) => (
                    <div
                      key={`child-${index}`}
                      className="p-3 border border-gray-200 rounded-md"
                    >
                      <div className="grid grid-cols-2 gap-3">
                        <div>
                          <label className="text-sm font-medium mb-2 block">
                            Child {index + 1} - Full Name
                          </label>
                          <Controller
                            control={form.control}
                            name={`travelers.children.${index}.name`}
                            render={({ field }) => (
                              <Input {...field} placeholder="Full Name" />
                            )}
                          />
                        </div>
                        <div>
                          <label className="text-sm font-medium mb-2 block">
                            Age
                          </label>
                          <Controller
                            control={form.control}
                            name={`travelers.children.${index}.age`}
                            render={({ field }) => (
                              <Input
                                type="number"
                                min="2"
                                max="17"
                                {...field}
                                onChange={(e) =>
                                  field.onChange(parseInt(e.target.value) || 10)
                                }
                                value={field.value || 10}
                              />
                            )}
                          />
                        </div>
                      </div>
                    </div>
                  )
                )}
              </div>
            )}

            {/* Infants */}
            {availabilityData && availabilityData.infants > 0 && (
              <div className="space-y-3">
                <Heading level="h3" className="font-medium text-gray-700">
                  Infants ({availabilityData.infants})
                </Heading>
                {Array.from({ length: availabilityData.infants }).map(
                  (_, index) => (
                    <div
                      key={`infant-${index}`}
                      className="p-3 border border-gray-200 rounded-md"
                    >
                      <div className="grid grid-cols-2 gap-3">
                        <div>
                          <label className="text-sm font-medium mb-2 block">
                            Infant {index + 1} - Full Name
                          </label>
                          <Controller
                            control={form.control}
                            name={`travelers.infants.${index}.name`}
                            render={({ field }) => (
                              <Input {...field} placeholder="Full Name" />
                            )}
                          />
                        </div>
                        <div>
                          <label className="text-sm font-medium mb-2 block">
                            Age (months)
                          </label>
                          <Controller
                            control={form.control}
                            name={`travelers.infants.${index}.age`}
                            render={({ field }) => (
                              <Input
                                type="number"
                                min="0"
                                max="23"
                                {...field}
                                onChange={(e) =>
                                  field.onChange(parseInt(e.target.value) || 0)
                                }
                                value={field.value || 0}
                              />
                            )}
                          />
                        </div>
                      </div>
                    </div>
                  )
                )}
              </div>
            )}
          </div>
        </div>

        {/* Special Requests Card */}
        <div className="border rounded-lg p-6">
          <div className="space-y-2">
            <label className="text-sm font-medium">Special Requests</label>
            <Controller
              control={form.control}
              name="special_requests"
              render={({ field }) => (
                <Textarea
                  {...field}
                  placeholder="Any special requests or requirements"
                  rows={3}
                />
              )}
            />
          </div>
        </div>
      </form>

      <div className="flex justify-between items-center pt-6 border-t">
        <Button variant="secondary" onClick={onBack}>
          Back
        </Button>
        <div className="flex space-x-3">
          <Button variant="secondary" onClick={onClose}>
            Cancel
          </Button>
          <Button onClick={form.handleSubmit(onSubmit)}>
            Continue to Add-ons
          </Button>
        </div>
      </div>
    </div>
  );
};

export default CustomerInformationModal;
