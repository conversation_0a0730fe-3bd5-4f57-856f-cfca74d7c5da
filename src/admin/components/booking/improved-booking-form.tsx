import React, { useState, useEffect } from "react";
import {
  Input,
  Button,
  Select,
  DatePicker,
  Heading,
  Text,
  Container,
  FocusModal,
  toast,
} from "@camped-ai/ui";
import { Controller, useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { format } from "date-fns";
import { Users, Hotel } from "lucide-react";
import CustomerInformationModal from "./customer-information-modal";
import BookingConfirmationModal from "./booking-confirmation-modal";
import { AddOnSelectionStep, SelectedAddOn } from "./add-on-selection";

const availabilitySchema = z.object({
  hotel_id: z.string().min(1, "Hotel is required"),
  check_in_date: z.date(),
  check_out_date: z.date(),
  adults: z.number().min(1, "At least 1 adult is required"),
  children: z.number().default(0),
  infants: z.number().default(0),
  board_type: z.string().default("none"),
  child_ages: z.array(z.object({ age: z.number() })).default([]),
});

type AvailabilityFormData = z.infer<typeof availabilitySchema>;

interface ImprovedBookingFormProps {
  bookingId?: string | null;
  isEdit?: boolean;
}

const ImprovedBookingForm: React.FC<ImprovedBookingFormProps> = ({
  isEdit = false,
}) => {
  const [hotels, setHotels] = useState<any[]>([]);
  const [isCheckingAvailability, setIsCheckingAvailability] = useState(false);
  const [availabilityResult, setAvailabilityResult] = useState<any>(null);
  const [selectedRoom, setSelectedRoom] = useState<any>(null);
  const [customerData, setCustomerData] = useState<any>(null);

  // Single modal state with step management
  const [showBookingModal, setShowBookingModal] = useState(false);
  const [currentStep, setCurrentStep] = useState<
    "customer" | "addons" | "confirmation"
  >("customer");

  // Add-on selection state
  const [selectedAddOns, setSelectedAddOns] = useState<SelectedAddOn[]>([]);

  // Customer form data persistence state
  const [persistedCustomerFormData, setPersistedCustomerFormData] =
    useState<any>(null);

  const form = useForm<AvailabilityFormData>({
    resolver: zodResolver(availabilitySchema),
    defaultValues: {
      hotel_id: "",
      check_in_date: new Date(),
      check_out_date: new Date(Date.now() + 24 * 60 * 60 * 1000),
      adults: 2,
      children: 0,
      infants: 0,
      board_type: "none",
      child_ages: [],
    },
  });

  const watchChildren = form.watch("children");

  // Fetch hotels
  const fetchHotels = async () => {
    try {
      const response = await fetch("/admin/hotel-management/hotels");
      if (!response.ok) throw new Error("Failed to fetch hotels");
      const data = await response.json();
      setHotels(data.hotels || []);
    } catch (error) {
      console.error("Error fetching hotels:", error);
      toast.error("Error", { description: "Failed to fetch hotels" });
    }
  };

  useEffect(() => {
    fetchHotels();
  }, []);

  // Update child ages when children count changes
  useEffect(() => {
    const currentChildAges = form.getValues("child_ages") || [];
    let newChildAges = [...currentChildAges];

    if (newChildAges.length < watchChildren) {
      while (newChildAges.length < watchChildren) {
        newChildAges.push({ age: 10 });
      }
    } else if (newChildAges.length > watchChildren) {
      newChildAges = newChildAges.slice(0, watchChildren);
    }

    form.setValue("child_ages", newChildAges);
  }, [watchChildren, form]);

  const checkAvailability = async (data: AvailabilityFormData) => {
    try {
      setIsCheckingAvailability(true);
      setAvailabilityResult(null);

      const formattedCheckIn = format(data.check_in_date, "yyyy-MM-dd");
      const formattedCheckOut = format(data.check_out_date, "yyyy-MM-dd");
      const childAgesParam = encodeURIComponent(
        JSON.stringify(data.child_ages)
      );

      const response = await fetch(
        `/admin/hotel-management/hotels/${data.hotel_id}/availability?check_in=${formattedCheckIn}&check_out=${formattedCheckOut}&adults=${data.adults}&children=${data.children}&infants=${data.infants}&board_type=${data.board_type}&include_unavailable=true&child_ages=${childAgesParam}`
      );

      if (!response.ok) {
        throw new Error("Failed to check availability");
      }

      const result = await response.json();
      setAvailabilityResult(result);

      if (!result.available_rooms || result.available_rooms.length === 0) {
        toast.error("No Availability", {
          description: "No rooms are available for the selected dates",
        });
      } else {
        const availableCount = result.available_rooms.filter(
          (room: any) => room.available
        ).length;
        toast.success("Availability Found", {
          description: `${availableCount} room types are available for the selected dates`,
        });
      }
    } catch (error) {
      console.error("Error checking availability:", error);
      toast.error("Error", {
        description: "Failed to check availability",
      });
    } finally {
      setIsCheckingAvailability(false);
    }
  };

  const handleCustomerNext = (data: any) => {
    setCustomerData(data);
    setPersistedCustomerFormData(data); // Persist the form data
    setCurrentStep("addons");
  };

  const handleAddOnsSelected = (addOns: SelectedAddOn[]) => {
    setSelectedAddOns(addOns);
    setCurrentStep("confirmation");
  };

  const handleAddOnBack = () => {
    setCurrentStep("customer");
  };

  const handleCloseAllModals = () => {
    setShowBookingModal(false);
    setCurrentStep("customer");
    setCustomerData(null);
    setSelectedAddOns([]);
    setPersistedCustomerFormData(null); // Clear persisted data when closing
  };

  const handleBackToAddOns = () => {
    setCurrentStep("addons");
  };

  return (
    <Container>
      <div className="space-y-8">
        {/* Header */}
        {/* <div className="text-center">
          <Heading level="h1" className="text-3xl font-bold text-gray-900 mb-4">
            {isEdit ? "Edit Booking" : "Create New Booking"}
          </Heading>
          <Text className="text-lg text-gray-600">
            {isEdit 
              ? "Update your booking details"
              : "Check availability and select your room"
            }
          </Text>
        </div> */}

        {/* Availability Check Form */}
        <div className="rounded-lg border border-border bg-card p-6">
          <div className="mb-6">
            <div className="flex items-center mb-2">
              <Heading level="h2" className="text-xl font-semibold">
                Create New Booking
              </Heading>
            </div>
            <Text className="text-muted-foreground">
              Select hotel, dates, and occupancy details to find available rooms
            </Text>
          </div>

          <form
            onSubmit={form.handleSubmit(checkAvailability)}
            className="space-y-6"
          >
            {/* Hotel Selection */}
            <div className="space-y-2">
              <label className="text-sm font-medium flex items-center text-foreground">
                <Hotel className="w-4 h-4 mr-2" />
                Hotel
              </label>
              <Controller
                control={form.control}
                name="hotel_id"
                render={({ field }) => (
                  <>
                    <Select value={field.value} onValueChange={field.onChange}>
                      <Select.Trigger>
                        <Select.Value placeholder="Select a hotel" />
                      </Select.Trigger>
                      <Select.Content>
                        {hotels.map((hotel) => (
                          <Select.Item key={hotel.id} value={hotel.id}>
                            {hotel.name}
                          </Select.Item>
                        ))}
                      </Select.Content>
                    </Select>
                    {form.formState.errors.hotel_id && (
                      <p className="text-sm text-destructive">
                        {form.formState.errors.hotel_id.message}
                      </p>
                    )}
                  </>
                )}
              />
            </div>

            {/* Dates */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <label className="text-sm font-medium text-foreground">
                  Check-in Date
                </label>
                <Controller
                  control={form.control}
                  name="check_in_date"
                  render={({ field }) => (
                    <>
                      <DatePicker
                        value={field.value}
                        onChange={field.onChange}
                      />
                      {form.formState.errors.check_in_date && (
                        <p className="text-sm text-destructive">
                          {form.formState.errors.check_in_date.message}
                        </p>
                      )}
                    </>
                  )}
                />
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium text-foreground">
                  Check-out Date
                </label>
                <Controller
                  control={form.control}
                  name="check_out_date"
                  render={({ field }) => (
                    <>
                      <DatePicker
                        value={field.value}
                        onChange={field.onChange}
                      />
                      {form.formState.errors.check_out_date && (
                        <p className="text-sm text-destructive">
                          {form.formState.errors.check_out_date.message}
                        </p>
                      )}
                    </>
                  )}
                />
              </div>
            </div>

            {/* Occupancy */}
            <div className="space-y-4">
              <label className="text-sm font-medium flex items-center text-foreground">
                <Users className="w-4 h-4 mr-2" />
                Occupancy Details
              </label>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="space-y-2">
                  <label className="text-sm font-medium text-foreground">
                    Adults
                  </label>
                  <Controller
                    control={form.control}
                    name="adults"
                    render={({ field }) => (
                      <>
                        <Input
                          type="number"
                          min={1}
                          {...field}
                          onChange={(e) =>
                            field.onChange(parseInt(e.target.value) || 1)
                          }
                        />
                        {form.formState.errors.adults && (
                          <p className="text-sm text-destructive">
                            {form.formState.errors.adults.message}
                          </p>
                        )}
                      </>
                    )}
                  />
                </div>

                <div className="space-y-2">
                  <label className="text-sm font-medium text-foreground">
                    Children
                  </label>
                  <Controller
                    control={form.control}
                    name="children"
                    render={({ field }) => (
                      <>
                        <Input
                          type="number"
                          min={0}
                          {...field}
                          onChange={(e) =>
                            field.onChange(parseInt(e.target.value) || 0)
                          }
                        />
                        {form.formState.errors.children && (
                          <p className="text-sm text-destructive">
                            {form.formState.errors.children.message}
                          </p>
                        )}
                      </>
                    )}
                  />
                </div>

                <div className="space-y-2">
                  <label className="text-sm font-medium text-foreground">
                    Infants
                  </label>
                  <Controller
                    control={form.control}
                    name="infants"
                    render={({ field }) => (
                      <>
                        <Input
                          type="number"
                          min={0}
                          {...field}
                          onChange={(e) =>
                            field.onChange(parseInt(e.target.value) || 0)
                          }
                        />
                        {form.formState.errors.infants && (
                          <p className="text-sm text-destructive">
                            {form.formState.errors.infants.message}
                          </p>
                        )}
                      </>
                    )}
                  />
                </div>
              </div>
            </div>

            {/* Child Ages */}
            {watchChildren > 0 && (
              <div className="space-y-3">
                <label className="text-sm font-medium text-foreground">
                  Child Ages
                </label>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
                  {Array.from({ length: watchChildren }).map((_, index) => (
                    <div key={index} className="space-y-1">
                      <label className="text-xs font-medium text-foreground">
                        Child {index + 1}
                      </label>
                      <Select
                        value={String(
                          form.getValues("child_ages")?.[index]?.age || 10
                        )}
                        onValueChange={(value) => {
                          const childAges = [
                            ...(form.getValues("child_ages") || []),
                          ];
                          while (childAges.length <= index) {
                            childAges.push({ age: 10 });
                          }
                          childAges[index] = { age: parseInt(value) };
                          form.setValue("child_ages", childAges);
                        }}
                      >
                        <Select.Trigger>
                          <Select.Value />
                        </Select.Trigger>
                        <Select.Content>
                          {Array.from({ length: 16 }, (_, i) => i + 2).map(
                            (age) => (
                              <Select.Item key={age} value={String(age)}>
                                {age} years
                              </Select.Item>
                            )
                          )}
                        </Select.Content>
                      </Select>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Board Type */}
            <div className="space-y-2">
              <label className="text-sm font-medium text-foreground">
                Board Type
              </label>
              <Controller
                control={form.control}
                name="board_type"
                render={({ field }) => (
                  <>
                    <Select value={field.value} onValueChange={field.onChange}>
                      <Select.Trigger>
                        <Select.Value placeholder="Select board type" />
                      </Select.Trigger>
                      <Select.Content>
                        <Select.Item value="none">Room Only</Select.Item>
                        <Select.Item value="bb">Bed & Breakfast</Select.Item>
                        <Select.Item value="hb">Half Board</Select.Item>
                        <Select.Item value="fb">Full Board</Select.Item>
                      </Select.Content>
                    </Select>
                    {form.formState.errors.board_type && (
                      <p className="text-sm text-destructive">
                        {form.formState.errors.board_type.message}
                      </p>
                    )}
                  </>
                )}
              />
            </div>

            {/* Check Availability Button */}
            <div className="flex justify-end pt-4 border-t border-border">
              <Button type="submit" disabled={isCheckingAvailability}>
                {isCheckingAvailability ? "Checking..." : "Check Availability"}
              </Button>
            </div>
          </form>
        </div>

        {/* Availability Results */}
        {availabilityResult && (
          <div className="rounded-lg border border-border bg-card p-6">
            <div className="mb-6">
              <Heading level="h2" className="text-xl font-semibold mb-2">
                Available Rooms
              </Heading>
              <Text className="text-muted-foreground">
                {availabilityResult.nights} night
                {availabilityResult.nights !== 1 ? "s" : ""} ·{" "}
                {availabilityResult.adults} adult
                {availabilityResult.adults !== 1 ? "s" : ""}
                {availabilityResult.children > 0
                  ? ` · ${availabilityResult.children} child${
                      availabilityResult.children !== 1 ? "ren" : ""
                    }`
                  : null}
                {availabilityResult.infants > 0
                  ? ` · ${availabilityResult.infants} infant${
                      availabilityResult.infants !== 1 ? "s" : ""
                    }`
                  : null}
              </Text>
            </div>

            {availabilityResult.available_rooms &&
            availabilityResult.available_rooms.length > 0 ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {availabilityResult.available_rooms.map((room: any) => {
                  const isAvailable = room.available;

                  return (
                    <div
                      key={room.id}
                      className={`
                        bg-card border rounded-xl overflow-hidden transition-all duration-200 flex flex-col
                        ${
                          isAvailable
                            ? "border-border hover:border-blue-400 hover:shadow-lg cursor-pointer"
                            : "border-border opacity-60 cursor-not-allowed"
                        }
                      `}
                      onClick={() => {
                        if (isAvailable) {
                          setSelectedRoom(room);
                          setShowBookingModal(true);
                          setCurrentStep("customer");
                        }
                      }}
                    >
                      {/* Card Header */}
                      <div className="p-6 pb-4 flex-1">
                        <div className="flex justify-between items-start mb-3">
                          <Heading
                            level="h3"
                            className="text-lg font-bold text-foreground flex-1 pr-3"
                          >
                            {room.title || room.name}
                          </Heading>
                          {room.available_rooms > 0 ? (
                            <span
                              className={`
                              inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium whitespace-nowrap
                              ${
                                room.available_rooms <= 2
                                  ? "bg-orange-100 text-orange-800 dark:bg-orange-900/20 dark:text-orange-300"
                                  : "bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-300"
                              }
                            `}
                            >
                              {room.available_rooms <= 2
                                ? `Only ${room.available_rooms} left`
                                : `${room.available_rooms} available`}
                            </span>
                          ) : (
                            <span className="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-300 whitespace-nowrap">
                              Fully booked
                            </span>
                          )}
                        </div>

                        {room.description ? (
                          <Text className="text-muted-foreground text-sm leading-relaxed mb-4">
                            {room.description}
                          </Text>
                        ) : null}

                        {/* Room Specifications */}
                        <div className="space-y-2 mb-4">
                          {room.room_size ? (
                            <div className="flex items-center text-sm text-muted-foreground">
                              <div className="w-2 h-2 bg-blue-500 rounded-full mr-2"></div>
                              <span>Room size: {room.room_size} m²</span>
                            </div>
                          ) : null}
                          {room.bed_type ? (
                            <div className="flex items-center text-sm text-muted-foreground">
                              <div className="w-2 h-2 bg-blue-500 rounded-full mr-2"></div>
                              <span>
                                Bed:{" "}
                                {room.bed_type.charAt(0).toUpperCase() +
                                  room.bed_type.slice(1)}
                              </span>
                            </div>
                          ) : null}
                        </div>

                        {/* Occupancy */}
                        <div className="flex items-center text-sm bg-muted/30 rounded-lg">
                          <Users className="w-4 h-4 mr-2 text-blue-500" />
                          <span className="text-foreground font-medium text-xs">
                            Max: {room.max_adults || 0} adults
                            {room.max_children > 0 &&
                              `, ${room.max_children} children`}
                            {room.max_infants > 0 &&
                              `, ${room.max_infants} infants`}
                          </span>
                        </div>
                      </div>

                      {/* Card Footer */}
                      <div className="p-6 pt-4 border-t border-border bg-muted/10 mt-auto">
                        {isAvailable && room.price ? (
                          <div className="flex justify-between items-center">
                            <div className="flex-1">
                              <div className="text-xl font-bold text-foreground">
                                {(() => {
                                  const boardType =
                                    form.getValues("board_type") || "none";
                                  const currencyCode =
                                    (typeof room.price === "object" &&
                                      room.price.currency_code) ||
                                    availabilityResult?.currency_code ||
                                    "USD";

                                  let totalPrice = 0;

                                  if (typeof room.price === "object") {
                                    if (
                                      room.price.meal_plans &&
                                      room.price.meal_plans[boardType]
                                    ) {
                                      const mealPlanPrice =
                                        room.price.meal_plans[boardType];
                                      totalPrice =
                                        mealPlanPrice.total_amount ||
                                        mealPlanPrice.amount *
                                          availabilityResult.nights;
                                    } else {
                                      totalPrice =
                                        room.price.total_amount ||
                                        room.price.amount *
                                          availabilityResult.nights;
                                    }
                                  } else if (typeof room.price === "number") {
                                    totalPrice =
                                      room.price * availabilityResult.nights;
                                  }

                                  return new Intl.NumberFormat("en-US", {
                                    style: "currency",
                                    currency: currencyCode,
                                  }).format(totalPrice);
                                })()}
                              </div>
                              <div className="text-sm text-muted-foreground">
                                {(() => {
                                  const boardType =
                                    form.getValues("board_type") || "none";
                                  const currencyCode =
                                    (typeof room.price === "object" &&
                                      room.price.currency_code) ||
                                    availabilityResult?.currency_code ||
                                    "USD";

                                  let perNightPrice = 0;

                                  if (typeof room.price === "object") {
                                    if (
                                      room.price.meal_plans &&
                                      room.price.meal_plans[boardType]
                                    ) {
                                      const mealPlanPrice =
                                        room.price.meal_plans[boardType];
                                      perNightPrice =
                                        mealPlanPrice.per_night_amount ||
                                        mealPlanPrice.amount;
                                    } else {
                                      perNightPrice =
                                        room.price.per_night_amount ||
                                        room.price.amount;
                                    }
                                  } else if (typeof room.price === "number") {
                                    perNightPrice = room.price;
                                  }

                                  return `${new Intl.NumberFormat("en-US", {
                                    style: "currency",
                                    currency: currencyCode,
                                  }).format(perNightPrice)} per night`;
                                })()}
                              </div>
                              <div className="text-xs text-muted-foreground">
                                for {availabilityResult.nights} night
                                {availabilityResult.nights !== 1 ? "s" : ""}
                              </div>
                            </div>

                            <div className="ml-4">
                              <Button
                                size="small"
                                className="bg-blue-600 hover:bg-blue-700 text-white"
                              >
                                Select Room
                              </Button>
                            </div>
                          </div>
                        ) : (
                          <div className="text-center">
                            <div className="text-muted-foreground text-sm">
                              {!room.available
                                ? "Not available"
                                : "Insufficient rooms"}
                            </div>
                          </div>
                        )}
                      </div>
                    </div>
                  );
                })}
              </div>
            ) : (
              <div className="text-center py-12">
                <Text className="text-muted-foreground">
                  No rooms are available for the selected dates and criteria.
                </Text>
              </div>
            )}
          </div>
        )}
      </div>

      {/* Modals */}

      {/* Single Focus Modal for all booking steps */}
      <FocusModal open={showBookingModal} onOpenChange={setShowBookingModal}>
        <FocusModal.Content>
          {currentStep === "customer" && (
            <>
              <FocusModal.Header>
                <FocusModal.Title>Customer Information</FocusModal.Title>
              </FocusModal.Header>
              <FocusModal.Body className="overflow-y-auto max-h-[99vh]">
                <CustomerInformationModal
                  open={true}
                  onClose={handleCloseAllModals}
                  onNext={handleCustomerNext}
                  onBack={handleCloseAllModals}
                  availabilityData={form.getValues()}
                  initialFormData={persistedCustomerFormData}
                />
              </FocusModal.Body>
            </>
          )}

          {currentStep === "addons" && (
            <>
              <FocusModal.Header>
                <FocusModal.Title>Select Add-ons</FocusModal.Title>
              </FocusModal.Header>
              <FocusModal.Body className="overflow-y-auto max-h-[99vh]">
                <AddOnSelectionStep
                  onAddOnsSelected={handleAddOnsSelected}
                  onBack={handleAddOnBack}
                  onSkip={() => handleAddOnsSelected([])}
                  initialSelectedAddOns={selectedAddOns}
                />
              </FocusModal.Body>
            </>
          )}

          {currentStep === "confirmation" && (
            <>
              <FocusModal.Header>
                <FocusModal.Title>Booking Confirmation</FocusModal.Title>
              </FocusModal.Header>
              <FocusModal.Body className="overflow-y-auto max-h-[99vh]">
                <BookingConfirmationModal
                  open={true}
                  onClose={handleCloseAllModals}
                  onBack={handleBackToAddOns}
                  availabilityData={form.getValues()}
                  selectedRoom={selectedRoom}
                  customerData={customerData}
                  selectedAddOns={selectedAddOns}
                />
              </FocusModal.Body>
            </>
          )}
        </FocusModal.Content>
      </FocusModal>
    </Container>
  );
};

export default ImprovedBookingForm;
