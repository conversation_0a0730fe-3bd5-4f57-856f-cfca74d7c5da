import React, { useState } from "react";
import { Button, Input, Textarea, Heading, Text, toast } from "@camped-ai/ui";
import { Controller, useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { format, differenceInDays } from "date-fns";
import { CreditCard, FileText, Users, Calendar, Plus } from "lucide-react";
import { useNavigate } from "react-router-dom";
import { SelectedAddOn } from "./add-on-selection";

const confirmationSchema = z.object({
  total_amount: z.number().min(0, "Total amount must be a positive number"),
  currency_code: z.string().default("USD"),
  notes: z.string().optional(),
});

type ConfirmationFormData = z.infer<typeof confirmationSchema>;

interface BookingConfirmationModalProps {
  open: boolean;
  onClose: () => void;
  onBack: () => void;
  availabilityData: any;
  selectedRoom: any;
  customerData: any;
  selectedAddOns?: SelectedAddOn[];
}

const BookingConfirmationModal: React.FC<BookingConfirmationModalProps> = ({
  open,
  onClose,
  onBack,
  availabilityData,
  selectedRoom,
  customerData,
  selectedAddOns = [],
}) => {
  const navigate = useNavigate();
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Calculate pricing
  const calculatePricing = () => {
    if (!availabilityData) {
      return {
        totalPrice: 0,
        perNightPrice: 0,
        currencyCode: "USD",
        nights: 1,
      };
    }

    const nights = differenceInDays(
      availabilityData.check_out_date,
      availabilityData.check_in_date
    );
    const boardType = availabilityData.board_type || "none";

    let totalPrice = 0;
    let perNightPrice = 0;
    let currencyCode = "USD";

    if (selectedRoom?.price) {
      if (typeof selectedRoom.price === "object") {
        currencyCode = selectedRoom.price.currency_code || "USD";

        if (
          selectedRoom.price.meal_plans &&
          selectedRoom.price.meal_plans[boardType]
        ) {
          const mealPlanPrice = selectedRoom.price.meal_plans[boardType];
          totalPrice =
            mealPlanPrice.total_amount || mealPlanPrice.amount * nights;
          perNightPrice =
            mealPlanPrice.per_night_amount || mealPlanPrice.amount;
        } else {
          totalPrice =
            selectedRoom.price.total_amount ||
            selectedRoom.price.amount * nights;
          perNightPrice =
            selectedRoom.price.per_night_amount || selectedRoom.price.amount;
        }
      } else if (typeof selectedRoom.price === "number") {
        totalPrice = selectedRoom.price * nights;
        perNightPrice = selectedRoom.price;
      }
    }

    // Calculate add-ons total
    const addOnsTotal = selectedAddOns.reduce(
      (sum, addOn) => sum + addOn.selling_price * addOn.quantity,
      0
    );

    return {
      totalPrice: totalPrice + addOnsTotal,
      perNightPrice,
      currencyCode,
      nights,
      roomTotal: totalPrice,
      addOnsTotal,
    };
  };

  const {
    totalPrice,
    perNightPrice,
    currencyCode,
    nights,
    roomTotal,
    addOnsTotal,
  } = calculatePricing();

  const form = useForm<ConfirmationFormData>({
    resolver: zodResolver(confirmationSchema),
    defaultValues: {
      total_amount: totalPrice,
      currency_code: currencyCode,
      notes: "",
    },
  });

  const formatPrice = (amount: number, currency: string = "USD") => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: currency,
    }).format(amount);
  };

  const getBoardTypeText = (boardType: string) => {
    switch (boardType) {
      case "none":
        return "Room Only";
      case "bb":
        return "Bed & Breakfast";
      case "hb":
        return "Half Board";
      case "fb":
        return "Full Board";
      default:
        return boardType;
    }
  };

  const onSubmit = async (data: ConfirmationFormData) => {
    try {
      setIsSubmitting(true);

      // Prepare booking data
      const bookingData = {
        hotel_id: availabilityData.hotel_id,
        room_config_id: selectedRoom.id,
        room_type: selectedRoom?.title || selectedRoom.name,
        check_in_date: format(availabilityData.check_in_date, "yyyy-MM-dd"),
        check_out_date: format(availabilityData.check_out_date, "yyyy-MM-dd"),
        check_in_time: "12:00",
        check_out_time: "12:00",
        guest_name: customerData.guest_name,
        guest_email: customerData.guest_email,
        guest_phone: customerData.guest_phone || "",
        adults: availabilityData.adults,
        children: availabilityData.children,
        infants: availabilityData.infants,
        number_of_rooms: 1,
        board_type: availabilityData.board_type,
        total_amount: data.total_amount,
        currency_code: data.currency_code,
        region_id: "reg_01JP9R0NP6B5DXGDYHFSSW0FK1",
        special_requests: customerData.special_requests || "",
        notes: data.notes || "",
        child_ages: availabilityData.child_ages || [],
        selectedAddOns: selectedAddOns, // Include selected add-ons in the payload
        metadata: {
          travelers: customerData.travelers,
        },
      };

      console.log("Creating booking with data:", bookingData);
      console.log("Selected add-ons being sent:", selectedAddOns);

      const response = await fetch("/admin/hotel-management/bookings", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(bookingData),
      });

      if (!response.ok) {
        let errorMessage = "Failed to create booking";
        try {
          const errorData = await response.json();
          errorMessage = errorData.message || errorMessage;
        } catch (parseError) {
          console.error("Could not parse error response:", parseError);
        }
        throw new Error(errorMessage);
      }

      const result = await response.json();
      const newBookingId = result.order.id;

      toast.success("Success", {
        description: "Booking created successfully",
      });

      // Navigate to booking details
      navigate(`/hotel-management/bookings/${newBookingId}`);
    } catch (error) {
      console.error("Error creating booking:", error);
      toast.error("Error", {
        description:
          error instanceof Error ? error.message : "Failed to create booking",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="p-6">
      <div className="space-y-6">
        {/* Booking Summary */}
        <div className="border rounded-lg p-6">
          <div className="flex items-center mb-4">
            <Calendar className="w-5 h-5 text-blue-600 mr-2" />
            <Heading level="h3" className="text-lg font-semibold text-blue-700">
              Booking Summary
            </Heading>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <div className="space-y-3">
                <div>
                  <Text className="text-sm font-medium text-gray-600">
                    Room Type
                  </Text>
                  <Text className="text-lg font-semibold">
                    {selectedRoom?.title || selectedRoom?.name || "N/A"}
                  </Text>
                </div>
                <div>
                  <Text className="text-sm font-medium text-gray-600">
                    Dates
                  </Text>
                  <Text>
                    {availabilityData
                      ? `${format(
                          availabilityData.check_in_date,
                          "MMM dd, yyyy"
                        )} - ${format(
                          availabilityData.check_out_date,
                          "MMM dd, yyyy"
                        )}`
                      : "N/A"}
                  </Text>
                  <Text className="text-sm text-gray-500">
                    {nights} night{nights !== 1 ? "s" : ""}
                  </Text>
                </div>
                <div>
                  <Text className="text-sm font-medium text-gray-600">
                    Board Type
                  </Text>
                  <Text>
                    {availabilityData
                      ? getBoardTypeText(availabilityData.board_type)
                      : "N/A"}
                  </Text>
                </div>
              </div>
            </div>

            <div>
              <div className="space-y-3">
                <div>
                  <Text className="text-sm font-medium text-gray-600">
                    Occupancy
                  </Text>
                  <div className="flex items-center space-x-4">
                    {availabilityData ? (
                      <>
                        <span>
                          {availabilityData.adults} adult
                          {availabilityData.adults !== 1 ? "s" : ""}
                        </span>
                        {availabilityData.children > 0 && (
                          <span>
                            {availabilityData.children} child
                            {availabilityData.children !== 1 ? "ren" : ""}
                          </span>
                        )}
                        {availabilityData.infants > 0 && (
                          <span>
                            {availabilityData.infants} infant
                            {availabilityData.infants !== 1 ? "s" : ""}
                          </span>
                        )}
                      </>
                    ) : (
                      <span>N/A</span>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Customer Information */}
        <div className="border rounded-lg p-6">
          <div className="flex items-center mb-4">
            <Users className="w-5 h-5 text-purple-600 mr-2" />
            <Heading
              level="h3"
              className="text-lg font-semibold text-purple-700"
            >
              Guest Information
            </Heading>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <div className="space-y-2">
                <div>
                  <Text className="text-sm font-medium text-gray-600">
                    Primary Guest
                  </Text>
                  <Text className="font-semibold">
                    {customerData?.guest_name || "N/A"}
                  </Text>
                </div>
                <div>
                  <Text className="text-sm font-medium text-gray-600">
                    Email
                  </Text>
                  <Text>{customerData?.guest_email || "N/A"}</Text>
                </div>
                {customerData?.guest_phone && (
                  <div>
                    <Text className="text-sm font-medium text-gray-600">
                      Phone
                    </Text>
                    <Text>{customerData.guest_phone}</Text>
                  </div>
                )}
              </div>
            </div>

            <div>
              {customerData?.special_requests && (
                <div>
                  <Text className="text-sm font-medium text-gray-600">
                    Special Requests
                  </Text>
                  <Text className="text-sm">
                    {customerData.special_requests}
                  </Text>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Add-ons */}
        {selectedAddOns.length > 0 && (
          <div className="border rounded-lg p-6">
            <div className="flex items-center mb-4">
              <Plus className="w-5 h-5 text-green-600 mr-2" />
              <Heading
                level="h3"
                className="text-lg font-semibold text-green-700"
              >
                Selected Add-ons ({selectedAddOns.length})
              </Heading>
            </div>

            <div className="space-y-3">
              {selectedAddOns.map((addOn) => (
                <div
                  key={addOn.id}
                  className="flex justify-between items-start p-3 bg-gray-50 rounded-lg"
                >
                  <div className="flex-1">
                    <Text className="font-medium">{addOn.title}</Text>
                    <Text className="text-sm text-gray-600">
                      Qty: {addOn.quantity} ×{" "}
                      {formatPrice(addOn.selling_price, addOn.selling_currency)}
                    </Text>

                    {/* Show customer field responses */}
                    {Object.keys(addOn.customerFieldResponses).length > 0 && (
                      <div className="mt-2">
                        <Text className="text-xs text-gray-500">
                          {Object.entries(addOn.customerFieldResponses)
                            .slice(0, 2)
                            .map(([key, value]) => `${key}: ${value}`)
                            .join(", ")}
                          {Object.keys(addOn.customerFieldResponses).length >
                            2 && "..."}
                        </Text>
                      </div>
                    )}
                  </div>

                  <Text className="font-semibold ml-4">
                    {formatPrice(
                      addOn.selling_price * addOn.quantity,
                      addOn.selling_currency
                    )}
                  </Text>
                </div>
              ))}

              <div className="border-t pt-3 mt-3">
                <div className="flex justify-between items-center">
                  <Text className="font-medium">Add-ons Subtotal:</Text>
                  <Text className="font-semibold">
                    {formatPrice(addOnsTotal || 0, currencyCode)}
                  </Text>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Pricing */}
        <div className="border rounded-lg p-6">
          <div className="flex items-center mb-4">
            <CreditCard className="w-5 h-5 text-green-600 mr-2" />
            <Heading
              level="h3"
              className="text-lg font-semibold text-green-700"
            >
              Pricing Details
            </Heading>
          </div>

          <div className="space-y-3">
            <div className="flex justify-between">
              <Text>Room rate per night</Text>
              <Text>{formatPrice(perNightPrice, currencyCode)}</Text>
            </div>
            <div className="flex justify-between">
              <Text>Number of nights</Text>
              <Text>{nights}</Text>
            </div>
            <div className="flex justify-between">
              <Text>Room subtotal</Text>
              <Text>{formatPrice(roomTotal || 0, currencyCode)}</Text>
            </div>
            {(addOnsTotal || 0) > 0 && (
              <div className="flex justify-between">
                <Text>Add-ons subtotal</Text>
                <Text>{formatPrice(addOnsTotal || 0, currencyCode)}</Text>
              </div>
            )}
            <div className="border-t pt-3">
              <div className="flex justify-between items-center">
                <Text className="text-lg font-semibold">Total Amount</Text>
                <Text className="text-2xl font-bold text-green-700">
                  {formatPrice(totalPrice, currencyCode)}
                </Text>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="flex justify-between items-center pt-6 border-t">
        <Button variant="secondary" onClick={onBack}>
          Back
        </Button>
        <div className="flex space-x-3">
          <Button variant="secondary" onClick={onClose}>
            Cancel
          </Button>
          <Button onClick={form.handleSubmit(onSubmit)} disabled={isSubmitting}>
            {isSubmitting ? "Creating Booking..." : "Confirm & Create Booking"}
          </Button>
        </div>
      </div>
    </div>
  );
};

export default BookingConfirmationModal;
