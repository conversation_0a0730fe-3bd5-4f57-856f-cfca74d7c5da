import React from "react";
import { Input, Label, Text, Select, Textarea, Checkbox } from "@camped-ai/ui";
import { CustomerField } from "./AddOnSelectionStep";

interface DynamicFieldRendererProps {
  field: CustomerField;
  value: any;
  onChange: (value: any) => void;
  error?: string;
}

export const DynamicFieldRenderer: React.FC<DynamicFieldRendererProps> = ({
  field,
  value,
  onChange,
  error,
}) => {
  // Safety check for field object
  if (!field || typeof field !== 'object') {
    console.warn('DynamicFieldRenderer: Invalid field object', field);
    return null;
  }

  const renderField = () => {
    switch (field.field_type) {
      case "text":
        return (
          <Input
            value={value || ""}
            onChange={(e) => onChange(e.target.value)}
            placeholder={`Enter ${field.field_name?.toLowerCase() || 'value'}`}
            className={error ? "border-ui-border-error" : ""}
          />
        );

      case "textarea":
        return (
          <Textarea
            value={value || ""}
            onChange={(e) => onChange(e.target.value)}
            placeholder={`Enter ${field.field_name?.toLowerCase() || 'value'}`}
            rows={3}
            className={error ? "border-ui-border-error" : ""}
          />
        );

      case "number":
        return (
          <Input
            type="number"
            value={value || ""}
            onChange={(e) => onChange(e.target.value)}
            placeholder={`Enter ${field.field_name?.toLowerCase() || 'number'}`}
            className={error ? "border-ui-border-error" : ""}
          />
        );

      case "email":
        return (
          <Input
            type="email"
            value={value || ""}
            onChange={(e) => onChange(e.target.value)}
            placeholder={`Enter ${field.field_name?.toLowerCase() || 'email'}`}
            className={error ? "border-ui-border-error" : ""}
          />
        );

      case "date":
        return (
          <Input
            type="date"
            value={value || ""}
            onChange={(e) => onChange(e.target.value)}
            className={error ? "border-ui-border-error" : ""}
          />
        );

      case "time":
        return (
          <Input
            type="time"
            value={value || ""}
            onChange={(e) => onChange(e.target.value)}
            className={error ? "border-ui-border-error" : ""}
          />
        );

      case "boolean":
        return (
          <div className="flex items-center space-x-2">
            <Checkbox
              checked={value === true || value === "true"}
              onCheckedChange={(checked) => onChange(checked)}
            />
            <Text className="text-sm">Yes</Text>
          </div>
        );

      case "dropdown":
      case "select":
        const options = field.options || [];
        return (
          <Select
            value={value || ""}
            onValueChange={onChange}
          >
            <Select.Trigger className={error ? "border-ui-border-error" : ""}>
              <Select.Value placeholder={`Select ${field.field_name?.toLowerCase() || 'option'}`} />
            </Select.Trigger>
            <Select.Content>
              {options.map((option, index) => (
                <Select.Item key={index} value={option}>
                  {option}
                </Select.Item>
              ))}
            </Select.Content>
          </Select>
        );

      case "multi-select":
        const multiOptions = field.options || [];
        const selectedValues = Array.isArray(value) ? value : [];
        
        return (
          <div className="space-y-2">
            {multiOptions.map((option, index) => (
              <div key={index} className="flex items-center space-x-2">
                <Checkbox
                  checked={selectedValues.includes(option)}
                  onCheckedChange={(checked) => {
                    if (checked) {
                      onChange([...selectedValues, option]);
                    } else {
                      onChange(selectedValues.filter(v => v !== option));
                    }
                  }}
                />
                <Text className="text-sm">{option}</Text>
              </div>
            ))}
          </div>
        );

      default:
        return (
          <Input
            value={value || ""}
            onChange={(e) => onChange(e.target.value)}
            placeholder={`Enter ${field.field_name?.toLowerCase() || 'value'}`}
            className={error ? "border-ui-border-error" : ""}
          />
        );
    }
  };

  return (
    <div className="space-y-2">
      <Label className="flex items-center gap-1">
        {field.field_name || 'Field'}
        {field.is_required && (
          <span className="text-ui-fg-error">*</span>
        )}
      </Label>
      
      {renderField()}
      
      {error && (
        <Text className="text-sm text-ui-fg-error">
          {error}
        </Text>
      )}
      
      {/* Field type indicator for debugging */}
      {process.env.NODE_ENV === "development" && (
        <Text className="text-xs text-ui-fg-subtle">
          Type: {field.field_type || 'unknown'} | Key: {field.field_key || 'unknown'}
        </Text>
      )}
    </div>
  );
};
