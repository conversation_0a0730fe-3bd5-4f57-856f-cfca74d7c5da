import React, { useState, useEffect } from "react";
import { Drawer, Text, Button } from "@camped-ai/ui";
import { DynamicFieldRenderer } from "./DynamicFieldRenderer.tsx";
import { SelectedAddOn } from "./AddOnSelectionStep";

interface CustomerFieldsModalProps {
  addOn: SelectedAddOn;
  onSave: (addOnId: string, responses: Record<string, any>) => void;
  onCancel: () => void;
}

export const CustomerFieldsModal: React.FC<CustomerFieldsModalProps> = ({
  addOn,
  onSave,
  onCancel,
}) => {
  const [fieldResponses, setFieldResponses] = useState<Record<string, any>>(
    addOn.customerFieldResponses || {}
  );
  const [errors, setErrors] = useState<Record<string, string>>({});

  const customerFields = addOn.customer_fields || [];

  // Initialize field responses with existing values
  useEffect(() => {
    setFieldResponses(addOn.customerFieldResponses || {});
  }, [addOn.customerFieldResponses]);

  const handleFieldChange = (fieldKey: string, value: any) => {
    setFieldResponses((prev) => ({ ...prev, [fieldKey]: value }));

    // Clear error when user starts typing
    if (errors[fieldKey]) {
      setErrors((prev) => ({ ...prev, [fieldKey]: "" }));
    }
  };

  const validateFields = (): boolean => {
    const newErrors: Record<string, string> = {};

    customerFields.forEach((field) => {
      const value = fieldResponses[field.field_key];

      // Required field validation
      if (
        field.is_required &&
        (!value || value === "" || value === null || value === undefined)
      ) {
        newErrors[field.field_key] = `${field.field_name} is required`;
        return;
      }

      // Type-specific validation
      if (value !== null && value !== undefined && value !== "") {
        switch (field.field_type) {
          case "number":
            if (isNaN(Number(value))) {
              newErrors[
                field.field_key
              ] = `${field.field_name} must be a valid number`;
            }
            break;
          case "email":
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(value)) {
              newErrors[
                field.field_key
              ] = `${field.field_name} must be a valid email`;
            }
            break;
          // Add more validation rules as needed
        }
      }

      // Custom validation rules
      if (field.validation_rules && value) {
        // Implement custom validation based on validation_rules
        // This can be extended based on your validation requirements
      }
    });

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSave = () => {
    if (validateFields()) {
      onSave(addOn.id, fieldResponses);
    }
  };

  const hasRequiredFields = customerFields.some((field) => field.is_required);

  return (
    <Drawer open={true} onOpenChange={onCancel}>
      <Drawer.Content className="max-w-2xl">
        <Drawer.Header>
          <Drawer.Title>Configure: {addOn.title}</Drawer.Title>
          <Text className="text-gray-600 mt-1">
            Please provide the required information for this add-on
          </Text>
        </Drawer.Header>

        <Drawer.Body className="p-6">
          <div className="space-y-6">
            {/* Add-on Info */}
            <div className="p-4 bg-ui-bg-subtle rounded-lg">
              <Text className="font-medium">{addOn.title}</Text>
              {addOn.description && (
                <Text className="text-sm text-ui-fg-subtle mt-1">
                  {addOn.description}
                </Text>
              )}
              <Text className="text-sm text-ui-fg-subtle mt-1">
                Price:{" "}
                {new Intl.NumberFormat("en-US", {
                  style: "currency",
                  currency: addOn.selling_currency,
                }).format(addOn.selling_price)}
              </Text>
            </div>

            {/* Customer Fields */}
            <div className="space-y-4">
              {customerFields
                .sort((a, b) => a.display_order - b.display_order)
                .map((field) => (
                  <DynamicFieldRenderer
                    key={field.field_key}
                    field={field}
                    value={fieldResponses[field.field_key] || ""}
                    onChange={(value) =>
                      handleFieldChange(field.field_key, value)
                    }
                    error={errors[field.field_key]}
                  />
                ))}
            </div>

            {/* Help Text */}
            {hasRequiredFields && (
              <div className="p-3 bg-ui-bg-highlight rounded-lg">
                <Text className="text-sm text-ui-fg-subtle">
                  <span className="text-ui-fg-error">*</span> Required fields
                  must be completed before proceeding
                </Text>
              </div>
            )}
          </div>
        </Drawer.Body>

        <div className="flex justify-between items-center p-6 border-t">
          <Button variant="secondary" onClick={onCancel}>
            Cancel
          </Button>
          <Button variant="primary" onClick={handleSave}>
            Save Configuration
          </Button>
        </div>
      </Drawer.Content>
    </Drawer>
  );
};
