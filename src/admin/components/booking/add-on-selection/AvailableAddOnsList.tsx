import React from "react";
import { Heading, Text } from "@camped-ai/ui";
import { AddOnCard } from "./AddOnCard";
import { AvailableAddOn, SelectedAddOn } from "./AddOnSelectionStep";

interface AvailableAddOnsListProps {
  addOns: AvailableAddOn[];
  selectedAddOns: SelectedAddOn[];
  onAddOnSelect: (addOn: AvailableAddOn, action?: string) => void;
  loading: boolean;
  error: any;
}

export const AvailableAddOnsList: React.FC<AvailableAddOnsListProps> = ({
  addOns,
  selectedAddOns,
  onAddOnSelect,
  loading,
  error,
}) => {
  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="flex items-center space-x-2">
          <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-ui-fg-base"></div>
          <Text>Loading available add-ons...</Text>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-12">
        <Text className="text-ui-fg-error mb-2">Failed to load add-ons</Text>
        <Text className="text-ui-fg-subtle text-sm mb-2">
          {error.message || "Please try again later"}
        </Text>
        {process.env.NODE_ENV === 'development' && (
          <details className="text-left mt-4 p-3 bg-red-50 rounded text-xs">
            <summary className="cursor-pointer font-medium">Debug Information</summary>
            <pre className="mt-2 whitespace-pre-wrap">{JSON.stringify(error, null, 2)}</pre>
          </details>
        )}
      </div>
    );
  }

  if (addOns.length === 0) {
    return (
      <div className="text-center py-12">
        <Text className="text-ui-fg-subtle">No add-ons available</Text>
        <Text className="text-ui-fg-subtle text-sm mt-1">
          {process.env.NODE_ENV === 'development'
            ? "No add-ons found. You may need to create some test add-ons first."
            : "Check back later for additional services"
          }
        </Text>
      </div>
    );
  }

  return (
    <div>
      <div className="flex justify-end items-center mb-4">
        <Text className="text-ui-fg-subtle text-sm">
          {addOns.length} service{addOns.length !== 1 ? 's' : ''} available
        </Text>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {addOns.map(addOn => (
          <AddOnCard
            key={addOn.id}
            addOn={addOn}
            isSelected={selectedAddOns.some(s => s.id === addOn.id)}
            onSelect={onAddOnSelect}
          />
        ))}
      </div>
    </div>
  );
};
