import React, { useState, useEffect } from "react";
import { useQuery } from "@tanstack/react-query";
import { Container, Heading, Text, Button } from "@camped-ai/ui";
import { AvailableAddOnsList } from "./AvailableAddOnsList.tsx";
import { SelectedAddOnsSummary } from "./SelectedAddOnsSummary.tsx";
import { CustomerFieldsModal } from "./CustomerFieldsModal.tsx";

interface AddOnSelectionStepProps {
  onAddOnsSelected: (selectedAddOns: SelectedAddOn[]) => void;
  onBack: () => void;
  onSkip: () => void;
  initialSelectedAddOns?: SelectedAddOn[];
}

export interface AvailableAddOn {
  id: string;
  title: string;
  description: string;
  selling_price: number;
  selling_currency: string;
  service_level: string;
  customer_fields: CustomerField[];
  metadata: Record<string, any>;
}

export interface CustomerField {
  field_id: string;
  field_name: string;
  field_type: string;
  field_key: string;
  is_required: boolean;
  display_order: number;
  options?: string[] | null;
  validation_rules?: Record<string, any> | null;
}

export interface SelectedAddOn extends AvailableAddOn {
  quantity: number;
  customerFieldsCompleted: boolean;
  customerFieldResponses: Record<string, any>;
}

const fetchAvailableAddOns = async (): Promise<{
  add_ons: AvailableAddOn[];
  count: number;
}> => {
  console.log("🔧 fetchAvailableAddOns called - making API request");

  const response = await fetch("/admin/bookings/add-ons/available", {
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${localStorage.getItem("medusa_admin_token")}`,
    },
  });

  console.log("🔧 API response:", response.status, response.statusText);

  if (!response.ok) {
    throw new Error("Failed to fetch available add-ons");
  }

  return response.json();
};

export const AddOnSelectionStep: React.FC<AddOnSelectionStepProps> = ({
  onAddOnsSelected,
  onBack,
  onSkip,
  initialSelectedAddOns = [],
}) => {
  console.log("🔧 AddOnSelectionStep component rendered");

  const [selectedAddOns, setSelectedAddOns] = useState<SelectedAddOn[]>(
    initialSelectedAddOns
  );
  const [showCustomerFieldsModal, setShowCustomerFieldsModal] = useState(false);
  const [currentAddOnConfig, setCurrentAddOnConfig] =
    useState<SelectedAddOn | null>(null);

  // Update selectedAddOns when initialSelectedAddOns changes (e.g., when navigating back)
  useEffect(() => {
    setSelectedAddOns(initialSelectedAddOns);
  }, [initialSelectedAddOns]);

  const {
    data: addOnsResponse,
    isLoading,
    error,
  } = useQuery({
    queryKey: ["available-add-ons"],
    queryFn: fetchAvailableAddOns,
  });

  console.log("🔧 useQuery state:", { isLoading, error, addOnsResponse });

  const addOns = addOnsResponse?.add_ons || [];
  console.log("🔧 Processed addOns:", addOns);

  const handleAddOnSelect = (addOn: AvailableAddOn, action?: string) => {
    if (action === "configure") {
      // Open configuration modal
      const existingSelection = selectedAddOns.find((s) => s.id === addOn.id);
      if (existingSelection) {
        setCurrentAddOnConfig(existingSelection);
        setShowCustomerFieldsModal(true);
      }
      return;
    }

    // Use functional state update to prevent race conditions
    setSelectedAddOns((prevSelectedAddOns) => {
      const isSelected = prevSelectedAddOns.some((s) => s.id === addOn.id);

      if (isSelected) {
        // Remove from selection
        return prevSelectedAddOns.filter((s) => s.id !== addOn.id);
      } else {
        // Double-check for duplicates before adding (extra safety)
        const alreadyExists = prevSelectedAddOns.find((s) => s.id === addOn.id);
        if (alreadyExists) {
          console.warn(
            `Addon ${addOn.id} already exists, skipping duplicate addition`
          );
          return prevSelectedAddOns;
        }

        // Add to selection
        const newSelection: SelectedAddOn = {
          ...addOn,
          quantity: 1,
          customerFieldsCompleted: addOn.customer_fields.length === 0, // Auto-complete if no fields
          customerFieldResponses: {},
        };

        // If has customer fields, open modal immediately
        if (addOn.customer_fields.length > 0) {
          // Use setTimeout to avoid state update conflicts
          setTimeout(() => {
            setCurrentAddOnConfig(newSelection);
            setShowCustomerFieldsModal(true);
          }, 0);
        }

        return [...prevSelectedAddOns, newSelection];
      }
    });
  };

  const handleRemoveAddOn = (addOnId: string) => {
    setSelectedAddOns((prevSelectedAddOns) => {
      const filtered = prevSelectedAddOns.filter((s) => s.id !== addOnId);
      console.log(`Removed addon ${addOnId}, remaining: ${filtered.length}`);
      return filtered;
    });
  };

  const handleConfigureAddOn = (addOn: SelectedAddOn) => {
    setCurrentAddOnConfig(addOn);
    setShowCustomerFieldsModal(true);
  };

  const handleCustomerFieldsSave = (
    addOnId: string,
    responses: Record<string, any>
  ) => {
    setSelectedAddOns((prevSelectedAddOns) =>
      prevSelectedAddOns.map((addOn) =>
        addOn.id === addOnId
          ? {
              ...addOn,
              customerFieldResponses: responses,
              customerFieldsCompleted: true,
            }
          : addOn
      )
    );
    setShowCustomerFieldsModal(false);
    setCurrentAddOnConfig(null);
  };

  const canContinue =
    selectedAddOns.length === 0 ||
    selectedAddOns.every((addOn) => addOn.customerFieldsCompleted);

  return (
    <div className="space-y-6 p-6">
      {/* Step Header */}
      <div>
        <Heading level="h2">Select Add-ons</Heading>
        <Text className="text-ui-fg-subtle">
          Enhance your stay with additional services (optional)
        </Text>
      </div>

      {/* Available Add-ons */}
      <Container className="p-6">
        <AvailableAddOnsList
          addOns={addOns}
          selectedAddOns={selectedAddOns}
          onAddOnSelect={handleAddOnSelect}
          loading={isLoading}
          error={error}
        />
      </Container>

      {/* Selected Add-ons Summary */}
      {selectedAddOns.length > 0 && (
        <SelectedAddOnsSummary
          selectedAddOns={selectedAddOns}
          onRemove={handleRemoveAddOn}
          onConfigure={handleConfigureAddOn}
        />
      )}

      {/* Customer Fields Modal */}
      {showCustomerFieldsModal && currentAddOnConfig && (
        <CustomerFieldsModal
          addOn={currentAddOnConfig}
          onSave={handleCustomerFieldsSave}
          onCancel={() => {
            setShowCustomerFieldsModal(false);
            setCurrentAddOnConfig(null);
          }}
        />
      )}

      {/* Navigation */}
      <div className="flex justify-between items-center pt-4 border-t">
        <Button variant="secondary" onClick={onBack}>
          Back to Customer Info
        </Button>

        <div className="flex gap-3">
          <Button variant="secondary" onClick={onSkip}>
            Skip Add-ons
          </Button>
          <Button
            onClick={() => onAddOnsSelected(selectedAddOns)}
            disabled={!canContinue}
          >
            Continue to Payment ({selectedAddOns.length} add-ons)
          </Button>
        </div>
      </div>
    </div>
  );
};
