import React from "react";
import { Container, Heading, Text, But<PERSON>, Badge } from "@camped-ai/ui";
import { SelectedAddOn } from "./AddOnSelectionStep";

interface SelectedAddOnsSummaryProps {
  selectedAddOns: SelectedAddOn[];
  onRemove: (addOnId: string) => void;
  onConfigure: (addOn: SelectedAddOn) => void;
}

const formatCurrency = (amount: number, currency: string): string => {
  return new Intl.NumberFormat("en-US", {
    style: "currency",
    currency: currency,
    minimumFractionDigits: 2,
  }).format(amount);
};

export const SelectedAddOnsSummary: React.FC<SelectedAddOnsSummaryProps> = ({
  selectedAddOns,
  onRemove,
  onConfigure,
}) => {
  if (selectedAddOns.length === 0) return null;

  const totalPrice = selectedAddOns.reduce(
    (sum, addOn) => sum + addOn.selling_price * addOn.quantity,
    0
  );

  // Use the currency from the first add-on (assuming all are in the same currency)
  const currency = selectedAddOns[0]?.selling_currency || "CHF";

  return (
    <Container className="border border-gray-200 rounded-lg">
      <div className="p-6">
        <div className="flex justify-between items-center mb-4">
          <Heading level="h3">Selected Add-ons</Heading>
          <Badge size="small">{selectedAddOns.length} selected</Badge>
        </div>

        <div className="space-y-4">
          {selectedAddOns.map((addOn) => (
            <div
              key={addOn.id}
              className="flex justify-between items-start p-4 border border-ui-border-base rounded-lg"
            >
              <div className="flex-1 min-w-0">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <Text className="font-medium">{addOn.title}</Text>
                    <div className="flex items-center gap-2 mt-1">
                      <Text className="text-sm text-ui-fg-subtle">
                        Qty: {addOn.quantity} ×{" "}
                        {formatCurrency(
                          addOn.selling_price,
                          addOn.selling_currency
                        )}
                      </Text>
                      <Badge size="small">
                        {addOn.metadata?.type || "Service"}
                      </Badge>
                    </div>
                  </div>

                  <Text className="font-semibold ml-4">
                    {formatCurrency(
                      addOn.selling_price * addOn.quantity,
                      addOn.selling_currency
                    )}
                  </Text>
                </div>

                {/* Customer Fields Status */}
                {addOn.customer_fields?.length > 0 && (
                  <div className="mt-2">
                    <Badge size="small">
                      {addOn.customerFieldsCompleted
                        ? "✓ Configured"
                        : "⚠ Needs Configuration"}
                    </Badge>

                    {/* Show configured fields preview */}
                    {addOn.customerFieldsCompleted &&
                      Object.keys(addOn.customerFieldResponses).length > 0 && (
                        <div className="mt-1">
                          <Text className="text-xs text-ui-fg-subtle">
                            {Object.entries(addOn.customerFieldResponses)
                              .slice(0, 2) // Show first 2 fields
                              .map(([key, value]) => `${key}: ${value}`)
                              .join(", ")}
                            {Object.keys(addOn.customerFieldResponses).length >
                              2 && "..."}
                          </Text>
                        </div>
                      )}
                  </div>
                )}

                {/* Actions */}
                <div className="flex gap-2 mt-3">
                  {addOn.customer_fields?.length > 0 && (
                    <Button
                      variant="secondary"
                      size="small"
                      onClick={() => onConfigure(addOn)}
                    >
                      {addOn.customerFieldsCompleted
                        ? "Edit Config"
                        : "Configure"}
                    </Button>
                  )}

                  <Button
                    variant="danger"
                    size="small"
                    onClick={() => onRemove(addOn.id)}
                  >
                    Remove
                  </Button>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Total */}
        <div className="border-t pt-4 mt-4">
          <div className="flex justify-between items-center">
            <Text className="font-semibold">Total Add-ons:</Text>
            <Text className="font-bold text-lg">
              {formatCurrency(totalPrice, currency)}
            </Text>
          </div>

          {selectedAddOns.some((addOn) => !addOn.customerFieldsCompleted) && (
            <Text className="text-sm text-ui-fg-subtle mt-1">
              ⚠ Some add-ons need configuration before proceeding
            </Text>
          )}
        </div>
      </div>
    </Container>
  );
};
