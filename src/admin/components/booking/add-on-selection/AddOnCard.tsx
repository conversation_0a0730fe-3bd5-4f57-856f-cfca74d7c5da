import React, { useState, useCallback } from "react";
import { Container, Heading, Text, But<PERSON>, Badge } from "@camped-ai/ui";
import { AvailableAddOn } from "./AddOnSelectionStep";

interface AddOnCardProps {
  addOn: AvailableAddOn;
  isSelected: boolean;
  onSelect: (addOn: AvailableAddOn, action?: string) => void;
}

const formatCurrency = (amount: number, currency: string): string => {
  return new Intl.NumberFormat("en-US", {
    style: "currency",
    currency: currency,
    minimumFractionDigits: 2,
  }).format(amount);
};

export const AddOnCard: React.FC<AddOnCardProps> = ({
  addOn,
  isSelected,
  onSelect,
}) => {
  const hasCustomerFields = addOn.customer_fields?.length > 0;
  const [isProcessing, setIsProcessing] = useState(false);

  // Debounced select handler to prevent rapid clicking
  const handleSelect = useCallback(
    (action?: string) => {
      if (isProcessing) {
        console.log(
          `Addon ${addOn.id} selection already in progress, ignoring click`
        );
        return;
      }

      setIsProcessing(true);
      onSelect(addOn, action);

      // Reset processing state after a short delay
      setTimeout(() => {
        setIsProcessing(false);
      }, 500);
    },
    [addOn, onSelect, isProcessing]
  );

  return (
    <Container
      className={`transition-all duration-200 hover:shadow-md border rounded-lg p-4 cursor-pointer ${
        isSelected
          ? "ring-2 ring-blue-500 border-blue-500 bg-blue-50"
          : "border-gray-200 hover:border-gray-300"
      }`}
      onClick={() => handleSelect()}
    >
      <div className="p-4">
        {/* Header */}
        <div className="flex justify-between items-start mb-3">
          <div className="flex-1 min-w-0">
            <Heading level="h3" className="text-sm font-medium truncate">
              {addOn.title}
            </Heading>
            <div className="flex items-center gap-2 mt-1">
              <Badge size="small">{addOn.metadata?.type || "Service"}</Badge>
              <Badge size="small">{addOn.service_level}</Badge>
            </div>
          </div>
        </div>

        {/* Description */}
        {addOn.description && (
          <Text className="text-sm text-ui-fg-subtle mb-3 line-clamp-2">
            {addOn.description}
          </Text>
        )}

        {/* Customer Fields Indicator */}
        {hasCustomerFields && (
          <div className="mb-3">
            <Badge size="small">
              📝 {addOn.customer_fields.length} field
              {addOn.customer_fields.length !== 1 ? "s" : ""} required
            </Badge>
          </div>
        )}

        {/* Pricing */}
        <div className="flex justify-between items-center mb-4">
          <div>
            <Text className="text-lg font-semibold text-ui-fg-base">
              {formatCurrency(addOn.selling_price, addOn.selling_currency)}
            </Text>
            <Text className="text-xs text-ui-fg-subtle">per unit</Text>
          </div>
        </div>

        {/* Actions */}
        <div className="flex gap-2">
          <Button
            variant={isSelected ? "secondary" : "primary"}
            size="small"
            onClick={(e) => {
              e.stopPropagation();
              handleSelect();
            }}
            disabled={isProcessing}
            className="flex-1"
          >
            {isProcessing
              ? "Processing..."
              : isSelected
              ? "✓ Selected"
              : "Select"}
          </Button>

          {hasCustomerFields && isSelected && (
            <Button
              variant="secondary"
              size="small"
              onClick={(e) => {
                e.stopPropagation();
                handleSelect("configure");
              }}
              disabled={isProcessing}
              className="px-3"
            >
              Configure
            </Button>
          )}
        </div>
      </div>
    </Container>
  );
};
