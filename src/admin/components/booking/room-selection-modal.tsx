import React, { useState, useEffect } from "react";
import {
  FocusModal,
  But<PERSON>,
  Heading,
  Text,
  toast,
} from "@camped-ai/ui";
import { format } from "date-fns";
import { Users, CheckCircle, AlertCircle } from "lucide-react";

interface AvailabilityData {
  hotel_id: string;
  check_in_date: Date;
  check_out_date: Date;
  adults: number;
  children: number;
  infants: number;
  number_of_rooms: number;
  board_type: string;
  child_ages: { age: number }[];
}

interface RoomSelectionModalProps {
  open: boolean;
  onClose: () => void;
  onNext: (selectedRoom: any, availabilityData: AvailabilityData) => void;
  onBack: () => void;
  availabilityData: AvailabilityData;
}

const RoomSelectionModal: React.FC<RoomSelectionModalProps> = ({
  open,
  onClose,
  onNext,
  onBack,
  availabilityData,
}) => {
  const [availableRooms, setAvailableRooms] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [selectedRoom, setSelectedRoom] = useState<any>(null);
  const [availabilityResult, setAvailabilityResult] = useState<any>(null);

  const checkAvailability = async () => {
    try {
      setIsLoading(true);
      
      const formattedCheckIn = format(availabilityData.check_in_date, "yyyy-MM-dd");
      const formattedCheckOut = format(availabilityData.check_out_date, "yyyy-MM-dd");
      const childAgesParam = encodeURIComponent(JSON.stringify(availabilityData.child_ages));

      const response = await fetch(
        `/admin/hotel-management/hotels/${availabilityData.hotel_id}/availability?check_in=${formattedCheckIn}&check_out=${formattedCheckOut}&adults=${availabilityData.adults}&children=${availabilityData.children}&infants=${availabilityData.infants}&board_type=${availabilityData.board_type}&include_unavailable=true&child_ages=${childAgesParam}`
      );

      if (!response.ok) {
        throw new Error("Failed to check availability");
      }

      const data = await response.json();
      setAvailabilityResult(data);
      setAvailableRooms(data.available_rooms || []);

      if (!data.available_rooms || data.available_rooms.length === 0) {
        toast.error("No Availability", {
          description: "No rooms are available for the selected dates",
        });
      }
    } catch (error) {
      console.error("Error checking availability:", error);
      toast.error("Error", {
        description: "Failed to check availability",
      });
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    if (open && availabilityData && availabilityData.hotel_id) {
      checkAvailability();
    }
  }, [open, availabilityData]);

  const handleRoomSelect = (room: any) => {
    if (!room.available || (room.available_rooms && room.available_rooms < availabilityData.number_of_rooms)) {
      toast.error("Cannot Select Room", {
        description: "This room doesn't have enough availability for your requested number of rooms.",
      });
      return;
    }
    setSelectedRoom(room);
  };

  const handleNext = () => {
    if (!selectedRoom) {
      toast.error("No Room Selected", {
        description: "Please select a room to continue",
      });
      return;
    }
    onNext(selectedRoom, availabilityData);
  };

  const formatPrice = (amount: number, currencyCode: string = "USD") => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: currencyCode,
    }).format(amount);
  };

  const getBoardTypeText = (boardType: string) => {
    switch (boardType) {
      case "none": return "Room Only";
      case "bb": return "Bed & Breakfast";
      case "hb": return "Half Board";
      case "fb": return "Full Board";
      default: return boardType;
    }
  };

  return (
    <FocusModal open={open} onOpenChange={onClose}>
      <FocusModal.Content className="max-w-4xl mx-auto my-8 max-h-[90vh]">
        <FocusModal.Header>
          <FocusModal.Title>
            Select Room
          </FocusModal.Title>
        </FocusModal.Header>

        <FocusModal.Body className="p-6 overflow-y-auto">
          {isLoading ? (
            <div className="flex items-center justify-center py-12">
              <div className="text-center">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
                <Text className="mt-4 text-gray-600">Checking availability...</Text>
              </div>
            </div>
          ) : (
            <div className="space-y-4">
              {/* Availability Summary */}
              {availabilityResult && (
                <div className="p-4 rounded-md bg-blue-50 border border-blue-200">
                  <Text className="text-sm text-blue-600">
                    {availabilityResult.nights} night{availabilityResult.nights !== 1 ? "s" : ""} · {" "}
                    {availabilityResult.adults} adult{availabilityResult.adults !== 1 ? "s" : ""}
                    {availabilityResult.children > 0 && ` · ${availabilityResult.children} child${availabilityResult.children !== 1 ? "ren" : ""}`}
                    {availabilityResult.infants > 0 && ` · ${availabilityResult.infants} infant${availabilityResult.infants !== 1 ? "s" : ""}`}
                    {" · "}{getBoardTypeText(availabilityData.board_type)}
                  </Text>
                </div>
              )}

              {/* Room List */}
              {availableRooms.length > 0 ? (
                <div className="space-y-4">
                  {availableRooms.map((room) => {
                    const isAvailable = room.available && (!room.available_rooms || room.available_rooms >= availabilityData.number_of_rooms);
                    const isSelected = selectedRoom?.id === room.id;
                    
                    return (
                      <div
                        key={room.id}
                        className={`
                          p-6 rounded-lg border cursor-pointer transition-all
                          ${isSelected 
                            ? "bg-green-50 border-green-300 ring-2 ring-green-200" 
                            : isAvailable 
                              ? "bg-white border-gray-200 hover:border-blue-300 hover:shadow-md" 
                              : "bg-gray-50 border-gray-200 opacity-70 cursor-not-allowed"
                          }
                        `}
                        onClick={() => isAvailable && handleRoomSelect(room)}
                      >
                        <div className="flex justify-between items-start">
                          <div className="flex-1">
                            <div className="flex items-center mb-2">
                              <Heading level="h3" className="text-lg font-semibold">
                                {room.title || room.name}
                              </Heading>
                              {isSelected && (
                                <CheckCircle className="w-5 h-5 text-green-600 ml-2" />
                              )}
                            </div>
                            
                            {room.description && (
                              <Text className="text-gray-600 mb-3">
                                {room.description}
                              </Text>
                            )}

                            <div className="flex items-center space-x-4 text-sm text-gray-500">
                              {room.room_size && (
                                <span>Room size: {room.room_size} m²</span>
                              )}
                              {room.bed_type && (
                                <span>Bed: {room.bed_type.charAt(0).toUpperCase() + room.bed_type.slice(1)}</span>
                              )}
                            </div>

                            <div className="flex items-center mt-3 text-sm">
                              <Users className="w-4 h-4 mr-1 text-gray-400" />
                              <span className="text-gray-600">
                                Max occupancy: {room.max_adults || 0} adults
                                {room.max_children > 0 && `, ${room.max_children} children`}
                                {room.max_infants > 0 && `, ${room.max_infants} infants`}
                              </span>
                            </div>
                          </div>

                          <div className="text-right ml-6">
                            {isAvailable && room.price ? (
                              <div>
                                <div className="text-2xl font-bold text-gray-900">
                                  {(() => {
                                    const boardType = availabilityData.board_type || "none";
                                    const currencyCode = (typeof room.price === "object" && room.price.currency_code) || availabilityResult?.currency_code || "USD";
                                    
                                    let totalPrice = 0;

                                    if (typeof room.price === "object") {
                                      if (room.price.meal_plans && room.price.meal_plans[boardType]) {
                                        const mealPlanPrice = room.price.meal_plans[boardType];
                                        totalPrice = mealPlanPrice.total_amount || mealPlanPrice.amount * availabilityResult.nights;
                                      } else {
                                        totalPrice = room.price.total_amount || room.price.amount * availabilityResult.nights;
                                      }
                                    } else if (typeof room.price === "number") {
                                      totalPrice = room.price * availabilityResult.nights;
                                    }

                                    return formatPrice(totalPrice, currencyCode);
                                  })()}
                                </div>
                                <div className="text-sm text-gray-600">
                                  {(() => {
                                    const boardType = availabilityData.board_type || "none";
                                    const currencyCode = (typeof room.price === "object" && room.price.currency_code) || availabilityResult?.currency_code || "USD";
                                    
                                    let perNightPrice = 0;

                                    if (typeof room.price === "object") {
                                      if (room.price.meal_plans && room.price.meal_plans[boardType]) {
                                        const mealPlanPrice = room.price.meal_plans[boardType];
                                        perNightPrice = mealPlanPrice.per_night_amount || mealPlanPrice.amount;
                                      } else {
                                        perNightPrice = room.price.per_night_amount || room.price.amount;
                                      }
                                    } else if (typeof room.price === "number") {
                                      perNightPrice = room.price;
                                    }

                                    return `${formatPrice(perNightPrice, currencyCode)} per night`;
                                  })()}
                                </div>
                                <div className="text-xs text-gray-500 mt-1">
                                  {availabilityResult.nights} night{availabilityResult.nights !== 1 ? "s" : ""} · {getBoardTypeText(availabilityData.board_type)}
                                </div>
                              </div>
                            ) : (
                              <div className="text-gray-500">
                                {!room.available ? "Not available" : "Insufficient rooms"}
                              </div>
                            )}

                            <div className="mt-3 text-right">
                              {room.available_rooms > 0 ? (
                                <span className={`text-xs font-medium ${
                                  room.available_rooms < availabilityData.number_of_rooms ? "text-red-600" : "text-green-600"
                                }`}>
                                  {room.available_rooms < availabilityData.number_of_rooms 
                                    ? "Insufficient rooms" 
                                    : `${room.available_rooms} room${room.available_rooms !== 1 ? "s" : ""} available`
                                  }
                                </span>
                              ) : (
                                <span className="text-xs font-medium text-red-600">
                                  Fully booked
                                </span>
                              )}
                            </div>
                          </div>
                        </div>
                      </div>
                    );
                  })}
                </div>
              ) : (
                <div className="text-center py-12">
                  <AlertCircle className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                  <Heading level="h3" className="text-gray-600 mb-2">
                    No Rooms Available
                  </Heading>
                  <Text className="text-gray-500">
                    No rooms are available for the selected dates and criteria.
                  </Text>
                </div>
              )}
            </div>
          )}
        </FocusModal.Body>

        <div className="flex justify-between items-center p-6 border-t bg-gray-50">
          <Button onClick={onBack}>
            Back
          </Button>
          <div className="flex space-x-3">
            <Button onClick={onClose}>
              Cancel
            </Button>
            <Button
              onClick={handleNext}
              disabled={!selectedRoom || isLoading}
            >
              Continue with Selected Room
            </Button>
          </div>
        </div>
      </FocusModal.Content>
    </FocusModal>
  );
};

export default RoomSelectionModal;
