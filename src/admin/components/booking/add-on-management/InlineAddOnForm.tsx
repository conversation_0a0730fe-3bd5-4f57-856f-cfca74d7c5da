import React, { useState, useEffect } from "react";
import { Button, Select, Text, toast, Input } from "@camped-ai/ui";
import { Plus, Loader2, Search } from "lucide-react";
import { useCascadingAddOnData } from "../../../hooks/booking/use-cascading-add-on-data";
import { useAddBookingAddOn } from "../../../hooks/concierge-management/use-booking-add-ons";
import { useDateBasedSupplierOfferings } from "../../../hooks/booking/use-date-based-supplier-offerings";

interface InlineAddOnFormProps {
  bookingId: string;
  checkInDate?: string;
  checkOutDate?: string;
  onAddOnAdded: () => void;
  onCancel: () => void;
  existingAddOnIds?: string[];
}

interface FormData {
  category_id: string;
  product_service_id: string;
  start_date: string;
  end_date: string;
  cost_price: string;
  quantity: string;
}

export const InlineAddOnForm: React.FC<InlineAddOnFormProps> = ({
  bookingId,
  checkInDate,
  checkOutDate,
  onAddOnAdded,
  onCancel,
  existingAddOnIds = [],
}) => {
  const [formData, setFormData] = useState<FormData>({
    category_id: "",
    product_service_id: "",
    start_date: checkInDate || "",
    end_date: checkOutDate || "",
    cost_price: "",
    quantity: "1",
  });
  const [errors, setErrors] = useState<Record<string, string>>({});

  // Use cascading data hook for categories and products
  const {
    categories,
    categoriesLoading,
    categoriesError,
    products,
    productsLoading,
    productsError,
  } = useCascadingAddOnData({
    categoryId: formData.category_id,
    productServiceId: formData.product_service_id,
    checkInDate: undefined, // Don't use booking dates for offerings
    checkOutDate: undefined, // Don't use booking dates for offerings
    existingAddOnIds,
    disableOfferingsFetch: true, // Disable automatic supplier offerings fetch
  });

  // Use date-based supplier offerings hook
  const {
    offerings,
    isLoading: offeringsLoading,
    searchOfferings,
    clearOfferings,
    hasSearched: hasSearchedOfferings,
  } = useDateBasedSupplierOfferings({
    productServiceId: formData.product_service_id,
    startDate: formData.start_date,
    endDate: formData.end_date,
    enabled: false, // Never auto-fetch, only manual search
  });

  // Add booking add-on mutation
  const addBookingAddOnMutation = useAddBookingAddOn();



  // Reset dependent fields when parent field changes
  useEffect(() => {
    if (formData.category_id) {
      setFormData(prev => ({
        ...prev,
        product_service_id: "",
        supplier_offering_id: "",
      }));
    }
  }, [formData.category_id]);

  useEffect(() => {
    if (formData.product_service_id) {
      setFormData(prev => ({
        ...prev,
        supplier_offering_id: "",
      }));
    }
  }, [formData.product_service_id]);

  const handleFieldChange = (field: keyof FormData, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value,
    }));

    // Clear error for this field
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: "",
      }));
    }

    // Reset cost price when dates change
    if (field === "start_date" || field === "end_date") {
      setFormData(prev => ({
        ...prev,
        cost_price: "",
      }));
    }
  };

  // Check if we can search for supplier offerings
  const canSearchOfferings = Boolean(
    formData.product_service_id &&
    formData.start_date &&
    formData.end_date &&
    !offeringsLoading
  );

  // Handle finding supplier offerings and auto-populate cost price
  const handleFindOfferings = async () => {
    if (!canSearchOfferings) return;

    await searchOfferings();
  };

  // Auto-populate cost price when offerings are found
  useEffect(() => {
    if (hasSearchedOfferings && offerings.length > 0) {
      // Find the highest calculated selling price in selling currency from all offerings
      const prices = offerings
        .map(offering => offering.calculated_selling_price_selling_currency || 0)
        .filter(price => price > 0);

      if (prices.length > 0) {
        const maxPrice = Math.max(...prices);
        setFormData(prev => ({
          ...prev,
          cost_price: maxPrice.toFixed(2),
        }));
      }
    }
  }, [hasSearchedOfferings, offerings]);

  const resetForm = () => {
    setFormData({
      category_id: "",
      product_service_id: "",
      start_date: checkInDate || "",
      end_date: checkOutDate || "",
      cost_price: "",
      quantity: "1",
    });
    setErrors({});
    clearOfferings(); // Clear any previously searched offerings
  };

  const handleCancel = () => {
    resetForm();
    onCancel();
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.category_id) {
      newErrors.category_id = "Category is required";
    }
    if (!formData.product_service_id) {
      newErrors.product_service_id = "Product & Service is required";
    }
    if (!formData.start_date) {
      newErrors.start_date = "Start date is required";
    }

    if (formData.start_date && formData.end_date && formData.start_date > formData.end_date) {
      newErrors.end_date = "End date must be after start date";
    }
    if (!formData.cost_price) {
      newErrors.cost_price = "Cost price is required";
    } else {
      const costPrice = parseFloat(formData.cost_price);
      if (isNaN(costPrice) || costPrice <= 0) {
        newErrors.cost_price = "Cost price must be a valid positive number";
      }
    }
    if (!formData.quantity) {
      newErrors.quantity = "Quantity is required";
    } else {
      const quantity = parseInt(formData.quantity);
      if (isNaN(quantity) || quantity <= 0) {
        newErrors.quantity = "Quantity must be a valid positive number";
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async () => {
    if (!validateForm()) {
      return;
    }

    try {
      // Find the selected product/service for name
      const selectedProduct = products.find(
        product => product.id === formData.product_service_id
      );

      if (!selectedProduct) {
        toast.error("Error", {
          description: "Selected product/service not found",
        });
        return;
      }

      // Parse the cost price from the form
      const costPrice = parseFloat(formData.cost_price);
      if (isNaN(costPrice) || costPrice <= 0) {
        toast.error("Error", {
          description: "Please enter a valid cost price",
        });
        return;
      }

      // Create the booking add-on input
      // Convert supplier offering ID to product variant ID
      // Product variants are created with ID pattern: variant_addon_{product_service_id}
      const productVariantId = `variant_addon_${formData.product_service_id}`;

      const quantity = parseInt(formData.quantity);

      const addOnInput = {
        concierge_order_id: bookingId,
        add_on_variant_id: productVariantId, // Use product variant ID instead of supplier offering ID
        add_on_name: selectedProduct.name,
        quantity: quantity, // Use quantity from form
        unit_price: costPrice, // Use the cost price from the form
        currency_code: "GBP", // Default currency
        customer_field_responses: {},
        add_on_metadata: {
          category_id: formData.category_id,
          product_service_id: formData.product_service_id,
          cost_price: costPrice,
          start_date: formData.start_date,
          end_date: formData.end_date,
        },
        // Include the new fields for the booking-addons API
        category_id: formData.category_id,
        start_date: formData.start_date,
        end_date: formData.end_date,
      };

      await addBookingAddOnMutation.mutateAsync(addOnInput);

      // Reset form and notify parent
      resetForm();
      onAddOnAdded();
    } catch (error) {
      console.error("Error adding add-on:", error);
      // Error handling is done in the mutation
    }
  };

  const isSubmitDisabled = !formData.category_id || !formData.product_service_id || !formData.start_date || !formData.end_date || !formData.cost_price || !formData.quantity || addBookingAddOnMutation.isPending;

  return (
    <div className="space-y-4">
      {/* Row 1: Category + Product & Service */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">

        {/* Category Dropdown */}
        <div className="space-y-2">
          <div className="flex items-center gap-2">
            <Text className="text-sm font-medium text-ui-fg-base">
              Category <span className="text-red-500">*</span>
            </Text>
            {categoriesLoading && (
              <Loader2 className="h-4 w-4 animate-spin text-ui-fg-muted" />
            )}
          </div>
          <Select
            value={formData.category_id}
            onValueChange={(value) => handleFieldChange("category_id", value)}
            disabled={categoriesLoading}
          >
            <Select.Trigger className={`h-8 transition-colors ${errors.category_id ? "border-red-500 focus:border-red-500" : "border-ui-border-base focus:border-ui-border-interactive hover:border-ui-border-strong"}`}>
              <Select.Value placeholder={categoriesLoading ? "Loading categories..." : "Select a category"} />
            </Select.Trigger>
            <Select.Content>
              {categoriesError ? (
                <div className="px-3 py-2 text-sm text-red-500">
                  Error loading categories. Please try again.
                </div>
              ) : categories.length === 0 && !categoriesLoading ? (
                <div className="px-3 py-2 text-sm text-ui-fg-muted">
                  No categories available
                </div>
              ) : (
                categories.map((category) => (
                  <Select.Item key={category.id} value={category.id}>
                    {category.name}
                  </Select.Item>
                ))
              )}
            </Select.Content>
          </Select>
          {errors.category_id && (
            <Text className="text-sm text-red-500">{errors.category_id}</Text>
          )}
          {categoriesError && (
            <Text className="text-sm text-red-500">
              Failed to load categories. Please try refreshing the page.
            </Text>
          )}
        </div>

        {/* Product & Service Dropdown */}
        <div className="space-y-2">
          <div className="flex items-center gap-2">
            <Text className="text-sm font-medium text-ui-fg-base">
              Product & Service <span className="text-red-500">*</span>
            </Text>
            {productsLoading && (
              <Loader2 className="h-4 w-4 animate-spin text-ui-fg-muted" />
            )}
          </div>
          <Select
            value={formData.product_service_id}
            onValueChange={(value) => handleFieldChange("product_service_id", value)}
            disabled={!formData.category_id || productsLoading}
          >
            <Select.Trigger className={`h-8 transition-colors ${errors.product_service_id ? "border-red-500 focus:border-red-500" : "border-ui-border-base focus:border-ui-border-interactive hover:border-ui-border-strong"} ${!formData.category_id ? "opacity-50 cursor-not-allowed" : ""}`}>
              <Select.Value
                placeholder={
                  !formData.category_id
                    ? "Select category first"
                    : productsLoading
                      ? "Loading products..."
                      : products.length === 0
                        ? "No products available"
                        : "Select product/service"
                }
              />
            </Select.Trigger>
            <Select.Content>
              {productsError ? (
                <div className="px-3 py-2 text-sm text-red-500">
                  Error loading products. Please try again.
                </div>
              ) : products.length === 0 && formData.category_id && !productsLoading ? (
                <div className="px-3 py-2 text-sm text-ui-fg-muted">
                  No products/services available for this category
                </div>
              ) : (
                products.map((product) => (
                  <Select.Item key={product.id} value={product.id}>
                    {product.name}
                  </Select.Item>
                ))
              )}
            </Select.Content>
          </Select>
          {errors.product_service_id && (
            <Text className="text-sm text-red-500">{errors.product_service_id}</Text>
          )}
          {productsError && (
            <Text className="text-sm text-red-500">
              Failed to load products/services. Please try refreshing the page.
            </Text>
          )}
        </div>
      </div>

      {/* Row 2: Date Fields */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        {/* Start Date */}
        <div className="space-y-2">
          <Text className="text-sm font-medium text-ui-fg-base">
            Start Date <span className="text-red-500">*</span>
          </Text>
          <Input
            type="date"
            value={formData.start_date}
            onChange={(e) => handleFieldChange("start_date", e.target.value)}
            className={`h-8 transition-colors ${errors.start_date ? "border-red-500 focus:border-red-500" : "border-ui-border-base focus:border-ui-border-interactive hover:border-ui-border-strong"}`}
          />
          {errors.start_date && (
            <Text className="text-sm text-red-500">{errors.start_date}</Text>
          )}
        </div>

        {/* End Date */}
        <div className="space-y-2">
          <Text className="text-sm font-medium text-ui-fg-base">
            End Date
          </Text>
          <Input
            type="date"
            value={formData.end_date}
            onChange={(e) => handleFieldChange("end_date", e.target.value)}
            className={`h-8 transition-colors ${errors.end_date ? "border-red-500 focus:border-red-500" : "border-ui-border-base focus:border-ui-border-interactive hover:border-ui-border-strong"}`}
          />
          {errors.end_date && (
            <Text className="text-sm text-red-500">{errors.end_date}</Text>
          )}
        </div>

        {/* Find Button */}
        <div className="space-y-2">
          <Text className="text-sm font-medium text-ui-fg-base">
            Find Offerings
          </Text>
          <Button
            onClick={handleFindOfferings}
            disabled={!canSearchOfferings}
            size="small"
            variant="secondary"
            className="h-8 flex items-center justify-start w-full text-left"
          >
            {offeringsLoading ? (
              <Loader2 className="h-4 w-4 animate-spin mr-1" />
            ) : (
              <Search className="h-4 w-4 mr-1" />
            )}
            Find
          </Button>
        </div>
      </div>

      {/* Row 3: Cost Price + Quantity */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="space-y-2">
          <div className="flex items-center gap-2">
            <Text className="text-sm font-medium text-ui-fg-base">
              Price <span className="text-red-500">*</span>
            </Text>
            {offeringsLoading && (
              <Loader2 className="h-4 w-4 animate-spin text-ui-fg-muted" />
            )}
          </div>
          <Input
            type="number"
            step="0.01"
            min="0"
            value={formData.cost_price}
            onChange={(e) => handleFieldChange("cost_price", e.target.value)}
            placeholder="Enter cost price or click Find to auto-populate"
            className={`h-8 transition-colors ${errors.cost_price ? "border-red-500 focus:border-red-500" : "border-ui-border-base focus:border-ui-border-interactive hover:border-ui-border-strong"}`}
          />
          {errors.cost_price && (
            <Text className="text-sm text-red-500">{errors.cost_price}</Text>
          )}
          {hasSearchedOfferings && offerings.length === 0 && (
            <Text className="text-sm text-ui-fg-muted">
              No supplier offerings found for selected dates. Please enter cost price manually.
            </Text>
          )}
          {hasSearchedOfferings && offerings.length > 0 && (
            <Text className="text-sm text-ui-fg-subtle">
              Auto-populated with highest calculated selling price from {offerings.length} offering(s)
            </Text>
          )}
        </div>

        {/* Quantity Field */}
        <div className="space-y-2">
          <Text className="text-sm font-medium text-ui-fg-base">
            Quantity <span className="text-red-500">*</span>
          </Text>
          <Input
            type="number"
            min="1"
            value={formData.quantity}
            onChange={(e) => handleFieldChange("quantity", e.target.value)}
            placeholder="Enter quantity"
            className={`h-8 transition-colors ${errors.quantity ? "border-red-500 focus:border-red-500" : "border-ui-border-base focus:border-ui-border-interactive hover:border-ui-border-strong"}`}
          />
          {errors.quantity && (
            <Text className="text-sm text-red-500">{errors.quantity}</Text>
          )}
        </div>
      </div>

      {/* Form Action Buttons */}
      <div className="flex gap-2 pt-4 border-t border-ui-border-base">
        <Button
          onClick={handleSubmit}
          disabled={isSubmitDisabled}
          size="small"
          className="h-8"
        >
          {addBookingAddOnMutation.isPending ? (
            <Loader2 className="h-4 w-4 animate-spin mr-2" />
          ) : (
            <Plus className="h-4 w-4 mr-2" />
          )}
          Save Add-on
        </Button>
        <Button
          onClick={handleCancel}
          variant="secondary"
          size="small"
          className="h-8"
          disabled={addBookingAddOnMutation.isPending}
        >
          Cancel
        </Button>
      </div>
    </div>
  );
};
