import React, { useState, useEffect } from "react";
import { Drawer, Text, Button, Container, Heading, toast } from "@camped-ai/ui";
import { useQuery } from "@tanstack/react-query";

import { AvailableAddOnsList } from "../add-on-selection/AvailableAddOnsList";
import { CustomerFieldsModal } from "../add-on-selection/CustomerFieldsModal";
interface CustomerField {
  field_id: string;
  field_key: string;
  field_name: string;
  field_type: string;
  is_required: boolean;
  options?: string[];
  display_order: number;
  validation_rules?: any;
}

interface AvailableAddOn {
  id: string;
  title: string;
  description: string;
  selling_price: number;
  per_day_child_price: number;
  selling_currency: string;
  category: string;
  service_level: string;
  destination_id?: string;
  destination_name?: string;
  hotel_id?: string;
  max_capacity?: number;
  customer_fields: CustomerField[];
  images: string[];
  metadata: Record<string, any>;
  adult_variant_id: string;
  child_variant_id: string;
  package_variant_id: string;
  validation?: {
    isValid: boolean;
    reason?: string;
  };
}

interface SelectedAddOn extends AvailableAddOn {
  quantity: number;
  customerFieldsCompleted: boolean;
  customerFieldResponses: Record<string, any>;
}

interface AddOnManagementPanelProps {
  open: boolean;
  onClose: () => void;
  onAddOnAdded: (addOn: SelectedAddOn) => void;
  bookingId: string;
  destinationId?: string;
  existingAddOnIds?: string[]; // To filter out already added add-ons
  checkInDate?: string;
  checkOutDate?: string;
  hotelId?: string;
}

// Fetch available add-ons from supplier-management products-services
const fetchAvailableAddOns = async (destinationId?: string, hotelId?: string): Promise<{ add_ons: AvailableAddOn[] }> => {
  console.log("🔍 Fetching available add-ons from supplier-management for destination:", destinationId, "hotel:", hotelId);

  try {
    // Build query parameters for supplier-management products-services API
    const params = new URLSearchParams({
      status: "active",
      limit: "100",
      offset: "0",
      sort_by: "name",
      sort_order: "asc"
    });

    // Filter by destination if provided (but don't be too restrictive)
    if (destinationId) {
      // Try destination filter but don't make it mandatory
      params.append("destination_id", destinationId);
    }

    // Filter by hotel if provided
    if (hotelId) {
      params.append("hotel_id", hotelId);
    }

    const url = `/admin/supplier-management/products-services?${params.toString()}`;
    console.log("🔍 Fetching from URL:", url);

    const response = await fetch(url, {
      method: "GET",
      credentials: "include",
      headers: {
        "Content-Type": "application/json",
        "Accept": "application/json",
      },
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error("❌ Response error:", response.status, errorText);
      throw new Error(`HTTP error! status: ${response.status} - ${errorText}`);
    }

    const data = await response.json();
    console.log("✅ Fetched products/services:", data);
    console.log("📊 Products/services count:", data.product_services?.length || 0);

    // Transform supplier products/services to add-on format
    const transformedAddOns: AvailableAddOn[] = (data.product_services || []).map((ps: any) => {
      const variantId = `variant_addon_${ps.id.replace('ps_', '')}`;
      console.log("🔄 Transforming product/service:", ps.name, "ID:", ps.id, "→ Variant ID:", variantId);
      return {
        id: ps.id,
        title: ps.name,
        description: ps.description || "",
        selling_price: parseFloat(ps.base_cost) || 0,
        per_day_child_price: 0, // Could be derived from custom_fields if needed
        selling_currency: "GBP", // Default currency
        category: ps.category?.name || "general",
        service_level: ps.service_level || "hotel",
        destination_id: ps.destination_id,
        destination_name: ps.destination?.name || "",
        hotel_id: ps.hotel_id,
        max_capacity: ps.custom_fields?.max_capacity || null,
        customer_fields: transformCustomFieldsToCustomerFields(ps.custom_fields || {}),
        images: [],
        metadata: {
          product_service_id: ps.id,
          type: ps.type,
          custom_fields: ps.custom_fields,
          base_cost: ps.base_cost,
          highest_price: ps.highest_price,
          highest_price_currency: ps.highest_price_currency,
        },
        // Variant IDs for compatibility (convert product service ID to variant ID)
        adult_variant_id: variantId,
        child_variant_id: variantId,
        package_variant_id: variantId,
      };
    });

    console.log("✅ Transformed add-ons count:", transformedAddOns.length);
    return { add_ons: transformedAddOns };
  } catch (error) {
    console.error("❌ Error fetching add-ons:", error);
    throw error;
  }
};

// Helper function to transform custom fields to customer fields format
const transformCustomFieldsToCustomerFields = (customFields: Record<string, any>): any[] => {
  const customerFields: any[] = [];

  Object.entries(customFields).forEach(([key, value]) => {
    // Skip certain fields that are not user inputs
    if (['max_capacity', 'base_cost', 'destination', 'hotels'].includes(key)) {
      return;
    }

    let fieldType = 'text';
    let options: string[] | undefined;

    // Determine field type based on value
    if (Array.isArray(value)) {
      fieldType = 'select';
      options = value.map(String);
    } else if (typeof value === 'boolean') {
      fieldType = 'select';
      options = ['Yes', 'No'];
    } else if (typeof value === 'number') {
      fieldType = 'number';
    } else if (key.toLowerCase().includes('date')) {
      fieldType = 'date';
    }

    customerFields.push({
      field_id: key,
      field_key: key,
      field_name: key.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase()),
      field_type: fieldType,
      is_required: false, // Could be made configurable
      options,
      display_order: customerFields.length,
    });
  });

  return customerFields;
};

export const AddOnManagementPanel: React.FC<AddOnManagementPanelProps> = ({
  open,
  onClose,
  onAddOnAdded,
  bookingId,
  destinationId,
  existingAddOnIds = [],
  checkInDate,
  checkOutDate,
  hotelId,
}) => {
  const [selectedAddOns, setSelectedAddOns] = useState<SelectedAddOn[]>([]);
  const [currentAddOnConfig, setCurrentAddOnConfig] = useState<SelectedAddOn | null>(null);
  const [showCustomerFieldsModal, setShowCustomerFieldsModal] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Fetch available add-ons from supplier-management with fallback
  const {
    data: addOnsResponse,
    isLoading,
    error,
  } = useQuery({
    queryKey: ["supplier-products-services-add-ons", destinationId, hotelId],
    queryFn: async () => {
      try {
        // First try with destination and hotel filters
        if (destinationId || hotelId) {
          const result = await fetchAvailableAddOns(destinationId, hotelId);
          if (result.add_ons && result.add_ons.length > 0) {
            return result;
          }
          console.log("🔄 No results with filters, trying without filters...");
        }

        // Fallback: fetch all active products/services
        return await fetchAvailableAddOns();
      } catch (error) {
        console.error("❌ Error fetching add-ons, trying fallback:", error);
        // Final fallback: fetch all active products/services
        return await fetchAvailableAddOns();
      }
    },
    enabled: open, // Only fetch when panel is open
    retry: 1, // Retry once on failure
  });

  // Validation functions
  const validateAddOnCompatibility = (addOn: AvailableAddOn): { isValid: boolean; reason?: string } => {
    // Check if already added
    if (existingAddOnIds.includes(addOn.id)) {
      return { isValid: false, reason: "This add-on is already added to the booking" };
    }

    // For now, be more permissive with destination/hotel filtering
    // We'll show all active products/services and let the user decide

    // Only exclude if there's a clear mismatch (strict hotel-only services)
    if (addOn.service_level === "hotel" && hotelId && addOn.hotel_id && addOn.hotel_id !== hotelId) {
      return { isValid: false, reason: "This service is only available for specific hotels" };
    }

    // Check date range compatibility (if add-on has seasonal restrictions)
    if (checkInDate && checkOutDate && addOn.metadata?.seasonal_restrictions) {
      // Add seasonal validation logic here if needed
    }

    return { isValid: true };
  };

  // Filter and validate available add-ons
  const availableAddOns = (addOnsResponse?.add_ons || [])
    .map(addOn => ({
      ...addOn,
      validation: validateAddOnCompatibility(addOn)
    }))
    .filter(addOn => addOn.validation.isValid);

  // Reset state when panel opens/closes
  useEffect(() => {
    if (!open) {
      setSelectedAddOns([]);
      setCurrentAddOnConfig(null);
      setShowCustomerFieldsModal(false);
    }
  }, [open]);

  const handleAddOnSelect = (addOn: AvailableAddOn, action?: string) => {
    if (action === "configure") {
      // Open configuration modal
      const existingSelection = selectedAddOns.find((s) => s.id === addOn.id);
      if (existingSelection) {
        setCurrentAddOnConfig(existingSelection);
        setShowCustomerFieldsModal(true);
      }
      return;
    }

    // Validate add-on before selection
    const validation = validateAddOnCompatibility(addOn);
    if (!validation.isValid) {
      toast.error("Cannot add service", {
        description: validation.reason || "This service is not available for your booking",
      });
      return;
    }

    // Toggle selection
    setSelectedAddOns((prevSelectedAddOns) => {
      const isSelected = prevSelectedAddOns.some((s) => s.id === addOn.id);

      if (isSelected) {
        // Remove from selection
        return prevSelectedAddOns.filter((s) => s.id !== addOn.id);
      } else {
        // Check for maximum capacity if specified
        if (addOn.max_capacity && addOn.max_capacity > 0) {
          // Add capacity validation logic here if needed
        }

        // Add to selection
        const newSelection: SelectedAddOn = {
          ...addOn,
          quantity: 1,
          customerFieldsCompleted: addOn.customer_fields.length === 0,
          customerFieldResponses: {},
        };

        // If has customer fields, open modal immediately
        if (addOn.customer_fields.length > 0) {
          setTimeout(() => {
            setCurrentAddOnConfig(newSelection);
            setShowCustomerFieldsModal(true);
          }, 0);
        }

        return [...prevSelectedAddOns, newSelection];
      }
    });
  };

  const handleCustomerFieldsSave = (addOnId: string, responses: Record<string, any>) => {
    setSelectedAddOns((prev) =>
      prev.map((addOn) =>
        addOn.id === addOnId
          ? {
              ...addOn,
              customerFieldResponses: responses,
              customerFieldsCompleted: true,
            }
          : addOn
      )
    );
    setShowCustomerFieldsModal(false);
    setCurrentAddOnConfig(null);
  };

  const handleCustomerFieldsCancel = () => {
    // If this was a new selection with required fields, remove it
    if (currentAddOnConfig && currentAddOnConfig.customer_fields.length > 0) {
      const hasRequiredFields = currentAddOnConfig.customer_fields.some(field => field.is_required);
      if (hasRequiredFields && !currentAddOnConfig.customerFieldsCompleted) {
        setSelectedAddOns(prev => prev.filter(addOn => addOn.id !== currentAddOnConfig.id));
      }
    }
    setShowCustomerFieldsModal(false);
    setCurrentAddOnConfig(null);
  };

  const handleAddToBooking = async () => {
    if (selectedAddOns.length === 0) return;

    setIsSubmitting(true);
    try {
      // Add each selected add-on to the booking
      for (const addOn of selectedAddOns) {
        await onAddOnAdded(addOn);
      }
      onClose();
    } catch (error) {
      console.error("Error adding add-ons to booking:", error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const canProceed = selectedAddOns.length > 0 && selectedAddOns.every(addOn => addOn.customerFieldsCompleted);

  return (
    <>
      <Drawer open={open} onOpenChange={onClose}>
        <Drawer.Content className="max-w-4xl">
          <Drawer.Header>
            <div className="flex items-center justify-between">
              <div>
                <Drawer.Title>Add Services to Booking</Drawer.Title>
                <Text className="text-ui-fg-subtle mt-1">
                  Select additional services to enhance the guest experience
                </Text>
              </div>
            </div>
          </Drawer.Header>

          <Drawer.Body className="p-6 overflow-y-auto">
            <div className="space-y-6">
              {/* Available Add-ons */}
              <div>
                <AvailableAddOnsList
                  addOns={availableAddOns}
                  selectedAddOns={selectedAddOns}
                  onAddOnSelect={handleAddOnSelect}
                  loading={isLoading}
                  error={error}
                />

                {/* Debug information in development */}
                {process.env.NODE_ENV === 'development' && !isLoading && (
                  <div className="mt-4 p-3 bg-gray-100 rounded text-xs">
                    <strong>Debug Info:</strong><br />
                    Destination ID: {destinationId || 'None'}<br />
                    Hotel ID: {hotelId || 'None'}<br />
                    API Response: {addOnsResponse ? 'Success' : 'None'}<br />
                    Total Available: {addOnsResponse?.add_ons?.length || 0}<br />
                    After Filtering: {availableAddOns.length}<br />
                    Existing Add-on IDs: {existingAddOnIds.join(', ') || 'None'}<br />
                    {error && (
                      <>
                        Error: {error.message}<br />
                        Error Details: {JSON.stringify(error, null, 2)}
                      </>
                    )}
                  </div>
                )}
              </div>

              {/* Selected Add-ons Summary */}
              {selectedAddOns.length > 0 && (
                <Container className="p-6">
                  <div className="space-y-4">
                    <Heading level="h3">Selected Services ({selectedAddOns.length})</Heading>
                    <div className="space-y-3">
                      {selectedAddOns.map((addOn) => (
                        <div key={addOn.id} className="flex items-center justify-between p-3 bg-ui-bg-subtle rounded-lg">
                          <div>
                            <Text className="font-medium">{addOn.title}</Text>
                            <Text className="text-sm text-ui-fg-subtle">
                              {new Intl.NumberFormat("en-US", {
                                style: "currency",
                                currency: addOn.selling_currency,
                              }).format(addOn.selling_price)}
                            </Text>
                          </div>
                          <div className="flex items-center gap-2">
                            {!addOn.customerFieldsCompleted && (
                              <Text className="text-xs text-ui-fg-error">Configuration required</Text>
                            )}
                            <Button
                              variant="secondary"
                              size="small"
                              onClick={() => setSelectedAddOns(prev => prev.filter(s => s.id !== addOn.id))}
                            >
                              Remove
                            </Button>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </Container>
              )}
            </div>
          </Drawer.Body>

          <Drawer.Footer>
            <div className="flex items-center justify-between">
              <Button variant="secondary" onClick={onClose}>
                Cancel
              </Button>
              <Button
                onClick={handleAddToBooking}
                disabled={!canProceed || isSubmitting}
                isLoading={isSubmitting}
              >
                Add {selectedAddOns.length} Service{selectedAddOns.length !== 1 ? 's' : ''} to Booking
              </Button>
            </div>
          </Drawer.Footer>
        </Drawer.Content>
      </Drawer>

      {/* Customer Fields Modal */}
      {showCustomerFieldsModal && currentAddOnConfig && (
        <CustomerFieldsModal
          addOn={currentAddOnConfig}
          onSave={handleCustomerFieldsSave}
          onCancel={handleCustomerFieldsCancel}
        />
      )}
    </>
  );
};

export default AddOnManagementPanel;
