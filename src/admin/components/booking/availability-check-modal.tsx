import React, { useState, useEffect } from "react";
import {
  FocusModal,
  Button,
  Input,
  Select,
  DatePicker,
  toast,
} from "@camped-ai/ui";
import { Controller, useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";

import { Users, Hotel } from "lucide-react";

const availabilitySchema = z.object({
  hotel_id: z.string().min(1, "Hotel is required"),
  check_in_date: z.date(),
  check_out_date: z.date(),
  adults: z.number().min(1, "At least 1 adult is required"),
  children: z.number().default(0),
  infants: z.number().default(0),
  number_of_rooms: z.number().min(1, "At least 1 room is required"),
  board_type: z.string().default("none"),
  child_ages: z.array(z.object({ age: z.number() })).default([]),
});

type AvailabilityFormData = z.infer<typeof availabilitySchema>;

interface AvailabilityCheckModalProps {
  open: boolean;
  onClose: () => void;
  onNext: (data: AvailabilityFormData) => void;
  initialData?: Partial<AvailabilityFormData>;
}

const AvailabilityCheckModal: React.FC<AvailabilityCheckModalProps> = ({
  open,
  onClose,
  onNext,
  initialData,
}) => {
  const [hotels, setHotels] = useState<any[]>([]);
  const [isLoading] = useState(false);

  const form = useForm<AvailabilityFormData>({
    resolver: zodResolver(availabilitySchema),
    defaultValues: {
      hotel_id: initialData?.hotel_id || "",
      check_in_date: initialData?.check_in_date || new Date(),
      check_out_date: initialData?.check_out_date || new Date(Date.now() + 24 * 60 * 60 * 1000),
      adults: initialData?.adults || 2,
      children: initialData?.children || 0,
      infants: initialData?.infants || 0,
      number_of_rooms: initialData?.number_of_rooms || 1,
      board_type: initialData?.board_type || "none",
      child_ages: initialData?.child_ages || [],
    },
  });

  const watchChildren = form.watch("children");

  // Fetch hotels
  const fetchHotels = async () => {
    try {
      const response = await fetch("/admin/hotel-management/hotels");
      if (!response.ok) throw new Error("Failed to fetch hotels");
      const data = await response.json();
      setHotels(data.hotels || []);
    } catch (error) {
      console.error("Error fetching hotels:", error);
      toast.error("Error", { description: "Failed to fetch hotels" });
    }
  };

  useEffect(() => {
    if (open) {
      fetchHotels();
    }
  }, [open]);

  // Update child ages when children count changes
  useEffect(() => {
    const currentChildAges = form.getValues("child_ages") || [];
    let newChildAges = [...currentChildAges];

    if (newChildAges.length < watchChildren) {
      while (newChildAges.length < watchChildren) {
        newChildAges.push({ age: 10 });
      }
    } else if (newChildAges.length > watchChildren) {
      newChildAges = newChildAges.slice(0, watchChildren);
    }

    form.setValue("child_ages", newChildAges);
  }, [watchChildren, form]);

  const onSubmit = (data: AvailabilityFormData) => {
    onNext(data);
  };

  return (
    <FocusModal open={open} onOpenChange={onClose}>
      <FocusModal.Content className="max-w-2xl mx-auto my-8">
        <FocusModal.Header>
          <FocusModal.Title>
            Check Availability
          </FocusModal.Title>
        </FocusModal.Header>

        <FocusModal.Body className="p-6">
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            {/* Hotel Selection */}
            <div className="space-y-2">
              <label className="text-sm font-medium flex items-center">
                <Hotel className="w-4 h-4 mr-2" />
                Hotel
              </label>
              <Controller
                control={form.control}
                name="hotel_id"
                render={({ field }) => (
                  <>
                    <Select value={field.value} onValueChange={field.onChange}>
                      <Select.Trigger>
                        <Select.Value placeholder="Select a hotel" />
                      </Select.Trigger>
                      <Select.Content>
                        {hotels.map((hotel) => (
                          <Select.Item key={hotel.id} value={hotel.id}>
                            {hotel.name}
                          </Select.Item>
                        ))}
                      </Select.Content>
                    </Select>
                    {form.formState.errors.hotel_id && (
                      <p className="text-sm text-red-500">
                        {form.formState.errors.hotel_id.message}
                      </p>
                    )}
                  </>
                )}
              />
            </div>

            {/* Dates */}
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <label className="text-sm font-medium">Check-in Date</label>
                <Controller
                  control={form.control}
                  name="check_in_date"
                  render={({ field }) => (
                    <>
                      <DatePicker value={field.value} onChange={field.onChange} />
                      {form.formState.errors.check_in_date && (
                        <p className="text-sm text-red-500">
                          {form.formState.errors.check_in_date.message}
                        </p>
                      )}
                    </>
                  )}
                />
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium">Check-out Date</label>
                <Controller
                  control={form.control}
                  name="check_out_date"
                  render={({ field }) => (
                    <>
                      <DatePicker value={field.value} onChange={field.onChange} />
                      {form.formState.errors.check_out_date && (
                        <p className="text-sm text-red-500">
                          {form.formState.errors.check_out_date.message}
                        </p>
                      )}
                    </>
                  )}
                />
              </div>
            </div>

            {/* Occupancy */}
            <div className="space-y-4">
              <label className="text-sm font-medium flex items-center">
                <Users className="w-4 h-4 mr-2" />
                Occupancy Details
              </label>
              
              <div className="grid grid-cols-4 gap-4">
                <div className="space-y-2">
                  <label className="text-sm font-medium">Adults</label>
                  <Controller
                    control={form.control}
                    name="adults"
                    render={({ field }) => (
                      <>
                        <Input
                          type="number"
                          min={1}
                          {...field}
                          onChange={(e) => field.onChange(parseInt(e.target.value) || 1)}
                        />
                        {form.formState.errors.adults && (
                          <p className="text-sm text-red-500">
                            {form.formState.errors.adults.message}
                          </p>
                        )}
                      </>
                    )}
                  />
                </div>

                <div className="space-y-2">
                  <label className="text-sm font-medium">Children</label>
                  <Controller
                    control={form.control}
                    name="children"
                    render={({ field }) => (
                      <>
                        <Input
                          type="number"
                          min={0}
                          {...field}
                          onChange={(e) => field.onChange(parseInt(e.target.value) || 0)}
                        />
                        {form.formState.errors.children && (
                          <p className="text-sm text-red-500">
                            {form.formState.errors.children.message}
                          </p>
                        )}
                      </>
                    )}
                  />
                </div>

                <div className="space-y-2">
                  <label className="text-sm font-medium">Infants</label>
                  <Controller
                    control={form.control}
                    name="infants"
                    render={({ field }) => (
                      <>
                        <Input
                          type="number"
                          min={0}
                          {...field}
                          onChange={(e) => field.onChange(parseInt(e.target.value) || 0)}
                        />
                        {form.formState.errors.infants && (
                          <p className="text-sm text-red-500">
                            {form.formState.errors.infants.message}
                          </p>
                        )}
                      </>
                    )}
                  />
                </div>

                <div className="space-y-2">
                  <label className="text-sm font-medium">Rooms</label>
                  <Controller
                    control={form.control}
                    name="number_of_rooms"
                    render={({ field }) => (
                      <>
                        <Input
                          type="number"
                          min={1}
                          {...field}
                          onChange={(e) => field.onChange(parseInt(e.target.value) || 1)}
                        />
                        {form.formState.errors.number_of_rooms && (
                          <p className="text-sm text-red-500">
                            {form.formState.errors.number_of_rooms.message}
                          </p>
                        )}
                      </>
                    )}
                  />
                </div>
              </div>
            </div>

            {/* Child Ages */}
            {watchChildren > 0 && (
              <div className="space-y-3">
                <label className="text-sm font-medium">Child Ages</label>
                <div className="grid grid-cols-4 gap-3">
                  {Array.from({ length: watchChildren }).map((_, index) => (
                    <div key={index} className="space-y-1">
                      <label className="text-xs font-medium">Child {index + 1}</label>
                      <Select
                        value={String(form.getValues("child_ages")?.[index]?.age || 10)}
                        onValueChange={(value) => {
                          const childAges = [...(form.getValues("child_ages") || [])];
                          while (childAges.length <= index) {
                            childAges.push({ age: 10 });
                          }
                          childAges[index] = { age: parseInt(value) };
                          form.setValue("child_ages", childAges);
                        }}
                      >
                        <Select.Trigger>
                          <Select.Value />
                        </Select.Trigger>
                        <Select.Content>
                          {Array.from({ length: 16 }, (_, i) => i + 2).map((age) => (
                            <Select.Item key={age} value={String(age)}>
                              {age} years
                            </Select.Item>
                          ))}
                        </Select.Content>
                      </Select>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Board Type */}
            <div className="space-y-2">
              <label className="text-sm font-medium">Board Type</label>
              <Controller
                control={form.control}
                name="board_type"
                render={({ field }) => (
                  <>
                    <Select value={field.value} onValueChange={field.onChange}>
                      <Select.Trigger>
                        <Select.Value placeholder="Select board type" />
                      </Select.Trigger>
                      <Select.Content>
                        <Select.Item value="none">Room Only</Select.Item>
                        <Select.Item value="bb">Bed & Breakfast</Select.Item>
                        <Select.Item value="hb">Half Board</Select.Item>
                        <Select.Item value="fb">Full Board</Select.Item>
                      </Select.Content>
                    </Select>
                    {form.formState.errors.board_type && (
                      <p className="text-sm text-red-500">
                        {form.formState.errors.board_type.message}
                      </p>
                    )}
                  </>
                )}
              />
            </div>

            {/* Actions */}
            <div className="flex justify-end space-x-3 pt-4 border-t">
              <Button type="button" onClick={onClose}>
                Cancel
              </Button>
              <Button type="submit" disabled={isLoading}>
                {isLoading ? "Checking..." : "Check Availability"}
              </Button>
            </div>
          </form>
        </FocusModal.Body>
      </FocusModal.Content>
    </FocusModal>
  );
};

export default AvailabilityCheckModal;
