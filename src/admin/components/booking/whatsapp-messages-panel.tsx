import React, { useState, useEffect, useRef } from "react";
import { But<PERSON>, Heading, Text, Input, toast, Toaster } from "@camped-ai/ui";
import axios from "axios";

interface WhatsAppMessage {
  id: string;
  content: string;
  from_phone: string;
  to_phone: string;
  status: string;
  statusCode: number; // 0: sent, 1: delivered, 2: read
  statusText: string;
  isOutbound: boolean;
  created_at: string;
}

interface WhatsAppMessagesPanelProps {
  bookingId?: string;
}

const WhatsAppMessagesPanel: React.FC<WhatsAppMessagesPanelProps> = ({
  bookingId,
}) => {
  const [messages, setMessages] = useState<WhatsAppMessage[]>([]);
  const [loading, setLoading] = useState(true);
  const [sending, setSending] = useState(false);
  const [newMessage, setNewMessage] = useState("");
  const [error, setError] = useState<string | null>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Fetch messages
  const fetchMessages = async () => {
    if (!bookingId) return;

    try {
      setLoading(true);
      const response = await axios.get(
        `/admin/hotel-management/bookings/${bookingId}/whatsapp`
      );

      // Check if we have new messages
      const newMessages = response.data.messages || [];
      const hasNewMessages = newMessages.length > messages.length;

      setMessages(newMessages);
      setError(null);

      // If there are new messages and this isn't the initial load, show a notification
      if (hasNewMessages && messages.length > 0) {
        // Find the newest message that wasn't in our previous list
        const newestMessage = newMessages.find(
          (newMsg: WhatsAppMessage) =>
            !messages.some((oldMsg: WhatsAppMessage) => oldMsg.id === newMsg.id)
        );

        if (newestMessage && !newestMessage.isOutbound) {
          // Show a toast notification for the new incoming message
          toast.info("New WhatsApp Message", {
            description:
              newestMessage.content.substring(0, 100) +
              (newestMessage.content.length > 100 ? "..." : ""),
          });
        }
      }
    } catch (err) {
      console.error("Error fetching WhatsApp messages:", err);
      setError("Failed to load WhatsApp messages");
    } finally {
      setLoading(false);
    }
  };

  // Send a new message
  const sendMessage = async () => {
    if (!bookingId || !newMessage.trim()) return;

    try {
      setSending(true);
      const response = await axios.post(
        `/admin/hotel-management/bookings/${bookingId}/whatsapp`,
        {
          message: newMessage,
        }
      );

      if (response.data.success) {
        console.log("Message sent successfully");
        toast.success("WhatsApp message sent successfully");
        setNewMessage("");
        // Refresh messages
        fetchMessages();
      } else {
        console.error("Failed to send message:", response.data.message);
        toast.error("Failed to send message", {
          description: response.data.message,
        });
      }
    } catch (err: any) {
      console.error("Error sending WhatsApp message:", err);
      toast.error("Failed to send message", {
        description: err.response?.data?.message || "An error occurred",
      });
    } finally {
      setSending(false);
    }
  };

  // Scroll to bottom when messages change
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  }, [messages]);

  // Fetch messages on component mount and set up auto-refresh
  useEffect(() => {
    if (bookingId) {
      fetchMessages();

      // Set up auto-refresh every 15 seconds
      const refreshInterval = setInterval(() => {
        fetchMessages();
      }, 15000);

      // Clean up interval on unmount
      return () => clearInterval(refreshInterval);
    }
  }, [bookingId]);

  // Render status icon based on status code
  const renderStatusIcon = (statusCode: number) => {
    switch (statusCode) {
      case 2: // Read
        return (
          <span className="text-blue-500 inline-flex">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="20"
              height="20"
              viewBox="0 0 20 20"
              fill="none"
            >
              <path
                d="M5.6 10.8L2.5 7.7L1.3 8.9L5.6 13.2L14.7 4.1L13.5 2.9L5.6 10.8Z"
                fill="#34B7F1"
              />
              <path
                d="M9.7 10.8L6.6 7.7L5.4 8.9L9.7 13.2L18.8 4.1L17.6 2.9L9.7 10.8Z"
                fill="#34B7F1"
              />
            </svg>
          </span>
        );
      case 1: // Delivered
        return (
          <span className="text-muted-foreground inline-flex">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="20"
              height="20"
              viewBox="0 0 20 20"
              fill="none"
            >
              <path
                d="M5.6 10.8L2.5 7.7L1.3 8.9L5.6 13.2L14.7 4.1L13.5 2.9L5.6 10.8Z"
                fill="currentColor"
              />
              <path
                d="M9.7 10.8L6.6 7.7L5.4 8.9L9.7 13.2L18.8 4.1L17.6 2.9L9.7 10.8Z"
                fill="currentColor"
              />
            </svg>
          </span>
        );
      case 0: // Sent
      default:
        return (
          <span className="text-muted-foreground">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="20"
              height="20"
              viewBox="0 0 20 20"
              fill="none"
            >
              <path
                d="M5.6 10.8L2.5 7.7L1.3 8.9L5.6 13.2L14.7 4.1L13.5 2.9L5.6 10.8Z"
                fill="currentColor"
              />
            </svg>
          </span>
        );
    }
  };

  // Format timestamp
  const formatTime = (timestamp: string) => {
    try {
      const date = new Date(timestamp);
      return date.toLocaleTimeString([], {
        hour: "2-digit",
        minute: "2-digit",
      });
    } catch (e) {
      return "";
    }
  };

  // Format date for message groups
  const formatDate = (timestamp: string) => {
    try {
      const date = new Date(timestamp);
      return date.toLocaleDateString([], {
        weekday: "long",
        year: "numeric",
        month: "long",
        day: "numeric",
      });
    } catch (e) {
      return "";
    }
  };

  // Group messages by date
  const groupMessagesByDate = () => {
    const groups: { [key: string]: WhatsAppMessage[] } = {};

    messages.forEach((message) => {
      const date = new Date(message.created_at).toLocaleDateString();
      if (!groups[date]) {
        groups[date] = [];
      }
      groups[date].push(message);
    });

    return Object.entries(groups).map(([date, messages]) => ({
      date,
      messages,
    }));
  };

  if (!bookingId) {
    return (
      <div className="p-4 bg-card rounded-lg shadow border border-border">
        <Text>No booking ID provided</Text>
      </div>
    );
  }

  return (
    <div className="flex flex-col h-full border border-border rounded-lg">
      {/* Add Toaster component for notifications */}
      <Toaster />

      <div className="flex justify-between items-center p-4 border-b border-border">
        <Heading level="h2" className="text-lg">
          WhatsApp
        </Heading>
        <Button
          variant="secondary"
          size="small"
          onClick={fetchMessages}
          disabled={loading}
        >
          Refresh
        </Button>
      </div>

      {/* Messages container */}
      <div className="flex-1 overflow-hidden flex flex-col">
        {/* Messages list */}
        <div
          className="flex-1 p-4 overflow-y-auto bg-muted/30"
          style={{ minHeight: "300px", maxHeight: "500px" }}
        >
          {loading ? (
            <div className="flex justify-center items-center h-full">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-foreground"></div>
            </div>
          ) : error ? (
            <div className="flex justify-center items-center h-full">
              <Text className="text-destructive">{error}</Text>
            </div>
          ) : messages.length === 0 ? (
            <div className="flex justify-center items-center h-full">
              <Text className="text-muted-foreground">No messages yet</Text>
            </div>
          ) : (
            <div className="space-y-4">
              {groupMessagesByDate().map((group) => (
                <div key={group.date} className="space-y-2">
                  <div className="flex justify-center">
                    <Text className="text-xs text-muted-foreground bg-background px-2 py-1 rounded border border-border">
                      {formatDate(group.messages[0].created_at)}
                    </Text>
                  </div>

                  {group.messages.map((message) => (
                    <div
                      key={message.id}
                      className={`flex ${
                        message.isOutbound ? "justify-end" : "justify-start"
                      }`}
                    >
                      <div
                        className={`max-w-[75%] rounded-lg p-3 ${
                          message.isOutbound
                            ? "bg-green-100 dark:bg-green-900/30 rounded-tr-none"
                            : "bg-card rounded-tl-none border border-border"
                        }`}
                      >
                        <Text className="whitespace-pre-wrap">
                          {message.content}
                        </Text>
                        <div className="flex justify-end items-center mt-1 space-x-1">
                          <Text className="text-xs text-muted-foreground">
                            {formatTime(message.created_at)}
                          </Text>
                          {message.isOutbound && (
                            <span className="text-xs">
                              {renderStatusIcon(message.statusCode)}
                            </span>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ))}
              <div ref={messagesEndRef} />
            </div>
          )}
        </div>

        {/* Message input */}
        <div className="p-4 border-t border-border">
          <div className="flex space-x-2">
            <Input
              placeholder="Type a message..."
              value={newMessage}
              onChange={(e) => setNewMessage(e.target.value)}
              onKeyDown={(e) => {
                if (e.key === "Enter" && !e.shiftKey) {
                  e.preventDefault();
                  sendMessage();
                }
              }}
              disabled={sending}
              className="flex-1"
            />
            <Button
              variant="primary"
              size="small"
              onClick={sendMessage}
              disabled={sending || !newMessage.trim()}
              className="whitespace-nowrap"
            >
              {sending ? (
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary-foreground mr-2"></div>
              ) : (
                <span className="mr-2">📤</span>
              )}
              Send
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default WhatsAppMessagesPanel;
