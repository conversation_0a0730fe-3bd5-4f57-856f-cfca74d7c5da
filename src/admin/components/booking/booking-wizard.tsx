import React, { useState } from "react";
import { But<PERSON>, Heading, Text, Container } from "@camped-ai/ui";
import { Calendar, Bed, User, CheckCircle } from "lucide-react";
import StepIndicator from "./step-indicator";
import AvailabilityCheckModal from "./availability-check-modal";
import RoomSelectionModal from "./room-selection-modal";
import CustomerInformationModal from "./customer-information-modal";
import BookingConfirmationModal from "./booking-confirmation-modal";
import { AddOnSelectionStep, SelectedAddOn } from "./add-on-selection";

const STEPS = [
  {
    id: 1,
    title: "Check Availability",
    description: "Select hotel and dates",
  },
  {
    id: 2,
    title: "Select Room",
    description: "Choose your room",
  },
  {
    id: 3,
    title: "Guest Information",
    description: "Enter guest details",
  },
  {
    id: 4,
    title: "Select Add-ons",
    description: "Choose additional services",
  },
  {
    id: 5,
    title: "Confirm Booking",
    description: "Review and confirm",
  },
];

interface BookingWizardProps {
  bookingId?: string | null;
  isEdit?: boolean;
}

const BookingWizard: React.FC<BookingWizardProps> = ({ isEdit = false }) => {
  const [currentStep, setCurrentStep] = useState(1);
  const [completedSteps, setCompletedSteps] = useState<number[]>([]);
  const [availabilityData, setAvailabilityData] = useState<any>(null);
  const [selectedRoom, setSelectedRoom] = useState<any>(null);
  const [customerData, setCustomerData] = useState<any>(null);
  const [selectedAddOns, setSelectedAddOns] = useState<SelectedAddOn[]>([]);

  // Modal states
  const [showAvailabilityModal, setShowAvailabilityModal] = useState(false);
  const [showRoomSelectionModal, setShowRoomSelectionModal] = useState(false);
  const [showCustomerModal, setShowCustomerModal] = useState(false);
  const [showAddOnSelection, setShowAddOnSelection] = useState(false);
  const [showConfirmationModal, setShowConfirmationModal] = useState(false);

  const handleStepComplete = (stepId: number) => {
    if (!completedSteps.includes(stepId)) {
      setCompletedSteps([...completedSteps, stepId]);
    }
  };

  const handleAvailabilityNext = (data: any) => {
    setAvailabilityData(data);
    handleStepComplete(1);
    setCurrentStep(2);
    setShowAvailabilityModal(false);
    setShowRoomSelectionModal(true);
  };

  const handleRoomSelectionNext = (room: any, availability: any) => {
    setSelectedRoom(room);
    setAvailabilityData(availability);
    handleStepComplete(2);
    setCurrentStep(3);
    setShowRoomSelectionModal(false);
    setShowCustomerModal(true);
  };

  const handleCustomerNext = (data: any) => {
    setCustomerData(data);
    handleStepComplete(3);
    setCurrentStep(4);
    setShowCustomerModal(false);
    setShowAddOnSelection(true);
  };

  const handleAddOnsSelected = (addOns: SelectedAddOn[]) => {
    setSelectedAddOns(addOns);
    handleStepComplete(4);
    setCurrentStep(5);
    setShowAddOnSelection(false);
    setShowConfirmationModal(true);
  };

  const handleAddOnBack = () => {
    setCurrentStep(3);
    setShowAddOnSelection(false);
    setShowCustomerModal(true);
  };

  const handleAddOnSkip = () => {
    setSelectedAddOns([]);
    handleStepComplete(4);
    setCurrentStep(5);
    setShowAddOnSelection(false);
    setShowConfirmationModal(true);
  };

  const handleBackToAvailability = () => {
    setCurrentStep(1);
    setShowRoomSelectionModal(false);
    setShowAvailabilityModal(true);
  };

  const handleBackToRoomSelection = () => {
    setCurrentStep(2);
    setShowCustomerModal(false);
    setShowRoomSelectionModal(true);
  };

  const handleBackToAddOns = () => {
    setCurrentStep(4);
    setShowConfirmationModal(false);
    setShowAddOnSelection(true);
  };

  const handleCloseAllModals = () => {
    setShowAvailabilityModal(false);
    setShowRoomSelectionModal(false);
    setShowCustomerModal(false);
    setShowAddOnSelection(false);
    setShowConfirmationModal(false);
  };

  const startBookingProcess = () => {
    setCurrentStep(1);
    setShowAvailabilityModal(true);
  };

  return (
    <Container>
      <div className="space-y-8">
        {/* Header */}
        <div className="text-center">
          <Heading level="h1" className="text-3xl font-bold text-gray-900 mb-4">
            {isEdit ? "Edit Booking" : "Create New Booking"}
          </Heading>
          <Text className="text-lg text-gray-600">
            {isEdit
              ? "Update your booking details using our step-by-step process"
              : "Create a new booking using our simple step-by-step process"}
          </Text>
        </div>

        {/* Step Indicator */}
        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <StepIndicator
            steps={STEPS}
            currentStep={currentStep}
            completedSteps={completedSteps}
          />
        </div>

        {/* Current Step Summary */}
        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <div className="text-center">
            {currentStep === 1 && !availabilityData && (
              <div className="space-y-4">
                <div className="flex justify-center">
                  <Calendar className="w-16 h-16 text-blue-500" />
                </div>
                <div>
                  <Heading level="h3" className="text-xl font-semibold mb-2">
                    Ready to Start?
                  </Heading>
                  <Text className="text-gray-600 mb-6">
                    Let's begin by checking availability for your desired dates
                    and hotel.
                  </Text>
                  <Button onClick={startBookingProcess} size="large">
                    Start Booking Process
                  </Button>
                </div>
              </div>
            )}

            {availabilityData && !selectedRoom && (
              <div className="space-y-4">
                <div className="flex justify-center">
                  <Bed className="w-16 h-16 text-green-500" />
                </div>
                <div>
                  <Heading level="h3" className="text-xl font-semibold mb-2">
                    Availability Confirmed
                  </Heading>
                  <Text className="text-gray-600 mb-6">
                    Great! We found available rooms for your dates. Click below
                    to view and select your room.
                  </Text>
                  <Button
                    onClick={() => setShowRoomSelectionModal(true)}
                    size="large"
                  >
                    View Available Rooms
                  </Button>
                </div>
              </div>
            )}

            {selectedRoom && !customerData && (
              <div className="space-y-4">
                <div className="flex justify-center">
                  <User className="w-16 h-16 text-purple-500" />
                </div>
                <div>
                  <Heading level="h3" className="text-xl font-semibold mb-2">
                    Room Selected
                  </Heading>
                  <Text className="text-gray-600 mb-2">
                    Perfect! You've selected:{" "}
                    <strong>{selectedRoom.title || selectedRoom.name}</strong>
                  </Text>
                  <Text className="text-gray-600 mb-6">
                    Now let's collect the guest information for this booking.
                  </Text>
                  <Button
                    onClick={() => setShowCustomerModal(true)}
                    size="large"
                  >
                    Enter Guest Details
                  </Button>
                </div>
              </div>
            )}

            {customerData && (
              <div className="space-y-4">
                <div className="flex justify-center">
                  <CheckCircle className="w-16 h-16 text-emerald-500" />
                </div>
                <div>
                  <Heading level="h3" className="text-xl font-semibold mb-2">
                    Almost Done!
                  </Heading>
                  <Text className="text-gray-600 mb-2">
                    Guest information collected for:{" "}
                    <strong>{customerData.guest_name}</strong>
                  </Text>
                  <Text className="text-gray-600 mb-6">
                    Review your booking details and confirm to complete the
                    reservation.
                  </Text>
                  <Button
                    onClick={() => setShowConfirmationModal(true)}
                    size="large"
                  >
                    Review & Confirm Booking
                  </Button>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Progress Summary Cards */}
        {(availabilityData || selectedRoom || customerData) && (
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {/* Availability Summary */}
            {availabilityData && (
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <div className="flex items-center mb-3">
                  <Calendar className="w-5 h-5 text-blue-600 mr-2" />
                  <Heading level="h3" className="font-semibold text-blue-700">
                    Dates & Occupancy
                  </Heading>
                </div>
                <div className="space-y-1 text-sm">
                  <Text>
                    Check-in:{" "}
                    {availabilityData.check_in_date?.toLocaleDateString()}
                  </Text>
                  <Text>
                    Check-out:{" "}
                    {availabilityData.check_out_date?.toLocaleDateString()}
                  </Text>
                  <Text>
                    {availabilityData.adults} adult
                    {availabilityData.adults !== 1 ? "s" : ""}
                    {availabilityData.children > 0 &&
                      `, ${availabilityData.children} child${
                        availabilityData.children !== 1 ? "ren" : ""
                      }`}
                    {availabilityData.infants > 0 &&
                      `, ${availabilityData.infants} infant${
                        availabilityData.infants !== 1 ? "s" : ""
                      }`}
                  </Text>
                </div>
                <Button
                  variant="secondary"
                  size="small"
                  className="mt-3"
                  onClick={() => setShowAvailabilityModal(true)}
                >
                  Edit
                </Button>
              </div>
            )}

            {/* Room Summary */}
            {selectedRoom && (
              <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                <div className="flex items-center mb-3">
                  <Bed className="w-5 h-5 text-green-600 mr-2" />
                  <Heading level="h3" className="font-semibold text-green-700">
                    Selected Room
                  </Heading>
                </div>
                <div className="space-y-1 text-sm">
                  <Text className="font-medium">
                    {selectedRoom.title || selectedRoom.name}
                  </Text>
                  {selectedRoom.room_size && (
                    <Text>Size: {selectedRoom.room_size} m²</Text>
                  )}
                  {selectedRoom.bed_type && (
                    <Text>Bed: {selectedRoom.bed_type}</Text>
                  )}
                </div>
                <Button
                  variant="secondary"
                  size="small"
                  className="mt-3"
                  onClick={() => setShowRoomSelectionModal(true)}
                >
                  Change Room
                </Button>
              </div>
            )}

            {/* Customer Summary */}
            {customerData && (
              <div className="bg-purple-50 border border-purple-200 rounded-lg p-4">
                <div className="flex items-center mb-3">
                  <User className="w-5 h-5 text-purple-600 mr-2" />
                  <Heading level="h3" className="font-semibold text-purple-700">
                    Guest Information
                  </Heading>
                </div>
                <div className="space-y-1 text-sm">
                  <Text className="font-medium">{customerData.guest_name}</Text>
                  <Text>{customerData.guest_email}</Text>
                  {customerData.guest_phone && (
                    <Text>{customerData.guest_phone}</Text>
                  )}
                </div>
                <Button
                  variant="secondary"
                  size="small"
                  className="mt-3"
                  onClick={() => setShowCustomerModal(true)}
                >
                  Edit Details
                </Button>
              </div>
            )}
          </div>
        )}
      </div>

      {/* Modals */}
      <AvailabilityCheckModal
        open={showAvailabilityModal}
        onClose={handleCloseAllModals}
        onNext={handleAvailabilityNext}
        initialData={availabilityData}
      />

      {availabilityData && (
        <RoomSelectionModal
          open={showRoomSelectionModal}
          onClose={handleCloseAllModals}
          onNext={handleRoomSelectionNext}
          onBack={handleBackToAvailability}
          availabilityData={availabilityData}
        />
      )}

      {availabilityData && (
        <CustomerInformationModal
          open={showCustomerModal}
          onClose={handleCloseAllModals}
          onNext={handleCustomerNext}
          onBack={handleBackToRoomSelection}
          availabilityData={availabilityData}
        />
      )}

      {/* Add-on Selection Step */}
      {showAddOnSelection && (
        <div className="fixed inset-0 z-50 bg-white overflow-y-auto">
          <div className="max-w-6xl mx-auto p-6">
            <AddOnSelectionStep
              onAddOnsSelected={handleAddOnsSelected}
              onBack={handleAddOnBack}
              onSkip={handleAddOnSkip}
            />
          </div>
        </div>
      )}

      {availabilityData &&
        selectedRoom &&
        customerData &&
        !showAddOnSelection && (
          <BookingConfirmationModal
            open={showConfirmationModal}
            onClose={handleCloseAllModals}
            onBack={handleBackToAddOns}
            availabilityData={availabilityData}
            selectedRoom={selectedRoom}
            customerData={customerData}
            selectedAddOns={selectedAddOns}
          />
        )}
    </Container>
  );
};

export default BookingWizard;
