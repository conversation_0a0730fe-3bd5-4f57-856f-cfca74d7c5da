import { MedusaRequest } from "@camped-ai/framework/http";
import jwt from "jsonwebtoken";

/**
 * Extract customer ID from various request sources
 * Following patterns used in existing store routes
 */
export function extractCustomerId(req: MedusaRequest): string | null {
  // Try to get customer ID from different sources in order of preference
  let customerId: string | null = null;

  // 1. Try session first
  if (req.session?.customer_id) {
    customerId = req.session.customer_id;
    console.log("Found customer ID in session:", customerId);
    return customerId;
  }

  // 2. Try user object from authentication context
  if ((req as any).user) {
    const user = (req as any).user;
    
    // Check if customer_id is in user object directly
    if (user.customer_id) {
      customerId = user.customer_id;
      console.log("Found customer ID in user object:", customerId);
      return customerId;
    }

    // Check if it's in the app_metadata
    if (user.app_metadata?.customer_id) {
      customerId = user.app_metadata.customer_id;
      console.log("Found customer ID in user app_metadata:", customerId);
      return customerId;
    }

    // Check if actor_id is a customer ID
    if (user.actor_id && user.actor_type === "customer") {
      customerId = user.actor_id;
      console.log("Found customer ID in user actor_id:", customerId);
      return customerId;
    }
  }

  // 3. Try to extract from Authorization header (Bearer token)
  if (req.headers.authorization) {
    try {
      const authHeader = req.headers.authorization;
      if (authHeader.startsWith("Bearer ")) {
        const token = authHeader.substring(7);
        
        // Try to decode JWT without verification (for customer_id extraction)
        const decoded = jwt.decode(token) as any;
        if (decoded?.customer_id) {
          customerId = decoded.customer_id;
          console.log("Found customer ID in Bearer token:", customerId);
          return customerId;
        }
        
        if (decoded?.actor_id && decoded?.actor_type === "customer") {
          customerId = decoded.actor_id;
          console.log("Found customer ID in Bearer token actor_id:", customerId);
          return customerId;
        }
      }
    } catch (error) {
      console.log("Error decoding Bearer token:", error);
    }
  }

  // 4. Try to extract from x-customer-id header directly
  if (req.headers["x-customer-id"]) {
    customerId = req.headers["x-customer-id"] as string;
    console.log("Found customer ID in x-customer-id header:", customerId);
    return customerId;
  }

  // 5. Fallback: Try to extract from cookies if still no customer ID
  if (req.headers.cookie) {
    try {
      // Find the JWT token in the cookies
      const cookies = req.headers.cookie.split(";").map((c) => c.trim());
      const jwtCookie = cookies.find((c) => c.startsWith("connect.sid="));
      
      if (jwtCookie) {
        const cookieValue = jwtCookie.split("=")[1];
        if (cookieValue) {
          // Try to decode the cookie value as JWT
          const decoded = jwt.decode(decodeURIComponent(cookieValue)) as any;
          if (decoded?.customer_id) {
            customerId = decoded.customer_id;
            console.log("Found customer ID in cookie JWT:", customerId);
            return customerId;
          }
        }
      }
    } catch (error) {
      console.log("Error extracting customer ID from cookies:", error);
    }
  }

  // 6. Try auth_context from Medusa authentication
  if ((req as any).auth_context?.actor_id && (req as any).auth_context?.actor_type === "customer") {
    customerId = (req as any).auth_context.actor_id;
    console.log("Found customer ID in auth_context:", customerId);
    return customerId;
  }

  console.log("No customer ID found in request");
  return null;
}

/**
 * Extract customer ID and throw error if not found
 */
export function requireCustomerId(req: MedusaRequest): string {
  const customerId = extractCustomerId(req);
  
  if (!customerId) {
    throw new Error("Customer authentication required");
  }
  
  return customerId;
}

/**
 * Validate customer ID format
 */
export function isValidCustomerId(customerId: string): boolean {
  // Medusa customer IDs typically start with 'cus_' followed by alphanumeric characters
  return /^cus_[a-zA-Z0-9]+$/.test(customerId);
}
