import { ADD_ON_SERVICE } from "../modules/hotel-management/add-on-service";
import AddOnServiceModuleService from "../modules/hotel-management/add-on-service/service";
import { SUPPLIER_PRODUCTS_SERVICES_MODULE } from "../modules/supplier-products-services";
import SupplierProductsServicesModuleService from "../modules/supplier-products-services/service";
import { SUPPLIER_MANAGEMENT_MODULE } from "../modules/vendor_management";
import SupplierModuleService from "../modules/vendor_management/service";
import { ITINERARY_SERVICE, CONCIERGE_MANAGEMENT_MODULE } from "../modules/concierge-management";
import ItineraryService from "../modules/concierge-management/itinerary-service";
import ConciergeManagementService from "../modules/concierge-management/service";
import { BaseRepository } from "../modules/concierge-management/repositories";
import { asClass } from "awilix";

/**
 * Utility function to ensure required services are registered in the container
 * This prevents Awilix resolution errors when services are not properly loaded
 */
export function ensureServicesRegistered(container: any) {
  // Register ADD_ON_SERVICE if not already registered
  if (!container.hasRegistration(ADD_ON_SERVICE)) {
    container.register({
      [ADD_ON_SERVICE]: {
        resolve: () => new AddOnServiceModuleService(container),
      },
    });
    console.log("✅ ADD_ON_SERVICE registered via utility");
  }

  // Register SUPPLIER_PRODUCTS_SERVICES_MODULE if not already registered
  if (!container.hasRegistration(SUPPLIER_PRODUCTS_SERVICES_MODULE)) {
    container.register({
      [SUPPLIER_PRODUCTS_SERVICES_MODULE]: {
        resolve: () => new SupplierProductsServicesModuleService(container),
      },
    });
    console.log("✅ SUPPLIER_PRODUCTS_SERVICES_MODULE registered via utility");
  }

  // Register SUPPLIER_MANAGEMENT_MODULE if not already registered
  if (!container.hasRegistration(SUPPLIER_MANAGEMENT_MODULE)) {
    container.register({
      [SUPPLIER_MANAGEMENT_MODULE]: {
        resolve: () => new SupplierModuleService(container),
      },
    });
    console.log("✅ SUPPLIER_MANAGEMENT_MODULE registered via utility");
  }

  // Register ITINERARY_SERVICE if not already registered
  if (!container.hasRegistration(ITINERARY_SERVICE)) {
    container.register({
      [ITINERARY_SERVICE]: asClass(ItineraryService).singleton(),
    });
    console.log("✅ ITINERARY_SERVICE registered via utility");
  }

  // Register itineraryService with string key for backward compatibility
  if (!container.hasRegistration("itineraryService")) {
    container.register({
      itineraryService: asClass(ItineraryService).singleton(),
    });
    console.log("✅ itineraryService (string key) registered via utility");
  }

  // Register CONCIERGE_MANAGEMENT_MODULE if not already registered
  if (!container.hasRegistration(CONCIERGE_MANAGEMENT_MODULE)) {
    container.register({
      [CONCIERGE_MANAGEMENT_MODULE]: {
        resolve: () => new ConciergeManagementService(container),
      },
    });
    console.log("✅ CONCIERGE_MANAGEMENT_MODULE registered via utility");
  }
}

/**
 * Utility function to ensure the itinerary service is registered and available
 */
export function ensureItineraryServiceRegistered(container: any) {
  // First ensure baseRepository is registered (required by ItineraryService)
  if (!container.hasRegistration("baseRepository")) {
    container.register({
      baseRepository: asClass(BaseRepository).singleton(),
    });
    console.log("✅ baseRepository registered via ensureItineraryServiceRegistered");
  }

  // Register ITINERARY_SERVICE if not already registered
  if (!container.hasRegistration(ITINERARY_SERVICE)) {
    container.register({
      [ITINERARY_SERVICE]: asClass(ItineraryService).singleton(),
    });
    console.log("✅ ITINERARY_SERVICE registered via ensureItineraryServiceRegistered");
  }

  // Register itineraryService with string key for backward compatibility
  if (!container.hasRegistration("itineraryService")) {
    container.register({
      itineraryService: asClass(ItineraryService).singleton(),
    });
    console.log("✅ itineraryService (string key) registered via ensureItineraryServiceRegistered");
  }

  return container.resolve("itineraryService");
}

/**
 * Utility function to safely create a ProductServiceSyncService instance
 * with proper service registration
 */
export function createProductServiceSyncService(container: any) {
  // Ensure all required services are registered
  ensureServicesRegistered(container);

  // Import here to avoid circular dependencies
  const { ProductServiceSyncService } = require("../services/product-service-sync-service");

  return new ProductServiceSyncService(container);
}
