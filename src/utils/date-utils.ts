/**
 * Centralized date utility functions to handle timezone-safe date parsing
 * for supplier offerings and other date-sensitive operations.
 */

/**
 * Parse a date string as UTC to avoid timezone issues.
 * This function ensures that date strings like "2025-08-20" are interpreted
 * as UTC midnight rather than local timezone, preventing date shifts.
 * 
 * @param dateValue - Date string in YYYY-MM-DD format, Date object, or null/undefined
 * @returns Date object in UTC or null if parsing fails
 */
export const parseDateAsUTC = (dateValue: any): Date | null => {
  if (!dateValue || dateValue === "") return null;

  try {
    // If it's already a Date object, return as-is
    if (dateValue instanceof Date) {
      return dateValue;
    }

    // If it's a string, parse it as UTC
    if (typeof dateValue === 'string') {
      const trimmedValue = dateValue.trim();

      // Try ISO format first (YYYY-MM-DD)
      const isoMatch = trimmedValue.match(/^(\d{4})-(\d{1,2})-(\d{1,2})$/);
      if (isoMatch) {
        const [, year, month, day] = isoMatch;
        // Create date in UTC - JavaScript months are 0-based, so subtract 1
        const date = new Date(Date.UTC(parseInt(year), parseInt(month) - 1, parseInt(day)));
        if (!isNaN(date.getTime())) {
          return date;
        }
      }

      // Try parsing as ISO string (handles YYYY-MM-DDTHH:mm:ss.sssZ format)
      if (trimmedValue.includes('T') || trimmedValue.includes('Z')) {
        const isoDate = new Date(trimmedValue);
        if (!isNaN(isoDate.getTime())) {
          return isoDate;
        }
      }

      // Fallback: try native Date parsing but convert to UTC
      const fallbackDate = new Date(trimmedValue);
      if (!isNaN(fallbackDate.getTime())) {
        // If the fallback parsing worked, create a UTC version
        return new Date(Date.UTC(
          fallbackDate.getFullYear(),
          fallbackDate.getMonth(),
          fallbackDate.getDate()
        ));
      }
    }

    return null;
  } catch (error) {
    console.error("Error parsing date as UTC:", dateValue, error);
    return null;
  }
};

/**
 * Parse a date and set it to start of day in UTC (00:00:00.000Z)
 * 
 * @param dateValue - Date string or Date object
 * @returns Date object set to start of day in UTC or null
 */
export const parseDateAsUTCStartOfDay = (dateValue: any): Date | null => {
  const date = parseDateAsUTC(dateValue);
  if (!date) return null;

  // Ensure it's set to start of day in UTC
  return new Date(Date.UTC(
    date.getUTCFullYear(),
    date.getUTCMonth(),
    date.getUTCDate(),
    0, 0, 0, 0
  ));
};

/**
 * Parse a date and set it to end of day in UTC (23:59:59.999Z)
 * 
 * @param dateValue - Date string or Date object
 * @returns Date object set to end of day in UTC or null
 */
export const parseDateAsUTCEndOfDay = (dateValue: any): Date | null => {
  const date = parseDateAsUTC(dateValue);
  if (!date) return null;

  // Set to end of day in UTC
  return new Date(Date.UTC(
    date.getUTCFullYear(),
    date.getUTCMonth(),
    date.getUTCDate(),
    23, 59, 59, 999
  ));
};

/**
 * Format a date as YYYY-MM-DD string in UTC
 * 
 * @param date - Date object
 * @returns Date string in YYYY-MM-DD format or null
 */
export const formatDateAsUTCString = (date: Date | null | undefined): string | null => {
  if (!date || !(date instanceof Date) || isNaN(date.getTime())) {
    return null;
  }

  const year = date.getUTCFullYear();
  const month = String(date.getUTCMonth() + 1).padStart(2, '0');
  const day = String(date.getUTCDate()).padStart(2, '0');
  
  return `${year}-${month}-${day}`;
};

/**
 * Legacy function for Excel date parsing - kept for backward compatibility
 * but now uses the UTC-safe parsing internally
 * 
 * @param dateValue - Date value from Excel or other sources
 * @returns Date object or null
 * @deprecated Use parseDateAsUTC instead
 */
export const parseExcelDate = (dateValue: any): Date | null => {
  return parseDateAsUTC(dateValue);
};
