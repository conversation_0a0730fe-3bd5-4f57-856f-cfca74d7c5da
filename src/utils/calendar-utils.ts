export function getCategoryColor(categoryName: string): string {
  switch (categoryName.toLowerCase()) {
    case "transportation":
      return "bg-transportation";
    case "gym":
      return "bg-gym";
    case "accommodation":
      return "bg-accommodation";
    case "dining":
      return "bg-dining";
    case "spa & wellness":
      return "bg-spa";
    case "events & entertainment":
      return "bg-events";
    default:
      return "bg-primary";
  }
}

export function formatDateRange(from: Date, to: Date): string {
  const fromStr = from.toLocaleDateString();
  const toStr = to.toLocaleDateString();

  if (fromStr === toStr) {
    return fromStr;
  }

  return `${fromStr} - ${toStr}`;
}

export function isEventInDateRange(
  eventStart: Date,
  eventEnd: Date,
  rangeStart: Date,
  rangeEnd: Date
): boolean {
  return eventStart <= rangeEnd && eventEnd >= rangeStart;
}
