/**
 * Cart validation utilities for Medusa.js standards compliance
 */

import { MedusaError, Modules } from "@camped-ai/framework/utils";
import { MedusaContainer } from "@camped-ai/framework/types";

export interface CartValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}

export interface RegionCurrencyPair {
  region_id: string;
  currency_code: string;
  region_name?: string;
  is_default?: boolean;
}

/**
 * Validates that a region and currency combination is valid
 */
export async function validateRegionCurrency(
  container: MedusaContainer,
  region_id: string,
  currency_code: string
): Promise<CartValidationResult> {
  const result: CartValidationResult = {
    isValid: true,
    errors: [],
    warnings: []
  };

  try {
    // Get region module service
    const regionModuleService = container.resolve(Modules.REGION);
    
    // Validate region exists
    let region;
    try {
      region = await regionModuleService.retrieveRegion(region_id);
    } catch (error) {
      result.isValid = false;
      result.errors.push(`Region with ID '${region_id}' not found`);
      return result;
    }

    // Validate currency matches region
    if (region.currency_code !== currency_code) {
      result.isValid = false;
      result.errors.push(
        `Currency '${currency_code}' does not match region currency '${region.currency_code}'`
      );
    }

    // Check if region is active
    if (region.metadata?.is_disabled) {
      result.isValid = false;
      result.errors.push(`Region '${region.name}' is currently disabled`);
    }

  } catch (error) {
    result.isValid = false;
    result.errors.push(`Failed to validate region/currency: ${error.message}`);
  }

  return result;
}

/**
 * Gets the default region for cart creation
 */
export async function getDefaultRegion(
  container: MedusaContainer
): Promise<RegionCurrencyPair | null> {
  try {
    const regionModuleService = container.resolve(Modules.REGION);

    // List all regions and find the default one
    const regions = await regionModuleService.listRegions({
      metadata: { is_default: true }
    });

    if (regions.length === 0) {
      // Fallback: get the first available region
      const allRegions = await regionModuleService.listRegions({}, { take: 1 });
      if (allRegions.length > 0) {
        return {
          region_id: allRegions[0].id,
          currency_code: allRegions[0].currency_code,
          region_name: allRegions[0].name,
          is_default: false
        };
      }

      // No regions found at all - this is a critical setup issue
      console.error("❌ CRITICAL: No regions found in the system!");
      console.error("   Cart creation will fail until regions are created.");
      console.error("   Please run the region setup loader or create regions via admin API.");
      return null;
    }

    const defaultRegion = regions[0];
    return {
      region_id: defaultRegion.id,
      currency_code: defaultRegion.currency_code,
      region_name: defaultRegion.name,
      is_default: true
    };

  } catch (error) {
    console.error("Failed to get default region:", error);
    return null;
  }
}

/**
 * Checks if the system has any regions configured
 */
export async function hasRegionsConfigured(container: MedusaContainer): Promise<boolean> {
  try {
    const regionModuleService = container.resolve(Modules.REGION);
    const regions = await regionModuleService.listRegions({}, { take: 1 });
    return regions.length > 0;
  } catch (error) {
    console.error("Failed to check region configuration:", error);
    return false;
  }
}

/**
 * Validates cart line items
 */
export async function validateCartItems(
  container: MedusaContainer,
  items: any[]
): Promise<CartValidationResult> {
  const result: CartValidationResult = {
    isValid: true,
    errors: [],
    warnings: []
  };

  if (!items || items.length === 0) {
    result.warnings.push("Cart has no items");
    return result;
  }

  try {
    const productModuleService = container.resolve(Modules.PRODUCT);

    for (let i = 0; i < items.length; i++) {
      const item = items[i];
      
      // Validate required fields
      if (!item.variant_id) {
        result.isValid = false;
        result.errors.push(`Item ${i + 1}: variant_id is required`);
        continue;
      }

      if (!item.quantity || item.quantity <= 0) {
        result.isValid = false;
        result.errors.push(`Item ${i + 1}: quantity must be greater than 0`);
        continue;
      }

      // Validate variant exists
      try {
        const variant = await productModuleService.retrieveProductVariant(item.variant_id);
        
        // Check if variant is active
        if (variant.metadata?.is_disabled) {
          result.isValid = false;
          result.errors.push(`Item ${i + 1}: Product variant is disabled`);
        }

        // Validate custom pricing if provided
        if (item.unit_price !== undefined) {
          if (typeof item.unit_price !== 'number' || item.unit_price < 0) {
            result.isValid = false;
            result.errors.push(`Item ${i + 1}: unit_price must be a positive number`);
          } else {
            result.warnings.push(`Item ${i + 1}: Using custom pricing (${item.unit_price})`);
          }
        }

      } catch (error) {
        result.isValid = false;
        result.errors.push(`Item ${i + 1}: Product variant '${item.variant_id}' not found`);
      }
    }

  } catch (error) {
    result.isValid = false;
    result.errors.push(`Failed to validate cart items: ${error.message}`);
  }

  return result;
}

/**
 * Validates sales channel access
 */
export async function validateSalesChannel(
  container: MedusaContainer,
  sales_channel_id?: string
): Promise<CartValidationResult> {
  const result: CartValidationResult = {
    isValid: true,
    errors: [],
    warnings: []
  };

  if (!sales_channel_id) {
    result.warnings.push("No sales channel specified - using default");
    return result;
  }

  try {
    const salesChannelModuleService = container.resolve(Modules.SALES_CHANNEL);
    
    const salesChannel = await salesChannelModuleService.retrieveSalesChannel(sales_channel_id);
    
    if (salesChannel.is_disabled) {
      result.isValid = false;
      result.errors.push(`Sales channel '${salesChannel.name}' is disabled`);
    }

  } catch (error) {
    result.isValid = false;
    result.errors.push(`Sales channel '${sales_channel_id}' not found`);
  }

  return result;
}

/**
 * Comprehensive cart validation
 */
export async function validateCartCreation(
  container: MedusaContainer,
  cartData: {
    region_id?: string;
    currency_code?: string;
    items?: any[];
    sales_channel_id?: string;
    email?: string;
  }
): Promise<CartValidationResult> {
  const result: CartValidationResult = {
    isValid: true,
    errors: [],
    warnings: []
  };

  // Validate required fields
  if (!cartData.region_id) {
    result.isValid = false;
    result.errors.push("region_id is required");
  }

  if (!cartData.currency_code) {
    result.isValid = false;
    result.errors.push("currency_code is required");
  }

  // If basic validation fails, return early
  if (!result.isValid) {
    return result;
  }

  // Validate region/currency combination
  const regionValidation = await validateRegionCurrency(
    container,
    cartData.region_id!,
    cartData.currency_code!
  );
  
  result.errors.push(...regionValidation.errors);
  result.warnings.push(...regionValidation.warnings);
  if (!regionValidation.isValid) {
    result.isValid = false;
  }

  // Validate items if provided
  if (cartData.items && cartData.items.length > 0) {
    const itemsValidation = await validateCartItems(container, cartData.items);
    result.errors.push(...itemsValidation.errors);
    result.warnings.push(...itemsValidation.warnings);
    if (!itemsValidation.isValid) {
      result.isValid = false;
    }
  }

  // Validate sales channel if provided
  const salesChannelValidation = await validateSalesChannel(
    container,
    cartData.sales_channel_id
  );
  result.errors.push(...salesChannelValidation.errors);
  result.warnings.push(...salesChannelValidation.warnings);
  if (!salesChannelValidation.isValid) {
    result.isValid = false;
  }

  // Validate email (Medusa uses email to associate with customer)
  if (!cartData.email) {
    result.warnings.push("No email provided - cart will be created as guest cart");
  }

  return result;
}

/**
 * Helper function to throw validation errors
 */
export function throwValidationErrors(validation: CartValidationResult): void {
  if (!validation.isValid) {
    throw new MedusaError(
      MedusaError.Types.INVALID_DATA,
      `Cart validation failed: ${validation.errors.join(', ')}`
    );
  }
}
