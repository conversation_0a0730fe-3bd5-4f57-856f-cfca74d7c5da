/**
 * Comprehensive currency utilities for Medusa compliance
 * 
 * This module provides utilities to ensure all currency handling follows
 * Medusa's recommended practices of storing amounts in smallest currency units
 * (e.g., cents for USD) and displaying them in proper decimal format.
 */

/**
 * Get decimal digits for a currency code
 * Based on ISO 4217 standard
 */
export function getCurrencyDecimalDigits(currencyCode: string): number {
  const code = currencyCode.toUpperCase();
  
  // Currencies with 0 decimal places
  const zeroDecimalCurrencies = [
    "BIF", "CLP", "DJF", "GNF", "JPY", "KMF", "KRW", 
    "MGA", "PYG", "RWF", "UGX", "VND", "VUV", "XAF", "XOF", "XPF"
  ];
  
  // Currencies with 3 decimal places
  const threeDecimalCurrencies = ["BHD", "IQD", "JOD", "KWD", "OMR", "TND"];
  
  if (zeroDecimalCurrencies.includes(code)) return 0;
  if (threeDecimalCurrencies.includes(code)) return 3;
  
  // Default to 2 decimal places for most currencies
  return 2;
}

/**
 * Convert amount from display format to smallest currency unit
 * @param amount - Amount in display format (e.g., 10.50)
 * @param currencyCode - Currency code (e.g., "USD")
 * @returns Amount in smallest currency unit (e.g., 1050 cents)
 */
export function toSmallestCurrencyUnit(amount: number, currencyCode: string): number {
  const decimalDigits = getCurrencyDecimalDigits(currencyCode);
  return Math.round(amount * Math.pow(10, decimalDigits));
}

/**
 * Convert amount from smallest currency unit to display format
 * @param amount - Amount in smallest currency unit (e.g., 1050 cents)
 * @param currencyCode - Currency code (e.g., "USD")
 * @returns Amount in display format (e.g., 10.50)
 */
export function fromSmallestCurrencyUnit(amount: number, currencyCode: string): number {
  const decimalDigits = getCurrencyDecimalDigits(currencyCode);
  return amount / Math.pow(10, decimalDigits);
}

/**
 * Format currency amount for display using Intl.NumberFormat
 * @param amount - Amount in smallest currency unit (e.g., 1050 cents)
 * @param currencyCode - Currency code (e.g., "USD")
 * @param locale - Locale for formatting (defaults to "en-US")
 * @returns Formatted currency string (e.g., "$10.50")
 */
export function formatCurrencyDisplay(
  amount: number,
  currencyCode: string,
  locale: string = "en-US"
): string {
  const displayAmount = fromSmallestCurrencyUnit(amount, currencyCode);
  
  return new Intl.NumberFormat(locale, {
    style: "currency",
    currency: currencyCode.toUpperCase(),
  }).format(displayAmount);
}

/**
 * Parse currency string to smallest unit
 * @param value - Currency string (e.g., "$10.50" or "10.50")
 * @param currencyCode - Currency code (e.g., "USD")
 * @returns Amount in smallest currency unit (e.g., 1050 cents)
 */
export function parseCurrencyToSmallestUnit(value: string, currencyCode: string): number {
  // Remove currency symbols and non-numeric characters except decimal point and minus sign
  const cleanValue = value.replace(/[^\d.-]/g, "");
  const parsed = parseFloat(cleanValue);
  
  if (isNaN(parsed)) return 0;
  
  return toSmallestCurrencyUnit(parsed, currencyCode);
}

/**
 * Validate that an amount is in the correct smallest unit format
 * @param amount - Amount to validate
 * @param currencyCode - Currency code
 * @returns True if amount appears to be in smallest units
 */
export function isAmountInSmallestUnit(amount: number, currencyCode: string): boolean {
  const decimalDigits = getCurrencyDecimalDigits(currencyCode);
  
  // For currencies with decimal places, amounts in smallest units should be integers
  if (decimalDigits > 0) {
    return Number.isInteger(amount);
  }
  
  // For zero-decimal currencies, any number is valid
  return true;
}

/**
 * Convert legacy decimal amounts to smallest units
 * Use this for migrating existing data
 * @param amount - Amount in decimal format
 * @param currencyCode - Currency code
 * @returns Amount in smallest currency unit
 */
export function migrateLegacyAmount(amount: number, currencyCode: string): number {
  // If the amount is already likely in smallest units (integer for decimal currencies), return as-is
  if (isAmountInSmallestUnit(amount, currencyCode)) {
    return amount;
  }
  
  // Otherwise, convert from decimal to smallest unit
  return toSmallestCurrencyUnit(amount, currencyCode);
}

/**
 * Create a currency input handler for React forms
 * @param currencyCode - Currency code
 * @param onChange - Callback that receives amount in smallest units
 * @returns Input change handler
 */
export function createCurrencyInputHandler(
  currencyCode: string,
  onChange: (amount: number) => void
) {
  return (event: React.ChangeEvent<HTMLInputElement>) => {
    const displayValue = parseFloat(event.target.value) || 0;
    const smallestUnitValue = toSmallestCurrencyUnit(displayValue, currencyCode);
    onChange(smallestUnitValue);
  };
}

/**
 * Get display value for currency input
 * @param amount - Amount in smallest currency unit
 * @param currencyCode - Currency code
 * @returns Display value for input field
 */
export function getCurrencyInputDisplayValue(amount: number, currencyCode: string): number {
  return fromSmallestCurrencyUnit(amount, currencyCode);
}

/**
 * Currency conversion utilities
 */
export const CurrencyHelpers = {
  toSmallestUnit: toSmallestCurrencyUnit,
  fromSmallestUnit: fromSmallestCurrencyUnit,
  format: formatCurrencyDisplay,
  parse: parseCurrencyToSmallestUnit,
  isSmallestUnit: isAmountInSmallestUnit,
  migrate: migrateLegacyAmount,
  createInputHandler: createCurrencyInputHandler,
  getInputDisplayValue: getCurrencyInputDisplayValue,
  getDecimalDigits: getCurrencyDecimalDigits,
};

export default CurrencyHelpers;
