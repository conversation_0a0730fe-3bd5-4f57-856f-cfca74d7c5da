/**
 * URL validation utilities for form inputs
 * Provides comprehensive URL validation with different levels of strictness
 */

/**
 * Basic URL validation - checks if a string looks like a URL
 * More permissive, allows common URL patterns
 */
export function isValidUrl(url: string): boolean {
  if (!url || url.trim() === '') return true; // Empty URLs are valid (optional fields)
  
  const trimmedUrl = url.trim();
  
  // Check for basic URL patterns
  const urlPatterns = [
    /^https?:\/\/.+/i, // http:// or https://
    /^www\..+\..+/i,   // www.example.com
    /^[a-zA-Z0-9-]+\.[a-zA-Z]{2,}/i, // example.com
  ];
  
  return urlPatterns.some(pattern => pattern.test(trimmedUrl));
}

/**
 * Strict URL validation using URL constructor
 * Only accepts properly formatted URLs with protocol
 */
export function isValidStrictUrl(url: string): boolean {
  if (!url || url.trim() === '') return true; // Empty URLs are valid (optional fields)
  
  try {
    new URL(url.trim());
    return true;
  } catch {
    return false;
  }
}

/**
 * Normalize URL by adding protocol if missing
 * Helps users by automatically adding https:// if needed
 */
export function normalizeUrl(url: string): string {
  if (!url || url.trim() === '') return '';
  
  const trimmedUrl = url.trim();
  
  // If it already has a protocol, return as-is
  if (/^https?:\/\//i.test(trimmedUrl)) {
    return trimmedUrl;
  }
  
  // If it starts with www., add https://
  if (/^www\./i.test(trimmedUrl)) {
    return `https://${trimmedUrl}`;
  }
  
  // If it looks like a domain, add https://
  if (/^[a-zA-Z0-9-]+\.[a-zA-Z]{2,}/i.test(trimmedUrl)) {
    return `https://${trimmedUrl}`;
  }
  
  // Otherwise, return as-is (might be invalid, but let validation handle it)
  return trimmedUrl;
}

/**
 * Get URL validation error message
 */
export function getUrlValidationError(url: string, fieldName: string = 'URL'): string | null {
  if (!url || url.trim() === '') return null; // Empty is valid
  
  if (!isValidUrl(url)) {
    return `${fieldName} must be a valid URL (e.g., https://example.com or www.example.com)`;
  }
  
  return null;
}

/**
 * React Hook Form compatible URL validator
 */
export function createUrlValidator(fieldName: string = 'URL') {
  return (value: string) => {
    return getUrlValidationError(value, fieldName) || true;
  };
}

/**
 * Zod schema for URL validation
 */
export function createUrlSchema(fieldName: string = 'URL') {
  return (value: string) => {
    const error = getUrlValidationError(value, fieldName);
    if (error) {
      throw new Error(error);
    }
    return value;
  };
}
