#!/usr/bin/env node

/**
 * Debug script to capture SQL queries from the enhanced supplier management API
 * This script will make API calls and capture the detailed logs including SQL queries
 */

const fetch = require('node-fetch');

const BASE_URL = 'http://localhost:9000';
const API_ENDPOINT = '/admin/supplier-management/on-request';

async function debugSQLQueries() {
  console.log('🔍 SQL Query Debug Script for Enhanced Supplier Management API');
  console.log('=' .repeat(80));

  try {
    console.log('\n📋 Making API call to capture SQL queries...');
    console.log(`URL: ${BASE_URL}${API_ENDPOINT}?status=under_review&limit=5`);
    
    const response = await fetch(`${BASE_URL}${API_ENDPOINT}?status=under_review&limit=5`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        // Add any authentication headers if needed
      }
    });
    
    console.log(`\n📊 Response Status: ${response.status} ${response.statusText}`);
    
    if (!response.ok) {
      const errorText = await response.text();
      console.log(`❌ Error Response: ${errorText}`);
      return;
    }
    
    const data = await response.json();
    
    console.log('\n✅ API Response Summary:');
    console.log(`- Total items: ${data.count}`);
    console.log(`- Items returned: ${data.concierge_order_items?.length || 0}`);
    console.log(`- Limit: ${data.limit}`);
    console.log(`- Offset: ${data.offset}`);
    
    if (data.concierge_order_items && data.concierge_order_items.length > 0) {
      console.log('\n📋 First Item Structure Analysis:');
      const firstItem = data.concierge_order_items[0];
      
      console.log('🔍 Base Fields:');
      console.log(`- ID: ${firstItem.id}`);
      console.log(`- Title: ${firstItem.title}`);
      console.log(`- Quantity: ${firstItem.quantity}`);
      console.log(`- Unit Price: ${firstItem.unit_price}`);
      console.log(`- Total Price: ${firstItem.total_price}`);
      console.log(`- Status: ${firstItem.status}`);
      console.log(`- Requested Date: ${firstItem.requested_date}`);
      
      console.log('\n🔍 Relationship Fields:');
      console.log(`- Has concierge_order: ${!!firstItem.concierge_order}`);
      console.log(`- Has order: ${!!firstItem.order}`);
      console.log(`- Has order_line_item: ${!!firstItem.order_line_item}`);
      console.log(`- Has product: ${!!firstItem.product}`);
      console.log(`- Has category: ${!!firstItem.category}`);
      console.log(`- Has travel_dates: ${!!firstItem.travel_dates}`);
      
      if (firstItem.concierge_order) {
        console.log('\n📋 Concierge Order Details:');
        console.log(`- Concierge Order ID: ${firstItem.concierge_order.id}`);
        console.log(`- Order ID: ${firstItem.concierge_order.order_id}`);
        console.log(`- Status: ${firstItem.concierge_order.status}`);
      }
      
      if (firstItem.order) {
        console.log('\n📋 Order Details:');
        console.log(`- Order ID: ${firstItem.order.id}`);
        console.log(`- Display ID: ${firstItem.order.display_id}`);
        console.log(`- Email: ${firstItem.order.email}`);
        console.log(`- Currency: ${firstItem.order.currency_code}`);
      }
      
      if (firstItem.order_line_item) {
        console.log('\n📋 Order Line Item Details:');
        console.log(`- Line Item ID: ${firstItem.order_line_item.id}`);
        console.log(`- Title: ${firstItem.order_line_item.title}`);
        console.log(`- Product ID: ${firstItem.order_line_item.product_id}`);
        console.log(`- Variant ID: ${firstItem.order_line_item.variant_id}`);
      }
      
      if (firstItem.product) {
        console.log('\n📋 Product Details:');
        console.log(`- Product ID: ${firstItem.product.id}`);
        console.log(`- Title: ${firstItem.product.title}`);
        console.log(`- Description: ${firstItem.product.description?.substring(0, 100)}...`);
      }
      
      if (firstItem.category) {
        console.log('\n📋 Category Details:');
        console.log(`- Category ID: ${firstItem.category.id}`);
        console.log(`- Name: ${firstItem.category.name}`);
        console.log(`- Handle: ${firstItem.category.handle}`);
      }
      
      if (firstItem.travel_dates) {
        console.log('\n📋 Travel Dates:');
        console.log(`- Check-in: ${firstItem.travel_dates.check_in_date}`);
        console.log(`- Check-out: ${firstItem.travel_dates.check_out_date}`);
      }
      
      console.log('\n📋 Complete First Item JSON:');
      console.log(JSON.stringify(firstItem, null, 2));
    }
    
    console.log('\n🎉 Debug analysis completed!');
    console.log('\n📝 Next Steps:');
    console.log('1. Check the server logs for detailed SQL queries and debug information');
    console.log('2. Look for [DEBUG] and [SQL] prefixed log entries');
    console.log('3. Verify that all relationship data is being populated correctly');
    
  } catch (error) {
    console.error('\n❌ Debug script failed:', error.message);
    console.error('Stack trace:', error.stack);
  }
}

// Run the debug script
if (require.main === module) {
  debugSQLQueries();
}

module.exports = { debugSQLQueries };
