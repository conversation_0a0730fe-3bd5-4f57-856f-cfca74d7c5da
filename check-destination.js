require('dotenv').config();
const { Client } = require('pg');

async function checkDestination() {
  console.log('DATABASE_URL:', process.env.DATABASE_URL ? 'Set' : 'Not set');

  const client = new Client({
    connectionString: process.env.DATABASE_URL,
  });

  try {
    await client.connect();
    console.log('Connected to database');

    const result = await client.query(`
      SELECT
        id,
        name,
        handle,
        internal_web_link,
        external_web_link,
        currency_code,
        margin_percentage,
        created_at
      FROM destination
      WHERE handle = 'test-currency-margin-destination'
    `);

    console.log('Query result:');
    console.log(result.rows);

    if (result.rows.length > 0) {
      const destination = result.rows[0];
      console.log('\n=== DESTINATION DETAILS ===');
      console.log('ID:', destination.id);
      console.log('Name:', destination.name);
      console.log('Handle:', destination.handle);
      console.log('Internal Web Link:', destination.internal_web_link);
      console.log('External Web Link:', destination.external_web_link);
      console.log('Currency Code:', destination.currency_code);
      console.log('Margin Percentage:', destination.margin_percentage);
      console.log('Created At:', destination.created_at);
    } else {
      console.log('No destination found with handle: test-currency-margin-destination');
    }

  } catch (error) {
    console.error('Error:', error);
  } finally {
    await client.end();
  }
}

checkDestination();
